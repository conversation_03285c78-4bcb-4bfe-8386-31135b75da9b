# IaC 产品组运营平台前端项目

## 项目介绍

IaC 产品组运营平台前端项目，项目地址: admin.iac.oa.com

## 快速上手

### 开发工具

本项目依赖 tea-cli 进行工程化开发，请先安装。

```
npm i @tencent/tea-cli -g --registry=https://mirrors.tencent.com/npm/ --proxy=http://127.0.0.1:12639
```

### 开发

使用 tea dev 命令启动本地开发服务器：

```
tea dev
```

### 部署

打 Tag 即可触发蓝盾流水线部署
流水线地址：http://devops.oa.com/console/pipeline/iac-admin-fontend/list

### 组件

使用 Tea 组件进行开发，文档地址：http://tea.tencent.com/component

### 目录规范

-   `src/app.ts` 入口文件，主要进行业务路由定义
-   `src/routes` 存放业务路由实现
-   `src/configs` 存放 CSS 和菜单配置
-   `src/components` 存放公共业务组件
-   `src/utils` 存放公共工具方法

## 行为准则

-   代码尽量简单实现, 减少维护成本
-   能使用 Tea 提供的组件就不要自己去实现
-   和代码仓库风格一致, 不要新起风格

## 常见问题

1. 正式环境登陆后显示无权限/左侧菜单只显示 Advisor
   游客权限通过[智能网关](https://paas.oa.com/page/smartgate?paasId=admin_iac)配置，admin 权限联系 yalinpei

## 如何加入

1. 本项目开发人员，自己拉特性分支。本地验证 OK，合入到 develop 分支，部署后验收。
2. 非本项目开发人员，fork 到自己的远程库，提交 MR 的方式参与开发。
3. 协作与分支管理策略，见：[代码仓库协作流程](https://iwiki.woa.com/pages/viewpage.action?pageId=404008555)

## 团队介绍

1. 我们是一个年轻的团队，每天都在探索新鲜的事物，没有任何历史包袱。
2. 我们充满活力、激情与智慧。
3. 我们不会去探索什么是云，我们所创造的就是云。
4. 欢迎有共识的同学加入我们的团队\^\_^。
