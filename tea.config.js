/**
 * Tea 项目配置
 * @type {import("@tencent/tea-types/config").Configuration}
 */

const SentryPlugin = require('@tencent/webpack-sentry-plugin');

const moment = require('moment');

module.exports = {
	command: {
		dev: {
			// port: 8321,
			template: './public/index.html',
			proxy: {
				'/ts:auth/tauth/info': {
					target: 'https://isa-test.woa.com/',
					changeOrigin: true,
					onProxyRes(proxyRes, req, res) {
						res.send({
							ChnName: '翟光明',
							DeptNameString: 'CSIG云与智慧产业事业群/云技术运营服务部/云顾问产品中心/平台研发组',
							EngName: 'gmzhai',
							PositionName: '前端开发',
							Timestamp: '1704357981',
							WorkPlaceID: 1,
						});
					},
				},
				'/1/qcloud': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/qcloud': '/qcloud/v1',
					},
				},
				'/1/interface': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/interface': '/interface/v1',
					},
				},
				'/1/guard': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/guard': '/interface/v1',
					},
				},
				'/1/broadcast': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/broadcast': '/interface/v1',
					},
				},
				'/1/iam': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/iam': '/interface/v1',
					},
				},
				'/1/fault': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/fault': '/interface/v1',
					},
				},
				'/1/alarm': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/alarm': '/interface/v1',
					},
				},
				'/1/event': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/event': '/interface/v1',
					},
				},
				'/1/public': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/public': '/interface/v1',
					},
				},
				'/1/risk': {
					target: 'http://9.139.144.96/',
					changeOrigin: true,
					pathRewrite: {
						'^/1/risk': '/interface/v1',
					},
				},
			},
		},
		build: {
			template: './public/index.html',
		},
	},
	webpack: (config, { webpack }) => ({
		...config,
		externals: {},
		mode: 'development',
		devtool: false,
		resolve: {
			...config.resolve,
			alias: {
				...config.resolve.alias,
				'react-dom': '@hot-loader/react-dom',
				'tvision-charts': 'tvision-charts/dist/cjs/index.js',
				'tvision-charts-react': 'tvision-charts-react/dist/cjs/index.js'
			},
		},
		output: {
			...config.output,
			publicPath: '/',
		},
		plugins: [
			...config.plugins,
			new webpack.ContextReplacementPlugin(/moment[/\\]locale$/, /zh-cn|ja|ko/),
			new SentryPlugin({
				// 项目名(英文)  ID，在 项目设置 > 项目名(英文)  查找
				project: 'tsa-oss',
				// https://sentry.oa.com/api/ 申请，记得勾选  project:write 权限
				apiKey: 'cf5d8b3fc3aa47809d3218a8d9a9e223fed27caae57549bb8ef1ffead47762f1',
				suppressErrors: true,
				// include 一定要写，公司内部这是个正则对象，不能为数组，一定要注意。这里可以模拟正则对象，表示webpack 构建结果全量上传
				include: {
					test(filename) {
						return true;
					},
				},
				// 发布版本名，建议以天为名称，你也可以用 git 提交 hash
				release(hash) {
					return moment().format('YYYY_MM_DD');
				},
				// 指定 cdn 路径，注意可以用 ~ 替换 域名，标识这部分内容可被任意域名替换。
				filenameTransform: (filename) => `~/${filename}`,
			}),
		],
	}),
	sourceMap: {},
};
