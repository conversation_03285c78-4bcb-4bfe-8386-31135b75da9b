import React, { useState } from "react";
import ReactD<PERSON> from "react-dom";
import { Router, Route, Redirect, Switch } from "react-router-dom";
import <PERSON> from 'raven-js';
import Aegis from 'aegis-web-sdk';
import moment from "moment";
import { getProcessEnv } from './utils';

import { createBrowserHistory } from "history";
import { ConfigProvider } from "@tea/component";
import { HistoryContext } from "./history-context";
import { LogContext } from './pageLogContext';
import { CollapsedContext } from '@src/components/collapsed-context';
import Layout from "./components/Layout";
import MicroDevMode from "@src/components/MicroDevMode";
import { AppRouteMap, AppMenu } from "./types";
import 'regenerator-runtime';

const history = createBrowserHistory();

const env = getProcessEnv();
const MODE = {
	dev: 'development',
	pro: 'production'
}
interface AppProps {
	routes: AppRouteMap;
	menu: AppMenu;
}
const operator = localStorage.getItem('engName');

process.env.NODE_ENV === MODE.pro &&
	Raven.config('https://<EMAIL>/4348',
		{
			release: moment().format('YYYY_MM_DD'),
			environment: process.env.NODE_ENV,
			sampleRate: 1,
		}
	).install()

export const aegis = new Aegis({
	id: 'qVK1gfLDQv309aKzlD', // 项目上报id
	uin: operator, // 用户唯一标识（可选）
	env: Aegis.environment[env],
	reportApiSpeed: true, // 接口测速
	reportAssetSpeed: true, // 静态资源测速
	spa: true, // spa 页面需要开启，页面切换的时候上报pv
	api: { // 接口错误，上报参数信息
		apiDetail: true,
	},
	beforeRequest: onBeforeRequest,
	modifyRequest(options) {
		// TODO: 发起请求前调用，做上报参数处理
		return options
	},
	afterRequest: function (msg) {
		// TODO: 埋点请求完，做失败处理逻辑
	}
});

function onBeforeRequest(data) {
	const { logType, logs } = data;
	switch (logType) {
		case 'speed':
			const { type, payload, url, duration } = logs;
			if (type === 'fetch') {
				const { Action } = JSON.parse(payload);
				data.logs.url = `${url}/${Action}`
				if (duration > 2000) {
					aegis.report({ msg: 'api-log', level: Aegis.logType.INFO_ALL, ext1: `cost-time-${duration}`, ext2: url, ext3: payload })
				}
			}
			if (type === 'static' && duration > 2000) {
				aegis.report({ msg: 'static-log', level: Aegis.logType.INFO_ALL, ext1: `cost-time-${duration}`, ext2: url })
			}
			break;
		case 'performance':
			const { ttfb, firstScreenTiming } = logs;
			if (firstScreenTiming > 4000) {
				aegis.report({ msg: 'page-log~SLOW', level: Aegis.logType.INFO_ALL, ext1: `firstScreenTiming-${firstScreenTiming}` })
			}
			if (ttfb > 5000) {
				aegis.report({ msg: 'page-log~TTFB:SLOW', level: Aegis.logType.INFO_ALL, ext1: `TTFB-${ttfb}` })
			}
			break;
		default:
			break;
	}
	return data;
}

function App({ routes, menu }: AppProps) {
	const [collapsed, setCollapsed] = useState<boolean>(false)
	return (
		<CollapsedContext.Provider value={{ collapsed: collapsed, setCollapsed: setCollapsed }}>
			<Router history={history}>
				<HistoryContext.Provider value={{ history }}>
					<ConfigProvider>
						<LogContext.Provider value={{ aegis }}>
							<Layout menu={menu}>
								<Switch>
									{Object.entries(routes).map(([path, route]) => {
										const routeProps = {
											path,
											key: path,
											// @ts-ignore
											exact: route.exact,
										};
										if (typeof route === "object") {
											if ("render" in route) {
												return (
													<Route
														{...routeProps}
														render={route.render}
													/>
												);
											}
											if ("component" in route) {
												return (
													<Route
														{...routeProps}
														component={route.component}
													/>
												);
											}
										}
										return (
											<Route
												{...routeProps}
												component={
													route as React.ComponentType<any>
												}
											/>
										);
									})}
									<Route path="*">
										<Redirect to={Object.keys(routes)[0]} />
									</Route>
								</Switch>
							</Layout>
							{ env !== 'production' && <MicroDevMode /> }
						</LogContext.Provider>
					</ConfigProvider>
				</HistoryContext.Provider>
			</Router>
		</CollapsedContext.Provider>
	);
}

export async function routes(appRoutes: AppRouteMap, appMenu: AppMenu) {
	ReactDOM.render(
		<App routes={appRoutes} menu={appMenu} />,
		document.querySelector("#root")
	);
}
