import React, { useMemo } from "react";
import { <PERSON>, with<PERSON><PERSON><PERSON>, RouteComponentProps } from "react-router-dom";
import { Menu as TeaMenu, ExternalLink } from "@tea/component";
import { AppMenu, AppSubMenu, AppMenuItem, AppMenuLabel} from "../types";
import { CollapsedContext } from '@src/components/collapsed-context';

interface MenuProps extends RouteComponentProps<any> {
	menu: AppMenu;
}

function addDefaultOpened(route, menu) {
	let found = false;

	function traverse(items) {
			for (const item of items) {
					if (item.route === route) {
							found = true;
							return true; // 找到目标路由
					}
					if (item.items) {
							if (traverse(item.items)) {
									item.defaultOpened = true; // 在父级对象上添加属性
									return true; // 继续向上返回
							}
					}
			}
			return false; // 没有找到
	}

	traverse(menu.items);
	return menu;
}

export default withRouter<MenuProps, React.ComponentType<MenuProps>>(
	function Menu({ location, menu }) {
		const { pathname } = location;
		const specialPathList = ['/advisor/operation-manage', '/advisor/privilege'];
    const sameAppPathList = ['/advisor/operation-manage/restore-arch', '/advisor/operation-manage/inspect-overview', '/advisor/operation-manage/inspect-detail'];
    const samePrevPathList = ['/advisor/privilege'];
    const isInSameAppPathList = sameAppPathList.indexOf(pathname) !== -1;

		const menuNew = useMemo(() => {
			const updatedMenu = addDefaultOpened(pathname, menu);
			return updatedMenu;
		}, [menu, pathname])

		const renderItems = (item: any, key, className?: string) => {
			return (
				item.route === undefined && item.items?.length === 0 && item.href === undefined
				?
					<></>
					:
				<TeaMenu.Item
					className={className || ''}
					key={key}
					title={item.title}
					icon={item.icon}
					selected={item.route ? (pathname === item.route?.replace("#", "") || (specialPathList.includes(item.route?.replace("#", "")) && pathname.indexOf(item.route?.replace("#", "")) !== -1 && !isInSameAppPathList)) : false}
					render={(children) => {
						return item.href?
							(
								<ExternalLink href={item.href} className="menuLink">
									{children}
								</ExternalLink>):
							item.route
								?
								(
									<Link to={item.route}>
										{children}
									</Link>
								)
								:
								<div style={
									{
										cursor: 'not-allowed'
									}
								}>
									{children}
								</div>
					}}
				/>
			)
		}

		return (
			<CollapsedContext.Consumer>
				{({ collapsed, setCollapsed }) => (
					<TeaMenu
						className={'isa-oss-menu'}
						theme="dark"
						title={menu.title}
						icon={menu.icon}
						collapsable={true}
						// collapsed={collapsed}
						onCollapsedChange={(v) => { setCollapsed(v) }}
					>
						{
							menuNew.items.map((item: any, k) => {
								return item.items?.length > 0 ? <TeaMenu.Group key={k} title={item.label} >
									{
										item.items.map((val: any, j) => {
											return val?.items?.length > 0
												?
												<TeaMenu.SubMenu
												defaultOpened={val.defaultOpened}
												key={j}
												title={val.title}
												icon={val.icon}
												className={'tea-menu-oss-sub-item'}
											>
												{
													val?.items?.map((item: any, i) => {
														if (item?.items?.length > 0) {
															return <TeaMenu.SubMenu
																defaultOpened={item.defaultOpened}
																key={i}
																title={item.title}
																icon={item.icon}
																className={item.isThird?"third_submenu":""}
															>
																{
																	item?.items?.map((item: any, i) => {
																		return renderItems(item, i);
																	})
																}
															</TeaMenu.SubMenu>
														} else {
															return renderItems(item, i);
														}
													})
												}
											</TeaMenu.SubMenu>
												:
												renderItems(val, j, 'tea-menu-oss-sub-item');
										})
									}
								</TeaMenu.Group>
									:
									<></>;
							})
						}
					</TeaMenu>)}
			</CollapsedContext.Consumer>
		);
	}
);
