import React, { useEffect, useMemo, useState } from 'react';
import { withRouter, RouteComponentProps } from 'react-router-dom';
import { getSearchParam } from '@src/utils/common';
import { EX_SOURCE } from '@src/routes/architecture/conf/eidtorConfig';
import { includes, omit } from 'lodash';
import { getUrlParamsStr, queryStringObj } from '@src/utils/common';
import {
	Layout as TeaLayout,
	NavMenu,
	Popover,
	Card,
	Button,
	message as tips,
	Select,
} from '@tencent/tea-component';
import Menu from './Menu';
import { AppMenu, AppSubMenu, AppMenuItem } from '../types';
import { getUserInfo, getMenuKeys } from '@src/api/common';
import { ProductConfig } from '@src/types/common';
import { menu as baseMenu } from '@src/configs/menu';
import { getStorage } from "@src/utils/storage";
import { cloneDeep } from "lodash";

const { Header, Body, Sider } = TeaLayout;

const options = [
	{ value: 'china', text: '国内站' },
	{ value: 'sinapore', text: '国际站' },
];

interface LayoutProps extends RouteComponentProps<any> {
	menu: AppMenu;
}

export default withRouter<LayoutProps, React.ComponentType<LayoutProps>>(function Layout({ history, menu, children }) {
	const [engName, setEngName] = useState('');

	const [site, setSite] = useState(() => {
		if (localStorage.getItem('site') === 'sinapore') {
			return 'sinapore';
		} else {
			return 'china';
		}
	});

	const [filterMenu, setFilterMenu] = useState<AppMenu>({
		title: '云顾问运营平台',
		items: [],
	});

	useEffect(() => {
		if (process.env.NODE_ENV === 'production') {
			localStorage.setItem('menuItems', null);
		}
		fetch();
	}, []);

	const isShowNavMenu = useMemo(() => {
		return includes(EX_SOURCE, getSearchParam('source', location));
	}, []);

	const fetch = async () => {
		try {
			const { data, status } = await getUserInfo();
			setEngName(data.EngName);
			setFilterMenu(menu);
			// console.log(process.env.NODE_ENV);
			if (process.env.NODE_ENV === 'production') {
				const { data, status } = await getUserInfo();
				if (status === 200 && data && data.EngName) {
					setEngName(data.EngName);
					const res = await getMenuKeys({
						Username: data.EngName,
					});
					if (res.Error) {
						tips.error({ content: res.Error.Message });
						return;
					} else {
						filterMenuByKeys(res.Items);
					}
				}
			} else {
				// 其它环境：默认全部菜单选项显示
				setFilterMenu(baseMenu);
			}
			localStorage.setItem('engName', data.EngName);
		} catch (err) {
			const message: string = err.msg || err.toString() || 'unknown error';
			// tips.error({ content: message })
		}
	};
	const transformSingleArr = (obj, productList, defaultKeys) => {
		const objNew = cloneDeep(obj);
		const list = [];
		const dic = (el)=> {
			if (el?.items?.length > 0) {
				el.items.forEach((item, i)=>{
					if (productList.includes(item.key) || defaultKeys.includes(item.key)) {
						if (item.route === '') {
							item.route = 'isa-temporary-location';
						}
						list.push(cloneDeep(item));
					}
					if (item?.items?.length > 0) {
						dic(item);
					}
				});
			}
		};
		dic(objNew);
		return {
			objNew,
			list
		};
	};
	const filterMenuByKeys = (productList: Array<string>) => {
		let _filterMenu = cloneDeep(baseMenu);
		let newMenuItems = [];
		let defaultKeys = ['assess'];
		const info = transformSingleArr(cloneDeep(baseMenu), productList, defaultKeys);
		// _filterMenu.items.forEach((groupItem: AppMenuItem) => {
		// 	//避免菜单为空，服务报告(assess)的菜单默认都存在，无任何权限的用户，查看appid的服务报告，会提示无权限
		//	
		// 	if (productList.includes(groupItem.key) || defaultKeys.includes(groupItem.key)) {
		// 		newMenuItems.push(groupItem);
		// 	}
		// });
		const getFilterMenu = (initMenu, resMenu) => {
			initMenu.items.forEach((item: any, i) => {
				const obj: any = {
					...item
				};
				obj.items = [];
				resMenu[i] = obj;
				if (item.items && item.items?.length > 0) {
					item.items.forEach((el)=>{
						if (el.items && el.items?.length > 0) {
							const obj: any = {
								...el
							};
							obj.items = [];
							el.items.forEach((val)=>{
								if ((productList.includes(val.key) || defaultKeys.includes(val.key))) {
									obj.items.push(cloneDeep(val));
								}
							});
							resMenu[i].items.push(cloneDeep(obj));
						} else {
							if (productList.includes(el.key) || defaultKeys.includes(el.key)) {
								resMenu[i].items.push(cloneDeep(el));
							}
						}
					});
				}
			});
		};
		// console.log(_filterMenu)
		getFilterMenu(_filterMenu, newMenuItems);
		_filterMenu.items = newMenuItems;
		setFilterMenu(_filterMenu);
		localStorage.setItem('menuItems', JSON.stringify(info.list));
	};

	// 切换站点
	const handleChangeSite = (value: string) => {
		const isForeign = getStorage('site') === 'sinapore';
		let targetUrl = '';
		// let domain = document.domain;
		if (isForeign) {
			targetUrl = `https://isa.woa.com/advisor/assess`;
			// 考虑后续海外也有三个地址测试、预发和正式，且可能只有前缀改变，其他不变
			// targetUrl = `https://${domain.replace('intl.', '')}/advisor/assess`;
		} else {
			targetUrl = `https://isa-intl.woa.com/advisor/assess`;
			// targetUrl = `http://${'intl.' + domain}/advisor/assess`;
		}

		// 国内访问国外
		if (value === 'sinapore' && !isForeign) {
			window.open(targetUrl, '_blank');
		}

		// 国外访问国内
		if (value === 'china' && isForeign) {
			window.open(targetUrl, '_blank');
		}

	};

	return (
		<TeaLayout>
			{!isShowNavMenu && (
				<Header>
					<NavMenu
						left={
							<NavMenu.Item type="logo" onClick={() => history.push('/')}>
								<img src="//imgcache.qq.com/qcloud/app/tea/logo.svg" alt="logo" />
							</NavMenu.Item>
						}
						right={
							<>
								<img
									src="https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/d57baedb-ee1c-4130-80a3-a44725b76d37-hover.svg"
									alt="logo"
								/>
								<a
									style={{ marginLeft: 5, marginRight: 5, color: 'white' }}
									href="https://iwiki.woa.com/pages/viewpage.action?pageId=1935645821"
									target="_blank"
									rel="noopener noreferrer"
								>
									帮助中心
								</a>
								<div style={{ marginRight: 5, color: 'gray' }}>|</div>
								<Select
									type="simulate"
									appearance="default"
									boxSizeSync
									options={options}
									value={site}
									onChange={handleChangeSite}
									style={{ marginRight: 20, color: 'white' }}
								/>
								{engName ? (
									<Popover
										placement="top-start"
										overlay={
											<Card>
												<div
													style={{
														width: 100,
														height: 70,
														textAlign: 'center',
													}}
												>
													<h3>{engName}</h3>
													<Button
														onClick={() => {
															location.href = `${location.origin}/_logout/?url=${location.origin}`;
														}}
														style={{
															marginTop: 10,
														}}
													>
														注销
													</Button>
												</div>
											</Card>
										}
									>
										<img
											style={{
												height: 32,
												width: 32,
												marginRight: 20,
												borderRadius: 32,
												verticalAlign: 'middle',
												cursor: 'pointer',
											}}
											src={`https://rhrc.woa.com/photo/150/${engName}.png`}
										/>
									</Popover>
								) : (
									<span style={{ color: 'white' }}>未登录</span>
								)}
							</>
						}
					/>
				</Header>
			)}
			<Body>
				{filterMenu && !isShowNavMenu && (
					<Sider>
						<Menu menu={filterMenu} />
					</Sider>
				)}
				{children}
			</Body>
		</TeaLayout>
	);
});
