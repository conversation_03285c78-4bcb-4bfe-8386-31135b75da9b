const ENV_INFO = {
	'isa.woa.com': { name: '正式环境', value: 'production' },
	'isa-pre.woa.com': { name: '预发布环境', value: 'pre' },
	'isa-test.woa.com': { name: '测试环境', value: 'test' },
	'isa-intl-pre.woa.com': { name: '海外预发布环境', value: 'pre-abroad' },
	'isa-intl.woa.com': { name: '海外正式环境', value: 'production-abroad' },
};

export const getProcessEnv = () => {
	const { hostname } = location;
	return ENV_INFO[hostname]?.value || 'others';
};
