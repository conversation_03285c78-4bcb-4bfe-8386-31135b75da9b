#root {
	height: 100%;
}

.search-card-header {
	display: flex;
	padding: 20px;
	justify-content: center;
}
.empty-card-body {
	min-height: 70vh;
	display: flex;
	justify-content: center;
	align-items: center;
}
.select-search-form {
	display: flex;
	.select {
		width: 110px;
	}
	.action {
		margin-top: 0px;
		margin-left: 30px;
		border-top: none;
		padding-top: 0px;
	}
}
.assess-search-form {
	display: flex;
	.item {
		margin-right: 35px;
	}
	.action {
		margin-top: 0px;
		border-top: none;
		padding-top: 0px;
	}
}
.condition-form-item {
	.tea-form__controls--text {
		padding-top: 0;
	}
	.condition {
		display: flex;
		button {
			margin-left: 10px;
		}
	}
}

.hello {
	flex: 1;
	align-self: center;
	text-align: center;
	height: 100%;
	h1 {
		text-align: center;
		font-size: 36px;
		color: pink;
		font-family: Audiowide;
		text-shadow: 0 0 5px #fff, 0 0 10px #fff, 0 0 15px #fff, 0 0 20px pink,
			0 0 35px pink, 0 0 40px pink, 0 0 50px pink, 0 0 75px pink;
	}
}
.menuLink:after{
	margin-top: 5px;
}

.third_submenu.tea-menu__submenu.is-expanded{
	.tea-menu__list{
		display: block;
		padding-top: 0;
		padding-bottom: 10px;
	} 
	.tea-menu__item .tea-icon-arrowdown {
		transform: scaleY(-1);
		-ms-transform: scaleY(-1);
		-moz-transform: scaleY(-1);
		-webkit-transform: scaleY(-1);
		-o-transform: scaleY(-1);
	}
}
.third_submenu.tea-menu__submenu {
	.tea-menu__list{
		display: none;
		padding-top: 0;
		padding-bottom: 10px;
	}
	.tea-menu__item .tea-icon-arrowdown {
		transform: scaleY(1);
		-ms-transform: scaleY(1);
		-moz-transform: scaleY(1);
		-webkit-transform: scaleY(1);
		-o-transform: scaleY(1);
	}
	>.tea-menu__list {
		margin-left: 14px;
	}
}	
.isa-oss-menu {
	.tea-menu__list li.is-selected > .tea-menu__item {
		background: #2274FF;
		position: relative;
		&:before {
			background-color: #fff;
		}
		&:after {
			content: '';
			position: absolute;
			width: 16px;
			height: 16px;
			background-color: #2274FF;
			left: 4px;
			top: 11px;
		}
		img {
			filter: drop-shadow(16px 0 0 #fff);
			position: relative;
			margin-right: 24px;
			margin-left: -16px;
		}
	}
	.tea-menu__label .tea-menu__text {
		color: #A19D9D;
	}
	.tea-menu__submenu.is-expanded .tea-menu__text {
		color: #A7A7A7;
	}
	.tea-menu-oss-sub-item {
		>.tea-menu__item {
			.tea-menu__text {
				color: #E8E7E7!important;
			}
		}
	}
	.is-selected .tea-menu__text {
		color: #E8E7E7!important;
	}
}
