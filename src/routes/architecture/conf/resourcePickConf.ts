
export const resourcePickConf = {
    type: 'Resource',
    icon: 'icon-re-bind',
    title: '资源绑定',
    theme: 'success',
    subType: 'instance_id'
}

export const tagPickConf = {
    type: 'Tag',
    icon: 'icon-tags',
    title: '标签绑定',
    theme: 'warning',
    subType: 'instance_tag'
}

export const fuzzyPickConf = {
    type: 'Fuzzy',
    icon: 'icon-file-add',
    title: '模糊匹配绑定',
    theme: 'error',
    subType: 'fuzzy_binding'
}

export const allPickerConf = {
    instance_id: resourcePickConf,
    instance_tag: tagPickConf,
    fuzzy_binding: fuzzyPickConf
}

export const allBindType = ['instance_id', 'instance_tag', 'fuzzy_binding'];