
export const tabConfig = [
	{ id: 'template', label: '架构模板列表' },
	{ id: 'architecture', label: '架构图列表' },
];

export const templateTypeOption = {
	0: '公共模板',
	1: '自定义模板',
	'-1': '全部类型',
	// '2': '推送客户模板'
};

export const templateTypeOptionList = {
	2: '推送客户模板',
	0: '非推送模板',
	1: '非推送模板',
};

export const BASEURLPART1 = 'https://';
export const BASEURLPART2 = 'console.cloud.t';
export const BASEURLPART3 = 'encent.com';
export const BASEURL = `${BASEURLPART1}${BASEURLPART2}${BASEURLPART3}`;

export const CLOUD_MAP_INFO = [
	{ name: '项目类型：', tips: '', paramsName: 'ProjectType' },
	{ name: 'appid：', tips: '', paramsName: 'AppId' },
	{ name: '客户名称：', tips: '', paramsName: 'CustomerName' },
	{ name: '架构图名称：', tips: '', paramsName: 'MapName' },
	{ name: '架构图id：', tips: '', paramsName: 'MapUUId' },
	{ name: '架构图行业类型：', tips: '', paramsName: 'Industry' },
	{ name: '架构图创建人：', tips: '', paramsName: 'Creator' },
	{ name: '创建人部门/中心：', tips: '', paramsName: 'CreaterDepartment' },
	{ name: '架构图负责人', tips: '架构图第一责任人，审批架构图相关权限。', paramsName: 'Lead' },
	{ name: '负责人部门/中心：', tips: '', paramsName: 'LeadDepartment' },
	{
		name: '协作人',
		tips: '对架构图有编辑，资源增减，以及报告查看权限的。可多人协作，架构负责人可在 ‘操作’-‘协作人’ 手动增减',
		paramsName: 'Collaborator',
	},
	{ name: '客户健康度风险评分', tips: '点击分数查看评分算法', paramsName: 'AfterSaleScore' },
	{
		name: 'APPID组件比例',
		tips: 'appid组件比例：架构图已绑定资源的产品数量/appid下是所有产品数量',
		paramsName: 'ComponentRatio',
	},
	{ name: 'APPID核心组件实例绑定比例：', tips: '', paramsName: 'CoreComponentInsRatio' },
];

export const PROJECT_EXTRO_INFO = {
	转售后项目: [
		{ name: '售后TAM组：', paramsName: 'TAMGroup' },
		{ name: '转售后时间：', paramsName: 'AfterSaleTime' },
		{ name: '初始架构图和实例', tips: '转售后流程中，架构和实例', paramsName: 'InitialCLoudMapUrl' },
		{ name: '初始appid组件比例：', paramsName: 'InitialComponentRatio' },
		{ name: '初始appid核心组件实例绑定比例：', paramsName: 'InitialCoreComponentInsRatio' },
		{ name: '备注：', tips: '', paramsName: 'Comment' },
	],
	售后项目: [
		{ name: '售后TAM组：', paramsName: 'TAMGroup' },
		{ name: '备注：', tips: '', paramsName: 'Comment' },
	],
	行业项目: [
		{ name: '是否开通云顾问：', paramsName: 'IsAuthorized' },
		{ name: '备注：', tips: '', paramsName: 'Comment' },
	],
	专家项目: [{ name: '是否开通云顾问：', paramsName: 'IsAuthorized' }, { name: '备注：', tips: '', paramsName: 'Comment' }],
};

export const PROJECT_TYPE = {
	转售后项目: { theme: 'error', tips: '' },
	售后项目: { theme: 'warning', tips: '售后项目：已转售后的客户架构图，appid售后owner为架构图负责人' },
	行业项目: { theme: 'success', tips: '行业项目：未转售后的客户架构图，行业相关角色为架构负责人' },
	专家项目: { theme: 'primary', tips: '专家项目：高可用或容灾项目，售后技术支持专家为架构图负责人' },
	客户项目: { theme: 'default', tips: '客户项目：客户项目' },
};

export const FILE_TYPE = {
	ALL: 0,
	SVG: 1,
	NONE: 2,
};
