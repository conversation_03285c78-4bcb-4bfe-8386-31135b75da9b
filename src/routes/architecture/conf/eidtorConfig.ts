export const categoryMapping = {
	COMMON: '通用',
	COMPUTE: '计算',
	CONTAINER: '容器和中间件',
	STORAGE: '存储',
	DATABASE: '数据库',
	NETWORK: '网络与CDN',
	VIDEO: '视频服务',
	SAFETY: '安全',
	BIGDATA: '大数据',
	AI: '人工智能',
	INDUSTRY: '行业应用',
	ENTERPRISE: '企业服务',
	OTHER: '其他',
	SECURITY: '云安全',
	DEVELOPER: '开发者服务',
};

export const nodeToProductMap = {
	CVM: 'cvm',
	Ckafka: 'ckafka',
	CDB: 'mysql',
	Redis: 'redis',
	CDN: 'cdn',
	CLB: 'clb',
	COS: 'cos',
	Domain: 'domain',
	EMR: 'emr',
	LVB: 'live',
	SMS: 'sms',
	SSL: 'ssl',
	TKE: 'tke',
	TRTC: 'trtc',
	VOD: 'vod',
	WAF: 'waf',
	ES: 'Elasticsearch Service',
	MongoDB: 'mongodb',
	TDSQL: 'tdsql',
	CCN: 'ccn',
	NAT: 'nat',
	'DNS Pod': 'dnspod',
	'NAT Gateway': 'nat',
	'Dedicated VPN': 'vpnx',
	'TDSQL-C MySQL': 'cynosdb',
	'TDSQL MySQL': 'dcdb',
	'DDos': 'bgp',
	'DDoS Pro Anti-IP': 'bgpip',
	'Pulsar': 'tdmq',
	'File Storage': 'cfs'
};

export const noSupportScanProduct = ['AUTO SCALING', 'ZONE'];

export const supportScanPro = [
	'CVM',
	'Ckafka',
	'CDB',
	'Redis',
	'CDN',
	'CLB',
	'COS',
	'DNS Pod',
	'Domain',
	'EMR',
	'LVB',
	'NAT Gateway',
	'SMS',
	'SSL',
	'TKE',
	'TRTC',
	'VOD',
	'Dedicated VPN',
	'WAF',
	'ES',
	'MongoDB',
	'TDSQL',
	'CCN',
	'NAT',
	'TDSQL-C MySQL',
	'TDSQL MySQL',
	'DDos',
	'DDoS Pro Anti-IP',
	'Pulsar',
	'File Storage'
];

export const noSupporRegiontProductList = ['dnspod'];

export const operateDesHref =
	'https://iwiki.woa.com/pages/viewpage.action?pageId=4006823505#id-%E4%BA%91%E6%9E%B6%E6%9E%84%E6%9E%B6%E6%9E%84%E5%9B%BE%E4%BF%9D%E5%AD%98%E5%92%8C%E6%96%B0%E5%BB%BA-%E7%BB%91%E5%AE%9A%E8%B5%84%E6%BA%90';

export const feedBackUrl = 'https://iwiki.woa.com/pages/viewpage.action?pageId=4007117102';

export const OPRATION_TYPE = {
	MANUAL: 0,
	AUTO: 1,
};

export const OPERATE_TYPE = {
	READ: '1',
	WRITE: '0',
};

// 默认获取url参数
export const urlParams = ['optype', 'source', 'signature', 'timestamp', 'mapType', 'projectType'];

export const EX_SOURCE = ['pm', 'Antool', 'CONSOLE', 'ISA'];

// 创建dialog的类型
export const tabConfig = [
	{ id: 'cloudMap', label: '保存为架构图' },
	{ id: 'template', label: '保存为模板' },
];

export const createTabConfig = [{ id: 'cloudMap', label: '另存为（克隆）架构图' }];

export const pmTabConfig = [{ id: 'pmCloudMap', label: '保存架构图' }];
export const STYLE_TEXT_MAP = {
	route: '旋转',
	line: '线段样式',
	lineStart: '线段起点类型',
	lineEnd: '线段终点类型',
	pattern: '模式',
	shape: '区域形状',
	strokeStyle: '线段样式',
	scale: '缩放',
	fontSize: '字体大小',
	lineWidth: '线段宽度',
	fontWeight: '字体粗细',
	padding: '内边距',
	width: '宽度',
	height: '高度',
	depth: '深度',
	opacity: '透明度',
	shadow: '是否阴影',
	outline: '是否描边',
	is3d: '是否3D',
	isFlat: '是否平面',
	contour: '轮廓颜色',
	fill: '填充颜色',
	fillDark: '填充节点颜色',
	stroke: '填充颜色',
	lineColor: '填充线条颜色',
	color: '填充颜色',
	radius: '半径',
	hide: '隐藏',
	contourSize: '边框宽度'
}