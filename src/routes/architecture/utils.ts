import { get, toPairs, mapKeys, reduce, isEmpty } from 'lodash';

export function getNodeLabelName(node) {
    const { value = '', default: defaultName } = get(node, 'styles.label') || {};
    return value || defaultName;
}

// attr: label等
// style: 节点样式
// value: 样式值
export function setNodeStyleByKey(sigma, node, styleObj) {
    const { attr, value } = styleObj;
    const style = node?.styles[attr];
    const data = {};
    style.value = value;
    data[attr] = style;
    sigma.setStyleNode(data, node?.key);
}

export function getNodeStyleByKey(node, attr) {
    const style = node?.styles[attr];
    return style?.value;
}

export const swapItem = (arr, fromIndex, toIndex) => {
    arr[toIndex] = arr.splice(fromIndex, 1, arr[toIndex])[0];
    return arr;
}

export const initStrColor = (str) => {
    return str.replace(/#4286C5/gi, "#e2e6ec");
}

export function getViewPort(widthOrHeight) {
    // 如果传递的值为width就获取屏幕可视区宽度，如果传递的值为height就获取屏幕可视区域的高度
    if (widthOrHeight == 'width') {
        return document.documentElement.clientWidth || document.body.clientWidth;
    } if (widthOrHeight == 'height') {
        return document.documentElement.clientHeight || document.body.clientHeight;
    }
}

// 下载文件
export function downloadSVG(svg, name = 'download') {
    const downloadElement = document.createElement('a');
    downloadElement.setAttribute(
        'href',
        `data:text/plain;charset=utf-8,${encodeURIComponent(svg)}`
    );
    downloadElement.setAttribute('download', `${name}.svg`);
    downloadElement.click();
}

export const downloadFile = (url) => {
    const filename = url.split('/').pop();
    const eleLink = document.createElement('a');
    eleLink.download = filename;
    eleLink.style.display = 'none';
    eleLink.href = url;
    eleLink.target = '_blank';
    document.body.appendChild(eleLink);
    eleLink.click();
    document.body.removeChild(eleLink);
};

export const downloadUrl = (url) => {
    const filename = url.split('/').pop();
    window.open(`/api/download?name=${filename}`);
};

export const clearGraphCustomerData = (sigma, graphData) => {
    mapKeys(graphData, (value, key) => {
        sigma.resetCustomize(key)
    })
}

export const getRiskLayerData = (riskObj, scale) => {
    return reduce(toPairs(riskObj), (ret, [key, value]) => {
        const { customize } = value;
        const domRect = document.getElementById(key);
        if (!domRect) return ret;
        const rect = domRect.getBoundingClientRect();

        const itemLeft = rect.x - 190;
        const itemTop = rect.y - 50;
        ret.push({ key, riskData: !isEmpty(customize) ? customize : value, ratio: scale, position: { itemLeft, itemTop } });
        return ret;
    }, []);
}