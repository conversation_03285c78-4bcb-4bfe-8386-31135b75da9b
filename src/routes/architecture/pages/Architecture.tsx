import React, { useState, useEffect, useMemo } from 'react';
import KeepAlive from 'react-activation';
import { useHistory } from '@tea/app';

import { Layout, Tabs, TabPanel, message } from '@tencent/tea-component';
import { ArchiTemplateList } from '../components/ArchiTemplateList';
import { ArchitectureList } from '../components/ArchitectureList';

import { tabConfig } from '@src/routes/architecture/conf/architecture';

import { DescribeCloudMapConfig } from '@src/api/architecture/architecture';

const { Body, Content } = Layout;

export function Architecture() {

	const [industryConfig, setIndustryConfig] = useState([]);

	const getIndustryConfig = async () => {
		try {
			const res = await DescribeCloudMapConfig({ AppId: 1253985742 });
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setIndustryConfig(res.Industry);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	useEffect(() => {
		getIndustryConfig();
	}, []);

	return (
		<Body className='cloud-body' style={{ position: 'relative' }}>
			<Content>
				<Content.Header title="云架构" />
				<Content.Body>
					<Tabs ceiling animated={false} tabs={tabConfig}>
						<TabPanel id="template">
							<ArchiTemplateList industryConfig={industryConfig} />
						</TabPanel>
						<TabPanel id="architecture">
							<ArchitectureList industryConfig={industryConfig} />
						</TabPanel>
					</Tabs>
				</Content.Body>
			</Content>
		</Body>
	);
}
