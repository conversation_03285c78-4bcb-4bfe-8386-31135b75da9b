import React, { useState, useEffect } from 'react';
import { useHistory } from '@tea/app';

import { NotPermission } from '@src/routes/NotPermission';
import { Architecture } from './Architecture';

const NO_PERMISSION = 2;
const ALLOW_PERMISSION = 1;
const NO_DATA = 0;

export function ArchitectureIndex() {
    // 页面权限状态
    const history1 = useHistory();
    // 0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
    const [permission, setPermission] = useState(0);

    let timer = null;

    // 从localStorage获取菜单列表，并根据当前路径判断是否有权限
    const CheckPermission = () => {
        const menuItems = JSON.parse(localStorage.getItem('menuItems'));
        if (menuItems) {
            // 判断是否存在
            // const tmp = menuItems.filter((i) => {
            //     if (history1.location.pathname.includes(i.route)) {
            //         return i;
            //     }
            // });
            setPermission(ALLOW_PERMISSION);
            // if (tmp.length > 0) {
            //     setPermission(ALLOW_PERMISSION);
            // } else {
            //     setPermission(NO_PERMISSION);
            // }
        } else {
            setPermission(NO_DATA);
        }
    };

    // 持续从localStorage获取菜单列表
    useEffect(() => {
        timer = setInterval(() => {
            CheckPermission();
        }, 100);
        if (timer && permission != 0) {
            clearInterval(timer);
        }
        return () => clearInterval(timer);
    }, [permission]);

    return (
        <>
            {(() => {
                switch (permission) {
                    case NO_DATA: return <></>;
                    case NO_PERMISSION: return <NotPermission />;
                    case ALLOW_PERMISSION: return <Architecture />;
                    default: return <></>;
                }
            })()}
        </>
    );
}
