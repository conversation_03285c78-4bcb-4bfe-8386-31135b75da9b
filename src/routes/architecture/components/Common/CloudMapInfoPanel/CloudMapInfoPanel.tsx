import React, { useState, useEffect, useMemo, FC } from 'react';
import './style.less';
import { Tag, Bubble, Row, Col, List, Text, message, Icon, Button, Status, ExternalLink } from '@tencent/tea-component';
import { CLOUD_MAP_INFO, PROJECT_EXTRO_INFO, PROJECT_TYPE } from '../../../conf/architecture';

import { reduce, toPairs, map, isEmpty, sortBy, pick } from 'lodash';
import { BasicBar } from "@tencent/tea-chart/lib/basicbar";
import { IconTsa } from '@src/components/IconTsa';
import {
	DescribeCloudMapDetailInfo,
	DescribeDownLoadScoreExcel
} from '@src/api/architecture/architecture';
import { useToggle } from '../../../hooks/common';
interface ICloudMapInfoPanelProps {
	cloudMapDetail: any;
}

const ComponentInsRatio = ({ insRatioInfo }) => {
	const insData = reduce(toPairs(insRatioInfo), (ret, [key, value]) => {
		ret.push({ key, value: parseInt(value) });
		return ret
	}, []);

	const sortInsData = useMemo(() => {
		return sortBy(insData, ['value']).reverse();
	}, [insData]);

	const textInsRatio = useMemo(() => {
		const temp = sortInsData.slice(0, 2);
		let text = '';
		map(temp, ({ key, value }) => {
			text = text + `${key}：${value}%； `
		})
		return text
	}, [sortInsData]);

	return <>
		{
			sortInsData.length < 2
				? <Text theme="text" style={{ fontWeight: 'bold' }}>
					{textInsRatio}
				</Text>
				: <Bubble
					placement="right"
					trigger="hover"
					className='bubble-layer-ratio'
					style={{ maxWidth: 1000, width: 700, zIndex: 10 }}
					content={
						<BasicBar
							height={350}
							size={30}
							position={"key*value"}
							dataLabels
							yAxis={{ axisLabel: { formatter: item => item + '%' } }}
							dataSource={sortInsData}
						/>
					}
				>
					<div className='ratio-box'>
						<span className='ins-ratio-text'>{textInsRatio}</span>
						<IconTsa type='icon-right-arow' className='icon-svg-arrow' />
					</div>
				</Bubble>
		}
	</>
}

const CloudMapInfoPanel: FC<ICloudMapInfoPanelProps> = ({ cloudMapDetail }: ICloudMapInfoPanelProps) => {
	const { AppId, MapUUId } = cloudMapDetail;
	const [isLoading, startLoad, endLoad] = useToggle(false);
	const [isDownLoading, startDownLoad, endDownLoad] = useToggle(false);
	const operator = localStorage.getItem('engName');

	const [excelDownLoadInfo, setExcelDownLoadInfo] = useState<any>({});

	const [cloudMapInfo, setCloudMapInfo] = useState<any>({})
	const GetCloudMapDetailInfo = async () => {
		startLoad();
		try {
			const params = {
				AppId,
				MapUUId,
				Operator: operator,
			};
			const res = await DescribeCloudMapDetailInfo(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setCloudMapInfo(res)
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	useEffect(() => {
		GetCloudMapDetailInfo();
	}, []);

	// 异步请求生成下载分数报告
	const handleSaleScoreClick = async () => {
		startDownLoad()
		try {
			const res = await DescribeDownLoadScoreExcel({
				MapUUId,
				AppId,
				Operator: operator,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				endDownLoad();
				return;
			}
			const excelDownLoadInfo = pick(res, ['Message', 'CosUrl']);
			setExcelDownLoadInfo(excelDownLoadInfo);
			endDownLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endDownLoad();
		}
	};

	useEffect(() => {
		const { CosUrl, Message } = excelDownLoadInfo;
		if (Message === 'success' && CosUrl) {
			window.open(CosUrl);
		}
	}, [excelDownLoadInfo]);

	return (
		<div className='cloud-map-info-panel'>
			{
				isLoading
					? <Status icon="loading" size="m" title={<><Button icon='loading' />数据正在加载中...</>} />
					: <>
						<h3 className='cloud-map-info-panel-title'>架构图基本信息</h3>
						<hr />
						<div>
							{
								CLOUD_MAP_INFO.map((item, index) => {
									return <Row key={index}>
										<Col span={12}>
											<List>
												<Text theme="label" className='map-label'>{item.name}</Text>
												{
													item.tips
													&& <Bubble content={item.tips} placement="top" dark>
														<Icon type="help" />
													</Bubble>
												}
											</List>
										</Col>
										<Col span={12}>
											<List className='panel-data'>
												{(() => {
													let mapInfoItem = cloudMapInfo[item.paramsName];
													const dataTheme = mapInfoItem && parseInt(mapInfoItem) > 90 ? 'success' : parseInt(mapInfoItem) > 60 ? 'warning' : 'danger';
													mapInfoItem = mapInfoItem ? mapInfoItem : '--';
													switch (item.paramsName) {
														case 'ProjectType':
															return <List.Item className='panel-data-tag'>
																<Tag dark theme={PROJECT_TYPE[cloudMapInfo.ProjectType]?.theme}>
																	{mapInfoItem}
																</Tag>
															</List.Item>
														case 'Industry':
															return <List.Item className='panel-data-tag'>
																{
																	cloudMapInfo.Industry?.map((item, index) => (
																		<div key={index}>
																			{
																				index < 3
																					? <Tag theme='default' >{item}</Tag>
																					: <></>
																			}
																		</div>
																	))
																}
															</List.Item>
														case 'ComponentRatio':
															return <List.Item>
																<Text theme={dataTheme}>{mapInfoItem}</Text>
															</List.Item>
														case 'CoreComponentInsRatio':
															return <List.Item>
																<ComponentInsRatio insRatioInfo={mapInfoItem} />
															</List.Item>
														case 'AfterSaleScore':
															return <List.Item>
																{
																	isDownLoading
																		? <Icon type="loading" />
																		: <Text
																			theme={dataTheme}
																			className="panel-sale-score"
																			onClick={() => {
																				if (mapInfoItem !== '--') {
																					handleSaleScoreClick();
																				}
																			}}
																		>
																			{mapInfoItem}
																		</Text>
																}
															</List.Item>
														default:
															return <List.Item>
																{mapInfoItem}
															</List.Item>
													}
												})()}
											</List>
										</Col>
									</Row>
								})
							}
							{
								PROJECT_EXTRO_INFO[cloudMapInfo?.ProjectType]?.map((item, index) => {
									return <Row key={index}>
										<Col span={12}>
											<List>
												<Text theme="label" className='map-label'>{item.name}</Text>
												{
													item.tips
													&& <Bubble content={item.tips} placement="top" dark>
														<Icon type="help" />
													</Bubble>
												}
											</List>
										</Col>
										<Col span={12}>
											<List className='panel-data'>
												{(() => {
													let mapInfoItem = cloudMapInfo[item.paramsName];
													const dataTheme = mapInfoItem && parseInt(mapInfoItem) > 90 ? 'success' : parseInt(mapInfoItem) > 60 ? 'warning' : 'danger';
													mapInfoItem = mapInfoItem ? mapInfoItem : '--';
													switch (item.paramsName) {
														case 'InitialComponentRatio':
															return <List.Item>
																<Text theme={dataTheme}>{mapInfoItem}</Text>
															</List.Item>
														case 'InitialCoreComponentInsRatio':
															return <List.Item>
																{
																	isEmpty(mapInfoItem)
																		? '--'
																		: <ComponentInsRatio insRatioInfo={mapInfoItem} />
																}
															</List.Item>
														case 'IsAuthorized':
															return <List.Item>
																{cloudMapInfo[item.paramsName] ? '是' : '否'}
															</List.Item>
														case 'InitialCLoudMapUrl':
															return <List.Item>
																<ExternalLink href={mapInfoItem}>
																	初始架构图
																</ExternalLink>
															</List.Item>
														case 'Comment':
															return <List.Item>
																<Bubble
																	placement="right"
																	trigger="hover"
																	dark
																	content={mapInfoItem}
																>
																	<p className='content-item-bd content-clamp' >
																		{mapInfoItem}
																	</p>
																</Bubble>
															</List.Item>
														default:
															return <List.Item>
																{mapInfoItem}
															</List.Item>
													}
												})()}
											</List>
										</Col>
									</Row>
								})
							}
						</div>
					</>
			}
		</div >
	)
};
export default CloudMapInfoPanel;
