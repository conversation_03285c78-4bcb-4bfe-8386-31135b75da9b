import React, { useState, useEffect, FC } from 'react';
import './style.less';
import HelpDialog from '../HelpDialog';
import { Modal, List, Button } from '@tencent/tea-component';
interface IHelpDropDownProps {
	name?: string;
}
const HelpDropDown: FC<IHelpDropDownProps> = () => {
	// 打开帮助弹框
	const handleHelpBtnClick = () => {
		Modal.alert({
			// @ts-ignore
			caption: '帮助',
			maskClosable: true,
			size: '90%',
			message: <HelpDialog />,
			buttons: [],
		});
	};
	return (
		<div className='help-down'>
			<h3>快捷键</h3>
			<List type="option" className='help-down-list'>
				<List.Item className='help-list-item'>
					<span>拖动画布</span>
					<span>空格键 + 鼠标左键</span>
				</List.Item>
				<List.Item className='help-list-item'>
					<span>保存架构图</span>
					<span>CTRL + S</span>
				</List.Item>
				<List.Item className='help-list-item'>
					<span>缩放画布</span>
					<span>CTRL + 滚动中轮</span>
				</List.Item>
				<List.Item className='help-list-item'>
					<span>操作菜单</span>
					<span>鼠标右键</span>
				</List.Item>
			</List>
			<Button type="link" className='help-list-link' onClick={handleHelpBtnClick}>
				{'更多 >'}
			</Button>
		</div>
	);
};
export default HelpDropDown;
