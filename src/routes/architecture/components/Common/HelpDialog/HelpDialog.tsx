import React, { FC } from 'react';
import './style.less';

import { IconTsa } from '@src/components/IconTsa';
import { Table } from '@tencent/tea-component';

interface IHelpDocumentDialogProps {
	name?: string
}

const commonRecords = [
	{
		leftKey: 'DEL',
		funcDesc: '删除选中的元素',

		leftKey_r: 'CTRL',
		rightKey_r: 'X',
		inlineKey_r: '+',
		funcDesc_r: '剪切选中的元素',
	},
	{
		leftKey: 'BackSpace',
		funcDesc: '删除选中元素',

		leftKey_r: 'CTRL',
		rightKey_r: 'Y',
		inlineKey_r: '+',
		funcDesc_r: '操作前进',
	},
	{
		leftKey: 'CTRL',
		rightKey: 'F',
		inlineKey: '+',
		funcDesc: '查看帮助',

		leftKey_r: 'CTRL',
		rightKey_r: 'Z',
		inlineKey_r: '+',
		funcDesc_r: '操作后退',
	},
	{
		leftKey: 'CTRL',
		rightKey: 'S',
		inlineKey: '+',
		funcDesc: '保存架构图',

		leftKey_r: 'CTRL',
		rightKey_r: 'L',
		inlineKey_r: '+',
		funcDesc_r: '锁定或解除锁定当前的元素',
	},
	{
		leftKey: 'CTRL',
		rightKey: 'C',
		inlineKey: '+',
		funcDesc: '复制选中的元素',

		leftKey_r: 'CTRL',
		rightKey_r: 'A',
		inlineKey_r: '+',
		funcDesc_r: '全选',
	},
	{
		leftKey: 'CTRL',
		rightKey: 'V',
		inlineKey: '+',
		funcDesc: '粘贴元素到画布中',

		leftKey_r: 'CTRL',
		rightKey_r: 'G',
		inlineKey_r: '+',
		funcDesc_r: '绑定/解除选中元素一组',
	},
];

const viewRecords = [
	{
		leftKey: 'CTRL',
		rightKey: '+',
		inlineKey: '+',
		funcDesc: '放大画布',
	},
	{
		leftKey: 'CTRL',
		rightKey: '-',
		inlineKey: '+',
		funcDesc: '缩小画布',
	},
	{
		leftKey: 'CTRL',
		rightKey: '0',
		inlineKey: '+',
		funcDesc: '还原到1:1',
	},
	{
		leftKey: '↑',
		rightKey: 'W',
		inlineKey: '/',
		funcDesc: '当前元素向上移动一格',
	},
	{
		leftKey: '→',
		rightKey: 'D',
		inlineKey: '/',
		funcDesc: '当右元素向右移动一格',
	},
	{
		leftKey: '↓',
		rightKey: 'S',
		inlineKey: '/',
		funcDesc: '当前元素向下移动一格',
	},
	{
		leftKey: '←',
		rightKey: 'A',
		inlineKey: '/',
		funcDesc: '当前元素向左移动一格',
	},
];

const mouseRecords = [
	{
		leftKey: 'SPACE',
		rightKey: '鼠标左键',
		inlineKey: '+',
		funcDesc: '拖动画布位置',
	},
	{
		leftKey: 'CTRL',
		rightKey: '鼠标左键',
		inlineKey: '+',
		funcDesc: '多选元素',
	},
	{
		leftKey: 'CTRL',
		rightKey: '滚动中轮',
		inlineKey: '+',
		funcDesc: '缩放画布',
	},
	{
		icon: 'icon-mouse',
		mouseAction: '鼠标左键',
		funcDesc: '选中元素',
	},
	{
		icon: 'icon-mouse',
		mouseAction: '鼠标右键',
		funcDesc: '右键菜单',
	},
	{
		leftKey: 'SHIFT',
		rightKey: '鼠标左键',
		inlineKey: '+',
		funcDesc: '选中全部相同类型元素',
	},
];

const helpRecordsList = [
	{
		title: '鼠标操作',
		records: mouseRecords,
	},
	{
		title: '视图',
		records: viewRecords,
	},
	{
		title: '通用',
		records: commonRecords,
	},
];

const HelpDialog: FC<IHelpDocumentDialogProps> = ({ }: IHelpDocumentDialogProps) => {
	const columns = [
		{
			key: '',
			align: 'left',
			header: '快捷键',
			width: '60%',
			render: (item) => {
				const { leftKey, rightKey, icon, mouseAction, inlineKey } = item;
				return <div className='help-modal__item'>
					{icon && <IconTsa type={icon} />}
					{leftKey && <span className='help-modal-key'>{leftKey}</span>}
					{mouseAction && <span className='help-modal-key'>{mouseAction}</span>}
					{
						rightKey
						&& <>
							<span className='help-modal-char'>{inlineKey}</span>
							<span className='help-modal-key'>{rightKey}</span>
						</>
					}
				</div>;
			},
		},
		{
			key: 'funcDesc',
			align: 'left',
			header: '功能描述',
			render: (item) => <span style={{ fontWeight: 'bold' }}>{item.funcDesc}</span>
		},
	];

	const commonColumns = [
		{
			key: 'leftKey',
			align: 'left',
			header: '快捷键',
			width: '25%',
			render: (item) => {
				const { leftKey, rightKey, icon, mouseAction, inlineKey } = item;
				return <div className='help-modal__item'>
					{icon && <IconTsa type={icon} />}
					{leftKey && <span className='help-modal-key'>{leftKey}</span>}
					{mouseAction && <span className='help-modal-key'>{mouseAction}</span>}
					{
						rightKey
						&& <>
							<span className='help-modal-char'>{inlineKey}</span>
							<span className='help-modal-key'>{rightKey}</span>
						</>
					}
				</div>;
			},
		},
		{
			key: 'funcDesc',
			align: 'left',
			header: '功能描述',
			render: (item) => <span style={{ fontWeight: 'bold' }}>{item.funcDesc}</span>
		},
		{
			key: 'leftKey_r',
			align: 'left',
			header: '快捷键',
			width: '25%',
			render: (item) => {
				const { leftKey_r, rightKey_r, icon_r, mouseAction_r, inlineKey_r } = item;
				return <div className='help-modal__item'>
					{icon_r && <IconTsa type={icon_r} />}
					{leftKey_r && <span className='help-modal-key'>{leftKey_r}</span>}
					{mouseAction_r && <span className='help-modal-key'>{mouseAction_r}</span>}
					{
						rightKey_r
						&& <>
							<span className='help-modal-char'>{inlineKey_r}</span>
							<span className='help-modal-key'>{rightKey_r}</span>
						</>
					}
				</div>;
			},
		},
		{
			key: 'funcDesc_r',
			align: 'left',
			header: '功能描述',
			render: (item) => <span style={{ fontWeight: 'bold' }}>{item.funcDesc_r}</span>
		},
	];
	return (
		<div className='help-modal'>
			{
				helpRecordsList.map((item, index) => (
					<div className={`help-modal-body ${item.title === '通用' && 'help-modal-body-common'}`} key={index}>
						<h1 className='help-modal__title'>{item.title}</h1>
						<Table
							style={{ marginTop: 10 }}
							recordKey="funcDesc"
							// @ts-ignore
							columns={item.title === '通用' ? commonColumns : columns}
							records={item.records}
						/>
					</div>
				))
			}
		</div>
	);
};
export default HelpDialog;
