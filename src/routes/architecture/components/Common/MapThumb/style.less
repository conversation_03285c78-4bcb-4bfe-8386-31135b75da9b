.tpl-item {
    position: relative;
    width: 100%;
    height: 100%;
    border: 1px solid #eee;
    cursor: pointer;
    overflow: hidden;
    border-bottom: none;

    svg{
      overflow: visible;
    }

    .tpl-item-svg {
        width: 100%;
        svg {
            width: 100%;
            transition: all .35s;
            &:hover{
              transform: scale(1.05);
            }
        }
    }
    .tpl-item-info {
        position: absolute;
        width: 100%;
        bottom: 0;
        padding: 8px 12px;
        background-color: rgba(0,0,0,.5);
        box-sizing: border-box;
        line-height: 1.5;
        z-index: 1;
		.item-operate-box {
			display: flex;
			justify-content: space-between;

			.item-operate-box-icon {
				margin-right: 10px;

				&:hover {
					svg path {
						fill: rgb(18, 183, 229);
					}
				}
				svg path {
					fill: #fff;
				}
			}
		}
        .item-info-hd {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .item-info-title {
            width: 100%;
            height: 21px;
            color: #fff;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: bold;
          }
    
          .item-info-type {
            width: 33%;
            height: 21px;
            color: #fff;
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .item-info-industry {
            display: flex;
            justify-content: end;
            width: 80%;
            height: 21px;
          }
        }
        .item-info-sub-info {
          display: flex;
          justify-content: space-between;
        
          .item-info-desc,
          .item-info-creator {
            height: 18px;
            margin-top: 4px;
            color: #eee;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
  
          .item-info-desc {
            width: 100%;
          }
  
          .item-info-creator {
            width: 30%;
            text-align: end;
          }
        } 
    }
}

.content-item-bd {
	font-size: 13px;
	color: #535151;
	cursor: pointer;
}
.content-clamp {
	display: -webkit-box;
	overflow: hidden;
	-webkit-line-clamp: 1;
	-webkit-box-orient: vertical;
	text-overflow: ellipsis;
	line-height: 1.8em;
}

.bubble-layer {
	.tea-bubble__inner {
		border-radius: 10px;
	}
}