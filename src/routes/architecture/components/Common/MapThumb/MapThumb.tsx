import React, { useState, useEffect, FC, useRef } from 'react';
import './style.less';
import { useHistory } from '@tea/app';

import { Tag, Bubble, Row, Col, List, Text } from '@tencent/tea-component';
import { templateTypeOption } from '../../../conf/architecture';
import { IconTsa } from '@src/components/IconTsa';
import { iconSvg } from '@src/configs/iconSvg';

import { random } from 'lodash';
import { useActivate, useUnactivate, withActivation } from 'react-activation';
import { useToggle } from '../../../hooks/common';
interface IMapThumbProps {
	mapDetail: any;
	type: string;
	isShowDetail?: boolean;
	hasOperateBox?: boolean
	onMapDelete?: Function
	onMapPickCallback?: Function;
}

const TPL_INFO = ['模板名', '创建人', '模板属性', '行业类型', '模板属性描述', '模板使用场景', '更新时间'];
const PUSH_TPL_INFO = ['模板名', '创建人', '模板属性', '推送人', '推送客户', '推送时间'];

const tplTypeOption = {
	'0': "公共模板",
	'1': "自定义模板",
	'-1': '全部类型',
	'2': '推送客户模板'
}
const MapDetailPopover = ({ mapDetail }) => {
	const isRecomTpl = mapDetail.Property === 2;
	const tplInfoLabel = isRecomTpl ? PUSH_TPL_INFO : TPL_INFO;
	return (
		<div>
			<Row>
				<Col span={8}>
					<List>
						{tplInfoLabel.map((item, index) => (
							<List.Item key={index}>
								<Text theme="label" className='risk-label'>{item}</Text>
							</List.Item>))}
					</List>
				</Col>
				<Col span={16}>
					<List className='risk-data'>
						<List.Item style={{ fontWeight: 'bold' }}>{mapDetail.TemplateName}</List.Item>
						<List.Item>{mapDetail.Creator}</List.Item>
						<List.Item>{tplTypeOption[mapDetail.Property]}</List.Item>
						{
							isRecomTpl
								? <>
									<List.Item>{mapDetail.Creator}</List.Item>
									<List.Item>
										<Bubble
											placement="right"
											trigger="hover"
											dark
											content={`${mapDetail.AppId}/${mapDetail.CustomerName}`}
										>
											<p className='content-item-bd content-clamp' >
												{`${mapDetail.AppId}/${mapDetail.CustomerName}`}
											</p>
										</Bubble>
									</List.Item>
									<List.Item>{mapDetail.CreateTime}</List.Item>
								</>
								: <>
									<List.Item style={{ display: 'flex', marginBottom: 4, marginTop: -5 }}>
										{
											mapDetail.Industry?.map((item, index) => (
												<div key={index}>
													{
														index < 3
															? <Tag theme='default' >{item}</Tag>
															: <></>
													}
												</div>
											))
										}
									</List.Item>
									<List.Item style={{ marginBottom: 4 }}>
										<Bubble
											placement="right"
											trigger="hover"
											dark
											content={mapDetail.PropertyDesc}
										>
											<p className='content-item-bd content-clamp' >
												{mapDetail.PropertyDesc}
											</p>
										</Bubble>
									</List.Item>
									<List.Item>
										<Bubble
											placement="right"
											trigger="hover"
											dark
											content={mapDetail.Scenario}
										>
											<p className='content-item-bd content-clamp' >
												{mapDetail.Scenario}
											</p>
										</Bubble>
									</List.Item>
									<List.Item>{mapDetail.UpdateTime}</List.Item>
								</>
						}
					</List>
				</Col>
			</Row>
		</div>
	)
}

const MapThumb: FC<IMapThumbProps> = ({
	mapDetail,
	type,
	isShowDetail = true,
	hasOperateBox = false,
	onMapDelete,
	onMapPickCallback
}: IMapThumbProps) => {
	const history = useHistory();
	const tplPop = useRef(null);

	const [popupLayerVisible, openPopupLayer, closePopupLayer] = useToggle(false);

	useUnactivate(() => {
		openPopupLayer();
	})
	return (
		<div className='tpl-item' onClick={() => onMapPickCallback?.(mapDetail)} ref={tplPop} >
			<div dangerouslySetInnerHTML={{ __html: mapDetail.SvgFile ? mapDetail.SvgFile : iconSvg['svg-blank'] }} className='tpl-item-svg' />
			{
				isShowDetail
				&& <Bubble
					placement="right"
					trigger="hover"
					className='bubble-layer'
					style={{ maxWidth: 500, width: 400, zIndex: 10 }}
					popupContainer={popupLayerVisible ? tplPop.current : document.body}
					content={type === 'tpl' ? <MapDetailPopover mapDetail={mapDetail} /> : <></>}
				>
					<div className='tpl-item-info' >
						<div className='item-info-hd'>
							{
								type === 'tpl'
									? <p className='item-info-title'>{mapDetail.TemplateName}</p>

									: <p className='item-info-title'>{mapDetail.MapName}</p>
							}
							<div className='item-info-industry'>
								{
									type === 'tpl'
										&& mapDetail.Property === 2
										? <div><Tag dark theme="warning">推送客户模板</Tag></div>
										: mapDetail.Property === 0
											? mapDetail.Industry?.map((item, index) => (
												<div key={index}>
													{
														index < 2
															// @ts-ignore
															? <Tag dark theme={["primary", 'success', 'warning'][random(0, 2)]} >{item}</Tag>
															: <></>
													}
												</div>
											))
											: <></>
								}
							</div>
						</div>
						<div className='item-info-sub-info'>
							{
								hasOperateBox
									? <div className='item-operate-box'>
										{
											mapDetail.CanRead
											&& <Bubble
												popupContainer={popupLayerVisible ? tplPop.current : document.body}
												content="查看"
												placement="top"
												dark
											>
												<div
													className="item-operate-box-icon"
													onClick={(e) => {
														e.stopPropagation();
														history.push(
															`/advisor/architecture/template-editor/${mapDetail.MapTemplateUUId}?optype=1`
														);
													}}
												>
													<IconTsa type='icon-view' />
												</div>
											</Bubble>
										}
										{
											mapDetail.CanWrite
											&& <Bubble
												popupContainer={popupLayerVisible ? tplPop.current : document.body}
												content="编辑"
												placement="top"
												dark
											>
												<div
													className="item-operate-box-icon"
													onClick={(e) => {
														e.stopPropagation();
														history.push(
															`/advisor/architecture/template-editor/${mapDetail.MapTemplateUUId}?optype=0`
														);
													}}
												>
													<IconTsa type='icon-edit' />
												</div>
											</Bubble>
										}
										{
											mapDetail.CanDelete
											&& <Bubble content="删除" placement="top" dark>
												<div
													className="item-operate-box-icon"
													onClick={(e) => {
														e.stopPropagation();
														onMapDelete(mapDetail);
													}}
												>
													<IconTsa type='icon-delete' />
												</div>
											</Bubble>
										}
									</div>
									: <p className='item-info-desc'>{mapDetail.Creator}</p>
							}
							<p className='item-info-creator'>{type === 'tpl' ? templateTypeOption[mapDetail.Property] : mapDetail.CustomerName}</p>
						</div>
					</div>
				</Bubble>
			}
		</div >
	);
};
export default MapThumb;
