import React, { useState, useEffect } from 'react';
import { useHistory } from '@tea/app';
import '@src/routes/architecture/style.less';

import {
	Card,
	Table,
	Tag,
	Button,
	message,
	StatusTip,
	Justify,
	Bubble,
	Segment,
	H3,
	Modal,
} from '@tencent/tea-component';
import MapThumb from '../MapThumb';
import { GetArchitectureTemplateList, DeleteCloudMapTemplate } from '@src/api/architecture/architecture';

import { useActivate, useUnactivate, withActivation } from 'react-activation';
import { templateTypeOptionList } from '@src/routes/architecture/conf/architecture';
import { FILE_TYPE } from '@src/routes/architecture/conf/architecture';
import { useToggle } from '../../../hooks/common';
const { pageable } = Table.addons;
interface paramsProps {
	filter: any;
}

export function ArchiTableList({ filter }: paramsProps) {
	const history = useHistory();
	const operator = localStorage.getItem('engName');
	const [isLoading, startLoad, endLoad] = useToggle(false);
	const [templateList, setTemplateList] = useState([]);

	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	const getArchiTemplateList = async () => {
		startLoad();
		try {
			const params = {
				AppId: 1253985742,
				Filters: filter,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
				Operator: operator,
				FileType: FILE_TYPE.NONE,
			};
			const res = await GetArchitectureTemplateList(params);
			if (res?.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setTemplateList(res.CloudMapTemplate);
				setTotalPage(res.TotalCount);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};
	useEffect(() => {
		setPageNumber(1);
	}, [filter]);

	useEffect(() => {
		getArchiTemplateList();
	}, [pageNumber, pageSize, filter]);

	useActivate(() => {
		getArchiTemplateList();
	});

	const columns = [
		{
			key: 'TemplateName',
			align: 'left',
			header: '架构模板名称',
			render: (item) => (
				<Bubble
					placement="right-start"
					trigger="hover"
					style={{ maxWidth: 500, width: 385 }}
					content={
						<>
							<H3>{item.TemplateName}</H3>
							<div className="tpl-thumb-item">
								<MapThumb mapDetail={item} type="tpl" />
							</div>
						</>
					}
				>
					<span style={{ cursor: 'pointer' }}>{item.TemplateName}</span>
				</Bubble>
			),
		},
		{
			key: 'Property',
			align: 'left',
			header: '推送模板属性',
			render: (item) => <>{templateTypeOptionList[item.Property]}</>,
		},
		{
			key: 'CustomerName',
			align: 'left',
			header: '推送客户',
			width: '15%',
			render: (item) => {
				return <>
					{
						item.Property === 2
							? <Bubble
								arrowPointAtCenter
								placement='bottom'
								trigger="hover"
								content={
									<>
										<p>推送客户：{`${item.AppId}/${item.CustomerName}`}</p>
										<p>推送人 ：{item.Creator}</p>
										<p>推送时间：{item.CreateTime}</p>
									</>
								}
							>
								<Button type="link" > {item.AppId}</Button>
								<br />
								{item.CustomerName && <Tag>{item.CustomerName}</Tag>}
							</Bubble>
							: '--'
					}
				</>
			},
		},
		{
			key: 'Creator',
			align: 'left',
			header: '创建人',
		},
		{
			key: 'PropertyDesc',
			align: 'left',
			header: '模板属性描述',
		},
		{
			key: 'Scenario',
			align: 'left',
			header: '模板使用场景',
		},
		{
			key: 'Industry',
			align: 'left',
			header: '行业类型',
			render: (item) => (
				<>
					{item.Industry?.map((item, index) => (
						<div key={index}>
							<Tag theme="primary">{item}</Tag>
							<br />
						</div>
					))}
				</>
			),
		},
		{
			key: 'UpdateTime',
			align: 'left',
			header: '最近编辑时间',
		},
		{
			key: 'Detail',
			header: '操作',
			align: 'center',
			render: (item) => (
				<>
					<Button
						type="link"
						disabled={!item.CanRead}
						onClick={() =>
							history.push(`/advisor/architecture/template-editor/${item.MapTemplateUUId}?optype=1`)
						}
					>
						查看
					</Button>
					<Button
						type="link"
						disabled={!item.CanWrite}
						onClick={() =>
							history.push(`/advisor/architecture/template-editor/${item.MapTemplateUUId}?optype=0`)
						}
					>
						编辑
					</Button>
					<Button type="link" disabled={!item.CanDelete} onClick={() => handleDeleteTemplate(item)}>
						删除
					</Button>
				</>
			),
		},
	];
	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	const handleDeleteTemplate = (currentTemplate) => {
		Modal.confirm({
			icon: 'warning',
			message: '确认删除当前模板？',
			description: `删除 “${currentTemplate.TemplateName}” 模板后不能恢复，是否确定？`,
			okText: '删除',
			cancelText: '取消',
			onOk: () => deleteTemplate(currentTemplate),
		});
	};
	const deleteTemplate = async (currentTemplate) => {
		try {
			const { MapTemplateUUId } = currentTemplate;
			const res = await DeleteCloudMapTemplate({
				AppId: 1253985742,
				MapTemplateUUIds: [MapTemplateUUId],
				Operator: operator,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				getArchiTemplateList();
				message.success({ content: '删除成功' });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const tableOptionProps = {
		style: { marginTop: 15 },
		recordKey: 'MapTemplateUUId',
		verticalTopL: true,
		columns,
		records: templateList,
		topTip: isLoading ? (
			<StatusTip status="loading"></StatusTip>
		) : (
			templateList.length === 0 && <StatusTip status="empty" />
		),
		addons: [
			pageable({
				pageIndex: pageNumber,
				recordCount: totalPage,
				onPagingChange: handlePageChange,
			}),
		],
	};
	return (
		<>
			{/* @ts-ignore */}
			<Table {...tableOptionProps} />
			{/* <iframe src="https://isa-test.woa.com/advisor/architecture/cloud-editor/266?optype=0&source=pm&signature=b63f4d66486e37fcaa1befcccb7aab9a5561f2d1ccd76a3a16921a98e6c9e11f&timestamp=1672285235" width="100%" height="1000px"></iframe> */}
		</>
	);
}
