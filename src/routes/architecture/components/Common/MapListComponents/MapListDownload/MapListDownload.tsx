import React, { useState, useEffect, FC } from 'react';
import './style.less';
import { Button, message, Icon, Text } from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import { isEmpty } from 'lodash';
import { DownloadListReport, GetDownloadTask } from '@src/api/architecture/architecture';

interface IMapListDownloadProps {
	filter: any
}
const MapListDownload: FC<IMapListDownloadProps> = ({ filter }: IMapListDownloadProps) => {
	let reportTimer;
	const operateName = localStorage.getItem('engName');
	const [mapListReport, setMapListReport] = useState<any>({});
	const [showLoading, setShowLoading] = useState(false);

	// 请求Excel异步下载
	const handleReportAsync = async () => {
		setShowLoading(true);

		try {
			const res = await DownloadListReport({
				AppId: 1253985742,
				ListType: 0,
				Name: operateName,
				Filters: filter,
			});
			if (res.Error) {
				message.error({ content: res.Error });
				return;
			}
			if (res.ResultId) {
				getDownloadTask(res.ResultId);
				reportTimer = setInterval(() => {
					getDownloadTask(res.ResultId);
				}, 2000);
			} else {
				message.error({ content: '生成巡检报告错误！' });
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 获取Excel异步下载结果
	const getDownloadTask = async (resultId: string) => {
		try {
			const res = await GetDownloadTask({ ResultID: resultId, AppId: 1253985742 });
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			if (res.TaskStatus === 'success' || res.TaskStatus === 'failed') {
				clearInterval(reportTimer);
				setMapListReport({
					CosUrl: res.CosUrl || '',
					TaskStatus: res.TaskStatus || '',
				});
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	const handleListReportDownload = () => {
		if (mapListReport.CosUrl) {
			window.open(mapListReport.CosUrl);
		}
	}
	useEffect(() => {
		if (mapListReport.TaskStatus === 'success' || mapListReport.TaskStatus === 'failed') {
			setShowLoading(false);
		}
	}, [mapListReport]);

	return (
		<div className='download-list'>
			{
				showLoading
					? <Button
						type="text"
						className='btn__report'
					>
						<Icon type="loading" />
						<span className='text-bt'>列表生成中</span>
					</Button>
					:
					!isEmpty(mapListReport) && mapListReport.TaskStatus === 'success'
						? <Button
							type="text"
							className='btn__report'
							onClick={handleListReportDownload}
						>
							<div className="item-svg__box">
								<IconTsa type='icon-export__red' className='icon-svg svg-red' />
								<span className='item-ht color__red'>下载列表</span>
							</div>
						</Button>
						: <Button
							type="text"
							className='btn__report'
							onClick={handleReportAsync}
						>
							<div className="item-svg__box">
								<IconTsa type='icon-export__black' className='icon-svg' />
								<span className='item-ht'>生成列表</span>
							</div>
						</Button>

			}
		</div>

	);
};
export default MapListDownload;
