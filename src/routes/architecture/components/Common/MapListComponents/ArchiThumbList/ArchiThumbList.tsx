import React, { useState, useEffect, FC, useMemo, useRef } from 'react';
import './style.less';
import { useHistory } from '@tea/app';

import { Status, Modal, Button, notification, message } from '@tencent/tea-component';
import { MapThumb } from '../../..';

import { isEmpty } from 'lodash';
import { useInfiniteScroll } from 'ahooks';
import { useActivate, useUnactivate, withActivation } from 'react-activation';
import { getViewPort } from '../../../../utils';

import { GetArchitectureTemplateList, DeleteCloudMapTemplate } from '@src/api/architecture/architecture';
import { FILE_TYPE } from '@src/routes/architecture/conf/architecture';

interface IArchiThumbListProps {
	filters: any;
	industryOption: any;
	clearFilter: Function;
	optionsConfig: any;
}

interface Result {
	list: any;
	nextId: boolean;
}

const PAGE_SIZE = 10;

const tplMapCategories = [
	{ name: '公共模板', key: '0' },
	{ name: '自定义模板', key: '1' },
]

const ArchiThumbList: FC<IArchiThumbListProps> = ({
	filters,
	clearFilter,
}: IArchiThumbListProps) => {
	const operator = localStorage.getItem('engName');
	const history = useHistory();

	const [currentItem, setCurrentItem] = useState('0');

	const [listFilter, setListFilter] = useState([{ Name: 'property', Values: ['0'] }]);

	const [canGetList, setCanGetList] = useState(false);

	const handleCategorieChange = (key) => {
		let retFilter = [];
		clearFilter();
		switch (true) {
			case key === '0':
				retFilter.push({ Name: 'property', Values: [key] });
				break;
			case key === '1':
				retFilter.push({ Name: 'property', Values: ['1', '2'] });
				break;
		}
		setCurrentItem(key);
		setListFilter(retFilter);
	};
	useEffect(() => {
		if (isEmpty(filters)) return;
		setCurrentItem('-1');
		setListFilter(filters);
	}, [filters]);

	const tplListRef = useRef<HTMLDivElement>(null);

	const viewPortHeight = getViewPort('height');

	const { data, loading, loadMore, loadingMore, noMore, reload } = useInfiniteScroll(
		(d) => {
			const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
			return getLoadMoreList(d?.nextId, page);
		},
		{ target: tplListRef, isNoMore: (d) => d?.nextId }
	);

	const getLoadMoreList = async (nextId, page): Promise<Result> => {
		const params = {
			AppId: 1253985742,
			Filters: listFilter,
			Offset: (page - 1) * PAGE_SIZE,
			Limit: PAGE_SIZE,
			Operator: operator,
			FileType: FILE_TYPE.SVG,
		};
		const res = await GetArchitectureTemplateList(params);
		const isNoMore = page * PAGE_SIZE > res.TotalCount;

		return new Promise((resolve) => {
			resolve({
				list: res.CloudMapTemplate,
				nextId: isNoMore,
			});
		});
	};

	useEffect(() => {
		if (canGetList) {
			reload();
		}
		setCanGetList(true);
	}, [listFilter]);

	useActivate(() => {
		reload();
	});

	const handleMapItemClick = (mapItem) => {
		if (mapItem.CanWrite) {
			history.push(`/advisor/architecture/template-editor/${mapItem.MapTemplateUUId}?optype=0`);
		} else if (mapItem.CanRead) {
			history.push(`/advisor/architecture/template-editor/${mapItem.MapTemplateUUId}?optype=1`);
		} else {
			notification.warning({ description: '您没有编辑当前图的权限!' });
		}
	};

	const handleDeleteTemplate = (currentTemplate) => {
		Modal.confirm({
			icon: 'warning',
			message: '确认删除当前模板？',
			description: `删除 “${currentTemplate.TemplateName}” 模板后不能恢复，是否确定？`,
			okText: '删除',
			cancelText: '取消',
			onOk: () => deleteTemplate(currentTemplate),
		});
	};
	const deleteTemplate = async (currentTemplate) => {
		try {
			const { MapTemplateUUId } = currentTemplate;
			const res = await DeleteCloudMapTemplate({
				AppId: 1253985742,
				MapTemplateUUIds: [MapTemplateUUId],
				Operator: operator,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				reload();
				message.success({ content: '删除成功' });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	return (
		<div className="archi-thumb-list-wraper">
			<div className="tpl-tabs">
				<div className="tpl-tabs-tab">
					<div className="tab-inner">
						{tplMapCategories.map((item) => (
							<span
								key={item.key}
								className={currentItem === item.key ? 'tab-item actived' : 'tab-item'}
								onClick={() => handleCategorieChange(item.key)}
							>
								{item.name}
							</span>
						))}
					</div>
				</div>
				<div
					className="tpl-tabs-wrap tpl-scrollbar"
					style={{ height: `${viewPortHeight - 200}px` }}
					ref={tplListRef}
				>
					{loading ? (
						<Status
							icon="loading"
							size="m"
							title={
								<>
									<Button icon="loading" />
									数据正在加载中...
								</>
							}
						/>
					) : (
						<>
							<div className="tpl-tabs-wrap-main">
								{data?.list?.map((item) => (
									<div key={item.MapTemplateUUId} className="tpl-list-picker-item">
										<MapThumb
											mapDetail={item}
											type="tpl"
											hasOperateBox={true}
											onMapDelete={(map) => handleDeleteTemplate(map)}
											onMapPickCallback={handleMapItemClick}
										/>
									</div>
								))}
								{data?.list?.length === 0 && <Status icon="blank" size="m" title="暂无数据" />}
							</div>
							<div className="tpl-tabs-wrap-bd">
								{!noMore && (
									<Button
										icon={loadingMore ? 'loading' : 'sortdown'}
										onClick={loadMore}
										disabled={loadingMore}
									/>
								)}
								{noMore && <span>没有更多数据</span>}
							</div>
						</>
					)}
				</div>
			</div>
		</div>
	);
};
export default ArchiThumbList;
