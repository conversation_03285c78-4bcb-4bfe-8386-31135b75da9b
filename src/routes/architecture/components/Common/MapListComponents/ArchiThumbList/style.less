.archi-thumb-list-wraper{
	.tpl-tabs {
		width: 100%;
		display: flex;
			&-tab {
				border-right: 1px solid #c8c5c5;
				padding-left: 8px;
				margin-left: -8px;

				.tab-inner {
					width: 96px;
					margin-right: -2px;
					font-size: 14px;
				}
	
				.tab-item {
					height: 36px;
					line-height: 36px;
					border-right: 3px solid transparent;
					cursor: pointer;
					display: block;
					width: 100%;
					padding-left: 8px;
	
					&.actived {
						cursor: default;
						color: #0052d9;
						border-right-color: #0052d9;
						font-weight: bold;
						margin-left: -8px;
					}
	
					&:hover {
						color: #0052d9;
						border-right-color: #0052d9;
						background-color: #eee;
						margin-left: -8px;
					}
				}
			}
			&-wrap {
				flex: 1;
				overflow-y: auto;
				&-main {
					padding: 20px;
					display: flex;
					flex-wrap: wrap;
					margin-top: -32px;
				}
				.tabs-icons-item {
					width: 96px;
					height: 96px;
					overflow: hidden;
					margin: 20px;
					cursor: pointer;
					border: 1px solid transparent;
					border-radius: 2px;
					box-sizing: border-box;
					padding-top: 8px;
					&:hover {
						border-color: #0052d9;
					}
				}
				.icon-img {
					display: block;
					text-align: center;
				}
				img {
					display: inline-block;
					height: 32px;
					width: 32px;
				}
				p {
					max-height: 40px;
					line-height: 20px;
					overflow: hidden;
				}
			}
		}
	
	.tpl-icons-filter {
		margin-bottom: 40px;
	}
	
	.tpl-tabs-wrap {
		overflow-y: auto;
		padding-top: 6px;
		max-height: 100%;

		&::-webkit-scrollbar {
			width: 2px;
			height: 6px;
		}
		&::-webkit-scrollbar-thumb {
			background: #888;
			border-radius: 20px;
		}
		&::-webkit-scrollbar-track {
			border-radius: 20px;
		}

		.tpl-tabs-wrap-main {
			.tpl-list-picker-item {
				position: relative;
				width: calc(25% - 20px);
				margin: 15px 9px;
				border: 1px solid #eee;
				border-top-left-radius: 10px;
				border-top-right-radius: 10px;
				overflow: hidden;
				box-shadow: 3px 6px 4px 0 rgba(54, 58, 80, 0.48);
				border-right: none;
				border-bottom: none;
				transition: all .3s ease;
				min-width: 230px;
				height: 250px;
				cursor: pointer;
				&:hover {
					box-shadow: 0 26px 40px -24px rgb(0 36 100 / 50%);
				}
			}
		}
		.tpl-tabs-wrap-bd {
			display: flex;
			justify-content: center;
			margin-top: 30px;
		}
	}
	
}