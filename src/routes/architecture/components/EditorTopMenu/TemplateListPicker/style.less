

.hgd-bd {
  margin-top: 10px;
  .tpl-list-picker {
    display: flex;
    flex-wrap: wrap;
  
    .tpl-list-picker-item {
      position: relative;
      width: calc(33.33% - 14px);
      margin: 12px 6px;
      border: 1px solid #eee;
      cursor: pointer;
      overflow: hidden;
  
      .tpl-list-picker-item-svg svg{
        width: 288px;
        overflow: visible;
        transition: all .35s;
        &:hover{
          transform: scale(1.05);
        }
      }
  
      .tpl-list-picker-item-info {
        position: absolute;
        width: 100%;
        bottom: 0;
        padding: 8px 12px;
        background-color: rgba(0,0,0,.5);
        box-sizing: border-box;
        line-height: 1.5;
        z-index: 1;
  
        .item-info-title {
          width: 100%;
          height: 21px;
          color: #fff;
          font-size: 14px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
  
        .item-info-sub-info {
          display: flex;
          justify-content: space-between;
        
          .item-info-desc,
          .item-info-creator {
            height: 18px;
            margin-top: 4px;
            color: #eee;
            font-size: 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
  
          .item-info-desc {
            width: 100%;
          }
  
        }
        
      }
    }
  }
}

.tpl-list-bd {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

.tpl-list-picker-loading{
  display: flex;
  justify-content: center;
}
