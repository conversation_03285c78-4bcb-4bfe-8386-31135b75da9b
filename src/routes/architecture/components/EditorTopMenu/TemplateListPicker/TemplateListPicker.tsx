import React, { useState, useEffect, useMemo, useRef } from 'react';
import './style.less';
import { useInfiniteScroll } from 'ahooks';

import { Input, StatusTip, Button } from '@tencent/tea-component';
import { MapThumb } from '../..';

import { filter } from 'lodash'
import { GetArchitectureTemplateList } from '@src/api/architecture/architecture';
import { FILE_TYPE } from '@src/routes/architecture/conf/architecture';
interface ITemplateListPickerProps {
	handlePickTplLoad?: Function;
}
interface Result {
	list: any;
	nextId: boolean
}

const PAGE_SIZE = 10;

const TemplateListPicker = ({ handlePickTplLoad }: ITemplateListPickerProps) => {
	const operator = localStorage.getItem('engName');

	const [filterName, setFilterName] = useState('');
	const tplListRef = useRef<HTMLDivElement>(null);

	const { data, loading, loadMore, loadingMore, noMore } = useInfiniteScroll((d) => {
		const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
		return getLoadMoreList(d?.nextId, page)
	}, { target: tplListRef, isNoMore: (d) => d?.nextId });

	const getLoadMoreList = async (nextId, page): Promise<Result> => {
		const params = {
			AppId: 1253985742,
			Filters: [],
			Offset: (page - 1) * PAGE_SIZE,
			Limit: PAGE_SIZE,
			Operator: operator,
			FileType: FILE_TYPE.ALL
		};
		const res = await GetArchitectureTemplateList(params);
		const isNoMore = page * PAGE_SIZE > res.TotalCount;

		return new Promise((resolve) => {
			resolve({
				list: res.CloudMapTemplate,
				nextId: isNoMore,
			});
		});
	}
	const dataList = useMemo(() => {
		let list = data?.list;
		if (filterName) {
			list = data?.list.filter((d) => d.TemplateName.includes(filterName));
		}
		return list;
	}, [data, filterName]);

	return (
		<>
			<div className="hgd-hd">
				<Input
					size="full"
					value={filterName}
					onChange={(value) => setFilterName(value)}
					onKeyDown={(e) => e.stopPropagation()}
					placeholder="请输入模板名检索"
				/>
			</div>
			<div className="hgd-bd scrollbar" ref={tplListRef}>
				{loading ? (
					<div className="tpl-list-picker-loading">
						<StatusTip status="loading" />
					</div>
				) : (
					<>
						<div className="tpl-list-picker">
							{dataList?.map((item) => (
								<div key={item.MapTemplateUUId} className="tpl-list-picker-item">
									<MapThumb
										mapDetail={item}
										type="tpl"
										onMapPickCallback={() => handlePickTplLoad(item)}
									/>
								</div>
							))}
						</div>
						<div className="tpl-list-bd">
							{!noMore && (
								<Button
									icon={loadingMore ? 'loading' : 'sortdown'}
									onClick={loadMore}
									disabled={loadingMore}
								/>
							)}
							{noMore && <span>没有更多数据</span>}
						</div>
					</>
				)}
			</div>
		</>
	);
};
export default TemplateListPicker;
