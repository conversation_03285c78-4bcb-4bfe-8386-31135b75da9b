import React from 'react';
import { tabTitleMap } from '@src/routes/advisor/components/AdvisorConfig';
import { Collapse } from '@tencent/tea-component/lib/collapse';
import { Text } from '@tencent/tea-component/lib/text';

interface Prop {
	id: string
	detectionStrategies: Array<any>
}

// 警告条件主题颜色映射
// const conditionThemeConfig = {
// 	'-1': 'weak',
// 	0: 'success',
// 	1: 'warning',
// 	2: 'warning',
// 	3: 'danger',
// };

export function ConfigPanel({ id, detectionStrategies }: Prop) {
	return (
		<div className="intlc-assessment-tabitem">
			<div className="intlc-assessment-tabitem__advice">{tabTitleMap.get(id)}</div>
			{detectionStrategies.map(strategy => (
				<Collapse
					key={strategy.StrategyId}
					icon={() => (
						<Text
							verticalAlign="top"
							className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--item"
							style={{ backgroundColor: '#bbb' }}
						></Text>
					)}
					iconPosition="left"
					className="intlc-assessment-tabitem__content"
				>
					<Collapse.Panel
						id={strategy.StrategyId.toString()}
						title={
							<>
								<div className="intlc-assessment-tabitem__title">{strategy.Name}</div>
								<div className="intlc-assessment-tabitem__introduction">
									<div className="intlc-assessment-tabitem__instructions">{strategy.Desc}</div>
								</div>
							</>
						}
					></Collapse.Panel>
				</Collapse>
			))}
		</div>
	);
}
