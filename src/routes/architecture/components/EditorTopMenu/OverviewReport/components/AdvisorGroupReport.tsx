import React, { useState, useEffect } from 'react';
import { Text } from '@tencent/tea-component/lib/text';
import { Icon } from '@tencent/tea-component/lib/icon';
import { Modal } from '@tea/component/modal';
import { Button } from '@tea/component/button';
import { Segment } from '@tencent/tea-component/lib/segment';

// 策略组ID映射
const tabIdMap = new Map([
	['overview', -1],
	['security', 1],
	['architecture', 2],
	['resource', 3],
	['cost', 4],
	['performance', 5],
]);

export function AdvisorGroupReport({ id, type, reportStatusQueue, onChangeReportAsync }) {
	const [showLoading, setShowLoading] = useState(false);
	const [visible, setVisible] = useState(false);
	const [reportType, setReportType] = useState(1);

	const currentReport = reportStatusQueue.find(report => tabIdMap.get(id) === report.Id && report.Type === type);

	useEffect(() => {
		if (currentReport && (currentReport.TaskStatus === 'success' || currentReport.TaskStatus === 'failed')) {
			setShowLoading(false);
		}
	}, [currentReport]);

	return (
		<>
			{(!currentReport || currentReport.TaskStatus === 'failed') && !showLoading && (
				<Text
					style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer', marginLeft: 30 }}
					className="intlc-assessment-create-report"
					onClick={() => {
						setShowLoading(true);
						onChangeReportAsync(tabIdMap.get(id), type);
					}}
				>
					<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" style={{ marginRight: 5 }}>
						<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#006cff" />
					</svg>
					<Text style={{ fontSize: 12, color: '#006cff' }}>
						生成报告
					</Text>
				</Text>
			)}
			{showLoading && (
				<Text style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}>
					<Icon type="loading" style={{ marginLeft: 10 }}></Icon>
					<Text style={{ fontSize: 12, color: '#006cff', marginLeft: 5 }}>
						报告生成中
					</Text>
				</Text>
			)}
			{currentReport
				&& currentReport.TaskStatus === 'success'
				&& !showLoading
				&& (currentReport.CosUrlPdf !== '' ? (
					<Text
						style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
						onClick={() => setVisible(true)}
					>
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" style={{ marginLeft: 30, marginRight: 5 }}>
							<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#d71111" />
						</svg>
						<Text style={{ fontSize: 12, color: '#d71111' }}>
							下载报告
						</Text>
					</Text>
				) : (
					<Text
						style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
						onClick={(e) => {
							e.stopPropagation();
							window.open(currentReport.CosUrl);
						}}
					>
						<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" style={{ marginLeft: 30, marginRight: 5 }}>
							<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#d71111" />
						</svg>
						<Text style={{ fontSize: 12, color: '#d71111' }}>
							下载报告
						</Text>
					</Text>
				))}
			<Modal visible={visible} onClose={() => setVisible(false)} caption='评估报告下载'>
				<Modal.Body>
					请选择下载报告类型：
					<br />

					<Segment
						value={reportType.toString()}
						onChange={value => setReportType(parseInt(value, 10))}
						options={[
							{
								text: (
									<>
										<div>EXCEL</div>
									</>
								),
								value: '1',

								style: { width: '200px', height: '80px' },
							},
							{
								text: (
									<>
										<div>PDF</div>
									</>
								),
								value: '2',

								style: { width: '200px', height: '80px', marginLeft: '10px' },
							},
						]}
					/>
				</Modal.Body>
				<Modal.Footer>
					<Button
						type="primary"
						onClick={(e) => {
							if (reportType === 1) {
								e.stopPropagation();
								window.open(currentReport.CosUrl);
							} else if (reportType === 2) {
								e.stopPropagation();
								window.open(currentReport.CosUrlPdf);
							}
							setVisible(false);
						}}
					>
						确定
					</Button>
					<Button type="weak" onClick={() => setVisible(false)}>
						取消
					</Button>
				</Modal.Footer>
			</Modal>
		</>
	);
}
