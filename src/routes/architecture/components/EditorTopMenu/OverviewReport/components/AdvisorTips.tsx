import React, { useMemo } from 'react';
import { Text } from '@tencent/tea-component/lib/text';

interface Prop {
	strategyRiskLevels: any,
	strategy: any,
	strategyAssessesTotalCountReal: any,
	strategyIgnoresTotalCountReal: any,
	strategyAssesses: any,
}

// 警告条件主题颜色映射
const conditionThemeConfig = {
	'-1': 'weak',
	0: 'success',
	1: 'warning',
	2: 'warning',
	3: 'danger',
};

export function AdvisorTips({
	strategyRiskLevels,
	strategy,
	strategyAssessesTotalCountReal,
	strategyIgnoresTotalCountReal,
	strategyAssesses,
}: Prop) {
	const l1 = useMemo(() => {
		const l = strategy.Notice.split('%d');
		if (strategy.StrategyId in strategyAssesses) {
			l.splice(1, 0, `${strategyAssessesTotalCountReal[strategy.StrategyId] || 0}`);
		} else {
			l.splice(1, 0, `${strategy.HighRiskCount + strategy.MediumRiskCount || 0}`);
		}
		return l;
	}, [strategyAssessesTotalCountReal]);

	const l2 = useMemo(() => {
		const l = strategy.Ignore.split('%d');
		if (strategy.StrategyId in strategyAssesses) {
			l.splice(1, 0, `${strategyIgnoresTotalCountReal[strategy.StrategyId] || 0}`);
		} else {
			l.splice(1, 0, `${strategy.IgnoredInstanceCount || 0}`);
		}
		return l;
	}, [strategyIgnoresTotalCountReal]);


	return (
		<>
			<Text>
				{
					l1.map((i, index) => {
						if (index === 1) {
							return <span key={index}>
								<Text theme={conditionThemeConfig[strategyRiskLevels[strategy.StrategyId]]}>{i}</Text>
								&nbsp;</span>;
						}
						return <Text key={index}>{i}</Text>;
					})
				}
			</Text>
			<Text>
				{
					l2.map((i, index) => {
						if (index === 1) {
							return <span key={index}><Text  >{i}</Text>&nbsp;</span>;
						}
						return <Text key={index}>{i}</Text>;
					})
				}
			</Text>
		</>
	);
}
