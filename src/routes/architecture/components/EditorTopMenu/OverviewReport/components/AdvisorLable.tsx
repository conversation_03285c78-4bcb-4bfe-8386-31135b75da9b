import React from 'react';
import { MediaObject } from '@tencent/tea-component/lib/mediaobject';
import { Text } from '@tencent/tea-component/lib/text';
import { tabTitleMap } from '@src/routes/advisor/components/AdvisorConfig';

interface Props {
	id: string;
	groupSummary: object;
}
export function AdvisorLable({ id, groupSummary }: Props) {
	function getMediaClassName(id: string): string {
		let type;
		switch (id) {
			case 'property':
				type = 'performance';
				break;
			case 'disaster':
				type = 'resource';
				break;
			case 'loadBalance':
				type = 'security';
				break;
			case 'securityArchitect':
				type = 'architecture';
				break;
			default:
				type = id;
				break;
		}
		return `intlc-assessment-tabs__media intlc-assessment-tabs__media--${type}`;
	}
	return (
		<>
			<MediaObject media={<div className={getMediaClassName(id)}></div>}>
				<div className="intlc-assessment-tabs__title">{tabTitleMap.get(id)}</div>
				{
					<div className="intlc-assessment-tabs__content">
						<div className="intlc-assessment-tabs-tag">
							<Text
								bgTheme="danger"
								verticalAlign="top"
								className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--tabs"
							></Text>
							<Text theme="danger" className="intlc-assessment-tabs-tag__num">
								{groupSummary[id] && groupSummary[id].high >= 0 ? groupSummary[id].high : '-'}
							</Text>
						</div>
						<div className="intlc-assessment-tabs-tag">
							<Text
								bgTheme="warning"
								verticalAlign="top"
								className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--tabs"
							></Text>
							<Text theme="warning" className="intlc-assessment-tabs-tag__num">
								{groupSummary[id] && groupSummary[id].medium >= 0 ? groupSummary[id].medium : '-'}
							</Text>
						</div>
						<div className="intlc-assessment-tabs-tag">
							<Text
								bgTheme="success"
								verticalAlign="top"
								className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--tabs"
							></Text>
							<Text theme="success" className="intlc-assessment-tabs-tag__num">
								{groupSummary[id] && groupSummary[id].none >= 0 ? groupSummary[id].none : '-'}
							</Text>
						</div>
					</div>
				}
			</MediaObject>
		</>
	);
}
