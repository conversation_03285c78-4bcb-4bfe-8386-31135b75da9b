import React, { useState, useEffect, useMemo } from 'react';
import { modifyIgnoreInstance, modifyIgnoreInstances } from '@src/api/advisor/estimate';
import { Instance, ConfigStrategy, RegionCode, RiskFieldsDescItem } from '@src/types/architecture/cloudReport';
import { Table, StatusTip, Tabs, TabPanel, Bubble, Tag, Button, Justify, Text, TagSearchBox, Icon, message } from '@tencent/tea-component';
import { SortBy } from '@tencent/tea-component/lib/table/addons';

import { cloneDeep, remove } from 'lodash';
import { insertCSS } from '@src/utils/insertCSS';
insertCSS(
	'AdvisorTable',
	`
.app-advisor-bg-danger {
		background-color: #f70505 !important;
	}

.app-advisor-bg-warning {
	background-color: #e89b0d !important;
}
`
);
const { pageable, sortable, selectable } = Table.addons;

interface Prop {
	strategyRiskFields: RiskFieldsDescItem
	product: string
	strategyId: number
	strategy: ConfigStrategy
	regionCodes: Array<RegionCode>
	currentTaskId: string
	strategyAssesses: Array<Instance>
	assessTotalCount: number
	strategyIgnores: Array<Instance>
	ignoreTotalCount: number
	onChangeAssesses: Function
	onChangeIgnores: Function
	handStrategyIgnoreChange: Function
}

export function AdvisorTable({
	strategyRiskFields,
	// product,
	strategyId,
	// strategy,
	regionCodes,
	currentTaskId,
	strategyAssesses,
	assessTotalCount,
	strategyIgnores,
	ignoreTotalCount,
	onChangeAssesses,
	onChangeIgnores,
	handStrategyIgnoreChange,
}: Prop) {
	// 评估中的实例列表
	const [assessInstances, setAssessInstances] = useState([]);
	// 忽略的实例列表
	const [ignoreInstances, setIgnoreInstances] = useState([]);
	// 评估中的实例列表的当前忽略状态
	const [assessStatus, setAssessStatus] = useState({});
	// 忽略的实例列表的当前忽略状态
	const [ignoreStatus, setIgnoreStatus] = useState({});
	// 评估中的实例列表当前页长
	const [assessPageSize, setAssessPageSize] = useState(10);
	// 忽略的实例列表当前分页长
	const [ignorePageSize, setIgnorePageSize] = useState(10);
	// 评估中的实例列表当前页码
	const [assessPageIndex, setAssessPageIndex] = useState(1);
	// 忽略的实例列表当前分页长
	const [ignorePageIndex, setIgnorePageIndex] = useState(1);
	// 列表项配置
	const [columns, setColumns] = useState([]);
	// 当前评估中的列表项
	const [assessRecords, setAssessRecords] = useState([]);
	const [assessSorts, setAssessSorts] = useState([{ by: 'Level', order: 'desc' } as SortBy]);
	// 当前忽略的列表项
	const [ignoreRecords, setIgnoreRecords] = useState([]);
	const [ignoreSorts, setIgnoreSorts] = useState([{ by: 'Level', order: 'desc' } as SortBy]);
	// 当前评估中的列表项状态
	const [assessTableStatus, setAssessTableStatus] = useState('none');
	// 当前忽略的列表项状态
	const [ignoreTableStatus, setIgnoreTableStatus] = useState('none');
	// 当前评估中的列表项已选择项
	const [assessSelectedKeys, setAssessSelectedKeys] = useState([]);
	// 当前忽略的列表项已选择项
	const [ignoreSelectedKeys, setIgnoreSelectedKeys] = useState([]);
	// 资源名称输入值
	// const [resourceInputValue, setResourceInputValue] = useState('');
	// 当前选项卡
	const [currentTabId, setCurrentTabId] = useState('assess');
	// 当前操作
	const [currentOperation, setCurrentOperation] = useState('');
	// 当前操作实例
	const [optInstanceId, setOptInstanceId] = useState('');
	// 当前标签键/标签值选项
	const [tagSearchOptions, setTagSearchOptions] = useState([]);
	// 当前评估中的列表标签搜索支持属性
	// const [assessTagSearchAttributes, setAssessTagSearchAttributes] = useState([])
	// 当前评估中的列表标签搜索值
	const [assessTagSearchValue, setAssessTagSearchValue] = useState([]);
	// 当前评估中的列表标签值选项
	const [assessTagValueOptions, setAssessTagValueOptions] = useState([]);
	// 当前忽略的列表标签搜索支持属性
	// const [ignoreTagSearchAttributes, setIgnoreTagSearchAttributes] = useState([])
	// 当前忽略的列表标签搜索值
	const [ignoreTagSearchValue, setIgnoreTagSearchValue] = useState([]);
	// 当前忽略的列表标签值选项
	const [ignoreTagValueOptions, setIgnoreTagValueOptions] = useState([]);

	const resourceTabs = [
		{ id: 'assess', label: '评估中的资源列表' },
		{ id: 'ignore', label: '被忽略的资源列表' },
	];
	// 警告条件主题颜色映射
	const conditionThemeConfig = {
		'-1': 'weak',
		0: 'success',
		1: 'warning',
		2: 'warning',
		3: 'danger',
	};

	// 根据strategyRiskFields 动态获取表头
	const getColumns = () => {
		// 定义标签render
		const getTagColumn = Item => (Item.Tags && Item.Tags.length ? (
			<>
				{Item.Tags.map(tag => (
					<Tag>
						{tag.Key || ''}:{tag.Value || ''}
					</Tag>
				))}
			</>
		) : (
			<>无</>
		));
		// 获取超链接表头名称
		const LinkId = strategyRiskFields.RiskFieldsDesc.find((i) => {
			if (i.Field === strategyRiskFields.LinkId) {
				return i;
			}
		});
		let columns: Array<any> = [];
		// 添加固定表头 Level
		columns = [
			{
				key: 'Level',
				header: <></>,
				align: 'center',
				width: 60,
				render: (Item) => {
					const level = Item.Level;
					return parseInt(level) >= 0 ? (
						<Text bgTheme={conditionThemeConfig[level]} className="intlc-assessment-tabs-tag__block"></Text>
					) : (
						<Text style={{ backgroundColor: '#bbb' }} className="intlc-assessment-tabs-tag__block"></Text>
					);
				},
			}];
		// 根据Url判断，是否存在超链接字段
		columns.push({
			key: 'LinkId',
			header: <>{LinkId ? LinkId.FieldName : ''}</>,
			render: (Item) => {
				const str = LinkId ? Item[LinkId.Field] || '' : '';
				if (strategyRiskFields.Url && str) {
					let url = '';
					// 判断超链接是否有包含${rid}，如果有，就尝试获取rid，并渲染。
					if (/\$\{rid\}/.exec(strategyRiskFields.Url)) {
						const ridcode = regionCodes.find(code => code.Region === Item.Region);
						const rid = ridcode ? ridcode.Code : '';
						if (rid) {
							url = eval(`\`${strategyRiskFields.Url}\``);
						}
					} else {
						url = eval(`\`${strategyRiskFields.Url}\``);
					}
					// 如果渲染url成功，则返回超链接格式
					if (url) {
						return (
							<a style={{ color: '#197CFF' }} href={url} target="_blank">{str}</a>
						);
					}
					return <Text>{str}</Text>;
				}
				return <Text>{str}</Text>;
			},
		});
		// 其他字段 除去Tags 已渲染的超链接字段 ，以及FieldName为空的
		strategyRiskFields.RiskFieldsDesc.filter((i) => {
			const LinkField = LinkId ? LinkId.Field : '';
			if (i.Field != 'Tags' && i.Field != LinkField && i.FieldName) {
				return i;
			}
		}).map((j) => {
			columns.push({
				key: j.Field,
				header: <>{j.FieldName}</>,
				render: (Item) => {
					let str = '';
					// FieldType 判断，不同的转义逻辑 string int ，需要尽量从FieldDict获取;  stringSlice 需要;拼接
					if (j.FieldType === 'string' || j.FieldType === 'int') {
						if (j.FieldDict && j.FieldDict.length && Item[j.Field]) {
							// 尝试从FieldDict获取
							const tmp = j.FieldDict.find((k) => {
								const key = j.FieldType === 'int' ? `${Item[j.Field]}` : Item[j.Field];
								if (k.Key === key) {
									return k;
								}
							});
							if (tmp) {
								str = tmp.Value;
							} else {
								str = Item[j.Field];
							}
						} else {
							str = Item[j.Field];
						}
					} else if (j.FieldType === 'stringSlice') {
						if (Item[j.Field] && Item[j.Field].length) {
							str = Item[j.Field].join(';');
						}
					} else {
						if (Item[j.Field]) {
							str = Item[j.Field];
						}
					}
					return <>{str}</>;
				},
			});
		});
		// Tags 字段
		columns.push({
			key: 'Tags',
			header: <>标签</>,
			render: getTagColumn,
		});
		// 操作 字段
		columns.push({
			key: 'Operation',
			header: TipHeaderComponent,
			render: Item => <>{Item.Operation}</>,
		});
		return columns;
	};

	const TipHeaderComponent = useMemo(() => (
		<>
			操作
			<Bubble
				content={
					currentTabId === 'assess' ? (
						<>
							<Text parent="div">
								<Text style={{ fontWeight: 'bold' }}>
									忽略
								</Text>
								：忽略后的资源，将不再被评估；
							</Text>
							<Text parent="div">
								<Text style={{ fontWeight: 'bold' }}>
									添加
								</Text>
								：将资源添加到评估列表中，下一次评估生效；
							</Text>
						</>
					) : (
						<>
							<Text parent="div">
								<Text style={{ fontWeight: 'bold' }}>
									添加
								</Text>
								：将资源添加到评估列表中，下一次评估生效；
							</Text>
							<Text parent="div">
								<Text style={{ fontWeight: 'bold' }}>
									忽略
								</Text>
								：忽略后的资源，将不再被评估；
							</Text>
						</>
					)
				}
			>
				<Icon type="info" />
			</Bubble>
		</>
	), [currentTabId]);

	// 标签搜索值转换为过滤值
	const getConvertTagSearchValue2Filter = (tagSearchValue) => {
		// 目前只支持 fuzzy 模糊查询实例id或名称
		const v: Array<string> = [];
		if (tagSearchValue) {
			const tagv = tagSearchValue.find((i) => {
				if (i.attr.key === 'InstanceId') {
					return i;
				}
			});
			if (tagv) {
				tagv.values.map((i) => {
					v.push(i.name);
				});
			}
		}
		return v;
	};

	// 添加/删除单个实例
	const modifyInstance = async (id, type, operation, instance) => {
		try {
			// @ts-ignore
			const result = await modifyIgnoreInstance({
				StrategyId: id,
				Operate: operation,
				Instance: { Id: instance[strategyRiskFields.PriId], Region: instance.Region },
				TaskID: currentTaskId || '',
				// AppId: appId
			});
			if (result.Error) {
				message.error({ content: result.Error.Message });
				return;
			}

			setCurrentOperation(operation);
			setOptInstanceId(instance[strategyRiskFields.PriId]);

			if (type === 'assess') {
				const currentAssessStatus = cloneDeep(assessStatus);
				currentAssessStatus[instance[strategyRiskFields.PriId]] = operation === 'add';
				setAssessStatus(currentAssessStatus);
			} else if (type === 'ignore') {
				const currentIgnoreStatus = cloneDeep(ignoreStatus);
				currentIgnoreStatus[instance[strategyRiskFields.PriId]] = !(operation === 'delete');
				setIgnoreStatus(currentIgnoreStatus);
			}
			if (operation === 'add' && type === 'assess') {
				setAssessTagSearchValue([]);
				setIgnoreTagSearchValue([]);
				handStrategyIgnoreChange(id);
				setIgnorePageIndex(1);
				setIgnorePageSize(10);
				setAssessPageIndex(1);
				setAssessPageSize(10);
			}
			if (operation === 'add') {
				message.error({ content: '忽略资源成功' });
			} else {
				message.error({ content: '添加资源成功' });
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 添加/删除多个实例
	const modifyInstances = async (id, type, operation) => {
		try {
			let currentInstances;

			if (type === 'assess') {
				currentInstances = assessInstances
					.filter(instance => assessSelectedKeys.includes(instance[strategyRiskFields.PriId]))
					.map(instance => ({
						Id: instance[strategyRiskFields.PriId],
						Region: instance.Region,
					}));
			} else if (type === 'ignore') {
				currentInstances = ignoreInstances
					.filter(instance => ignoreSelectedKeys.includes(instance[strategyRiskFields.PriId]))
					.map(instance => ({
						Id: instance[strategyRiskFields.PriId],
						Region: instance.Region,
					}));
			}
			// @ts-ignore
			const result = await modifyIgnoreInstances({
				StrategyId: id,
				Operate: operation,
				IgnoreInstances: currentInstances,
				TaskID: currentTaskId || '',
				// AppId: appId
			});
			if (result.Error) {
				message.error({ content: result.Error.Message });
				return;
			}

			if (type === 'assess') {
				const currentAssessStatus = cloneDeep(assessStatus);

				assessSelectedKeys.forEach((instanceId) => {
					currentAssessStatus[instanceId] = operation === 'add';
				});

				setAssessStatus(currentAssessStatus);
				setAssessSelectedKeys([]);
			} else if (type === 'ignore') {
				const currentIgnoreStatus = cloneDeep(ignoreStatus);

				ignoreSelectedKeys.forEach((instanceId) => {
					currentIgnoreStatus[instanceId] = !(operation === 'delete');
				});

				setIgnoreStatus(currentIgnoreStatus);
				setIgnoreSelectedKeys([]);
			}
			if (operation === 'add' && type === 'assess') {
				setAssessTagSearchValue([]);
				setIgnoreTagSearchValue([]);
				handStrategyIgnoreChange(id);
				setIgnorePageIndex(1);
				setIgnorePageSize(10);
				setAssessPageIndex(1);
				setAssessPageSize(10);
			}
			if (operation === 'add') {
				message.error({ content: '忽略资源成功' });
			} else {
				message.error({ content: '添加资源成功' });
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 获取对应产品的实例列表状态
	const getIgnoredStatus = () => {
		const currentAssessStatus = {};
		const currentIgnoreStatus = {};

		strategyAssesses.forEach((instance) => {
			currentAssessStatus[instance[strategyRiskFields.PriId]] = instance.IgnoredStatus;
		});
		strategyIgnores.forEach((instance) => {
			currentIgnoreStatus[instance[strategyRiskFields.PriId]] = instance.IgnoredStatus;
		});

		setAssessStatus(currentAssessStatus);
		setIgnoreStatus(currentIgnoreStatus);
	};

	// 获取对应产品的实例列表
	const getInstances = async () => {
		function handleAssessInstances() {
			const currAssInstances = strategyAssesses.map(instance => ({
				...instance,
				Operation: (
					<>
						<Button
							className="intlc-assessment-tabcoll__button"
							type="link"
							disabled={assessStatus[instance[strategyRiskFields.PriId]]}
							onClick={() => {
								modifyInstance(strategyId, 'assess', 'add', instance);
							}}
						>
							忽略
						</Button>
						<Button
							className="intlc-assessment-tabcoll__button"
							type="link"
							disabled={!assessStatus[instance[strategyRiskFields.PriId]]}
							onClick={() => {
								modifyInstance(strategyId, 'assess', 'delete', instance);
							}}
						>
							添加
						</Button>
					</>
				),
			}));
			setAssessInstances(currAssInstances);
		}

		function handleIgnoreInstances() {
			const currIgInstances = strategyIgnores.map(instance => ({
				...instance,
				Operation: (
					<>
						<Button
							className="intlc-assessment-tabcoll__button"
							type="link"
							disabled={!ignoreStatus[instance[strategyRiskFields.PriId]]}
							onClick={() => modifyInstance(strategyId, 'ignore', 'delete', instance)
							}
						>
							添加
						</Button>
						<Button
							className="intlc-assessment-tabcoll__button"
							type="link"
							disabled={ignoreStatus[instance[strategyRiskFields.PriId]]}
							onClick={() => modifyInstance(strategyId, 'ignore', 'add', instance)
							}
						>
							忽略
						</Button>
					</>
				),
			}));

			setIgnoreInstances(currIgInstances);
		}

		try {
			handleAssessInstances();
			handleIgnoreInstances();
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	useEffect(() => {
		const currentColumns = getColumns();
		setColumns(currentColumns);
	}, [currentTabId, regionCodes]);

	useEffect(() => {
		getIgnoredStatus();
	}, [strategyAssesses, strategyIgnores]);

	useEffect(() => {
		getInstances();
	}, [strategyAssesses, strategyIgnores, assessStatus, ignoreStatus]);

	useEffect(() => {
		switch (currentTabId) {
			case 'assess':
				setAssessRecords(assessInstances);
				if (assessInstances.length > 0) {
					setAssessTableStatus('none');
				} else {
					setAssessTableStatus('empty');
				}

				break;
			case 'ignore':
				setIgnoreRecords(ignoreInstances);
				if (ignoreInstances.length > 0) {
					setIgnoreTableStatus('none');
				} else {
					setIgnoreTableStatus('empty');
				}

				break;
		}
	}, [currentTabId, assessInstances, ignoreInstances]);

	return (
		<>
			<div className="intlc-assessment-tabcoll__block">
				<Tabs
					tabs={resourceTabs}
					activeId={currentTabId}
					onActive={(tab) => {
						setCurrentTabId(tab.id);
					}}
				>
					{resourceTabs.map(tab => (
						<TabPanel key={tab.id} id={tab.id}></TabPanel>
					))}
				</Tabs>
			</div>
			<div className="intlc-assessment-tabcoll__table">
				{currentTabId === 'assess' ? (
					<>
						<Table.ActionPanel>
							<Justify
								left={
									<>
										<Button
											type="primary"
											disabled={
												!assessSelectedKeys.length
												|| (assessSelectedKeys?.some(instanceId => assessStatus[instanceId]))
											}
											onClick={() => {
												modifyInstances(strategyId, 'assess', 'add');
											}}
										>
											忽略
										</Button>
										<Button
											disabled={
												!assessSelectedKeys.length
												|| (assessSelectedKeys.length
													&& assessSelectedKeys.some(instanceId => !assessStatus[instanceId]))
											}
											onClick={() => {
												modifyInstances(strategyId, 'assess', 'delete');
											}}
										>
											添加
										</Button>
									</>
								}
								right={
									<TagSearchBox
										attributes={[
											{
												type: 'input',
												key: 'InstanceId',
												name: '实例ID/名称',
											},
										]}
										minWidth={420}
										value={assessTagSearchValue}
										onChange={(tagSearchValue) => {
											const currentTagSearchValue = cloneDeep(tagSearchValue);
											// 如果存在非法输入，自动替换为实例ID/名称
											if (tagSearchValue.some(v => !v.attr)) {
												// 实例ID/名称对应的标签属性
												const instanceIdAttribute = { type: 'input', key: 'InstanceId', name: '实例ID/名称' };
												const instanceIdAttributeKey = instanceIdAttribute.key;

												remove(
													currentTagSearchValue,
													v => v.attr?.key === instanceIdAttributeKey
												);

												currentTagSearchValue.forEach((v) => {
													if (!v.attr) {
														v.attr = cloneDeep(instanceIdAttribute);
													}
												});
											}

											setAssessTagSearchValue(currentTagSearchValue);

											// 获取标签键对应的标签值选项
											const currentTagKeyOption = currentTagSearchValue.find(t => t.attr.key === 'TagKey') || {};

											if (currentTagKeyOption?.values?.[0]?.key) {
												const tagOptions = tagSearchOptions
													.find(t => t.TagKey === currentTagKeyOption.values[0].key) || {};

												const tagValueOptions = tagOptions.TagValues.length
													? tagOptions.TagValues.map(value => ({
														key: value || '',
														name: value || '',
													}))
													: [];

												setAssessTagValueOptions(tagValueOptions);
											} else {
												setAssessTagValueOptions([]);
											}

											// 自动触发搜索
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter = getConvertTagSearchValue2Filter(currentTagSearchValue);

											// 更新输入值后从第一页开始展示
											setAssessPageIndex(1);
											onChangeAssesses(strategyId, currentFilter, 0, assessPageSize);
										}}
										onSearchButtonClick={(e, value) => {
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter = getConvertTagSearchValue2Filter(value);

											// 更新输入值后从第一页开始展示
											setAssessPageIndex(1);
											onChangeAssesses(strategyId, currentFilter, 0, assessPageSize);
										}}
									/>
								}
							/>
						</Table.ActionPanel>
						<Table
							bordered={true}
							records={[...assessRecords].sort(sortable.comparer(assessSorts)) || []}
							recordKey={strategyRiskFields.PriId}
							columns={columns || []}
							topTip={
								assessTableStatus !== 'none' && (
									<StatusTip
										// @ts-ignore
										status={assessTableStatus}
										onClear={() => setAssessTableStatus('loading')}
										onRetry={() => setAssessTableStatus('loading')}
									/>
								)
							}
							addons={[
								// @ts-ignore
								pageable({
									recordCount: assessTotalCount,
									pageIndex: assessPageIndex,
									pageSize: assessPageSize,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setAssessTableStatus('loading');
										setAssessPageIndex(pageIndex);
										setAssessPageSize(pageSize);
										setAssessSelectedKeys([]);

										const currentFilter = getConvertTagSearchValue2Filter(assessTagSearchValue);

										onChangeAssesses(strategyId, currentFilter, (pageIndex - 1) * pageSize, pageSize);
									},
								}),
								selectable({
									value: assessSelectedKeys,
									onChange: (keys, context) => {
										setAssessSelectedKeys(keys);
									},
								}),
							]}
						/>
					</>
				) : (
					<>
						<Table.ActionPanel>
							<Justify
								left={
									<>
										<Button
											type="primary"
											disabled={
												!ignoreSelectedKeys.length
												|| (ignoreSelectedKeys.length
													&& ignoreSelectedKeys.some(instanceId => !ignoreStatus[instanceId]))
											}
											onClick={() => modifyInstances(strategyId, 'ignore', 'delete')}
										>
											添加
										</Button>
										<Button
											disabled={
												!ignoreSelectedKeys.length
												|| (ignoreSelectedKeys.length && ignoreSelectedKeys.some(instanceId => ignoreStatus[instanceId]))
											}
											onClick={() => modifyInstances(strategyId, 'ignore', 'add')}
										>
											忽略
										</Button>
									</>
								}
								right={
									<TagSearchBox
										attributes={[
											{
												type: 'input',
												key: 'InstanceId',
												name: '实例ID/名称',
											},
										]}
										minWidth={420}
										value={ignoreTagSearchValue}
										onChange={(tagSearchValue) => {
											const currentTagSearchValue = cloneDeep(tagSearchValue);
											// 如果存在非法输入，自动替换为实例ID/名称
											if (tagSearchValue.some(v => !v.attr)) {
												// 实例ID/名称对应的标签属性
												const instanceIdAttribute = { type: 'input', key: 'InstanceId', name: '实例ID/名称' };
												const instanceIdAttributeKey = instanceIdAttribute.key;

												remove(currentTagSearchValue, v => v.attr && v.attr.key === instanceIdAttributeKey);

												currentTagSearchValue.forEach((v) => {
													if (!v.attr) {
														v.attr = cloneDeep(instanceIdAttribute);
													}
												});
											}

											setIgnoreTagSearchValue(currentTagSearchValue);

											// 获取标签键对应的标签值选项
											const currentTagKeyOption = currentTagSearchValue.find(t => t.attr.key === 'TagKey') || {};

											if (
												currentTagKeyOption
												&& currentTagKeyOption.values
												&& currentTagKeyOption.values[0]
												&& currentTagKeyOption.values[0].key
											) {
												const tagOptions = tagSearchOptions.find(t => t.TagKey === currentTagKeyOption.values[0].key) || {};

												const tagValueOptions = tagOptions.TagValues.length
													? tagOptions.TagValues.map(value => ({
														key: value || '',
														name: value || '',
													}))
													: [];

												setIgnoreTagValueOptions(tagValueOptions);
											} else {
												setIgnoreTagValueOptions([]);
											}

											// 自动触发搜索
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter = getConvertTagSearchValue2Filter(currentTagSearchValue);

											// 更新输入值后从第一页开始展示
											setIgnorePageIndex(1);
											onChangeIgnores(strategyId, currentFilter, 0, ignorePageSize);
										}}
										onSearchButtonClick={(e, value) => {
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter = getConvertTagSearchValue2Filter(value);

											// 更新输入值后从第一页开始展示
											setIgnorePageIndex(1);
											onChangeIgnores(strategyId, currentFilter, 0, ignorePageSize);
										}}
									/>
								}
							/>
						</Table.ActionPanel>
						<Table
							bordered={true}
							records={[...ignoreRecords].sort(sortable.comparer(ignoreSorts)) || []}
							recordKey={strategyRiskFields.PriId}
							columns={columns || []}
							topTip={
								ignoreTableStatus !== 'none' && (
									<StatusTip
										// @ts-ignore
										status={ignoreTableStatus}
										onClear={() => setIgnoreTableStatus('loading')}
										onRetry={() => setIgnoreTableStatus('loading')}
									/>
								)
							}
							addons={[
								// @ts-ignore
								pageable({
									recordCount: ignoreTotalCount,
									pageIndex: ignorePageIndex,
									pageSize: ignorePageSize,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setIgnoreTableStatus('loading');
										setIgnorePageIndex(pageIndex);
										setIgnorePageSize(pageSize);
										setIgnoreSelectedKeys([]);

										const currentFilter = getConvertTagSearchValue2Filter(ignoreTagSearchValue);

										onChangeIgnores(strategyId, currentFilter, (pageIndex - 1) * pageSize, pageSize);
									},
								}),
								selectable({
									value: ignoreSelectedKeys,
									onChange: (keys, context) => {
										setIgnoreSelectedKeys(keys);
									},
								}),
							]}
						/>
					</>
				)}
			</div>
		</>
	);
}
