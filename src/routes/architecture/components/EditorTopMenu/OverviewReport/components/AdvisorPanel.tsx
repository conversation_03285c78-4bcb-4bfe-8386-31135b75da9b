import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import {
	getReportFileAsync,
	getReportResultAsync,
} from '@src/api/advisor/estimate';

import { DescribeTaskStrategyRisks, DescribeTaskStrategyIgnores } from '@src/api/architecture/cloudReport';
import { CLOUD_TYPE } from '../../../../conf/report';

import { RiskFieldsDescItem } from '@src/types/architecture/cloudReport';
import { Text, message, Collapse } from '@tencent/tea-component';

import { tabTitleMap } from './AdvisorConfig';
import { StatusIcon } from './StatusIcon';
import { AdvisorTips } from './AdvisorTips';
// import { AdvisorGroupReport } from './AdvisorGroupReport';
// import { AdvisorStrategyReport } from './AdvisorStrategyReport';
// import FileSaver from 'file-saver';
// import { base64toBlob } from '@src/utils/base64toBlob';
import _ from 'lodash';
import { insertCSS } from '@src/utils/insertCSS';
insertCSS(
	'AdvisorPanel',
	`
.app-advisor-bg-danger {
	background-color: #f70505 !important;
}

.app-advisor-bg-warning {
	background-color: #e8a323 !important;
}
`
);

interface Prop {
	id: string
	appId: number
	currentTaskId: string
	CurrentProducts: Array<string>
	CurrentStrategyNames: Array<string>
	taskCompleted: boolean
	// strategySummary: object
	onChangeReportRunning: Function
	GroupStrategySummariesSon: Array<any>
	regionCodes: Array<any>
	fixedStraegyId: number
	AllStrategyColumns: Array<RiskFieldsDescItem>
	hasFixed: boolean
	scanType: number
	onChangehasFixed: Function
	handleConditionEditor: Function,
}

// 当前下载报告状态队列池
let currentReportStatusQueue = [];

export function AdvisorPanel({
	id,
	appId,
	currentTaskId,
	CurrentStrategyNames,
	CurrentProducts,
	taskCompleted,
	// strategySummary,
	regionCodes,
	onChangeReportRunning,
	GroupStrategySummariesSon,
	fixedStraegyId,
	scanType,
	// AllStrategyColumns,
	hasFixed,
	onChangehasFixed,
	handleConditionEditor,
}: Prop) {
	// 策略配置信息警告条件集合
	// const [detectionConditions, setDetectionConditions] = useState({});
	// 策略执行结果概览Id集合
	const [summaryStrategyIds, setSummaryStrategyIds] = useState([]);
	// 策略执行状态集合
	const [strategyStatus, setStrategyStatus] = useState({});
	// 策略风险等级集合
	const [strategyRiskLevels, setStrategyRiskLevels] = useState({});
	// 策略有风险实例数量集合
	// const [strategyRiskNum, setStrategyRiskNum] = useState({})
	// 策略被忽略实例数量集合
	// const [strategyIgnoreNum, setStrategyIgnoreNum] = useState({})
	// 策略评估中的实例列表
	const [strategyAssesses, setStrategyAssesses] = useState({});
	// 策略评估中的实例列表总数
	const [strategyAssessesTotalCount, setStrategyAssessesTotalCount] = useState({});
	// 策略评估中的实例列表总数-real
	const [strategyAssessesTotalCountReal, setStrategyAssessesTotalCountReal] = useState({});
	// 策略评估中的实例列表是否已加载首屏
	const [strategyAssessesFirstPageStatus, setStrategyAssessesFirstPageStatus] = useState({});
	// 策略忽略的实例列表
	const [strategyIgnores, setStrategyIgnores] = useState({});
	// 策略忽略的实例列表总数
	const [strategyIgnoresTotalCount, setStrategyIgnoresTotalCount] = useState({});
	//	策略忽略的实例列表总数-real
	const [strategyIgnoresTotalCountReal, setStrategyIgnoresTotalCountReal] = useState({});
	// 策略忽略的实例列表是否已加载首屏
	const [strategyIgnoresFirstPageStatus, setStrategyIgnoresFirstPageStatus] = useState({});
	// 下载报告状态队列池
	const [reportStatusQueue, setReportStatusQueue] = useState([]);
	// 策略面板激活Id组
	const [strategyActiveIds, setStrategyActiveIds] = useState([]);

	// 警告条件主题颜色映射
	const conditionThemeConfig = {
		'-1': 'weak',
		0: 'success',
		1: 'warning',
		2: 'warning',
		3: 'danger',
	};

	// // 策略组ID映射
	// const tabIdMap = new Map([
	// 	['overview', -1],
	// 	['security', 1],
	// 	['architecture', 2],
	// 	['resource', 3],
	// 	['cost', 4],
	// 	['performance', 5],
	// ]);

	// 当前策略评估中的实例列表
	const currentStrategyAssesses = _.cloneDeep(strategyAssesses);
	// 当前策略评估中的实例列表总数
	const currentStrategyAssessesTotalCount = _.cloneDeep(strategyAssessesTotalCount);
	// 当前策略评估中的实例列表总数-Real
	const currentStrategyAssessesTotalCountReal = _.cloneDeep(strategyAssessesTotalCountReal);
	// 当前策略评估中的实例列表是否已加载首屏
	const currentStrategyAssessesFirstPageStatus = _.cloneDeep(strategyAssessesFirstPageStatus);
	// 当前策略忽略的实例列表
	const currentStrategyIgnores = _.cloneDeep(strategyIgnores);
	// 当前策略忽略掉实例列表总数
	const currentStrategyIgnoresTotalCount = _.cloneDeep(strategyIgnoresTotalCount);
	// 当前策略忽略掉实例列表总数-real
	const currentStrategyIgnoresTotalCountReal = _.cloneDeep(strategyIgnoresTotalCountReal);
	// 当前策略忽略的实例列表是否已加载首屏
	const currentStrategyIgnoresFirstPageStatus = _.cloneDeep(strategyIgnoresFirstPageStatus);

	// 获取任务执行的有风险实例结果详情
	const getTaskUnsafeDetail = async (
		id: number,
		filter?: Array<string>,
		offset?: number,
		limit?: number,
		isFirstPage = false
	) => {
		// 如果该策略获取过首屏数据，直接返回
		if (currentStrategyAssessesFirstPageStatus?.[id] && isFirstPage) {
			return;
		}
		// 如果该策略未获取过首屏数据，重置策略状态
		if (isFirstPage) {
			const currentStrategyStatus = _.cloneDeep(strategyStatus);
			currentStrategyStatus[id] = 'running';
			setStrategyStatus(currentStrategyStatus);
		}

		try {
			const unsafeDetail = await DescribeTaskStrategyRisks({
				AppId: appId,
				StrategyId: id,
				Filters: filter.length ? [{ Name: 'fuzzy', Values: filter }] : [], // 这里只用到fuzzy 模糊查询实例id或名称
				IncludeAllFields: true,
				Offset: offset || 0,
				Limit: limit || 10,
				TaskID: currentTaskId || '',
			});

			if (unsafeDetail.Error) {
				message.error({ content: unsafeDetail.Error.Message });
				return;
			}
			// 更新当前策略评估资源清单
			if (unsafeDetail.RiskTotalCount) {
				currentStrategyAssesses[id] = JSON.parse(unsafeDetail.Risks) || [];
			} else {
				currentStrategyAssesses[id] = [];
			}
			setStrategyAssesses(currentStrategyAssesses);
			// 更新当前策略评估资源总数-real
			if (filter.length === 0) {
				currentStrategyAssessesTotalCountReal[id] = unsafeDetail.RiskTotalCount || 0;
				setStrategyAssessesTotalCountReal(currentStrategyAssessesTotalCountReal);
			}
			// 更新当前策略评估资源总数
			currentStrategyAssessesTotalCount[id] = unsafeDetail.RiskTotalCount || 0;
			setStrategyAssessesTotalCount(currentStrategyAssessesTotalCount);
			// 是否首屏
			if (isFirstPage) {
				currentStrategyAssessesFirstPageStatus[id] = true;
				setStrategyAssessesFirstPageStatus(currentStrategyAssessesFirstPageStatus);
				const currentStrategyStatus = _.cloneDeep(strategyStatus);
				currentStrategyStatus[id] = 'success';
				setStrategyStatus(currentStrategyStatus);
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 获取任务执行的被忽略实例结果详情
	const getTaskIgnoredDetail = async (
		id: number,
		filter?: Array<string>,
		offset?: number,
		limit?: number,
		isFirstPage = false
	) => {
		// 如果该策略获取过首屏数据，直接返回
		if (currentStrategyIgnoresFirstPageStatus?.[id] && isFirstPage) {
			return;
		}
		try {
			const ignoredDetail = await DescribeTaskStrategyIgnores({
				TaskID: currentTaskId || '',
				AppId: appId,
				StrategyId: id,
				Filters: filter.length ? [{ Name: 'fuzzy', Values: filter }] : [], // 这里只用到fuzzy 模糊查询实例id或名称
				Offset: offset || 0,
				Limit: limit || 10,
				IncludeAllFields: true,
			});
			if (ignoredDetail.Error) {
				message.error({ content: ignoredDetail.Error.Message });
				return;
			}
			// 更新当前策略评估资源清单
			if (ignoredDetail.RiskTotalCount) {
				currentStrategyIgnores[id] = JSON.parse(ignoredDetail.Risks) || [];
			} else {
				currentStrategyIgnores[id] = [];
			}
			setStrategyIgnores(currentStrategyIgnores);
			// 更新当前策略评估资源总数-real
			currentStrategyIgnoresTotalCountReal[id] = ignoredDetail.RiskTotalCount || 0;
			setStrategyIgnoresTotalCountReal(currentStrategyIgnoresTotalCountReal);
			// 更新当前策略评估资源总数
			currentStrategyIgnoresTotalCount[id] = ignoredDetail.RiskTotalCount || 0;
			setStrategyIgnoresTotalCount(currentStrategyIgnoresTotalCount);
			//
			// 是否首屏
			if (isFirstPage) {
				currentStrategyIgnoresFirstPageStatus[id] = true;
				setStrategyIgnoresFirstPageStatus(currentStrategyIgnoresFirstPageStatus);
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 获取Excel异步下载结果
	const getResultAsync = async (id, currentReportStatusQueue, reportTimer) => {
		try {
			const reportResult = await getReportResultAsync({
				ResultId: id,
				AppId: appId,
			});
			if (reportResult.Error) {
				message.error({ content: reportResult.Error.Message });
				return;
			}

			const currentReport = currentReportStatusQueue.find(report => report.ReportId === id) || {};

			currentReport.CosUrl = reportResult.CosUrl || '';
			currentReport.CosUrlPdf = reportResult.CosUrlPdf || '';
			currentReport.TaskStatus = reportResult.TaskStatus || '';
			if (
				reportTimer
				&& currentReportStatusQueue.every(report => report.TaskStatus === 'success' || report.TaskStatus === 'failed')
			) {
				clearInterval(reportTimer);
				setReportStatusQueue(currentReportStatusQueue);

				onChangeReportRunning(false);
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 请求Excel异步下载
	const handleReportAsync = async (id, type) => {
		onChangeReportRunning(true);
		try {
			const reportAsync = await getReportFileAsync({
				Id: id,
				AppId: appId,
				Type: type,
				TaskId: currentTaskId,
				Products: CurrentProducts,
				// @ts-ignore
				StrategyIds: summaryStrategyIds,
			});
			if (reportAsync.Error) {
				message.error({ content: reportAsync.Error });
				return;
			}

			if (currentReportStatusQueue.find(report => id === report.Id && report.Type === type)) {
				let currentReport = currentReportStatusQueue.find(report => id === report.Id && report.Type === type);
				currentReport = {
					Id: id,
					Type: type,
					ReportId: reportAsync.ResultId || '',
					CosUrl: '',
					TaskStatus: '',
				};
			} else {
				currentReportStatusQueue = [
					...currentReportStatusQueue,
					{
						Id: id,
						Type: type,
						ReportId: reportAsync.ResultId || '',
						CosUrl: '',
						TaskStatus: '',
					},
				];
			}

			let reportTimer;

			getResultAsync(reportAsync.ResultId, currentReportStatusQueue, reportTimer);

			reportTimer = setInterval(() => {
				getResultAsync(reportAsync.ResultId, currentReportStatusQueue, reportTimer);
			}, 2000);
		} catch (err) {
			onChangeReportRunning(false);

			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 提取风险等级--以及评估项id列表，用于下载报告传参数
	useEffect(() => {
		setSummaryStrategyIds(GroupStrategySummariesSon.map(i => i.StrategyId));
		const currentStrategyRiskLevels = {};
		const currentStrategyStatus = {};
		GroupStrategySummariesSon.forEach((currentStrategy) => {
			// 获取当前策略风险等级
			if (currentStrategy.HighRiskCount > 0) {
				currentStrategyRiskLevels[currentStrategy.StrategyId] = 3;
			} else if (currentStrategy.MediumRiskCount > 0) {
				currentStrategyRiskLevels[currentStrategy.StrategyId] = 2;
			} else if (currentStrategy.LowRiskCount > 0) {
				currentStrategyRiskLevels[currentStrategy.StrategyId] = 1;
			} else {
				currentStrategyRiskLevels[currentStrategy.StrategyId] = 0;
			}
			currentStrategyStatus[currentStrategy.StrategyId] = currentStrategy.Code;
		});
		setStrategyStatus(currentStrategyStatus);
		setStrategyRiskLevels(currentStrategyRiskLevels);
	}, [GroupStrategySummariesSon]);


	// 开始评估后清空下载报告状态队列池、已加载首屏状态
	useEffect(() => {
		if (!taskCompleted) {
			currentReportStatusQueue = [];
			setReportStatusQueue([]);

			setStrategyAssessesFirstPageStatus({});
			setStrategyIgnoresFirstPageStatus({});
		}
	}, [taskCompleted]);

	// 标签搜索条件变更后，情况下载报告状态队列池
	useEffect(() => {
		currentReportStatusQueue = [];
		setReportStatusQueue([]);
	}, [CurrentProducts, CurrentStrategyNames]);

	// 重新搜索后清空评估中/已忽略实例列表已加载首屏状态和激活面板Id组
	useEffect(() => {
		setStrategyAssessesFirstPageStatus({});
		setStrategyIgnoresFirstPageStatus({});
		setStrategyActiveIds([]);
	}, [CurrentStrategyNames, CurrentProducts, GroupStrategySummariesSon]);

	// 策略配置信息排序条件
	const handleSortStrategies = (prev, curr) => {
		const prevId = prev.StrategyId || 0;
		const currId = curr.StrategyId || 0;
		const prevProduct = prev.Product || '';
		const currProduct = curr.Product || '';
		const prevLevel = strategyRiskLevels[prevId] >= 0 ? strategyRiskLevels[prevId] : -1;
		const currLevel = strategyRiskLevels[currId] >= 0 ? strategyRiskLevels[currId] : -1;

		// 排序条件 风险等级（由高到低）> 产品名称（字母排序，由小到大）> 策略ID（由小到大）
		if (prevLevel !== currLevel) {
			return currLevel - prevLevel;
		} if (prevProduct.toLowerCase() !== currProduct.toLowerCase()) {
			return prevProduct.toLowerCase() < currProduct.toLowerCase() ? -1 : 1;
		}
		return prevId - currId;
	};

	// 把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
	const simpleMarkdownToHTML = (input: string): string => {
		const reg = new RegExp(
			'(\\[[\u4e00-\u9fa5_a-zA-Z0-9,、/-\\s]+\\])(\\((\\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\\))',
			'g'
		);
		let result = input.replace(reg, (match, text, link) => `<a href="${link.slice(1, -1)}" target="_blank">${text.slice(1, -1)}</a>`);

		// 无序列表markdown语法转换
		if (result.includes('- ')) {
			result = result
				.split('- ')
				.filter(r => r.length)
				.map((r, index) => (!index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`))
				.join('');
		}
		return result;
	};

	// 监听AllStrategyColumns fixedStraegyId，定位到指定窗口 只有首次定位
	useEffect(() => {
		if (fixedStraegyId > 0 && !hasFixed) {
			const anchorElement = document.getElementById(fixedStraegyId.toString());
			// 如果对应id的锚点存在
			if (anchorElement) {
				anchorElement.scrollIntoView();
			}
			// 需要定位到指定窗口，加载内容，展开窗口
			// getTaskUnsafeDetail(fixedStraegyId, [], 0, 10, true)
			// getTaskIgnoredDetail(fixedStraegyId, [], 0, 10, true)
			strategyActiveIds.push(fixedStraegyId.toString());
			setStrategyActiveIds(strategyActiveIds);
			onChangehasFixed(true);
		}
	}, [fixedStraegyId]);

	// 监听hash
	window.addEventListener('hashchange', () => {
		const { hash } = window.location;
		if (hash) {
			const tmp = hash.split('#');
			if (tmp.length === 2) {
				const strategyId = parseInt(tmp[1]) || 0;
				if (strategyId > 0) {
					if (strategyActiveIds.indexOf(strategyId.toString()) === -1) {
						strategyActiveIds.push(strategyId.toString());
						setStrategyActiveIds(strategyActiveIds);
						// getTaskUnsafeDetail(strategyId, [], 0, 10, true)
						// getTaskIgnoredDetail(strategyId, [], 0, 10, true)
					}
				}
			}
		}
	});

	// 忽略实例列表出现变化（新增忽略或取消忽略）的策略ID，回调函数，进行刷新页面
	const strategyIgnoreChangeCallback = async (id) => {
		// 刷新页面
		// getTaskUnsafeDetail(id, [], 0, 10, false)
		// getTaskIgnoredDetail(id, [], 0, 10, false)
		// 刷新评估结果
	};

	return (
		<div id={id} className="intlc-assessment-tabitem">
			<div className="intlc-assessment-tabitem__advice">
				<Text style={{ display: 'inline-flex', alignItems: 'center' }}>
					{tabTitleMap.get(id)}
					{/* {taskCompleted && GroupStrategySummariesSon.length > 0 &&
						reportStatusQueue && (
							<AdvisorGroupReport
								id={id}
								type="Group"
								reportStatusQueue={reportStatusQueue}
								onChangeReportAsync={handleReportAsync}
							></AdvisorGroupReport>
						)} */}
				</Text>
			</div>
			{GroupStrategySummariesSon.length > 0 ? (
				GroupStrategySummariesSon.sort(handleSortStrategies).map(strategy => (
					<div id={strategy.StrategyId.toString()} key={strategy.StrategyId.toString()} >
						<Collapse
							key={strategy.StrategyId}
							activeIds={strategyActiveIds}
							icon={active => (
								<StatusIcon
									active={active}
									taskCompleted={taskCompleted}
									strategyStatus={strategyStatus[strategy.StrategyId]}
									strategyLevel={
										strategyRiskLevels[strategy.StrategyId] >= 0 ? strategyRiskLevels[strategy.StrategyId] : -1
									}
								></StatusIcon>
							)}
							iconPosition="left"
							onActive={(activeIds) => {
								setStrategyActiveIds(activeIds);
								// 点击后，获取不安全资源列表和资源忽略列表
								// getTaskUnsafeDetail(strategy.StrategyId, [], 0, 10, true)
								// getTaskIgnoredDetail(strategy.StrategyId, [], 0, 10, true)
							}}
							className="intlc-assessment-tabitem__content"
							// 暂时调整未评估策略与评估中策略左对齐
							style={{
								paddingLeft: !(taskCompleted && strategyStatus[strategy.StrategyId] === 'success')
									? 25
									: 0,
							}}

						>
							<Collapse.Panel
								id={strategy.StrategyId.toString()}
								title={active => (
									<>
										<div className="intlc-assessment-tabitem__title">
											<Text style={{ display: 'inline-flex', alignItems: 'center' }}>
												{strategy.Name}
												{/* {taskCompleted && strategyStatus[strategy.StrategyId] === 'success' && reportStatusQueue && (
														<AdvisorStrategyReport
															id={strategy.StrategyId}
															type="Strategy"
															reportStatusQueue={reportStatusQueue}
															onChangeReportAsync={handleReportAsync}
														></AdvisorStrategyReport>
													)} */}
												{/* 调整规则 */
													strategy.IsSupportCustom && <Text
														style={{ marginLeft: 10, display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
														onClick={(e) => {
															e.stopPropagation();
															handleConditionEditor(strategy);
														}}>
														<Text style={{ fontSize: 12, color: '#006cff', marginLeft: 5 }}>
															调整规则
														</Text>
													</Text>
												}
											</Text>
										</div>
										<div className="intlc-assessment-tabitem__introduction">
											<div className="intlc-assessment-tabitem__instructions">{strategy.Desc}</div>
											{taskCompleted && !active && (
												<div className="intlc-assessment-tabitem__instructions intlc-assessment-tabitem__instructions--multiple">
													<AdvisorTips
														strategyAssessesTotalCountReal={strategyAssessesTotalCountReal}
														strategyIgnoresTotalCountReal={strategyIgnoresTotalCountReal}
														strategy={strategy}
														strategyRiskLevels={strategyRiskLevels}
														strategyAssesses={strategyAssesses}
													/>
												</div>
											)}
										</div>
									</>
								)}
							>
								{taskCompleted && strategyActiveIds.some((i) => {
									if (i === strategy.StrategyId.toString()) {
										return i;
									}
								}) && strategyStatus[strategy.StrategyId] === 'success' && (
										<div
											className={
												strategyStatus[strategy.StrategyId] === 'success'
													? 'intlc-assessment-tabcoll intlc-assessment-tabcoll--complete'
													: strategyStatus[strategy.StrategyId] === 'init'
														|| strategyStatus[strategy.StrategyId] === 'running'
														? 'intlc-assessment-tabcoll intlc-assessment-tabcoll--loading'
														: 'intlc-assessment-tabcoll'
											}
										>
											{/* 警告条件 */}
											<div className="intlc-assessment-tabcoll__title">
												警告条件
											</div>
											{strategy.Conditions.sort((c1, c2) => c2.Level - c1.Level).map(condition => (
												<div key={condition.ConditionId} className="intlc-assessment-tabcoll__conditions">
													<Text
														bgTheme={conditionThemeConfig[condition.Level.toString()]}
														verticalAlign="top"
														className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--instructions"
													></Text>
													<div className="intlc-assessment-tabitem__instructions intlc-assessment-tabitem__instructions--inline">
														{condition.Desc}
													</div>
												</div>
											))}

											{/* 优化建议 */}
											<div className="intlc-assessment-tabcoll__title">
												优化建议
											</div>
											<div
												className="intlc-assessment-tabcoll__advice"
												dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(strategy.Repair) }}
											></div>

											{/* 资源列表 */}
											{
												scanType !== CLOUD_TYPE
												&& <>
													<div className="intlc-assessment-tabcoll__title">
														资源列表
													</div>
													<div className="intlc-assessment-tabitem__instructions">
														<AdvisorTips
															strategyAssessesTotalCountReal={strategyAssessesTotalCountReal}
															strategyIgnoresTotalCountReal={strategyIgnoresTotalCountReal}
															strategy={strategy}
															strategyRiskLevels={strategyRiskLevels}
															strategyAssesses={strategyAssesses}
														/>
													</div>
												</>
											}
											{
												scanType === CLOUD_TYPE
												&& <>
													<div className="intlc-assessment-tabcoll__title">
														存在风险节点
													</div>
													<div className="intlc-assessment-tabcoll__advice">
														{strategy.NodeNameList ? strategy.NodeNameList.join('、') : '无'}
													</div>
												</>
											}

											{/* <AdvisorTable
												strategyRiskFields={AllStrategyColumns.find(i => { if (i.StrategyId === strategy.StrategyId) { return i } }) || {
													StrategyId: 0,
													Url: '',
													RiskFieldsDesc: [],
													PriId: '',
													LinkId: '',
												}}
												product={strategy.Product}
												strategyId={strategy.StrategyId}
												strategy={strategy}
												regionCodes={regionCodes}
												currentTaskId={currentTaskId}
												strategyAssesses={strategyAssesses[strategy.StrategyId] || []}
												assessTotalCount={strategyAssessesTotalCount[strategy.StrategyId] || 0}
												strategyIgnores={strategyIgnores[strategy.StrategyId] || []}
												ignoreTotalCount={strategyIgnoresTotalCount[strategy.StrategyId] || 0}
												onChangeAssesses={getTaskUnsafeDetail}
												onChangeIgnores={getTaskIgnoredDetail}
												handStrategyIgnoreChange={strategyIgnoreChangeCallback}
											></AdvisorTable> */}
										</div>
									)}
							</Collapse.Panel>
						</Collapse></div>
				))
			) : 1 === 1 ? (
				<div className="intlc-assessment-tabitem">
					<div className="intlc-assessment-tabitem__empty">
						暂无内容
					</div>
				</div>
			) : (
				<div className="intlc-assessment-tabitem">
					<div className="intlc-assessment-tabitem__empty">
						暂无评估项，请在
						<Link style={{ color: '#006eff' }} to="advisor/switch">
							评估设置
						</Link>
						添加评估项。
					</div>
				</div>
			)}
		</div>
	);
}
