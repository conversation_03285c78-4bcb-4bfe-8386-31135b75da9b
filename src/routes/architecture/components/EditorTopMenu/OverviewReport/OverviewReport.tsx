import React, { useState, useEffect, useMemo, FC } from 'react';
import './style.less';

import { getProductsGroups, DescribeRiskDisplay } from '@src/api/architecture/cloudReport';
import { getArchTaskSummary, getTaskProgressSummary, getCustomerName } from '@src/api/advisor/estimate';

import { toUTCtime } from '@src/utils/toUTCtime';

import { Layout, Tabs, TabPanel, Card, Justify, TagSearchBox, Tag, message } from '@tencent/tea-component';
import { AdvisorPanel } from './components/AdvisorPanel';
import { ConfigPanel } from './components/ConfigPanel';
import { EmptyPanel } from './components/EmptyPanel';
import { AdvisorReport } from './components/AdvisorReport';
import { AdvisorLable } from '@src/routes/advisor/components/AdvisorLable';

import { assessmentTabsList, CLOUD_TYPE, RESOURCE_LIST, CLOUD_LIST } from '../../../conf/report';

import { map } from 'lodash';

const { Body, Content } = Layout;
interface IResourceOverviewReportProps {
	appId: number;
	MapUUId: string;
	cloudDetail: any;
	successTaskId?: string;
	location?: any;
	scanType: number;
	onLastSuccTaskIdChange: Function;
}

const OverviewReport: FC<IResourceOverviewReportProps> = ({
	appId,
	MapUUId,
	successTaskId,
	cloudDetail,
	scanType,
	onLastSuccTaskIdChange,
}: IResourceOverviewReportProps) => {
	// 自动开启巡检
	// 当前任务ID
	const [currentTaskId, setCurrentTaskId] = useState('');

	const [lastSuccTaskId, setLastSuccTaskId] = useState('');

	// 当前评估时间
	const [currentTaskTime, setCurrentTaskTime] = useState(0);
	// 策略组任务结果概览
	const [groupSummary, setGroupSummary] = useState({});
	// 策略任务结果概览
	const [strategySummary, setStrategySummary] = useState({});

	// 下载报告状态队列池中是否有正在执行的任务
	const [isReportRunning, setIsReportRunning] = useState(false);

	// 是否首屏获取上一次策略任务中
	// const [lastTaskStatus, setLastTaskStatus] = useState(false);
	// 云产品清单
	const [Products, setProducts] = useState([]);
	// 标签搜索框选中内容
	const [TagSelectValue, setTagSelectValue] = useState([]);
	const [CurrentProducts, setCurrentProducts] = useState([]);
	const [CurrentStrategyNames, setCurrentStrategyNames] = useState([]);
	// 评估结果详情--按维度区分
	const [GroupStrategySummaries, setGroupStrategySummaries] = useState({});
	// 地区对应编码
	const [regionCodes, setRegionCodes] = useState([]);
	// 全量评估项表头--原始内容
	const [AllStrategyColumns, setAllStrategyColumns] = useState<Array<any>>([]);
	// 定位锚点
	const [fixedStraegyId, setFixedStraegyId] = useState(0);
	// 是否已定位过锚点 //避免重复定位
	const [hasFixed, setHasFixed] = useState(false);
	// 搜索框value
	const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);
	// 搜索框是否变化
	const [tagSelectBoxValueHasChange, setTagSelectBoxValueHasChange] = useState(false);
	// 自定义阈值弹窗
	const [showConditionEditor, setShowConditionEditor] = useState<boolean>(false);
	const [currentStrategyId, setCurrentStrategyId] = useState<number>(0);
	const [currentStrategy, setCurrentStrategy] = useState<any>({});

	const [customerName, setCustomerName] = useState('--');

	// 查询任务结果定时器
	let taskTimer;

	let reportTimer;
	// 策略组名称映射
	const tabNameMap = new Map([
		// 资源
		[-1, 'overview'],
		[1, 'security'],
		[2, 'architecture'],
		[3, 'resource'],
		[4, 'cost'],
		[5, 'performance'],
		// 架构
		[6, 'property'], // 性能
		[7, 'disaster'], // 容灾容错
		[8, 'loadBalance'], // 负载均衡
		[9, 'securityArchitect'], // 架构安全
	]);

	const assessmentLabelsList = useMemo(() => {
		const reportList = scanType === CLOUD_TYPE ? CLOUD_LIST : RESOURCE_LIST;
		return map(reportList, ({ id, iconId }) => ({
			id,
			label: <AdvisorLable id={id} iconId={iconId} groupSummary={groupSummary} />,
		}));
	}, [groupSummary]);

	// 获取地区对应编码
	// const getCodes = async () => {
	// 	if (regionCodes.length) return;
	// 	try {
	// 		const codes = await DescribeRegionsAndZones({
	// 			AppId: appId
	// 		})
	// 		if (codes.Error) {
	// 			message.error({ content: codes.Error.Message });
	// 			return;
	// 		}
	// 		let tmp = []
	// 		codes.ResourceRegionSet.map(item => {
	// 			tmp.push({ Region: item.Region, Code: item.RegionId })
	// 		})
	// 		setRegionCodes(tmp)
	// 	} catch (err) {
	// 		const message = err.msg || err.toString() || '未知错误';
	// 		message.error({ content: message });
	// 	}
	// }

	// // 开始评估创建任务ID
	// const createCurrentTask = async () => {
	// 	setCurrentTaskTime(0)
	// 	setCurrentTaskId('')
	// 	setGroupSummary({})
	// 	setTaskProgress(0)
	// 	setTaskEstimatedTime(-1)
	// 	setGroupStrategySummaries({})
	// 	setAuto(false) //只要执行一次，便不再自动执行
	// 	try {
	// 		const createTask = await CreateScanTaskJob({ appId: 1231233 })
	// 		if (createTask.Error) {
	// 			message.error({ content: createTask.Error.Message });
	// 			return
	// 		}
	// 		//创建评估任务后，重新开始查询，且创建定时查询任务
	// 		getCurrentTaskSummary([])
	// 		taskTimer = setInterval(() => {
	// 			getCurrentTaskSummary([])
	// 		}, 5000)
	// 		setCurrentTaskId(createTask.TaskId)
	// 	} catch (err) {
	// 		const message = err.msg || err.toString() || '未知错误'
	// 		message.error({ content: message });
	// 	}
	// }

	// 获取当前巡检进度
	const getTaskProgress = async (filters, taskId) => {
		// 任务执行进度概览
		const progressInfo = await getTaskProgressSummary({
			AppId: appId,
			TaskId: taskId,
		});
		if (progressInfo.Error) {
			message.error({ content: progressInfo.Error.Message });
			return;
		}
		if (progressInfo.Finished) {
			// 进度依赖Summary执行完成，完成前都为99%
			if (taskTimer) {
				clearInterval(taskTimer);
				getCurrentTaskSummary(filters);
			}
		} else {
		}
	};

	// 查询当前任务执行结果和进度概览
	const getCurrentTaskSummary = async (Filters) => {
		let scanTip;
		try {
			// 任务执行结果概览
			const res = await getArchTaskSummary({
				AppId: appId,
				ScanSource: 0,
				TaskId: successTaskId ? successTaskId : currentTaskId,
				TaskType: 'mapTaskType',
				Filters,
				MapUUId: MapUUId,
				ReportType: scanType,
			});

			// 判断接口异常
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			// 判断是否有存量任务
			if (!res.Has) {
				// 关闭定时任务
				if (taskTimer) {
					clearInterval(taskTimer);
				}
				return;
			}
			setCurrentTaskId(res.TaskId);
			setLastSuccTaskId(res.LastSuccessTaskId);
			onLastSuccTaskIdChange(res.LastSuccessTaskId);
			// 判断是否在执行
			if (!res.IsFinish) {
				// 任务执行进度概览
				getTaskProgress(Filters, res.TaskId);
				taskTimer = setInterval(() => {
					getTaskProgress(Filters, res.TaskId);
				}, 5000);
				scanTip = message.loading({
					content: '当前有巡检任务正在执行，页面展示为上次的巡检报告。',
					duration: 10 * 1000,
				});
			} else {
				// 关闭定时任务
				if (taskTimer && scanTip) {
					clearInterval(taskTimer);
					scanTip?.hide();
				}
			}

			if (res.GroupSummaries) {
				const currentGroupSummary = {};
				res.GroupSummaries.forEach((summary) => {
					currentGroupSummary[tabNameMap.get(summary.GroupId)] = {
						high: summary.HighRiskCount,
						medium: summary.MediumRiskCount,
						low: summary.LowRiskCount,
						none: summary.NoRiskCount,
					};
				});
				setGroupSummary(currentGroupSummary);
			}
			if (res.StrategySummaries) {
				const tmp = {};
				let all = [];
				for (const i in res.GroupStrategySummaries) {
					tmp[tabNameMap.get(parseInt(i))] = res.GroupStrategySummaries[i];
					all = all.concat(res.GroupStrategySummaries[i]);
				}
				tmp[tabNameMap.get(-1)] = all;
				setGroupStrategySummaries(tmp);
			}

			if (res.FinishTime.length > 0) {
				const finishTimeStamp = new Date(res.FinishTime).getTime() / 1000;
				setCurrentTaskTime(finishTimeStamp);
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({ AppId: appId });
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			const tmp = [];
			for (const i in res.ProductDict) {
				tmp.push({ key: i, name: res.ProductDict[i] });
			}
			setProducts(tmp);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 轮询获取当前任务执行结果
	useEffect(() => {
		// 初始化filters
		const filters = [];
		// 初始化的时候，未知任务状态，执行首次查询，且创建定时查询任务
		getCurrentTaskSummary(filters);
		getProductsGroupsInfo();
		// getCodes()
		return () => {
			clearInterval(taskTimer);
		};
	}, []);

	// 获取全量评估项表头
	const getAllStrategyColumns = async () => {
		try {
			const result = await DescribeRiskDisplay({ AppId: appId });
			if (result.Error) {
				message.error({ content: result.Error.Message });
				return;
			}
			setAllStrategyColumns(result.DisplayFields);
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 监听tag搜索框，同步到变量中，共子组件AdvisorPanel使用
	useEffect(() => {
		// 更新值
		const tmp1 =
			TagSelectValue.find((i) => {
				if (i.Name === 'product') {
					return i;
				}
			}) || [];
		if (tmp1.length) {
			setCurrentProducts(tmp1[0].Values);
		} else {
			setCurrentProducts([]);
		}
		const tmp2 =
			TagSelectValue.find((i) => {
				if (i.Name === 'strategyName') {
					return i;
				}
			}) || [];
		if (tmp2.length) {
			setCurrentStrategyNames(tmp2[0].Values);
		} else {
			setCurrentStrategyNames([]);
		}
	}, [TagSelectValue]);

	// 任务执行完成状态
	const taskCompleted = useMemo(() => currentTaskTime > 0, [currentTaskTime]);

	// 策略任务预估剩余时间格式化
	// const taskEstimatedDate = useMemo(() => {
	// 	return taskEstimatedTime === -1 ? '-' : taskEstimatedTime / 60 >= 1 ? Math.round(taskEstimatedTime / 60) : 1
	// }, [taskEstimatedTime])

	// 获取评估项表头清单
	useEffect(() => {
		getAllStrategyColumns();
	}, []);

	// 监听当前任务是否完成，是否开启自动巡检
	// useEffect(() => {
	// 	if (currentTaskTime === 0) {
	// 		return;
	// 	}
	// 	if (currentTaskTime > 0 && auto) {
	// 		setAuto(false)
	// 		createCurrentTask()
	// 	} else {
	// 		setAuto(false)
	// 	}
	// }, [currentTaskTime])

	// 监听任务是否完成   已完成且存在评估项id参数，则更新锚点id
	useEffect(() => {
		const fixedId = window.location.search.split('?strategyid=')[1] || '0';
		if (taskCompleted && fixedId != '0' && parseInt(fixedId) && !hasFixed && AllStrategyColumns.length > 0) {
			// 判断fixedId是否有效
			if (
				AllStrategyColumns.find((i) => {
					if (i.StrategyId === parseInt(fixedId)) return i;
				})
			) {
				setFixedStraegyId(parseInt(fixedId));
			}
		}
	}, [taskCompleted, AllStrategyColumns]);

	const getCustomerNameByAppid = async () => {
		try {
			const res = await getCustomerName({
				AppId: appId,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}

			setCustomerName(res.CustomerName);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		getCustomerNameByAppid();
	}, []);

	const handleTagSearchChange = (value) => {
		let strategyNameValues = [];
		let productValues = [];
		value.map((j) => {
			const Name = j.attr ? j.attr.key : 'strategyName'; // 默认按评估项名称模糊搜索
			const Values = j.values.map((k) => {
				if (j.attr) {
					if (j.attr.key === 'product') {
						return k.key;
					}
					return k.name;
				}
				return k.name;
			});
			if (Name === 'strategyName') {
				strategyNameValues = strategyNameValues.concat(Values);
			} else {
				productValues = Values;
			}
		});
		// 合并评估项名称模糊搜索
		const Filters = [
			{ Name: 'strategyName', Values: strategyNameValues },
			{ Name: 'product', Values: productValues },
		];
		getCurrentTaskSummary(Filters);
		setTagSelectValue(Filters);
		setTagSelectBoxValueHasChange(true);
		setTagSelectBoxValue(value); // 标签搜索框真实值
	};

	return (
		<Layout>
			<Body style={{ margin: '0 -10px' }}>
				<Content className="intlc-stack-content intlc-stack-fullscreen intlc-stack-has-min-width">
					<Content.Body>
						<div className="intlc-survey-overview">
							<div className="intlc-survey-date report-info">
								<div className="report-info-detail">
									<div className="report-info__item">
										<span>AppID：</span>
										<Tag theme="primary">{appId}</Tag>
									</div>
									<div className="report-info__item">
										<span>客户名称：</span>
										<Tag>{customerName}</Tag>
									</div>
								</div>
								<div className="report-info-detail">
									<div className="report-info__item">
										<span>架构图名称：</span>
										<Tag theme="primary">{cloudDetail.MapName}</Tag>
									</div>
									<div className="report-info__item">
										<span>最近一次评估：</span>
										<Tag>{currentTaskTime ? toUTCtime(currentTaskTime) : '-'}</Tag>
									</div>
								</div>
							</div>
						</div>
						<Justify
							left={
								<span
									onKeyDown={(e) => e.stopPropagation()}
									style={{ width: '100%', marginTop: 10, marginBlockEnd: -5 }}
								>
									<TagSearchBox
										minWidth={420}
										attributes={[
											{
												type: 'multiple',
												key: 'product',
												name: '云产品',
												values: Products,
											},
											{
												type: 'input',
												key: 'strategyName',
												name: '评估项名称',
											},
										]}
										value={tagSelectBoxValue}
										onChange={handleTagSearchChange}
										onSearchButtonClick={() => getCurrentTaskSummary(TagSelectValue)}
										hideHelp={true}
										tips="支持按云产品和评估项名称过滤，多个关键字用竖线 “|” 分隔"
									></TagSearchBox>
								</span>
							}
							right={<></>}
						/>
						<div className="intlc-assessment-layout">
							<Card>
								<Card.Body style={{ padding: 0 }}>
									<section>
										<Tabs
											className="intlc-assessment-tabs"
											tabs={assessmentLabelsList}
											tabBarRender={(children) => <a>{children}</a>}
										>
											{assessmentTabsList.map((tab) => (
												// 未激活时强制渲染
												<TabPanel key={tab.id} id={tab.id}>
													{GroupStrategySummaries[tab.id] && strategySummary ? (
														<AdvisorPanel
															id={tab.id}
															appId={appId}
															currentTaskId={currentTaskId}
															taskCompleted={taskCompleted}
															onChangeReportRunning={setIsReportRunning}
															CurrentProducts={CurrentProducts}
															CurrentStrategyNames={CurrentStrategyNames}
															GroupStrategySummariesSon={
																GroupStrategySummaries[tab.id] || []
															}
															regionCodes={regionCodes}
															fixedStraegyId={fixedStraegyId}
															AllStrategyColumns={AllStrategyColumns}
															hasFixed={hasFixed}
															scanType={scanType}
															onChangehasFixed={setHasFixed}
															handleConditionEditor={(i) => {
																setCurrentStrategy(i);
																setCurrentStrategyId(i.StrategyId);
																setShowConditionEditor(true);
															}}
														></AdvisorPanel>
													) : GroupStrategySummaries[tab.id] ? (
														<ConfigPanel
															id={tab.id}
															detectionStrategies={GroupStrategySummaries[tab.id]}
														/>
													) : (
														<EmptyPanel id={tab.id}></EmptyPanel>
													)}
												</TabPanel>
											))}
										</Tabs>
									</section>
								</Card.Body>
							</Card>
						</div>
					</Content.Body>
				</Content>
			</Body>
		</Layout>
	);
};
export default OverviewReport;
