.scan-button {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30px;
	padding: 0 20px;
    color: #006EFF;
    font-size: 12px;
    text-decoration: none;
    overflow: hidden;
	border: 1px solid #CFD5DE;
	margin-right: 16px;
    cursor: pointer;

	.icon-cluster-svg {
        margin-right: 4px;
        margin-top: 4px;
    }
}

.progress {
    content: '';
    position: absolute;
    display: flex;
    top: 0;
	bottom: 0;
    left: 0;
    white-space: nowrap;
    align-items: center;
    overflow: hidden;
    height: 100%;
    background: #4776E6;
    background: -webkit-linear-gradient(to right, #8E54E9, #4776E6);
    background: linear-gradient(to right, #8E54E9, #4776E6);
    transition: all 3s;
	z-index: 99;
}

.progress_box {
    display: flex;
    padding: 0 10px;
    white-space: nowrap;
    align-items: center;
    overflow: hidden;
}

.normal-icon {
	margin-right: 6px;
}

.color__blue {
    color: #006EFF;
}

.refresh-icon {
	width: 16px;
	height: 16px;
	margin-right: 6px;
	padding-top: 4px;
	z-index: 10;
}

.text__black,
.text__whilte {
	z-index: 10;
}

.text__black {
	color: rgba(0, 0, 0, 0.9);
}

.text__loading {
	color: #4776E6;
}

.text__whilte {
	color: #fff;
}

.progress_tip {
    .tea-bubble__inner {
        border-radius: 50px;
    }
}

.no-wrap {
    white-space: nowrap;
}

.notification_content {
    z-index: 888;
    .tea-notification__text {
        -webkit-line-clamp: 6
    }
}
@keyframes iconRotate {
    0% {
        transform: rotate(0deg);
		transform-origin: 50% 50%;
    }

    100% {
        transform: rotate(360deg);
		transform-origin: 50% 50%;
    }
}

.refresh-icon{
    animation: iconRotate 1s linear infinite;
}

@keyframes downloading {
    0% {
        transform: scale(.7);
    }

    100% {
        transform: scale(1);
    }
}

.fa-download {
    animation: downloading 1s ease infinite alternate-reverse;
}
