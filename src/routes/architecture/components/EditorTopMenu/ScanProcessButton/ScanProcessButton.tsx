import React, { useState, useEffect, FC, useRef } from 'react';
import './style.less';
import { Modal, Icon, message, notification } from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import { getArchTaskSummary, getTaskProgressSummary } from '@src/api/advisor/estimate';
import { CreateScanTaskJob } from '@src/api/architecture/architecture';
import { useToggle } from '../../../hooks/common';
import moment from 'moment';

interface IScanProcessButtonProps {
	appId: number;
	uin: string;
	MapUUId: string;
	isShowScanBtn: boolean;
	successTaskId?: string;
	projectType: string;
	onOpenReport: Function;
	onScanReportChange: Function;
}

const FORMAT_DATE = 'YYYY-MM-DD HH:mm:ss';

const ScanProcessButton: FC<IScanProcessButtonProps> = ({
	appId,
	uin,
	MapUUId,
	isShowScanBtn,
	successTaskId,
	projectType,
	onOpenReport,
	onScanReportChange,
}: IScanProcessButtonProps) => {
	const progressBtn = useRef(null);

	const [taskProgress, setTaskProgress] = useState(0);

	const [isShowProgress, setIsShowProgress] = useState(false);

	const [isScanFinished, scanFinish, scanNotFinish] = useToggle(true);

	// 当前任务ID
	// const [currentTaskId, setCurrentTaskId] = useState('');

	let taskTimer;

	// 发起架构图巡检
	const handleMapScanTask = async () => {
		if (!isScanFinished) {
			message.warning({ content: '当前存在任务在巡检，不能再次发起巡检。' });
			return;
		}
		try {
			const res = await CreateScanTaskJob({
				AppId: appId,
				Uin: `${uin}`,
				TaskType: 'mapTaskType',
				MapUUId,
				Env: 'all',
				DataSource: 'lastTask',
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				getCurrentTaskSummary();
				message.success({ content: `发起成功，任务ID：${res.TaskId}` });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getTaskProgress = async (taskId) => {
		// 任务执行进度概览
		const progressInfo = await getTaskProgressSummary({
			AppId: appId,
			TaskId: taskId,
		});
		if (progressInfo.Error) {
			message.error({ content: progressInfo.Error.Message });
			return;
		}
		if (progressInfo.Finished) {
			// 进度依赖Summary执行完成，完成前都为99%
			setTaskProgress(100);
			if (taskTimer) {
				clearInterval(taskTimer);
				getCurrentTaskSummary();
			}
		} else {
			const { TotalCount, ScannedCount } = progressInfo?.Overview;

			const currentTaskProgress = TotalCount ? Math.floor((ScannedCount / TotalCount) * 100) : 0;
			setTaskProgress(currentTaskProgress);
			// 巡检剩余时间（不准确）
			// const currentTaskEstimatedTime = progressInfo.EstimatedTime;
		}
	};

	// 查询当前任务执行结果和进度概览
	const getCurrentTaskSummary = async () => {
		if (!appId && !MapUUId) return;
		try {
			// 任务执行结果概览
			const res = await getArchTaskSummary({
				AppId: appId,
				ScanSource: 0,
				TaskId: successTaskId,
				TaskType: 'mapTaskType',
				MapUUId,
			});

			// 判断接口异常
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			// 判断是否有存量任务
			if (!res.Has) {
				// 无存量任务，允许开始评估按钮显示
				scanFinish();

				if (taskTimer) {
					clearInterval(taskTimer);
				}
				return;
			}
			// setCurrentTaskId(res.TaskId);

			const { LastSuccessTaskId, TaskId } = res;
			onScanReportChange({ lastTaskId: LastSuccessTaskId, taskId: TaskId });

			// 判断是否在执行
			if (!res.IsFinish) {
				// 禁止再次巡检
				scanNotFinish();

				// 任务执行进度概览
				getTaskProgress(res.TaskId);
				clearInterval(taskTimer);
				taskTimer = setInterval(() => {
					getTaskProgress(res.TaskId);
				}, 5000);
			} else {
				notification.success({
					title: '架构巡检完成',
					description: (
						<>
							<p>当前架构巡检任务已完成，可点击查看报告进行查看。</p>
							<p>开始时间：{moment(res.CreateTime).format(FORMAT_DATE)}</p>
							<p>结束时间：{moment(res.FinishTime).format(FORMAT_DATE)}</p>
							<p>任务ID：{res.TaskId}</p>
							<p>巡检时长：{moment(res.FinishTime).diff(moment(res.CreateTime), 'minutes')}分钟</p>
						</>
					),
					className: 'notification_content',
					extra: <Icon type="daily" onClick={() => onOpenReport({ lastTaskId: LastSuccessTaskId })} />,
					footer: '查看巡检报告',
					onFooterClick: () => onOpenReport({ lastTaskId: LastSuccessTaskId }),
				});
				// 当前任务执行完毕，允许再次执行
				scanFinish();
				// 关闭定时任务
				if (taskTimer) {
					clearInterval(taskTimer);
				}
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 轮询获取当前任务执行结果
	useEffect(() => {
		// 初始化的时候，未知任务状态，执行首次查询，且创建定时查询任务
		if (projectType !== '客户项目') {
			getCurrentTaskSummary();
		}
		return () => {
			clearInterval(taskTimer);
		};
	}, [MapUUId, appId]);

	//
	useEffect(() => {
		if (!isScanFinished) {
			progressBtn.current?.click();
		}
	}, [isScanFinished]);

	const handleMapUpdate = () => {
		Modal.alert({
			type: 'warning',
			message: '升级通知',
			description: <>
				云架构已升级到最新版本，请进入
				<a href="/advisor/new-architecture" target='_blank'>新版</a>
				绘制架构图。获取客户租户端服务授权后，将架构图发送给客户即可绑定资源执行巡检。
			</>,
		});
	};
	return (
		<>
			{!isShowScanBtn ? (
				<></>
			) : isScanFinished ? (
				<div className="scan-button color__blue" onClick={handleMapUpdate}>
					<div className="nav-item-svg__box">
						<IconTsa type="icon-cluster" className="icon-cluster-svg" />
						<span className="nav-item-ht no-wrap">架构巡检</span>
					</div>
				</div>
			) : (
				<div
					className="scan-button no-allow"
					onClick={() => setIsShowProgress(!isShowProgress)}
					ref={progressBtn}
				>
					{taskProgress === 0 ? (
						<>
							<Icon type="loading" className="normal-icon" />
							<span className="text__loading">任务加载中</span>
						</>
					) : (
						<>
							<IconTsa type="icon-refresh-blue" className="refresh-icon" />
							<span className="text__loading">巡检中</span>
							<div className="progress" style={{ width: '100%' }}>
								<div className="progress_box">
									<IconTsa type="icon-refresh" className="refresh-icon" />
									<span className="text__whilte">巡检中</span>
								</div>
							</div>
						</>
					)}
				</div>
			)}
		</>
	);
};
export default ScanProcessButton;

// 后端进度数据不准确，暂时不展示进度
// <Bubble
// 	placement="bottom"
// 	trigger="click"
// 	style={{ maxWidth: 500, width: 270 }}
// 	className="progress_tip"
// 	content={<>
// 		<Progress
// 			percent={taskProgress}
// 			text={percent => `${percent} %`}
// 			type="circle"
// 			strokeColor={taskProgress < 30 ? "#0abf5b" : taskProgress < 75 ? "#8E54E9" : '#4776E6'}
// 			strokeWidth={16}
// 			tips={
// 				<p style={{ lineHeight: "16px" }}>
// 					<Icon type="pending" style={{ marginRight: 6 }} />
// 					架构正在巡检中，请稍等...
// 				</p>
// 			}
// 		/>
// 	</>
// 	}
// >
// </Bubble >
