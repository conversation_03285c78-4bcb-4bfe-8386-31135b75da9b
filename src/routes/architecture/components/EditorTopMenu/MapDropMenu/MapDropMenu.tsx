import React, { useState, useEffect, useMemo, useRef, FC } from 'react';
import './style.less';
import { useInfiniteScroll } from 'ahooks';
import moment from 'moment';
import { Button, Bubble, Input, H3, StatusTip } from '@tencent/tea-component';

import { MapThumb } from '../..';
import { DescribeCloudMapBaseInfo } from '@src/api/architecture/architecture';
import { FILE_TYPE } from '@src/routes/architecture/conf/architecture';
import { filter } from 'lodash';

interface IMapDropMenuProps {
	MapUUId: string;
	isShowThumbDetail?: boolean;
	handleCloudMapChange: Function;
}

interface Result {
	list: any;
	nextId: boolean
}

const PAGE_SIZE = 10;
const MapDropMenu: FC<IMapDropMenuProps> = ({
	MapUUId,
	isShowThumbDetail = true,
	handleCloudMapChange,
}: IMapDropMenuProps) => {
	const operator = localStorage.getItem('engName');

	const [filterName, setFilterName] = useState('');
	const dropListRef = useRef<HTMLDivElement>(null);

	const { data, loading, loadMore, loadingMore, noMore } = useInfiniteScroll(
		(d) => {
			const page = d ? Math.ceil(d.list.length / PAGE_SIZE) + 1 : 1;
			return getLoadMoreList(d?.nextId, page);
		},
		{ target: dropListRef, isNoMore: (d) => d?.nextId }
	);

	const getLoadMoreList = async (nextId, page): Promise<Result> => {
		const params = {
			AppId: 1253985742,
			Filters: [],
			Offset: (page - 1) * PAGE_SIZE,
			Limit: PAGE_SIZE,
			Operator: operator,
			FileType: FILE_TYPE.SVG,
		};
		const res = await DescribeCloudMapBaseInfo(params);
		const mapList = filter(res.CloudMap, (i) => i.CanWrite) || [];
		const isNoMore = page * PAGE_SIZE > (res?.TotalCount || 0);

		return new Promise((resolve) => {
			resolve({
				list: mapList,
				nextId: isNoMore,
			});
		});
	};
	const dataList = useMemo(() => {
		let list = data?.list;
		if (filterName) {
			list = data?.list.filter((d) => d.MapName.includes(filterName));
		}
		return list;
	}, [data, filterName]);

	return (
		<div className="header-graphs-dropdown">
			<div className="hgd-hd">
				<Input
					size="full"
					value={filterName}
					onChange={(value) => setFilterName(value)}
					onKeyDown={(e) => e.stopPropagation()}
					placeholder="请输入架构图名检索"
				/>
			</div>
			<div className="hgd-bd scrollbar" ref={dropListRef}>
				{loading ? (
					<div className="dropdown-loading">
						<StatusTip status="loading" />
					</div>
				) : (
					<>
						<ul className="hgd-list">
							{dataList?.map((g, index) => (
								<Bubble
									key={index}
									placement="right-start"
									trigger="hover"
									style={{ maxWidth: 500, width: 385 }}
									content={
										<>
											<H3>{g.MapName}</H3>
											<div className="tpl-thumb-item">
												<MapThumb
													mapDetail={g}
													type="cloud"
													key={index}
													isShowDetail={isShowThumbDetail}
												/>
											</div>
										</>
									}
								>
									<li
										className="hgd-item"
										key={g.MapUUId}
										onClick={() => handleCloudMapChange(g.MapUUId, g.ProjectType)}
									>
										<div className="hgd-item-content">
											<span className={g.MapUUId === MapUUId ? 'current' : ''}>{g.MapName}</span>
											<i>
												上次修改时间:
												{moment(g.UpdateTime).format('YYYY/MM/DD')}
											</i>
										</div>
									</li>
								</Bubble>
							))}
						</ul>
						<div className="dropdown-bd">
							{!noMore && (
								<Button
									icon={loadingMore ? 'loading' : 'sortdown'}
									onClick={loadMore}
									disabled={loadingMore}
								/>
							)}
							{noMore && <span>没有更多数据</span>}
						</div>
					</>
				)}
			</div>
		</div>
	);
};
export default MapDropMenu;
