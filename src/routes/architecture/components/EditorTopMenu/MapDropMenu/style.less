.header-graphs-dropdown {
	
    background-color: #fff;
    padding: 20px 0;
    width: 314px;
	

	.dropdown-loading {
		display: flex;
		justify-content: center;
		margin: 30px auto;
	}

	.dropdown-bd {
		display: flex;
		justify-content: center;
		margin-top: 8px;
	}
    .hgd {
		&-hd {
			padding: 0 16px;
			margin-bottom: 12px;
			.tea-input {
				border-width: 0 !important;
				border-bottom-width: 1px !important;
				padding-left: 2px;
				box-shadow: none !important;
				&:focus,
				&:hover {
					border-right-width: 0 !important;
					border-color: #0052d9;
				}
			}
		}
		&-bd {
			// height: 496px;
			max-height: 496px;
			overflow: auto;
		}
		&-item {
			display: block;
			padding: 0 14px;
			transition: background-color 0.25s;
			&-content {
				display: flex;
				width: 100%;
				height: 100%;
				height: 40px;
				line-height: 40px;
				overflow: hidden;
				justify-content: space-between;
				
				cursor: pointer;
				span {
					font-size: 14px;
					color: rgba(0, 0, 0, 0.87);
					max-width: 156px;
					overflow: hidden;
					white-space: nowrap;
					text-overflow: ellipsis;
					&.current {
						color: #0052d9;
					}
				}
				i {
					font-size: 12px;
					color: #bbb;
					white-space: nowrap;
				}
			}
			&:hover {
				background-color: rgba(0, 0, 0, 0.07);
			}
		}
    }
}