.exportMenu {
	&-block {
		& + .exportMenu-block {
			margin-top: 28px;
		}
		.ant-radio-button-wrapper {
			min-width: 64px;
			text-align: center;
		}
	}
	&-title {
		font-size: 20px;
	}
	&-hd {
		margin-bottom: 16px;
		font-size: 16px;
	}
	&-checkbox {
		padding-left: 19px;
		& + .exportMenu-checkbox {
			margin-top: 12px;
		}
	}
	&-ft {
		margin-top: 48px;
		.ant-btn {
			& + .ant-btn {
				margin-left: 12px;
			}
		}
	}
	&-center {
		display: flex;
		align-items: center;
		justify-content: center;
	}
	&-column {
		flex-direction: column;
	}
	&-size {
		margin-top: 24px;
		display: flex;
		align-items: flex-end;
		justify-content: center;
		&-title {
			margin-bottom: 6px;
			color: #999999;
			font-size: 12px;
		}
		.ant-input-affix-wrapper,
		.ant-input {
			border-width: 0;
			border-color: transparent;
			border-right-color: transparent !important;
			border-bottom-width: 1px;
			border-bottom-color: #dddddd;
			outline: none;
			width: 96px;
		&:hover,
		&:focus {
			box-shadow: none;
			border-right-color: transparent;
			border-bottom-color: #0052d9;
		}
		&[disabled] {
			background-color: transparent;
			border-bottom-color: #dddddd !important;
		}
		}
		.ant-input-affix-wrapper-focused {
			box-shadow: none;
			border-right-color: transparent;
			border-bottom-color: #0052d9;
		}
	}
	&-input-block {
		& + .exportMenu-input-block {
			margin-left: 12px;
		}
	}
	
}

.operator-btn + .operator-btn {
	margin-left: 10px;
}

.svg__box {
	display: flex;
	justify-content: space-around;
	.icon-svg {
		display: flex;
		align-items: center;
		margin-right: 5px;
	}
}

// .ant-radio-group-solid
//   .ant-radio-button-wrapper-checked:not(.ant-radio-button-wrapper-disabled):hover {
//   background-color: #0052d9;
//   border-color: #0052d9;
//   cursor: default;
// }

// .ant-radio-button-wrapper-checked:not([class*=' ant-radio-button-wrapper-disabled']).ant-radio-button-wrapper:first-child {
//   border-right-color: #0052d9;
// }
// .ant-radio-button-wrapper:hover {
//   color: #0052d9;
// }
