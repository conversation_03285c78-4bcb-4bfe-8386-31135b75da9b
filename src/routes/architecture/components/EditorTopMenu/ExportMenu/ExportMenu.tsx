import React, { useState, useEffect, FC } from 'react';
import './style.less';
import { round } from 'lodash';
import { Button, Input, Segment, message, Drawer, InputAdornment, Modal, Checkbox } from '@tencent/tea-component';
import { downloadSVG, getViewPort } from '../../../utils';
import { IconTsa } from '@src/components/IconTsa';
interface IExportMenuProps {
	sigma: any;
	visible: boolean
	onClose: Function
	graph: any
}

const FILE_TYPE = [
	{ text: 'SVG', value: 'svg' },
	// {
	// 	text: 'PNG',
	// 	value: 'png',
	// 	disabled: true,
	// 	tooltip: '暂时不支持PNG格式导出',
	// },
	// {
	// 	text: 'JPEG',
	// 	value: 'jpeg',
	// 	disabled: true,
	// 	tooltip: '暂时不支持JPEG格式导出',
	// },
];

const SIZE_TYPE = [
	// { text: '自定义', value: 'custom' },
	{ text: 'HD 720p', value: 'hd' },
	{ text: 'FHD 1080p', value: 'fhd' },
	{ text: 'QHD 1440p', value: 'qhd' },
	{ text: 'UHD 4K', value: 'uhd' },
];

const ExportMenu: FC<IExportMenuProps> = ({ sigma, visible, onClose, graph }: IExportMenuProps) => {
	const [option, setOption] = useState({
		width: 1280,
		height: 720,
		format: 'svg',
	});
	const [image, setImage] = useState({
		includeGrid: false,
		backgroundTransparent: false,
		onlySelection: false,
	});
	const [sizeType, setSizeType] = useState('hd');
	// const [paper, setPaper] = useState('original');
	// const [orientation, setOrientation] = useState('portrait');
	const [scale, setScale] = useState(100);
	// const [imageSrc, setImageSrc] = useState('');

	const name = (graph?.MapName && graph?.TemplateName) || `download-${Date.now()}`;

	// const [downloadPDFMutation] = useMutation(M_DOWNLOAD_PDF, {
	// 	onCompleted: (res) => {
	// 		downloadUrl(res.downloadPDF);
	// 	},
	// 	onError: (err) => {
	// 		message.error(getErrorMessage(err));
	// 	},
	// });
	// const [downloadImageMutation] = useMutation(M_DOWNLOAD_IMAGE, {
	// 	onCompleted: (res) => {
	// 		downloadUrl(res.downloadImage);
	// 	},
	// 	onError: (err) => {
	// 		message.error(getErrorMessage(err));
	// 	},
	// });

	const handleSizeChange = (value) => {
		let width; let height;
		switch (value) {
			// case 'custom':
			// 	width = 0;
			// 	height = 0;
			// 	break;
			case 'hd':
				width = 1280;
				height = 720;
				break;
			case 'fhd':
				width = 1920;
				height = 1080;
				break;
			case 'qhd':
				width = 2560;
				height = 1440;
				break;
			case 'uhd':
				width = 3840;
				height = 2160;
				break;
			default:
		}
		setOption({ ...option, width, height });
		setSizeType(value);
	};

	const download = async () => {
		const tip = message.loading({ content: '正在生成图片，请稍后...' });
		const svgString = await sigma.getRenderedXML({ ...option, ...image });
		if (option.format === 'svg') {
			downloadSVG(svgString, name);
		} else if (option.format === 'pdf') {
			// downloadPDFMutation({
			// 	variables: {
			// 		data: {
			// 			svg: svgString,
			// 			format: paper,
			// 			orientation: paper === 'original' ? undefined : orientation,
			// 			name,
			// 			width: option.width,
			// 			height: option.height,
			// 		},
			// 	},
			// });
		} else {
			// downloadImageMutation({
			// 	variables: {
			// 		data: {
			// 			svg: svgString,
			// 			format: option.format,
			// 			width: option.width,
			// 			height: option.height,
			// 			backgroundTransparent: image.backgroundTransparent,
			// 			name,
			// 		},
			// 	},
			// });
		}
		tip.hide();
		message.success({ content: '图片已下载' });
	};

	const onlineView = async () => {
		const tip = message.loading({ content: '正在生成图片，请稍后...' });
		const svgString = await sigma.getRenderedXML({ ...option, ...image });
		const blob = new Blob([svgString], { type: 'image/svg+xml' });
		const url = URL.createObjectURL(blob);
		const viewWidth = getViewPort('width');
		const viewHeight = getViewPort('height');
		const iframeWidth = option.width < viewWidth ? option.width : viewWidth - 300;
		const iframeHeight = option.height < viewHeight ? option.height : viewHeight - 40;
		Modal.alert({
			// @ts-ignore
			caption: '在线预览',
			maskClosable: true,
			size: iframeWidth + 50,
			message: <iframe src={url} width={iframeWidth} height={iframeHeight} style={{ border: '1px solid #ddd' }}></iframe>,
			buttons: [],
		});
		tip.hide();
	};

	useEffect(() => {
		if (sigma) {
			const { w = 0, h = 0 } = sigma.getBackgroundGrid();
			const width = round(w);
			const height = round(h);
			if (
				(option.width !== width || option.height !== height)
				&& sizeType === 'custom'
			) {
				setOption({ ...option, width: round(w), height: round(h) });
			}
		}
	}, [sigma, option, sizeType]);

	return (
		<Drawer
			title={
				<span className="exportMenu-title">导出文件</span>
			}
			size='l'
			style={{ width: 550 }}
			placement="right"
			onClose={() => onClose()}
			visible={visible}
		>
			<div className="exportMenu-block">
				<div className="exportMenu-hd">文件格式</div>
				<div className="exportMenu-bd exportMenu-center">
					<Segment
						value={option.format}
						onChange={value => setOption({ ...option, format: value })}
						options={FILE_TYPE}
					/>
					{/* <Radio.Group
						value={option.format}
						onChange={value => setOption({ ...option, format: value })}
					>
						<Radio name="svg">SVG</Radio>
						<Radio.Button value="png">PNG</Radio.Button>
						<Radio.Button value="jpg">JPEG</Radio.Button>

						<Radio.Button value="pdf">PDF</Radio.Button>
					</Radio.Group> */}
				</div>
			</div>
			<div className="exportMenu-block">
				<div className="exportMenu-hd">尺寸</div>
				<div className="exportMenu-bd exportMenu-center exportMenu-column">
					<div>
						{/* {option.format === 'pdf' ? (
							<Radio.Group
								value={paper}
								size="small"
								buttonStyle="solid"
								onChange={(e) => setPaper(e.target.value)}
							>
								<Radio.Button value="original">Original</Radio.Button>
								<Radio.Button value="letter">Letter</Radio.Button>
								<Radio.Button value="a4">A4</Radio.Button>
							</Radio.Group>
						) : (

						)} */}
						<Segment
							value={sizeType}
							onChange={handleSizeChange}
							options={SIZE_TYPE}
						/>
						{/* <Radio.Group
							value={sizeType}
							onChange={handleSizeChange}
						>
							<Radio.Button value="custom">
								自定义
							</Radio.Button>
							<Radio.Button value="hd">HD 720p</Radio.Button>
							<Radio.Button value="fhd">FHD 1080p</Radio.Button>
							<Radio.Button value="qhd">QHD 1440p</Radio.Button>
							<Radio.Button value="uhd">UHD 4K</Radio.Button>
						</Radio.Group> */}
					</div>
					{sizeType === 'custom' && option.format !== 'pdf' && (
						<div className="exportMenu-size">
							<div className="exportMenu-input-block">
								<div className="exportMenu-size-title">Scale</div>
								<InputAdornment after="%">
									<Input value={`${scale}`} size='s' onChange={value => setScale(+value)} />
								</InputAdornment>

							</div>
						</div>
					)}

					{option.format !== 'pdf' && (
						<div className="exportMenu-size">
							<div className="exportMenu-input-block">
								<div className="exportMenu-size-title">Width</div>
								<Input
									value={`${option.width}`}
									size='s'
									disabled={sizeType !== 'custom'}
									onChange={value => setOption({ ...option, width: +value })
									}
								/>
							</div>
							<div className="exportMenu-input-block">
								<div className="exportMenu-size-title">×</div>
							</div>
							<div className="exportMenu-input-block">
								<div className="exportMenu-size-title">Height</div>
								<Input
									value={`${option.height}`}
									size='s'
									disabled={sizeType !== 'custom'}
									onChange={value => setOption({ ...option, height: +value })
									}
								/>
							</div>
						</div>
					)}
				</div>
			</div>
			{/* {option.format === 'pdf' && paper !== 'original' && (
				<div className="exportMenu-block">
					<div className="exportMenu-hd">Orientation</div>
					<div className="exportMenu-bd exportMenu-center exportMenu-column">
						<Radio.Group
							value={orientation}
							size="small"
							buttonStyle="solid"
							onChange={(e) => setOrientation(e.target.value)}
						>
							<Radio.Button value="portrait">Portrait</Radio.Button>
							<Radio.Button value="landscape">Landscape</Radio.Button>
						</Radio.Group>
					</div>
				</div>
			)} */}
			<div className="exportMenu-block">
				<div className="exportMenu-hd">其他选项</div>
				<div className="exportMenu-bd">
					<div className="exportMenu-checkbox">
						<Checkbox
							value={image.includeGrid}
							onChange={value => setImage({ ...image, includeGrid: value })
							}
						>
							包含网格
						</Checkbox>
					</div>
					<div className="exportMenu-checkbox">
						<Checkbox
							value={image.backgroundTransparent}
							onChange={value => setImage({ ...image, backgroundTransparent: value })
							}
						>
							背景透明
						</Checkbox>
					</div>
					<div className="exportMenu-checkbox">
						<Checkbox
							value={image.onlySelection}
							onChange={value => setImage({ ...image, onlySelection: value })
							}
						>
							仅导出选中
						</Checkbox>
					</div>
				</div>
			</div>
			<div className="exportMenu-ft exportMenu-center">
				<Button type="primary" onClick={download} className='operator-btn'>
					<div className="svg__box">
						<IconTsa type='icon-export' className='icon-svg' />
						<span>导出文件</span>
					</div>
				</Button>
				<Button onClick={onlineView} className='operator-btn'>
					<div className="svg__box">
						<IconTsa type='icon-image' className='icon-svg' />
						<span>在线预览</span>
					</div>
				</Button>
				<Button onClick={() => onClose()} className='operator-btn'>
					取消
				</Button>
			</div>
		</Drawer>
	);
};
export default ExportMenu;
