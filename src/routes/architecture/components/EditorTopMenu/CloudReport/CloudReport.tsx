import React, { FC, useState } from 'react';
import './style.less';
import { Layout, Tabs, TabPanel, Drawer, message } from '@tencent/tea-component';
import { RESOURCE_TYPE, CLOUD_TYPE } from '../../../conf/report';
import { AdvisorReport } from '../OverviewReport/components/AdvisorReport';
import OverviewReport from '../OverviewReport';
import {
	getReportFileAsync,
	getReportResultAsync,
} from '@src/api/advisor/estimate';
interface ICloudReportProps {
	appId: number;
	MapUUId: string;
	cloudDetail: any;
	successTaskId?: string;
	closeReportDrawer: Function;
}
const { Body, Content } = Layout;;

export const tabConfig = [
	{ id: 'cloudReport', label: '架构巡检报告' },
	{ id: 'resourceReport', label: '资源巡检报告' },
];

const CloudReport: FC<ICloudReportProps> = ({
	appId,
	MapUUId,
	successTaskId,
	cloudDetail,
	closeReportDrawer,
}: ICloudReportProps) => {
	let reportTimer;
	const [lastSuccTaskId, setLastSuccTaskId] = useState<string>('');
	const [currentReport, setCurrentReport] = useState<any>({});
	console.log(lastSuccTaskId);

	// 请求Excel异步下载
	const handleReportAsync = async () => {
		try {
			const res = await getReportFileAsync({
				AppId: appId,
				TaskId: successTaskId ? successTaskId : lastSuccTaskId,
				CloudMapUuid: MapUUId,
				TopicType: 1,
			});
			if (res.Error) {
				message.error({ content: res.Error });
				return;
			}

			if (res.ResultId) {
				getResultAsync(res.ResultId);
				reportTimer = setInterval(() => {
					getResultAsync(res.ResultId);
				}, 2000);
			} else {
				message.error({ content: '生成巡检报告错误！' });
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	// 获取Excel异步下载结果
	const getResultAsync = async (resultId) => {
		try {
			const reportResult = await getReportResultAsync({
				ResultId: resultId,
				AppId: appId,
			});
			if (reportResult.Error) {
				message.error({ content: reportResult.Error.Message });
				return;
			}

			if (reportTimer && (reportResult.TaskStatus === 'success' || reportResult.TaskStatus === 'failed')) {
				clearInterval(reportTimer);
				setCurrentReport({
					...currentReport,
					CosUrl: reportResult.CosUrl || '',
					CosUrlPdf: reportResult.CosUrlPdf || '',
					TaskStatus: reportResult.TaskStatus || '',
				});
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};
	return (
		<Drawer
			visible={true}
			outerClickClosable={false}
			title="云架构巡检报告"
			className="drawer-header"
			subtitle={
				<div className="drawer-download">
					<AdvisorReport currentReport={currentReport} onChangeReportAsync={handleReportAsync} />
				</div>
			}
			style={{ width: 'calc(100% - 200px)' }}
			size="l"
			onClose={() => closeReportDrawer()}
		>
			<Layout>
				<Body>
					<Content>
						<Content.Body>
							<Tabs ceiling animated={false} tabs={tabConfig}>
								<TabPanel id="cloudReport">
									<OverviewReport
										appId={appId}
										MapUUId={MapUUId}
										successTaskId={successTaskId}
										cloudDetail={cloudDetail}
										scanType={CLOUD_TYPE}
										onLastSuccTaskIdChange={(lastTaskId) => setLastSuccTaskId(lastTaskId)}
									/>
								</TabPanel>
								<TabPanel id="resourceReport">
									<OverviewReport
										appId={appId}
										MapUUId={MapUUId}
										successTaskId={successTaskId}
										cloudDetail={cloudDetail}
										scanType={RESOURCE_TYPE}
										onLastSuccTaskIdChange={(lastTaskId) => setLastSuccTaskId(lastTaskId)}
									/>
								</TabPanel>
							</Tabs>
						</Content.Body>
					</Content>
				</Body>
			</Layout>
		</Drawer>
	);
};
export default CloudReport;
