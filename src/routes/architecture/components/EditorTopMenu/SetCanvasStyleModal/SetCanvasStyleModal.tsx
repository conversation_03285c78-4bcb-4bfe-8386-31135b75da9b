import React, { useState, FC } from 'react';
import './style.less';
import { Button, Form, Input, Checkbox, Modal } from '@tencent/tea-component';
interface ISettingModalProps {
    sigma: any;
    visible: boolean;
    onModalClose: Function;
}
const SetCanvasStyleModal: FC<ISettingModalProps> = ({ sigma, visible, onModalClose }: ISettingModalProps) => {
    const [data, setData] = useState({
        backgroundColor: '',
        quarterColor: '',
        entireColor: '',
        remember: false,
    });

    const handleOk = () => {
        sigma.setEditor(data);
        onModalClose();
    };
    const handleReset = () => {
        sigma.resetEditor();
        onModalClose();
    };

    const setColor = (key, color) => {
        if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color)) {
            const dataObj = {};
            dataObj[key] = color;
            setData({ ...data, ...dataObj });
        }
    };

    return (
        <Modal
            visible={visible}
            caption="编辑器设置"
            onClose={() => onModalClose()}
            className="setting-modal"
        >
            <Modal.Body>
                <Form>
                    <Form.Item label="背景色">
                        <Input
                            size="l"
                            placeholder="请输入16进制颜色，示例: #ffffff"
                            onChange={value => setColor('backgroundColor', value)}
                            onKeyDown={e => e.stopPropagation()}
                            defaultValue={data.backgroundColor}
                        />
                    </Form.Item>
                    <Form.Item label="网格颜色">
                        <Input
                            size="l"
                            placeholder="请输入16进制颜色，示例: #c3c3c3"
                            onChange={value => setColor('entireColor', value)}
                            onKeyDown={e => e.stopPropagation()}
                            defaultValue={data.entireColor}
                        />
                    </Form.Item>
                    <Form.Item label="辅助线色">
                        <Input
                            size="l"
                            placeholder="请输入16进制颜色，示例: #eeeeee"
                            onChange={value => setColor('quarterColor', value)}
                            onKeyDown={e => e.stopPropagation()}
                            defaultValue={data.quarterColor}
                        />
                    </Form.Item>
                    <Form.Item>
                        <Checkbox onChange={value => setData({ ...data, remember: value })}>记住设置</Checkbox>
                        <Button onClick={handleReset} type="link">恢复初始设置</Button>
                    </Form.Item>
                </Form>
            </Modal.Body>
            <Modal.Footer>
                <Button type="primary" onClick={handleOk}>确认</Button>
                <Button onClick={() => onModalClose()}>取消</Button>
            </Modal.Footer>
        </Modal>
    );
};
export default SetCanvasStyleModal;
