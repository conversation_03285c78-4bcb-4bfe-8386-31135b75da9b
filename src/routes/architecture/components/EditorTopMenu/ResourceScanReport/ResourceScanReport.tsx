import React, { useState, useEffect, useMemo, FC } from 'react';

import { Text, Bubble, Icon, Table, Card, Layout, Tabs, Status, message, Tag } from '@tencent/tea-component';
import { BasicLine, BasicPie } from '@tencent/tea-chart';
import moment from 'moment';
import { getSummaryMsg } from '@src/api/advisor/summary';
import {
	getReportFileAsync,
	getReportResultAsync,
	getProductsGroups,
	getCustomerName,
	describeReportTemplateList,
} from '@src/api/advisor/estimate';
import { AdvisorGroupReport } from '@src/routes/advisor/estimate/AdvisorGroupReport';
import { AdvisorLable } from '@src/routes/advisor/components/AdvisorLable';
import './style.less';
import { autotip } from '@tencent/tea-component/lib/table/addons';
import { orderBy, merge, sumBy, cloneDeep, remove, map, isEmpty, toPairs, forEach } from 'lodash';

import { insertCSS } from '@src/utils/insertCSS';
import { useToggle } from '@src/routes/advisor/pages/broadcast/hooks/common';
insertCSS(
	'AdvisorLable',
	`
.app-advisor-layout__content-body-inner {
	max-width: 100% !important;
	}

`
);

const RISK_TREND_MAP = {
	1: '安全',
	2: '可靠',
	3: '服务限制',
	4: '成本',
	5: '性能',
	'-1': '总览',
};

interface IResourceScanReportProps {
	appId: number;
	cloudDetail: any;
}

const { Content } = Layout;
const { scrollable } = Table.addons;

const ResourceScanReport: FC<IResourceScanReportProps> = ({ appId, cloudDetail }: IResourceScanReportProps) => {
	// 报告类别，默认为外部报告
	const [env, setEnv] = useState('public');
	const [reportDate, setReportDate] = useState('');
	// 维度列表
	const [Groups, setGroups] = useState<Array<{ Id: number, GroupName: string }>>([]);
	// 产品列表--下拉框
	const [TemplateOptions, setTemplateOptions] = useState([]);
	// 产品列表
	const [Products, setProducts] = useState([]);
	// 产品列表--下拉框
	const [ProductsOptions, setProductsOptions] = useState([]);
	// 产品映射表
	const [ProductsMap, setProductsMap] = useState(new Map());

	// 当前任务ID
	const [currentTaskId, setCurrentTaskId] = useState('');
	// 当前报告下载ID
	const [currentRequestId, setCurrentRequestId] = useState('');
	// 下载地址
	const [cosUrl, setCosUrl] = useState({ CosUrl: '', CosUrlPdf: '' });
	// 当前报告下载状态
	const [downStatus, setDownStatus] = useState('');
	// 最近一次巡检时间
	const [lastTaskTime, setTlastTaskTime] = useState('--');
	// 客户名称
	const [customerName, setCustomerName] = useState('--');
	// 策略组任务结果概览
	const [groupSummary, setGroupSummary] = useState({});
	// 当前评估数据
	const [currentProductSummaries, setCurrentProductSummaries] = useState([]);

	// 风险分布数据
	const [riskPie, setRiskPie] = useState([]);

	// 高危评估数据
	const [strategyTopSummaries, setStrategyTopSummaries] = useState([]);

	// 风险趋势数据
	const [riskDays, setRiskDays] = useState([]);

	// 风险趋势数据
	const [flag, setFlag] = useState(0);

	// 查询任务结果定时器
	let taskTimer;

	// 下载报告按钮，错误提示文本
	const [downloadFileReportError, setdownloadFileReportError] = useState(null);

	// 下载报告状态队列池
	const [reportStatusQueue, setReportStatusQueue] = useState([]);

	// 数据加载状态
	// const [isLoading, setIsLoading] = useState(true);

	const [isLoading, startLoad, endLoad] = useToggle(true);

	// 数据加载时显示的内容
	const [loadingMsg, setLoadingMsg] = useState('');

	// 策略组名称映射
	const tabNameMap = new Map([
		[-1, 'overview'],
		[1, 'security'],
		[2, 'architecture'],
		[3, 'resource'],
		[4, 'cost'],
		[5, 'performance'],
	]);

	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: appId,
				Env: env,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			setProducts(res.Products);
			setGroups(res.Groups);
			const tmpProductsOptions = [];
			const tmpProductsMap = new Map();
			for (const i in res.ProductDict) {
				tmpProductsOptions.push({ value: i, text: res.ProductDict[i] });
				tmpProductsMap.set(i, res.ProductDict[i]);
			}
			setProductsOptions(tmpProductsOptions);// 产品下拉框
			setProductsMap(tmpProductsMap);// 产品映射表
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取所有用户模板信息
	const getTemplatesInfo = async (queryAppId) => {
		try {
			const res = await describeReportTemplateList({
				AppId: queryAppId,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			const tmpTemplateOptions = [{ value: '0', text: '不使用模板' }];
			if (res.TemplateList) {
				res.TemplateList.map((item) => {
					tmpTemplateOptions.push({ value: item.Id, text: item.Name });
				});
			}
			setTemplateOptions(tmpTemplateOptions);// 模板下拉框*/
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 全部维度id列表
	const AllGroups = useMemo(() => {
		const tmp = [];
		Groups.forEach((i) => {
			tmp.push(i.Id.toString());
		});
		return tmp;
	}, [Groups]);

	useEffect(() => {
		getProductsGroupsInfo();
		getTemplatesInfo(appId);
		setDownStatus('');
		setCosUrl({ CosUrl: '', CosUrlPdf: '' });
		startLoad();
		setLoadingMsg('数据加载中...');
	}, [env, reportDate]);

	useEffect(() => {
		if (ProductsMap.size) {
			getSummaryMsgData();
		} else {
			startLoad();
			setLoadingMsg('暂无报告数据。');
		}
	}, [ProductsMap]);

	const getCustomerNameByAppid = async () => {
		try {
			const res = await getCustomerName({
				AppId: appId,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}

			setCustomerName(res.CustomerName);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		getCustomerNameByAppid();
	}, []);

	// 获取当前的报告数据
	const getSummaryMsgData = async () => {
		try {
			const summaryData = await getSummaryMsg({
				AppId: appId,
				Env: env,
				Date: reportDate,
				TaskType: 'mapTaskType',
				MapUUId: cloudDetail.MapUUId,
			});
			if (summaryData.Error) {
				const msg = summaryData.Error.Message || '';
				message.error({ content: msg });
				endLoad();
				return;
			}

			if (summaryData.Has) {
				const {
					TaskId,
					GroupSummaries,
					ProductSummaries,
					StrategyTopSummaries,
					RiskDaysList,
					FinishTime,
				} = summaryData;
				setCurrentTaskId(TaskId);
				// 概览数据
				if (!isEmpty(GroupSummaries)) {
					const currentGroupSummary = {};
					forEach(GroupSummaries, (summary) => {
						currentGroupSummary[tabNameMap.get(summary.GroupId)] = {
							high: summary.HighRiskCount,
							medium: summary.MediumRiskCount,
							low: summary.LowRiskCount,
							none: summary.NoRiskCount,
						};
					});
					setGroupSummary(currentGroupSummary);
				} else {
					setGroupSummary({});
				}

				// 产品评估
				if (!isEmpty(ProductSummaries)) {
					const ProductSummariesAll = orderBy(
						map(ProductSummaries, item => merge(item, {
							productName: ProductsMap.get(item.Product),
							type: ProductsMap.get(item.Product),
							value: item.RiskCount,
						})),
						['value'],
						['desc']
					);
					setCurrentProductSummaries(ProductSummariesAll);

					// 风险分布饼图
					const ProductSummariesTopFive = ProductSummariesAll.slice(0, 5);
					const ProductSummariesOther = ProductSummariesAll.slice(5);
					const otherValue = sumBy(ProductSummariesOther, 'value');
					const riskDistribute = cloneDeep(ProductSummariesTopFive);

					riskDistribute.push({ type: '其他', value: otherValue });
					remove(riskDistribute, item => item.value === 0);
					setRiskPie(riskDistribute);
				} else {
					setCurrentProductSummaries([]);
					setRiskPie([]);
				}

				// 评估top5
				setStrategyTopSummaries(StrategyTopSummaries);

				// 风险趋势
				if (!isEmpty(RiskDaysList)) {
					const riskDataList = [];
					map(toPairs(RiskDaysList), ([key, value]) => {
						if (!isEmpty(value)) {
							map(value, (i) => {
								riskDataList.push({ time: i.Date, value: i.Count, key: RISK_TREND_MAP[key] });
							});
						}
					});
					setRiskDays(riskDataList);
				} else {
					setRiskDays([]);
				}

				const lastTaskTime = moment(FinishTime).format('YYYY-MM-DD HH:mm:ss');
				setTlastTaskTime(lastTaskTime || '-');
			} else {
				if (summaryData.IsAuthorized === 0) {
					message.error({ content: '报告不存在！' });
					setLoadingMsg('提示：用户已开通云顾问授权，但该日期报告不存在，请确认最近一次巡检时间并重新选择日期。');
				} else if (summaryData.IsAuthorized === 1) {
					message.error({ content: '报告不存在！' });
					setLoadingMsg('提示：用户已开通云顾问授权，暂无报告数据，请等待自动巡检数据的生成。');
				} else if (summaryData.IsAuthorized === 2) {
					message.error({ content: '用户未开通云顾问授权！' });
					setLoadingMsg('提示：用户未开通云顾问授权。');
				} else {
					message.error({ content: '云顾问权限判断异常！' });
					setLoadingMsg('提示：云顾问权限判断异常。');
				}
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	// 异步请求生成下载报告
	const handleReportAsync = async (
		taskId,
		env,
		type,
		currentLanguage,
		currentProducts,
		currentGroups,
		currentTags
	) => {
		try {
			// 参数处理
			const tmp = [];
			currentGroups.forEach((i) => {
				tmp.push(parseInt(i));
			});
			currentGroups = tmp;

			// 全选，则传入空列表，后台表示全部维度
			if (currentGroups.length === Groups.length) {
				currentGroups = [];
			}
			// 全选，则传入空列表，后台标识全部产品
			if (currentProducts.length === Products.length) {
				currentProducts = [];
			}

			const reportAsync = await getReportFileAsync({
				AppId: appId,
				Id: -1,
				Type: 'Group',
				TaskId: taskId,
				Env: env,
				Products: currentProducts,
				GroupIDs: currentGroups,
				// GroupIDs: [1,2,3],
				Tags: currentTags.filter((i) => {
					if (i.TagKey != '') {
						return i;
					}
				}).map(i => ({ TagKey: i.TagKey, TagValues: i.TagValues })),
				Language: currentLanguage,
			});

			if (reportAsync.Error) {
				const msg = reportAsync.Error.Message || '';
				message.error({ content: msg });
				setdownloadFileReportError(msg);// 触发异步报告生成任务或下载报告失败，均提示错误
				return;
			}
			setCurrentRequestId(reportAsync.ResultId);
			taskTimer = setInterval(() => {
				setFlag(flag + 1);
				getResultAsync(reportAsync.ResultId);
			}, 2000);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			setdownloadFileReportError(msg);// 触发异步报告生成任务或下载报告失败，均提示错误
		}
	};

	// 下载报告
	const getResultAsync = async (requestId) => {
		try {
			const reportResult = await getReportResultAsync({
				AppId: appId,
				ResultId: requestId,
			});
			if (reportResult.Error) {
				setDownStatus('failed');
				const msg = reportResult.Error.Message || '';
				message.error({ content: msg });
				setdownloadFileReportError(msg);// 触发异步报告生成任务或下载报告失败，均提示错误

				return;
			}
			if (reportResult.TaskStatus === 'success') {
				setCosUrl({ CosUrl: reportResult.CosUrl, CosUrlPdf: reportResult.CosUrlPdf });
				setDownStatus('success');

				clearInterval(taskTimer);
			} else if (reportResult.TaskStatus === 'failed') {
				setDownStatus('failed');
				message.error({ content: '生成报告失败' });
				clearInterval(taskTimer);
			} else {
				if (flag > 10) {
					clearInterval(taskTimer);
				}
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			setdownloadFileReportError(msg);// 触发异步报告生成任务或下载报告失败，均提示错误
		}
	};

	const assessmentLabelsList = useMemo(() => {
		const summaryType = ['security', 'architecture', 'performance', 'cost', 'resource'];
		return map(summaryType, item => ({
			id: { item },
			label: <AdvisorLable id={item} groupSummary={groupSummary} />,
		}));
	}, [groupSummary]);

	// 产品评估表头
	const assessmentColumns = [
		{
			key: 'productName',
			header: '产品名',
		},
		{
			key: 'HighRiskCount',
			header: () => (<span style={{ color: '#e54545' }}>高风险</span>),
		},
		{
			key: 'MediumRiskCount',
			header: () => (<span style={{ color: '#ff9d00' }}>中风险</span>),
		},
		{
			key: 'Count',
			header: '资源数',
		},
		{
			key: 'RiskRate',
			header: () => (
				<>
					风险率
					<Bubble content={'风险率=当前风险数/(资源数*已开启资源型巡检项)'}>
						<Icon type="help" />
					</Bubble>
				</>
			),
			render: item => `${(item.RiskRate * 100).toString().match(/^\d+(?:\.\d{0,2})?/)}%`,
		},
		{
			key: 'StrategyCount',
			header: '已开启评估策略数',
		},
	];

	// top5 表头
	const topFiveColumns = [
		{
			key: 'StrategyName',
			header: '评估项名称',
		},
		{
			key: 'HighRiskCount',
			width: 100,
			header: () => (<span style={{ color: '#e54545' }}>高风险</span>),
		},
		{
			key: 'MediumRiskCount',
			width: 100,
			header: () => (<span style={{ color: '#ff9d00' }}>中风险</span>),
		},
		{
			key: 'Count',
			width: 100,
			header: '资源数',
		},
	];

	return (
		<Content className="intlc-survey-content intlc-stack-fullscreen intlc-stack-has-min-width cloud-report">
			<Content.Body>
				{isLoading
					? (<Card>
						<Status
							icon="blank"
							size="m"
							title={'暂无数据'}
							description="该状态的详细描述"
						/>
					</Card>)
					: (<div>
						<Card>
							<Card.Body
								title={
									<>
										<span className='report-overview-title'>资源巡检报告概览</span>
										<Text style={{ display: 'inline-flex', alignItems: 'center' }}>
											{reportStatusQueue && (
												<AdvisorGroupReport
													id=""
													AppId={appId}
													type="Group"
													env={env}
													currentTaskId={currentTaskId}
													TemplateOptions={TemplateOptions}
													Products={Products}
													ProductsOptions={ProductsOptions}
													Groups={Groups}
													AllGroups={AllGroups}
													reportStatusQueue={reportStatusQueue}
													onChangeReportAsync={handleReportAsync}
													getTemplatesList={getTemplatesInfo}
													downStatus={downStatus}
													cosUrl={cosUrl}
												></AdvisorGroupReport>
											)}
										</Text>
									</>
								}
							>
								<div className='intlc-survey-overview'>
									<div className='intlc-survey-date report-info'>
										<div className='report-info-detail'>
											<div className='report-info__item'>
												<span>AppID：</span>
												<Tag theme="primary">{appId}</Tag>
											</div>
											<div className='report-info__item'>
												<span>客户名称：</span>
												<Tag>{customerName}</Tag>
											</div>
										</div>
										<div className='report-info-detail'>
											<div className='report-info__item'>
												<span>架构图名称：</span>
												<Tag theme="primary">{cloudDetail.MapName}</Tag>
											</div>
											<div className='report-info__item'>
												<span>最近一次评估：</span>
												<Tag>{lastTaskTime}</Tag>
											</div>
										</div>
									</div>

									<div className="intlc-assessment-layout">
										<section>
											<Tabs className="intlc-assessment-tabs" tabs={assessmentLabelsList}></Tabs>
										</section>
									</div>
								</div>
							</Card.Body>
						</Card>
						<Card>
							<Card.Body title='云产品评估'>
								<Table
									records={currentProductSummaries}
									columns={assessmentColumns}
									addons={[
										autotip({ emptyText: '没有数据' }),
										scrollable({ maxHeight: 250 }),
									]}
								/>
							</Card.Body>
						</Card>

						<div className="intlc-survey-summary">
							<Card>
								<Card.Body title="风险评估Top5">
									<Table
										records={strategyTopSummaries}
										columns={topFiveColumns}
										addons={[
											autotip({
												emptyText: '没有数据',
											}),
										]}
									></Table>
								</Card.Body>
							</Card>

							<Card className="app-international-code-card">
								<Card.Body title="风险分布">
									<BasicPie
										circle
										height={300}
										dataSource={riskPie}
										position="value"
										color="type"
										dataLabels={{
											enable: true,
											formatter: (value, index, data) => `${data.serieName}: ${data.percent}%`,
										}}
										legend={{
											align: 'bottom',
										}}
									/>
								</Card.Body>
							</Card>
						</div>

						<Card>
							<Card.Body title='风险趋势'>
								<BasicLine
									smooth
									height={250}
									position="time*value"
									dataSource={riskDays}
									color="key"
									tooltip={{
										enableSort: true,
										header: { typeText: '类型', valueText: '风险数量' },
									}}
								/>
							</Card.Body>
						</Card>
					</div>)
				}
			</Content.Body>
		</Content>
	);
};
export default ResourceScanReport;
