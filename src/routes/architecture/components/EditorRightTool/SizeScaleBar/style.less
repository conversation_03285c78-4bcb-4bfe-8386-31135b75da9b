.silde-bar {
	position: absolute;
	box-sizing: border-box;
	background-color: #fff;
	border-right: 1px solid #ddd;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	justify-content: center;
	z-index: 9;
	transition: all linear 0.1s;

	&-slider {
		display: flex;
		flex-direction: column;
		align-content: flex-start;
		border-radius: 8px;

		&-top,
		&-middle {
			position: relative;
			&::after {
				content: '';
				display: block;
				position: absolute;
				bottom: 0px;
				right: 4px;
				width: 16px;
				height: 1px;
				background-color: #CFD5DE;
			}
		}
	}

	&-tools {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		box-shadow: 0px 3px 1px -2px rgba(0, 0, 0, 0.3),
			0px 2px 2px 0px rgba(0, 0, 0, 0.24), 0px 1px 5px 0px rgba(0, 0, 0, 0.22);
		padding: 13px 12px;
		border-radius: 8px;
		z-index: 1;

		.slider-pointer {
			height: 150px;
			padding: 0;
		}

		.silde-bar-icon {
			cursor: pointer;
		}

		.silde-icon {
			padding: 0 4px;
			margin: 16px auto;

			.icon-active {
				svg {
					fill: #006EFF;
				}
			}
		}
	}
}