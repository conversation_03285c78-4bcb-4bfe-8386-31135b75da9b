import React, { useState, useEffect, FC } from 'react';
import './style.less';
import render3DProductShape from '@tencent/sigma-editor';
import { IconTsa } from '@src/components/IconTsa';
import { Bubble } from '@tencent/tea-component';
interface ISliderBarProps {
	sigma: any
	initScale: number
	isPositionOffset?: boolean
}
const defaultPosition = {
	x: 18,
	y: 70,
}

const SizeScaleBar: FC<ISliderBarProps> = ({ sigma, initScale, isPositionOffset = false }: ISliderBarProps) => {
	const [scale, setScale] = useState(initScale);

	const positon = isPositionOffset ? { x: 298, y: 70 } : defaultPosition;
	const [isGrab, setIsGrab] = useState(false);
	useEffect(() => {
		setScale(initScale);
	}, [initScale]);

	useEffect(() => {
		if (!sigma) return;
		sigma.setCursorBehavior('move')
	}, [sigma]);

	return (
		<div className="silde-bar" style={{ top: `${positon.y}px`, right: `${positon.x}px` }}>
			<div className="silde-bar-tools">
				<div className="silde-bar-slider">
					<div className='silde-bar-slider-top'>
						<Bubble content={"选择"} placement="left" dark>
							<span
								className="silde-bar-icon icon-scale-small"
								onClick={() => {
									setIsGrab(!isGrab);
									sigma.setCursorBehavior('move')
								}}
							>
								<div className='silde-icon'>
									<IconTsa type="icon-choose" className={`${isGrab ? '' : 'icon-active'}`} />
								</div>
							</span>
						</Bubble>
						<Bubble content={"拖动"} placement="left" dark>
							<span
								className="silde-bar-icon icon-scale-small"
								onClick={() => {
									setIsGrab(!isGrab);
									sigma.setCursorBehavior('grab')
								}}
							>
								<div className='silde-icon'>
									<IconTsa type="icon-move" className={`${isGrab ? 'icon-active' : ''}`} />
								</div>
							</span>
						</Bubble>
					</div>
					<div className='silde-bar-slider-middle'>
						<Bubble content="适应屏幕 Command V" placement="left">
							<span
								className="silde-bar-icon"
								onClick={() => {
									sigma.zoomGraphFit();
								}}
							>
								<div className='silde-icon'>
									<IconTsa type="icon-full-screen-bing" />
								</div>
							</span>
						</Bubble>
					</div>
					<div className='silde-bar-slider-down'>
						<Bubble content={"缩小"} placement="left" dark>
							<span
								className="silde-bar-icon icon-scale-small"
								onClick={() => {
									setScale(scale + 0.1);
									sigma.scaleStep(0.1);
								}}
							>
								<div className='silde-icon'>
									<IconTsa type="icon-zoomout" />
								</div>
							</span>
						</Bubble>
						<Bubble content={"放大"} placement="left" dark>
							<span
								className="silde-bar-icon icon-scale-big"
								onClick={() => {
									setScale(scale - 0.1);
									sigma.scaleStep(-0.1);
								}}
							>
								<div className='silde-icon'>
									<IconTsa type="icon-zoomin" />
								</div>
							</span>
						</Bubble>
					</div>
				</div>
			</div>
		</div >
	);
};
export default SizeScaleBar;
