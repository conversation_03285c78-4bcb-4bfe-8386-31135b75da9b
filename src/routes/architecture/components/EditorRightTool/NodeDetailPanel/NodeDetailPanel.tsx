import React, { useState, useEffect, FC } from 'react';
import './style.less';
import { Text, Collapse, Drawer } from '@tencent/tea-component';
import { SigmaType } from '@tencent/sigma-editor';
import { getNodeLabelName } from '@src/routes/architecture/utils';
import { EditeStyleTool } from '@src/routes/architecture/components';
import { isEmpty } from 'lodash';
interface INodeDetailPanelProps {
	sigma: SigmaType,
	visible?: boolean,
	checkedNodes: any,
	riskDataList: any,
	domRef: any,
	defaultCheckedStyle: any
	closeDrawer?: Function;
}

const NodeDetailPanel: FC<INodeDetailPanelProps> = ({ sigma, visible, riskDataList, defaultCheckedStyle, domRef, checkedNodes, closeDrawer }: INodeDetailPanelProps) => {
	const [nodeLabelName, setNodeLabelName] = useState('');
	useEffect(() => {
		if (checkedNodes.length !== 1) {
			setNodeLabelName('');
			return;
		};
		if (!checkedNodes[0].linkTextLabel) {
			setNodeLabelName(checkedNodes[0]?.name);
			return;
		}
		let graphDataObj = {};
		try {
			graphDataObj = JSON.parse(sigma.getLocalGraphData());
		} catch (err) {
			// @ts-ignore
			graphDataObj = sigma.core.data.shapes; // 如意JSON失败，兜底使用原始数据
		}
		const currentLabel = graphDataObj[checkedNodes[0].linkTextLabel];
		setNodeLabelName(getNodeLabelName(currentLabel) || '');
	}, [checkedNodes]);

	return (
		<Drawer
			visible={visible}
			title="属性"
			className="node-detail-panel"
			style={{ width: 280 }}
			size="l"
			// popupContainer={domRef.current}
			outerClickClosable={false}
			onClose={() => closeDrawer()}
		>
			<div className='edit-panel-info' key="node-name">
				{`${checkedNodes.length === 1 ? `${nodeLabelName}` : `${checkedNodes.length} 个元素 `}`}
			</div>
			<Collapse defaultActiveIds={["style"]} className='edit-panel-list'>
				<Collapse.Panel id="style" title="外观&位置" className='edit-collapse-panel-item'>
					<EditeStyleTool
						sigma={sigma}
						checkedNodes={checkedNodes}
						defaultCheckedStyle={defaultCheckedStyle}
					/>
				</Collapse.Panel>
				{/* {
					isEmpty(riskDataList)
						? <></>
						: <>
							<Collapse.Panel id="nodeDetail" title="节点详情" className='edit-collapse-panel-item'>
								123123
							</Collapse.Panel>
							<Collapse.Panel id="risk" title="风险" className='edit-collapse-panel-item'>
								123123
							</Collapse.Panel>
						</>
				} */}
			</Collapse>
		</Drawer>
	);
};
export default NodeDetailPanel;
