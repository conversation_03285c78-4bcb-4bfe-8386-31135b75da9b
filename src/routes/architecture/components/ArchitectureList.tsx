import React, { useState, useEffect, useMemo } from 'react';
import { useHistory } from '@tea/app';
import '../style.less';
import {
	Card,
	Tag,
	Table,
	Button,
	message,
	StatusTip,
	Justify,
	Modal,
	TagSearchBox,
	Bubble,
	Text,
	Alert,
} from '@tencent/tea-component';
import MapListDownload from './Common/MapListComponents/MapListDownload';
import { CloudMapForm } from './EditorCenterSvg/CloudMapForm';
import { CloudMapInfoPanel, PushClientMapForm } from '../components';

import { AttributeValue } from '@tencent/tea-component/src/tagsearchbox/AttributeSelect';
import { PROJECT_TYPE, FILE_TYPE, BASEURL } from '../conf/architecture';

import { map, forEach, pick } from 'lodash';
import { useToggle } from '../hooks/common';
import { useActivate, useUnactivate, withActivation } from 'react-activation';

import {
	DescribeCloudMapBaseInfo,
	DeleteCloudMapBaseInfo,
	CreateCloudMapBaseInfo,
	DescribeTAMGroupInfo,
	DescribeCollaoratorInfo,
	ModifyCollaoratorInfo,
	DescribeDownLoadScoreExcel,
	CreatePushTemplate,
} from '@src/api/architecture/architecture';
import RTXPicker from '@tencent/qmfe-yoa-react-ui/es/RTXPicker/index';
import { initStrColor } from '@src/routes/architecture/utils';

const { pageable, scrollable } = Table.addons;
interface paramsProps {
	industryConfig: Array<string>;
}

export function ArchitectureList({ industryConfig }: paramsProps) {
	const history = useHistory();
	const [cloudMapList, setCloudMapList] = useState([]);
	const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);

	const [excelDownLoadInfo, setExcelDownLoadInfo] = useState<any>({});

	const [createDialogVisible, openCreateDialog, closeCreateDialog] = useToggle(false);

	const [collaoratorVisible, openCollaoratorDialog, closeCollaoratorDialog] = useToggle(false);

	const [pushCustomerVisible, openPushDialog, closePushDialog] = useToggle(false);
	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	const [isLoading, startLoad, endLoad] = useToggle(false);

	const operator = localStorage.getItem('engName');

	const [mapListFilter, setMapListFilter] = useState([]);

	const [tamGroup, setTAMGroup] = useState([]);

	const [projectTypeOption, setProjectTypeOption] = useState([]);

	const [collaoratorList, setCollaoratorList] = useState<any>([]);

	const [currentMap, setCurrentMap] = useState<any>({});

	useEffect(() => {
		getCloudMapList();
	}, [pageSize, pageNumber, mapListFilter]);

	const industryOption = useMemo(() => map(industryConfig, item => ({ key: item, name: item })), [industryConfig]);

	useActivate(() => {
		getCloudMapList();
	});

	useEffect(() => {
		getFilterOptionInfo();
	}, []);

	// 获取架构图列表
	const getCloudMapList = async () => {
		startLoad();
		try {
			const params = {
				AppId: 1253985742,
				Filters: mapListFilter,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
				Operator: operator,
				FileType: FILE_TYPE.NONE,
			};
			const res = await DescribeCloudMapBaseInfo(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				const cMap = map(res.CloudMap, (item, index) => ({
					...item,
					SortIndex: (pageNumber - 1) * pageSize + index + 1,
				}));
				setCloudMapList(cMap);
				setTotalPage(res.TotalCount);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	// 获取协作人信息
	const getCollaoratorInfo = async (cloudMap) => {
		const { AppId, MapUUId } = cloudMap;
		setCurrentMap(cloudMap);
		try {
			const params = {
				AppId,
				Operator: operator,
				MapUUId,
			};
			const res = await DescribeCollaoratorInfo(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setCollaoratorList(res.Collaorators);
				openCollaoratorDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 修改协作人
	const modifyCollaoratorInfo = async () => {
		const { AppId, MapUUId } = currentMap;
		try {
			const params = {
				AppId,
				Operator: operator,
				MapUUId,
				CollaoratorList: collaoratorList,
			};
			const res = await ModifyCollaoratorInfo(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '修改协作人成功！' });
				closeCollaoratorDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleDeleteCloudMap = (currentCloudMap) => {
		Modal.confirm({
			icon: 'warning',
			message: '确认删除当前架构图？',
			description: `删除 “${currentCloudMap.MapName}” 架构图后不能恢复，是否确定？`,
			okText: '删除',
			cancelText: '取消',
			onOk: () => deleteCloudMap(currentCloudMap),
		});
	};
	const deleteCloudMap = async (currentCloudMap) => {
		try {
			const { MapUUId, AppId } = currentCloudMap;
			const res = await DeleteCloudMapBaseInfo({
				AppId: +AppId,
				MapUUIds: [MapUUId],
				Operator: operator,
			});
			if (res.Error) {
				getCloudMapList();
				message.error({ content: res.Error.Message });
			} else {
				getCloudMapList();
				message.success({ content: '删除成功' });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleCollaoratorOperate = (item) => {
		getCollaoratorInfo(item);
	};

	const handlePushCustomer = (cloudMap) => {
		setCurrentMap(cloudMap);
		openPushDialog();
	};

	// 异步请求生成下载分数报告
	const handleSaleScoreClick = async (item) => {
		try {
			const { MapUUId, AppId } = item;
			const res = await DescribeDownLoadScoreExcel({
				MapUUId,
				AppId,
				Operator: operator,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			const excelDownLoadInfo = pick(res, ['Message', 'CosUrl']);
			setExcelDownLoadInfo(excelDownLoadInfo);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		const { CosUrl, Message } = excelDownLoadInfo;
		if (Message === 'success' && CosUrl) {
			window.open(CosUrl);
		}
	}, [excelDownLoadInfo]);

	const columns = [
		{
			key: 'MapUUId',
			align: 'left',
			header: '架构id',
			width: '7%',
		},
		{
			key: 'ProjectType',
			align: 'left',
			header: '项目类型',
			render: item => (item.ProjectType ? (
				<Tag theme={PROJECT_TYPE[item.ProjectType].theme} dark>
					{item.ProjectType}
				</Tag>
			) : (
				<></>
			)),
		},
		{
			key: 'AppId',
			align: 'left',
			header: '客户APPID | 名称',
			width: '15%',
			render: item => (
				<>
					<Tag> {item.AppId}</Tag>
					<br />
					{item.CustomerName && <Tag>{item.CustomerName}</Tag>}
				</>
			),
		},
		{
			key: 'MapName',
			align: 'left',
			header: '架构名称',
			width: '12%',
			render: item => (
				<div style={{ display: 'flex', alignItems: 'center' }}>
					<Text theme="text" overflow tooltip>
						{item.MapName}
					</Text>
					{item.ProjectType !== '客户项目' && (
						<Bubble
							placement="right"
							trigger="click"
							className="bubble-layer"
							style={{ maxWidth: 500, width: 450, zIndex: 10 }}
							content={<CloudMapInfoPanel cloudMapDetail={item} />}
						>
							<Button icon="externallink" />
						</Bubble>
					)}
				</div>
			),
		},
		{
			key: 'AfterSaleScore',
			align: 'left',
			header: '客户健康度风险评分',
			width: '10%',
			render: (item) => {
				const dataTheme =					parseInt(item.AfterSaleScore) > 90
					? 'success'
					: parseInt(item.AfterSaleScore) > 60
						? 'warning'
						: 'danger';
				return (
					<>
						{item.AfterSaleScore ? (
							<Text
								theme={dataTheme}
								style={{ fontWeight: 'bold', cursor: 'pointer' }}
								onClick={() => handleSaleScoreClick(item)}
							>
								{item.AfterSaleScore}
							</Text>
						) : (
							<Bubble
								content="有未完成的巡检任务，每天凌晨自动巡检完成后，可展示分数"
								placement="top"
								dark
							>
								<span>--</span>
							</Bubble>
						)}
					</>
				);
			},
		},
		{
			key: 'Creator',
			align: 'left',
			header: '创建人',
		},
		{
			key: 'CreaterDepartment',
			align: 'left',
			header: '创建人部门',
		},
		{
			key: 'Lead',
			align: 'left',
			header: '负责人',
		},
		{
			key: 'LeadDepartment',
			align: 'left',
			header: '负责人部门',
		},
		{
			key: 'Industry',
			align: 'left',
			header: '行业类型',
			render: item => (
				<>
					{item.Industry?.map((item, index) => (
						<div key={index}>
							<Tag theme="primary">{item}</Tag>
							<br />
						</div>
					))}
				</>
			),
		},
		{
			key: 'TAMGroup',
			align: 'left',
			header: '客户所属TAM组',
			width: '7%',
		},
		{
			key: 'Detail',
			header: '操作',
			align: 'center',
			width: '12%',
			fixed: 'right',
			render: item => (
				<div style={{ display: 'flex', justifyContent: 'center' }}>
					<Bubble
						arrowPointAtCenter
						placement="top"
						trigger="hover"
						content="仅APPID的tam同学可推送"
					>
						<Button
							type="link"
							disabled={!item.CanPush}
							onClick={() => handlePushCustomer(item)}
						>
							推送客户
						</Button>
					</Bubble>
					<Button
						type="link"
						disabled={!item.CanRead}
						onClick={() => history.push(`/advisor/architecture/cloud-editor/${item.MapUUId
						}?optype=1&projectType=${encodeURIComponent(item.ProjectType)}`)
						}
					>
						查看
					</Button>
					<Button
						type="link"
						disabled={!item.CanWrite}
						onClick={() => history.push(`/advisor/architecture/cloud-editor/${item.MapUUId
						}?optype=0&projectType=${encodeURIComponent(item.ProjectType)}`)
						}
					>
						编辑
					</Button>
					<Button type="link" disabled={!item.CanDelete} onClick={() => handleDeleteCloudMap(item)}>
						删除
					</Button>
					<Button type="link" disabled={!item.CanWrite} onClick={() => handleCollaoratorOperate(item)}>
						协作人
					</Button>
				</div>
			),
		},
	];

	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};
	const tableOptionProps = {
		style: { marginTop: 15 },
		recordKey: 'MapUUId',
		verticalTopL: true,
		columns,
		records: cloudMapList,
		topTip: isLoading ? (
			<StatusTip status="loading"></StatusTip>
		) : (
			cloudMapList.length === 0 && <StatusTip status="empty" />
		),
		className: 'table-scrollbar',
		addons: [
			pageable({
				pageIndex: pageNumber,
				recordCount: totalPage,
				onPagingChange: handlePageChange,
			}),
			scrollable({
				minWidth: 2000,
			}),
		],
	};

	const getCloudMapItem = async (values) => {
		const { MapUUId } = currentMap;
		try {
			const filters = [{ Name: 'map_uuid', Values: [MapUUId] }];

			const params = {
				AppId: 1253985742,
				Filters: filters,
				Operator: operator,
				FileType: FILE_TYPE.ALL,
			};
			const res = await DescribeCloudMapBaseInfo(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				const { Detail = '', SvgFile = '' } = res.CloudMap[0];
				await handlePushTplCreate(values, Detail, SvgFile);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	const handlePushTplCreate = async (values, Detail, SvgFile) => {
		try {
			const baseParams = {
				...values,
				...pick(currentMap, ['AppId', 'CustomerName', 'Industry']),
				Detail,
				SvgFile: initStrColor(SvgFile),
				creator: operator,
				PropertyDesc: currentMap.Comment,
			};
			const res = await CreatePushTemplate(baseParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '推送架构图成功' });
				closePushDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleCloudMapCreate = async (values, { custName, creatorDep, leadDep = '' }) => {
		try {
			const baseParams = {
				...values,
				CustomerName: custName,
				CreaterDepartment: creatorDep,
				LeadDepartment: leadDep,
				AppId: +values.AppId,
				Detail: '',
				SvgFile: '',
			};
			const reqParams = { ...baseParams, Creator: operator };
			const res = await CreateCloudMapBaseInfo(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '创建架构图成功' });
				history.push(`/advisor/architecture/cloud-editor/${res.MapUUId}?optype=0&projectType=${res.ProjectType}`);
				closeCreateDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getFilterOptionInfo = async () => {
		try {
			const res = await DescribeTAMGroupInfo({ AppId: 1253985742 });
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				const tamGroup = map(res.TAMGroups, ({ TAMName }) => ({ key: TAMName, name: TAMName }));
				setTAMGroup(tamGroup);
				res.ProjectTypes = ['售后项目', '行业项目', '转售后项目', '专家项目', '客户项目'];
				const projectTypeOption = map(res.ProjectTypes, item => ({ key: item, name: item }));
				setProjectTypeOption(projectTypeOption);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleSearchTagChange = (tags) => {
		const mapListFilter = [];
		forEach(tags, (tag) => {
			const name = tag.attr ? tag.attr.key : 'map_name';
			const valueList = map(tag.values, item => (tag.attr?.key === 'property' ? item.key : item.name));
			mapListFilter.push({
				Name: name,
				Values: valueList,
			});
		});
		setPageNumber(1);
		setMapListFilter(mapListFilter);
		setTagSelectBoxValue(tags);
	};

	// 标签搜索框
	const attributes: Array<AttributeValue> = [
		{
			type: ['single', { searchable: true }],
			key: 'project_type',
			name: '项目类型',
			values: projectTypeOption,
		},
		{
			type: 'input',
			key: 'appid',
			name: 'APPID',
		},
		{
			type: 'input',
			key: 'map_name',
			name: '架构名称',
		},
		{
			type: 'input',
			key: 'map_uuid',
			name: '架构ID',
		},
		{
			type: 'input',
			key: 'creator',
			name: '创建人',
		},
		{
			type: 'input',
			key: 'creater_department',
			name: '创建人部门',
		},
		{
			type: 'input',
			key: 'lead',
			name: '负责人',
		},
		{
			type: 'input',
			key: 'lead_department',
			name: '负责人部门',
		},
		{
			type: ['single', { searchable: true }],
			key: 'tam_group',
			name: '售后TAM组',
			values: tamGroup,
		},
		{
			type: ['multiple', { searchable: true }],
			key: 'industry',
			name: '行业类型',
			values: industryOption,
		},
		{
			type: 'input',
			key: 'customer_name',
			name: '客户名称',
		},
	];
	const tagSearchBoxProps = {
		minWidth: '55%',
		attributes,
		value: tagSelectBoxValue,
		hideHelp: true,
		tips: '支持AppId、创建人、负责人、架构id、架构名称过滤，多个关键词用竖线"|"分隔',
		onChange: handleSearchTagChange,
	};
	const handleCreateClick = () => {
		Modal.alert({
			type: 'warning',
			message: '升级通知',
			description: <>
				云架构已经升级到新版本，请进入
				<a href="/advisor/new-architecture" target='_blank'>新版</a>
				绘制架构图。获取客户租户端服务授权后，将架构图发送给客户即可绑定资源。
			</>,
		});
	};
	return (
		<Card>
			<Card.Body>
				<Justify
					left={
						<div style={{ display: 'flex', marginBottom: 10 }}>
							<Button type="primary" onClick={handleCreateClick}>
								新建
							</Button>
							<MapListDownload filter={mapListFilter} />
						</div>
					}
					right={<div>
						<TagSearchBox {...tagSearchBoxProps} />
					</div>}
				/>
				{/* @ts-ignore */}
				<Table {...tableOptionProps} />
			</Card.Body>
			<Modal visible={createDialogVisible} onClose={() => closeCreateDialog()}>
				<Modal.Body>
					<CloudMapForm
						cloudMapDetail={{}}
						industryConfig={industryConfig}
						isExistCloudMap={false}
						onSubmit={handleCloudMapCreate}
						closeCreateDialog={closeCreateDialog}
					/>
				</Modal.Body>
			</Modal>
			{
				collaoratorVisible
				&& <Modal visible={collaoratorVisible} onClose={() => closeCollaoratorDialog()}>
					<Modal.Body>
						<Alert>协作人：对架构图有编辑、资源增减以及报告查看权限。架构图负责人可增减相关人员</Alert>
						<RTXPicker
							placeholder="请输入需要添加的协作人英文ID，并按回车键分割"
							disabled={operator !== currentMap.Lead}
							initRtx={collaoratorList}
							valueType="array"
							onChange={tags => setCollaoratorList(tags)}
						/>
					</Modal.Body>
					<Modal.Footer>
						<Button type="primary" disabled={operator !== currentMap.Lead} onClick={modifyCollaoratorInfo}>
							确定
						</Button>
						<Button type="weak" onClick={() => closeCollaoratorDialog()}>
							取消
						</Button>
					</Modal.Footer>
				</Modal>
			}
			{
				pushCustomerVisible
				&& <PushClientMapForm
					cloudMapDetail={currentMap}
					onSubmit={getCloudMapItem}
					closeDialog={closePushDialog}
				/>
			}
		</Card>
	);
}
