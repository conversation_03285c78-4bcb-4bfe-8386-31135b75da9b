import React, { useState, FC, useMemo } from 'react';
import './style.less';
import { IconTsa } from '@src/components/IconTsa';
import { filter } from 'lodash';
interface ILayerMenuProps {
	sigma: any;
}
const IGNORE_LAYER = ['sigma-layer-Image', 'sigma-layer-Icon', 'sigma-layer-AutoScale'];
const LayerMenu: FC<ILayerMenuProps> = ({ sigma }: ILayerMenuProps) => {
	const [hideLayer, setHideLayer] = useState([]);

	const layerClick = (layer) => {
		const index = hideLayer.indexOf(layer.key);
		sigma.toggleLayerVisable(layer.layer);
		if (index === -1) {
			setHideLayer([...hideLayer, layer.key]);
		} else {
			hideLayer.splice(index, 1);
			setHideLayer([...hideLayer]);
		}
	};

	if (!sigma) {
		return null;
	}

	const layerList = useMemo(() => {
		if (!sigma) return [];
		return filter(sigma.getLayers(), item => !IGNORE_LAYER.includes(item.key))
	}, [sigma])

	return (
		<div className='layer-dropdown'>
			{
				layerList?.map(layer => (
					<div key={layer.key} onClick={() => layerClick(layer)} className='layer-dropdown-item'>
						{
							hideLayer.indexOf(layer.key) !== -1
								? <IconTsa type="icon-eye-invisible" className='icon-svg__light' />
								: <IconTsa type="icon-eye" className='icon-svg' />
						}
						{layer.cName}
					</div>
				))
			}
		</div>
	);
};
export default LayerMenu;
