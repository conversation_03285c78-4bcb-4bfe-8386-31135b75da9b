.setting-layer-wraper,
.setting-layer-option {
	position: absolute;
	bottom: 40px;
    cursor: move;
    box-sizing: border-box;
    z-index: 9;

	.setting-layer__inner {
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 10px 0;
		width: 50px;
		height: 50px;
		border-radius: 28px;
		background-color: #fff;
		box-sizing: border-box;
		text-align: center;
		font-size: 0;
		cursor: pointer;
		box-shadow: 0 46px 49px -2px rgb(19 41 75 / 15%), 0 14px 15px -2px rgb(19 41 75 / 14%), 0 6px 6px -2px rgb(19 41 75 / 14%), 0 2px 2px -2px rgb(19 41 75 / 13%);
		
		&:hover {
			background-color: #2b86ff;
    		transition: all .3s;
		}
	}
	.icon-item {
		&:hover {
			transition: all 0.8s;
			svg path {
				fill: #fff;
			}
		}
	}
}

.setting-layer-option {
	display: flex;
	justify-content: flex-start;
    right: 100px;

	.icon-item {
		margin-left: 15px;
	}
	.font-item {
		color: #fff;
		font-size: 18px;
		font-weight: bold;
		background-color: #f8b400;
	}

	.active {
		background-color: #f8b400;
	}
}