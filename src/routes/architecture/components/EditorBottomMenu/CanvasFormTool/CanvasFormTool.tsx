import React, { FC, useEffect } from 'react';
import './style.less';
import { SigmaType } from '@tencent/sigma-editor';
import { Bubble, SlideTransition, Dropdown, Modal, message } from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import LayerMenu from '../LayerMenu';
import HelpDialog from '../../Common/HelpDialog';
import { map, toPairs, includes, get } from 'lodash';
import { useToggle } from '../../../hooks/common';
import { supportScanPro } from '../../../conf/eidtorConfig';
import { setNodeStyleByKey } from '../../../utils';
import { setStorage, getStorage } from '@src/utils/storage';
interface ISettingLayerProps {
	sigma: SigmaType
	currentMode: string
	isPositionOffset: boolean
	isShowRiskEntry: boolean
	riskLayerVisible?: boolean
	onRiskLayerOpen?: Function
	onRiskLayerClose?: Function
	handleModeChange: Function
}

const defaultPosition = {
	x: 75,
	y: 40,
}
const CanvasFormTool: FC<ISettingLayerProps> = ({ sigma, currentMode, isShowRiskEntry, isPositionOffset = false, riskLayerVisible, onRiskLayerOpen, onRiskLayerClose, handleModeChange }: ISettingLayerProps) => {
	//const [visible, unfold, fold] = useToggle(true);
	const is2DMode = currentMode === '2d';
	const [isViewing, openView, closeView] = useToggle(false);
	const positon = isPositionOffset ? { x: 298, y: 40 } : defaultPosition;
	const renderLayer = (
		<Bubble content="设置浮层" placement="top" dark>
			<div className='setting-layer__inner icon-item'>
				<IconTsa type="icon-layer" />
			</div>
		</Bubble>
	);
	// const handleHelpBtnClick = () => {
	// 	Modal.alert({
	// 		// @ts-ignore
	// 		caption: '帮助',
	// 		maskClosable: true,
	// 		size: 'l',
	// 		message: <HelpDialog />,
	// 		buttons: [],
	// 	});
	// };
	const handleViewProductClick = (type) => {
		if (!sigma) return;
		try {
			const graphDataObj = JSON.parse(sigma.getLocalGraphData());
			map(toPairs(graphDataObj), ([key, item]) => {
				if (includes(supportScanPro, item.name)
					&& get(item, 'styles.fillDark.value') !== '#4286C5'
				) {
					if (type === 'view') {
						setNodeStyleByKey(sigma, item, { attr: 'fillDark', value: '#7ef3a1' });
					}
					else {
						setNodeStyleByKey(sigma, item, { attr: 'fillDark', value: '#E2E6EC' });
					}
				}
			});
			if (type === 'view') {
				openView();
			}
			else {
				closeView();
			}
		} catch (err) {
			message.error({ content: 'GraphData JSON Error' });
		}
	};

	const handleRiskBtnClick = () => {
		if (!riskLayerVisible) {
			if (is2DMode) return;
			onRiskLayerOpen();
		}
		else {
			onRiskLayerClose();
		}
	}
	return (
		<div className="setting-layer">
			<div className="setting-layer-option" style={{ bottom: `${positon.y}px`, right: `${positon.x}px` }}>
				{is2DMode ? (
					<Bubble content="点击切换成3D" placement="top" dark>
						<div
							className="setting-layer__inner font-item"
							onClick={() => {
								handleModeChange('3d');
							}}
						>
							2D
						</div>
					</Bubble>
				) : (
					<Bubble content="点击切换成2D" placement="top" dark>
						<div
							className="setting-layer__inner icon-3d"
							onClick={() => {
								handleModeChange('2d');
							}}
						>
							<IconTsa type="icon-3d" />
						</div>
					</Bubble>
				)}
				{isShowRiskEntry && (
					<Bubble content={riskLayerVisible ? '关闭节点风险浮层' : '查看节点风险'} placement="top" dark>
						<div
							className={`setting-layer__inner icon-item ${riskLayerVisible ? 'active' : ''}`}
							onClick={() => handleRiskBtnClick()}
						>
							<IconTsa type={riskLayerVisible ? 'icon-stop' : 'icon-unit'} />
						</div>
					</Bubble>
				)}
				<Bubble content={isViewing ? '关闭查看' : '查看支持绑定资源的产品'} placement="top" dark>
					<div
						className={`setting-layer__inner icon-item ${isViewing ? 'active' : ''}`}
						onClick={() => handleViewProductClick(isViewing ? 'quit' : 'view')}
					>
						<IconTsa type={isViewing ? 'icon-eye-invisible-l' : 'icon-eye-l'} />
					</div>
				</Bubble>
				<Dropdown clickClose={false} button={renderLayer} appearance="pure" destroyOnClose={false}>
					<LayerMenu sigma={sigma} />
				</Dropdown>
				{/* <Bubble content="帮助" placement="top" dark>
        			<div
            			className='setting-layer__inner icon-item'
            			onClick={handleHelpBtnClick}
        			>
            			<IconTsa type="icon-help" />
        			</div>
    				</Bubble> */}
			</div>
		</div >
	);
};
export default CanvasFormTool;
