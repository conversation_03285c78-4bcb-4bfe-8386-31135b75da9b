import React, { useState, useEffect, FC, useMemo } from 'react';
import './style.less';
import { Badge, Bubble, Row, Col, List, Text, Tag, Alert, notification } from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import { random, filter } from 'lodash';

interface IRiskInfoLayerProps {
	riskDataList: any;
	onViewScanReport: Function
	onViewBindResource: Function
}

const INIT_SIZE = 40

const RISK_LABEL_TEXT = ['名称', '绑定资源数', '地域', '可用区']
const RiskInfoItem = ({ riskData, viewScanReport, viewBindResource }) => {
	const {
		NodeName,
		HighRiskCount,
		MidRiskCount,
		LowRiskCount,
		NodeBindNumber,
		NodeRegion,
		NodeArea,
		HighStrategyInfoList,
		MidStrategyInfoList
	} = riskData

	const regionList = useMemo(() => {
		return NodeRegion?.slice(0, 2);
	}, [NodeRegion]);

	const areaList = useMemo(() => {
		return NodeArea?.slice(0, 2);
	}, [NodeArea])

	const RegionTags = () => {
		return NodeRegion?.map((item, index) => (
			// @ts-ignore
			<Tag key={index} dark theme={["primary", 'success', 'warning'][random(0, 2)]} style={{ margin: '0 5px 5px 0' }}>
				{item}
			</Tag>)
		)
	}
	const AreaTags = () => {
		return NodeArea?.map((item, index) => (
			// @ts-ignore
			<Badge key={index} dark theme={["danger", 'success', 'warning'][random(0, 2)]} style={{ margin: '0 5px 5px 0' }}>
				{item}
			</Badge>
		))
	}
	return (
		<div className='risk-info-item'>
			<div className='risk-consultant-box'>
				<h3 className='risk-title'>{NodeName}（架构风险）</h3>
				<div className='risk-consultant'>
					<div className='risk-consultant-item'>
						<div className='item-hd'>高风险</div>
						<div className='item-bd risk-danger'>{HighRiskCount}</div>
					</div>
					<div className='risk-consultant-item'>
						<div className='item-hd'>中风险</div>
						<div className='item-bd risk-warning'>{MidRiskCount}</div>
					</div>
					<div className='risk-consultant-item'>
						<div className='item-hd'>健康</div>
						<div className='item-bd risk-normal'>{LowRiskCount}</div>
					</div>
				</div>
			</div>
			<div className='risk-data-list'>
				<h3 className='risk-data-list-title'>架构风险项</h3>
				<div className="risk-data-list-content">
					{
						HighStrategyInfoList?.map((item, index) => (
							<div className='risk-data-list-content-item' key={index}>
								<div className='risk-data-list-content-item-title'>
									<Badge ring theme="danger" />
									<div className='content-item__text'>
										{item.StrategyName}
									</div>
								</div>
								<Bubble
									placement="right"
									trigger="hover"
									dark
									content={item.StrategyDesc}
								>
									<p className='content-item-bd content-clamp' >
										{item.StrategyDesc}
									</p>
								</Bubble>
							</div>
						))
					}
					{
						MidStrategyInfoList?.map((item, index) => (
							<div className='risk-data-list-content-item' key={index}>
								<div className='risk-data-list-content-item-title'>
									<Badge ring theme="warning" />
									<div className='content-item__text'>
										{item.StrategyName}
									</div>
								</div>
								<Bubble
									placement="right"
									trigger="hover"
									dark
									content={item.StrategyDesc}
								>
									<p className='content-item-bd content-clamp' >
										{item.StrategyDesc}
									</p>
								</Bubble>
							</div>
						))
					}
				</div>
			</div>
			<div className='risk-detail'>
				<h3 className='risk-detail-title'>节点详情</h3>
				<Row>
					<Col span={6}>
						<List>
							{RISK_LABEL_TEXT.map((item, index) => (
								<List.Item key={index}>
									<Text theme="label" className='risk-label'>{item}</Text>
								</List.Item>))}
						</List>
					</Col>
					<Col span={18}>
						<List className='risk-data'>
							<List.Item>{NodeName}</List.Item>
							<List.Item>{NodeBindNumber}</List.Item>
							<List.Item className='region-item'>
								<div>
									{regionList?.map((item, index) => (<Tag key={index} theme="default" >{item}</Tag>))}
								</div>
								{
									NodeRegion?.length > 2
									&& <Bubble
										placement="right"
										trigger="hover"
										content={<RegionTags />}
									>
										<div>
											<IconTsa type='icon-right-arow' className='icon-svg-arow' />
										</div>
									</Bubble>
								}
							</List.Item>
							<List.Item className='region-item'>
								<div>
									{areaList?.map((item, index) => (<Badge key={index} theme="success" style={{ margin: '0 5px 5px 0' }}>{item}</Badge>))}
								</div>
								{
									NodeArea?.length > 2
									&& <Bubble
										placement="right"
										trigger="hover"
										content={<AreaTags />}
									>
										<div>
											<IconTsa type='icon-right-arow' className='icon-svg-arrow-bd' />
										</div>
									</Bubble>
								}
							</List.Item>
						</List>
					</Col>
				</Row>
			</div>
			<Alert.Notice
				title={<>优化建议以及资源风险详情，请查看 <a onClick={viewScanReport}>云架构巡检报告</a></>}
				style={{ marginBottom: 0, padding: '5px 0' }}
			>
			</Alert.Notice>
		</div>
	)
}

const RiskInfoLayer: FC<IRiskInfoLayerProps> = ({
	riskDataList,
	onViewScanReport,
	onViewBindResource
}: IRiskInfoLayerProps) => {
	const [canTipInfo, setCanTipInfo] = useState(true);

	const riskList = useMemo(() => {
		return filter(riskDataList, item => item.riskData?.HighRiskCount || item.riskData?.MidRiskCount)
	}, [riskDataList]);

	useEffect(() => {
		if (canTipInfo && riskList?.length === 0) {
			notification.warning({ description: '当前架构图没有高风险和中风险的节点!' })
		}
		setCanTipInfo(false);
	}, [riskList]);
	return (
		<>
			{
				riskList?.map((item, index) => (
					<Bubble
						key={index}
						placement="right-end"
						trigger="hover"
						style={{ maxWidth: 500, width: 410, zIndex: 10 }}
						content={<>
							<RiskInfoItem
								riskData={item.riskData}
								viewScanReport={onViewScanReport}
								viewBindResource={onViewBindResource}
							/>
						</>
						}
					>
						<div
							className="risk-layer-item"
							style={{
								left: `${item.position.itemLeft}px`,
								top: `${item.position.itemTop}px`,
								width: `${INIT_SIZE / item.ratio}px`,
								height: `${INIT_SIZE / item.ratio}px`,
								lineHeight: `${INIT_SIZE / item.ratio}px`,
								backgroundColor: item.riskData?.HighRiskCount ? '#F64041' : "#ff7200"
							}}
						>
							{item.riskData?.HighRiskCount + item.riskData?.MidRiskCount}
						</div>
					</Bubble >
				))
			}
		</>
	);
};
export default RiskInfoLayer;
