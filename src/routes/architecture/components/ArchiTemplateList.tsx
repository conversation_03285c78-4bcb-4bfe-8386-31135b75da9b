import React, { useState, useEffect, useMemo } from 'react';
import { useHistory } from '@tea/app';
import '../style.less';

import { Card, Button, message, Justify, Segment, Modal, TagSearchBox, Alert, ExternalLink } from '@tencent/tea-component';
import { TemplateForm } from './EditorCenterSvg/TemplateForm';
import { ArchiTableList } from './Common/MapListComponents/ArchiTableList';
import { ArchiThumbList } from '../components';
import { IconTsa } from '@src/components/IconTsa';

import { templateTypeOption, templateTypeOptionList } from '@src/routes/architecture/conf/architecture';

import { toPairs, reduce, map, forEach } from 'lodash';
import { useToggle } from '../hooks/common';

import { useActivate, useUnactivate, withActivation } from 'react-activation';
import { AttributeValue } from '@tencent/tea-component/src/tagsearchbox/AttributeSelect';
import { feedBackUrl } from '@src/routes/architecture/conf/eidtorConfig';
import { BASEURL } from '../conf/architecture';
import { CreateCloudMapTemplate } from '@src/api/architecture/architecture';

interface paramsProps {
	industryConfig: Array<string>
}

export function ArchiTemplateList({ industryConfig }: paramsProps) {
	const history = useHistory();
	const operator = localStorage.getItem('engName');

	const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);

	const [tplListFilter, setTplListFilter] = useState([]);

	const [mode, setMode] = useState('thumb');

	const [createDialogVisible, openCreateDialog, closeCreateDialog] = useToggle(false);

	const optionsConfig = reduce(
		toPairs(templateTypeOption),
		(ret, [key, value]) => ret.concat({ name: value, key }), []
	);
	const industryOption = map(industryConfig, item => ({ key: item, name: item }));

	const pushPropertyOption = useMemo(() => reduce(toPairs(templateTypeOptionList), (ret, [key, value]) => {
		if (key === '0') return ret;
		return ret.concat({ name: value, key });
	}, []), []);

	// 标签搜索框
	const attributes: Array<AttributeValue> = [
		{
			type: 'input',
			key: 'template_name',
			name: '模板名称',
		},
		{
			type: 'input',
			key: 'creator',
			name: '创建人',
		},
		{
			type: ['single', { searchable: true }],
			key: 'property',
			name: '模板属性',
			values: optionsConfig,
		},
		{
			type: ['multiple', { searchable: true }],
			key: 'industry',
			name: '行业类型',
			values: industryOption,
		},
		{
			type: 'input',
			key: 'property_desc',
			name: '模板属性描述',
		},
		{
			type: 'input',
			key: 'scenario',
			name: '模板使用场景',
		},
		{
			type: ['single', { searchable: false }],
			key: 'push_property',
			name: '推送模板属性',
			values: pushPropertyOption,
		},
	];

	const handleSearchTagChange = (tags) => {
		const tempFilter = [];
		forEach(tags, (tag) => {
			const name = tag.attr ? tag.attr.key : 'template_name';
			let valueList = [];
			if (name === 'property' || name === 'push_property') {
				map(tag.values, (item) => {
					if (item.key === '1') {
						valueList = name === 'push_property' ? ['0', '1'] : name === 'property' ? ['2', '1'] : [];
					} else {
						valueList.push(item.key);
					}
				});
			} else {
				valueList = map(tag.values, item => (item.name));
			}

			tempFilter.push({
				Name: name,
				Values: valueList,
			});
		});
		setTplListFilter(tempFilter);
		setTagSelectBoxValue(tags);
	};

	const tagSearchBoxProps = {
		minWidth: '55%',
		attributes,
		value: tagSelectBoxValue,
		hideHelp: true,
		tips: '支持模板名、模板的属性、场景、行业类型和创建人过滤，多个关键词用竖线"|"分隔',
		onChange: handleSearchTagChange,
	};

	const handleTemplateCreate = async (values) => {
		try {
			const Owner = JSON.stringify([operator, 'runmouzou', 'ryanzczhang', 'klayzhuang', 'dannyxiang']);
			const baseParams = {
				...values,
				AppId: 1253985742,
				Property: +values.Property,
				Owner,
				Detail: '',
				SvgFile: '',
			};
			const reqParams = { ...baseParams, Creator: operator };
			const res = await CreateCloudMapTemplate(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '创建模板成功' });
				history.push(`/advisor/architecture/template-editor/${res.MapTemplateUUId}?optype=0`);
				closeCreateDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleModeChange = (value) => {
		setMode(value);
		setTagSelectBoxValue([]);
		setTplListFilter([]);
	};
	const handleCreateClick = () => {
		Modal.alert({
			type: 'warning',
			message: '升级通知',
			description: <>
				云架构已经升级到新版本，请进入
				<a href="/advisor/new-architecture" target='_blank'>新版</a>
				绘制架构图。获取客户租户端服务授权后，将架构图发送给客户即可绑定资源。
			</>,
		});
	};
	return (
		<>
			<Card>
				<Card.Body>
					<Alert defaultVisible>
                        云架构欢迎您提交功能/体验等方面的需求和建议，期待您的声音！
						<ExternalLink href={feedBackUrl}>
                            点击提交
						</ExternalLink>
					</Alert>
					<Justify
						style={{ marginBottom: 20 }}
						left={
							<Button type="primary" onClick={handleCreateClick}>新建</Button>
						}
						right={
							<div style={{ display: 'flex', justifyContent: 'flex-end' }}>
								<div style={{ width: '60%' }}>
									<TagSearchBox {...tagSearchBoxProps} />
								</div>
								<Segment
									value={mode}
									onChange={value => handleModeChange(value)}
									options={[
										{
											text: (<div style={{ display: 'flex' }}>
												<div
													className={mode === 'thumb' ? 'icon-active' : ''}
													style={{ paddingTop: 4 }}
												>
													<IconTsa type='icon-thumb' />
												</div>
												<span style={{ marginLeft: 6 }}>缩略图</span>
											</div>),
											value: 'thumb',
										},
										{
											text: (<div style={{ display: 'flex' }}>
												<div
													className={mode === 'list' ? 'icon-active' : ''}
													style={{ paddingTop: 4 }}
												>
													<IconTsa type='icon-list' />
												</div>
												<span style={{ marginLeft: 6 }}>列表</span>
											</div>),
											value: 'list',
										},
									]}
								/>
							</div>
						}
					/>
					{
						mode === 'thumb'
							? <ArchiThumbList
								filters={tplListFilter}
								clearFilter={() => setTagSelectBoxValue([])}
								industryOption={industryOption}
								optionsConfig={optionsConfig}
							/>
							: <ArchiTableList
								filter={tplListFilter}
							/>
					}
				</Card.Body>
				<Modal
					caption="新建架构图模板"
					maskClosable
					visible={createDialogVisible}
					onClose={() => closeCreateDialog()}
				>
					<Modal.Body>
						<TemplateForm
							templateDetail={{}}
							industryConfig={industryConfig}
							onSubmit={handleTemplateCreate}
							closeCreateDialog={closeCreateDialog}
						/>
					</Modal.Body>
				</Modal>
			</Card>
			{/* <iframe src="https://isa-test.woa.com/advisor/architecture/cloud-editor/266?optype=0&source=pm&signature=b63f4d66486e37fcaa1befcccb7aab9a5561f2d1ccd76a3a16921a98e6c9e11f&timestamp=1672285235" width="100%" height="1000px"></iframe> */}
		</>
	);
}
