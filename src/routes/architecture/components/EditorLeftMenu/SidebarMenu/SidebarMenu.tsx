/* 图形组件列表 */
import React, { useState, useEffect, useMemo } from 'react';
import { Menu, message, SearchBox, StatusTip, Bubble, Text } from '@tencent/tea-component';
import './style.less';
// @ts-ignore
import Sigma, { render3DProductShape } from '@tencent/sigma-editor';
import { filter, map, find, toPairs, reduce, mapKeys, isEmpty, some, isElement, includes } from 'lodash';
import { useToggle } from '../../../hooks/common';
import { categoryMapping, noSupportScanProduct, supportScanPro } from '../../../conf/eidtorConfig';
import { getCloudMapShapes } from '@src/api/architecture/architecture';
import { IconTsa } from '@src/components/IconTsa';
import { getViewPort, swapItem } from '../../../utils';

interface Prop {
  sigma: any;
  currentMode: string
  scale?: number;
  onNewNodeCreate?: Function
}

const RenderTitle = ({ shape, is3DMode }) => {
  const isHightTag = find(supportScanPro, item => item === shape?.name);
  const productIcon = useMemo(() => {
    return is3DMode ? render3DProductShape(shape?.data?.d3) : shape?.data?.d2;
  }, [is3DMode]);
  return <>
    <div className='menu-item-content'>
      <div className='menu-item__box'>
        {
          shape?.data?.d2
          && <div
            className={`sidebar-product-icon ${is3DMode ? 'sidebar-3d-icon' : ''} ${isHightTag ? 'active-product-icon' : ''}`}
            dangerouslySetInnerHTML={{ __html: productIcon }}
          >
          </div>
        }
        <span className='menu-item-title'>{`${shape.cName} ${shape?.name}`}</span>
      </div>
      <span className='menu-item-desc'>{shape.description}</span>
      {
        shape.category !== "COMMON"
        && <Bubble
          placement="right"
          trigger="hover"
          content={
            <div className='shape-tips-content'>
              <h3>{shape?.cName}</h3>
              <p className='tip-item'>
                <span>绑定资源</span>
                <Text theme={isHightTag ? 'success' : 'text'}>{isHightTag ? '支持' : '不支持'}</Text>
              </p>
              <p className='tip-item'>
                <span>英文名称</span>
                <Text theme='text'>{shape?.name}</Text>
              </p>
              <p className='tip-item'>
                <span>属性描述</span>
                <Text theme='text'>{shape?.description}</Text>
              </p>
              <hr />
              <p className='bottom-tip'>提示: 可点击或拖拽组件添加</p>
            </div>
          }
        >
          <div className='icon-help'>
            <IconTsa type='icon-info-bing' />
          </div>
        </Bubble>
      }
    </div>
  </>;
};

let box;
let menuItem;
let isDrag = false;
let dragData: { scale: number, shapeName: string };
const IGNORE_PRODUCT = ['IMAGE', 'TDSQL'];

function SidebarMenu({ sigma, scale, currentMode, onNewNodeCreate }: Prop) {
  const [keywords, setKeywords] = useState('');
  const [shapeCompentsMaps, setShapeCompentsMaps] = useState([]);
  const [components, setComponents] = useState([]);
  const [isShapesLoading, startLoad, endLoad] = useToggle(false);
  const [isFirstLoad, setFirstLoad] = useState(true);

  const is3DMode = currentMode === '3d';

  const getShapes = async (sigma) => {
    if (!sigma || !isFirstLoad) {
      return;
    }
    startLoad();
    try {
      const res = await getCloudMapShapes({ AppId: 1253985742 });
      const mapShape = map(
        res?.MapShape,
        item => mapKeys(item, (value, key) => key.substring(0, 1).toLowerCase() + key.substring(1))
      );
      sigma.loadShapes(mapShape);
      // 获取图标组件清单
      const components = sigma.getComponents();
      setComponents(components);
      setFirstLoad(false);
      endLoad();
    } catch (err) {
      const msg = err.msg || err.toString() || '未知错误';
      message.error({ content: msg });
      endLoad();
    }
  };

  // 过滤逻辑
  useEffect(() => {
    const comReg = new RegExp(keywords, 'ig');
    const categoryShapes = {};

    // 转换为maps
    const tempShapes = filter(components, component => (
      !keywords
      || comReg.test(component?.name)
      || comReg.test(component.cName)
      || comReg.test(component.description)
    ));

    map(tempShapes, (product) => {
      if (product.category === 'SECURITY' || includes(IGNORE_PRODUCT, product?.name)) return;
      const productCategory = categoryMapping[product.category];
      const shapes = categoryShapes[productCategory] || [];
      shapes.push(product);
      categoryShapes[productCategory] = shapes;
    });

    const compentsMaps = reduce(
      toPairs(categoryShapes),
      (ret, [key, value]) => {
        // 替换DOS高防IP顺序
        if (key === "安全") {
          value = swapItem(value, 9, 1);
        }

        return ret.concat([{ category: key, shapes: value }]);
      }, []
    );
    setShapeCompentsMaps(compentsMaps);
  }, [keywords, components]);

  // 初始化加载
  useEffect(() => {
    getShapes(sigma);
  }, [sigma]);

  const handleShapesLoadNode = (shape, position = {}) => {
    const isNormalCreate = some(noSupportScanProduct, item => item === shape?.name) || shape.category === 'COMMON';
    if (isNormalCreate) {
      isEmpty(position)
        ? sigma.createNode(shape)
        : sigma.createNode(shape, { position });
    } else {
      isEmpty(position)
        ? sigma.createNode(shape, { label: shape?.name })
        : sigma.createNode(shape, { label: shape?.name, position });
    }
  };

  const handleClick = (shape) => {
    if (!isDrag) {
      handleShapesLoadNode(shape);
    }
  };

  const handleMouseUp = (e) => {
    let shape = null;
    const { scale, shapeName } = dragData;
    map(shapeCompentsMaps, (item) => {
      const currentShape = find(item.shapes, shape => shape?.name === shapeName) || {};
      if (!isEmpty(currentShape)) {
        shape = currentShape;
      }
    });

    if (isElement(box) && shape) {
      if (e.clientX < 480) {
        // 当鼠标回到左侧物料区时取消生成新节点
        // handleShapesLoadNode(shape);
      } else {
        const left = e.clientX - 480 - 58;
        const top = e.clientY - 100 - 46;
        const { x, y } = sigma.getReferOriginPosition();

        handleShapesLoadNode(shape, { x: left * scale + x, y: top * scale + y });
      }
      menuItem[0].removeChild(box);
      box = null;
    }
    document.removeEventListener('mousemove', move);
  };

  const move = (event) => {
    // 获取鼠标距离坐标x的位置
    const x = event.clientX;
    // 获取鼠标距离坐标y的位置
    const y = event.clientY;
    // 盒子的左侧位置=鼠标x位置-鼠标在盒子中的x位置
    // 坐标 = 鼠标位置 - 导航条大小 - 盒子一半大小
    let currentLeft = x - 200 - 58;
    let currentTop = y - 50 - 46;
    // 设置盒子移动边界
    if (currentLeft < 0) {
      currentLeft = 0;
    } else if (currentLeft >= getViewPort('width') - box?.offsetWidth - 200) {
      currentLeft = getViewPort('width') - box?.offsetWidth - 200;
    }

    if (currentTop < 50) {
      currentTop = 50;
    } else if (currentTop >= getViewPort('height') - box?.offsetHeight - 50) {
      currentTop = getViewPort('height') - box?.offsetHeight - 50;
    }

    // 当鼠标松开时防止顺带触发Click事件
    isDrag = true;

    box.style.left = `${currentLeft}px`;
    box.style.top = `${currentTop}px`;
  };

  const handleMouseDown = (e, shape) => {
    // 鼠标按下时恢复初始化，此时不确定后续是点击还是拖拽
    isDrag = false;
    const moveDragData = {
      scale,
      shapeName: shape?.name,
    };
    dragData = { ...moveDragData };
    box = document.createElement('div');
    menuItem = document.getElementsByClassName('cloud-body');

    menuItem[0].appendChild(box);

    const isNormal = typeof shape?.data?.d2 === 'undefined';

    box.className = isNormal ? 'virtual-normal-box' : 'virtual-box';

    if (is3DMode) {
      box.style.width = `${152 / scale}px`;
      box.style.height = `${121 / scale}px`;
      box.innerHTML = isNormal ? `` : render3DProductShape(shape?.data?.d3);
    }
    else {
      box.style.width = `${74 / scale}px`;
      box.style.height = `${74 / scale}px`;
      box.innerHTML = isNormal ? `` : shape?.data?.d2;
    }

    document.addEventListener('mousemove', move); // 为document绑定移动事件
    document.addEventListener('mouseup', handleMouseUp);
  };

  return (<>
    <Menu
      style={{ zIndex: 1 }}
      className="sider-menu scrollbar"
      title={<SearchBox
        placeholder="请输入关键字"
        size="full"
        className='menu-search-box'
        onKeyDown={e => e.stopPropagation()}
        onSearch={word => setKeywords(word)}
        onChange={word => setKeywords(word)}
      />}
    >
      {
        isShapesLoading
          ? <StatusTip status="loading" className='sider-loading' />
          : shapeCompentsMaps.map((item, index) => (
            <Menu.SubMenu
              defaultOpened={index === 0 || index === 1}
              title={item.category} key={`${item.category}--${index}`}
              style={{ backgroundColor: '#fff' }}
            >
              {
                item.shapes.map((shape, index) => {
                  if (!shape) return <></>
                  const isHightTag = find(supportScanPro, item => item === shape?.name);
                  return <div
                    key={`${shape?.name}--${index}`}
                    className='menu-sub-item'
                    onMouseDown={e => handleMouseDown(e, shape)}
                  >
                    <Menu.Item
                      className={`menu-item ${isHightTag ? 'active-item' : ''}`}
                      title={<RenderTitle shape={shape} is3DMode={is3DMode} />}
                      onClick={() => handleClick(shape)}
                    />
                  </div>
                })
              }
            </Menu.SubMenu>
          ))
      }
    </Menu>
  </>);
}

export const SidebarMenuMemo = React.memo(SidebarMenu)
