
.sider-menu {
	width: 280px !important;

	.tea-menu__header {
		width: 100%;
		
		.tea-menu__title {
			width: 100%;
			height: 70px;
			display: block;
			padding-top: 18px;
			.menu-search-box {
				.tea-input {
					border: none;
					background-color: #F1F2F5;
				}
			}
		}
	}

	.tea-menu__body {
		width: 100%;

		&::-webkit-scrollbar {
			width: 2px;
			height: 6px;
		}
		&::-webkit-scrollbar-thumb {
			background: #888;
			border-radius: 20px;
		}
		&::-webkit-scrollbar-track {
			border-radius: 20px;
		}

		.tea-menu__submenu > .tea-menu__item {
			border-top: 1px solid #d9d9d9;

			.app-advisor-menu__text {
				font-size: 14px;
				color: rgba(0, 0, 0, 0.9);
				font-weight: bold;	
			}
		}

		.tea-menu__text {
			width: 100%;
			max-width: 280px;
		}
	}

	.sidebar-product-icon {
		padding-top: 2px;
		margin-right: 8px;
		svg {
			width: 20px;
			height: 20px;
		}
	}

	.sidebar-3d-icon {
		svg {
			width: 30px;
			height: 30px;
		}
	}

	.active-product-icon {
		svg path{
			// fill: #0ABF5B;
		}
	}

	.sider-loading {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
	}

	.menu-item {
		.tea-menu__item {
			position: relative;
			padding: 8px 16px;
			justify-content: flex-start;
			align-items: center;
			width: 100%;
			box-sizing: border-box;
			text-decoration: none;
			transition: background-color .15s cubic-bezier(.4,0,.2,1) 0ms;
			transition: background-color .25s;
			cursor: pointer;
			&::before {
				content: none;
			}
		}
	}
	.active-item {
		.tea-menu__item {
			position: relative;
			&::before {
				content: '';
				display: block;
				position: absolute;
				top: -3px;
				left: -6px;
				width: 3px;
				height: 52px;
				background: #0ABF5B;
			}
		}
	}
}

.menu-item-content {
	position: relative;
	.icon-help {
		position: absolute;
		display: none;
		right: 0;
		top: 12px;
		z-index: 999;
	}
	&:hover {
		.icon-help {
			display: block;
		}
	}
}

.shape-tips-content {
	.tip-item{
		color: #888888;
		line-height: 17px;
		font-weight: 400;
		margin: 10px 0;
		span {
			margin-right: 15px;
		}
	}
	hr {
		margin: 10px 0;
	}
	.bottom-tip {
		color: rgba(0, 0, 0, 0.4);
		text-align: center;
	}
}

.menu-item__box {
	display: flex;
	align-items: center;
	.menu-item-title{
		display: block;
		margin-right: 6px;
		line-height: 20px;
		font-size: 13px;
		color: rgba(0, 0, 0, 0.9);
	}

	
}	

.menu-item-desc {
	display: block;
    line-height: 1.5;
	color: rgba(0, 0, 0, 0.4);
	font-size: 10px;
}

.virtual-box {
	position: absolute;
	display: block;
	left: 0;
	right: 0;
	font-size: 18px;
	font-weight: bold;
	z-index:0;
	cursor: pointer;

	svg {
		width: 100%;
		height: 100%;
	}
}

.virtual-normal-box {
	position: absolute;
	left: 0;
	right: 0;
	z-index:0;
	cursor: pointer;
	border: 1px solid #888888;
}