/* 图形组件列表 */
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Prompt } from 'react-router';
import './style.less';
import { useHistory, useAegisLog } from '@tea/app';
import Sigma, { SigmaType } from '@tencent/sigma-editor';

import {
	Button,
	Modal,
	Bubble,
	Input,
	PopConfirm,
	message,
	NavMenu,
	Tabs,
	Checkbox,
	Layout,
	Dropdown,
	notification,
	TabPanel,
	Alert,
	ExternalLink,
	List,
	CollapseTransition,
} from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import { ResourceBoundTable } from '../EditorCenterSvg/ResourceBoundTable';
import { TemplateForm } from '../EditorCenterSvg/TemplateForm';
import { CloudMapForm } from '../EditorCenterSvg/CloudMapForm';
import { PmCloudMapForm } from '../EditorCenterSvg/PmCloudMapForm';
import {
	RightOperateNode,
	TemplateListPicker,
	SizeScaleBar,
	NodeDetailPanel,
	HelpDialog,
	CanvasFormTool,
	SidebarMenu,
	SetCanvasStyleModal,
	CloudReport,
	ScanProcessButton,
	ExportMenu,
	ResourceBindNodePicker,
	MapDropMenu,
	RiskInfoLayer,
	HelpDropDown,
} from '../../components';

import {
	supportScanPro,
	operateDesHref,
	nodeToProductMap,
	OPRATION_TYPE,
	OPERATE_TYPE,
	EX_SOURCE,
	urlParams,
	tabConfig,
	createTabConfig,
	pmTabConfig,
} from '../../conf/eidtorConfig';
import { FILE_TYPE } from '../../conf/architecture';
import { allBindType } from '../../conf/resourcePickConf';
import { CloudMapTemplate } from '@src/types/architecture/architecture';

import {
	isElement,
	some,
	pick,
	isEmpty,
	map,
	includes,
	keys,
	find,
	toPairs,
	mapKeys,
} from 'lodash';
import { useToggle } from '../../hooks/common';
import { getStorage, setStorage } from '@src/utils/storage';
import { setNodeStyleByKey, clearGraphCustomerData, getRiskLayerData, initStrColor } from '../../utils';
import { getUrlParamFromLocation, getSearchParam, getUrlParamsStr } from '@src/utils/common';

import {
	DeleteCloudMapItem,
	CreateCloudMapBaseInfo,
	ModifyCloudMapBaseInfo,
	DeleteCloudMapBaseInfo,
	CreateCloudMapTemplate,
	ModifyCloudMapTemplate,
	DescribeCloudMapBaseInfo,
	DescribeCloudMapConfig,
	AsyncNodeListInfo,
	GetNodeRiskInfo,
	DescribeMapItemIsInstance,
	DescribeMainMapBySubMapId,
} from '@src/api/architecture/architecture';
import { getCustomerName } from '@src/api/advisor/estimate';

const defaultDialogPosition = {
	x: 0,
	y: 0,
};

export function CloudMapEditor(match) {
	const history = useHistory();

	const aegis = useAegisLog();

	// 操作人
	const operator = localStorage.getItem('engName');

	const isExSource = includes(EX_SOURCE, getSearchParam('source', location));

	const { mapUUId } = match.match.params;

	const locationParam: any = getUrlParamFromLocation(urlParams, location);

	// @ts-ignore
	const projectType = decodeURIComponent(locationParam.projectType);
	// @ts-ignore
	const mapEditable = locationParam?.optype === OPERATE_TYPE.WRITE;
	// @ts-ignore
	const mapType = +locationParam?.mapType;

	// 操作人uin
	const [uin, setUin] = useState('');

	const [mainMapInfo, setMainMapInfo] = useState<any>({});

	// ========架构图业务逻辑变量========
	const [canCover, setCanCover] = useState(false);

	const [isDelNode, setIsDelNode] = useState(false);

	// 架构图信息
	const [cloudMapDetail, setCloudMapDetail] = useState<any>({});

	// 架构图应用方式
	const [isExistCloudMap, setIsExistCloudMap] = useState(true);

	const { MapName, MapUUId, AppId, VersionUUID, Detail: mapDetail, CanCreateTask = false } = cloudMapDetail;

	const [industryConfig, setIndustryConfig] = useState([]);

	// 模板名编辑
	const [mapNameEditing, setMapNameEditing] = useState(MapName);

	// 模板信息
	const [templateDetail, setTemplateDetail] = useState<CloudMapTemplate>();

	// 模板列表选择弹窗
	const [tplPickerVisible, openTplPickerDialog, closeTplPickerDialog] = useToggle(false);

	// 保存架构图弹窗
	const [createMapDialogVisiable, openCreateDialog, closeCreateDialog] = useToggle(false);

	// 导出文件抽屉
	const [exportVisiable, openExportMenu, closeExportMenu] = useToggle(false);

	// ========编辑器画布编辑变量========
	// 获取编辑器DOM
	const editorContainer = useRef();
	// sigma对象
	const [sigma, setSigma] = useState<SigmaType>();
	// 当前选择节点
	const [currentNode, setCurrentNode] = useState<any>({});
	// 节点锁定状态
	const [isNodeLock, lockNode, unlockNode] = useToggle(false);
	// 节点编辑菜单panel
	const [popDialogVisible, openPopDialog, closePopDialog] = useToggle(false);
	// 节点编辑菜单位置
	const [popDialogPosition, setPopDialogPosition] = useState(defaultDialogPosition);
	// 删除节点
	const [deleteNodes, setDeleteNodes] = useState([]);
	const [deleteFlag, setDeleteFlag] = useState(true);
	// 画布大小比例
	const [scale, setScale] = useState(1);
	// 画布属性设置
	const [checkedNodes, setCheckedNodes] = useState([]);
	const [checkedStyles, setCheckedStyles] = useState({});
	// 节点属性编辑panel
	const [nodeEditorPanelVisible, openNodeEditorPanel, closeNodeEditorPanel] = useToggle(false);
	// 当前维度
	const [mapMode, setMapMode] = useState<'2d' | '3d'>(getStorage('sigma_mode') === 'SIGMA_GRAPH_MODE_2D' ? '2d' : '3d');

	// ============架构巡检变量===========
	// 节点绑定资源弹窗
	const [panelVisiable, openPanelDialog, closePanelDialog] = useToggle(false);
	// 绑定实例列表抽屉
	const [insListDrawerVisible, openDrawer, closeDrawer] = useToggle(false);
	const [riskDataList, setRiskDataList] = useState([]);
	// 风险信息展示浮层
	const [riskLayerVisible, openRiskLayer, closeRiskLayer] = useToggle(false);
	// 是否有存量的巡检报告
	const [taskObj, setTaskIdObj] = useState<any>({});

	// 巡检报告抽屉
	const [reportVisible, openReportDrawer, closeReportDrawer] = useToggle(false);

	let autoSaveTimer;

	useEffect(() => {
		setMapNameEditing(MapName);
	}, [MapName]);

	useEffect(() => {
		initSigmaContainer();
	}, [MapUUId, mapEditable]);

	// 初始化
	useEffect(() => {
		getCustomerInfo();
		getIndustryConfig();
	}, []);

	useEffect(() => {
		getCloudMap();
		getMapItemIsInstance();
	}, [mapUUId]);

	const getIndustryConfig = async () => {
		try {
			const res = await DescribeCloudMapConfig({ AppId: 1253985742 });
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setIndustryConfig(res.Industry);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getCloudMap = async () => {
		try {
			const params = {
				AppId: 1253985742,
				Filters: [
					{ Name: 'map_uuid', Values: [`${mapUUId}`] },
					{ Name: 'project_type', Values: [`${projectType}`] },
				],
				Operator: operator,
				MapType: mapType,
				FileType: FILE_TYPE.ALL,
			};
			const res = await DescribeCloudMapBaseInfo(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
				message.error({ content: '架构图初始化错误！' });
			} else {
				const currentMap = find(res.CloudMap, i => i.MapUUId === mapUUId) || {};
				if (!isEmpty(currentMap)) {
					if ((mapEditable && currentMap.CanWrite) || (!mapEditable && currentMap.CanRead)) {
						setCloudMapDetail(currentMap);
						message.success({ content: '架构图初始化完成！' });
						return;
					}
				}
				Modal.error({
					message: '架构图ID错误或无架构图编辑权限',
					description: '无法找到当前架构图ID的架构图！',
					onClose: () => {
						window.open(`${window.location.origin}/advisor/architecture`, '_self');
					},
				});
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取 appid uin
	const getCustomerInfo = async () => {
		if (!AppId) return;
		try {
			const customerNameData = await getCustomerName({ AppId });
			if (customerNameData.Error) {
				message.error({ content: customerNameData.Error.Message });
				return;
			}
			setUin(customerNameData.Uin);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 快照获取主图信息
	const getMainMapBySubMapId = async () => {
		if (!AppId) return;
		try {
			const res = await DescribeMainMapBySubMapId({ AppId, SubMapId: mapUUId, Operator: operator });
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			setMainMapInfo(res);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// ===========编辑器================
	// 初始化架构图
	function initSigmaContainer() {
		const sigmaEditorContainer = editorContainer.current;
		if (!isElement(sigmaEditorContainer)) {
			return;
		}
		const instance = new Sigma(sigmaEditorContainer, {
			mode: mapMode,
			callbacks: {
				onCheckedChange,
				onDocClick,
				onScaleChange,
				onShapeMouseRightClick,
				onShapeDelete: nodes => setDeleteNodes(nodes),
			},
		});
		if (mapDetail) {
			instance.initWithGraphData({ content: mapDetail });
		} else {
			instance.clearPaint();
		}
		setSigma(instance);
	}

	// 定时自动保存数据
	useEffect(() => {
		if (!sigma) return;
		if (autoSaveTimer) {
			clearInterval(autoSaveTimer);
			autoSaveTimer = null;
		}
		autoSaveTimer = setInterval(() => {
			handleCloudSaveSilent(cloudMapDetail);
		}, 1000 * 60 * 5);
		return () => {
			clearInterval(autoSaveTimer);
		};
	}, [sigma, cloudMapDetail]);

	useEffect(() => {
		if (!sigma) return;
		getMapItemIsInstance();
		if (mapEditable) {
			setNodeListSync();
		}
	}, [sigma]);

	useEffect(() => {
		if (mapType && AppId) {
			getMainMapBySubMapId();
		}
	}, [mapType, AppId]);

	useEffect(() => {
		if (deleteFlag) {
			handleCloudSaveSilent(cloudMapDetail);
		}
	}, [deleteNodes]);

	// 鼠标抬起事件的模拟
	useEffect(() => {
		const ele = checkedNodes[0]?.component.node.ownerSVGElement;
		const handleMouseup = (e) => {
			const right = window.innerWidth - e.clientX;
			if ((right) <= 280) {
				const mouseEvent = new MouseEvent('mouseup');
				ele.dispatchEvent(mouseEvent);
			}
			window.removeEventListener('mouseup', handleMouseup);
		};
		if (nodeEditorPanelVisible) {
			if (checkedNodes.length == 1) {
				window.addEventListener('mouseup', handleMouseup);
			}
		}
		return () => {
			window.removeEventListener('mouseup', handleMouseup);
		};
	}, [nodeEditorPanelVisible, checkedNodes]);

	// 点击画布空白回调
	const onDocClick = () => {
		closePopDialog();
		closeNodeEditorPanel();
	};

	// 画布大小变化回调
	const onScaleChange = (scl) => {
		setScale(scl);
	};

	// 节点选中回调
	const onCheckedChange = (nodes) => {
		if (nodes.length === 0) return;
		openNodeEditorPanel();

		setCheckedNodes(nodes);
		if (!nodes.length) {
			setCheckedStyles({});
			return;
		}
		const attrArray = [];
		const firtNode = nodes[0];
		for (let i = 0; i < nodes.length; i++) {
			const node = nodes[i];
			attrArray.push(node.editable);
		}
		const attrs = (attrArray.shift() || []).filter(v => attrArray.every(a => a.indexOf(v) !== -1));
		const attrEditor = {};
		for (let j = 0; j < attrs.length; j++) {
			const attr = attrs[j];
			attrEditor[attr] = firtNode.styles[attr];
		}
		setCheckedStyles(attrEditor);
	};

	// 右键回调
	const onShapeMouseRightClick = (e, node) => {
		setPopDialogPosition({ x: e.clientX - (isExSource ? 0 : 200), y: e.clientY - (isExSource ? 0 : 50) });
		openPopDialog();
		setCurrentNode(node);
	};

	// 锁定节点
	const handleNodelock = () => {
		if (isNodeLock) {
			sigma.unlockNode();
			unlockNode();
		} else {
			sigma.lockNode();
			lockNode();
		}
	};
	// 画图快捷键
	useEffect(() => {
		document.body.style.userSelect = 'none';
		document.addEventListener('keydown', keyboardEvent);
		return () => {
			document.body.style.userSelect = null;
			document.removeEventListener('keydown', keyboardEvent);
		};
	});
	const keyboardEvent = (e) => {
		switch (true) {
			case (e.ctrlKey || e.metaKey) && (e.key === 'f' || e.key === 'F'):
				e.preventDefault();
				handleHelpBtnClick();
				break;
			case (e.ctrlKey || e.metaKey) && (e.key === 's' || e.key === 'S'):
				e.preventDefault();
				handleCloudSaveSilent(cloudMapDetail);
				break;
			case e.code === 'Space':
				e.preventDefault();
				closeRiskLayer();
				break;
			default:
		}
	};

	// 打开帮助弹框
	const handleHelpBtnClick = () => {
		Modal.alert({
			// @ts-ignore
			caption: '帮助',
			maskClosable: true,
			size: '90%',
			message: <HelpDialog />,
			buttons: [],
		});
	};

	// 维度切换方法
	const handleModeChange = (mode: '2d' | '3d') => {
		if (mapMode !== mode) {
			setMapMode(mode);
			sigma.toggleMode();
			setStorage('sigma_mode', mode === '3d' ? 'SIGMA_GRAPH_MODE_3D' : 'SIGMA_GRAPH_MODE_2D');
		}
	};

	useEffect(() => {
		if (riskLayerVisible) {
			handleRiskLayerOpen();
		}
	}, [scale, mapEditable]);

	useEffect(() => {
		if (taskObj.lastTaskId) {
			handleRiskLayerOpen();
		}
	}, [taskObj.lastTaskId]);

	// 风险节点展示
	const handleRiskLayerOpen = async () => {
		let graphDataObj = {};
		try {
			graphDataObj = JSON.parse(sigma.getLocalGraphData());
		} catch (err) {
			message.error({ content: 'GraphData JSON Error' });
		}
		const isExistRiskMap = some(toPairs(graphDataObj), ([key, value]) => !isEmpty(value.customize));
		const { lastTaskId } = taskObj;

		if (!lastTaskId) {
			clearGraphCustomerData(sigma, graphDataObj);
		}
		const preTaskId = getStorage('preTaskId');
		if (preTaskId === lastTaskId && isExistRiskMap) {
			const riskLayerDataList = getRiskLayerData(graphDataObj, scale);
			setRiskDataList(riskLayerDataList);
			openRiskLayer();
		} else {
			if (!lastTaskId) {
				message.error({ content: '架构图还未进行过巡检，不支持查看！' });
				return;
			}
			try {
				const res = await GetNodeRiskInfo({
					AppId,
					TaskId: lastTaskId,
					MapUUId,
					Operator: operator,
				});
				if (res.Error) {
					message.error({ content: res.Error.Message });
				} else {
					clearGraphCustomerData(sigma, graphDataObj);
					mapKeys(graphDataObj, (value, key) => {
						sigma.setCustomize(res.NodeRiskInfoMap[key], key);
					});
					setStorage('preTaskId', lastTaskId);
					const riskLayerDataList = getRiskLayerData(res.NodeRiskInfoMap, scale);

					setRiskDataList(riskLayerDataList);
					handleCloudSaveSilent(cloudMapDetail);
					openRiskLayer();
				}
			} catch (err) {
				const msg = err.msg || err.toString() || '未知错误';
				message.error({ content: msg });
			}
		}
	};

	// =============业务逻辑==============
	const isExistTemplate = useMemo(() => !!templateDetail?.MapTemplateUUId, [templateDetail]);

	// 产品是否支持绑定
	const isShowBindResource = useMemo(
		() => some(supportScanPro, item => item === currentNode.name),
		[currentNode, AppId]
	);

	// 获取节点绑定实例状态
	const getMapItemIsInstance = async () => {
		if (!sigma || projectType === '客户项目') return;
		try {
			const graphDataObj = JSON.parse(sigma.getLocalGraphData());
			if (isEmpty(graphDataObj)) return;
			const res = await DescribeMapItemIsInstance({
				AppId,
				MapUUId,
				Opertor: operator,
				ItemUUIdList: keys(graphDataObj),
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				if (!isEmpty(res.ItemIsInstances)) {
					map(res.ItemIsInstances, ({ UUID, Flag }) => {
						if (includes(supportScanPro, graphDataObj[UUID].name)) {
							setNodeStyleByKey(sigma, graphDataObj[UUID], {
								attr: 'fillDark',
								value: Flag ? '#4286C5' : '#E2E6EC',
							});
						}
					});
				}
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 同步节点数据
	const setNodeListSync = async () => {
		if (!sigma) return;
		try {
			const graphDataObj = JSON.parse(sigma.getLocalGraphData());
			if (isEmpty(graphDataObj)) return;
			const res = await AsyncNodeListInfo({
				AppId,
				MapUUId,
				Opertor: operator,
				ItemUUIdList: keys(graphDataObj),
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 路由守卫
	const handlePrompt = () => {
		const localGraphData = sigma.getLocalGraphData();
		if (localGraphData !== mapDetail && mapEditable) {
			return window.confirm('当前架构图还没有被保存，退出后将无法找回，确认退出吗？');
		}
		return true;
	};

	// 静默自动保存
	const handleCloudSaveSilent = async (cloudMapDetail) => {
		if (!mapEditable || !sigma) return;
		setDeleteFlag(false);
		try {
			const Detail = sigma.getLocalGraphData();
			const SvgFile = await sigma.getRenderedXML();
			const baseParams = {
				...pick(cloudMapDetail, ['MapName', 'AppId', 'MapUUId', 'VersionUUID']),
				Detail,
				SvgFile,
				OperationType: OPRATION_TYPE.MANUAL,
			};
			const extParams = {
				...pick(cloudMapDetail, ['Industry', 'CustomerName', 'TAMGroup', 'ProjectType', 'Comment']),
				Operator: operator,
			};
			const reqParams = isExSource ? { ...baseParams } : { ...baseParams, ...extParams };
			const res = await ModifyCloudMapBaseInfo(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
				clearInterval(autoSaveTimer);
			} else {
				const { CurrentVersion, VersionUUID } = res;
				if (!CurrentVersion) {
					handleVeisionConflict(VersionUUID);
					return;
				}
				setCanCover(false);
				setDeleteFlag(true);
				setCloudMapDetail({ ...cloudMapDetail, Detail, VersionUUID });
				setNodeListSync();
				message.success({ content: '保存架构图成功' });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			clearInterval(autoSaveTimer);
			message.error({ content: msg });
		}
	};

	// 点击删除架构图
	const handleDelCloudMapBtnClick = () => {
		if (!MapUUId) {
			message.error({ content: '当前还没有保存架构图，不能删除' });
			return;
		}
		Modal.confirm({
			icon: 'warning',
			message: '确认删除当前架构图？',
			description: `删除 “${MapName}” 架构图后不能恢复，是否确定？`,
			okText: '删除',
			cancelText: '取消',
			onOk: () => delCloudMap(),
		});
	};

	// 删除架构图
	const delCloudMap = async () => {
		try {
			const res = await DeleteCloudMapBaseInfo({ AppId, MapUUIds: [MapUUId], Operator: operator });
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '删除成功' });
				history.push('/advisor/architecture');
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 加载新的模板
	const loadNewTpl = (tplInfo) => {
		const { Detail } = tplInfo;
		sigma.initWithTPLGraphData(Detail);
		sigma.zoomGraphFit();
		getMapItemIsInstance();
		closeTplPickerDialog();
	};

	// 创建 or 保存模板
	const handleTemplateCreateOrSave = async (values) => {
		if (!mapEditable) return;
		try {
			const Detail = sigma.getLocalGraphData();
			const SvgFile = await sigma.getRenderedXML();
			const baseParams = {
				...values,
				AppId: 1253985742,
				Property: +values.Property,
				Detail,
				SvgFile: initStrColor(SvgFile),
			};
			const reqParams = isExistTemplate
				? {
					...baseParams,
					MapTemplateUUId: templateDetail.MapTemplateUUId,
					Operator: operator,
					OperationType: OPRATION_TYPE.MANUAL,
				}
				: { ...baseParams, Creator: operator };
			const res = isExistTemplate
				? await ModifyCloudMapTemplate(reqParams)
				: await CreateCloudMapTemplate(reqParams);
			if (res.Error) {
				setTemplateDetail({ ...reqParams, Property: `${reqParams.Property}` });
				message.error({ content: res.Error.Message });
				clearInterval(autoSaveTimer);
				closeCreateDialog();
			} else {
				setTemplateDetail({ ...reqParams, Property: `${reqParams.Property}` });
				message.success({ content: isExistTemplate ? '保存模板成功' : '创建模板成功' });
				closeCreateDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 保存架构图
	const handleCloudMapSave = async (values, { custName, creatorDep, leadDep = '' }) => {
		if (!mapEditable) return;
		try {
			const Detail = sigma.getLocalGraphData();
			const SvgFile = await sigma.getRenderedXML();
			const baseParams = {
				...values,
				AppId: +values.AppId,
				Detail,
				SvgFile,
				MapUUId,
				VersionUUID,
				OperationType: OPRATION_TYPE.MANUAL,
			};
			const extParams = {
				CustomerName: custName,
				CreaterDepartment: creatorDep,
				LeadDepartment: leadDep,
				Operator: operator,
			};
			const reqParams = isExSource ? { ...baseParams } : { ...baseParams, ...extParams };
			const res = await ModifyCloudMapBaseInfo(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
				clearInterval(autoSaveTimer);
			} else {
				const { VersionUUID } = res;
				if (!res.CurrentVersion) {
					closeCreateDialog();
					handleVeisionConflict(VersionUUID);
					return;
				}
				setCloudMapDetail({ ...cloudMapDetail, ...reqParams, VersionUUID });
				message.success({ content: '保存架构图成功' });
				setNodeListSync();
				closeCreateDialog();
			}
		} catch (err) {
			clearInterval(autoSaveTimer);
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 冲突解决
	const handleVeisionConflict = (VersionUUID) => {
		clearInterval(autoSaveTimer);
		Modal.alert({
			// @ts-ignore
			caption: '保存版本冲突',
			type: 'warning',
			maskClosable: false,
			disableCloseIcon: true,
			size: 'l',
			message:
				'此架构图在其他地方被修改，与当前架构图冲突，此版本是否覆盖其他版本（如不做“覆盖”或者另存为操作，此页更新部分将不保存）',
			buttons: [
				<Button type="primary" onClick={() => handleCloudMapCover(VersionUUID)}>
					覆盖
				</Button>,
				<Button type="primary" onClick={handleNewMapCreate}>
					另存为
				</Button>,
			],
		});
	};

	// 覆盖最新版本图
	const handleCloudMapCover = (VersionUUID) => {
		setCanCover(true);
		setCloudMapDetail({ ...cloudMapDetail, VersionUUID });
	};

	useEffect(() => {
		if (canCover) {
			handleCloudSaveSilent(cloudMapDetail);
		}
	}, [cloudMapDetail.VersionUUID]);

	// 架构图另存为 or 克隆
	const handleNewMapCreate = () => {
		setIsExistCloudMap(false);
		openCreateDialog();
	};
	const handleNewCloudSave = async (values, { custName, creatorDep, leadDep = '' }) => {
		const Detail = sigma.getLocalGraphData();
		const SvgFile = await sigma.getRenderedXML();
		try {
			const baseParams = {
				...values,
				AppId: +values.AppId,
				Detail,
				SvgFile: initStrColor(SvgFile),
			};
			const extParams = {
				CustomerName: custName,
				CreaterDepartment: creatorDep,
				LeadDepartment: leadDep,
				Creator: operator,
			};
			const reqParams = isExSource
				? { ...baseParams, source: locationParam.source }
				: { ...baseParams, ...extParams };
			const res = await CreateCloudMapBaseInfo(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '创建架构图成功' });
				if (isExSource) {
					history.push(`/advisor/architecture/cloud-editor/${res.MapUUId}?${getUrlParamsStr(locationParam)}&projectType=${res.ProjectType}`);
					location.reload();
				} else {
					history.push(`/advisor/architecture/cloud-editor/${res.MapUUId}?optype=0&projectType=${res.ProjectType}`);
					location.reload();
				}
				notification.warning({ description: '请点击保存，查看架构图基础信息是否正确！' });
				setIsExistCloudMap(true);
				closeCreateDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// =============架构巡检逻辑==============
	// 查看巡检报告
	const handleViewReportBtnClick = (obj = null) => {
		const authorList = JSON.parse(getStorage('authors'));
		if (isEmpty(authorList) || !authorList[MapUUId] || !includes(authorList[MapUUId], operator)) {
			Modal.alert({
				// @ts-ignore
				caption: '客户授权',
				maskClosable: true,
				size: 'm',
				message: (
					<Checkbox
						className="cloud-author__radio"
						defaultChecked={false}
						onChange={handleClientAuthorChange}
					>
						<span style={{ color: 'red' }}>*</span>
						我已与客户沟通本次巡检会对客户资源进行扫描，并获得客户授权
					</Checkbox>
				),
				buttons: [<Button id="authorBtn" style={{ visibility: 'hidden' }} />],
			});
			return;
		}
		if (obj) {
			if (!obj.lastTaskId) {
				message.warning({ content: '未有已完成的巡检任务， 每天凌晨自动巡检，巡检完成后，可查看报告' });
				return;
			}
		} else if (!taskObj.lastTaskId) {
			message.warning({ content: '未有已完成的巡检任务， 每天凌晨自动巡检，巡检完成后，可查看报告' });
			return;
		}
		openReportDrawer();
	};

	const handleClientAuthorChange = (value) => {
		if (!value) {
			message.warning({ content: '未得到客户授权，禁止打开巡检报告' });
			return;
		}
		const authorList = JSON.parse(getStorage('authors')) || {};
		if (!authorList[MapUUId]) {
			authorList[MapUUId] = [];
		}
		authorList[MapUUId].push(operator);
		setStorage('authors', JSON.stringify(authorList));
		document.getElementById('authorBtn').click();
	};

	// 绑定资源
	const handleResourceBind = () => {
		if (!currentNode.linkTextLabel) {
			message.warning({
				content: (
					<>
						组件关联节点名称为空，无法绑定资源，具体操作请参考
						<ExternalLink href={operateDesHref}>操作说明</ExternalLink>
					</>
				),
				duration: 7 * 1000,
			});
			return;
		}
		openPanelDialog();
	};

	// 清空节点组件信息
	const delCloudMapItem = async () => {
		try {
			const res = await DeleteCloudMapItem({
				MapUUId,
				Ids: [currentNode.key],
				AppId,
				BindingTypes: allBindType,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			setIsDelNode(!isDelNode);
			message.success({ content: '清空成功' });
			getMapItemIsInstance();
			setNodeStyleByKey(sigma, currentNode, { attr: 'fillDark', value: '#E2E6EC' });
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		handleCloudSaveSilent(cloudMapDetail);
	}, [isDelNode]);

	// 节点操作集
	const bindNodeFuns = {
		openDrawer,
		handleResourceBind,
		delCloudMapItem,
	};

	const popConfirmFooter = close => (
		<>
			<Button
				type="primary"
				onClick={() => {
					handleCloudSaveSilent({
						...cloudMapDetail,
						MapName: mapNameEditing,
					});
					close();
				}}
			>
				保存
			</Button>
			<Button
				onClick={() => {
					setMapNameEditing(MapName);
					close();
				}}
			>
				取消
			</Button>
		</>
	);

	const DropOperateMenu = close => (
		<List type="option">
			<PopConfirm
				title="编辑架构图名称"
				message={<Input size="m" value={mapNameEditing} onChange={value => setMapNameEditing(value)} onKeyDown={e => e.stopPropagation()} />}
				footer={popConfirmFooter}
				placement="right"
			>
				<List.Item>重命名</List.Item>
			</PopConfirm>
			<List.Item onClick={() => {
				handleNewMapCreate();
				close();
			}}>
				克隆当前架构图
			</List.Item>
			{!isExSource && (
				<List.Item onClick={() => {
					handleDelCloudMapBtnClick();
					close();
				}}>
					删除
				</List.Item>
			)}
		</List>
	);

	// 查看转编辑模式
	const handleCloudMapEdit = () => {
		history.push(`/advisor/architecture/cloud-editor/${mapUUId}?optype=0&projectType=${projectType}`);
	};

	return (
		<Layout.Body className="cloud-body" style={{ position: 'relative' }}>
			{/* 顶部工具导航栏 */}
			<NavMenu
				className="intlc-cloud-nav"
				left={
					<>
						<div className='cloud-editor-hd-left'>
							<NavMenu.Item >
								<Button
									type='text'
									className='nav-item-home-btn'
									onClick={() => history.push('/advisor/architecture')}
								>
									<div className="nav-item-svg__box nav-home-btn">
										<IconTsa type='icon-home' className='home-icon' />
										<span className='nav-item-ht'>回到列表</span>
									</div>
								</Button>
							</NavMenu.Item>
							<NavMenu.Item >
								{
									mapEditable
										? <Dropdown
											className='nav-item-input'
											trigger="click"
											clickClose={false}
											button={MapName}
											children={DropOperateMenu}
										/>
										: <Bubble
											dark
											placement="top"
											trigger="hover"
											content={MapName}
										>
											<h3 className='nav-item-text'>
												{MapName}
											</h3>
										</Bubble>
								}
							</NavMenu.Item>
						</div>
						{
							mapEditable
								? <NavMenu.Item>
									<Button
										type="text"
										onClick={() => {
											openTplPickerDialog();
										}}
										className='tpl-input-btn'
									>
										<div className="nav-item-svg__box">
											<IconTsa type='icon-input' className='icon-input' />
											<span className='nav-item-ht'>导入模板</span>
										</div>
									</Button>
								</NavMenu.Item>
								: <NavMenu.Item className="nav-save-item">
									<Bubble content="另存为新的架构图" placement="bottom" dark>
										<Button
											type="text"
											className="intlc-cloud-nav-item-btn"
											onClick={() => handleNewMapCreate()}
										>
											<div className="nav-item-svg__box">
												<IconTsa type="icon-save-other" className="icon-svg" />
												<span className="nav-item-ht">另存为</span>
											</div>
										</Button>
									</Bubble>
								</NavMenu.Item>
						}
						<NavMenu.Item>
							<Bubble
								placement="bottom"
								trigger="hover"
								className="bubble-layer"
								style={{ maxWidth: 500, width: 268, zIndex: 10 }}
								content={<HelpDropDown />}
							>
								<Button type='text' className='nav-help-btn'>
									<div className="nav-item-svg__box">
										<IconTsa type='icon-help-bing' className='icon-help' />
										<span className='nav-item-ht'>帮助</span>
									</div>
								</Button>
							</Bubble>
						</NavMenu.Item>
					</>
				}
				right={
					<>
						{
							mapEditable
							&& <>
								<NavMenu.Item className='nav-item__left'>
									<Bubble content="撤回（Ctrl + Z）" placement="bottom" dark>
										<div onClick={() => sigma.undo()} className="nav-item-svg__box nav-item-svg__right">
											<IconTsa type='icon-undo-bing' />
										</div>
									</Bubble>
								</NavMenu.Item>
								<NavMenu.Item className='nav-item__left nav-item-svg__right'>
									<Bubble content="重做（Ctrl + Y）" placement="bottom" dark>
										<div onClick={() => sigma.redo()} className="nav-item-svg__box nav-item-svg__right">
											<IconTsa type='icon-redo-bing' />
										</div>
									</Bubble>
								</NavMenu.Item>
								<NavMenu.Item className='nav-item__left nav-item-svg__right'>
									<Bubble content="保存" placement="bottom" dark>
										<div onClick={() => openCreateDialog()} className="nav-item-svg__box nav-item-svg__right">
											<IconTsa type='icon-save-bing' />
										</div>
									</Bubble>
								</NavMenu.Item>
								<NavMenu.Item className='nav-item__left nav-item-svg__right'>
									<Button type="link" onClick={() => openExportMenu()}>
										导出
									</Button>
								</NavMenu.Item>
							</>
						}
						<NavMenu.Item>
							{
								!mapEditable && cloudMapDetail.CanWrite
								&& <Button style={{ marginRight: '16px' }} type="weak" onClick={() => handleCloudMapEdit()}>
									编辑
								</Button>
							}
							<ScanProcessButton
								appId={AppId}
								MapUUId={mapType ? mainMapInfo?.MapUUId : MapUUId}
								uin={uin}
								successTaskId={mapType ? mainMapInfo.SuccessAsyncId : undefined}
								isShowScanBtn={mapEditable && CanCreateTask && !mapType} // 管理员快照不可巡检
								projectType={projectType}
								onOpenReport={obj => handleViewReportBtnClick(obj)}
								onScanReportChange={val => setTaskIdObj(val)}
							/>
						</NavMenu.Item>
						{
							projectType !== '客户项目'
							&& <NavMenu.Item>
								<Bubble content="必须确认客户已授权" placement="bottom" error>
									<Button
										type="primary"
										onClick={() => handleViewReportBtnClick()}
									>
										查看报告
									</Button>
								</Bubble>
							</NavMenu.Item>
						}
					</>
				}
			/>

			{/* 左侧sidebar */}
			{mapEditable && <SidebarMenu sigma={sigma} scale={scale} currentMode={mapMode} />}

			{/* 画布容器 */}
			<div ref={editorContainer} className={mapEditable ? 'editor-container' : 'view-container'} />

			{/* 画布大小工具 */}
			<SizeScaleBar sigma={sigma} initScale={scale} isPositionOffset={nodeEditorPanelVisible && mapEditable} />

			{/* 画布浮层视野设置 */}
			<CanvasFormTool
				sigma={sigma}
				currentMode={mapMode}
				isShowRiskEntry={true}
				riskLayerVisible={riskLayerVisible}
				onRiskLayerOpen={handleRiskLayerOpen}
				onRiskLayerClose={closeRiskLayer}
				handleModeChange={handleModeChange}
				isPositionOffset={nodeEditorPanelVisible && mapEditable}
			/>

			{riskLayerVisible && (
				<RiskInfoLayer
					riskDataList={riskDataList}
					onViewBindResource={() => {
						openDrawer();
					}}
					onViewScanReport={() => {
						openReportDrawer();
					}}
				/>
			)}
			{/* 画布样式设置 */}
			{/* <SetCanvasStyleModal sigma={sigma} visible={settingVisiable} onModalClose={closeSettingDialog} /> */}

			<Modal
				maskClosable
				size="xl"
				visible={tplPickerVisible}
				caption="选择模板"
				onClose={() => closeTplPickerDialog()}
			>
				<Modal.Body>
					<TemplateListPicker handlePickTplLoad={loadNewTpl} />
				</Modal.Body>
				<Modal.Footer>
					<Button type="weak" onClick={() => closeTplPickerDialog()}>
						取消
					</Button>
				</Modal.Footer>
			</Modal>

			{/* 保存架构图 */}
			{createMapDialogVisiable && (
				<Modal visible={true} onClose={() => closeCreateDialog()}>
					<Modal.Body>
						<Tabs
							ceiling
							animated={false}
							tabs={isExSource ? pmTabConfig : isExistCloudMap ? tabConfig : createTabConfig}
							tabBarStyle={{ position: 'relative', top: '20px', padding: 0 }}
						>
							<TabPanel id="template">
								<Alert>保存为模板将不保存绑定的实例信息</Alert>
								<TemplateForm
									templateDetail={templateDetail}
									industryConfig={industryConfig}
									onSubmit={handleTemplateCreateOrSave}
									closeCreateDialog={closeCreateDialog}
								/>
							</TabPanel>
							<TabPanel id="cloudMap">
								<CloudMapForm
									cloudMapDetail={cloudMapDetail}
									industryConfig={industryConfig}
									isExistCloudMap={isExistCloudMap}
									onSubmit={isExistCloudMap ? handleCloudMapSave : handleNewCloudSave}
									closeCreateDialog={closeCreateDialog}
								/>
							</TabPanel>
							<TabPanel id="pmCloudMap">
								<PmCloudMapForm
									cloudMapDetail={cloudMapDetail}
									isExistCloudMap={isExistCloudMap}
									onSubmit={isExistCloudMap ? handleCloudMapSave : handleNewCloudSave}
									closeCreateDialog={closeCreateDialog}
								/>
							</TabPanel>
						</Tabs>
					</Modal.Body>
				</Modal>
			)}

			{/* 右键操作面板 */}
			{popDialogVisible && (
				<RightOperateNode
					isShowBindResource={isShowBindResource}
					isOnlyShowBind={!mapEditable}
					sigma={sigma}
					appId={AppId}
					position={popDialogPosition}
					isNodeLock={isNodeLock}
					handleNodelock={handleNodelock}
					closePanel={closePopDialog}
					bindNodeFuns={bindNodeFuns}
					projectType={projectType}
				/>
			)}

			{/* 节点详情展示面板 */}
			<CollapseTransition in={nodeEditorPanelVisible && mapEditable} timeout={10}>
				<NodeDetailPanel
					sigma={sigma}
					checkedNodes={checkedNodes}
					visible={nodeEditorPanelVisible && mapEditable}
					riskDataList={[]}
					domRef={editorContainer}
					defaultCheckedStyle={checkedStyles}
					closeDrawer={() => closeNodeEditorPanel()}
				/>
			</CollapseTransition>

			<ExportMenu
				sigma={sigma}
				onClose={() => closeExportMenu()}
				visible={exportVisiable}
				graph={cloudMapDetail}
			/>

			{/* 资源绑定 */}
			{panelVisiable && (
				<ResourceBindNodePicker
					sigma={sigma}
					appId={AppId}
					MapUUId={MapUUId}
					uin={`${uin}`}
					node={currentNode}
					productName={nodeToProductMap[currentNode.name]}
					onResourceBind={() => handleCloudSaveSilent(cloudMapDetail)}
					onUpdateResource={() => getMapItemIsInstance()}
					openResourceList={() => openDrawer()}
					closeDialog={() => closePanelDialog()}
				/>
			)}

			{/* 绑定资源列表 */}
			{insListDrawerVisible && (
				<ResourceBoundTable
					appId={AppId}
					MapUUId={MapUUId}
					sigma={sigma}
					disableClearIns={mapEditable}
					productName={nodeToProductMap[currentNode.name]}
					node={currentNode}
					delCloudMapItem={delCloudMapItem}
					closeDrawer={() => closeDrawer()}
				/>
			)}

			{/* 查看报告 */}
			{reportVisible && (
				<CloudReport
					appId={AppId}
					cloudDetail={cloudMapDetail}
					MapUUId={mapType ? mainMapInfo?.MapUUId : MapUUId}
					successTaskId={mapType ? mainMapInfo.SuccessAsyncId : undefined}
					closeReportDrawer={() => closeReportDrawer()}
				/>
			)}
			{/* 路由切换提示 */}
			<Prompt message={handlePrompt} />
		</Layout.Body>
	);
}
