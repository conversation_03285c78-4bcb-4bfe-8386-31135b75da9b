
.intlc-cloud-nav {
	background-color: #fff;
	box-shadow: 2px 5px 10px  rgba(0, 0, 0, 0.2);
	z-index: 10;

	.nav-save-item {
		margin-left: 12px;
	}
	.cloud-editor-hd-left {
		position: relative;
		display: flex;
		justify-content: space-between;
		width: 252px;
		padding-right: 16px;

		.tea-dropdown__value {
			font-size: 14px;
			font-weight: bold;
			color: rgba(0, 0, 0, 0.9);
			margin-right: 12px;
			max-width: 100px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.nav-item-input {
			margin-top: 3px;
		}
		&::after {
			content: '';
			display: block;
			position: absolute;
			top: 10px;
			right: 0;
			width: 1px;
			height: 16px;
			background-color: #CFD5DE;
		}
	}
	
	.intlc-cloud-nav-item-icon {
		margin-left: 10px;
		cursor: pointer;
	}

	.nav-item-home-btn {
		background: #F1F2F5;
		.home-icon {
			margin-right: 9px;
			margin-top: 3px;
		}
	}

	.nav-help-btn {
		margin-left: 15px;
		.icon-help {
			margin-right: 5px;
			margin-top: 4px;
		}
	}
 
	.nav-item__left {
		margin-right: 24px;
	}

	.nav-item-svg__box {
		display: flex;
		justify-content: space-around;
		align-items: center;
		.icon-svg {
			display: flex;
			align-items: center;
			margin-right: 3px;
		}
		.nav-item-ht {
			line-height: 20px;
		}
		.icon-help_svg {
			display: flex;
			align-items: center;
			margin-right: 3px;
			svg {
				width: 20px;
				height: 20px;
			}
		}
	}

	.nav-home-btn {
		padding: 0 3px;
	}

	.tpl-input-btn {
		margin-left: 17px;
		.icon-input {
			margin-top: 5px;
			margin-right: 5px;
		}
	}

	.nav-item-svg__box {
		cursor: pointer;
	}

	.nav-item-svg__right {
		margin-top: 2px;
	}

	.nav-item-text {
		line-height: 33px;
		text-overflow:ellipsis;
		overflow:hidden;
		white-space:nowrap;
		width:147px;
	}

}

.scrollbar {
	&::-webkit-scrollbar {
		width: 4px;
		height: 6px;
	}
	&::-webkit-scrollbar-thumb {
		background: #888;
		border-radius: 20px;
	}
	&::-webkit-scrollbar-track {
	 	border-radius: 20px;
	}
}


.cloud-author__radio {
	display: flex;
	align-items: center;
}

.drawer-header {
	.tea-h3 {
		display: flex;
		align-items: center;
	}
	.tea-justify-grid__col--left {
		display: flex;
		align-items: center;
	}
}
.drawer-title {
	height: 28px;
	span {
		font-size: 17px;
		padding-top: 3px;
	}
}
.editor-container {
	position: absolute;
	top: 45px;
	left: 280px;
	width: calc(100% - 280px);
	height: calc(100% - 50px);

	svg:not(:root) {
		overflow: visible !important;
	}
}

.view-container {
	position: absolute;
	top: 45px;
	left: 0;
	width: 100%;
	height: calc(100% - 50px);
}

.editor-container,
.view-container {
	.sigma-c-line {
		display: none !important;
	}
	#sigma-c-box {
		z-index: 1 !important;
	}
}

