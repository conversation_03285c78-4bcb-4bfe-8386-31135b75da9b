
.intlc-template-nav {
    background-color: #fff;
    box-shadow: 2px 5px 10px  rgba(0, 0, 0, 0.2);
    z-index: 10;
  
    .intlc-template-nav-item-btn__scan {
		border-radius: 5px;
		border: 1px solid #3498DB;
    }
  
    .intlc-template-nav-item-btn__report {
		border-radius: 5px;
		border: 1px solid #566573;
    }
    
    .intlc-template-nav-item__line {
		background-color: rgba(0,0,0,.12);
		display: block;
		height: 32px;
		margin-left: 16px;
		margin-right: 16px;
		width: 1px;
    }
    .icon-help_svg {
		svg {
			width: 20px;
			height: 20px;
		}
    }
    .intlc-template-nav-item-svg__box {
		cursor: pointer;
		padding-top: 4px
    }
	.nav-item-svg__box {
		display: flex;
		justify-content: space-around;
		.icon-svg {
			display: flex;
			align-items: center;
			margin-right: 3px;
		}
		.nav-item-ht {
			line-height: 20px;
		}
		.icon-help_svg {
			display: flex;
			align-items: center;
			margin-right: 3px;
			svg {
				width: 20px;
				height: 20px;
			}
		}
	}

	.tpl-editor-hd-left {
		position: relative;
		display: flex;
		justify-content: space-between;
		width: 252px;
		padding-right: 16px;

		.tea-dropdown__value {
			font-size: 14px;
			font-weight: bold;
			color: rgba(0, 0, 0, 0.9);
			margin-right: 12px;
			max-width: 100px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
		}

		.nav-item-input {
			margin-top: 3px;
		}
		&::after {
			content: '';
			display: block;
			position: absolute;
			top: 10px;
			right: 0;
			width: 1px;
			height: 16px;
			background-color: #CFD5DE;
		}
	}

	.nav-item-home-btn {
		background: #F1F2F5;
		.home-icon {
			margin-right: 9px;
			margin-top: 3px;
		}
	}

	.nav-item-svg__box {
		display: flex;
		justify-content: space-around;
		align-items: center;
		.icon-svg {
			display: flex;
			align-items: center;
			margin-right: 3px;
		}
		.nav-item-ht {
			line-height: 20px;
		}
		.icon-help_svg {
			display: flex;
			align-items: center;
			margin-right: 3px;
			svg {
				width: 20px;
				height: 20px;
			}
		}
	}

	.nav-item__left {
		margin-right: 24px;
	}
	.nav-item-svg__right {
		margin-top: 2px;
	}
	.nav-item-svg__box {
		cursor: pointer;
	}

	.nav-help-btn {
		margin-left: 15px;
		.icon-help {
			margin-right: 5px;
			margin-top: 4px;
		}
	}

	.nav-item-text {
		line-height: 33px;
		text-overflow:ellipsis;
		overflow:hidden;
		white-space:nowrap;
		width:147px;
	}
}
  
.editor-container {
	position: absolute;
	top: 45px;
	left: 280px;
	width: calc(100% - 280px);
	height: calc(100% - 50px);
	svg:not(:root) {
		overflow: visible !important;
	}
}

.view-container {
	position: absolute;
	top: 45px;
	left: 0;
	width: 100%;
	height: calc(100% - 50px);
}

.editor-mask {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	z-index: 9;
	cursor: not-allowed;
}

.editor-container,
.view-container {
	.sigma-c-line {
		display: none !important;
	}
	#sigma-c-box {
		z-index: 1 !important;
	}
}