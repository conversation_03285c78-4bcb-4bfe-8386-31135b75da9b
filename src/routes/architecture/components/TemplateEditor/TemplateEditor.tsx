/* 图形组件列表 */
import React, { useState, useEffect, useRef, useMemo } from 'react';
import { Prompt } from 'react-router';
import { useHistory } from '@tea/app';
import './style.less';
import Sigma, { SigmaType } from '@tencent/sigma-editor';
import { getStorage, setStorage } from '@src/utils/storage';
import {
	Button,
	Bubble,
	Input,
	PopConfirm,
	message,
	NavMenu,
	Modal,
	Layout,
	notification,
	Dropdown,
	List,
	CollapseTransition,
} from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import { TemplateForm } from '../EditorCenterSvg/TemplateForm';
import {
	RightOperateNode,
	SizeScaleBar,
	NodeDetailPanel,
	CanvasFormTool,
	HelpDialog,
	SidebarMenu,
	ExportMenu,
	HelpDropDown,
} from '../../components';

import { OPRATION_TYPE, OPERATE_TYPE } from '../../conf/eidtorConfig';
import { FILE_TYPE } from '@src/routes/architecture/conf/architecture';

import { isElement, random, pick, find, isEmpty, map, toPairs } from 'lodash';
import { getUrlParamFromLocation } from '@src/utils/common';
import { getNodeStyleByKey, setNodeStyleByKey, initStrColor } from '@src/routes/architecture/utils';
import { useToggle } from '../../hooks/common';

import {
	CreateCloudMapTemplate,
	ModifyCloudMapTemplate,
	DeleteCloudMapTemplate,
	DescribeCloudMapConfig,
	GetArchitectureTemplateList,
} from '@src/api/architecture/architecture';

const defaultDialogPosition = {
	x: 0,
	y: 0,
};

export function TemplateEditor(match) {
	const history = useHistory();
	// 操作人
	const operator = localStorage.getItem('engName');

	const [canCover, setCanCover] = useState(false);

	const mapTemplateUUId = match.match.params.tplid;

	const locationParam = getUrlParamFromLocation(['optype', 'source'], location);

	// @ts-ignore
	const mapEditable = locationParam?.optype === OPERATE_TYPE.WRITE;

	// 模板信息
	const [templateDetail, setTemplateDetail] = useState<any>({});
	const { TemplateName, MapTemplateUUId, Detail: mapDetail, VersionUUID } = templateDetail;
	const [tempNameEditing, setTempNameEditing] = useState(TemplateName);

	const [isExistTplMap, setIsExistTplMap] = useState(true);
	// sigma对象
	const [sigma, setSigma] = useState<SigmaType>();

	// 获取编辑器DOM
	const editorContainer = useRef();

	// 导出文件抽屉
	const [exportVisiable, openExportMenu, closeExportMenu] = useToggle(false);

	// 节点锁定状态
	const [isNodeLock, lockNode, unlockNode] = useToggle(false);

	// 保存模板弹窗
	const [createMapDialogVisiable, openCreateDialog, closeCreateDialog] = useToggle(false);
	// 节点编辑菜单panel
	const [popDialogVisible, openPopDialog, closePopDialog] = useToggle(false);
	// 节点展现位置
	const [popDialogPosition, setPopDialogPosition] = useState(defaultDialogPosition);
	// 节点属性编辑panel
	const [nodeEditorPanelVisible, openNodeEditorPanel, closeNodeEditorPanel] = useToggle(false);

	const [industryConfig, setIndustryConfig] = useState([]);

	const [checkedNodes, setCheckedNodes] = useState([]);
	const [checkedStyles, setCheckedStyles] = useState({});

	const [scale, setScale] = useState(1);
	// 当前维度
	const [mapMode, setMapMode] = useState<'2d' | '3d'>(getStorage('sigma_mode') === 'SIGMA_GRAPH_MODE_2D' ? '2d' : '3d');

	let autoSaveTimer;// 定时器 id

	// 初始化模板架构图
	useEffect(() => {
		initSigmaContainer();
	}, [MapTemplateUUId, mapEditable]);

	// 初始化模板数据
	useEffect(() => {
		getTemplateMap();
	}, [mapTemplateUUId]);

	// 初始化架构图名称
	useEffect(() => {
		setTempNameEditing(TemplateName);
	}, [TemplateName]);

	// 初始化产品信息
	useEffect(() => {
		getIndustryConfig();
	}, []);

	// 鼠标抬起事件的模拟
	useEffect(() => {
		const ele = checkedNodes[0]?.component.node.ownerSVGElement;
		const handleMouseup = (e) => {
			const right = window.innerWidth - e.clientX;
			if ((right) <= 280) {
				const mouseEvent = new MouseEvent('mouseup');
				ele.dispatchEvent(mouseEvent);
			}
			window.removeEventListener('mouseup', handleMouseup);
		};
		if (nodeEditorPanelVisible) {
			if (checkedNodes.length == 1) {
				window.addEventListener('mouseup', handleMouseup);
			}
		}
		return () => {
			window.removeEventListener('mouseup', handleMouseup);
		};
	}, [nodeEditorPanelVisible, checkedNodes]);

	// 获取架构图类型信息
	const getIndustryConfig = async () => {
		try {
			const res = await DescribeCloudMapConfig({ AppId: 1253985742 });
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setIndustryConfig(res.Industry);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取模板数据
	const getTemplateMap = async () => {
		const loader = message.loading({ content: '模板初始化中，请稍后...', duration: 0 });
		try {
			const params = {
				AppId: 1253985742,
				Filters: [{ Name: 'map_tpl_uuid', Values: [`${mapTemplateUUId}`] }],
				Operator: operator,
				FileType: FILE_TYPE.ALL,
			};
			const res = await GetArchitectureTemplateList(params);
			loader.hide();
			if (res.Error) {
				message.error({ content: res.Error.Message });
				message.error({ content: '模板初始化错误！' });
			} else {
				const currentTpl = find(res.CloudMapTemplate, i => i.MapTemplateUUId === mapTemplateUUId) || {};
				if (!isEmpty(currentTpl)) {
					if ((mapEditable && currentTpl.CanWrite) || (!mapEditable && currentTpl.CanRead)) {
						setTemplateDetail(currentTpl);
						message.success({ content: '模板初始化完成！' });
						return;
					}
				}
				Modal.error({
					message: '模板ID错误或者无模板编辑权限',
					description: '无法找到当前模板ID的模板！',
					onClose: () => {
						window.open(`${window.location.origin}/advisor/architecture`, '_self');
					},
				});
			}
		} catch (err) {
			loader.hide();
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 退出校验
	const quitTemplateEditor = async () => {
		const localGraphData = sigma.getLocalGraphData();
		if (localGraphData !== mapDetail && mapEditable) {
			Modal.confirm({
				icon: 'warning',
				message: '确认退出？',
				description: '当前模板还没有被保存，退出后将无法找回，确认退出吗？',
				okText: '退出',
				cancelText: '取消',
				onOk: () => history.push('/advisor/architecture'),
			});
		} else {
			history.push('/advisor/architecture');
		}
	};

	// 初始化架构图模板
	const initSigmaContainer = () => {
		const sigmaEditorContainer = editorContainer.current;
		if (!isElement(sigmaEditorContainer)) {
			return;
		}
		const instance = new Sigma(sigmaEditorContainer, {
			mode: mapMode,
			callbacks: {
				onCheckedChange,
				onDocClick,
				onScaleChange,
				onShapeMouseRightClick,
			},
		});

		if (mapDetail) {
			instance.initWithGraphData({ content: mapDetail });
		} else {
			instance.clearPaint();
		}
		setSigma(instance);
	};

	// 定时保存模版数据
	useEffect(() => {
		if (!sigma) return;
		// @ts-ignore
		const graphDataObj = sigma.core.data.shapes;
		map(toPairs(graphDataObj), ([key, value]) => {
			const styleFillDark = getNodeStyleByKey(value, 'fillDark');
			if (styleFillDark === '#4286C5') {
				setNodeStyleByKey(sigma, value, { attr: 'fillDark', value: '#E2E6EC' });
			}
		});
		autoSaveTimer = setInterval(() => {
			handleTemplateSaveSilent(templateDetail);
		}, 1000 * 60 * 5);
		return () => {
			clearInterval(autoSaveTimer);
		};
	}, [sigma, templateDetail]);

	const onCheckedChange = (nodes) => {
		if (nodes.length === 0) return;
		openNodeEditorPanel();

		setCheckedNodes(nodes);
		if (!nodes.length) {
			setCheckedStyles({});
			return;
		}
		const attrArray = [];
		const firtNode = nodes[0];
		for (let i = 0; i < nodes.length; i++) {
			const node = nodes[i];
			attrArray.push(node.editable);
		}
		const attrs = (attrArray.shift() || []).filter(v => attrArray.every(a => a.indexOf(v) !== -1));
		const attrEditor = {};
		for (let j = 0; j < attrs.length; j++) {
			const attr = attrs[j];
			attrEditor[attr] = firtNode.styles[attr];
		}
		setCheckedStyles(attrEditor);
	};

	const onDocClick = () => {
		closePopDialog();
		closeNodeEditorPanel();
	};

	const onScaleChange = (scl) => {
		setScale(scl);
	};

	const onShapeMouseRightClick = (e) => {
		setPopDialogPosition({ x: e.clientX - 200, y: e.clientY - 50 });
		openPopDialog();
	};

	// 锁定节点
	const handleNodelock = () => {
		if (isNodeLock) {
			sigma.unlockNode();
			unlockNode();
		} else {
			sigma.lockNode();
			lockNode();
		}
	};

	// 静默自动保存
	const handleTemplateSaveSilent = async (templateDetail) => {
		if (!mapEditable) return;
		try {
			const Detail = sigma.getLocalGraphData();
			const SvgFile = await sigma.getRenderedXML();
			const Owner = JSON.stringify([operator, 'runmouzou', 'ryanzczhang', 'klayzhuang', 'dannyxiang']);
			const baseParams = {
				...pick(templateDetail, ['TemplateName', 'PropertyDesc', 'Scenario', 'Industry', 'VersionUUID']),
				AppId: 1253985742,
				Property: +templateDetail.Property,
				OperationType: OPRATION_TYPE.MANUAL,
				Owner,
				Detail,
				SvgFile,
			};
			const reqParams = { ...baseParams, MapTemplateUUId, Operator: operator };
			const res = await ModifyCloudMapTemplate(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
				clearInterval(autoSaveTimer);
			} else {
				const { CurrentVersion, VersionUUID } = res;
				if (!CurrentVersion) {
					handleVeisionConflict(VersionUUID);
					return;
				}
				setCanCover(false);
				setTemplateDetail({ ...templateDetail, Detail, VersionUUID });
				message.success({ content: '保存模板成功' });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			clearInterval(autoSaveTimer);
			message.error({ content: msg });
		}
	};

	// 画图快捷键
	useEffect(() => {
		document.body.style.userSelect = 'none';
		document.addEventListener('keydown', keyboardEvent);
		return () => {
			document.body.style.userSelect = null;
			document.removeEventListener('keydown', keyboardEvent);
		};
	});

	const keyboardEvent = (e) => {
		switch (true) {
			case (e.ctrlKey || e.metaKey) && (e.key === 'f' || e.key === 'F'):
				e.preventDefault();
				handleHelpBtnClick();
				break;
			case (e.ctrlKey || e.metaKey) && (e.key === 's' || e.key === 'S'):
				e.preventDefault();
				handleTemplateSaveSilent(templateDetail);
				break;
			default:
		}
	};

	const popConfirmFooter = close => (
		<>
			<Button
				type="primary"
				onClick={() => {
					handleTemplateSaveSilent({
						...templateDetail,
						TemplateName: tempNameEditing,
						Detail: sigma.getLocalGraphData(),
					});
					close();
				}}
			>
				保存
			</Button>
			<Button
				onClick={() => {
					// setTempNameEditing(TemplateName);
					close();
				}}
			>
				取消
			</Button>
		</>
	);

	// 删除云架构图模版
	const handleDelCloudTemplate = () => {
		if (!MapTemplateUUId) {
			message.error({ content: '当前还没有保存模版，不能删除' });
			return;
		}
		Modal.confirm({
			icon: 'warning',
			message: '确认删除当前模板？',
			description: `删除 “${TemplateName}” 模板后不能恢复，是否确定？`,
			okText: '删除',
			cancelText: '取消',
			onOk: () => delCloudTemplate(),
		});
	};

	// 删除模模板
	const delCloudTemplate = async () => {
		try {
			const res = await DeleteCloudMapTemplate({
				AppId: 1253985742,
				MapTemplateUUIds: [MapTemplateUUId],
				Operator: operator,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '删除成功' });
				history.push('/advisor/architecture');
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 保存模板
	const handleTemplateSave = async (values) => {
		try {
			const Detail = sigma.getLocalGraphData();
			const SvgFile = await sigma.getRenderedXML();
			const Owner = JSON.stringify([operator, 'runmouzou', 'ryanzczhang', 'klayzhuang', 'dannyxiang']);
			const baseParams = {
				...values,
				AppId: 1253985742,
				Property: +values.Property,
				Owner,
				VersionUUID,
				Detail,
				SvgFile,
				Operator: operator,
				OperationType: OPRATION_TYPE.MANUAL,
			};
			const reqParams = { ...baseParams, MapTemplateUUId };
			const res = await ModifyCloudMapTemplate(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
				closeCreateDialog();
			} else {
				const { VersionUUID } = res;
				if (!res.CurrentVersion) {
					closeCreateDialog();
					handleVeisionConflict(VersionUUID);
					return;
				}
				setTemplateDetail({ ...templateDetail, ...reqParams, Property: `${reqParams.Property}`, VersionUUID });

				message.success({ content: '保存模板成功' });
				closeCreateDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 解决版本冲突
	const handleVeisionConflict = (VersionUUID) => {
		clearInterval(autoSaveTimer);
		Modal.alert({
			// @ts-ignore
			caption: '保存版本冲突',
			type: 'warning',
			maskClosable: false,
			disableCloseIcon: true,
			size: 'l',
			message:
				'此模板在其他地方被修改，与当前模板冲突，此版本是否覆盖其他版本（如不做“覆盖”或者另存为操作，此页更新部分将不保存）',
			buttons: [
				<Button type="primary" onClick={() => handleCloudMapCover(VersionUUID)}>
					覆盖
				</Button>,
				<Button type="primary" onClick={handleNewMapCreate}>
					另存为
				</Button>,
			],
		});
	};

	// 覆盖最新版本图
	const handleCloudMapCover = (VersionUUID) => {
		setCanCover(true);
		setTemplateDetail({ ...templateDetail, VersionUUID });
	};

	// 模板另存为 or 克隆
	const handleNewMapCreate = () => {
		setIsExistTplMap(false);
		openCreateDialog();
	};
	const handleNewCloudSave = async (values) => {
		try {
			const Detail = sigma.getLocalGraphData();
			const SvgFile = await sigma.getRenderedXML();
			const Owner = JSON.stringify([operator, 'runmouzou', 'ryanzczhang', 'klayzhuang', 'dannyxiang']);
			const baseParams = {
				...values,
				AppId: 1253985742,
				Property: +values.Property,
				Owner,
				Detail,
				SvgFile: initStrColor(SvgFile),
			};
			const reqParams = { ...baseParams, Creator: operator };
			const res = await CreateCloudMapTemplate(reqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '创建模板成功' });
				history.push(`/advisor/architecture/template-editor/${res.MapTemplateUUId}?optype=0`);
				notification.warning({ description: '请点击保存，查看模板基础信息是否正确！' });
				setIsExistTplMap(true);
				closeCreateDialog();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		if (canCover) {
			handleTemplateSaveSilent(templateDetail);
		}
	}, [templateDetail.VersionUUID]);

	// 路由守卫
	const handlePrompt = () => {
		const localGraphData = sigma.getLocalGraphData();
		if (localGraphData !== mapDetail && mapEditable) {
			return window.confirm('当前模板还没有被保存，退出后将无法找回，确认退出吗？');
		}
		return true;
	};

	// 查看转编辑模式
	const handleCloudMapEdit = () => {
		history.push(`/advisor/architecture/template-editor/${MapTemplateUUId}?optype=0`);
	};

	// 打开帮助弹框
	const handleHelpBtnClick = () => {
		Modal.alert({
			// @ts-ignore
			caption: '帮助',
			maskClosable: true,
			size: '90%',
			message: <HelpDialog />,
			buttons: [],
		});
	};

	// 维度切换方法
	const handleModeChange = (mode: '2d' | '3d') => {
		if (mapMode !== mode) {
			setMapMode(mode);
			sigma.toggleMode();
			setStorage('sigma_mode', mode === '3d' ? 'SIGMA_GRAPH_MODE_3D' : 'SIGMA_GRAPH_MODE_2D');
		}
	};

	const DropOperateMenu = close => (
		<List type="option">
			<PopConfirm
				title="编辑模板名称"
				message={<Input size="m" value={tempNameEditing} onChange={value => setTempNameEditing(value)} onKeyDown={e => e.stopPropagation()} />}
				footer={popConfirmFooter}
				placement="right"
			>
				<List.Item>重命名</List.Item>
			</PopConfirm>
			<List.Item onClick={() => {
				handleNewMapCreate();
				close();
			}}>
				克隆当前模板
			</List.Item>
			<List.Item onClick={() => {
				handleDelCloudTemplate();
				close();
			}}>
				删除
			</List.Item>
		</List>
	);

	return (
		<Layout.Body className="cloud-body" style={{ position: 'relative' }}>
			<NavMenu
				className="intlc-template-nav"
				left={
					<>
						<div className='tpl-editor-hd-left'>
							<NavMenu.Item>
								<Button
									type='text'
									className='nav-item-home-btn'
									onClick={() => history.push('/advisor/architecture')}
								>
									<div className="nav-item-svg__box nav-home-btn">
										<IconTsa type='icon-home' className='home-icon' />
										<span className='nav-item-ht'>回到列表</span>
									</div>
								</Button>
							</NavMenu.Item>
							<NavMenu.Item >
								{
									mapEditable
										? <Dropdown
											className='nav-item-input'
											trigger="click"
											clickClose={false}
											button={TemplateName}
											children={DropOperateMenu}
										/>
										: <h3 className='nav-item-text'>
											{TemplateName}
										</h3>
								}
							</NavMenu.Item>
						</div>
						<NavMenu.Item>
							<Bubble
								placement="bottom"
								trigger="hover"
								className="bubble-layer"
								style={{ maxWidth: 500, width: 268, zIndex: 10 }}
								content={<HelpDropDown />}
							>
								<Button type='text' className='nav-help-btn' onClick={handleHelpBtnClick}>
									<div className="nav-item-svg__box">
										<IconTsa type='icon-help-bing' className='icon-help' />
										<span className='nav-item-ht'>帮助</span>
									</div>
								</Button>
							</Bubble>
						</NavMenu.Item>
					</>
				}
				right={<>
					{
						mapEditable
						&& <>
							<NavMenu.Item className='nav-item__left'>
								<Bubble content="撤回（Ctrl + Z）" placement="bottom" dark>
									<div onClick={() => sigma.undo()} className="nav-item-svg__box nav-item-svg__right">
										<IconTsa type='icon-undo-bing' />
									</div>
								</Bubble>
							</NavMenu.Item>
							<NavMenu.Item className='nav-item__left nav-item-svg__right'>
								<Bubble content="重做（Ctrl + Y）" placement="bottom" dark>
									<div onClick={() => sigma.redo()} className="nav-item-svg__box nav-item-svg__right">
										<IconTsa type='icon-redo-bing' />
									</div>
								</Bubble>
							</NavMenu.Item>
							<NavMenu.Item className='nav-item__left nav-item-svg__right'>
								<Bubble content="保存" placement="bottom" dark>
									<div onClick={() => openCreateDialog()} className="nav-item-svg__box nav-item-svg__right">
										<IconTsa type='icon-save-bing' />
									</div>
								</Bubble>
							</NavMenu.Item>
							<NavMenu.Item className='nav-item__left nav-item-svg__right'>
								<Button type="link" onClick={() => openExportMenu()}>
									导出
								</Button>
							</NavMenu.Item>
						</>
					}
					{
						!mapEditable
						&& templateDetail.CanWrite
						&& <NavMenu.Item className='nav-item-edit-btn'>
							<Button type="primary" onClick={() => handleCloudMapEdit()}>
								编辑
							</Button>
						</NavMenu.Item>
					}
				</>}
			/>
			{/* 侧边栏 */}
			{mapEditable && <SidebarMenu sigma={sigma} scale={scale} currentMode={mapMode} />}

			{/* 画布 */}
			<div ref={editorContainer} className={mapEditable ? 'editor-container' : 'view-container'}></div>

			{/* 画布操作 */}
			<SizeScaleBar sigma={sigma} initScale={scale} isPositionOffset={nodeEditorPanelVisible && mapEditable} />

			{/* 路由切换提示 */}
			<Prompt message={handlePrompt} />

			{/* 画布功能设置 */}
			<CanvasFormTool sigma={sigma} isShowRiskEntry={false} isPositionOffset={nodeEditorPanelVisible && mapEditable} currentMode={mapMode} handleModeChange={handleModeChange} />

			{/* 画布演样式设置 */}
			{/* <SetCanvasStyleModal sigma={sigma} visible={settingVisiable} onModalClose={closeSettingDialog} /> */}

			{/* 保存弹窗 */}
			{
				createMapDialogVisiable && (
					<Modal
						caption={isExistTplMap ? '保存架构图模板' : '另存为（克隆）架构图模板'}
						maskClosable
						visible
						onClose={() => closeCreateDialog()}
					>
						<Modal.Body>
							<TemplateForm
								templateDetail={templateDetail}
								isExistCloudTpl={true}
								industryConfig={industryConfig}
								onSubmit={isExistTplMap ? handleTemplateSave : handleNewCloudSave}
								closeCreateDialog={closeCreateDialog}
							/>
						</Modal.Body>
					</Modal>
				)
			}

			<ExportMenu
				sigma={sigma}
				onClose={() => closeExportMenu()}
				visible={exportVisiable}
				graph={templateDetail}
			/>

			{/* 右键操作面板 */}
			{
				popDialogVisible && (
					// @ts-ignore
					<RightOperateNode
						isShowBindResource={false}
						sigma={sigma}
						position={popDialogPosition}
						isNodeLock={isNodeLock}
						handleNodelock={handleNodelock}
						closePanel={closePopDialog}
					/>
				)
			}

			{/* 节点详情展示面板 */}
			<CollapseTransition in={nodeEditorPanelVisible && mapEditable} timeout={10}>
				<NodeDetailPanel
					sigma={sigma}
					checkedNodes={checkedNodes}
					visible={nodeEditorPanelVisible && mapEditable}
					riskDataList={[]}
					domRef={editorContainer}
					defaultCheckedStyle={checkedStyles}
					closeDrawer={() => closeNodeEditorPanel()}
				/>
			</CollapseTransition>
		</Layout.Body >
	);
}
