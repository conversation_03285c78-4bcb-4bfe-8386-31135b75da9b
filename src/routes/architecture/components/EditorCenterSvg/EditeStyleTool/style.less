.tool-bar{
	display: flex;
	flex-direction: column;

  	.edit-panel-new {
		padding: 12px 20px;
		transition: all .3s ease-in-out;

		.panel-row {
			display: flex;
			flex-direction: column;
			
			.edit-panel-info {
				background-color: #e0e0e0;
				border-radius: 3px;
				font-size: 18px;
				font-weight: 700;
				line-height: 55px;
				margin-right: 20px;
				padding: 0 12px;
			}

			.edit-panel-item {
				cursor: pointer;
			}
			.edit-panel-item-row {
				display: flex;
				flex-direction: row;
				justify-content: space-between;
				align-items: center;

				.edit-panel-hd {
					margin-bottom: 0;
				}
			}

			.edit-panel-bd {
				position: relative;
				align-items: center;
				display: flex;
				height: 32px;
				justify-content: center;
				min-width: 48px;
				cursor: pointer;

				.text-area-tool {
					z-index: 1;
				}

				.input-number-tool {
					.tea-input {
						width: calc(100% - 60px);
					}
				}

				.layer-tool {
					width: 100%;
					display: flex;
					align-items: center;
					border-radius: 3px;

					&-up,
					&-down {
						display: flex;
						justify-content: center;
						align-items: center;
						width: 50%;
						height: 32px;
						border: 1px solid #E3E6EB;

						.icon-layer {
							padding-top: 3px;
							margin-right: 2px;
						}
						&:hover {
							background-color: #F1F2F5;
						}
					}

					&-up {
						border-top-left-radius: 3px;
						border-bottom-left-radius: 3px;
					}
					&-down {
						border-top-right-radius: 3px;
						border-bottom-right-radius: 3px;
					}
				}
			}
			
			.edit-panel-textarea {
				width: 200px;
				align-items: unset;
				position: relative;
				.tea-textarea {
					line-height: 1.7em;
					&:focus {
						position: absolute;
						top: 0;
						width: 100%;
					}
				}
			}

			.edit-panel-hd {
				color: #13161B;
				font-size: 12px;
				margin-bottom: 5px;
				text-transform: capitalize;
				white-space: nowrap;
			}
		}
	}
}

.edit-panel-item + .edit-panel-item {
    margin-top: 16px;
}

.color-picker {
	box-sizing: border-box;
	width: 316px;

	&-list {
		display: flex;
		flex-wrap: wrap;
		margin: 0 -6px;
	}
	&-reset {
		color: #0052d9;
		cursor: pointer;
		margin-top: 20px;
	}
	&-item {
		display: block;
		width: 40px;
		height: 24px;
		margin-bottom: 12px;
		margin-left: 6px;
		margin-right: 6px;
		border: 1px solid #f0f0f0;
		cursor: pointer;
	}
	&-input {
		color: #fff;
		font-size: 14px;
		height: 24px;
		margin-bottom: 12px;
		margin-left: 6px;
		margin-right: 6px;
		text-align: center;
		width: 92px;
	}
}

.picker-color-display {
	display: flex;
	justify-content: start;
	align-items: center;
	width: 100%;
	height: 32px;
	background: #F1F2F5;
	border-radius: 3px;

	.color-box {
		width: 24px;
		height: 24px;
		border-radius: 3px;
		margin: 0 8px;
	}
}

.color-picker-popover {
	color: rgba(0,0,0,.85);
    padding: 12px 16px;
	background-color: #fff;
	box-shadow: 0 3px 1px -2px rgb(0 0 0 / 20%), 0 2px 2px 0 rgb(0 0 0 / 14%), 0 1px 5px 0 rgb(0 0 0 / 12%);
}

.kh-tabs {
	width: 100%;
	display: flex;
		&-tab {
			border-right: 1px solid #999999;
			.tab-inner {
				width: 96px;
				margin-right: -2px;
			}

			.tab-item {
				height: 36px;
				line-height: 36px;
				border-right: 3px solid transparent;
				cursor: pointer;
				display: block;
				width: 100%;

				&.actived {
					cursor: default;
					color: #0052d9;
					border-right-color: #0052d9;
				}

				&:hover {
					color: #0052d9;
				}
			}
		}
		&-wrap {
			flex: 1;
			max-height: 396px;
			overflow-y: auto;
			&-main {
				padding: 20px;
				display: flex;
				flex-wrap: wrap;
				margin-top: -32px;
			}
			.tabs-icons-item {
				width: 96px;
				height: 96px;
				overflow: hidden;
				margin: 20px;
				cursor: pointer;
				border: 1px solid transparent;
				border-radius: 2px;
				box-sizing: border-box;
				padding-top: 8px;
				&:hover {
					border-color: #0052d9;
				}
			}
			.icon-img {
				display: block;
				text-align: center;
			}
			img {
				display: inline-block;
				height: 32px;
				width: 32px;
			}
			p {
				max-height: 40px;
				line-height: 20px;
				overflow: hidden;
			}
		}
	}

.kh-icons-filter {
	margin-bottom: 40px;
}

.kh-tabs-wrap-main {
	.ant-empty-normal {
		margin: 72px auto;
	}
}

.picker-icon-btn {
	display: flex;
	align-items: center;
	.product_icon {
		vertical-align: top;
		width: 22px;
		height: 22px;
		margin-right: 8px;
	
		svg {
			width: 100%;
			height: 100%;
			display: block;
		}
	}
}
