import React, { useState, useEffect, FC } from 'react';
import './style.less';
import { STYLE_TEXT_MAP } from '@src/routes/architecture/conf/eidtorConfig';
import axios from 'axios';

import { IconTsa } from '@src/components/IconTsa';
import { Input, Switch, Popover, InputNumber, Checkbox, Select, Button, Modal, SearchBox, message, Status } from '@tencent/tea-component';
interface INodePropertyEditorPanelProps {
	sigma: any
	checkedNodes: any
	defaultCheckedStyle: any
}

const defaultColors = [
	'transparent',
	'#ffffff',
	'#333333',
	'#666666',
	'#999999',
	'#E6E7E8',
	'#0052D9',
	'#795548',
	'#F64041',
	'#FF7800',
	'#0CBF5B',
	'#242A35',
	'#D6DBE3',
	'#3D485D',
	'#98A3B7',
	'#FF9800',
	'#F5B720',
	'#3F51B5',
	'#E91E63',
	'#F44336',
	'#9E9E9E',
];

const routeOption = [
	{ value: 'e', text: '0°' },
	{ value: 's', text: '90°' },
	{ value: 'n', text: '180°' },
	{ value: 'w', text: '270°' },
];

const strokeStyleOption = [
	{ value: 'solid', text: '实线 Solid' },
	{ value: 'dashed', text: '虚线 Dashed' },
];

const lineEndOption = [
	{ value: 'line', text: '线 Line' },
	{ value: 'arrow', text: '箭头 Arrow' },
];

const patternOption = [
	{ value: 'gary', text: '灰色 Gary' },
	{ value: 'blue', text: '蓝色 Blue' },
];

const shapeOption = [
	{ value: 'rectangular', text: '矩形' },
	// { value: 'dynamic', text: '自适应', disabled: true },
	{ value: 'dynamic', text: '自适应' },
];

const EditeStyleTool: FC<INodePropertyEditorPanelProps> = ({
	sigma,
	checkedNodes,
	defaultCheckedStyle,
}: INodePropertyEditorPanelProps) => {

	const [iconSelectModel, setIconSelectModel] = useState(false);
	const [iconCategories, setIconCategories] = useState({
		current: undefined,
		data: [],
	});
	const [iconList, setIconList] = useState({ loading: false, data: [] });
	const [filters, setFilters] = useState({
		size: 48,
		keywords: '',
	});

	const [checkedStyles, setCheckedStyles] = useState(defaultCheckedStyle);
	const handleNodeStyleChange = (value, style, attr) => {
		const data = {};
		style.value = value;
		data[attr] = style;
		sigma.setStyleNode(data, '');
		setCheckedStyles(Object.assign({}, checkedStyles, data));
	};

	// 字体数字编辑
	const renderToolNumber = (attr) => {
		const style = checkedStyles[attr];
		const val = style.value === null ? style.default : style.value;
		const toolProps = { step: 1, min: 1 };
		if (attr === 'scale') {
			toolProps.step = 0.1;
			toolProps.min = 0.1;
		} else if (attr === 'fontWeight') {
			toolProps.step = 100;
			toolProps.min = 100;
		} else if (attr === 'padding') {
			toolProps.step = 0.5;
			toolProps.min = 0.5;
		} else if (attr === 'height') {
			// @ts-ignore
			toolProps.max = 7;
		} else if (attr === 'opacity') {
			toolProps.step = 0.1;
			toolProps.min = 0.1;
			// @ts-ignore
			toolProps.max = 1.0;
		}
		return (
			<div className="edit-panel-item" key={`edit-panel--${attr}`}>
				<div className="edit-panel-hd">{STYLE_TEXT_MAP[attr]}</div>
				<div className="edit-panel-bd">
					<InputNumber
						value={val}
						size="l"
						className='input-number-tool'
						onChange={value => handleNodeStyleChange(value, style, attr)}
						{...toolProps}
					/>
				</div>
			</div>
		);
	};

	// 颜色编辑器
	const renderToolColor = (attr) => {
		const style = checkedStyles[attr];
		const color = style.value === null ? style.default : style.value;

		const ColorPicker = (
			<div className="color-picker">
				<div className="color-picker-list">
					{defaultColors.map((color, index) => (
						<span
							className="color-picker-item"
							key={`color-picker--${index}`}
							style={{
								background:
									color === 'transparent'
										? 'url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAADBJREFUOE9jPHPmzH8GPMDY2BifNAPjqAHDIgz+//+PNx2cPXsWfzoYNYCBceiHAQD1OVMJc/dYeAAAAABJRU5ErkJggg==") left center'
										: null,
								backgroundColor: color,
							}}
							onClick={() => handleNodeStyleChange(color, style, attr)}
						/>
					))}
					<Input
						size="s"
						className="color-picker-input"
						style={{ background: color }}
						maxLength={7}
						value={color}
						onChange={value => handleNodeStyleChange(value, style, attr)}
						onClick={e => e.stopPropagation()}
						onKeyDown={e => e.stopPropagation()}
						onKeyPress={e => e.stopPropagation()}
					/>
				</div>
				<span
					className="color-picker-reset"
					onClick={() => handleNodeStyleChange(null, style, attr)}
				>
					重置颜色
				</span>
			</div>
		);
		return (
			<div className="edit-panel-item" key={`edit-panel--${attr}`}>
				<div className="edit-panel-hd">{STYLE_TEXT_MAP[attr]}</div>
				<div className="edit-panel-bd">
					<Popover
						placement="left"
						overlay={ColorPicker}
						trigger="click"
						overlayClassName="color-picker-popover"
					>
						<div className='picker-color-display'>
							<div className='color-box' style={{ background: color }}></div>
							<span className='color-text'>{color}</span>
						</div>
					</Popover>
				</div>
			</div>
		);
	};
	// label文字编辑器
	const renderToolLabel = (attr) => {
		const style = checkedStyles[attr];
		const checked = style.value === null ? style.default : style.value;
		return (
			<div className="edit-panel-item" key={`edit-panel--${attr}`}>
				<div className="edit-panel-hd">标签文字</div>
				<div className="edit-panel-bd">
					<Input
						size="full"
						value={checked}
						onChange={value => handleNodeStyleChange(value, style, attr)}
						onKeyDown={e => e.stopPropagation()}
						onKeyPress={e => e.stopPropagation()}
					/>
				</div>
			</div>
		);
	};

	// boolean编辑
	const renderToolCheckbox = (attr) => {
		const style = checkedStyles[attr];
		const val = style.value === null ? style.default : style.value;
		return (
			<div className="edit-panel-item edit-panel-item-row" key={`edit-panel--${attr}`}>
				<div className="edit-panel-hd">{STYLE_TEXT_MAP[attr]}</div>
				<div className="edit-panel-bd">
					<Switch value={val} onChange={value => handleNodeStyleChange(value, style, attr)}></Switch>
				</div>
			</div>
		);
	};

	// 下拉类型编辑
	const renderToolSelect = (attr) => {
		const style = checkedStyles[attr];
		const val = style.value === null ? style.default : style.value;
		let options = null;
		switch (attr) {
			case 'route':
				options = routeOption;
				break;
			case 'line':
			case 'strokeStyle':
				options = strokeStyleOption;
				break;
			case 'lineStart':
			case 'lineEnd':
				options = lineEndOption;
				break;
			case 'pattern':
				options = patternOption;
				break;
			case 'shape':
				options = shapeOption;
				break;
			default:
				console.log(`属性工具创建错误: ${attr}`);
		}
		return (
			<div className="edit-panel-item" key={`edit-panel--${attr}`}>
				<div className="edit-panel-hd">{STYLE_TEXT_MAP[attr]}</div>
				<div className="edit-panel-bd">
					<Select
						appearance="button"
						value={val}
						matchButtonWidth
						size='full'
						options={options}
						onChange={value => handleNodeStyleChange(value, style, attr)}
					/>
				</div>
			</div>
		);
	};

	// 大段文字编辑
	const renderToolTextarea = (attr) => {
		const style = checkedStyles[attr];
		const checked = style.value === null ? style.default : style.value;
		return (
			<div className="edit-panel-item" key={`edit-panel--${attr}`}>
				<div className="edit-panel-hd">标签文字</div>
				<div className="edit-panel-bd edit-panel-textarea">
					<Input.TextArea
						rows={4}
						value={checked}
						size="full"
						className='text-area-tool'
						onChange={value => handleNodeStyleChange(value, style, attr)}
						onKeyDown={e => e.stopPropagation()}
						onKeyPress={e => e.stopPropagation()}
					/>
				</div>
			</div>
		);
	};


	// 图层
	const layerIcon = (
		<div className="edit-panel-item" key="edit-panel--up">
			<div className="edit-panel-hd">图层位置</div>
			<div className="edit-panel-bd">
				<div className='layer-tool'>
					<div onClick={() => sigma.raiseShape()} className='layer-tool-up'>
						<IconTsa type="icon-up" className='icon-layer' />
						<span>上置</span>
					</div>
					<div onClick={() => sigma.lowerShape()} className='layer-tool-down'>
						<IconTsa type="icon-down" className='icon-layer' />
						<span>下置</span>
					</div>
				</div>
			</div>
		</div>
	);


	// 渲染图标
	const renderToolProductIcon = (attr) => {
		const style = checkedStyles[attr];
		const val = style.value === null ? style.default : style.value;
		return (
			<div className="edit-panel-item" key={`edit-panel--${attr}`}>
				<div className="edit-panel-hd">{attr}</div>
				<div className="edit-panel-bd">
					<Button onClick={() => setIconSelectModel(true)} className="picker-icon-btn">
						<span className="product_icon" dangerouslySetInnerHTML={{ __html: val }}></span>
						选择图标
					</Button>
				</div>
			</div>
		);
	};
	const tools = [];
	for (const key in checkedStyles) {
		let tool;
		switch (checkedStyles[key]?.type) {
			case 'color':
				tool = renderToolColor(key);
				break;
			case 'string':
				tool = renderToolLabel(key);
				break;
			case 'textarea':
				tool = renderToolTextarea(key);
				break;
			case 'number':
				tool = renderToolNumber(key);
				break;
			case 'select':
				tool = renderToolSelect(key);
				break;
			// case 'file':
			//   tool = _renderToolImage(key);
			//   break;
			case 'boolean':
				tool = renderToolCheckbox(key);
				break;
			case 'productIcon':
				tool = renderToolProductIcon(key);
				break;
			default:
				console.error(`未知属性，请检查${checkedStyles[key]}`);
		}
		tools.push(tool);
	}
	tools.push(layerIcon);
	useEffect(() => {
		setCheckedStyles(defaultCheckedStyle);
	}, [defaultCheckedStyle]);


	const handleIconSelectOk = (icon) => {
		const style = checkedStyles.icon;
		const data = {};
		style.value = unescape(icon.source_code);
		// @ts-ignore
		data.icon = style;
		sigma.setStyleNode(data, '');
		setCheckedStyles(Object.assign({}, checkedStyles, data));
		setIconSelectModel(false);
	};

	const fetchCategories = async () => {
		try {
			const { data } = await axios.get('https://api.icon.woa.com/api/category/58f1d4cb-ec86-48ba-8df6-e235649bb42c');
			if (data.code === 200) {
				const categories = data.data.children;
				setIconCategories({ current: categories[0].key, data: categories });
				setIconList({ ...iconList, loading: true });
			} else {
				message.error({ content: '获取图标类别错误！' });
			}
		} catch (err) {
			// message.error({ content: '获取图标类别接口出错' })
		}
	};

	const fetchIcons = async () => {
		if (iconCategories.current) {
			try {
				const { data } = await axios.get(`https://api.icon.woa.com/api/category/${iconCategories.current}`);
				if (data.code === 200) {
					const { icons } = data.data;
					setIconList({ loading: false, data: icons });
					return;
				}
				setIconList({ ...iconList, loading: false });
				message.error({ content: '获取图标错误！' });
			} catch (err) {
				// message.error({ content: '获取图标接口出错' })
			}
		}
	};

	useEffect(() => {
		fetchCategories();
	}, []);

	useEffect(() => {
		fetchIcons();
	}, [iconCategories.current]);

	const icons = iconList.data.filter(i => i.size === filters.size
		&& i.visable === 1
		&& i.c_name.toLowerCase().includes(filters.keywords.toLowerCase()));

	return (
		<div className='tool-bar'>
			<div className='edit-panel-new'>
				<div className='panel-row'>{tools}</div>
			</div>
			<Modal
				caption="选择图标"
				visible={iconSelectModel}
				size="xl"
				onClose={() => setIconSelectModel(false)}
			>
				<div className="kh-icons-filter">
					<SearchBox
						placeholder="请选择你要使用的图标"
						onSearch={k => setFilters({ ...filters, keywords: k })}
						onKeyDown={e => e.stopPropagation()}
						onKeyPress={e => e.stopPropagation()}
					/>
				</div>
				<div className="kh-tabs">
					<div className="kh-tabs-tab">
						<div className="tab-inner">
							{iconCategories.data.map(c => (
								<span
									key={c.key}
									className={
										iconCategories.current === c.key
											? 'tab-item actived'
											: 'tab-item'
									}
									onClick={() => setIconCategories({ ...iconCategories, current: c.key })
									}
								>
									{c.name}
								</span>
							))}
						</div>
					</div>
					<div className="kh-tabs-wrap kh-scrollbar">
						<div className="kh-tabs-wrap-main">
							{icons.map(icon => (
								<div
									className="tabs-icons-item"
									key={icon.key}
									onClick={() => handleIconSelectOk(icon)}
								>
									<span className="icon-img">
										<img
											src={icon.url_addr.replace(
												/^http(s)?:\/\/(.*?)\//,
												'https://api.icon.woa.com/'
											)}
										/>
										<p>{icon.c_name}</p>
									</span>
								</div>
							))}
							{
								icons.length === 0
								&& <Status icon="blank" size="m" title="暂无数据" />
							}
						</div>
					</div>
				</div>
			</Modal>
		</div>
	);
};
export default EditeStyleTool;
