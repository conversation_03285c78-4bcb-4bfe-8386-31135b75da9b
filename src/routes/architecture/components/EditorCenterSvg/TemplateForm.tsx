import React, { useMemo, useState, useEffect } from 'react';
import { useForm, useField } from 'react-final-form-hooks';
import { Button, RadioGroup, SelectMultiple, Form, Input, TextArea, Radio, message } from '@tencent/tea-component';
import { pick, isEmpty, map, filter } from 'lodash';
import { FILE_TYPE } from '@src/routes/architecture/conf/architecture';
import { GetArchitectureTemplateList } from '@src/api/architecture/architecture';
function getStatus(meta, validating) {
    if (meta.active && validating) {
        return 'validating';
    }
    if (!meta.touched) {
        return null;
    }
    return meta.error ? 'error' : 'success';
}

export function TemplateForm({ templateDetail, isExistCloudTpl = false, industryConfig, onSubmit, closeCreateDialog }) {
    const [isMapTplExist, setIsMapTplExist] = useState(false);
    const operator = localStorage.getItem('engName');
    const Industry = templateDetail?.Industry ? templateDetail.Industry : [];
    const initialVal = useMemo(() => (!isEmpty(templateDetail)
        ? { ...pick(templateDetail, ['TemplateName', 'PropertyDesc', 'Scenario']), Industry, Property: `${templateDetail?.Property}` }
        : { TemplateName: '', Property: '1', PropertyDesc: '', Scenario: '', Industry: [] }), [templateDetail]);

    const validateName = (name) => {
        const regExp = new RegExp('^((?![\\/]).)*$');
        if (!name) {
            return '名称不能为空哦';
        } if (regExp.test(name)) {
            return isMapTplExist ? '模板名称重复' : undefined;
        }
        return '名称命名不能包含 / 字符';
    };
    const industryOption = useMemo(() => map(industryConfig, item => ({ text: item, value: item })), [industryConfig]);

    const getCloudTplList = async () => {
        try {
            const filters = [{ Name: 'exact_template_name', Values: [tempName.input.value] }];

            const params = {
                AppId: 1253985742,
                Filters: filters,
                FileType: FILE_TYPE.NONE,
                Operator: operator,
            };
            const res = await GetArchitectureTemplateList(params);
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                if (isExistCloudTpl) {
                    const newCloudMapList = filter(res.CloudMapTemplate, i => i.MapName !== initialVal.MapName);
                    setIsMapTplExist(newCloudMapList?.length > 0);
                } else {
                    setIsMapTplExist(res.CloudMapTemplate?.length > 0);
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };
    const { form, handleSubmit, validating, submitting } = useForm({
        onSubmit,
        initialValuesEqual: () => true,
        initialValues: initialVal,
        // @ts-ignore
        validate: ({ TemplateName, Property, PropertyDesc, Scenario, Industry }) => ({
            TemplateName: validateName(TemplateName),
            Property: !Property ? '请选择模板类别' : undefined,
            PropertyDesc: !PropertyDesc ? '模板描述不能为空哦' : undefined,
            Scenario: !Scenario ? '模板使用场景不能为空哦' : undefined,
            Industry: isEmpty(Industry) ? '行业类型不能为空哦' : undefined,
        }),
    });
    const tempName = useField('TemplateName', form);
    const type = useField('Property', form);
    const desc = useField('PropertyDesc', form);
    const sence = useField('Scenario', form);
    const industry = useField('Industry', form);

    useEffect(() => {
        getCloudTplList();
    }, [tempName.input.value]);

    return <>
        <form onSubmit={handleSubmit}>
            <Form>
                <Form.Item
                    required
                    label="架构图模板名称"
                    status={getStatus(tempName.meta, validating)}
                    message={getStatus(tempName.meta, validating) === 'error' && tempName.meta.error}
                >
                    <Input {...tempName.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入模板名称" />
                </Form.Item>
                <Form.Item
                    required
                    label="模板属性"
                    status={getStatus(type.meta, validating)}
                    message={getStatus(type.meta, validating) === 'error' && type.meta.error}
                >
                    <RadioGroup {...type.input}>
                        <Radio name="1">自定义模板</Radio>
                        <Radio name='0' disabled>公共模板</Radio>
                    </RadioGroup>
                </Form.Item>
                <Form.Item
                    required
                    label="行业类型"
                    status={getStatus(industry.meta, validating)}
                    message={getStatus(industry.meta, validating) === 'error' && industry.meta.error}
                >
                    <SelectMultiple
                        {...industry.input}
                        matchButtonWidth
                        searchable
                        staging={false}
                        placeholder="请选择行业类型"
                        options={industryOption}
                        appearance="button"
                        size="m"
                    />
                </Form.Item>
                <Form.Item
                    required
                    label="模板属性描述"
                    status={getStatus(desc.meta, validating)}
                    message={getStatus(desc.meta, validating) === 'error' && desc.meta.error}
                >
                    <TextArea {...desc.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入模板属性描述" />
                </Form.Item>
                <Form.Item
                    required
                    label="模板使用场景"
                    status={getStatus(sence.meta, validating)}
                    message={getStatus(sence.meta, validating) === 'error' && sence.meta.error}
                >
                    <TextArea {...sence.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入模板使用场景" />
                </Form.Item>
            </Form>
            <Form.Action>
                <Button
                    type="primary"
                    htmlType="submit"
                    loading={submitting}
                    disabled={validating}
                >
                    保存
                </Button>
                <Button type="weak" htmlType="button" onClick={() => {
                    closeCreateDialog();
                    return false;
                }}>取消</Button>
            </Form.Action>
        </form>
    </>;
}
