import React, { useEffect, useMemo, useState } from 'react';

import { Button, Form, Input, message } from '@tencent/tea-component';

import { pick, isEmpty, filter } from 'lodash';
import { useForm, useField } from 'react-final-form-hooks';

import { DescribeCloudMapBaseInfo } from '@src/api/architecture/architecture';
import { FILE_TYPE } from '@src/routes/architecture/conf/architecture';

function getStatus(meta, validating) {
    if (meta.active && validating) {
        return 'validating';
    }
    if (!meta.touched) {
        return null;
    }
    return meta.error ? 'error' : 'success';
}
export function PmCloudMapForm({
    cloudMapDetail,
    isExistCloudMap,
    onSubmit,
    closeCreateDialog,
}) {
    const operator = localStorage.getItem('engName');
    const [isMapNameExist, setIsMapNameExist] = useState(false);

    const initialVal = useMemo(() => (!isEmpty(cloudMapDetail)
        ? { ...pick(cloudMapDetail, ['MapName', 'AppId']) }
        : { MapName: '', AppId: '' }), [cloudMapDetail]);

    const validateName = (name) => {
        const regExp = new RegExp('^((?![\\/]).)*$');
        if (!name) {
            return '名称不能为空哦';
        } if (regExp.test(name)) {
            return isMapNameExist ? '架构图名称重复' : undefined;
        }
        return '名称命名不能包含 / 字符';
    };

    // const validateAppId = (appId) => {
    //     const { IsAuthorized, IsValid } = appIdValidater;
    //     if (!appId) {
    //         return '请输入账户AppId';
    //     } if (!isAuthorization) {
    //         return '您没有权限访问该 APPID。请到 [权限管理] 申请相关权限。';
    //     } if (!IsValid) {
    //         return 'appid不合法';
    //     }
    //     return IsAuthorized ? undefined : 'appid未开通云顾问';
    // };

    const { form, handleSubmit, validating, submitting } = useForm({
        onSubmit,
        initialValuesEqual: () => true,
        initialValues: initialVal,
        // @ts-ignore
        validate: ({ MapName, AppId }) => ({
            MapName: validateName(MapName),
            AppId: undefined,
        }),
    });

    const mapName = useField('MapName', form);
    const appId = useField('AppId', form);

    const getCloudMapList = async () => {
        try {
            const filters = [{ Name: 'exact_map_name', Values: [mapName.input.value] }];

            const params = {
                AppId: 1253985742,
                Filters: filters,
                Offset: 0,
                Limit: 10,
                Operator: operator,
                FileType: FILE_TYPE.NONE,
            };
            const res = await DescribeCloudMapBaseInfo(params);
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                if (isExistCloudMap) {
                    const newCloudMapList = filter(res.CloudMap, i => i.MapName !== initialVal.MapName);
                    setIsMapNameExist(newCloudMapList.length > 0);
                } else {
                    setIsMapNameExist(res.CloudMap?.length > 0);
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    useEffect(() => {
        getCloudMapList();
    }, [mapName.input.value]);

    return <>
        <form onSubmit={handleSubmit}>
            <Form>
                <Form.Item
                    required
                    label="架构图名称"
                    status={getStatus(mapName.meta, validating)}
                    message={getStatus(mapName.meta, validating) === 'error' && mapName.meta.error}
                >
                    <Input {...mapName.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入架构图名称" />
                </Form.Item>
                <Form.Item
                    label="AppId"
                    status={getStatus(appId.meta, validating)}
                    message={getStatus(appId.meta, validating) === 'error' && appId.meta.error}
                >
                    <Input {...appId.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入用户AppId" />
                </Form.Item>
            </Form>
            <Form.Action>
                <Button
                    type="primary"
                    htmlType="submit"
                    loading={submitting}
                >
                    {isExistCloudMap ? '保存' : '创建'}
                </Button>
                <Button type="weak" onClick={() => closeCreateDialog()}>取消</Button>
            </Form.Action>
        </form>
    </>;
}
