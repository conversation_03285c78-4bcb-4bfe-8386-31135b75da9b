.pop-panel-wraper,
.pop-panel-popover{
  .pop-panel-item {
    display: flex;
    width: 160px;
    height: 25px;
    padding: 6px 16px;
    cursor: pointer;
    &:hover {
      background-color: rgba(0,0,0,.07);
      text-decoration: none;
      color: #0052d9;
    }
  }

  .pop-panel-empty {
    border: solid rgba(0,0,0,.12);
    border-width: 0 0 thin;
    margin:  8px 0;
  }

  .pop-panel-item__text {
    margin-left: 7px;
    font-size: 15px;
    flex: 1 1;
    max-width: 135px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .pop-panel-item__icon {
    line-height: 29px;
  }

  .pop-panel-item__text-help {
    color: #ccc;
    font-size: 12px;
  }
}

.pop-panel-wraper {
  position: absolute;
  box-shadow: 0 5px 5px -3px rgb(0 0 0 / 20%), 0 8px 10px 1px rgb(0 0 0 / 14%), 0 3px 14px 2px rgb(0 0 0 / 12%);
  padding: 8px 0;
  background-color: #fff;
  z-index: 99;

}


