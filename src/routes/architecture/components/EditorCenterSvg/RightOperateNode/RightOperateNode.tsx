import React, { useEffect, useState } from 'react';
import './style.less';

import { IconTsa } from '@src/components/IconTsa';
import { Modal, message, notification, Bubble } from '@tencent/tea-component';
import HelpDialog from '../../Common/HelpDialog';
import { isEmpty } from 'lodash';

import { getCustomerName } from '@src/api/advisor/estimate';

const NodeGroupLayer = ({ sigma, closePanel }) => {
    const [groupList, setGroupList] = useState([]) // 模块列表
    useEffect(() => {
        const groupList = sigma.getNetworkShapes();
        setGroupList(groupList);
    }, [])
    const handleSetGroup = (key = null) => {
        sigma.setSecurityGroup(key);
        closePanel();
    };
    return (
        <ul className='pop-panel-popover'>
            <li className='pop-panel-item' onClick={() => handleSetGroup()}>
                <IconTsa type="icon-create-group" />
                <span className='pop-panel-item__text'>新建模块</span>
            </li>
            {
                !isEmpty(groupList)
                && <li className='pop-panel-empty'></li>
            }
            {
                groupList?.map((item, index) => (
                    <li className='pop-panel-item' key={index} onClick={() => handleSetGroup(item.key)}>
                        <IconTsa type="icon-block" />
                        <span className='pop-panel-item__text'>
                            {item.styles.label.value || item.styles.label.default}
                        </span>
                    </li>
                ))
            }
        </ul>
    )
}

export function RightOperateNode({
    sigma,
    appId,
    closePanel,
    position,
    isShowBindResource,
    isOnlyShowBind,
    isNodeLock,
    handleNodelock,
    bindNodeFuns,
	projectType
}) {
    const handleHelpBtnClick = () => {
        Modal.alert({
            // @ts-ignore
            caption: '帮助',
            maskClosable: true,
            size: '90%',
            message: <HelpDialog />,
            buttons: [],
        });
    };

    // 获取 appid uin
    const getCustomerInfo = async () => {
        if (!appId) return;
        try {
            const res = await getCustomerName({ AppId: +appId });
            if (res.Error) {
                message.error({ content: res.Error.Message });
                return;
            }
            return res?.IsAuthorized;
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    const handleBindResource = async () => {
        const isAuthorization = await getCustomerInfo();
        if (!isAuthorization) {
            notification.warning({ description: "未开通云顾问appid，仅提供画图能力，不提供实例绑定和巡检" })
            return;
        };
        bindNodeFuns.handleResourceBind();
    }

    const handleOperateItemClick = (e) => {
        if (e.target.innerHTML === '组合模块') return;
        closePanel();
    }
    return (
        <>
            <ul
                className='pop-panel-wraper'
                onClick={e => handleOperateItemClick(e)}
                style={{ left: `${position.x}px`, top: `${position.y}px` }}
            >
                {
                    isOnlyShowBind
                        ? isShowBindResource && <>
						{ projectType !== '客户项目' &&
						    <li className='pop-panel-item' onClick={() => bindNodeFuns.openDrawer()}>
								<IconTsa type="icon-view" />
								<span className='pop-panel-item__text'>查看绑定资源列表</span>
							</li>
						}
                            <li className='pop-panel-empty'></li>
                            <li className='pop-panel-item' onClick={handleHelpBtnClick}>
                                <IconTsa type="icon-read" />
                                <span className='pop-panel-item__text'>帮助</span>
                            </li>
                        </>
                        : <>
                            <li className='pop-panel-item' onClick={() => sigma.cutNode()} >
                                <IconTsa type="icon-cut" />
                                <span className='pop-panel-item__text'>剪切</span>
                                <span className='pop-panel-item__text-help'>Ctrl + X</span>
                            </li>
                            <li className='pop-panel-item' onClick={() => sigma.copyNode()}>
                                <IconTsa type="icon-copy" />
                                <span className='pop-panel-item__text'>复制</span>
                                <span className='pop-panel-item__text-help'>Ctrl + C</span>
                            </li>
                            <Bubble
                                arrowPointAtCenter
                                placement="right"
                                trigger="click"
                                style={{ maxWidth: 500, width: 218, zIndex: 10 }}
                                content={<NodeGroupLayer sigma={sigma} closePanel={() => closePanel()} />}
                            >
                                <li className='pop-panel-item'>
                                    <IconTsa type="icon-group" />
                                    <span className='pop-panel-item__text'>组合模块</span>
                                    <span className='pop-panel-item__icon'>
                                        <IconTsa type="icon-right-arow-xl" />
                                    </span>
                                </li>
                            </Bubble>
                            <li className='pop-panel-empty'></li>
                            {
                                isShowBindResource
                                && <>
                                    <li className='pop-panel-item' onClick={() => bindNodeFuns.openDrawer()}>
                                        <IconTsa type="icon-view" />
                                        <span className='pop-panel-item__text'>查看绑定资源列表</span>
                                    </li>
                                    <li className='pop-panel-item' onClick={handleBindResource}>
                                        <IconTsa type="icon-bind" />
                                        <span className='pop-panel-item__text'>绑定资源</span>
                                    </li>
                                    <li className='pop-panel-item' onClick={() => bindNodeFuns.delCloudMapItem()}>
                                        <IconTsa type="icon-clear" />
                                        <span className='pop-panel-item__text'>清空资源信息</span>
                                    </li>
                                    <li className='pop-panel-empty'></li>
                                </>
                            }
                            <li className='pop-panel-item' onClick={handleNodelock}>
                                <IconTsa type="icon-lock" />
                                <span className='pop-panel-item__text'>{isNodeLock ? '解除锁定' : '锁定'}</span>
                            </li>
                            <li className='pop-panel-item' onClick={() => sigma.deleteNode()}>
                                <IconTsa type="icon-delete" />
                                <span className='pop-panel-item__text'>删除</span>
                                <span className='pop-panel-item__text-help'>Del</span>
                            </li>
                            <li className='pop-panel-item' onClick={handleHelpBtnClick}>
                                <IconTsa type="icon-read" />
                                <span className='pop-panel-item__text'>帮助</span>
                            </li>
                        </>
                }
            </ul>
        </>
    );
}
