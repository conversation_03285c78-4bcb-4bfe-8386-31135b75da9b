import React, { useState, useEffect, FC, useMemo } from 'react';
import './style.less';
import { Card, TabPanel, message, Tabs, Bubble } from '@tencent/tea-component';
import { isEmpty, map } from 'lodash';
import { DeleteCloudMapItem } from '@src/api/architecture/architecture';
import { ResourceBindList } from '@src/routes/architecture/components';
import TagSelect from '../TagSelect';

interface ITagBindPickerProps {
	customerInfo: any;
	productData: any;
	tags: any
	reload: Function
	onResourceBind: Function
	onUpdateResource: Function
}
const TagBindPicker: FC<ITagBindPickerProps> = ({
	customerInfo,
	productData,
	tags,
	onResourceBind,
	onUpdateResource,
	reload
}: ITagBindPickerProps) => {

	const { appId, MapUUId } = customerInfo;
	const { productName, currentRegion, node } = productData;

	const [bindCount, setBindCount] = useState(0);

	const [forceUpdateFlag, setForceUpdateFlag] = useState(true);


	// 标签绑定
	const handleTagBind = async tags => {
		if (isEmpty(tags)) {
			message.error({ content: '绑定的标签不能为空！' });
			return;
		}

		await onResourceBind({ tags, bindType: 'instance_tag' });
		setForceUpdateFlag(!forceUpdateFlag);
	}

	// 清空组件信息
	const delCloudMapItem = async () => {
		try {
			const res = await DeleteCloudMapItem({
				MapUUId: MapUUId,
				Ids: [node.key],
				AppId: appId,
				BindingTypes: ['instance_tag'],
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '清空成功' });
				// 更新节点资源状态
				onUpdateResource();
				setForceUpdateFlag(!forceUpdateFlag);
				reload();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const tagList = useMemo(() => {
		return map(tags, (item, index) => ({
			sortIndex: index,
			tagValues: item
		}))
	}, [tags])
	return (
		<Card className="tag-picker">
			<Card.Body>
				<Tabs
					tabs={[
						{ id: 'tagPicker', label: '标签绑定' },
						{
							id: 'bindView',
							label: (
								<Bubble content="同时满足以下标签关联资源数量">已关联绑定资源（{bindCount}）</Bubble>
							),
						},
					]}
					destroyInactiveTabPanel
				>
					<TabPanel id="tagPicker">
						<TagSelect
							appId={appId}
							productName={productName}
							regions={currentRegion}
							resourceCount={bindCount}
							tagList={tagList}
							onTagBind={(tags) => handleTagBind(tags)}
							delCloudMapItem={delCloudMapItem}
						/>
					</TabPanel>
					<TabPanel id="bindView" forceRender>
						<ResourceBindList
							appId={appId}
							MapUUId={MapUUId}
							productName={productName}
							node={node}
							queryType="instance_tag"
							updateFlag={forceUpdateFlag}
							onResourceUpdate={(count) => setBindCount(count)}
							delCloudMapItem={delCloudMapItem}
						/>
					</TabPanel>
				</Tabs>
			</Card.Body>
		</Card>
	);
};
export default TagBindPicker;
