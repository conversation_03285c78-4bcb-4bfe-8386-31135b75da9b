import React, { useState, useEffect, useMemo, FC } from 'react';
import './style.less';
import {
	Card,
	Transfer,
	Table,
	TagSearchBox,
	TabPanel,
	message,
	Tag,
	Tabs,
	Button,
	Text,
} from '@tencent/tea-component';
import { isEmpty, map, forEach, find, uniqWith, clone, uniqBy, filter, includes } from 'lodash';
import { useToggle } from '@src/routes/advisor/pages/broadcast/hooks/common';
import { getResourceByResourceId, DeleteCloudMapItem } from '@src/api/architecture/architecture';
import { ResourceBindList } from '@src/routes/architecture/components';
const { pageable, selectable, removeable, scrollable, autotip } = Table.addons;
import { noSupporRegiontProductList } from '@src/routes/architecture/conf/eidtorConfig';
interface IResourceIdPickerProps {
	customerInfo: any;
	productData: any;
	filterOption: any;
	associatedIns: any;
	reload: Function;
	onResourceBind: Function;
	onUpdateResource: Function;
}

// 源表
function SourceTable({ dataSource, targetKeys, onChange, loading, pageInfo }) {
	return (
		<Table
			records={dataSource}
			recordKey="InstanceId"
			rowDisabled={(record) => record.status === 'stopped'}
			rowClassName={(record) => record.status}
			style={{ maxHeight: 310 }}
			columns={sourceColumns}
			addons={[
				pageable(pageInfo),
				scrollable({
					maxHeight: 270,
				}),
				selectable({
					value: targetKeys,
					onChange,
					rowSelect: true,
				}),
				autotip({
					isLoading: loading,
				}),
			]}
		/>
	);
}
// 源表列定义
const sourceColumns = [
	{
		key: 'Instance',
		header: 'ID/实例名',
		width: '50%',
		render: (item) => {
			const { InstanceId, InstanceName } = item;
			return (
				<>
					{InstanceId && (
						<>
							<Tag theme="primary">
								<Text onClick={(e) => e.stopPropagation()} copyable={InstanceId}>
									{InstanceId}
								</Text>
							</Tag>
							<br />
						</>
					)}
					{InstanceName && <Tag>{InstanceName}</Tag>}
				</>
			);
		},
	},
	{
		key: 'Region',
		header: '地域',
	},
	{
		key: 'Status',
		header: '状态',
		width: 100,
		render: (resource) => <p>{resource.Status}</p>,
	},
];

// 目标表列定义
const targetColumns = [
	{
		key: 'Instance',
		header: 'ID/实例名',
		width: '50%',
		render: (item) => {
			const { InstanceId, InstanceName } = item;
			return (
				<>
					{InstanceId && (
						<>
							<Tag theme="primary">{InstanceId}</Tag>
							<br />
						</>
					)}
					{InstanceName && <Tag>{InstanceName}</Tag>}
				</>
			);
		},
	},
	{
		key: 'Region',
		header: '地域',
	},
	{
		key: 'Status',
		header: '状态',
		width: 100,
		render: (resource) => (
			<>
				<p>{resource.Status}</p>
			</>
		),
	},
];

// 目标表
function TargetTable({ dataSource, onRemove, loading }) {
	return (
		<Table
			records={dataSource}
			recordKey="InstanceId"
			columns={targetColumns}
			addons={[
				removeable({ onRemove }),
				autotip({
					isLoading: loading,
				}),
			]}
		/>
	);
}
const ResourceIdPicker: FC<IResourceIdPickerProps> = ({
	customerInfo,
	productData,
	filterOption,
	associatedIns,
	onResourceBind,
	onUpdateResource,
	reload,
}: IResourceIdPickerProps) => {
	const targetKeys = !isEmpty(associatedIns) ? associatedIns?.map((item) => item.InstanceId) : [];
	const { appId, uin, MapUUId } = customerInfo;
	const { productName, currentRegion, node } = productData;
	const [isLoading, startLoad, endLoad] = useToggle(false);

	// 分页
	const [pageSize, setPageSize] = useState<number>(50);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	const [bindCount, setBindCount] = useState(0);

	// 已选绑定的 Resource ID key 列表
	const [targetResourceIdKeys, setTargetResourceIdKeys] = useState(targetKeys);

	// 获取的 resource id 列表
	const [resourceIdList, setResourceIdList] = useState([]);

	// 资源列表总集
	const [allResourceIdList, setAllResourceIdList] = useState([]);

	// 搜索筛选项
	const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);

	const [filters, setFilters] = useState([]);

	const [forceUpdateFlag, setForceUpdateFlag] = useState(true);

	useEffect(() => {
		// 保证绑定资源后再次绑定能看到之前绑定的选择
		const targetIns = !isEmpty(associatedIns)
			? associatedIns?.map(item => item?.InstanceId)
			: [];
		setTargetResourceIdKeys(targetIns);
		
		// 保证存量已选择资源展示
		let temp = clone(allResourceIdList);
		map(associatedIns, item => {
			temp.unshift(item);
		});
		const allList = uniqBy(temp, 'InstanceId');
		setAllResourceIdList(allList);
	}, [associatedIns]);

	// 调用接口获取全量 ResourceID 列表及已绑定 ResourceID 列表
	const getResourceIdList = async (isRegionChange = false) => {
		startLoad();
		try {
			const res = await getResourceByResourceId({
				AppId: +appId,
				Uin: uin,
				Product: productName,
				Regions: currentRegion,
				Filters: filters,
				Limit: pageSize,
				Offset: pageSize * (pageNumber - 1),
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				endLoad();
				return;
			}
			// 每次读取新数据时都读取一下已经绑定的资源数据，防止地域切换时将绑定的数据过滤掉了
			setResourceIdList(res.Instances);
			let temp = clone(allResourceIdList);
			temp = temp.concat(res.Instances);
			map(associatedIns, item => {
				temp.unshift(item);
			});
			let allList = uniqWith(temp, (a, b)=> a.InstanceId === b.InstanceId);
			// 地域一切换则清空当前选择的资源,如果有绑定的资源则正常显示
			// 并且过滤已经读取过的资源allList
			if (isRegionChange) {
				allList = filter(clone(allList), function (item) {
					return includes(currentRegion,item.Region);
				});
				// 保证已经绑定过的资源正常显示
				const targetIns = !isEmpty(associatedIns)
					? associatedIns?.map(item => item?.InstanceId)
					: [];
				let newTargetRes = filter(targetIns, (targetResourceId) => {
					return find(allList, { 'InstanceId': targetResourceId });
				})
				setTargetResourceIdKeys(newTargetRes);
			}
			setAllResourceIdList(allList);
			setTotalPage(res?.TotalCount || 0);
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	useEffect(() => {
		if (!isEmpty(currentRegion) || noSupporRegiontProductList.includes(productName)) {
			getResourceIdList();
		}
	}, [filters, pageNumber, pageSize, currentRegion]);

	useEffect(() => {
		if (!isEmpty(currentRegion) || noSupporRegiontProductList.includes(productName)) {
			getResourceIdList(true);
		}
	}, [currentRegion]);

	const handleSearchTagChange = (tags) => {
		let temp = [];
		forEach(tags, (tag) => {
			const name = tag.attr ? tag.attr.key : '';
			const currentTag = find(filterOption, (item) => item.SqlField === name) || {};
			if (!isEmpty(currentTag) || !name) {
				temp.push({
					Name: name ? name : 'ins_id',
					Values: map(tag.values, (item) => item.name),
				});
			}
		});
		setFilters(temp);
		setTagSelectBoxValue(tags);
	};

	const attributes = useMemo(() => {
		return map(filterOption, (item) => ({
			type: 'input',
			key: item.SqlField,
			name: item.FieldName,
		}));
	}, [filterOption]);

	const tagSearchBoxProps = {
		minWidth: '100%',
		attributes,
		value: tagSelectBoxValue,
		hideHelp: true,
		tips: '支持资源ID、资源名过滤，多个关键词用竖线"|"分隔',
		onChange: handleSearchTagChange,
		onKeyDown: (e) => e.stopPropagation(),
		onSearchButtonClick: () => getResourceIdList(),
	};

	// 资源ID绑定
	const handleResourceIdBind = async () => {
		if (isEmpty(targetResourceIdKeys)) {
			message.error({ content: '绑定资源不能为空！' });
			return;
		}
		let bindInsList = [];
		if (targetResourceIdKeys?.length > allResourceIdList.length) {
			bindInsList = associatedIns.filter((i) => targetResourceIdKeys.includes(i.InstanceId));
		} else {
			bindInsList = allResourceIdList.filter((i) => targetResourceIdKeys.includes(i.InstanceId));
		}
		await onResourceBind({ bindInsList, bindType: 'instance_id' });
		setForceUpdateFlag(!forceUpdateFlag);
	};

	// 清空组件信息
	const delCloudMapItem = async () => {
		try {
			const res = await DeleteCloudMapItem({
				MapUUId: MapUUId,
				Ids: [node.key],
				AppId: appId,
				BindingTypes: ['instance_id'],
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '清空成功' });
				onUpdateResource();
				setForceUpdateFlag(!forceUpdateFlag);
				reload();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	return (
		<Card className="resource-picker">
			<Card.Body>
				<Tabs
					tabs={[
						{ id: 'resourcePicker', label: '资源绑定' },
						{ id: 'bindView', label: `已关联绑定资源（${bindCount}）` },
					]}
					destroyInactiveTabPanel
				>
					<TabPanel id="resourcePicker">
						<Transfer
							header={<></>}
							leftCell={
								<Transfer.Cell
									scrollable={false}
									title="选择您要绑定的对象"
									header={
										<span style={{ width: '100%' }} onKeyDown={(e) => e.stopPropagation()}>
											<TagSearchBox {...tagSearchBoxProps} />
										</span>
									}
								>
									<SourceTable
										dataSource={resourceIdList}
										targetKeys={targetResourceIdKeys}
										onChange={(keys) => setTargetResourceIdKeys(keys)}
										loading={isLoading}
										pageInfo={{
											pageSize,
											pageIndex: pageNumber,
											recordCount: totalPage,
											onPagingChange: handlePageChange,
										}}
									/>
								</Transfer.Cell>
							}
							rightCell={
								<Transfer.Cell title={`已选择 (${targetResourceIdKeys?.length})`}>
									<TargetTable
										dataSource={allResourceIdList.filter((i) =>
											targetResourceIdKeys.includes(i.InstanceId)
										)}
										onRemove={(key) =>
											setTargetResourceIdKeys(targetResourceIdKeys.filter((i) => i !== key))
										}
										loading={isLoading}
									/>
								</Transfer.Cell>
							}
						/>
						<div className="resource-bind-Btn">
							<Button type="primary" onClick={handleResourceIdBind}>
								确认绑定
							</Button>
							<Button
								type="primary"
								className="resource-bind-Btn-clear"
								onClick={delCloudMapItem}
								disabled={bindCount === 0}
							>
								一键清除
							</Button>
						</div>
					</TabPanel>
					<TabPanel id="bindView" forceRender>
						<ResourceBindList
							appId={appId}
							MapUUId={MapUUId}
							productName={productName}
							node={node}
							queryType="instance_id"
							updateFlag={forceUpdateFlag}
							onResourceUpdate={(count) => setBindCount(count)}
							delCloudMapItem={delCloudMapItem}
						/>
					</TabPanel>
				</Tabs>
			</Card.Body>
		</Card>
	);
};
export default ResourceIdPicker;
