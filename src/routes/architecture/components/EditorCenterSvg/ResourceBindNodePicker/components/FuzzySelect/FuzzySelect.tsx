import React, { useState, useEffect, FC, useMemo } from 'react';
import './style.less';
import { StatusTip, Table, TagSearchBox, Button, Bubble } from '@tencent/tea-component';
import { isEmpty, map, filter, forEach, clone, find } from 'lodash';
import { IconTsa } from '@src/components/IconTsa';
interface IFuzzySelectProps {
	filterOption: any
	fuzzyList: any;
	resourceCount: number
	onFuzzyBind: Function
	delCloudMapItem: Function
}
const FuzzySelect: FC<IFuzzySelectProps> = ({
	filterOption,
	fuzzyList,
	resourceCount,
	onFuzzyBind,
	delCloudMapItem
}: IFuzzySelectProps) => {

	const [selectFuzzyList, setSelectFuzzyList] = useState(fuzzyList);

	const handleFuzzyListDelete = (item) => {
		const temp = clone(selectFuzzyList);
		setSelectFuzzyList(filter(temp, i => i.sortIndex !== item.sortIndex));
	}
	useEffect(() => {
		if (isEmpty(fuzzyList)) {
			setSelectFuzzyList([]);
		}
	}, [fuzzyList]);

	const attributes = useMemo(() => {
		return map(filterOption, item => ({
			type: 'input',
			key: item.SqlField,
			name: item.FieldName,
		}))
	}, [filterOption])

	const handleSearchTagChange = (tags, index) => {
		const temp = []
		forEach(tags, (tag) => {
			const name = tag.attr ? tag.attr.key : '';
			const currentTag = find(filterOption, item => item.SqlField === name) || {};
			if (!isEmpty(currentTag)) {
				temp.push({
					Key: name,
					Value: map(tag.values, item => item.name)[0]
				})
			}
		});
		const newLists = clone(selectFuzzyList);
		map(newLists, item => {
			if (item.sortIndex === index) {
				item.fuzzyValues = temp
			}
		})
		setSelectFuzzyList(newLists)
	};

	const columns = [
		{
			key: 'Key',
			header: '模糊筛选值',
			width: '70%',
			render: item => {
				const defaultValue = map(item.fuzzyValues, i => {
					return {
						attr: find(attributes, attribute => attribute.key === i.Key),
						values: [{ name: i.Value }]
					}
				})
				return <span
					style={{ width: '100%' }}
					onKeyDown={e => e.stopPropagation()}
				>
					<TagSearchBox
						minWidth='70%'
						attributes={attributes}
						defaultValue={defaultValue}
						hideHelp
						tips='回车键确认，过滤标签用回车键分隔，匹配结果同时满足以上条件'
						onChange={tags => handleSearchTagChange(tags, item.sortIndex)}
					/>
				</span>
			}
		},
		{
			key: 'Oprate',
			header: '操作',
			render: item => (
				<>
					<div className="item-svg__box" onClick={() => handleFuzzyListDelete(item)}>
						<IconTsa type='icon-delete' className='icon-svg' />
						<span className='item-ht'>删除</span>
					</div>
				</>
			)
		},
	]
	const handleFuzzyFilterCreate = () => {
		const newSelectFuzzyList = clone(selectFuzzyList)
		newSelectFuzzyList.push({
			sortIndex: newSelectFuzzyList.length,
			fuzzyValues: []
		})
		setSelectFuzzyList(newSelectFuzzyList);
	}

	const handleFuzzyBind = () => {
		let paramsData = map(selectFuzzyList, item => item.fuzzyValues);
		onFuzzyBind(paramsData)
	}
	return (
		<div className='fuzzy-select-box'>
			<Table
				bordered
				records={selectFuzzyList}
				recordKey="sortIndex"
				columns={columns}
				topTip={
					selectFuzzyList.length === 0
					&& <StatusTip status="empty" />
				}
			/>
			<Bubble content="支持多种条件叠加绑定">
				<div className="item-svg__box" onClick={handleFuzzyFilterCreate}>
					<IconTsa type='icon-plus' className='icon-svg' />
					<span className='item-ht'>新建模糊条件</span>
				</div>
			</Bubble>
			<div className='fuzzy-bind-Btn'>
				<Button
					type="primary"
					onClick={handleFuzzyBind}
				>
					确认绑定
				</Button>

				<Button
					type="primary"
					className='fuzzy-bind-Btn-clear'
					onClick={() => delCloudMapItem()}
					disabled={resourceCount === 0}
				>
					一键清除
				</Button>
			</div>
		</div>
	);
};
export default FuzzySelect;
