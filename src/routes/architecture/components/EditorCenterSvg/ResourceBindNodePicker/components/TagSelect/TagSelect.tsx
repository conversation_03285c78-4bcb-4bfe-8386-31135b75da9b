import React, { useState, useEffect, useMemo, FC } from 'react';
import './style.less';
import { StatusTip, Table, message, TagSearchBox, Button, Bubble } from '@tencent/tea-component';
import { map, clone, forEach, find, isEmpty, filter } from 'lodash';
import { getTagsOption } from '@src/api/architecture/architecture';
import { IconTsa } from '@src/components/IconTsa';
interface ITagSelectProps {
	appId: number
	productName: string
	resourceCount: number
	tagList: any;
	regions: Array<string>
	onTagBind: Function
	delCloudMapItem: Function
}
const TagSelect: FC<ITagSelectProps> = ({ appId, productName, regions, resourceCount, tagList, onTagBind, delCloudMapItem }: ITagSelectProps) => {
	const [selectTags, setSelectTags] = useState(tagList);

	const [tagsOption, setTagsOption] = useState([])

	useEffect(() => {
		getTags();
	}, [regions]);

	useEffect(() => {
		if (isEmpty(tagList)) {
			setSelectTags([])
		}
	}, [tagList]);

	const getTags = async () => {
		if (isEmpty(regions)) return;
		try {
			const res = await getTagsOption({ AppId: +appId, Product: productName, Regions: regions });
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			setTagsOption(res.Tags);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleTagDelete = (item) => {
		const temp = clone(selectTags);
		setSelectTags(filter(temp, i => i.sortIndex !== item.sortIndex));
	}

	const attributes = useMemo(() => {
		return map(tagsOption, (item, index) => ({
			type: "single",
			name: item.TagKey,
			key: index + 1,
			values: map(item.TagValues, (i, index) => ({ key: index + 1, name: i })),
			removeable: false,
		}))
	}, [tagsOption])

	const handleSearchTagChange = (tags, index) => {
		const temp = []
		forEach(tags, (tag) => {
			const name = tag.attr ? tag.attr.name : '';
			const currentTag = find(tagsOption, item => item.TagKey === name) || {};
			if (!isEmpty(currentTag)) {
				temp.push({
					Key: name,
					Value: map(tag.values, item => item.name)[0]
				})
			}
		});
		const newLists = clone(selectTags);
		map(newLists, item => {
			if (item.sortIndex === index) {
				item.tagValues = temp
			}
		})
		setSelectTags(newLists)
	};

	const handleClearButtonClick = (index) => {
		let tags = clone(selectTags);
		setSelectTags(filter(tags, i => i.sortIndex !== index));
	}

	const columns = [
		{
			key: 'Value',
			header: '标签选择',
			width: '70%',
			render: item => {
				const value = map(item.tagValues, i => {
					return {
						attr: find(attributes, attribute => attribute.name === i.Key),
						values: [{ key: i.Value, name: i.Value }]
					}
				})

				return <span
					style={{ width: '100%' }}
					onKeyDown={e => e.stopPropagation()}
				>
					<TagSearchBox
						minWidth='70%'
						attributes={attributes}
						value={value}
						hideHelp
						onClearButtonClick={() => handleClearButtonClick(item.sortIndex)}
						tips='标签键：标签值，过滤标签用回车键分隔，匹配结果同时满足以上条件'
						onChange={tags => handleSearchTagChange(tags, item.sortIndex)}
					/>
				</span>
			}
		},
		{
			key: 'status',
			header: '操作',
			render: item => (
				<>
					<div className="item-svg__box" onClick={() => handleTagDelete(item)}>
						<IconTsa type='icon-delete' className='icon-svg' />
						<span className='item-ht'>删除</span>
					</div>
				</>
			)
		},
	]
	const handleNewTagCreate = () => {
		const newSelectTags = clone(selectTags)
		newSelectTags.push({
			sortIndex: newSelectTags.length,
			tagValues: []
		})
		setSelectTags(newSelectTags);
	}

	const handleTagBind = () => {
		let paramsData = map(selectTags, item => item.tagValues);
		onTagBind(paramsData)
	}
	return (
		<div className='tag-select-box'>
			<Table
				bordered
				records={selectTags}
				recordKey="sortIndex"
				columns={columns}
				topTip={
					selectTags.length === 0
					&& <StatusTip status="empty" />
				}
			/>
			<Bubble content="支持多种条件叠加绑定">
				<div className="item-svg__box" onClick={handleNewTagCreate}>
					<IconTsa type='icon-plus' className='icon-svg' />
					<span className='item-ht'>新建标签过滤条件</span>
				</div>
			</Bubble>
			<div className='tag-bind-Btn'>
				<Button
					type="primary"
					onClick={handleTagBind}
				>
					确认绑定
				</Button>
				<Button
					type="primary"
					className='tag-bind-Btn-clear'
					onClick={() => delCloudMapItem()}
					disabled={resourceCount === 0}
				>
					一键清除
				</Button>
			</div>
		</div>
	);
};
export default TagSelect;
