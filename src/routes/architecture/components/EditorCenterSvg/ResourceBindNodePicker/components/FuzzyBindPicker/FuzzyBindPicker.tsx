import React, { useState, useEffect, FC, useMemo } from 'react';
import './style.less';
import { Card, TabPanel, message, Tabs, Bubble } from '@tencent/tea-component';
import { isEmpty, map } from 'lodash';
import { DeleteCloudMapItem } from '@src/api/architecture/architecture';
import { ResourceBindList } from '@src/routes/architecture/components';
import FuzzySelect from '../FuzzySelect';

interface IFuzzyBindPickerProps {
	customerInfo: any;
	productData: any;
	filterOption: any
	fuzzyList: any
	reload: Function
	onResourceBind: Function
	onUpdateResource: Function
}
const FuzzyBindPicker: FC<IFuzzyBindPickerProps> = ({
	customerInfo,
	productData,
	filterOption,
	fuzzyList,
	onResourceBind,
	onUpdateResource,
	reload
}: IFuzzyBindPickerProps) => {

	const { appId, MapUUId } = customerInfo;
	const { productName, node } = productData;

	const [bindCount, setBindCount] = useState(0);

	const [forceUpdateFlag, setForceUpdateFlag] = useState(true);

	// 标签绑定
	const handleFuzzyBind = async fuzzyBind => {
		if (isEmpty(fuzzyBind)) {
			message.error({ content: '绑定的模糊条件不能为空！' });
			return;
		}

		await onResourceBind({ fuzzyBind, bindType: 'fuzzy_binding' });
		setForceUpdateFlag(!forceUpdateFlag);
	}

	// 清空组件信息
	const delCloudMapItem = async () => {
		try {
			const res = await DeleteCloudMapItem({
				MapUUId: MapUUId,
				Ids: [node.key],
				AppId: appId,
				BindingTypes: ['fuzzy_binding'],
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				message.success({ content: '清空成功' });
				onUpdateResource();
				setForceUpdateFlag(!forceUpdateFlag);
				reload();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const fuzzyLists = useMemo(() => {
		return map(fuzzyList, (item, index) => ({
			sortIndex: index,
			fuzzyValues: item
		}))
	}, [fuzzyList]);

	return (
		<Card className="fuzzy-picker">
			<Card.Body>
				<Tabs
					tabs={[
						{ id: 'fuzzyPicker', label: '模糊匹配绑定' },
						{
							id: 'bindView',
							label: (
								<Bubble content="同时满足以下模糊条件关联资源数量">
									已关联绑定资源（{bindCount}）
								</Bubble>
							),
						},
					]}
					destroyInactiveTabPanel
				>
					<TabPanel id="fuzzyPicker">
						<FuzzySelect
							filterOption={filterOption}
							fuzzyList={fuzzyLists}
							resourceCount={bindCount}
							onFuzzyBind={(fuzzys) => handleFuzzyBind(fuzzys)}
							delCloudMapItem={delCloudMapItem}
						/>
					</TabPanel>
					<TabPanel id="bindView" forceRender>
						<ResourceBindList
							appId={appId}
							MapUUId={MapUUId}
							productName={productName}
							node={node}
							queryType="fuzzy_binding"
							updateFlag={forceUpdateFlag}
							onResourceUpdate={(count) => setBindCount(count)}
							delCloudMapItem={delCloudMapItem}
						/>
					</TabPanel>
				</Tabs>
			</Card.Body>
		</Card>
	);
};
export default FuzzyBindPicker;
