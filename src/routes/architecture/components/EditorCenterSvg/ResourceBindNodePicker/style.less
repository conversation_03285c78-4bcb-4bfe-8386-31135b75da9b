.bind-create-box {
	display: flex;
	.nav-item-svg__box {
		display: flex;
		width: 100px;
		height: 30px;
		justify-content: center;
		border-radius: 8px;
		border: 1px dashed #ddd;
		&:hover {
			background-color: #eee;
			cursor: pointer;
		}
		.icon-svg {
			display: flex;
			align-items: center;
			margin-right: 3px;
		}
		.nav-item-ht {
			line-height: 30px;
		}
	}
}

.resource-bind-forms {
	.tea-form__item {
		margin-right: 42px;
	}
}

.icon-arow-box {
	margin: 0 4px;
	padding-top: 3px;
}

.resource__item {
	min-width: 420px;
	width: 100%;
	margin: 30px auto;
	background-color: #fff;
	border-radius: 0;
	padding-left: 10px;
	box-sizing: border-box;
	border-left: 4px solid #e6e6e6;

	.resource__panel {
	  	font-size: 16px;
		.tea-accordion__header {
			display: flex;

			.tea-accordion__header-title {
				display: flex;
			}
		}
	}
} 
