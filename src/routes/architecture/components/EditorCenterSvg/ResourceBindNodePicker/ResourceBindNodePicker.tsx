import React, { useState, useEffect, FC, useMemo } from 'react';
import './style.less';
import { Card, Form, Input, message, Modal, Button, Collapse, notification, SelectMultiple, Alert, List } from '@tencent/tea-component';
import { isEmpty, map, find, reduce, toPairs, includes } from 'lodash';
import { getNodeLabelName, setNodeStyleByKey } from '../../../utils';
import { IconTsa } from '@src/components/IconTsa';
import ResourceIdPicker from './components/ResourceIdPicker';
import TagBindPicker from './components/TagBindPicker';
import FuzzyBindPicker from './components/FuzzyBindPicker';
import {
	CreateCloudMapItem,
	ModifyCloudMapItem,
	getProductRegionZoneOptions,
	getProductFilterConfigs,
	DescribeCloudMapItem,
} from '@src/api/architecture/architecture';
import { resourcePickConf, tagPickConf, fuzzyPickConf } from '../../../conf/resourcePickConf';

interface CloudMapItem {
	Product?: string,
	AssociatedInstance: Array<any>,
	Regions: Array<string>,
	Zones: Array<string>,
	ItemName: string,
	ItemId: number,
}

const defaultCloudMapItem = {
	Product: '',
	AssociatedInstance: [],
	Regions: [],
	Zones: [],
	ItemName: '',
	ItemId: 0,
};
interface IResourceBindNodePickerProps {
	MapUUId: string;
	appId: number;
	productName: string;
	uin: string;
	node: any;
	openResourceList?: Function;
	onResourceBind: Function;
	onUpdateResource: Function;
	closeDialog: Function;
	sigma: any;
}

const ResourceBindNodePicker: FC<IResourceBindNodePickerProps> = ({
	MapUUId,
	appId,
	productName,
	uin,
	node,
	openResourceList,
	onResourceBind,
	onUpdateResource,
	closeDialog,
	sigma,
}: IResourceBindNodePickerProps) => {
	const operator = localStorage.getItem('engName');
	// 初始已绑定的数据
	const [cloudMapItem, setCloudMapItem] = useState<CloudMapItem>(defaultCloudMapItem);

	const [graphData, setGraphData] = useState({});

	const [nodeLabelName, setNodeLabelName] = useState('节点');

	const [initLabelName, setInitLabelName] = useState('');

	// 地域选项列表
	const [regionOptions, setRegionOptions] = useState([]);

	// 选中地域
	const [currentRegion, setCurrentRegion] = useState([]);

	const [filterConf, setFilterConf] = useState({ InstanceBinding: [], FuzzyBinding: [] });

	const [bindTypeList, setBindTypeList] = useState([]);

	useEffect(() => {
		try {
			const graphDataObj = JSON.parse(sigma.getLocalGraphData());
			const currentLabel = graphDataObj[node.linkTextLabel];
			setInitLabelName(getNodeLabelName(currentLabel));
			setNodeLabelName(getNodeLabelName(currentLabel));
			setGraphData(graphDataObj);
		} catch (err) {
			message.error({ content: 'GraphData JSON Error' });
		}
	}, []);

	const graphDataLabelList = useMemo(
		() =>
			reduce(
				toPairs(graphData),
				(ret, [key, item]) => {
					if (item.name === 'TEXT LABEL') {
						ret.push(getNodeLabelName(item));
					}
					return ret;
				},
				[]
			),
		[graphData, getNodeLabelName]
	);

	// 获取已绑定实例初始数据
	const describeAssociateIns = async () => {
		if (!MapUUId || !appId) return;
		try {
			const res = await DescribeCloudMapItem({
				AppId: +appId,
				Uuid: node.key,
				MapUUId: MapUUId,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setCloudMapItem({ ...res.CloudMapItem });
				let initBindList = [];
				const { AssociatedInstance, Tag, FuzzyBinding } = res.CloudMapItem;
				initBindList.push({ ...resourcePickConf, initBindData: AssociatedInstance });
				initBindList.push({ ...tagPickConf, initBindData: Tag });
				initBindList.push({ ...fuzzyPickConf, initBindData: FuzzyBinding });
				setBindTypeList(initBindList);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const isBindResource = useMemo(() => !!cloudMapItem.ItemId, [cloudMapItem]);

	useEffect(() => {
		describeAssociateIns();
		getRegionZoneOption();
		getFilterConfigs();
	}, []);

	// 初始化所有选项列表
	const getRegionZoneOption = async () => {
		if (!productName) return;
		// 调用后台接口获取 appid 的地域列表、可用区列表
		try {
			const res = await getProductRegionZoneOptions({
				AppId: +appId,
				Uin: uin,
				Product: productName,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			// dnspod 不支持地域和可用区，特殊处理
			if (productName === 'dnspod') return;

			// 生成地域选择列表
			const tmpRegionOptions = [];
			res.Regions?.forEach((i) => {
				tmpRegionOptions.push({ value: i.Region, text: i.RegionName });
			});
			setRegionOptions(tmpRegionOptions);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getFilterConfigs = async () => {
		// 调用后台接口获取 appid 的地域列表、可用区列表
		try {
			const res = await getProductFilterConfigs({ AppId: +appId });
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			const conf = find(res.ProductConfig, (item) => item.Product === productName) || {};
			setFilterConf(conf);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 初始地域默认值
	useEffect(() => {
		if (regionOptions?.length > 0) {
			const regionValue = isEmpty(cloudMapItem.Regions) ? [regionOptions[0].value] : cloudMapItem.Regions;
			setCurrentRegion(regionValue);
		}
	}, [regionOptions]);

	// 判断节点名重复
	const isNameRepeat = useMemo(() => {
		let buffer = [];
		let newLabelList = reduce(
			graphDataLabelList,
			(ret, item) => {
				if (item == initLabelName) {
					buffer.length === 1 ? ret.push(item) : buffer.push(item);
				} else {
					ret.push(item);
				}
				return ret;
			},
			[]
		);
		return includes(newLabelList, nodeLabelName);
	}, [nodeLabelName, graphDataLabelList]);

	// 节点名校验
	const verifyNodeName = (isTip = true) => {
		let verifyer = {
			message: '',
			status: true,
		};
		if (isNameRepeat) {
			isTip && message.error({ content: '架构巡检不允许节点名称重复，请更改！' });
			verifyer = {
				message: '架构巡检不允许节点名称重复，请更改！',
				status: false,
			};
		}
		if (!nodeLabelName) {
			isTip && message.error({ content: '架构图中节点名称不能为空！' });
			verifyer = {
				message: '架构图中节点名称不能为空！',
				status: false,
			};
		}
		return verifyer;
	};

	// 绑定资源
	const handleBindResource = async (bindData) => {
		const { bindInsList, tags, fuzzyBind, bindType } = bindData;
		if (!verifyNodeName().status) return;
		try {
			const baseReqParams = {
				AppId: +appId,
				MapUUId: MapUUId,
				Uuid: node.key,
				ItemName: nodeLabelName,
				Regions: currentRegion,
				Product: productName,
				Instance: bindInsList,
				FuzzyBinding: fuzzyBind,
				Tag: tags,
				Operator: operator,
				BindingType: bindType,
			};
			const res = isBindResource
				? await ModifyCloudMapItem({ ...baseReqParams, ItemId: cloudMapItem.ItemId })
				: await CreateCloudMapItem(baseReqParams);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				const labelNode = graphData[node.linkTextLabel];
				setNodeStyleByKey(sigma, labelNode, { attr: 'label', value: nodeLabelName });
				setNodeStyleByKey(sigma, node, { attr: 'fillDark', value: '#4286C5' });
				// 更新当前节点绑定的资源信息
				notification.success({ description: '绑定资源实例成功' });
				onResourceBind();
				describeAssociateIns();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleViewBindList = () => {
		closeDialog();
		openResourceList();
	};

	return (
		<>
			<Modal visible size="85%" onClose={() => closeDialog()} caption="绑定资源管理">
				<Alert>
					<h4>绑定方式三种：资源绑定、标签绑定、模糊匹配绑定</h4>
					<List type="bullet">
						<List.Item>资源绑定：静态绑定</List.Item>
						<List.Item>
							标签绑定、模糊匹配绑定：动态绑定资源，根据输入标签或者模糊匹配的规则，每天自动更新相关实例资源
						</List.Item>
						<List.Item>三种方式可叠加使用</List.Item>
					</List>
				</Alert>
				<Modal.Body>
					<Card>
						<Card.Body>
							<Form layout="inline-vertical" className="resource-bind-forms">
								<Form.Item label="产品">
									<Input size="m" value={productName} disabled />
								</Form.Item>
								<Form.Item
									label="节点名称"
									required
									message={verifyNodeName(false).message}
									status={verifyNodeName(false).status ? 'success' : 'error'}
								>
									<Input
										size="m"
										value={nodeLabelName}
										onChange={(value) => setNodeLabelName(value)}
										onKeyDown={(e) => e.stopPropagation()}
									/>
								</Form.Item>
								<Form.Item label="地域" tips="部分产品不支持地域选择。">
									<SelectMultiple
										size="m"
										options={regionOptions}
										staging={false}
										value={currentRegion}
										onChange={(value) => setCurrentRegion(value)}
										appearance="button"
										matchButtonWidth
										searchable
										disabled={regionOptions.length === 0}
										placeholder={regionOptions.length === 0 ? '产品不支持地域选择' : '请选择'}
										allOption={{ value: 'all', text: '全选' }}
									/>
								</Form.Item>
							</Form>
						</Card.Body>
					</Card>
					{
						<Collapse
							icon={(active) => (
								<IconTsa
									type={active ? 'icon-down-arow' : 'icon-right-arow'}
									className="icon-arow-box"
								/>
							)}
							iconPosition="right"
						>
							{bindTypeList.map((item, index) => (
								<div className="resource__item" key={index}>
									<Collapse.Panel
										id={`${index + 1}`}
										title={
											<>
												<IconTsa type={item.icon} className="icon-arow-box" />
												{item.title}
											</>
										}
										className="resource__panel"
									>
										{(() => {
											switch (item.type) {
												case 'Resource':
													return (
														<ResourceIdPicker
															customerInfo={{ appId, uin, MapUUId }}
															productData={{ productName, currentRegion, node }}
															filterOption={filterConf?.InstanceBinding}
															associatedIns={item.initBindData}
															onResourceBind={(data) => handleBindResource(data)}
															onUpdateResource={() => onUpdateResource()}
															reload={describeAssociateIns}
														/>
													);
												case 'Tag':
													return (
														<TagBindPicker
															customerInfo={{ appId, uin, MapUUId }}
															productData={{ productName, currentRegion, node }}
															tags={item.initBindData}
															onResourceBind={(data) => handleBindResource(data)}
															onUpdateResource={() => onUpdateResource()}
															reload={describeAssociateIns}
														/>
													);
												case 'Fuzzy':
													return (
														<FuzzyBindPicker
															customerInfo={{ appId, uin, MapUUId }}
															productData={{ productName, currentRegion, node }}
															filterOption={filterConf?.FuzzyBinding}
															fuzzyList={item.initBindData}
															onResourceBind={(data) => handleBindResource(data)}
															onUpdateResource={() => onUpdateResource()}
															reload={describeAssociateIns}
														/>
													);
												default:
													return <></>;
											}
										})()}
									</Collapse.Panel>
								</div>
							))}
						</Collapse>
					}
				</Modal.Body>

				<Modal.Footer>
					<Button type="primary" onClick={handleViewBindList}>
						查看绑定资源
					</Button>
					<Button type="primary" onClick={() => closeDialog()}>
						关闭
					</Button>
				</Modal.Footer>
			</Modal>
		</>
	);
};
export default ResourceBindNodePicker;
