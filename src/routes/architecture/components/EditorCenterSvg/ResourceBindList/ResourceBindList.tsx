import React, { useState, useEffect, FC, useMemo } from 'react';
import './style.less';
import { Table, message, StatusTip, Text, Card, Justify, Button, TagSearchBox, Tag } from '@tencent/tea-component';
import { isEmpty, forEach, map, find } from 'lodash';
import { DescribeCloudMapItemInstances, getProductFilterConfigs } from '@src/api/architecture/architecture';
import { useToggle } from '../../../hooks/common';
import { SigmaType } from '@tencent/sigma-editor';

const { pageable } = Table.addons;

interface IResourceBindListProps {
	appId: number;
	node: any;
	MapUUId: string;
	productName: string;
	queryType?: string;
	disableClearIns?: boolean;
	updateFlag?: boolean;
	onResourceUpdate?: Function;
	delCloudMapItem: Function;
}
const ResourceBindList: FC<IResourceBindListProps> = ({
	appId,
	MapUUId,
	node,
	productName,
	disableClearIns = false,
	queryType = '',
	updateFlag = false,
	onResourceUpdate,
	delCloudMapItem,
}: IResourceBindListProps) => {
	const [isTableLoading, startLoad, endLoad] = useToggle(false);

	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	// 表头数据
	const [fieldDesc, setFieldDesc] = useState([]);
	// 实例列表数据
	const [instances, setInstances] = useState([]);

	// 搜索筛选项
	const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);

	const [filterConf, setFilterConf] = useState([]);

	const [filters, setFilters] = useState([]);

	useEffect(() => {
		getFilterConfigs();
	}, []);

	const getFilterConfigs = async () => {
		// 调用后台接口获取 appid 的地域列表、可用区列表
		try {
			const res = await getProductFilterConfigs({ AppId: +appId });
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			const conf = find(res.ProductConfig, (item) => item.Product === productName) || {};
			setFilterConf(conf.InstanceBinding);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const attributes = useMemo(() => {
		return map(filterConf, (item) => ({
			type: 'input',
			key: item.SqlField,
			name: item.FieldName,
		}));
	}, [filterConf]);

	// 获取绑定实例列表
	const getCloudMapItemInstances = async () => {
		if (!MapUUId || !appId) return;
		startLoad();
		try {
			const res = await DescribeCloudMapItemInstances({
				AppId: +appId,
				Uuid: node.key,
				Env: 'all',
				MapUUId: MapUUId,
				Product: productName,
				Filter: filters,
				Offset: pageSize * (pageNumber - 1),
				Limit: pageSize,
				BindingTypes: queryType ? [queryType] : [],
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				endLoad();
			} else {
				setFieldDesc(res?.FieldDesc);
				if (res.Instances) {
					try {
						setInstances(JSON.parse(res?.Instances));
					} catch (err) {
						message.error({ content: '接口Instances数据 JSON error' });
					}
				} else {
					setInstances([]);
				}
				setTotalPage(res?.TotalCount || 0);
				onResourceUpdate?.(res?.TotalCount || 0);
				endLoad();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	// 生成并转换表头
	const DetailColumns = useMemo(() => {
		const tmp = [];
		fieldDesc?.map((i) => {
			if (i.Field !== 'PriId') {
				const c = {
					key: i.Field,
					header: i.FieldName,
					width: i.Field === 'Tags' ? '20%' : 'auto',
					render: (item) => {
						if (!isEmpty(i.FieldDict)) {
							// 根据 FieldDict 内容渲染字段
							return <Text>{i.FieldDict[item[i.Field]]}</Text>;
						}
						if (i.Field === 'Tags') {
							// Tags 字段显示格式为 key:value 列表
							let tagField = [];
							if (item[i.Field]) {
								if (typeof item[i.Field] === 'string') {
									tagField = item[i.Field].split(';');
								}
							}
							if (!isEmpty(tagField)) {
								return (
									<>
										{tagField.map((i, index) => (
											<div key={index}>{i && <Tag theme="primary">{i}</Tag>}</div>
										))}
									</>
								);
							}
							return <></>;
						}
						if (i.Field === 'privateIPAddresses' || i.Field === 'publicIPAddresses') {
							// 该两字段显示为 ip 列表
							if (item[i.Field]) {
								return (
									<>
										{item[i.Field].map((i) => (
											<div>
												<span>{i}</span>
											</div>
										))}
									</>
								);
							}
							return <></>;
						}
						if (i.Field === 'InstanceId' || i.Field === 'InstanceName') {
							return <Text copyable={{ text: item[i.Field] }}>{item[i.Field]} </Text>;
						}
						return <Text>{item[i.Field]}</Text>;
					},
				};
				tmp.push(c);
			}
		});
		return tmp;
	}, [fieldDesc]);

	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	useEffect(() => {
		getCloudMapItemInstances();
	}, [pageNumber, pageSize, filters, updateFlag]);

	const handleSearchTagChange = (tags) => {
		let temp = [];
		forEach(tags, (tag) => {
			const name = tag.attr ? tag.attr.key : '';
			const currentTag = find(filterConf, (item) => item.SqlField === name) || {};
			if (!isEmpty(currentTag)) {
				temp.push({
					Name: name,
					Values: map(tag.values, (item) => item.name),
				});
			}
		});
		setFilters(temp);
		setTagSelectBoxValue(tags);
	};

	const tagSearchBoxProps = {
		minWidth: '70%',
		attributes,
		value: tagSelectBoxValue,
		hideHelp: true,
		tips: '支持资源ID、资源名和地域过滤，多个关键词用竖线"|"分隔',
		onChange: handleSearchTagChange,
		onSearchButtonClick: () => getCloudMapItemInstances(),
	};

	const handleResourceClear = async () => {
		await delCloudMapItem();
		getCloudMapItemInstances();
	};
	return (
		<Card style={{ padding: 20 }}>
			<Justify
				left={
					<span style={{ width: '100%' }} onKeyDown={(e) => e.stopPropagation()}>
						<TagSearchBox {...tagSearchBoxProps} />
					</span>
				}
				right={
					disableClearIns && (
						<Button type="primary" onClick={handleResourceClear}>
							一键清除
						</Button>
					)
				}
			/>
			<Table
				columns={DetailColumns}
				records={instances}
				compact
				key="InstanceId"
				style={{ marginTop: 20 }}
				topTip={
					isTableLoading ? (
						<StatusTip status="loading"></StatusTip>
					) : (
						instances.length === 0 && <StatusTip status="empty" />
					)
				}
				addons={[
					pageable({
						pageIndex: pageNumber,
						recordCount: totalPage,
						onPagingChange: handlePageChange,
					}),
				]}
			/>
		</Card>
	);
};
export default ResourceBindList;
