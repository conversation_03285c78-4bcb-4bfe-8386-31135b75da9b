/* 图形组件列表 */
import React, { useEffect, useState } from 'react';
import { message, Tag, Drawer } from '@tencent/tea-component';
import { SigmaType } from '@tencent/sigma-editor';
import { getNodeLabelName } from '../../utils';
import ResourceBindList from './ResourceBindList';
interface Prop {
	appId: number;
	node: any;
	MapUUId: string;
	productName: string;
	disableClearIns: boolean;
	sigma: SigmaType;
	delCloudMapItem: Function;
	closeDrawer: Function;
}

export function ResourceBoundTable({
	appId,
	MapUUId,
	node,
	sigma,
	productName,
	disableClearIns,
	delCloudMapItem,
	closeDrawer,
}: Prop) {
	const [nodeLabelName, setNodeLabelName] = useState('');
	useEffect(() => {
		try {
			const graphDataObj = JSON.parse(sigma.getLocalGraphData());
			const currentLabel = graphDataObj[node.linkTextLabel];
			setNodeLabelName(getNodeLabelName(currentLabel));
		} catch (err) {
			message.error({ content: 'GraphData JSON Error' });
		}
	}, []);

	return (
		<Drawer
			visible={true}
			title={
				<Tag theme="primary" className="drawer-title">
					{nodeLabelName}节点
				</Tag>
			}
			subtitle="当前节点绑定的实例列表"
			className="drawer-header"
			style={{ width: 1300 }}
			size="l"
			onClose={() => closeDrawer()}
		>
			<ResourceBindList
				appId={appId}
				MapUUId={MapUUId}
				disableClearIns={disableClearIns}
				productName={productName}
				node={node}
				delCloudMapItem={delCloudMapItem}
			/>
		</Drawer>
	);
}
