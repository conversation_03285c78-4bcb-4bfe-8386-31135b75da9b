import React, { useState, useEffect, FC, useMemo } from 'react';
import './style.less';
import { useForm, useField } from 'react-final-form-hooks';
import { Button, Form, Input, Alert, message, Modal } from '@tencent/tea-component';
import { pick, isEmpty, filter } from 'lodash';
import { FILE_TYPE } from '../../../conf/architecture';
import { GetArchitectureTemplateList } from '@src/api/architecture/architecture';

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return 'validating';
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? 'error' : 'success';
}
interface IPushClientMapFormProps {
	cloudMapDetail: any;
	onSubmit: any;
	closeDialog: Function;
}
const PushClientMapForm: FC<IPushClientMapFormProps> = ({ cloudMapDetail, onSubmit, closeDialog }: IPushClientMapFormProps) => {
	const [isMapTplExist, setIsMapTplExist] = useState(false);
	const operator = localStorage.getItem('engName');
	const [isEditeMode, setIsEditeMode] = useState(true);

	const initialVal = useMemo(() => ({ TemplateName: '' }
	), []);

	const validateName = (name) => {
		const regExp = new RegExp('^((?![\\/]).)*$');
		if (!name) {
			return '名称不能为空哦';
		} if (regExp.test(name)) {
			return isMapTplExist ? '模板名称重复' : undefined;;
		}
		return '名称命名不能包含 / 字符';
	};

	const getCloudTplList = async () => {
		try {
			const filters = [{ Name: 'exact_template_name', Values: [tempName.input.value] }];

			const params = {
				AppId: 1253985742,
				Filters: filters,
				FileType: FILE_TYPE.NONE,
				Operator: operator,
			};
			const res = await GetArchitectureTemplateList(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setIsMapTplExist(res.CloudMapTemplate?.length > 0);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	const { form, handleSubmit, validating, submitting } = useForm({
		onSubmit,
		initialValuesEqual: () => true,
		initialValues: initialVal,
		// @ts-ignore
		validate: ({ TemplateName }) => ({
			TemplateName: validateName(TemplateName),
		}),
	});
	const tempName = useField('TemplateName', form);

	useEffect(() => {
		getCloudTplList();
	}, [tempName.input.value]);

	const handlePushMap = () => {
		const tipStr = validateName(tempName.input.value);
		if (tipStr) return;
		setIsEditeMode(false);
		return false;
	}
	return <Modal visible={true} caption={isEditeMode ? "推送我的客户" : '确认推送'} onClose={() => closeDialog()}>
		<Modal.Body>
			<Alert type="warning">
				{
					isEditeMode
						? '推送后将在客户云架构控制台“公共模板-专家推荐模板”展示，不包含绑定资源，无法手动撤销，请谨慎推送！'
						: '可在”自定义模板“列表中查看推送记录'
				}
			</Alert>
			<form onSubmit={handleSubmit}>
				<Form>
					{
						isEditeMode
							? <Form.Item
								required
								label="模板名称"
								status={getStatus(tempName.meta, validating)}
								message={getStatus(tempName.meta, validating) === 'error' && tempName.meta.error}
							>
								<Input {...tempName.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入模板名称" />
							</Form.Item>
							: <Form.Item label="模板名称">
								<Form.Text>{tempName.input.value}</Form.Text>
							</Form.Item>
					}
					<Form.Item label="客户APPID/名称">
						<Form.Text>{`${cloudMapDetail.AppId}/${cloudMapDetail.CustomerName}`}</Form.Text>
					</Form.Item>
				</Form>
				<Form.Action>
					{
						!isEditeMode
						&& <>
							<Button
								type="primary"
								htmlType="submit"
								loading={submitting}
								disabled={validating}
							>
								确认推送
							</Button>
							<Button type="weak" htmlType="button" onClick={() => {
								setIsEditeMode(true);
								return false;
							}}>取消</Button>
						</>
					}
				</Form.Action>
			</form >
			<div className='push-map-op'>
				{
					isEditeMode
					&& <>
						<Button
							type="primary"
							onClick={() => {
								handlePushMap();
								return false;
							}}
							htmlType="button"
							className='push-map-op-submit'
						>
							推送
						</Button>
						<Button type="weak" htmlType="button" onClick={() => {
							closeDialog();
							return false;
						}}>取消</Button>
					</>
				}
			</div>
		</Modal.Body>
	</Modal>
};
export default PushClientMapForm;
