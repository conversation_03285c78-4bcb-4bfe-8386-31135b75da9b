import React, { useEffect, useMemo, useState } from 'react';
import { useHistory } from '@tea/app';

import { Button, Form, Input, TextArea, SelectMultiple, message, Select, Alert, List, Icon } from '@tencent/tea-component';

import { pick, isEmpty, map, filter } from 'lodash';
import { useForm, useField } from 'react-final-form-hooks';
import { PROJECT_TYPE, FILE_TYPE } from '../../conf/architecture';
import { useToggle } from '@src/routes/advisor/pages/broadcast/hooks/common';

import { getCustomerName } from '@src/api/advisor/estimate';
import {
    DescribeCloudMapBaseInfo,
    DescribeTAMGroupInfo,
    DescribeCollaoratorInfo,
    DescribeStaffDepartment,
    DescribeAppidSalesSupportor,
} from '@src/api/architecture/architecture';
import { Card, Col, Modal, Row, Text } from "@tea/component";
import { getAppIDByUin } from "@src/api/common";

function getStatus(meta, validating) {
    if (meta.active && validating) {
        return 'validating';
    }
    if (!meta.touched) {
        return null;
    }
    return meta.error ? 'error' : 'success';
}
export function CloudMapForm({
    cloudMapDetail,
    industryConfig,
    isExistCloudMap,
    onSubmit,
    closeCreateDialog,
}) {
    const history = useHistory();
    const [custName, setCustName] = useState(cloudMapDetail?.CustomerName);
    const [saleLead, setSaleLead] = useState('');
    const [leadStaff, setLeadStaff] = useState(cloudMapDetail?.LeadDepartment);
    const [isValidLead, setIsValidLead] = useState(true);
    const [creatorStaff, setCreatorStaff] = useState(cloudMapDetail?.CreaterDepartment);

    const [appIdValidater, setAppIdValidater] = useState({ IsAuthorized: false, IsValid: false, IsAfterSale: false });
    const [isAuthorization, setIsAuthorization] = useState(false);
    const [TAMGroup, setTAMGroup] = useState([]);
    const [projectTypeOption, setProjectTypeOption] = useState([]);

    const [collaorators, setCollaorators] = useState('');
    const [isLoading, startLoad, endLoad] = useToggle(false);

    const Industry = cloudMapDetail?.Industry ? cloudMapDetail.Industry : [];
    const operator = localStorage.getItem('engName');
    const [isMapNameExist, setIsMapNameExist] = useState(false);

    const [tips, setTips] = useState('');

    const initialVal = useMemo(() => (!isEmpty(cloudMapDetail)
        ? { ...pick(cloudMapDetail, ['MapName', 'AppId', 'TAMGroup', 'ProjectType', 'Creator', 'Lead', 'Comment']), Industry }
        : { MapName: '', AppId: '', Industry: [], Creator: operator, Lead: '', Comment: '' }), [cloudMapDetail]);

    const industryOption = useMemo(() => map(industryConfig, item => ({ text: item, value: item })), [industryConfig]);

    const validateName = (name) => {
        const regExp = new RegExp('^((?![\\/]).)*$');
        if (!name) {
            return '名称不能为空哦';
        } if (regExp.test(name)) {
            return isMapNameExist ? '架构图名称重复' : undefined;
        }
        return '名称命名不能包含 / 字符';
    };

    const validateAppId = (appId) => {
        const { IsAuthorized, IsValid, IsAfterSale } = appIdValidater;
        if (!appId) {
            return '请输入账户AppId';
        } if (!isAuthorization) {
            return '您没有权限访问该 APPID。请到 [权限管理] 申请相关权限。';
        } if (!IsValid) {
            return 'appid不合法';
        }
        setTips('');
        if (IsAuthorized) {
            if (IsAfterSale) {
                return projectType.input.value === '行业项目' ? "已转售后appid，请选择'售后项目'" : undefined;
            }
            else {
                return projectType.input.value === '售后项目' ? "未转售后appid，请选择'行业项目 或 专家项目'" : undefined;
            }
        }
        else {
            setTips("未开通云顾问appid，仅提供画图能力，不提供实例绑定和巡检");
            if (projectType.input.value === '售后项目' && !IsAfterSale) {
                return "未转售后appid，请选择”行业项目”";
            }
        }
    };

    const getCollaoratorInfo = async () => {
        if (!isExistCloudMap) return;
        const { AppId, MapUUId } = cloudMapDetail;
        try {
            const params = {
                AppId,
                Operator: operator,
                MapUUId,
            };
            const res = await DescribeCollaoratorInfo(params);
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                setCollaorators(res.Collaorators?.join());
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    const { form, handleSubmit, validating, submitting } = useForm({
        onSubmit: values => onSubmit(values, { custName, creatorDep: creatorStaff, leadDep: leadStaff }),
        initialValuesEqual: () => true,
        initialValues: initialVal,
        // @ts-ignore
        validate: ({ MapName, AppId, Industry, ProjectType, Lead, TAMGroup }) => ({
            MapName: validateName(MapName),
            AppId: isExistCloudMap ? undefined : validateAppId(AppId),
            ProjectType: isEmpty(ProjectType) ? '项目类型不能为空' : undefined,
            Lead: isEmpty(Lead) ? '架构负责人不能为空' : isValidLead ? undefined : '负责人输入不合法，请输入正确的企业微信英文名',
            Industry: isEmpty(Industry) ? '行业类型不能为空哦' : undefined,
            TAMGroup: ProjectType !== '行业项目' && isEmpty(TAMGroup) ? '客户所属TAM组不能为空哦' : undefined,
        }),
    });

    // 获取 appid uin
    const getCustomerInfo = async (AppId) => {
        if (!AppId) return;
        startLoad();
        try {
            const res = await getCustomerName({ AppId: +AppId });
            if (res.Error) {
                const code = res.Error.Code;
                if (code === 401) {
                    setIsAuthorization(false);
                }
                endLoad();
                return;
            }
            setCustName(res.CustomerName);
            setIsAuthorization(true);
            setAppIdValidater(pick(res, ['IsAuthorized', 'IsValid', 'IsAfterSale']));
            endLoad();
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
            endLoad();
        }
    };

    const projectType = useField('ProjectType', form);
    const mapName = useField('MapName', form);
    const appId = useField('AppId', form);
    const tamGroup = useField('TAMGroup', form);
    const industry = useField('Industry', form);
    const lead = useField('Lead', form);
    const comment = useField('Comment', form);

    useEffect(() => {
        if (!isExistCloudMap) {
            form.initialize({
                ProjectType: projectType.input.value,
                MapName: mapName.input.value,
                AppId: appId.input.value,
                Industry: industry.input.value,
                TAMGroup: tamGroup.input.value,
                Comment: comment.input.value,
                Lead: saleLead,
            })
        }
    }, [saleLead, projectType.input.value]);

    useEffect(() => {
        getCustomerInfo(appId.input.value);
        if (projectType.input.value === '售后项目') {
            getSaleOwner(appId.input.value);
        }
    }, [appId.input.value, projectType.input.value]);

    useEffect(() => {
        getStaffDepartment(lead.input.value, 'lead');
    }, [lead.input.value]);

    useEffect(() => {
        getCustomerInfo(appId.input.value);
        getStaffDepartment(lead.input.value, 'lead');
        getTAMGroupInfo();
    }, []);

    const getCloudMapList = async () => {
        try {
            const filters = [{ Name: 'exact_map_name', Values: [mapName.input.value] }];

            const params = {
                AppId: 1253985742,
                Filters: filters,
                Offset: 0,
                Limit: 10,
                Operator: operator,
                FileType: FILE_TYPE.NONE,
            };
            const res = await DescribeCloudMapBaseInfo(params);
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                if (isExistCloudMap) {
                    const newCloudMapList = filter(res.CloudMap, i => i.MapName !== initialVal.MapName);
                    setIsMapNameExist(newCloudMapList.length > 0);
                } else {
                    setIsMapNameExist(res.CloudMap?.length > 0);
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    const getTAMGroupInfo = async () => {
        try {
            const res = await DescribeTAMGroupInfo({ AppId: 1253985742 });
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                const tamGroup = map(res.TAMGroups, ({ TAMName }) => ({ value: TAMName }))
                    .filter(({ value }) => !!value);
                setTAMGroup(tamGroup);

                let projectTypeOption = map(res.ProjectTypes, item => ({ value: item, text: item, tooltip: PROJECT_TYPE[item]?.tips }));
                projectTypeOption = isExistCloudMap ? projectTypeOption : filter(projectTypeOption, item => item.text !== '转售后项目');
                setProjectTypeOption(projectTypeOption);
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    // 获取人物部门
    const getStaffDepartment = async (opter, type) => {
        if (!opter) return;
        try {
            const res = await DescribeStaffDepartment({ AppId: 1253985742, Operator: opter });
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                const { StaffDepartment, IsValid } = res;
                if (type === 'creator') {
                    setCreatorStaff(StaffDepartment);
                } else {
                    setLeadStaff(StaffDepartment);
                    setIsValidLead(IsValid);
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    // 售后获取owner
    const getSaleOwner = async (AppId) => {
        if (!AppId) return;
        try {
            const res = await DescribeAppidSalesSupportor({ AppId: +AppId });
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                setSaleLead(res?.SalesSupportor);
                setLeadStaff(res?.DepartmentName);
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    useEffect(() => {
        getCloudMapList();
    }, [mapName.input.value]);

    useEffect(() => {
        getCollaoratorInfo();
    }, []);

    useEffect(() => {
        getStaffDepartment(operator, 'creator');
    }, [operator]);

    const alertTips = (
        <Alert>
            <h4>AppId创建后无法更改，如需绑定其他AppId，可以使用以下方法：</h4>
            <List type="bullet">
                <List.Item>克隆当前架构图，并绑定新的AppId。</List.Item>
                <List.Item>保存当前架构图为模板，在重新新建架构图后，引入此模板。</List.Item>
            </List>
        </Alert>
    );

    const projectTypeTips = (
        <>
            <p>在新建保存页面，三种类型</p>
            <p>1、售后项目：已转售后的客户架构图，appid售后owner为架构图负责人</p>
            <p>2、行业项目：未转售后的客户架构图，行业相关角色为架构图负责人</p>
            <p>3、专家项目：高可用或容灾项目，售后技术支持专家为架构图负责人</p>
            <p>转售后项目，是通过转售后流程流转，无法自己选择。</p>
            <p>行业项目，通过转售后项目审批后，可转为“转售后项目”</p>
        </>
    );
    const [uin, setUin] = useState<string>("");                                   // 转换源UIN
    const [appIdTransfered, setAppIdTransfered] = useState<number>(0);            // 转换目标APPID
    const [visibleUinTransfer, setVisibleUinTransfer] = useState(false);          // UIN转换
    // UIN转换
    const transferUin = async (uin: string) => {
        if (uin.trim() == "") {
            message.error({ content: "uin为空，请输入合法uin值" });
            return;
        }

        try {
            const res = await getAppIDByUin({
                SrcUin: uin,
            });
            if (res.Error) {
                const msg = res.Error.Message
                message.error({ content: msg });
                return;
            } else {
                setAppIdTransfered(res.AppId)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误";
            message.error({ content: msg });
        }
    };
    return <>
        <form onSubmit={handleSubmit}>
            <Form>
                <Form.Item
                    required
                    label="项目类型"
                    tips={projectTypeTips}
                    status={getStatus(projectType.meta, validating)}
                    message={getStatus(projectType.meta, validating) === 'error' && projectType.meta.error}
                >
                    <Select
                        {...projectType.input}
                        matchButtonWidth
                        searchable
                        placeholder="请选择项目类型"
                        disabled={isExistCloudMap}
                        options={projectTypeOption}
                        appearance="button"
                        size="m"
                    />
                </Form.Item>
                <Form.Item
                    required
                    label="架构图名称"
                    status={getStatus(mapName.meta, validating)}
                    message={getStatus(mapName.meta, validating) === 'error' && mapName.meta.error}
                >
                    <Input {...mapName.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入架构图名称" />
                </Form.Item>
                <Form.Item
                    required
                    label="AppId"
                    tips={isExistCloudMap && alertTips}
                    status={getStatus(appId.meta, validating)}
                    message={getStatus(appId.meta, validating) === 'error' && appId.meta.error}
                    suffix={<Button type="text" onClick={(e) => {
                        setVisibleUinTransfer(true);
                        e.preventDefault();
                    }
                    }>UIN转换</Button>}
                >
                    <Input {...appId.input} disabled={isExistCloudMap} onKeyDown={e => e.stopPropagation()} placeholder="请输入用户AppId" />
                </Form.Item>
                {
                    projectType.input.value !== '行业项目'
                    && <Form.Item
                        required
                        label="客户所属TAM组"
                        status={getStatus(tamGroup.meta, validating)}
                        message={getStatus(tamGroup.meta, validating) === 'error' && tamGroup.meta.error}
                    >
                        <Select
                            {...tamGroup.input}
                            matchButtonWidth
                            searchable
                            placeholder="请选择客户所属TAM组"
                            options={TAMGroup}
                            appearance="button"
                            size="m"
                        />
                    </Form.Item>
                }
                <Form.Item
                    required
                    label="行业类型"
                    status={getStatus(industry.meta, validating)}
                    message={getStatus(industry.meta, validating) === 'error' && industry.meta.error}
                >
                    <SelectMultiple
                        {...industry.input}
                        staging={false}
                        matchButtonWidth
                        searchable
                        placeholder="请选择行业类型"
                        options={industryOption}
                        appearance="button"
                        size="m"
                    />
                </Form.Item>
                <Form.Item label="架构图创建人" required>
                    <Input value={initialVal.Creator} onKeyDown={e => e.stopPropagation()} disabled />
                </Form.Item>
                <Form.Item label="创建人部门/中心">
                    <Input value={creatorStaff} onKeyDown={e => e.stopPropagation()} disabled />
                </Form.Item>
                <Form.Item
                    required
                    label="架构负责人"
                    tips="架构图第一责任人，审批架构图相关权限"
                    status={getStatus(lead.meta, validating)}
                    message={getStatus(lead.meta, validating) === 'error' && lead.meta.error}
                >
                    <Input
                        {...lead.input}
                        onKeyDown={e => e.stopPropagation()}
                        disabled={isExistCloudMap || projectType.input.value === '售后项目'}
                        placeholder="架构图第一责任人，审批架构图相关权限"
                    />
                </Form.Item>
                <Form.Item label="负责人部门/中心">
                    <Input value={leadStaff} onKeyDown={e => e.stopPropagation()} disabled />
                </Form.Item>
                <Form.Item label="协作人" tips="对架构图有编辑、资源增减以及报告查看权限。可多人协作，架构图负责人可在 '操作'-'协作人' 手动增减">
                    <Input value={isExistCloudMap ? collaorators : '对架构图有编辑、资源增减以及报告查看权限。可多人协作'} onKeyDown={e => e.stopPropagation()} disabled />
                </Form.Item>
                <Form.Item label="客户名称">
                    <Input value={custName} onKeyDown={e => e.stopPropagation()} disabled />
                </Form.Item>
                <Form.Item
                    label="备注"
                    status={getStatus(comment.meta, validating)}
                    message={getStatus(comment.meta, validating) === 'error' && comment.meta.error}
                >
                    <TextArea {...comment.input} onKeyDown={e => e.stopPropagation()} placeholder="请输入备注" />
                </Form.Item>
            </Form>
            {
                tips
                && <div style={{ display: 'flex', margin: '10px 0' }}>
                    <Icon type="pending" />
                    <span style={{ color: '#ff7200', marginLeft: 4 }}>{tips}</span>
                </div>
            }
            <Form.Action>
                <Button
                    type="primary"
                    htmlType="submit"
                    loading={submitting}
                    disabled={isLoading || !projectType.input.value}
                >
                    {isExistCloudMap ? '保存' : '创建'}
                </Button>
                <Button type="weak" htmlType="button" onClick={() => {
                    closeCreateDialog();
                    return false;
                }}>取消</Button>
            </Form.Action>
        </form>
        { // UIN转APPID弹窗
            <Modal size={"m"} visible={visibleUinTransfer} onClose={() => { setVisibleUinTransfer(false); close(); }} caption="将 UIN 转换为 APPID">
                <Modal.Body>
                    <Card key={"uinTransfer"} style={{ margin: 2 }}>
                        <Card.Body>
                            <Row gap={30} verticalAlign={"middle"}>
                                <Col span={4}>客户UIN</Col>
                                <Col span={6}>
                                    <Input
                                        value={uin}
                                        onChange={(value, context) => {
                                            setUin(value);
                                        }}
                                        placeholder="请输入UIN，以转换输出APPID"
                                    />
                                </Col>
                            </Row >
                            <Row>
                                <Col span={4}>
                                    <Button type="link" onClick={() => { transferUin(uin); }} >
                                        转换<Icon type="arrowright" />
                                    </Button>
                                </Col>
                                <Col span={6}>
                                    <Text align="left">{appIdTransfered ? appIdTransfered : ""}</Text>
                                </Col>
                            </Row >
                        </Card.Body>
                    </Card>
                </Modal.Body>
            </Modal>
        }
    </>;
}
