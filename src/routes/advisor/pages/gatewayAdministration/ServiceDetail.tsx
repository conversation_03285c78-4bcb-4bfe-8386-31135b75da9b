import React, { useEffect, useMemo, useState } from 'react';
import { cloneDeep } from "lodash";
import { Layout, Card, Button, Form, Input, message, Alert } from "@tea/component";
import { getStatus } from "@src/utils/form";
import { useField, useForm } from "react-final-form-hooks";
import { useHistory } from "@tea/app";
import { CreateTsaService, DescribeTsaServiceList, UpdateTsaService } from "@src/api/advisor/gatewayAdministration";
import { createWarnText } from "@src/routes/advisor/pages/gatewayAdministration/constants";


export function ServiceDetail({match}) {
  const history = useHistory();
  const { Body, Content } = Layout;
  const name = match.params.type;
  const isUpdate = name !== 'empty';
  const [submitLoading, setSubmitLoading] = useState(false);
  // 提交表单
  const onSubmit = async (value) => {
    setSubmitLoading(true);
    try {
      setSubmitLoading(false);
      if (isUpdate) {
        await UpdateTsaService({
          ...value,
          TimeOut: parseInt(value.TimeOut),
          ShowError: true,
          OnlyData: true
        });
      } else {
        await CreateTsaService({
          ...value,
          TimeOut: parseInt(value.TimeOut),
          ShowError: true,
          OnlyData: true
        });
      }
      message.success({
        content: '操作成功'
      });
      history.push(`/advisor/gateway-administration`);
    } catch (err) {
      setSubmitLoading(false);
    };
  };
  const { form, handleSubmit, values, validating } = useForm<any>({
    onSubmit: value => onSubmit(value),
    initialValuesEqual: () => true,
    initialValues: {},
    validate: (formInfo) => {
      return {
        ServiceName: !formInfo.ServiceName ? '请输入服务名称' : undefined,
        Description: !formInfo.Description ? '请输入服务描述' : undefined,
        Url: !formInfo.Url ? '请输入服务地址' : (formInfo.Url.startsWith('https://') || formInfo.Url.startsWith('http://')) ? undefined : '服务格式不地址',
        TimeOut: !formInfo.TimeOut ? '请输入服务超时时间' : !/^[1-9]\d*$/.test(formInfo.TimeOut) ? '请输入正整数' : undefined,
      };
    },
  });
  const serviceNameField = useField('ServiceName', form);
  const descriptionField = useField('Description', form);
  const urlField = useField('Url', form);
  const timeoutField = useField('TimeOut', form);
  const getDescribeTsaServiceList = async (serviceName?: string) => {
    try {
      const res = await DescribeTsaServiceList({
        Page: 1,
        PageSize: 10,
        Filters: [
          {
            Key: 'ServiceName',
            Values: [serviceName]
          }
        ],
        IsStrict: 1,
        ShowError: true,
        OnlyData: true,
      });
      const serviceInfo = res.ServiceList[0];
      form.initialize({
        ServiceName: serviceInfo.ServiceName,
        Description: serviceInfo.Description,
        Url: serviceInfo.Url,
        TimeOut: serviceInfo.TimeOut
      });
    } catch (err) {};
  };
  useMemo(()=>{
    if (isUpdate) {
      getDescribeTsaServiceList(name);
    }
  }, []);
  return <Body className={'gateway-form-body'}>
    <Content.Header
      showBackButton
      onBackButtonClick={() => {
        history.push('/advisor/gateway-administration');
      }}
      title={isUpdate ? '编辑服务' : '新增服务'}
      subtitle={
        <Alert type="warning">
          {createWarnText}
        </Alert>
      }
    ></Content.Header>
    <Content.Body>
      <Card>
        <Card.Body>
          <form onSubmit={handleSubmit}>
            <Form>
              <Form.Item
                required
                label={'服务名称'}
                status={getStatus(serviceNameField.meta, validating)}
                message={(getStatus(serviceNameField.meta, validating) === "error" && serviceNameField.meta.error)}
                tips={'服务名称为服务的唯一英文标识'}
              >
                <Input
                  {...serviceNameField.input}
                  placeholder={'请输入服务名称'}
                  size={'l'}
                  disabled={isUpdate}
                />
              </Form.Item>
              <Form.Item
                required
                label={'服务描述'}
                status={getStatus(descriptionField.meta, validating)}
                message={(getStatus(descriptionField.meta, validating) === "error" && descriptionField.meta.error)}
              >
                <Input
                  {...descriptionField.input}
                  placeholder={'请输入服务描述'}
                  size={'l'}
                />
              </Form.Item>
              <Form.Item
                required
                label={'服务地址'}
                status={getStatus(urlField.meta, validating)}
                message={(getStatus(urlField.meta, validating) === "error" && urlField.meta.error)}
              >
                <Input
                  {...urlField.input}
                  placeholder={'请输入服务地址'}
                  size={'l'}
                />
              </Form.Item>
              <Form.Item
                required
                label={'服务超时时间(s)'}
                status={getStatus(timeoutField.meta, validating)}
                message={(getStatus(timeoutField.meta, validating) === "error" && timeoutField.meta.error)}
              >
                <Input
                  {...timeoutField.input}
                  placeholder={'请输入服务超时时间'}
                  size={'l'}
                />
              </Form.Item>
            </Form>
            <div className='btn-wrap'>
              <Button htmlType="submit" type={'primary'} loading={submitLoading}>提交</Button>
              <Button onClick={
                ()=>{
                  history.push(`/advisor/gateway-administration`);
                }
              }>取消</Button>
            </div>
          </form>
        </Card.Body>
      </Card>
    </Content.Body>
  </Body>;
}