import React, { useEffect, useMemo, useState } from 'react';
import { Tabs, TabPanel, Layout } from '@tencent/tea-component';
import { Service } from "@src/routes/advisor/pages/gatewayAdministration/Service";
import { Api } from "@src/routes/advisor/pages/gatewayAdministration/Api";
import './index.less';

export function GatewayAdministration() {
  const { Body, Content } = Layout;
  const tabs = [
    { id: "service", label: "服务列表"},
    { id: "api", label: "API列表"}
  ];
  return <Body>
    <Content.Header title={'网关管理'}></Content.Header>
    <Content.Body>
      <Tabs
        ceiling
        animated={false}
        tabs={tabs}
        destroyInactiveTabPanel={false}
      >
        <TabPanel id="service">
          <Service />
        </TabPanel>
        <TabPanel id="api">
          <Api />
        </TabPanel>
      </Tabs>
    </Content.Body>
  </Body>;
}