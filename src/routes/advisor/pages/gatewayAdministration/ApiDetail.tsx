import React, { useMemo, useState } from 'react';
import { Layout, Card, Button, Form, Input, message, Alert, Select, Icon, Radio } from "@tea/component";
import { getStatus } from "@src/utils/form";
import { useField, useForm } from "react-final-form-hooks";
import { useHistory } from "@tea/app";
import {
  CreateTsaApi,
  DescribeTsaApiList,
  DescribeTsaServiceNameList,
  UpdateTsaApi,
} from "@src/api/advisor/gatewayAdministration";
import { createWarnText } from "@src/routes/advisor/pages/gatewayAdministration/constants";


export function ApiDetail({match}) {
  const history = useHistory();
  const { Body, Content } = Layout;
  const name = match.params.type;
  const isUpdate = name !== 'empty';
  const [submitLoading, setSubmitLoading] = useState(false);
  const [serviceNameList, setServiceNameList] = useState([]);
  // 提交表单
  const onSubmit = async (value) => {
    setSubmitLoading(true);
    try {
      setSubmitLoading(false);
      if (isUpdate) {
        await UpdateTsaApi({
          ...value,
          IsAuth: parseInt(value.IsAuth),
          TimeOut: parseInt(value.TimeOut),
          ShowError: true,
          OnlyData: true
        });
      } else {
        await CreateTsaApi({
          ...value,
          IsAuth: parseInt(value.IsAuth),
          TimeOut: parseInt(value.TimeOut),
          ShowError: true,
          OnlyData: true
        });
      }
      message.success({
        content: '操作成功'
      });
      history.push(`/advisor/gateway-administration`);
    } catch (err) {
      setSubmitLoading(false);
    };
  };
  const { form, handleSubmit, values, validating } = useForm<any>({
    onSubmit: value => onSubmit(value),
    initialValuesEqual: () => true,
    initialValues: {},
    validate: (formInfo) => {
      return {
        ServiceName: !formInfo.ServiceName ? '请选择服务名称' : undefined,
        ApiName: !formInfo.ApiName ? '请输入API英文名称 ' : undefined,
        ApiCName: !formInfo.ApiCName ? '请输入API中文名称 ' : undefined,
        ApiRuleType: !formInfo.ApiRuleType ? '请选择接口鉴权方式 ' : undefined,
        ApiType: !formInfo.ApiType ? '请选择接口类型 ' : undefined,
        IsAuth: !formInfo.IsAuth ? '请选择是否接入鉴权 ' : undefined,
        TimeOut: !formInfo.TimeOut ? '请输入API超时时间' : !/^[1-9]\d*$/.test(formInfo.TimeOut) ? '请输入正整数' : undefined,
      };
    },
  });
  const serviceNameField = useField('ServiceName', form);
  const apiNameField = useField('ApiName', form);
  const apiCNameField = useField('ApiCName', form);
  const apiRuleTypeField = useField('ApiRuleType', form);
  const apiTypeField = useField('ApiType', form);
  const isAuthField = useField('IsAuth', form);
  const timeoutField = useField('TimeOut', form);
  const getDescribeTsaApiList = async (serviceName?: string) => {
    try {
      const res = await DescribeTsaApiList({
        Page: 1,
        PageSize: 10,
        Filters: [
          {
            Key: 'ApiName',
            Values: [serviceName]
          }
        ],
        IsStrict: 1,
        ShowError: true,
        OnlyData: true,
      });
      const serviceInfo = res.ApiList[0];
      form.initialize({
        ServiceName: serviceInfo.ServiceName,
        ApiName: serviceInfo.ApiName,
        ApiCName: serviceInfo.ApiCName,
        ApiRuleType: serviceInfo.ApiRuleType,
        ApiType: serviceInfo.ApiType,
        IsAuth: String(serviceInfo.IsAuth),
        TimeOut: serviceInfo.TimeOut
      });
    } catch (err) {};
  };
  const getDescribeTsaServiceNameList = async () => {
    try {
      const res = await DescribeTsaServiceNameList({
        Page: 1,
        PageSize: 10000,
        ShowError: true,
        OnlyData: true,
      });
      setServiceNameList(res.ServiceNames);
    } catch (err) {};
  };
  useMemo(()=>{
    if (isUpdate) {
      getDescribeTsaApiList(name);
    }
    getDescribeTsaServiceNameList();
  }, []);
  return <Body className={'gateway-form-body'}>
    <Content.Header
      showBackButton
      onBackButtonClick={() => {
        history.push('/advisor/gateway-administration');
      }}
      title={isUpdate ? '编辑API' : '新增API'}
      subtitle={
        <Alert type="warning">
          {createWarnText}
        </Alert>
      }
    ></Content.Header>
    <Content.Body>
      <Card>
        <Card.Body>
          <form onSubmit={handleSubmit}>
            <Form>
              <Form.Item
                required
                label={'服务标识'}
                status={getStatus(serviceNameField.meta, validating)}
                message={(getStatus(serviceNameField.meta, validating) === "error" && serviceNameField.meta.error)}
                suffix={
                  <Icon type="refresh-blue"
                        style={
                          {
                            cursor: 'pointer'
                          }
                        }
                    onClick={
                      ()=>{
                        getDescribeTsaServiceNameList();
                      }
                    }
                  />
                }
              >
                <Select
                  {...serviceNameField.input}
                  placeholder={'请选择服务标识'}
                  size={'l'}
                  appearance={'button'}
                  options={
                    serviceNameList.map((item)=>{
                      return {
                        text: item,
                        value: item
                      };
                    })
                  }
                />
              </Form.Item>
              <Form.Item
                required
                label={'API英文名称'}
                status={getStatus(apiNameField.meta, validating)}
                message={(getStatus(apiNameField.meta, validating) === "error" && apiNameField.meta.error)}
                tips={'API英文名称，即公共请求参数的"Action"'}
              >
                <Input
                  {...apiNameField.input}
                  placeholder={'请输入API英文名称'}
                  size={'l'}
                  disabled={isUpdate}
                />
              </Form.Item>
              <Form.Item
                required
                label={'API中文名称'}
                status={getStatus(apiCNameField.meta, validating)}
                message={(getStatus(apiCNameField.meta, validating) === "error" && apiCNameField.meta.error)}
              >
                <Input
                  {...apiCNameField.input}
                  placeholder={'请输入API中文名称'}
                  size={'l'}
                />
              </Form.Item>
              <Form.Item
                required
                label={'接口鉴权方式'}
                status={getStatus(apiRuleTypeField.meta, validating)}
                message={(getStatus(apiRuleTypeField.meta, validating) === "error" && apiRuleTypeField.meta.error)}
              >
                <Radio.Group {...apiRuleTypeField.input}>
                  <Radio name="appid">按appid鉴权</Radio>
                  <Radio name="name">名字鉴权</Radio>
                  <Radio name="null">不需要鉴权</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                required
                label={'接口类型'}
                status={getStatus(apiTypeField.meta, validating)}
                message={(getStatus(apiTypeField.meta, validating) === "error" && apiTypeField.meta.error)}
              >
                <Radio.Group {...apiTypeField.input}>
                  <Radio name="read">读(read)</Radio>
                  <Radio name="write">写(write)</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                required
                label={'是否接入鉴权'}
                status={getStatus(isAuthField.meta, validating)}
                message={(getStatus(isAuthField.meta, validating) === "error" && isAuthField.meta.error)}
              >
                <Radio.Group {...isAuthField.input}>
                  <Radio name="1">是</Radio>
                  <Radio name="0">否</Radio>
                </Radio.Group>
              </Form.Item>
              <Form.Item
                required
                label={'API超时时间(s)'}
                status={getStatus(timeoutField.meta, validating)}
                message={(getStatus(timeoutField.meta, validating) === "error" && timeoutField.meta.error)}
              >
                <Input
                  {...timeoutField.input}
                  placeholder={'请输入API超时时间'}
                  size={'l'}
                />
              </Form.Item>
            </Form>
            <div className='btn-wrap'>
              <Button htmlType="submit" type={'primary'} loading={submitLoading}>提交</Button>
              <Button onClick={
                ()=>{
                  history.push(`/advisor/gateway-administration`);
                }
              }>取消</Button>
            </div>
          </form>
        </Card.Body>
      </Card>
    </Content.Body>
  </Body>;
}