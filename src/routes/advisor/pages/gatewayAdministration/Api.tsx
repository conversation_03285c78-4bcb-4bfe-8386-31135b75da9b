import React, { useEffect, useMemo, useState } from 'react';
import { cloneDeep } from "lodash";
import { Justify, Button, Table, Modal, Card } from '@tencent/tea-component';
import { DescribeTsaApiList, DeleteTsaApi } from "@src/api/advisor/gatewayAdministration";
import { message, StatusTip } from "@tea/component";
import { useHistory } from "@tea/app";
import { ComprehensiveSearch } from "@src/components/ComprehensiveSearch";
import { useActivate } from "react-activation";
import { statusMap } from "@src/routes/advisor/pages/gatewayAdministration/constants";

export function Api() {
  const history = useHistory();
  const { selectable, pageable } = Table.addons;
  const [onOff, setOnOff] = useState(false);
  const [pageIndex, setPageIndex] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [filters, setFilters] = useState([]);
  const [modalPageIndex, setModalPageIndex] = useState(1);
  const [modalPageSize, setModalPageSize] = useState(10);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [visible, setVisible] = useState(false);
  const [viewMap, setViewMap] = useState({});
  const [viewOne, setViewOne] = useState([]);
  const [delType, setDelType] = useState(1);
  const [loading, setLoading] = useState(false);
  const [delLoading, setDelLoading] = useState(false);
  const [records, setRecords] = useState({
    ApiList: [],
    Total: 0
  });
  const getDescribeTsaApiList = async () => {
    setLoading(true);
    try {
      const res = await DescribeTsaApiList({
        Page: pageIndex,
        PageSize: pageSize,
        Filters: filters,
        ShowError: true,
        OnlyData: true,
      });
      setRecords(res);
      res?.ApiList?.forEach((item)=>{
        viewMap[item.ApiName] = cloneDeep(item);
      });
      setViewMap(cloneDeep(viewMap));
      setLoading(false);
    } catch (err) {
      setLoading(false);
    };
  };
  useMemo(()=>{
    setTimeout(()=>{
      getDescribeTsaApiList();
    }, 0);
  }, [pageIndex, pageSize, filters, onOff]);
  useActivate(()=>{
    setTimeout(()=>{
      getDescribeTsaApiList();
    }, 0);
  });
  const columns = [
    {
      key: "ApiName",
      header: "API名称",
      render(item) {
        return <div>
          {
            item.ApiName
          }
        </div>;
      }
    },
    {
      key: "ApiCName",
      header: "API中文名称",
      render(item) {
        return <div>
          {
            item.ApiCName
          }
        </div>;
      },
    },
    {
      key: "TimeOut",
      header: "API超时时间(s)",
      render(item) {
        return <div>
          {
            item.TimeOut
          }
        </div>;
      },
    },
    {
      key: "ServiceName",
      header: "服务名称",
      render(item) {
        return <div>
          {
            item.ServiceName
          }
        </div>;
      },
      // width: 100
    },
    {
      key: "Env",
      header: "环境"
    },
    {
      key: "Status",
      header: "状态",
      render(item) {
        return <div>
          {statusMap[item.Status]}
        </div>;
      }
    },
    // {
    //   key: "Url",
    //   header: "服务地址",
    //   render(item) {
    //     return <div>
    //       {item.Url}
    //     </div>;
    //   }
    // },
    {
      key: "Creator",
      header: "创建人"
    },
    {
      key: "Updater",
      header: "更新人"
    },
    {
      key: "CreatTime",
      header: "创建时间",
      width: '135px'
    },
    {
      key: "UpdateTime",
      header: "更新时间",
      width: '135px'
    },
    {
      key: "handle",
      header: "操作",
      render(item) {
        return <>
          <Button
            type={'link'}
            onClick={
              ()=>{
                history.push(`/advisor/gateway-administration/api/${item.ApiName}`);
              }
            }
          >编辑</Button>
          <Button
            onClick={
              ()=>{
                setDelType(1);
                setViewOne([cloneDeep(item)]);
                setVisible(true);
              }
            }
            type={'link'}>删除</Button>
        </>;
      }
    },
  ];
  const getViewList = ()=>{
    const list = [];
    for (const key in viewMap) {
      if (selectedKeys.includes(key)) {
        list.push(viewMap[key]);
      }
    }
    return list;
  };
  const deleteTsaApi = async () => {
    setDelLoading(true);
    try {
      await DeleteTsaApi({
        ApiName: delType === 1 ? [viewOne[0]?.ApiName] : selectedKeys,
        ShowError: true,
        OnlyData: true
      });
      setDelLoading(false);
      setVisible(false);
      setSelectedKeys([]);
      message.success({
        content: '操作成功'
      });
      // setPageIndex(1);
      // setPageSize(10);
      setOnOff(!onOff);
    } catch (err) {
      setDelLoading(false);
    };
  };
  return <div>
    <Card>
      <Card.Body>
        <ComprehensiveSearch
          originFilterData={
            [
              {
                label: 'API名称',
                name: 'ApiName',
                type: 'input',
                value: ''
              },
              {
                label: '服务名称',
                name: 'ServiceName',
                type: 'input',
                value: ''
              },
              {
                label: '创建人',
                name: 'Creator',
                type: 'input',
                value: ''
              },
              {
                label: '更新人',
                name: 'Updater',
                type: 'input',
                value: ''
              },
            ] as any
          }
          onReset={()=>{
            setFilters([]);
            setPageIndex(1);
          }}
          onSearch={(filters)=>{
            setFilters(filters.map((item)=>{
              return {
                Key: item.Name,
                Values: item.Values
              };
            }));
            setPageIndex(1);
          }}
          suffix={
            <>
              <Button
                style={
                  {
                    margin: '0 10px'
                  }
                }
                onClick={
                  ()=>{
                    history.push('/advisor/gateway-administration/api/empty');
                  }
                }
              >新建</Button>
              <Button
                disabled={selectedKeys?.length === 0}
                onClick={
                  ()=>{
                    setDelType(2);
                    setVisible(true);
                  }
                }>删除</Button>
            </>
          }
        />
      </Card.Body>
    </Card>
    <Card>
      <Card.Body>
        <Table
          verticalTop
          records={records?.ApiList}
          recordKey="ApiName"
          columns={columns}
          topTip={(loading || records.ApiList?.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />}
          addons={[
            pageable({
              recordCount: records?.Total,
              pageIndex: pageIndex,
              pageSize: pageSize,
              onPagingChange: ({pageIndex, pageSize}) => {
                setPageSize(pageSize);
                setPageIndex(pageIndex);
              },
              pageSizeOptions: [10, 20, 50, 100, 1000],
            }),
            selectable({
              value: selectedKeys,
              onChange: (keys, context) => {
                setSelectedKeys(keys);
              }
            }),
          ]}
        />
      </Card.Body>
    </Card>
    <Modal size={'xl'} visible={visible} caption="确认删除API？" onClose={
      ()=>{
        setVisible(false);
      }
    }>
      <Modal.Body>
        <Table
          verticalTop
          records={delType === 1 ? viewOne : getViewList()}
          columns={columns.filter((item)=>item.key !== 'handle')}
          addons={[
            pageable({
              recordCount: delType === 1 ? 1 : getViewList()?.length,
              pageIndex: modalPageIndex,
              pageSize: modalPageSize,
              onPagingChange: ({pageIndex, pageSize}) => {
                setModalPageSize(pageSize);
                setModalPageIndex(pageIndex);
              },
              pageSizeOptions: [10, 20, 50, 100, 1000],
            }),
          ]}
        />
      </Modal.Body>
      <Modal.Footer>
        <Button loading={delLoading} type="primary" onClick={
          ()=>{
            deleteTsaApi();
          }
        }>
          确定
        </Button>
        <Button type="weak" onClick={
          ()=>{
            setVisible(false);
          }
        }>
          取消
        </Button>
      </Modal.Footer>
    </Modal>
  </div>;
}