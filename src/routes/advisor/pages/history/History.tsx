import React, { useState, useEffect, useMemo } from 'react';
import { Table, Justify, Button, SearchBox, Card, Layout, message as tips } from '@tencent/tea-component';
import { formatJson } from '@src/utils/formatJson';
import moment from 'moment';
import { getAdvisorCustomer } from '@src/api/advisor/overview';
import { Link } from 'react-router-dom';
import { areaList as regionList } from '@src/utils/list';

const { Body, Content } = Layout;
const { pageable, autotip, sortable, filterable } = Table.addons;

const columns = [
	{
		key: 'uin',
		header: '主账号UIN',
		render: (record) => <Link to={`/advisor/assess/result/${record.appid}`}>{record.uin}</Link>,
	},
	{
		key: 'appid',
		header: 'APPID',
	},
	{
		key: 'nickname',
		header: '客户简称',
	},
	{
		key: 'region',
		header: '所属地域',
	},
	{
		key: 'high',
		header: '高风险数量（个）',
	},
	{
		key: 'medium',
		header: '中风险数量（个）',
	},
	{
		key: 'low',
		header: '低风险数量（个）',
	},
	{
		key: 'authTime',
		header: '开通时间',
	},
	{
		key: 'lastAssessTime',
		header: '最后扫描时间',
	},
];
export function History() {
	// 用户列表
	const [userList, setUserList] = useState([]);
	// 列表项总数
	const [total, setTotal] = useState(0);

	const [loading, setLoading] = useState(false);

	const [error, setError] = useState(false);
	// 搜索输入
	const [searchInput, setSearchInput] = useState('');
	// 当前排序列
	const [sorts, setSorts] = useState([]);
	// 所选地域
	const [filterRegions, setFilterRegions] = useState([]);
	// 分页
	const [pageInfo, setPageInfo] = useState({
		pageIndex: 1,
		pageSize: 10,
	});

	const filterSort = useMemo(() => {
		let filterSort = '';
		sorts.forEach((item) => {
			switch (item.by) {
				case 'high':
					filterSort = item.order === 'desc' ? '-HighRiskCount' : 'HighRiskCount';
					break;
				case 'medium':
					filterSort = item.order === 'desc' ? '-MediumRiskCount' : 'MediumRiskCount';
					break;
				case 'low':
					filterSort = item.order === 'desc' ? '-LowRiskCount' : 'LowRiskCount';
					break;
				case 'authTime':
					filterSort = item.order === 'desc' ? '-OpeningTime' : 'OpeningTime';
					break;
				case 'lastAssessTime':
					filterSort = item.order === 'desc' ? '-LastEvaluationTime' : 'LastEvaluationTime';
					break;
			}
		});
		return filterSort;
	}, [sorts]);

	useEffect(() => {
		fetch();
	}, [pageInfo, filterSort, filterRegions]);

	const fetch = async () => {
		setLoading(true);
		setError(false);
		setUserList([]);
		try {
			const res = await getAdvisorCustomer({
				SearchWord: searchInput,
				OrderBy: filterSort,
				Regions: filterRegions,
				Offset: (pageInfo.pageIndex - 1) * pageInfo.pageSize,
				Limit: pageInfo.pageSize,
			});
			if (res.Error) {
				tips.error({ content: res.Error.Message });
				setError(true);
			} else {
				setTotal(res.Total);
				setUserList(
					res.CustomerList.map((customer) => ({
						uin: customer.Uin,
						appid: customer.AppID,
						nickname: customer.CustomerName,
						region: customer.Region,
						high: customer.HighRiskCount,
						medium: customer.MediumRiskCount,
						low: customer.LowRiskCount,
						authTime: moment(customer.OpeningTime).format('YYYY-MM-DD HH:mm:ss'),
						lastAssessTime:
							customer.LastEvaluationTime.slice(0, 4) === '1970'
								? '未进行过扫描'
								: moment(customer.LastEvaluationTime).format('YYYY-MM-DD HH:mm:ss'),
					}))
				);
			}
		} catch (err) {
			let message: string = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setError(true);
		}
		setLoading(false);
	};

	const handleDownload = () => {
		let filterVal = Object.keys(userList[0]);
		let tHeader = columns.map((column) => column.header);
		let data = formatJson(filterVal, userList);
		let filename = `Advisor用户总览${moment().format('YYYY-MM-DD')}`;
		const { export_json_to_excel } = require('@src/utils/exportExcel/Export2Excel'); //引入
		export_json_to_excel(tHeader, data, filename);
	};
	return (
		<Body>
			<Content>
				<Content.Header title="历史记录"></Content.Header>
				<Content.Body>
					<Table.ActionPanel>
						<Justify
							right={
								<>
									<SearchBox
										style={{ width: '300px' }}
										onSearch={(value) => {
											setSearchInput(value);
											setPageInfo({
												...pageInfo,
												pageIndex: 1,
											});
										}}
										onClear={() => {
											setSearchInput('');
											setPageInfo({
												...pageInfo,
												pageIndex: 1,
											});
										}}
										placeholder="输入主账号UIN或APPID或客户简称过滤"
									/>
									<Button
										icon="refresh"
										onClick={() => {
											fetch();
										}}
									/>
									<Button icon="download" onClick={handleDownload} />
								</>
							}
						/>
					</Table.ActionPanel>
					<Card>
						<Table
							verticalTop
							records={userList}
							//recordKey="appid"
							columns={columns}
							addons={[
								autotip({
									isLoading: loading,
									isError: error,
								}),
								// 对 region 列增加多选过滤支持
								filterable({
									type: 'multiple',
									column: 'region',
									value: filterRegions,
									onChange: (value) => {
										setFilterRegions(value.filter((x) => x !== 'all'));
									},
									all: {
										value: 'all',
										text: '全部',
									},
									options:
										regionList.map((item) => ({
											value: item,
											text: item,
										})) || [],
								}),
								sortable({
									columns: [
										{ key: 'high', prefer: 'desc' },
										{ key: 'medium', prefer: 'desc' },
										{ key: 'low', prefer: 'desc' },
										{ key: 'authTime', prefer: 'desc' },
										{
											key: 'lastAssessTime',
											prefer: 'desc',
										},
									],
									value: sorts,
									onChange: (value) => setSorts((value.length && [value[0]]) || value),
								}),
								pageable({
									recordCount: total,
									pageIndex: pageInfo.pageIndex,
									pageSize: pageInfo.pageSize,
									onPagingChange: (e) => {
										setPageInfo({
											pageIndex: e.pageIndex,
											pageSize: e.pageSize,
										});
									},
									pageSizeOptions: [10, 20, 50, 100, 500],
								}),
							]}
						/>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}
