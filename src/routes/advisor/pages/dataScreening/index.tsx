import React from 'react';
import {Layout, Tabs, TabPanel} from '@tencent/tea-component';
import RecommendTab from './components/RecommendTab';
import DiyTab from './components/DiyTab';
import './index.less';
import { useHistory } from "@tea/app";
import { report } from "@src/routes/advisor/pages/dataScreening/utils/report";
export default function DataScreening({match}) {
  const { Body, Content } = Layout;
  const matchTabList = ['recommend', 'diy'];
  const type = matchTabList.includes(match.params.type) ? match.params.type : matchTabList[0];
  const history = useHistory();
  const tabs = [
    { id: matchTabList[0], label: "平台推荐" },
    { id: matchTabList[1], label: "DIY报表" },
  ];
	return (
		<Body className={'data-screening-wrap'}>
			<Content.Header title={'运营数据总览'}></Content.Header>
      <Tabs tabs={tabs} activeId={type} onActive={
        (tab)=>{
          report('data-screening-click-tab', {
            tabName: tab.label
          });
          history.push(`/advisor/data-screening/${tab.id}`);
        }
      }>
        <TabPanel id="recommend">
          <RecommendTab />
        </TabPanel>
        <TabPanel id="diy">
          <DiyTab />
        </TabPanel>
      </Tabs>
			<Content.Body>
      </Content.Body>
		</Body>
	);
}
