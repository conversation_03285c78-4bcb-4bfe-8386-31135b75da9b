.data-screening-wrap {
  .data-screen-btn-wrap {
    display: flex;
    align-items: center;
    .share-btn {
      font-size: 12px;
      text-decoration: none;
    }
  }
  .tea-tabs {
    padding: 0 20px;
    //background-color: #F3F3F3;
  }
	.tea-layout__content-body {
		background-color: #F3F3F3;
	}
  .tea-justify-grid {
    .tea-input {
      padding-right: 25px;
      margin-left: 7px;
    }
    .tea-icon-search {
      margin-left: -25px;
      cursor: pointer;
    }
    .tea-icon-refresh {
      margin-left: 14px;
      cursor: pointer;
    }
    .search-select {
      width: 142px;
      margin-left: 0;
    }
  }
  .tea-card {
    margin-top: 13px;
    .loading-wrap {
      height: 250px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .report-card-wrap {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 25px;
      .report-card-item {
        width: 32%;
        box-sizing: border-box;
        border: 0.956px solid #C0C6C8;
        position: relative;
        transition: all .2s ease-in-out;
        &:hover {
          box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.25);
          transform: translateY(-3px);
        }
        a {
          padding: 18px;
          box-sizing: border-box;
          display: block;
          color: rgba(0, 0, 0, 0.9);
          text-decoration: none;
        }
        .t-wrap {
          display: flex;
          align-items: baseline;
          .tea-tag {
            margin-top: 0;
            margin-right: 10px;
            position: relative;
            top: -4px;
            span {
              white-space: nowrap;
            }
          }
          .t {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: bold;
          }
        }
        .line {
          display: flex;
          align-items: baseline;
          span {
            &:nth-child(2) {
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .tea-text-label {
            white-space: nowrap;
          }
          &:nth-child(2) {
            margin-top: 20px;
          }
          &:nth-child(3) {
            margin-top: 15px;
          }
          &:nth-child(4) {
            margin-top: 15px;
          }
        }
        .handle-area-wrap {
          position: absolute;
          right: 5px;
          top: 18px;
          svg {
            cursor: pointer;
            padding: 2px 3px;
            &:hover {
              background-color: #E6EAF0;
            }
          }
        }
      }
    }
  }
}
.data-handle-bubble-wrap {
	.tea-bubble__inner {
		padding-left: 0;
		padding-right: 0;
		li {
			padding: 0 15px;
			cursor: pointer;
			margin-bottom: 3px;
			&:hover {
				background-color: #F3F3F3;
			}
		}
	}
}
.data-editor-modal {
  .tea-dialog__inner {
    width: 570px;
  }
	.tea-dropdown.size-l .tea-dropdown__header {
		width: 400px;
	}
	.tea-input.size-l {
		width: 400px;
	}
}
@media screen and (max-width: 1266px){
	.data-screening-wrap .tea-layout__content-body .tea-card .report-card-wrap .report-card-item {
		width: 31%;
	}
}
@media screen and (max-width: 852px){
	.data-screening-wrap .tea-layout__content-body .tea-card .report-card-wrap .report-card-item {
		width: 30%;
	}
}