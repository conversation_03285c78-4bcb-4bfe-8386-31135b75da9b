import moment from "moment";
import { getStorage } from "@src/utils/storage";

export const report = (eid, remark?: Object)=>{
  const productHostList = ['isa.woa.com', 'isa-intl.woa.com'];
  if (productHostList.includes(location?.hostname ?? '')) {
    (window as any)?.isaCommonReport({
      eid,
      isaReportTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      remark: JSON.stringify(remark ?? {}),
      isaReportUserName: getStorage('engName')
    });
  }
}