import React, { useEffect, useState } from 'react';
import { Pagination, message, Justify, Button, Select, Input, Icon, Card, Tag, Text, Bubble, List, Modal, Form, StatusTip, PopConfirm, SelectMultiple } from '@tencent/tea-component';
import { useForm, useField } from 'react-final-form-hooks';
import {
  CreatePersonalOperationalReport,
  ModifyPersonalOperationalReport,
  DescribePersonalOperationalReportList,
  DeletePersonalOperationalReport,
  CreateTopPersonalOperationalReport,
} from "@src/api/advisor/dataScreening";
import { Alert } from "@tea/component";
import { report } from "@src/routes/advisor/pages/dataScreening/utils/report";

function getStatus(meta, validating) {
  if (meta.active && validating) {
    return 'validating';
  }
  if (!meta.touched) {
    return null;
  }
  return meta.error ? 'error' : 'success';
}
export default function DiyTab() {
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(false);
  const fieldErrorMsgMap = {
    Service: '请选择所属服务',
    Name: '请填写运营报表名称',
    Url: '请填写运营报表链接',
  };
  const [filterServices, setFilterServices] = useState([]);
  const [filterName, setFilterName] = useState('');
  const [limit, setLimit] = useState(12);
  const [pageSize, setPageSize] = useState(0);
  const [serviceList, setServiceList] = useState<any>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [reportList, setReportList] = useState([]);
  const [visible, setVisible] = useState(false);
  const [modifyId, setModifyId] = useState(0);
  const [onOff, setOnOff] = useState(false);
  const deleteOperationalReport = async (id, url) => {
    try {
      await DeletePersonalOperationalReport({
        Id: id,
        ShowError: true,
        OnlyData: true
      });
      message.success({
        content: '操作成功'
      });
      setOnOff(!onOff);
      report('data-screening-delete-diy-table', {
        url,
        id
      });
    } catch (err) {}
  };
  // 提交表单
  const onSubmit = async (value) => {
    const params: any = {
      ReportInfo: {
        ...value
      },
      ShowError: true,
      OnlyData: true
    };
    setLoading(true);
    try {
      if (modifyId === 0) {
        await CreatePersonalOperationalReport(params);
        report('data-screening-add-diy-table', {
          url: params.ReportInfo.Url,
          id: 0
        });
      } else {
        params.ReportInfo.Id = modifyId;
        await ModifyPersonalOperationalReport(params);
        report('data-screening-update-diy-table', {
          url: params.ReportInfo.Url,
          id: modifyId
        });
      }
      message.success({
        content: '操作成功'
      });
      setOnOff(!onOff);
      setLoading(false);
      setVisible(false);
    } catch (err) {
      setLoading(false);
    }
  };
  const { form, handleSubmit, validating} = useForm<any>({
    onSubmit: value => onSubmit(value),
    initialValuesEqual: () => true,
    validate: (formInfo) => {
      const validateFormInfo = {};
      for (const key in fieldErrorMsgMap) {
        if ((key === 'Url') && formInfo[key] && !/\b(?:http:\/\/|https:\/\/)?[a-zA-Z0-9.-]+\.woa\.com\b/.test(formInfo[key])) {
          validateFormInfo[key] = '仅支持绑定“*.woa.com”类型的域名';
        } else {
          validateFormInfo[key] = !formInfo[key] ? fieldErrorMsgMap[key] : undefined;
        }
      }
      return validateFormInfo;
    },
  });
  const service = useField('Service', form);
  const name = useField('Name', form);
  const url = useField('Url', form);
  const desc = useField('Desc', form);
  const getDescribeOperationalReportList = async () => {
    setPageLoading(true);
    try {
      const res = await DescribePersonalOperationalReportList({
        Services: filterServices,
        Name: filterName || '',
        Limit: limit,
        Offset: pageSize * limit,
        ShowError: true,
        OnlyData: true
      });
      setServiceList(res.ServiceList);
      setTotalCount(res.TotalCount || 0);
      setReportList(res.ReportList);
      setPageLoading(false);
    } catch (err) {
      setPageLoading(false);
    }
  };
  const resetFields = () => {
    setModifyId(0);
    setTimeout(()=>{
      form.resetFieldState('Service');
      form.resetFieldState('Name');
      form.resetFieldState('Url');
      form.resetFieldState('Desc');
      form.reset();
    }, 0);
  };
  const createTopOperationalReport = async (id, url) => {
    try {
      await CreateTopPersonalOperationalReport({
        Id: id,
        ShowError: true,
        OnlyData: true
      });
      message.success({
        content: '置顶成功'
      });
      setOnOff(!onOff);
      report('data-screening-top-diy-table', {
        url,
        id
      });
    } catch (err) {}
  };
  useEffect(()=>{
    if (visible === false) {
      resetFields();
    }
  }, [visible]);
  useEffect(()=>{
    setTimeout(()=>{
      getDescribeOperationalReportList();
    }, 0);
  }, [filterServices, pageSize, limit, onOff]);
  return (
    <>
      <Alert>
        当前报表为DIY报表，仅支持绑定“*.woa.com”类型的域名，且个人可见。如需配置云顾问相关报表，可通过<a href={'https://beacon.woa.com/datatalk/tarc/card'} target={'_blank'}>灯塔</a>进行自助配置添加，如有灯塔使用问题，请企业微信联系“灯塔小秘(灯塔小秘)”，如有云顾问的数据集、报表字段等问题，可企业微信联系 lanlanzhang(张兰)。
      </Alert>
      <Justify
        left={
          <>
            <Button
              type="primary"
              onClick={
                ()=>{
                  setVisible(true);
                }
              }
            >新增自定义报表</Button>
          </>
        }
        right={
          <>
            <SelectMultiple
              clearable
              appearance="button"
              allOption={{
                value: "all",
                text: "全选",
              }}
              options={
                serviceList.map((item)=>{
                  return {
                    text: item,
                    value: item
                  };
                })
              }
              placeholder="按照所属服务过滤"
              onChange={
                (val)=>{
                  setPageSize(0);
                  setFilterServices(val);
                  report('data-screening-diy-filter-select');
                }
              }
              className={'search-select'}
            />
            <Input
              placeholder="按照运营报表名称进行过滤"
              onChange={
                (val)=>{
                  setFilterName(val);
                }
              }
              className={'search-input'}
            />
            <Icon
              type="search"
              onClick={
                ()=>{
                  setPageSize(0);
                  setOnOff(!onOff);
                  report('data-screening-diy-filter-click-btn');
                }
              }
            />
            <Icon
              type="refresh"
              onClick={
                ()=>{
                  setOnOff(!onOff);
                  report('data-screening-diy-filter-refresh');
                }
              }
            />
          </>
        }
      />
      <Card>
        <Card.Body>
          {
            (pageLoading || reportList?.length === 0)
              ?
              <div className="loading-wrap">
                <StatusTip status={pageLoading ? 'loading' : 'empty'}/>
              </div>
              :
              <div className="report-card-wrap">
                {
                  reportList?.map((item, i)=>{
                    return <div className="report-card-item" key={i}>
                      <a href={item.Url} target={'_blank'} onClick={
                        ()=>{
                          report('data-screening-see-diy-table', {
                            id: item.Id,
                            url: item.Url
                          });
                        }
                      }>
                        <div className="t-wrap">
                          <Tag theme="primary">
                            {
                              item.Service
                            }
                          </Tag>
                          <span className="t" title={item.Name}>
														{
                              item.Name
                            }
													</span>
                        </div>
                        <div className="line">
                          <Text theme={'label'}>报表描述：</Text>
                          <Text title={item.Desc}>
                            {
                              item.Desc
                            }
                          </Text>
                        </div>
                        <div className="line">
                          <Text theme={'label'}>更新时间：</Text>
                          <Text theme={'label'}>
                            {
                              item.UpdateTime
                            }
                          </Text>
                        </div>
                        <div className="handle-area-wrap">
                          <Bubble content={'置顶'}>
                            <PopConfirm
                              title="确认要置顶当前报表？"
                              message="置顶后将无法撤销"
                              footer={close => (
                                <>
                                  <Button
                                    type="link"
                                    onClick={(e) => {
                                      createTopOperationalReport(item.Id, item.Url);
                                      e.stopPropagation();
                                    }}
                                  >
                                    确认
                                  </Button>
                                  <Button
                                    type="text"
                                    onClick={(e) => {
                                      close();
                                      e.stopPropagation();
                                    }}
                                  >
                                    取消
                                  </Button>
                                </>
                              )}
                              placement="top-start"
                            >
                              <svg
                                onClick={
                                  (e)=>{
                                    e.stopPropagation();
                                    e.preventDefault();
                                  }
                                }
                                width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g id="Frame">
                                  <path id="Vector" d="M8.00278 4.70016V14" stroke="#32394B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                  <path id="Vector_2" d="M4 8.66667L8 4.66667L12 8.66667" stroke="#32394B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                  <path id="Vector_3" d="M4 2H12" stroke="#32394B" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </g>
                              </svg>
                            </PopConfirm>
                          </Bubble>
                          <Bubble
                            trigger={'click'}
                            className={'data-handle-bubble-wrap'}
                            placement="left-start"
                            content={
                              <List>
                                <List.Item
                                  onClick={
                                    (e)=>{
                                      form.change('Service', item.Service);
                                      form.change('Url', item.Url);
                                      form.change('Name', item.Name);
                                      form.change('Desc', item.Desc);
                                      setModifyId(item.Id);
                                      setVisible(true);
                                      e.stopPropagation();
                                    }
                                  }
                                >
                                  编辑
                                </List.Item>
                                <List.Item
                                  onClick={
                                    (e)=>{
                                      e.stopPropagation();
                                    }
                                  }
                                >
                                  <PopConfirm
                                    title="确认删除当前运营数据？"
                                    message="删除后不可恢复"
                                    footer={close => (
                                      <>
                                        <Button
                                          type="link"
                                          onClick={(e) => {
                                            deleteOperationalReport(item.Id, item.Url);
                                            e.stopPropagation();
                                          }}
                                        >
                                          确认
                                        </Button>
                                        <Button
                                          type="text"
                                          onClick={(e) => {
                                            close();
                                            e.stopPropagation();
                                          }}
                                        >
                                          取消
                                        </Button>
                                      </>
                                    )}
                                    placement="top-start"
                                  >
                                    删除
                                  </PopConfirm>
                                </List.Item>
                              </List>
                            }
                          >
                            <svg
                              onClick={
                                (e)=>{
                                  e.stopPropagation();
                                  e.preventDefault();
                                }
                              }
                              xmlns="http://www.w3.org/2000/svg"  width="16"  height="16"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round">  <circle cx="12" cy="12" r="1" />  <circle cx="12" cy="5" r="1" />  <circle cx="12" cy="19" r="1" /></svg>
                          </Bubble>
                        </div>
                      </a>
                    </div>;
                  })
                }
              </div>
          }
          <div className="pagi-wrap">
            <Pagination
              recordCount={totalCount}
              pageIndex={pageSize + 1}
              pageSize={limit}
              pageSizeOptions={[12, 24, 48, 96]}
              onPagingChange={
                ({pageIndex, pageSize})=>{
                  setPageSize(pageIndex - 1);
                  setLimit(pageSize);
                }
              }
            />
          </div>
        </Card.Body>
      </Card>
      <Modal
        className={'data-editor-modal'}
        disableEscape
        visible={visible}
        caption={modifyId === 0 ? "新增运营报表" : "编辑运营报表"}
        onClose={
          () => {
            setVisible(false);
          }
        }
      >
        <form onSubmit={handleSubmit}>
          <Modal.Body>
            <Form>
              <Form.Item
                required={true}
                label={'所属服务'}
                status={getStatus(service.meta, validating)}
                message={(getStatus(service.meta, validating) === 'error' && service.meta.error)}
              >
                <Select
                  {...service.input}
                  appearance={'button'}
                  options={
                    serviceList.map((item)=>{
                      return {
                        text: item,
                        value: item
                      };
                    })
                  }
                  size={'l'}
                  placeholder={fieldErrorMsgMap['Service']}
                  listWidth={400}
                />
              </Form.Item>
              <Form.Item
                required={true}
                status={getStatus(name.meta, validating)}
                message={(getStatus(name.meta, validating) === 'error' && name.meta.error)}
                label={'运营报表名称'}
              >
                <Input
                  placeholder={fieldErrorMsgMap['Name']}
                  {...name.input}
                  size={'l'}
                />
              </Form.Item>
              <Form.Item
                required={true}
                label={'运营报表链接'}
                status={getStatus(url.meta, validating)}
                message={(getStatus(url.meta, validating) === 'error' && url.meta.error)}
                tips={'仅支持绑定“*.woa.com”类型的域名'}
              >
                <Input
                  placeholder={'请填写*.woa.com类型的域名'}
                  {...url.input}
                  size={'l'}
                />
              </Form.Item>
              <Form.Item
                label={'运营报表描述'}
              >
                <Input
                  {...desc.input}
                  size={'l'}
                  placeholder={'请填写运营报表描述'}
                />
              </Form.Item>
            </Form>
          </Modal.Body>
          <Modal.Footer>
            <Button
              type="primary"
              htmlType={'submit'}
              loading={loading}
            >
              保存
            </Button>
            <Button type="weak" onClick={
              (e) => {
                setVisible(false);
                e.stopPropagation();
                e.preventDefault();
              }
            }>
              取消
            </Button>
          </Modal.Footer>
        </form>
      </Modal>
    </>
  );
}
