import React, { useState, useEffect, useContext, useMemo } from 'react';
import { useHistory } from '@tea/app';
import { app } from '@tencent/tea-app';
import moment from 'moment';
import { DatePicker, Form } from "@tencent/tea-component";
const { RangePicker } = DatePicker;
import { message } from '@tencent/tea-component';
import { Layout } from '@tea/component/layout';
import { Bubble } from '@tea/component/bubble';
import { Icon } from '@tea/component/icon';
import { Input, Table, Card, Button, Modal, message as tips } from '@tencent/tea-component';
import { Row, Col } from "@tencent/tea-component";
import { Text } from "@tencent/tea-component";
import { insertCSS } from '@src/utils/insertCSS';
import { NotPermission } from '@src/routes/NotPermission';
import { getAllFailureItems, addFailureItem, deleteFailureItem, modifyFailureItem, getFailureSummary } from '@src/api/advisor/failure';
import { } from '@src/api/advisor/failure';
import _ from 'lodash';

insertCSS(
	'AdvisorLable',
	`
.app-advisor-layout__content-body-inner {
	max-width: 100% !important;
	}

`
);
const { Body, Content } = Layout;
const { autotip, pageable, scrollable, filterable } = Table.addons

export function FailureSummary({ match }) {
	//页面权限状态
	const history1 = useHistory();
	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history1.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
		// 临时测试
		setPermission(1);
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	const [startTime, setStartTime] = useState(moment().subtract(6, "d").startOf("d").format("YYYY-MM-DD"))
	const [endTime, setEndTime] = useState(moment().endOf("d").format("YYYY-MM-DD"))
	// const [currentTime, setCurrentTime] = useState(moment().endOf("minutes").format("YYYY-MM-DD HH:mm"))
	const [receiver, setReceiver] = useState(localStorage.getItem('engName'))
	const [product, setProduct] = useState('')
	// const [AppId, setAppId] = useState('')
	const [queryFailureName, setQueryFailureName] = useState('')
	const [queryFlag, setQueryFlag] = useState(true)
	const [addFailureFlag, setAddFailureFlag] = useState(false)

	const [deleteFailureVisiable, setDeleteFailureVisiable] = useState(false)
	const [modifyFailureVisiable, setModifyFailureVisiable] = useState(false)

	// 录故障单内容字段
	const [failureName, setFailureName] = useState('')
	const [failureOccurTime, setFailureOccurTime] = useState(moment().endOf("minutes").format("YYYY-MM-DD HH:mm"))
	const [failureManager, setFailureManager] = useState('')
	const [expertSupporter, setExpertSupporter] = useState('')
	const [opsSupporter, setOpsSupporter] = useState('')
	const [productSupporter, setProductSupporter] = useState('')

	// 告警明细内容
	const [failureDetails, setFailureDetails] = useState([])

	// 数据加载状态
	const [isLoading, setIsLoading] = useState(true);
	// 数据加载时显示的内容
	const [loadingMsg, setLoadingMsg] = useState('');

	// 页码
	const [failureDetailsPageIndex, setFailureDetailsPageIndex] = useState(1);

	//拉取故障列表数据
	const loadFailureSummary = async () => {
		setIsLoading(true)
		setLoadingMsg("数据加载中...")
		setFailureDetails([]);
		try {
			/*
			let tmpAppId = 0;
			if (AppId === '') {
				tmpAppId = 0;
			} else {
				tmpAppId = parseInt(AppId);
			}*/

			const res = await getFailureSummary({
				FaultId: Number(match.params.faultid),
				Limit: 20,
				Offset: 0,
			});
			if (res.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: res.Error.Message });
				}
				setLoadingMsg(res.Error.Message)
				return;
			} else {
				// console.log(res)
				if (res.FaultList) {
					setFailureDetails(res.FaultList);
				} else {
					setFailureDetails([]);
				}
			}
			setIsLoading(false);
		} catch (err) {
			const message = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setLoadingMsg(message);
		}
	}

	useEffect(() => {
		loadFailureSummary();
		// setFailureDetailsPageIndex(1);
	}, []);

	//拉取故障列表数据
	const getFailureDetails = async () => {
		setIsLoading(true)
		setLoadingMsg("数据加载中...")
		setFailureDetails([]);
		try {
			/*
			let tmpAppId = 0;
			if (AppId === '') {
				tmpAppId = 0;
			} else {
				tmpAppId = parseInt(AppId);
			}*/

			const res = await getAllFailureItems({});
			if (res.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: res.Error.Message });
				}
				setLoadingMsg(res.Error.Message)
				return;
			} else {
				// console.log(res)
				if (res.FaultList) {
					setFailureDetails(res.FaultList);
				} else {
					setFailureDetails([]);
				}
			}
			setIsLoading(false);
		} catch (err) {
			const message = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setLoadingMsg(message);
		}
	};

	useEffect(() => {
		getFailureDetails();
		setFailureDetailsPageIndex(1);
	}, [queryFlag]);

	//新增故障条目
	const addFailureRecord = async () => {
		try {
			const res = await addFailureItem({
				Name: failureName,
				FaultManager: failureManager,
				ExpertSupporter: expertSupporter,
				OpsSupporter: opsSupporter,
				ProductSupporter: productSupporter,
				FaultTime: failureOccurTime,
			});

			if (res.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: "操作失败：" + res.Error.Message });
				}
				return;
			} else {
				// console.log(res)
				if (res.FaultId) {
					setAddFailureFlag(false);
					message.success({ content: "提示：新增故障成功！" });

					setQueryFlag(!queryFlag);
				} else {
					message.error({ content: "操作失败：新增故障失败！" });
				}
			}
		} catch (err) {
			const message = err.msg || err.toString() || 'unknown error';
			tips.error({ content: "操作失败：" + message });
		}
	};

	//删除故障条目
	const deleteFailureRecord = async (faultId) => {
		try {
			const res = await deleteFailureItem({
				Ids: [faultId]
			});

			if (res.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: "操作失败：" + res.Error.Message });
				}
				return;
			} else {
				// console.log(res)
				if (res.FaultId) {
					setDeleteFailureVisiable(false);
					message.success({ content: "提示：删除成功！" });

					setQueryFlag(!queryFlag);
				} else {
					message.error({ content: "操作失败：删除失败！" });
				}
			}
		} catch (err) {
			const message = err.msg || err.toString() || 'unknown error';
			tips.error({ content: "操作失败：" + message });
		}
	};

	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
			<Content className="intlc-survey-content intlc-stack-fullscreen intlc-stack-has-min-width">
				<Content.Header title="故障详情"></Content.Header>
				<Content.Body>
					{isLoading ? (<div><br /><Card><Card.Body title={loadingMsg}></Card.Body></Card></div>) : (
						<div>
							<Card>
								<Card.Body
									title={
										<>
											<div>
												<div>
													<Text style={{ fontSize: 20 }} align="center" parent="div">故障名称：测试故障</Text>
												</div>
												<div>
													<Text style={{ fontSize: 20 }} align="center" parent="div">故障时长：xx 小时 xx 分 xx 秒</Text>
												</div>
												<hr />
												<section>
													<Row>
														<Col span={8}>
															<div>
																<Text style={{ fontSize: 18 }}>影响客户数：12</Text>
															</div>
															<div>
																<Text style={{ fontSize: 18 }}>影响战略客户数：</Text>
															</div>
															<div>
																<Text style={{ fontSize: 18 }}>影响 KA 客户数：</Text>
															</div>
														</Col>
														<Col span={8}>
															<div>
																<span style={{ fontSize: 18 }}>影响产品数：</span>
															</div>
															<div>
																<span style={{ fontSize: 18 }}>影响产品实例数：</span>
															</div>
															<div>
																<span style={{ fontSize: 18 }}>已恢复实例数：</span>
															</div>
														</Col>
														<Col span={8}>
															<div>
																<span style={{ fontSize: 18 }}>预计恢复时长：</span>
															</div>
															<div>
																<span style={{ fontSize: 18 }}>当前措施：</span>
															</div>
															<div>
																<span style={{ fontSize: 18 }}>对外同步话术：</span>
															</div>
														</Col>
													</Row>
												</section>
												<Form style={{ marginTop: 20 }} layout={"inline"}>
													<Button
														onClick={(e) => {
															setAddFailureFlag(true);
														}}
													>
														录入故障
													</Button>
													<Modal visible={addFailureFlag} onClose={() => setAddFailureFlag(false)} caption="录入故障">
														<Modal.Body>
															<div>
																<Form style={{ marginTop: 20 }} layout={"default"}>
																	<Form.Item label="故障名称" required={true}>
																		<Input
																			size='m'
																			value={failureName}
																			onChange={(value, context) => {
																				setFailureName(value);
																			}}
																			placeholder="请输入新的故障名称"
																		/>
																	</Form.Item>
																	<Form.Item label="故障发生时间" required={true}>
																		<DatePicker
																			format="YYYY-MM-DD HH:mm"
																			showTime={{ format: "HH:mm", minuteStep: 1 }}
																			defaultValue={moment(failureOccurTime)}
																			onChange={value =>
																				// console.log(value.format())
																				setFailureOccurTime(value.format("YYYY-MM-DD HH:mm"))
																			}
																		/>
																	</Form.Item>
																	<Form.Item label="故障经理">
																		<Input
																			size='m'
																			value={failureManager}
																			onChange={(value, context) => {
																				setFailureManager(value);
																			}}
																			placeholder="请输入故障经理企业微信英文名"
																		/>
																	</Form.Item>
																	<Form.Item label="专项接口人">
																		<Input
																			size='m'
																			value={expertSupporter}
																			onChange={(value, context) => {
																				setExpertSupporter(value);
																			}}
																			placeholder="请输入专项接口人企业微信英文名"
																		/>
																	</Form.Item>
																	<Form.Item label="运维接口人">
																		<Input
																			size='m'
																			value={opsSupporter}
																			onChange={(value, context) => {
																				setOpsSupporter(value);
																			}}
																			placeholder="请输入运维接口人企业微信英文名"
																		/>
																	</Form.Item>
																	<Form.Item label="产品接口人">
																		<Input
																			size='m'
																			value={productSupporter}
																			onChange={(value, context) => {
																				setProductSupporter(value);
																			}}
																			placeholder="请输入产品接口人企业微信英文名"
																		/>
																	</Form.Item>
																</Form>
															</div>
														</Modal.Body>
														<Modal.Footer>
															<Button
																type="primary"
																onClick={(e) => {
																	// setAddFailureFlag(false);
																	addFailureRecord()
																}}
															>
																确定
															</Button>
															<Button
																onClick={(e) => {
																	setAddFailureFlag(false);
																}}
															>
																取消
															</Button>
														</Modal.Footer>
													</Modal>
												</Form>
											</div>
										</>
									}
								>
								</Card.Body>
							</Card>

							<br />

							<Card>
								<Card.Body>
									<section>
										<Row>
											<Col span={6}>
												<div>
													<Text style={{ fontSize: 18 }}>故障经理：12</Text>
												</div>
											</Col>
											<Col span={6}>
												<div>
													<span style={{ fontSize: 18 }}>专项接口人：</span>
												</div>
											</Col>
											<Col span={6}>
												<div>
													<span style={{ fontSize: 18 }}>运维接口人：</span>
												</div>
											</Col>
											<Col span={6}>
												<div>
													<span style={{ fontSize: 18 }}>产品接口人：</span>
												</div>
											</Col>
										</Row>
									</section>
								</Card.Body>
							</Card>

							<br />

							<section>
								<Row>
									<Col span={8}>
										<Card>
											<Card.Body title={'重点客户影响详情'}>
												<Table
													records={failureDetails}
													columns={[
														{
															key: 'Name',
															header: '故障名称'
														},
														{
															key: 'FaultManager',
															header: '故障经理'
														},
														{
															key: 'ExpertSupporter',
															header: '专项接口人'
														},
														{
															key: 'FaultId',
															header: '故障详情',
															render: item => {
																return <><a href={`/failure/FailureManage/${item.FaultId}`}>进入</a></>
															},
														},
														{
															key: 'FaultId',
															header: '操作',
															render: item => {
																return <>
																	<Button
																		type="link"
																		onClick={(e) => { setDeleteFailureVisiable(true) }}
																		style={{ marginLeft: 10 }}
																	>
																		删除
																	</Button>
																	<Modal visible={deleteFailureVisiable} onClose={() => setDeleteFailureVisiable(false)} caption="删除故障">
																		<Modal.Body>
																			<div style={{ fontSize: 16, color: 'red' }}>
																				<b>确认是否删除故障：{item.Name}？</b>
																			</div>
																		</Modal.Body>
																		<Modal.Footer>
																			<Button
																				type="primary"
																				onClick={(e) => {
																					// setDeleteTemplateVisiable(false);
																					deleteFailureRecord(item.FaultId);
																				}}
																			>
																				确定
																			</Button>
																			<Button type="weak" onClick={() => setDeleteFailureVisiable(false)}>
																				取消
																			</Button>
																		</Modal.Footer>
																	</Modal>
																</>
															},
														}
													]}
													addons={[
														autotip({
															emptyText: '没有数据',
														}),
														pageable({
															pageIndex: failureDetailsPageIndex,
															onPagingChange: (pagingQuery) => {
																setFailureDetailsPageIndex(pagingQuery.pageIndex);
															},
															pageSize: 100,
														}),
													]}
												></Table>
											</Card.Body>
										</Card>
									</Col>
									<Col span={8}>
										<Card>
											<Card.Body title={'高优先级恢复实例'}>
												<Table
													records={failureDetails}
													columns={[
														{
															key: 'Name',
															header: '故障名称'
														},
														{
															key: 'FaultManager',
															header: '故障经理'
														},
														{
															key: 'ExpertSupporter',
															header: '专项接口人'
														},
														{
															key: 'FaultId',
															header: '故障详情',
															render: item => {
																return <><a href={`/failure/FailureManage/${item.FaultId}`}>进入</a></>
															},
														},
														{
															key: 'FaultId',
															header: '操作',
															render: item => {
																return <>
																	<Button
																		type="link"
																		onClick={(e) => { setDeleteFailureVisiable(true) }}
																		style={{ marginLeft: 10 }}
																	>
																		删除
																	</Button>
																	<Modal visible={deleteFailureVisiable} onClose={() => setDeleteFailureVisiable(false)} caption="删除故障">
																		<Modal.Body>
																			<div style={{ fontSize: 16, color: 'red' }}>
																				<b>确认是否删除故障：{item.Name}？</b>
																			</div>
																		</Modal.Body>
																		<Modal.Footer>
																			<Button
																				type="primary"
																				onClick={(e) => {
																					// setDeleteTemplateVisiable(false);
																					deleteFailureRecord(item.FaultId);
																				}}
																			>
																				确定
																			</Button>
																			<Button type="weak" onClick={() => setDeleteFailureVisiable(false)}>
																				取消
																			</Button>
																		</Modal.Footer>
																	</Modal>
																</>
															},
														}
													]}
													addons={[
														autotip({
															emptyText: '没有数据',
														}),
														pageable({
															pageIndex: failureDetailsPageIndex,
															onPagingChange: (pagingQuery) => {
																setFailureDetailsPageIndex(pagingQuery.pageIndex);
															},
															pageSize: 100,
														}),
													]}
												></Table>
											</Card.Body>
										</Card>
									</Col>
									<Col span={8}>
										<Card>
											<Card.Body title={'影响产品及明细'}>
												<Table
													records={failureDetails}
													columns={[
														{
															key: 'Name',
															header: '故障名称'
														},
														{
															key: 'FaultManager',
															header: '故障经理'
														},
														{
															key: 'ExpertSupporter',
															header: '专项接口人'
														},
														{
															key: 'FaultId',
															header: '故障详情',
															render: item => {
																return <><a href={`/failure/FailureManage/${item.FaultId}`}>进入</a></>
															},
														},
														{
															key: 'FaultId',
															header: '操作',
															render: item => {
																return <>
																	<Button
																		type="link"
																		onClick={(e) => { setDeleteFailureVisiable(true) }}
																		style={{ marginLeft: 10 }}
																	>
																		删除
																	</Button>
																	<Modal visible={deleteFailureVisiable} onClose={() => setDeleteFailureVisiable(false)} caption="删除故障">
																		<Modal.Body>
																			<div style={{ fontSize: 16, color: 'red' }}>
																				<b>确认是否删除故障：{item.Name}？</b>
																			</div>
																		</Modal.Body>
																		<Modal.Footer>
																			<Button
																				type="primary"
																				onClick={(e) => {
																					// setDeleteTemplateVisiable(false);
																					deleteFailureRecord(item.FaultId);
																				}}
																			>
																				确定
																			</Button>
																			<Button type="weak" onClick={() => setDeleteFailureVisiable(false)}>
																				取消
																			</Button>
																		</Modal.Footer>
																	</Modal>
																</>
															},
														}
													]}
													addons={[
														autotip({
															emptyText: '没有数据',
														}),
														pageable({
															pageIndex: failureDetailsPageIndex,
															onPagingChange: (pagingQuery) => {
																setFailureDetailsPageIndex(pagingQuery.pageIndex);
															},
															pageSize: 100,
														}),
													]}
												></Table>
											</Card.Body>
										</Card>
									</Col>
								</Row>
							</section>
						</div>)
					}
				</Content.Body>
			</Content>}</div>}
		</Body>
	);
}