import React, { useState, useEffect, useMemo } from 'react';
import { Table, Justify, Button, SearchBox, Card, Layout, Text, message, StatusTip } from '@tencent/tea-component';
import { formatJson } from '@src/utils/formatJson';
import moment from 'moment';
import { getAdvisorCustomer, listAccountScanResult, DescribeTsaAuthInfoOverview } from '@src/api/advisor/overview';
import { Link } from 'react-router-dom';
import { areaList as regionList } from '@src/utils/list';
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';
import { getStorage } from '@src/utils/storage';
import { getViewPort } from '@src/routes/architecture/utils';
import { reportVisitPage } from '@src/utils/report';
import { TabPanel, Tabs } from '@tea/component';

const { Body, Content } = Layout;
const { pageable, autotip, sortable, filterable, mergeable } = Table.addons;

const columns = [
	{
		key: 'GName',
		header: '公司名称',
	},
	{
		key: 'AppId',
		header: 'APPID',
	},
	{
		key: 'Uin',
		header: 'UIN',
	},
	{
		key: 'Owner',
		header: '负责人',
	},
	{
		key: 'RiskDescription',
		header: '账号风险项',
		render: (record) => {
			if (record.RiskDescription.length) {
				// console.log(record.RiskDescription)
				return (
					<>
						{record.RiskDescription.map((i) => {
							return (
								<div>
									<span>{i}</span>
								</div>
							);
						})}
					</>
				);
			} else {
				return (
					<div>
						<span>{record.RiskDescription}</span>
					</div>
				);
			}
		},
	},
	{
		key: 'CreateTime',
		header: '开通时间',
	},
	{
		key: 'operation',
		header: '操作',
		render: (record) => {
			if (record.CreateTime == '未开通') {
				return (
					<>
						<p> 暂无操作 </p>
					</>
				);
			} else {
				return (
					<>
						<p>
							<Link to={`/advisor/assess/result/${record.AppId}`} target="_blank">
								查看服务报告
							</Link>
						</p>
					</>
				);
			}
		},
	},
];
let timer;
export function OverviewIndex() {
	const history = useHistory();
	// history.push('/advisor/data-screening');
	// 用户列表
	const [userList, setUserList] = useState([]);
	// 列表项总数
	const [total, setTotal] = useState(0);

	const [loading, setLoading] = useState(false);

	const [error, setError] = useState(false);
	// 搜索输入
	const [searchInput, setSearchInput] = useState('');
	// 当前排序列
	const [sorts, setSorts] = useState([]);
	// 所选地域
	const [filterRegions, setFilterRegions] = useState([]);
	// 分页
	const [pageInfo, setPageInfo] = useState({
		pageIndex: 1,
		pageSize: 10,
	});
	// 合并信息
	const [combinePos, setCombinePos] = useState([]);

	//页面权限状态
	const [permission, setPermission] = useState(0); //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem('menuItems'));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter((i) => {
				if (i.key == 'overview') {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	};
	//持续从localStorage获取菜单列表
	useEffect(() => {
		timer = setInterval(() => {
			CheckPermission();
		}, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	const getAllAccountScanResult = async () => {
		setLoading(true);
		setError(false);
		setUserList([]);
		try {
			const res = await listAccountScanResult({
				SearchWord: searchInput,
				Offset: (pageInfo.pageIndex - 1) * pageInfo.pageSize,
				Limit: pageInfo.pageSize,
			});
			if (res.Error) {
				let msg = res.Error.Message;
				message.error({ content: msg });
				setError(true);
			} else {
				// 成功
				setTotal(res.TotalCount);
				if (res.AccountInfo) {
					setUserList(res.AccountInfo);

					// 根据结果生成需要合并的位置
					let offset = 0;
					let pos = 0;
					let tmpPos = [];

					for (let i = 1; i < res.AccountInfo.length; i++) {
						if (res.AccountInfo[i].GName === res.AccountInfo[i - 1].GName && offset === 0) {
							pos = i - 1;
							offset++;

							if (i === res.AccountInfo.length - 1) {
								tmpPos.push({ startPos: pos, offset: offset });
							}
						} else if (res.AccountInfo[i].GName === res.AccountInfo[i - 1].GName && offset !== 0) {
							offset++;

							if (i === res.AccountInfo.length - 1) {
								tmpPos.push({ startPos: pos, offset: offset });
							}
						} else if (res.AccountInfo[i].GName !== res.AccountInfo[i - 1].GName && offset !== 0) {
							tmpPos.push({ startPos: pos, offset: offset });
							pos = 0;
							offset = 0;
						} else {
							continue;
						}
					}

					setCombinePos(tmpPos);
				} else {
					setUserList([]);
				}
				setError(false);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			setError(true);
		}
		setLoading(false);
	};

	useEffect(() => {
		console.log(1111)
		getAllAccountScanResult();
	}, [pageInfo]);

	// const [isAdmin, setIsAdmin] = useState(false);
	const [grafanaUrl, setGrafanaUrl] = useState([]);
	const [overviewLoaded, setOverviewLoaded] = useState(false);
	const getDescribeTsaAuthInfoOverview = async () => {
		setOverviewLoaded(true);
		try {
			const res = await DescribeTsaAuthInfoOverview({
				Operator: getStorage('engName'),
				ShowError: true,
				OnlyData: true,
			});
			// setIsAdmin(res.IsAdmin);
			//console.log(res);
			setGrafanaUrl(res.UrlInfoSet);
			setOverviewLoaded(false);
			if (res.IsAdmin) {
				setTabs([
					{ id: 'index', label: '运营数据总览' },
					{ id: 'self', label: '客户详情' },
				]);
				setActiveId('index');
			}
		} catch (e) {
			setOverviewLoaded(false);
		}
	};
	useMemo(() => {
		getDescribeTsaAuthInfoOverview();
		reportVisitPage({
			isaReportMeunName: '客户总览',
		});
	}, []);
	const viewPortHeight = getViewPort('height');
	const [tabs, setTabs] = useState([{ id: 'self', label: '客户详情' }]);
	const [activeId, setActiveId] = useState('self');
	const changeActive = (tab) => {
		setActiveId(tab.id);
	};
	const contentStyle = {
		paddingLeft: 0,
		paddingRight: 0,
	};
	const renderIframes = (datas) => {
		return datas.map((data) => {
			return (
				<iframe
					key={data.UrlName}
					style={{ marginTop: '-20px', marginBottom: '-34px' }}
					id="iframe_id"
					width="100%"
					height={viewPortHeight - 131}
					src={data.Url}
				></iframe>
			);
		});
	};
	return (
		<Body>
			{permission === 0 ? (
				<div></div>
			) : (
				<div>
					{permission === 2 ? (
						<NotPermission />
					) : (
						<Content>
							<Content.Header title="客户总览"></Content.Header>
							<Content.Body style={activeId == 'index' ? contentStyle : {}}>
								{overviewLoaded ? (
									<div
										style={{
											textAlign: 'center',
											paddingTop: '200px',
											backgroundColor: '#fff',
											marginBottom: '-20px',
											marginTop: '-20px',
										}}
									>
										<StatusTip status={'loading'} />
									</div>
								) : (
									<Tabs
										ceiling
										animated={false}
										tabs={tabs}
										destroyInactiveTabPanel={false}
										activeId={activeId}
										onActive={changeActive}
									>
										<TabPanel id="index">{renderIframes(grafanaUrl)}</TabPanel>
										<TabPanel id="self">
											<>
												<Table.ActionPanel>
													<Justify
														right={
															<>
																<SearchBox
																	style={{ width: '300px' }}
																	onSearch={(value) => {
																		setSearchInput(value);
																		setPageInfo({
																			...pageInfo,
																			pageIndex: 1,
																		});
																	}}
																	onClear={() => {
																		setSearchInput('');
																		setPageInfo({
																			...pageInfo,
																			pageIndex: 1,
																		});
																	}}
																	placeholder="输入主账号UIN或APPID或公司名称过滤"
																/>
																<Button
																	icon="refresh"
																	onClick={() => {
																		getAllAccountScanResult();
																	}}
																/>
															</>
														}
													/>
												</Table.ActionPanel>
												<Card>
													<Table
														verticalTop
														records={userList}
														//recordKey="appid"
														bordered="all"
														columns={columns}
														addons={[
															autotip({
																isLoading: loading,
																isError: error,
															}),
															// 对 region 列增加多选过滤支持
															filterable({
																type: 'multiple',
																column: 'region',
																value: filterRegions,
																onChange: (value) => {
																	setFilterRegions(value.filter((x) => x !== 'all'));
																},
																all: {
																	value: 'all',
																	text: '全部',
																},
																options:
																	regionList.map((item) => ({
																		value: item,
																		text: item,
																	})) || [],
															}),
															/*
													sortable({
														columns: [{ key: 'authTime', prefer: 'desc' }],
														value: sorts,
														onChange: (value) => setSorts((value.length && [value[0]]) || value),
													}),*/
															pageable({
																recordCount: total,
																pageIndex: pageInfo.pageIndex,
																pageSize: pageInfo.pageSize,
																onPagingChange: (e) => {
																	setPageInfo({
																		pageIndex: e.pageIndex,
																		pageSize: e.pageSize,
																	});
																},
																pageSizeOptions: [10, 20, 50, 100, 1000],
															}),
															mergeable({
																rowSpan: (columnIndex, recordIndex) => {
																	if (columnIndex === 0) {
																		/*
																combinePos.forEach(i => {
																	// console.log(recordIndex)
																	// console.log(i)
				
																	if (recordIndex === i.startPos) {
																		console.log((i.offset + 1))
																		return (i.offset + 1);
																	}
					
																	if (recordIndex > i.startPos && recordIndex < (i.startPos + i.offset + 1)) {
																		console.log("ss")
																		return 0;
																	}
																	// console.log("11")
																})*/
																		for (let i = 0; i < combinePos.length; i++) {
																			if (
																				recordIndex === combinePos[i].startPos
																			) {
																				// console.log((combinePos[i].offset + 1))
																				return combinePos[i].offset + 1;
																			}

																			if (
																				recordIndex > combinePos[i].startPos &&
																				recordIndex <
																					combinePos[i].startPos +
																						combinePos[i].offset +
																						1
																			) {
																				// console.log("ss")
																				return 0;
																			}
																		}
																	}
																	return 1;
																},
															}),
														]}
													/>
												</Card>
											</>
										</TabPanel>
									</Tabs>
								)}
							</Content.Body>
						</Content>
					)}
				</div>
			)}
		</Body>
	);
}
