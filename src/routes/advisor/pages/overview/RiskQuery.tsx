import React, { useState, useEffect, useMemo } from 'react';
import { Table, Justify, Button, SearchBox, Card, Layout, message as tips } from '@tencent/tea-component';
import { Modal } from '@tencent/tea-component/lib/modal';
import { Radio, Checkbox, DatePicker } from "@tencent/tea-component";
import { Form } from '@tencent/tea-component/lib/form'
import { BasicLine } from '@tencent/tea-chart/lib/basicline';
import { formatJson } from '@src/utils/formatJson';
import moment from 'moment';
import { getAdvisorCustomer } from '@src/api/advisor/overview';
import { Link } from 'react-router-dom';
import { areaList as regionList } from '@src/utils/list';
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';
import { listAdvisorRiskCount } from '@src/api/advisor/overview';
import { message } from '@tea/component/message';

const { Body, Content } = Layout;
const { RangePicker } = DatePicker;
const { pageable, autotip, sortable, filterable, scrollable } = Table.addons;

export function RiskQuery({ match }) {
	const [isDefaultStrategy, setIsDefaultStrategy] = useState("true")
	const [riskLevel, setRiskLevel] = useState(["3"])
	const [startTime, setStartTime] = useState(moment().subtract(6, "d").startOf("d").format("YYYY-MM-DD"))
	const [endTime, setEndTime] = useState(moment().endOf("d").format("YYYY-MM-DD"))
	const [queryButtonClick, setQueryButtonClick] = useState([true])
	const [loadingFlag, setLoadingFlag] = useState(false)

	const [dailyRiskCount, setDailyRiskCount] = useState([])
	const [strategyRiskCount, setStrategyRiskCount] = useState([])


	// 当前已选巡检项
	const [strategyList, setStrategyList] = useState([]);
	// 巡检项所有选项
	const [strategyOptions, setStrategyOptions] = useState([]);

	const ALL_VALUE = "__ALL__";

	let filterStrategyRiskCount = strategyRiskCount.slice();
	// 根据过滤条件显示列表
	if (strategyList.length > 0) {
		filterStrategyRiskCount = filterStrategyRiskCount.filter(
			record => strategyList.indexOf(record.title) > -1
		);
	}

	const [pageInfo, setPageInfo] = useState({
		pageIndex: 1,
		pageSize: 500,
	});

	const initDate = async () => {
		let tmp = ""

		let today = new Date(); //转换成Data();
		var y = today.getFullYear();
		var m = today.getMonth() + 1;
		var month = m < 10 ? '0' + m : m;
		var d = today.getDate();
		var day = d < 10 ? ('0' + d) : d;
		tmp = y + '-' + month + '-' + day
		setEndTime(tmp);

		let sevenDaysAgo = new Date();
		sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6);
		var y = sevenDaysAgo.getFullYear();
		var m = sevenDaysAgo.getMonth() + 1;
		var month = m < 10 ? '0' + m : m;
		var d = sevenDaysAgo.getDate();
		var day = d < 10 ? ('0' + d) : d;
		tmp = y + '-' + month + '-' + day
		setStartTime(tmp);

		// console.log(tmp)
		// console.log(endTime)
		// console.log(startTime)
	}
	/*
	useEffect(() => {
		initDate();
	}, []);*/

	const getAdvisorRiskCount = async () => {
		// console.log(endTime)
		// console.log(startTime)
		setLoadingFlag(true)

		let boolIsDefaultStrategy = true
		if (isDefaultStrategy === "true") {
			boolIsDefaultStrategy = true
		} else {
			boolIsDefaultStrategy = false
		}

		let numberRiskLevel = []
		riskLevel.forEach(i => {
			numberRiskLevel.push(parseInt(i))
		})

		try {
			const res = await listAdvisorRiskCount({
				AppId: Number(match.params.appid),
				StartDate: startTime,
				EndDate: endTime,
				IsDefaultStrategy: boolIsDefaultStrategy,
				RiskLevel: numberRiskLevel,
			});
			if (res.Error) {
				// setError(true);
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					// setPermission(2);
					tips.error({ content: res.Error.Message });
				} else {
					tips.error({ content: res.Error.Message });
				}
			} else {
				// setDailyRiskCount(res.DailyRiskCount)
				// setStrategyRiskCount(res.RiskCount)
				if (res.DailyRiskCount) {
					let totalCount = []
					res.DailyRiskCount.map((item) => {
						totalCount.push({
							"time": item.Date,
							"value": item.Value
						})
					})
					setDailyRiskCount(totalCount)
				}

				if (res.RiskCount) {
					let tmpList = []
					/*
					 * [{ title: "abc",
					 *	  detail: [{time, value}, {time, value}...]
					 * },...]
					 */
					let tmpStrategyOptions = []
					let tmpStrategyList = []

					for (var item in res.RiskCount) {
						let title = item
						let detail = []
						// console.log(Object.keys(res.RiskCount[item]).length)
						// console.log(res.RiskCount[i])
						if (res.RiskCount[item].length > 0) {
							//console.log()
							res.RiskCount[item].map((item) => {
								detail.push({
									"time": item.Date,
									"value": item.Value
								})
							})
							/*
							for (var i in res.RiskCount[item]) {
								detail.push({
									"time": i,
									"value": res.RiskCount[item][i]
								})
							}*/

						}
						tmpList = tmpList.concat({
							"title": title,
							"detail": detail
						})

						tmpStrategyList.push(title)
						tmpStrategyOptions.push({ value: title, text: title})
					}
					// console.log(tmpList)
					setStrategyRiskCount(tmpList)
					setStrategyOptions(tmpStrategyOptions)
					setStrategyList(tmpStrategyList)
				} else {
					setStrategyRiskCount([])
					setStrategyOptions([])
					setStrategyList([])
				}
			}
			// console.log(dailyRiskCount)
			// console.log(strategyRiskCount)
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			////app.tips.error({ message });
			message.error({ content: msg });
		}

		setLoadingFlag(false)
	};

	useEffect(() => {
		getAdvisorRiskCount();
	}, [isDefaultStrategy, riskLevel, startTime, endTime]);

	function genTooltip(series) {
		const [{ title, value }] = series;
		return `<span style={{ fontSize: 16 }}>${title} 风险数量：${value}</span>`;
	}

	return (
		<Body>
			<Content>
				<Content.Header title="风险趋势"></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body
							title={
								<>
									<span style={{ fontSize: 20 }}>查询条件</span>
									<hr />
									<div>
										<Form style={{ marginTop: 20 }} layout={"default"}>
											<Form.Item label="评估项：">
												<Radio.Group value={isDefaultStrategy} onChange={value => {
													setIsDefaultStrategy(value);
												}
												}>
													<Radio name="true">关注评估项</Radio>
													<Radio name="false">所有评估项</Radio>
												</Radio.Group>
											</Form.Item>
											<Form.Item label="风险级别：">
												<Checkbox.Group value={riskLevel} onChange={value => setRiskLevel(value)}>
													<Checkbox name="3">高风险</Checkbox>
													<Checkbox name="2">中风险</Checkbox>
												</Checkbox.Group>
											</Form.Item>
											<Form.Item label="时间范围：">
												<RangePicker
													defaultValue={[moment(startTime), moment(endTime)]}
													range={[
														moment()
															.subtract(30, "d")
															.startOf("d"),
														moment().endOf("d"),
													]}
													onChange={(value) => {
														var startTimeStamp = new Date(Date.parse(value[0].format("YYYY-MM-DD").replace(/-/g, "/"))).getTime();
														var endTimeStamp = new Date(Date.parse(value[1].format("YYYY-MM-DD").replace(/-/g, "/"))).getTime();
														var dates = Math.abs((startTimeStamp - endTimeStamp)) / (1000 * 60 * 60 * 24);
														if (dates > 30) {
															// console.log("error");
															initDate();
														} else {
															setStartTime(value[0].format("YYYY-MM-DD"));
															setEndTime(value[1].format("YYYY-MM-DD"));
														}
														//console.log(startTime, endTime)
													}}
												// onOpenChange={open => console.log(open ? "open" : "close")}
												/>
											</Form.Item>
										</Form>
									</div>
								</>
							}
						>
						</Card.Body>
					</Card>
					<Card>
						<Card.Body
							title={
								<>
									<span style={{ fontSize: 20 }}>{match.params.appid} 评估项风险总量</span>
									<hr />
									<>
										{
											[1].map((item) => {
												if (!loadingFlag) {
													return <div>
														<Card>
															<Card.Body title="汇总">
																<BasicLine
																	smooth
																	height={250}
																	position="time*value"
																	dataSource={dailyRiskCount}
																	tooltip={{ formatter: genTooltip }}
																/>
															</Card.Body>
														</Card>
													</div>
												} else {
													return <div>
														<Card>
															<Card.Body title="汇总">
																<span>数据加载中</span>
															</Card.Body>
														</Card>
													</div>
												}
											})
										}
									</>
								</>
							}
						>
						</Card.Body>
					</Card>
					<Card>
						<Card>
							<Card.Body
								title={
									<>
										<span style={{ fontSize: 20 }}>{match.params.appid} 评估项风险明细</span>
										<Table
											records={filterStrategyRiskCount}
											recordKey="title"
											columns={[
												{
													key: 'title',
													header: '评估项名称',
													width:'15vw',
												},
												{
													key: 'detail',
													header: '风险变化趋势',
													render: item => {
														// console.log(item)
														return <div>
															<BasicLine
																smooth
																height={250}
																position="time*value"
																dataSource={item.detail}
																tooltip={{ formatter: genTooltip }}
															/>

														</div>
													},
												},
											]}
											addons={[
												autotip({
													emptyText: '没有数据',
													isLoading: loadingFlag,
													// isError: error,
												}),
												scrollable({
													maxHeight: 1000,
													virtualizedOptions: {
														height: 1000,
														itemHeight: 280,
														onScrollBottom: () => console.log("bottom")
													}
												}),
												filterable({
													type: "multiple",
													column: "title",
													value: strategyList,
													onChange: value => {
														setStrategyList(value);
														// console.log(products)
													},
													all: {
														value: ALL_VALUE,
														text: "全部",
													},
													options: strategyOptions || [],
													searchable: true,
												}),
												pageable({
													// recordCount: total,
													pageIndex: pageInfo.pageIndex,
													pageSize: pageInfo.pageSize,
													onPagingChange: (e) => {
														setPageInfo({
															pageIndex: e.pageIndex,
															pageSize: e.pageSize,
														});
													},
													pageSizeOptions: [500, 1000],
												}),
												
											]}
										></Table>
									</>
								}
							>
							</Card.Body>
						</Card>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}