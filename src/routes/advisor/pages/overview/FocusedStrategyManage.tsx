import React, { useState, useEffect, useMemo } from 'react';
import { Table, Justify, Button, SearchBox, Card, Layout, message as tips } from '@tencent/tea-component';
import { Modal } from '@tencent/tea-component/lib/modal'
import { formatJson } from '@src/utils/formatJson';
import moment from 'moment';
import { getAdvisorCustomer, listAdvisorAllScanStrategies, modifyAdvisorAuthorizedUserStrategyIds } from '@src/api/advisor/overview';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { Link } from 'react-router-dom';
import { areaList as regionList } from '@src/utils/list';
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';
import { message } from '@tea/component/message';

const { Body, Content } = Layout;
const { pageable, autotip, sortable, filterable, selectable, scrollable } = Table.addons;

// 类别分类项
const GROUP_OPTS = ['安全', '可靠', '性能', '成本', '服务限制']

const TYPE_TEXT = {
	"public": "外部评估项",
	"private": "内部评估项",
};

export function FocusedStrategyManage({ match }) {
	const [strategyList, setStrategyList] = useState([])
	const [currentStrategyIdList, setCurrentStrategyIdList] = useState([])

	const [loading, setLoading] = useState(false);

	const [error, setError] = useState(false);

	const [pageInfo, setPageInfo] = useState({
		pageIndex: 1,
		pageSize: 500,
	});

	// 产品筛选值
	const [products, setProducts] = useState([]);
	// 产品所有选项
	const [productsOptions, setProductsOptions] = useState([]);
	// 维度筛选值
	const [groups, setGroups] = useState([]);
	// 内外部评估项筛选值
	const [env, setEnv] = useState([]);

	const [confirmInfoVisible, setConfirmInfoVisible] = useState(false);

	const [refreshPageFlag, setRefreshPageFlag] = useState(true);

	const ALL_VALUE = "__ALL__";

	let filterStrategyList = strategyList.slice();
	// 根据过滤条件显示列表
	if (products.length > 0) {
		filterStrategyList = filterStrategyList.filter(
			record => products.indexOf(record.ProductName) > -1
		);
	}

	if (groups.length > 0) {
		filterStrategyList = filterStrategyList.filter(
			record => groups.indexOf(record.GroupName) > -1
		);
	}

	if (env.length > 0) {
		filterStrategyList = filterStrategyList.filter(
			record => env.indexOf(record.Env) > -1
		);
	}

	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: Number(match.params.appid),
				Env: 'all',
			})
			if (res.Error) {
				let msg = res.Error.Message
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });
				return
			} else {
				let tmpProductsOptions = []
				let tmpProductsList = []
				for (var i in res.ProductDict) {
					tmpProductsOptions.push({ value: res.ProductDict[i], text: res.ProductDict[i] })
					tmpProductsList.push(res.ProductDict[i])
				}
				setProductsOptions(tmpProductsOptions)// 生成所有产品下拉框
				setProducts(tmpProductsList)//初始化当前已选产品为所有

				setGroups(GROUP_OPTS)//初始化当前已选类别为所有
				setEnv(['public', 'private'])//初始化当前已选类型为所有
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });
		}
	}

	const getAllStrategiesData = async () => {
		try {
			const allStrategiesData = await listAdvisorAllScanStrategies({
				Env: "all",
			});
			// console.log(allStrategiesData);
			setStrategyList(allStrategiesData.Strategy);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			////app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	const getAdvisorFocusStrategiesData = async () => {
		setLoading(true);
		setError(false);
		setCurrentStrategyIdList([]);

		try {
			const res = await getAdvisorCustomer({
				SearchWord: match.params.appid,
			});
			// console.log(res)
			if (res.Error) {
				setError(true);
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					// setPermission(2);
					message.error({ content: res.Error.Message });
				} else {
					message.error({ content: res.Error.Message });
				}
			} else {
				// setTotal(res.Total);
				for (var i in res.CustomerList) {
					if (res.CustomerList[i].AppID === match.params.appid) {
						// console.log(res.CustomerList[i].FocusedStrategyId)
						if (res.CustomerList[i].FocusedStrategyId) {
							let tmp = []
							res.CustomerList[i].FocusedStrategyId.forEach(i => {
								tmp.push(i.toString())
							})
							setCurrentStrategyIdList(tmp)
						} else {
							let tmp = []
							// console.log("test")
							// console.log(strategyList)
							strategyList.forEach(i => {
								tmp.push(i.ID.toString())
							})
							setCurrentStrategyIdList(tmp)
						}
						// console.log(currentStrategyIdList)
					}
				}
			}
		} catch (err) {
			let msg: string = err.msg || err.toString() || 'unknown error';
			message.error({ content: msg });
			setError(true);
		}

		setLoading(false);
	};

	useEffect(() => {
		getAllStrategiesData();
		getProductsGroupsInfo();
	}, []);


	useEffect(() => {
		// console.log(strategyList)
		getAdvisorFocusStrategiesData();
	}, [strategyList, refreshPageFlag]);

	const modifyFocusStrategyIds = async () => {
		try {
			let tmp = []
			if (strategyList.length !== currentStrategyIdList.length) {
				currentStrategyIdList.forEach(i => {
					tmp.push(parseInt(i))
				})
			}
			const res = await modifyAdvisorAuthorizedUserStrategyIds({
				AppId: Number(match.params.appid),
				StrategyIds: tmp,
			});
			// console.log(res);
			if (res.Status === "success") {
				message.success({ content: "修改成功,自动刷新页面" })
				setRefreshPageFlag(!refreshPageFlag)
			} else {
				message.error({ content: "修改失败" })
			}
			// console.log(Number(match.params.appid));
			// console.log(currentStrategyIdList);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			////app.tips.error({ message });
			message.error({ content: msg });
		}
	}

	return (
		<Body>
			<Content>
				<Content.Header title="自定义关注评估项"></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body
							title={
								<>
									<span style={{ fontSize: 20 }}>{match.params.appid} 评估项列表</span>
									<Table
										records={filterStrategyList}
										recordKey="ID"
										columns={[
											{
												key: 'Name',
												header: '评估项名称',
												width: '20vw',
											},
											{
												key: 'ProductName',
												header: '云产品',
												width: '10vw',
											},
											{
												key: 'GroupName',
												header: '类别',
												width: '7vw',
											},
											{
												key: 'Desc',
												header: '描述',
											},
											/*
											{
												key: 'Repair',
												header: '建议',
											},
											{
												key: 'Notice',
												header: '关注',
											},
											{
												key: 'Ignore',
												header: '忽略',
											},
											{
												key: 'Online',
												header: 'Online',
											},*/
											{
												key: 'Env',
												header: '类型',
												render: x => TYPE_TEXT[x.Env],
												width: '5vw',
											},
										]}
										addons={[
											autotip({
												emptyText: '没有数据',
												isLoading: loading,
												isError: error,
											}),
											scrollable({ maxHeight: 630 }),
											selectable({
												// 选框放在「消息类型」列上
												targetColumnKey: "Name",
												// 提供子孙关系
												// relations,
												// 禁用全选
												all: true,
												// 已选中的键值
												value: currentStrategyIdList,
												// 选中键值发生变更
												onChange: value => {
													setCurrentStrategyIdList(value);
													// console.log(currentStrategyIdList);
												}
											}),
											/*
											// 支持表格排序
											sortable({
												// 这两列支持排序，其中 Product 列优先倒序
												columns: [{ key: "ProductName", prefer: "desc" }, "Name" ],
												value: sorts,
												onChange: value => setSorts(value),
											}),*/
											// 对 ProductName 列增加多选过滤支持
											filterable({
												type: "multiple",
												column: "ProductName",
												value: products,
												onChange: value => {
													setProducts(value);
													// console.log(products)
												},
												all: {
													value: ALL_VALUE,
													text: "全部",
												},
												options: productsOptions || [],
												searchable: true,
											}),
											// 对 GroupName 列增加多选过滤支持
											filterable({
												type: "multiple",
												column: "GroupName",
												value: groups,
												onChange: value => {
													setGroups(value);
													// console.log(groups)
												},
												all: {
													value: ALL_VALUE,
													text: "全部",
												},
												options: GROUP_OPTS.map((item) => ({
													value: item,
													text: item
												})) || [],
												searchable: true,
											}),
											// 对 Env 列增加多选过滤支持
											filterable({
												type: "multiple",
												column: "Env",
												value: env,
												onChange: value => {
													setEnv(value);
													// console.log(Env)
												},
												all: {
													value: ALL_VALUE,
													text: "全部",
												},
												options: [
													{ value: "public", text: "外部评估项" },
													{ value: "private", text: "内部评估项" },
												],
												searchable: true,
											}),
											pageable({
												// recordCount: total,
												pageIndex: pageInfo.pageIndex,
												pageSize: pageInfo.pageSize,
												onPagingChange: (e) => {
													setPageInfo({
														pageIndex: e.pageIndex,
														pageSize: e.pageSize,
													});
												},
												pageSizeOptions: [500, 1000],
											}),
										]}
									></Table>
									<br/>
									<Button type="primary"
										onClick={(e) => {
											setConfirmInfoVisible(true)
										}}
									>
										设置关注评估项
									</Button>
									<Modal visible={confirmInfoVisible} onClose={() => setConfirmInfoVisible(false)} caption="设置关注评估项">
										<Modal.Body>
											<span>请确认是否将已勾选的评估项设置为关注评估项！</span>
										</Modal.Body>
										<Modal.Footer>
											<Button
												type="primary"
												onClick={(e) => {
													modifyFocusStrategyIds();
													setConfirmInfoVisible(false);
												}}
											>
												确定
											</Button>
											<Button type="weak" onClick={() => setConfirmInfoVisible(false)}>
												取消
											</Button>
										</Modal.Footer>
									</Modal>
								</>
							}
						>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}