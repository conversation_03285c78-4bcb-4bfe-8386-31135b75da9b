import React, { useState, useEffect, useMemo } from 'react';
import { Table, Justify, Button, SearchBox, Card, Layout, message as tips } from '@tencent/tea-component';
import { formatJson } from '@src/utils/formatJson';
import moment from 'moment';
import { getAdvisorCustomer } from '@src/api/advisor/overview';
import { Link } from 'react-router-dom';
import { areaList as regionList } from '@src/utils/list';
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';

const { Body, Content } = Layout;
const { pageable, autotip, sortable, filterable } = Table.addons;

const columns = [
	{
		key: 'uin',
		header: '主账号UIN',
		render: (record) => <Link to={`/advisor/assess/result/${record.appid}`}>{record.uin}</Link>,
	},
	{
		key: 'appid',
		header: 'APPID',
	},
	{
		key: 'nickname',
		header: '客户简称',
	},
	{
		key: 'markname',
		header: '备注名称',
	},
	{
		key: 'salesSupportor',
		header: '售后Owner',
	},
	{
		key: 'responser1',
		header: '第一负责人',
	},
	{
		key: 'responser2',
		header: '第二负责人',
	},
	{
		key: 'businessManager',
		header: '商务经理',
	},
	{
		key: 'clientManager',
		header: '架构师',
	},
	{
		key: 'salesGrade',
		header: '售后等级',
	},
	{
		key: 'region',
		header: '所属地域',
	},
	{
		key: 'authTime',
		header: '开通时间',
	},
	/*
	{
		key: 'lastAssessTime',
		header: '最后扫描时间',
	},*/
	{
		key: 'operation',
		header: '操作',
		render: record => (
			<>
				<p>
					<Link to={`/advisor/overview/focused-strategy-manage/${record.appid}`} target="_blank">定义关注巡检项</Link>
				</p>
				<p>
					<Link to={`/advisor/overview/risk-query/${record.appid}`} target="_blank">查看风险趋势图</Link>
				</p>
				<p>
					<Link to={`/advisor/dashboard/result/${record.appid}`} target="_blank">查看产品信息大盘</Link>
				</p>
			</>
		),
	}
];

export function Overview() {
	// 用户列表
	const [userList, setUserList] = useState([]);
	// 列表项总数
	const [total, setTotal] = useState(0);

	const [loading, setLoading] = useState(false);

	const [error, setError] = useState(false);
	// 搜索输入
	const [searchInput, setSearchInput] = useState('');
	// 当前排序列
	const [sorts, setSorts] = useState([]);
	// 所选地域
	const [filterRegions, setFilterRegions] = useState([]);
	// 分页
	const [pageInfo, setPageInfo] = useState({
		pageIndex: 1,
		pageSize: 10,
	});
	//页面权限状态
	const history = useHistory();
	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	const filterSort = useMemo(() => {
		let filterSort = '';
		sorts.forEach((item) => {
			switch (item.by) {
				case 'high':
					filterSort = item.order === 'desc' ? '-HighRiskCount' : 'HighRiskCount';
					break;
				case 'medium':
					filterSort = item.order === 'desc' ? '-MediumRiskCount' : 'MediumRiskCount';
					break;
				case 'low':
					filterSort = item.order === 'desc' ? '-LowRiskCount' : 'LowRiskCount';
					break;
				case 'authTime':
					filterSort = item.order === 'desc' ? '-OpeningTime' : 'OpeningTime';
					break;
				case 'lastAssessTime':
					filterSort = item.order === 'desc' ? '-LastEvaluationTime' : 'LastEvaluationTime';
					break;
			}
		});
		return filterSort;
	}, [sorts]);

	useEffect(() => {
		fetch();
	}, [pageInfo, filterSort, filterRegions]);

	const fetch = async () => {
		setLoading(true);
		setError(false);
		setUserList([]);
		try {
			const res = await getAdvisorCustomer({
				SearchWord: searchInput,
				OrderBy: filterSort,
				Regions: filterRegions,
				Offset: (pageInfo.pageIndex - 1) * pageInfo.pageSize,
				Limit: pageInfo.pageSize,
			});

			if (res.Error) {
				setError(true);
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: res.Error.Message });
				}
			} else {
				setTotal(res.Total);
				setUserList(
					res.CustomerList.map((customer) => ({
						uin: customer.Uin,
						appid: customer.AppID,
						nickname: customer.CustomerName,
						markname: customer.MarkName,
						salesSupportor: customer.SalesSupportor,
						responser1: customer.Responser1,
						responser2: customer.Responser2,
						businessManager: customer.BusinessManager,
						clientManager: customer.ClientManager,
						salesGrade: customer.SalesGrade,
						focusedStrategyId: customer.FocusedStrategyId,
						region: customer.Region,
						authTime: moment(customer.OpeningTime).format('YYYY-MM-DD HH:mm:ss'),
						/*
						high: customer.HighRiskCount,
						medium: customer.MediumRiskCount,
						low: customer.LowRiskCount,
						lastAssessTime:
							customer.LastEvaluationTime.slice(0, 4) === '1970'
								? '未进行过扫描'
								: moment(customer.LastEvaluationTime).format('YYYY-MM-DD HH:mm:ss'),
						*/
					}))
				);
			}
		} catch (err) {
			let message: string = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setError(true);
		}
		setLoading(false);
	};

	const handleDownload = () => {
		let filterVal = Object.keys(userList[0]);
		let tHeader = columns.map((column) => column.header);
		let data = formatJson(filterVal, userList);
		let filename = `Advisor用户总览${moment().format('YYYY-MM-DD')}`;
		const { export_json_to_excel } = require('@src/utils/exportExcel/Export2Excel'); //引入
		export_json_to_excel(tHeader, data, filename);
	};
	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
			<Content>
				<Content.Header title="客户总览"></Content.Header>
				<Content.Body>
					<Table.ActionPanel>
						<Justify
							right={
								<>
									<SearchBox
										style={{ width: '300px' }}
										onSearch={(value) => {
											setSearchInput(value);
											setPageInfo({
												...pageInfo,
												pageIndex: 1,
											});
										}}
										onClear={() => {
											setSearchInput('');
											setPageInfo({
												...pageInfo,
												pageIndex: 1,
											});
										}}
										placeholder="输入主账号UIN或APPID或客户简称过滤"
									/>
									<Button
										icon="refresh"
										onClick={() => {
											fetch();
										}}
									/>
								</>
							}
						/>
					</Table.ActionPanel>
					<Card>
						<Table
							verticalTop
							records={userList}
							//recordKey="appid"
							columns={columns}
							addons={[
								autotip({
									isLoading: loading,
									isError: error,
								}),
								// 对 region 列增加多选过滤支持
								filterable({
									type: 'multiple',
									column: 'region',
									value: filterRegions,
									onChange: (value) => {
										setFilterRegions(value.filter((x) => x !== 'all'));
									},
									all: {
										value: 'all',
										text: '全部',
									},
									options:
										regionList.map((item) => ({
											value: item,
											text: item,
										})) || [],
								}),
								/*
								sortable({
									columns: [{ key: 'authTime', prefer: 'desc' }],
									value: sorts,
									onChange: (value) => setSorts((value.length && [value[0]]) || value),
								}),*/
								pageable({
									recordCount: total,
									pageIndex: pageInfo.pageIndex,
									pageSize: pageInfo.pageSize,
									onPagingChange: (e) => {
										setPageInfo({
											pageIndex: e.pageIndex,
											pageSize: e.pageSize,
										});
									},
									pageSizeOptions: [10, 20, 50, 100, 1000],
								}),
							]}
						/>
					</Card>
				</Content.Body>
			</Content>}</div>}
		</Body>
	);
}
