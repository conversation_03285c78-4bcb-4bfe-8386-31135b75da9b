export interface DescribeArchStrategyListParams {
	ArchId: string,
	ResultId: string,
	Uin?: string,
	AppId?: number,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface DescribeArchScanOverviewInfoParams {
	ArchId: string,
	ResultId: string,
	Uin?: string,
	AppId?: number,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface UpdateArchScanReportArchiveInfoParams {
	ArchIds?: Array<string>,
	DeleteStatus?: boolean,
	Uin?: string,
	AppId?: number,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface DescribeArchScanReportArchiveInfoParams {
	Limit: number,
	Offset: number,
	Filters: Array<{
		Name: string,
		Values: Array<string>
	}>,
	Uin?: string,
	AppId?: number,
	OnlyData?: boolean,
	ShowError?: boolean
}