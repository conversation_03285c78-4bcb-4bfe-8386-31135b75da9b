import request from '@src/api/request';
import {
	DescribeArchScanOverviewInfoParams,
	DescribeArchStrategyListParams,
	UpdateArchScanReportArchiveInfoParams,
	DescribeArchScanReportArchiveInfoParams
} from './apiType';



export const DescribeArchScanOverviewInfo = (data: DescribeArchScanOverviewInfoParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/arch',
		data: { ...data, Action: 'DescribeArchScanOverviewInfo' },
	});
};

// 架构图风险趋势
export const DescribeArchRiskTrendInfo = (data: DescribeArchScanOverviewInfoParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/arch',
		data: { ...data, Action: 'DescribeArchRiskTrendInfo' },
	});
};

// 架构图云巡检明细清单
export const DescribeArchStrategyList = (data: DescribeArchStrategyListParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/arch',
		data: { ...data, Action: 'DescribeArchStrategyList' },
	});
};

// 报告归档接口
export const UpdateArchScanReportArchiveInfo = (data: UpdateArchScanReportArchiveInfoParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/arch',
		data: { ...data, Action: 'UpdateArchScanReportArchiveInfo' },
	});
};

// 架构图云巡检明细清单
export const DescribeArchScanReportArchiveInfo = (data: DescribeArchScanReportArchiveInfoParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/arch',
		data: { ...data, Action: 'DescribeArchScanReportArchiveInfo' },
	});
};