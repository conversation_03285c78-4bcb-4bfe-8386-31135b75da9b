.inspect-report-modal {
	margin-top: -50px;
	padding-top: 50px;
	.tea-icon-close {
		background-size: auto;
		display: none;
	}
	.tea-link-external:after {
		background-size: auto;
	}
	.tea-dialog__inner {
		width: 100%;
		background-color: #1B3EC3;
		padding-bottom: 900px;
	}
	.t-area {
		position: relative;
		.t-area-text {
			color: #fff;
			position: relative;
			z-index: 2;
			.report-t {
				display: flex;
				align-items: center;
				margin-bottom: 14px;
				font-size: 24px;
				svg {
					margin-right: 16px;
				}
			}
			.report-t-en {
				font-size: 16px;
				margin-bottom: 20px;
			}
			.line {
				display: flex;
				margin-bottom: 16px;
				font-size: 16px;
				&:nth-last-child(1) {
					margin-bottom: 27px;
				}
				.line-t {
					margin-right: 10px;
				}
			}
		}
		.t-area-bg {
			background-image: url(https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/a7171bde-9cb7-4eae-810c-ad9c76bc8e17.png);
			position: absolute;
			right: 0;
			top: 0;
			background-size: 100% auto;
			width: 476px;
			height: 224px;
			background-repeat: no-repeat;
			background-position: 0px -28px;
			text-align: right;
			.svg-t {
				margin-top: 25px;
				margin-right: 79px;
			}
			.sub-svg-t {
				margin-right: 148px;
				margin-top: 13px;
			}
		}
	}
	.card-t {
		font-size: 16px;
		color: #1B3EC3;
		margin-bottom: 12px;
		font-weight: bold;
	}
	.pic-wrap {
		margin-top: 24px;
		margin-bottom: 34px;
		height: 382px;
		svg {
			width: 100%;
			height: 382px;
			overflow: auto;
		}
	}
	.overview-t-desc {
		font-size: 16px;
		margin-bottom: 8px;
		.percent-num {
			color: #1B3EC3;
		}
	}
	.overview-t-desc-sub {
		font-size: 13px;
		margin-bottom: 24px;
	}
	.risk-items-wrap {
		display: flex;
		gap: 16px;
		.risk-items {
			width: 25%;
			padding: 24px 0 24px 24px;
			.items-t {
				font-size: 16px;
				margin-bottom: 8px;
				line-height: 16px;
			}
			.item-num {
				font-size: 32px;
				margin-bottom: 8px;
				line-height: 32px;
			}
			.item-desc {
				line-height: 20px;
				font-size: 12px;
				color: rgba(0, 0, 0, 0.60);
				svg {
					vertical-align: middle;
					margin: -2px 5px 0;
				}
			}
			&:nth-child(1) {
				background-color: #F0F4FF;
				.items-t {
					font-size: 16px;
					margin-bottom: 8px;
				}
				.item-num {
					font-size: 32px;
					color: #1B3EC3;
					margin-bottom: 8px;
				}
				.item-desc {
					svg {
						path {
							fill: #1B3EC3;
						}
					}
					.num {
						color: #1B3EC3;
					}
				}
			}
			&:nth-child(2) {
				background-color: #FDF1F1;
				.items-t {

				}
				.item-num {
					color: #F64041;
				}
				.item-desc {
					svg {
						path {
							fill: #F64041;
						}
					}
					.num {
						color: #F64041;
					}
				}
			}
			&:nth-child(3) {
				background-color: #FFF4EC;
				.items-t {

				}
				.item-num {
					color: #FF7800;
				}
				.item-desc {
					svg {
						path {
							fill: #FF7800;
						}
					}
					.num {
						color: #FF7800;
					}
				}
			}
			&:nth-child(4) {
				background-color: rgba(224, 247, 235, 0.60);
				.items-t {

				}
				.item-num {
					color: #0CBF5B;
				}
				.item-desc {
					svg {
						path {
							fill: #0CBF5B;
						}
					}
					.num {
						color: #0CBF5B;
					}
				}
			}
		}
	}
	.chart-wrap {
		margin-top: 16px;
		display: flex;
		gap: 16px;
		&.chart-wrap-br {
			border: 1px solid #E6E9EF;
		}
		.chart-outer {
			height: 300px;
		}
		.chart-list {
			display: flex;
			flex-wrap: wrap;
			padding: 18px 24px 0;
			.desc-wrap {
				height: 170px!important;
				padding: 12px 20px;
				box-sizing: border-box;
				background-image: url(https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/264056be-fdcc-4c3b-b224-153634d2de5e.png);
				background-repeat: no-repeat;
				background-size: 100% 100%;
				.desc-t-wrap {
					background: #F8F9FF;
					// box-shadow: 0px 10px 20px 0px rgba(75, 91, 118, 0.08), 0px 4px 6px 0px rgba(75, 91, 118, 0.08);
					filter: drop-shadow(5px 5px 5px rgba(75, 91, 118, 0.08));
					width: 208px;
					padding: 4px 8px;
					margin-left: -8px;
				}
				.desc-t {
					color: #2D5EEA;
					font-size: 12px;
					font-style: normal;
					font-weight: 500;
					line-height: 18px;
				}
				.desc-line {
					margin-top: 7px;
					color: rgba(0, 0, 0, 0.90);
					font-size: 12px;
					font-style: normal;
					font-weight: 400;
					line-height: 18px;
				}
			}
			.line-pic-item {
				height: 250px;
				margin-bottom: -40px;
				width: 50%;
			}
		}
		.title {
			font-size: 16px;
			font-style: normal;
			font-weight: bold;
			padding-left: 24px;
		}
		.tea-link-external {
			margin-top: 8px;
			padding-left: 24px;
		}
		.chart-item {
			width: 50%;
			padding-top: 24px;
			.chart-outer {
				height: 300px;
				.pic-wrap {
					padding-left: 24px;
					margin-top: 24px;
					svg {
						width: 100%;
						height: 220px;
						overflow: auto;
					}
				}
				.line-pic-item {
					height: 250px;
					margin-bottom: -40px;
				}
			}
			&.chart-item-br {
				border: 1px solid #E6E9EF;
				&:nth-child(2) {
					.chart-outer {
						height: 350px;
					}
				}
			}
		}
	}
	.download-wrap {
		display: flex;
		align-items: center;
		color: #fff;
		margin-top: 24px;
		button {
			margin-left: 15px;
			display: flex;
			align-items: center;
			color: #000;
			svg {
				margin-right: 5px;
			}
		}
	}
	.tab-desc {
		font-size: 12px;
		color: #3D485D;
		margin-top: 10px;
	}
	.inspect-see-report-tab {
		th {
			background-color: #1B3EC3;
			> div {
				color: #fff;
			}
		}
		.tea-table__box tr:nth-child(even) {
			background-color: #ebeef2;
		}
		.repair-line-wrap {
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 5;
			overflow: hidden;
			text-overflow: ellipsis;
		}
	}
}