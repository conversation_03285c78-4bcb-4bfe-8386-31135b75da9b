import React, { useState, useEffect } from 'react';
import { Table, message, But<PERSON>, Card, Layout, StatusTip, Tag } from '@tencent/tea-component';
import { useToggle } from '../broadcast/hooks/common';
import { DescribeGuardProjects } from '@src/api/advisor/guard';
import { getViewPort } from '@src/routes/architecture/utils';
import { reportVisitPage } from '@src/utils/report';
import { useHistory, useAegisLog } from '@tea/app';
import './style.less';
const { Body, Content } = Layout;


export function GuardGrafana() {
    // 加载状态
    const [isLoading, startLoad, endLoad] = useToggle(false);
    const aegis = useAegisLog();

    const [guardProjects, setGuardProjects] = useState([]);

    const [isShowDetail, openDetail, closeDetail] = useToggle(false);

    const [currentGuardDetail, setCurrentGuardDetail] = useState<any>({})

    const getGuardProjects = async () => {
        startLoad();
        try {
            const res = await DescribeGuardProjects({ AppId: 1253985742, })
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                setGuardProjects(res.Projects);
            }
            endLoad();
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
            endLoad();
        }
    }

    useEffect(() => {
        getGuardProjects();
        aegis.reportEvent({
            name: 'manual-PV',
            ext1: location.pathname,
            ext2: '护航项目大盘',
            ext3: localStorage.getItem('engName')
        })
        reportVisitPage({
			isaReportMeunName: '护航项目大盘',
		});
    }, []);

    const handleGuardDetailView = item => {
        openDetail();
        setCurrentGuardDetail(item);
    }

    const handleDetailClose = () => {
        closeDetail();
        setCurrentGuardDetail({});
    }
    const viewPortHeight = getViewPort('height');

    const columns = [
        {
            key: 'Name',
            align: 'left',
            header: '项目',
            render: item => {
                return <Button type="link" onClick={() => handleGuardDetailView(item)}>{item.Name}</Button>
            }
        },
        {
            key: 'StartTime',
            align: 'left',
            header: '持续时间',
            render: item => {
                const { StartTime, EndTime } = item;
                return (<>
                    {StartTime && <><Tag theme="primary">{StartTime}</Tag> ~ </>}
                    {EndTime && <Tag theme="primary">{EndTime}</Tag>}
                </>)
            }
        },
        {
            key: 'AppIdCount',
            align: 'left',
            header: '客户数量',
        },
    ]

    const tableOptionProps = {
        style: { marginTop: 15 },
        recordKey: 'Id',
        verticalTopL: true,
        columns,
        records: guardProjects.filter(item => item.GrafanaStatus === 1),
        topTip: isLoading ? <StatusTip status="loading"></StatusTip> : guardProjects.length === 0 && <StatusTip status="empty" />,
    }

    return (
        <Body>
            <Content>
                <Content.Header
                    title={isShowDetail ? currentGuardDetail.Name : "护航项目大盘"}
                    onBackButtonClick={handleDetailClose}
                    showBackButton={isShowDetail}
                ></Content.Header>
                <Content.Body>
                    <Card>
                        <Card.Body className={isShowDetail && 'iframe-card'}>
                            {
                                isShowDetail
                                    ? <iframe width="100%" height={viewPortHeight - 120} src={currentGuardDetail.GrafanaUrl}></iframe>
                                    : <>
                                        {/* @ts-ignore */}
                                        < Table {...tableOptionProps} />
                                    </>
                            }
                        </Card.Body>
                    </Card>
                </Content.Body>
            </Content>
        </Body>
    );
}