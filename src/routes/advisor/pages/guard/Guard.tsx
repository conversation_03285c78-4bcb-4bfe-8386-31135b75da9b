import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
	Table,
	Justify,
	Button,
	Card,
	Layout,
	message as tips,
	SelectMultiple,
	StatusTip,
	Icon,
	Row,
	Col,
	Tag,
	DatePicker,
	Text,
	Input,
	Dropdown,
	List,
	Bubble,
	Alert,
} from '@tencent/tea-component';
import moment, { Moment } from 'moment';

import { Modal } from '@tencent/tea-component/lib//modal';
import { Segment } from '@tencent/tea-component/lib/segment';

import { getProductsGroups } from '@src/api/advisor/estimate';
import {
	DescribeGuardCalendar,
	DescribeGuardSheet,
	DescribeGuardProjects,
	PushGuardChat,
	DescribeIndustryList,
	deleteGuardSheet,
	CopyOneGuard,
	getDescribeAdvisorHint,
	restartGuardScanTask,
} from '@src/api/advisor/guard';
import {
	GuardParams,
	Filter,
	StatusDict,
	ShowStandardDict,
	map2options,
	AddStatusDict,
} from '@src/types/advisor/guard';
import { useHistory, useAegisLog } from '@tea/app';
import { DownloadReport } from './components/DownloadReport';
import { ExportGuardSheet } from './components/ExportGuardSheet';
import GuideLinks from './components/GuideLinks';
import { getUserInfo } from '@src/api/common';
import GuardListDownload from './components/guardListDownload';
import { setStorage, getStorage } from '@src/utils/storage';
import Loading from './components/Loading';
import GuardStatu from './components/GuardStatu';
import { getProcessEnv } from '../../../../../app/utils';
import { Divider } from 'tdesign-react';
import { AddIcon, BrowseIcon, BrowseOffIcon, FilterIcon, HelpCircleIcon } from 'tdesign-icons-react';
import { reportVisitPage } from '@src/utils/report';
import { GuardCalendar } from './components/calendar/GuardCalendar';
import { IconTsa } from '@src/components/IconTsa';
import { baseInfoModifyAllowed, getBaseInfoEditor, isInstanceModifyAllowed, getInstanceEditor } from '@src/utils/business';
import { getSearchParam } from '@src/utils/common';
const { RangePicker } = DatePicker;
const { Body, Content } = Layout;
const { pageable } = Table.addons;;

const createEnv = ['ISA', 'CONSOLE'];

// 护航单查询条件合集对象初始化值 也用户重置输入
export const descParams = {
	guard_id: '',
	guard_name: '',
	standard: [], // 默认标准版
	industry: [],
	project: [], // 默认全选
	status: [], // 默认全选
	start_time: moment().subtract(1, 'y'),
	end_time: moment().add(180, 'd'),
	created_by: '',
	responser: '',
	customerName: '',
	appid: '',
	product: [],
	origin: ['0'], // 默认运营端
};

// 持续查询任务
let timer;
const interval = 30000;

export function Guard() {
	const downloadRef = useRef(null);
	// 是否是测试环境
	const isTest = getProcessEnv() === 'test';
	const history = useHistory();
	const { search } = location;
	// 巡检失败链接跳转携带护航单id
	const failGuardId = getSearchParam('guardId', location);
	const hasAppid = search.indexOf('appid') !== -1;
	const searchVal = hasAppid ? search.split('=')[1] : '';
	const aegis = useAegisLog();
	const ctxUser = localStorage.getItem('engName');
	// 是否显示我的护航
	const [showMyGuard, setShowMyGuard] = useState(!failGuardId);
	// 是否显示过滤条件
	const [showFilter, setShowFilter] = useState(!!failGuardId);
	// 更多操作
	const moreOptions = [
		{ text: '列表模式', value: '1' },
		{ text: '日历模式', value: '2' },
		// { name: '统计模式', type: 'Item', value: 3 }
	];
	// 当前点击的操作
	const [clickOption, setClickOption] = useState(1);
	// 日历模式下护航单数据
	const [calendarData, setCalendarData] = useState([]);
	// 日历模式总护航单总数据
	const [guardData, setGuardData] = useState([]);
	// 日历当前时间
	const [calendarTime, setCalendarTime] = useState(moment());
	// 产品映射选项
	const [productDict, setProductDict] = useState([]);
	// 产品下拉框选项
	const [productsOptions, setProductsOptions] = useState([]);
	// 护航项目选项
	const [projectOptions, setProjectOptions] = useState([]);
	const [projectDict, setProjectDict] = useState(new Map());
	// 行业选项
	const [industryOptions, setIndustryOptions] = useState([]);
	const paramInfo = JSON.parse(getStorage('paramInfo'));
	// 护航单查询条件参数清单
	const [guardId, setGuardId] = useState<string>(failGuardId || paramInfo?.guardId || descParams.guard_id); // 护航ID
	const [statusList, setStatusList] = useState<Array<string>>(paramInfo?.statusList || descParams.status); // 护航状态 多选
	const [appId, setAppId] = useState<string>(paramInfo?.appId || descParams.appid); // 客户APPID
	const [guardName, setGuardName] = useState<string>(paramInfo?.guardName || descParams.guard_name); // 护航名称
	// 护航开始结束时间 默认过去7天
	// eslint-disable-next-line max-len
	const [startTime, setStartTime] = useState<Moment>(paramInfo?.startTime ? moment(paramInfo?.startTime) : descParams.start_time);
	// eslint-disable-next-line max-len
	const [endTime, setEndTime] = useState<Moment>(paramInfo?.endTime ? moment(paramInfo?.endTime) : descParams.end_time);
	// 涉及云产品 多选
	const [productList, setProductList] = useState<Array<string>>(paramInfo?.productList || descParams.product);
	// 护航类型 多选
	const [standardList, setStandardList] = useState<Array<string>>(paramInfo?.standardList || descParams.standard);
	// 行业 多选
	const [industryList, setIndustryList] = useState<Array<string>>(paramInfo?.industryList || descParams.industry);
	// 护航提单人
	// eslint-disable-next-line @typescript-eslint/naming-convention
	const [created_by, setCreated_by] = useState<string>(paramInfo?.created_by || descParams.created_by);
	// 护航来源 多选
	const [originList, setOriginList] = useState<Array<string>>(paramInfo?.originList || descParams.origin);
	// 护航项目 多选
	const [projectList, setProjectList] = useState<Array<string>>(paramInfo?.projectList || descParams.project);
	// 客户名称
	const [customerName, setCustomerName] = useState<string>(paramInfo?.customerName || descParams.customerName);
	const [responser, setResponser] = useState<string>(paramInfo?.responser || descParams.responser); // 护航负责人
	if (hasAppid) {
		history.replace(location.pathname);
	}
	if (hasAppid) {
		if (!appId) {
			setStartTime(null);
			setEndTime(null);
			setAppId(searchVal);
		}
	}
	if (paramInfo !== 'null') {
		setStorage('paramInfo', null);
	}
	// 护航单查询接口常规参数
	const [offset, setOffset] = useState<number>(0);
	const [limit, setLimit] = useState<number>(20);
	const [loading, setLoading] = useState<boolean>(false);
	// 护航单记录
	const [guards, setGuards] = useState<Array<GuardParams>>([]);
	const [total, setTotal] = useState<number>(0);
	// 当前删除单子
	const [currentDeleteMsg, setCurrentDeleteMsg] = useState<string>('');
	const [currentDeleteId, setCurrentDeleteId] = useState<number>(-1);
	const [currentDeleteAppid, setCurrentDeleteAppid] = useState<number>(1253985742);
	const [visibleDeleteConfirm, setVisibleDeleteConfirm] = useState<boolean>(false);

	// 下载报告弹窗
	const [visible, setVisible] = useState<boolean>(false);

	// const [modalContent, setModalContent] = useState<Array<DescribeAdvisorHintContent>>([]);
	// 当前报告url
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const [currentReport, setCurrentReport] = useState<any>({});

	// 报告类型
	const [reportType, setReportType] = useState<string>('PublicCosUrl');

	// 当前登录rtx
	const [rtx, setRtx] = useState<string>('');

	// 流程状态ID
	const processState = {
		onNoteId: 1,              // 草稿状态ID
		submitId: 2,              // 订单已提交状态ID
		onSaleApprovalId: 31,     // 售后审批状态ID
		onRunningId: 32,          // 正在巡检中状态ID
		onExpertApprovalId: 33,   // 专项分配人员状态ID
		onResultApprovalId: 34,   // 巡检结果审核状态ID
		onReportGenerating: 36,   // 巡检报告生成中
		onReportGenerated: 37,    // 巡检报告已完成
		instanceAltering: 40,     // 实例修改中ID
		instanceAlteredRun: 41,   // 实例修改后运行中ID
		tamCheckResultId: 48,     // 巡检风险审批（TAM）
		onFinishId: 50,           // 护航巡检完成状态ID
		processStopId: -1,        // 流程中止状态ID
		scanFailedId: -2,         // 巡检异常状态ID
		approvalFailedId: -3,     // 审核异常状态ID
		deletedId: -50,           // 已删除
	};
	// 页面加载开始，获取更新弹窗对话框的内容信息
	const getModalContent = async () => {
		try {
			const res = await getDescribeAdvisorHint({
				Action: 'DescribeAdvisorHint',
				CanShow: true,
				Filters: [{
					Name: 'page',
					Values: ['advisor/guard'],
				}],
				Limit: -1,
			});
			if (!res?.AdvisorHints?.length) return;
			const newIdString = res.AdvisorHints.map(item => item.Id).join('');
			const getCookie = (cname: string) => {
				const name = `${cname}=`;
				const ca = document.cookie.split(';');
				for (let i = 0; i < ca.length; i++) {
					const c = ca[i].trim();
					if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
				}
				return '';
			};
			const showMoalCookie: string = getCookie('modalCanShow');
			if (!showMoalCookie) {
				// setModalContent(res.AdvisorHints);
				// setVisibleUpdate(true);
			} else {
				if (showMoalCookie !== newIdString) {
					document.cookie = 'modalCanShow=; expires=Thu, 01 Jan 1970 00:00:00 GMT';
					// setModalContent(res.AdvisorHints);
					// setVisibleUpdate(true);
				}
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	useEffect(() => {
		getModalContent();
	}, []);

	// 重置状态和监听重置状态进行查询
	const [reStart, setRestart] = useState(true);
	useEffect(() => {
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: '护航管理/护航查询',
			ext3: ctxUser,
		});
		reportVisitPage({
			isaReportMeunName: '护航管理',
		});
		return () => {
			setStorage('paramInfo', null);
		};
	}, []);

	// 获取当前登录账号rtx
	const getCurrentOperator = async () => {
		try {
			const res = await getUserInfo();
			setRtx(res.data.EngName || '');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 重置查询条件
	function resetParams() {
		setGuardId(descParams.guard_id);
		setStatusList(descParams.status);
		setAppId(descParams.appid);
		setGuardName(descParams.guard_name);
		setStartTime(descParams.start_time);
		setEndTime(descParams.end_time);
		setProductList(descParams.product);
		setStandardList(descParams.standard);
		setIndustryList(descParams.industry);
		setCreated_by(descParams.created_by);
		setOriginList(descParams.origin);
		setProjectList(descParams.project);
		setResponser(descParams.responser);
		setCustomerName(descParams.customerName);
	}

	// 查询是否有处理护航巡检结果权限
	function isResultModifyAllowed(currentGuard: GuardParams) {
		// 非巡检完成状态不允许修改
		if (currentGuard.Status !== processState.tamCheckResultId) {
			return false;
		}

		// 有权限修改的人员
		const resEditorList = getResultEditor(currentGuard);
		if (resEditorList.indexOf(rtx) === -1) {
			return false;
		}

		return true;
	}

	// 查询是否有处理日报权限
	function isReportModifyAllowed(currentGuard: GuardParams) {
		// 仅“巡检风险审批（TAM）”、“护航巡检已完成”状态
		if (currentGuard.Status !== 48 && currentGuard.Status !== 50) {
			return false;
		}

		// 有权限修改的人员
		const resEditorList = getResultEditor(currentGuard);
		// 添加海明权限，方便现网验证日报
		resEditorList.push('hemingzhang');
		if (resEditorList.indexOf(rtx) === -1) {
			return false;
		}

		return true;
	}

	// 查询是有修改护航单巡检结果的人员
	function getResultEditor(currentGuard: GuardParams) {
		let guys = [];
		guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Supporter.split(';'));
		guys = guys.concat(currentGuard.Responser.split(';'));
		guys = guys.concat(currentGuard.AppIdOwner.split(';'));

		return Array.from(new Set(guys)).filter(i => i !== '');
	}

	// 查询是否有删除护航单实例权限
	function isDeleteAllowed(currentGuard: GuardParams) {
		let isDelete = false;
		const editMode = currentGuard.Status === processState.onNoteId;
		const creator = currentGuard.CreatedBy.trim();
		const inChargeList = currentGuard.Approvals.AfterSalesStatus.Supporter.split(';');

		// 草稿状态：建单人可以删除
		if (editMode && creator === rtx) {
			isDelete = true;
		}

		// 非草稿状态：建单人、护航负责人可以删除
		if (!editMode && (creator === rtx || inChargeList.includes(rtx))) {
			isDelete = true;
		}

		return isDelete;
	}

	// 护航负责人、提单人
	function reInspectingPerson(currentGuard) {
		const persons = currentGuard?.Responser?.split(';').concat([currentGuard?.SubmittedBy?.trim()]);
		return Array.from(new Set(persons)).filter(i => i !== '');
	}

	// 查询是否有重新发起失败任务权限
	function hasReInspectingAuth(currentGuard: GuardParams) {
		// 仅 巡检失败
		if (currentGuard.Status !== processState.scanFailedId) {
			return false;
		}
		// 有权限修改的人员
		const resEditorList = reInspectingPerson(currentGuard);
		if (resEditorList.indexOf(rtx) === -1) {
			return false;
		}
		return true;
	}

	// 重试失败任务
	async function reInspection(item) {
		try {
			const params  = {
				GuardId: item?.GuardId,
				Operator: rtx,
			};
			const res = await restartGuardScanTask(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			tips.success({ content: '重新发起失败任务成功' });
			getGuardSheet();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 跳转到对应架构图中，打开不同的抽屉
	function jumpToArchitecture(data, type, archInfo = null) {
		const appId = type === 'jump' ? archInfo?.AppId : data.MainAppId;
		const PlatformUniqueId = data.CloudGuardBaseInfoOtherPlatform?.find(i => createEnv.includes(i.Platform) && i.AppId === appId)?.PlatformUniqueId || '';
		let url = `${location.origin}/advisor/new-architecture/architecture/${PlatformUniqueId}?appid=${appId}&plugin=cloud-escort-sdk&fromOperate=${type}`;
		if (type === 'copy') {
			url += `&guardId=${data.GuardId}`;
		}
		window.open(url);
	}

	// 护航单表头
	const guardColumns = [
		{
			key: 'GuardId',
			header: '护航ID｜名称',
			width: '8%',
			render: (item) => {
				if (item.Status === 1) {
					return <Button type="link" style={{ whiteSpace: 'pre-wrap' }} disabled={!item?.IsAuth} onClick={() => {
						history.push(`/advisor/guard/create?guardid=${item.GuardId}`);
					}}>{item.GuardId}｜{item.GuardName}</Button>;
				}
				return <Button type="link" style={{ whiteSpace: 'pre-wrap' }} disabled={!item?.IsAuth}
					onClick={() => {
						history.push(`/advisor/guard/summary/${item.GuardId}`);
						setStorage('paramInfo', {
							guardId,
							guardName,
							standardList,
							industryList,
							statusList,
							created_by,
							responser,
							customerName,
							appId,
							productList,
							originList,
							projectList,
							startTime,
							endTime,
						});
					}
					}>{item.GuardId}｜{item.GuardName}</Button>;
			},
		},
		{
			key: 'GuardName',
			header: '客户APPID｜名称',
			width: '18%',
			render: item => (
				<>
					<Tag >{item.MainAppId}｜{item.CustomerName}</Tag><br />
					{item.RelatedAppId.map((i, index) => (<div key={i}>
						<Tag >{i}｜{item.RelatedCustomerNames[index]}</Tag><br />
					</div>))}
				</>
			),
		},
		{
			key: 'Status',
			header: '状态',
			width: 170,
			render: item => <GuardStatu showReInspection item={item} reInspectionCallback={() => {
				getGuardSheet();
			}}></GuardStatu>,
		},
		{
			key: 'Project',
			header: '护航项目',
			width: '5.8%',
			render: item => <Text>{projectDict.get(item.Project)}</Text>,
		},
		{
			key: 'Industry',
			header: <>行业<Bubble content="行业数据来源于磐石"><Icon type="info" /></Bubble></>,
			width: '5.8%',
			render: item => (item.Industry
				? <Text overflow tooltip>{item.Industry}</Text>
				: <Text theme="label" tooltip='待分配，行业数据来源于磐石'>待分配</Text>),
		},
		{
			key: 'Products',
			header: '护航云产品',
			width: '5.8%',
			render: item => <Text overflow tooltip>{
				(item?.Products?.length > 0)
					? item.Products.map(i => productDict[i]).join('，')
					: <Text theme="label">待分配</Text>
			}</Text>,
		},
		{
			key: 'StartTime',
			header: '护航时间',
			width: 145,
			render: item => <Text>{`${item.StartTime} ~ ${item.EndTime}`}</Text>,
		},
		{
			key: 'SubmittedBy',
			header: '建单人｜建单时间',
			width: 145,
			render: item => <Text>
				<Text>{item.CreatedBy}</Text><br />
				<Text>{item.CreateTime}</Text>
			</Text>,
		},
		{
			key: 'Responser',
			header: '护航负责人',
			width: 110,
			render: item => <Text>{item.Responser || <Text theme="label">待分配</Text>}</Text>,
		},
		{
			key: 'index',
			header: '下载报告',
			width: 70,
			render: item => (
				<DownloadReport
					GuardInfo={item}
					MainAppId={item.MainAppId}
					GuardId={item.GuardId}
					Status={item.Status}
					ReportDict={{}}
					// 直接下载外部报告，不打开下载弹框
					handleCurrentReport={(d) => {
						if (d.PublicCosUrl) {
							window.open(d.PublicCosUrl);
						}
					}}
				/>),
		},
		{
			key: 'riskData',
			header: '初始风险状态',
			width: 100,
			render: (item) => {
				if (item.Status < processState.tamCheckResultId) {
					return <Text>待处理</Text>;
				}
				const highRiskCount = item.RiskData.HighRiskStrategyCount || 0;
				const midRiskCount = item.RiskData.MediumRiskStrategyCount || 0;
				if (item.Status === processState.onFinishId && highRiskCount === 0 && midRiskCount === 0) {
					return <Text style={{ color: 'green' }}>已处理</Text>;
				}
				return (
					<Bubble content={'护航负责人或APPID负责人，可以在 [操作]-[更多]-[处理巡检结果] 进行风险确认和处理'}>
						<Text style={{ whiteSpace: 'pre-line' }}>
							<Text style={{ color: 'red' }}> 高风险 {highRiskCount} 个</Text>{'\n'}
							<Text>中风险 {midRiskCount} 个</Text>
						</Text>
					</Bubble>
				);
			},
		},
		{
			key: 'index1',
			header: '操作',
			width: 90,
			render: (item) => {
				const archInfoList = item.CloudGuardBaseInfoOtherPlatform?.filter(i => createEnv.includes(i.Platform));
				return <>
					<Dropdown
						trigger="hover"
						clickClose={false}
						style={{ marginRight: 10 }}
						button="更多"
					>
						{
							() => (
								<List type="option" style={{ maxHeight: 360 }}>
									{
										archInfoList?.map(i => <List.Item
											disabled={!item?.IsAuth}
											onClick={() => {
												jumpToArchitecture(item, 'jump', i);
											}}
										>
											<div className='architectureIconWrap' >
												<IconTsa type='icon-architecture' className='architectureIcon' />
												<span>架构图（{i?.PlatformUniqueId || '暂无'}）</span>
											</div>
										</List.Item>)
									}
									<List.Item
										disabled={!isDeleteAllowed(item)}
										onClick={() => {
											handleDeleteClick(item);
										}}
									>
										<Icon type="delete" style={{ marginRight: 3 }} />
                                        删除
									</List.Item>
									<List.Item
										disabled={!item?.IsAuth}
										onClick={
											() => {
												copyOneGuard(item);
											}
										}
									>
										<Icon type="copy" style={{ marginRight: 3 }} />
                                        复制
									</List.Item>

									<ExportGuardSheet
										isListItem={true}
										currLoginUser={rtx}
										GuardId={item.GuardId}
										Status={item.Status} // 护航单状态
										CreatedBy={item.CreatedBy}
										Approvals={item.Approvals}
										Responsor={item.Responser}
									/>

									<List.Item
										disabled={!item?.IsAuth}
										onClick={() => {
											if (archInfoList.length === 1) {
												jumpToArchitecture(item, 'broadcastedit');
											} else {
												history.push(`/advisor/broadcast?guardid=${item.GuardId}`);
											}
											aegis.reportEvent({
												name: 'Click',
												ext1: 'broadcast-btn',
												ext2: ctxUser,
												ext3: '护航管理/护航查询',
											});
										}}>
										<Icon type="notice" style={{ marginRight: 3 }} />
                    播报策略
									</List.Item>
									<List.Item
										disabled={(item.Status <= processState.onRunningId
                                        && !item.Approvals.AfterSalesStatus.IsConfirm) || !item?.IsAuth}
										tooltip={item.Status <= processState.onRunningId && !item.Approvals.AfterSalesStatus.IsConfirm ? <Bubble>护航负责人审批后建群</Bubble> : ''}
										onClick={() => {
											hitGuardChat(item.GuardId, item.MainAppId, [rtx]);
										}}>
										<Icon type="wechat" style={{ marginRight: 3 }} />
                                        一键入群
									</List.Item>
									<List.Item
										disabled={!baseInfoModifyAllowed(item)}
										tooltip={!baseInfoModifyAllowed(item) ? <Bubble>
											<Text style={{ whiteSpace: 'pre-line' }}>{` - 什么时候修改？护航期间，非草稿状态\n - 谁能修改？护航负责人、TAM Leader \n${getBaseInfoEditor(item).join(',')}`}</Text>
										</Bubble> : ''}
										onClick={() => {
											aegis.reportEvent({
												name: 'Click',
												ext1: 'ins-change-btn',
												ext2: ctxUser,
												ext3: '护航管理/护航信息变更',
											});
											history.push(`/advisor/guard/baseInfo/${item.GuardId}`);
										}}>
										<Icon type="pencil" style={{ marginRight: 3 }} />
                                        护航信息变更
									</List.Item>
									<List.Item
										disabled={!isInstanceModifyAllowed(item)}
										tooltip={!isInstanceModifyAllowed(item) ? <Bubble>
											<Text style={{ whiteSpace: 'pre-line' }}>{` - 什么时候修改？护航期间，在草稿或计算外的状态\n - 谁能修改？建单人、负责人、审批人\n${getInstanceEditor(item).join(',')}`}</Text>
										</Bubble> : ''}
										onClick={() => {
											aegis.reportEvent({
												name: 'Click',
												ext1: 'ins-change-btn',
												ext2: ctxUser,
												ext3: '护航管理/护航查询',
											});
											history.push(`/advisor/guard/instances/${item.GuardId}`);
										}}>
										<Icon type="pencil" style={{ marginRight: 3 }} />
                                        护航实例变更
									</List.Item>
									<List.Item
										disabled={!isResultModifyAllowed(item)}
										tooltip={!isResultModifyAllowed(item) ? <Bubble>
											<Text style={{ whiteSpace: 'pre-line' }}>{` - 什么时候可以处理？所有专项人员分配后\n - 谁能修改？护航负责人、APPID负责人\n${getResultEditor(item).join(',')}`}</Text>
										</Bubble> : ''}
										onClick={() => {
											aegis.reportEvent({
												name: 'Click',
												ext1: 'scan-result-btn',
												ext2: ctxUser,
												ext3: '护航管理/护航查询',
											});
											history.push(`/advisor/guard/result/${item.GuardId}/${item.MainAppId}`);
										}}>
										<Icon type="pencil" style={{ marginRight: 3 }} />
                                        处理巡检结果
									</List.Item>
									<List.Item
										disabled={!isReportModifyAllowed(item)}
										tooltip={
											!isReportModifyAllowed(item) ? <Bubble>
												<Text style={{ whiteSpace: 'pre-line' }}>{` - 什么时候可以修改？巡检风险审批（TAM）、护航巡检已完成\n - 谁能修改？护航负责人、APPID负责人\n${getResultEditor(item).join(',')}`}</Text>
											</Bubble> : ''
										}
										onClick={() => {
											setStorage('guardItemInfo', JSON.stringify(item));
											history.push('/advisor/guard/report');
										}}>
										<Icon type="pencil" style={{ marginRight: 3 }} />
                                        护航日报/总结报告
									</List.Item>
									<List.Item
										disabled={!hasReInspectingAuth(item)}
										tooltip={
											!hasReInspectingAuth(item) ? <Bubble>
												<Text style={{ whiteSpace: 'pre-line' }}>{` - 什么时候可以修改？巡检任务异常\n - 谁能修改？护航负责人、提单人\n${reInspectingPerson(item).join(',')}`}</Text>
											</Bubble> : ''
										}
										onClick={() => {
											reInspection(item);
										}}
									>
										<Icon type="refresh" style={{ marginRight: 3 }} />
                                        重试失败任务
									</List.Item>
									{item?.isAuth && <List.Item
										onClick={() => {
											window.open('https://wj.qq.com/s2/12840381/55d4/');
										}}
									>
										<Icon type="daily" style={{ marginRight: 3 }} />
                                        护航体验问卷
									</List.Item>}
								</List>
							)
						}
					</Dropdown>
				</>;
			},
		},
	];

	// 点击删除
	const handleDeleteClick = (item: GuardParams) => {
		setCurrentDeleteAppid(item.MainAppId);
		setCurrentDeleteId(item.GuardId);
		setCurrentDeleteMsg(`${item.GuardId} | ${item.GuardName}`);
		setVisibleDeleteConfirm(true);
		aegis.reportEvent({
			name: 'Click',
			ext1: 'delete-btn',
			ext2: ctxUser,
			ext3: '护航管理/护航查询',
		});
	};

	// 一键入群
	const hitGuardChat = async (guardId: number, appid: number, user: Array<string>) => {
		try {
			const res = await PushGuardChat({
				GuardId: guardId,
				AppId: appid,
				User: user,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			tips.success({ content: res.Message });
			aegis.reportEvent({
				name: 'Click',
				ext1: 'to-group-btn',
				ext2: ctxUser,
				ext3: '护航管理/护航查询',
			});
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询日历数据
	const getCalendarData = async (filters) => {
		try {
			const res = await DescribeGuardCalendar({
				AppId: 1253985742,
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
			});
			setLoading(false);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setCalendarData(res.Calendar || []);
			setGuardData(res.Guard || []);
			// 开启持续查询
			if (!timer) {
				timer = setInterval(() => {
					getGuardSheet();
				}, interval);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询护航单
	const getGuardSheet = async (Offset?, Limit?) => {
		// 每次查询，都清理上一次的持续任务
		if (timer) {
			clearInterval(timer);
			timer = null;
		}
		setLoading(true);
		try {
			const startTempTime = clickOption === 2 ? [moment(calendarTime).startOf('month')
				.format('YYYY-MM-DD 00:00:00')] : (startTime ? [startTime.format('YYYY-MM-DD 00:00:00')] : []);
			const endTempTime = clickOption === 2 ? [moment(calendarTime).endOf('month')
				.format('YYYY-MM-DD 23:59:59')] : (endTime ? [endTime.format('YYYY-MM-DD 23:59:59')] : []);

			const filters: Array<Filter> = [
				{ Name: 'guard_id', Values: guardId ? [guardId.trim()] : [] },
				{ Name: 'guard_name', Values: guardName ? [guardName.trim()] : [] },
				{ Name: 'standard', Values: standardList },
				{ Name: 'industry', Values: industryList },
				{ Name: 'status', Values: clickOption === 2 ? [] : statusList },
				{ Name: 'submitted_by', Values: created_by ? [created_by.trim()] : [] },
				{ Name: 'responsible_person', Values: responser ? [responser.trim()] : [] },
				{ Name: 'customer_name', Values: customerName ? [customerName.trim()] : [] },
				{ Name: 'appid', Values: appId ? [appId.trim()] : [] },
				{ Name: 'product', Values: productList },
				{ Name: 'origin', Values: originList },
				{ Name: 'project', Values: projectList },
				{ Name: 'start_time', Values: startTempTime },
				{ Name: 'end_time', Values: endTempTime },
			];
			if (showMyGuard) {
				filters.push({ Name: 'participants', Values: [ctxUser] });
			}
			if (clickOption === 1) {
				const res = await DescribeGuardSheet({
					Filters: filters.filter((i) => {
						if (i.Values.length) {
							return i;
						}
					}),
					Operator: rtx || localStorage.getItem('engName'),
					Offset,
					Limit,
					AppId: 1253985742,
					Type: 'list',
				});
				setLoading(false);
				if (res.Error) {
					const msg = res.Error.Message;
					tips.error({ content: msg });
					return;
				}
				setGuards(res.Guard);
				setTotal(res.TotalCount);
				// 开启持续查询
				if (!timer) {
					timer = setInterval(() => {
						getGuardSheet(Offset, Limit);
					}, interval);
				}
			} else {
				getCalendarData(filters);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 删除护航单
	const delGuardSheet = async () => {
		setLoading(true);
		try {
			const res = await deleteGuardSheet({
				AppId: currentDeleteAppid,
				Ids: [currentDeleteId],
				Operator: rtx,
			});
			setLoading(false);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			tips.success({ content: '删除成功!' });
			getGuardSheet(offset, limit);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 获取云产品清单
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: 1253985742, // 接口必须传appid  为获取全量产品列表，因此传内部中心账号
				Env: 'all',
				TaskType: 'guardTaskType',
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmpProductsOptions = [];
			for (const i in res.ProductDict) {
				tmpProductsOptions.push({ value: i, text: res.ProductDict[i] });
			}
			setProductDict(res.ProductDict || []);
			setProductsOptions(tmpProductsOptions);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询护航项目清单
	const getProjects = async () => {
		try {
			const res = await DescribeGuardProjects({
				AppId: 1253985742, // 接口必须传appid  为获取全量产品列表，因此传内部中心账号
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmp = [];
			const m = new Map();
			res.Projects.map((i) => {
				tmp.push({ value: i.Id.toString(), text: i.Name });
				m.set(i.Id, i.Name);
			});
			setProjectOptions(tmp);
			setProjectDict(m);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询护航状态
	const getStatusOptions = () => {
		const options: Array<{ text: string, value: string }> = [];

		StatusDict.forEach((v, k) => {
			options.push({ text: v, value: k.toString() });
		});

		// 新增的状态节点
		AddStatusDict.forEach((v, k) => {
			options.push({ text: v, value: k.toString() });
		});

		return options;
	};
	// 获取行业列表
	async function getDescribeIndustryList() {
		try {
			const res = await DescribeIndustryList({
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setIndustryOptions((res.IndustryList || []).map(i => ({ value: i, text: i })));
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}
	// 页面加载初始化 获取完全产品清单
	useEffect(() => {
		getDescribeIndustryList();
		getCurrentOperator();
		getProductsGroupsInfo();
		getProjects();
		return () => {
			if (timer) {
				clearInterval(timer);
			}
		};
	}, []);

	// 我的护航切换查询
	useEffect(() => {
		getGuardSheet(offset, limit);
	}, [showMyGuard]);

	// 查看模式切换
	useEffect(() => {
		getGuardSheet(0, limit);
	}, [clickOption]);

	const [copyLoading, setCopyLoading] = useState(false);
	// 复制护航单
	const copyOneGuard = async (data) => {
		setCopyLoading(true);
		try {
			const res = await CopyOneGuard({
				GuardId: data.GuardId,
				Operator: getStorage('engName'),
				AppId: data.MainAppId,
				ShowError: true,
				OnlyData: true,
			});
			setCopyLoading(false);
			history.push(`/advisor/guard/create?guardid=${res.GuardId}`);
		} catch (e) {
			setCopyLoading(false);
		}
	};
	// 添加回车键的模拟按钮点击事件
	const handleKeyDown = useCallback((event) => {
		if (event.key === 'Enter') {
			event.preventDefault();
			const button = document.querySelector('.guard-search-button');
			const mouseClickEvent = new MouseEvent('click', { bubbles: true, cancelable: true });// 需要冒泡！
			button.dispatchEvent(mouseClickEvent);
			window.removeEventListener('keydown', handleKeyDown);
		}
	}, []);
	// 注意：下面两个useEffect不要修改相对顺序
	useEffect(
		() => {
			window.removeEventListener('keydown', handleKeyDown);// 确保只有一个监听事件
			window.addEventListener('keydown', handleKeyDown);
		},
		[guardId, statusList, appId, guardName, startTime,
			endTime, productList, standardList, industryList,
			created_by, originList, projectList, responser, customerName]
	);
	// 监听是否被重置，并在重置后删除事件监听器
	useEffect(() => {
		window.removeEventListener('keydown', handleKeyDown);
		getGuardSheet(offset, limit);
	}, [reStart]);

	useEffect(() => {
		if (clickOption === 2) {
			getGuardSheet();
		}
	}, [calendarTime]);

	return (
		<Body>
			<Content>
				<Content.Header title="护航查询" operation={<GuideLinks />}></Content.Header>
				<Content.Body>
					<Alert type="info" style={{ marginTop: 8 }}>
            申请护航须知：如果是大型活动（比如双11、618、春晚等）需&nbsp;
						<span style={{ color: 'red' }}>至少提前14个工作日</span>
            &nbsp;发起护航需求申请流程，普通护航&nbsp;
						<span style={{ color: 'red' }}>至少提前3个工作日</span>&nbsp;发起。
					</Alert>
					<Table.ActionPanel>
						<Justify
							left={
								<>
									<Button
										type="primary"
										onClick={() => {
											aegis.reportEvent({
												name: 'Click',
												ext1: 'create-btn',
												ext2: ctxUser,
												ext3: '护航管理/护航查询',
											});
											window.open(`https://${(getProcessEnv() === 'production' || getProcessEnv() === 'production-abroad') ? '' : 'test-'}antool.woa.com/fe-base/antool-workbench/server-task-list`);
										}}
									>
										<div className='iconBtn'>
											<AddIcon />
											<span>新建护航</span>
										</div>
									</Button>
									<Bubble
										arrowPointAtCenter
										placement="top"
										content={<Button type="link" onClick={() => {
											window.open('https://doc.weixin.qq.com/slide/p3_Ad0AvAbaAPMgJ1KkCW2QJGiVmzdcK?scode=AJEAIQdfAAoZCAn1CwAJYAWQYMACo');
										}}>
                      Antool建单指引
										</Button>
										}
									>
										<HelpCircleIcon style={{ fontSize: 18, color: '#7d7d7d' }} />
									</Bubble>
									{isTest && <Button
										style={{ marginLeft: 10 }}
										type="primary"
										onClick={() => {
											history.push('/advisor/guard/create');
										}}
									>
                    云护航新建
									</Button>}
									<Divider style={{ borderLeft: '1px solid #d9d9d9' }} layout="vertical"></Divider>
									<Bubble
										arrowPointAtCenter
										placement="top"
										content={showMyGuard ? '点击查询所有护航' : '点击查询与我相关的护航'}
									>
										<span className={`myGuard ${showMyGuard ? 'selectedBtn' : ''} myGuardWrap`} onClick={() => {
											downloadRef.current.InitStatus();
											setShowMyGuard(val => !val);
										}}>
											{showMyGuard ? <BrowseIcon /> : <BrowseOffIcon />}
											<span>我的护航</span>
										</span>
									</Bubble>
									<Bubble
										arrowPointAtCenter
										placement="top"
										content={showFilter ? '折叠' : '展开更多筛选条件'}
									>
										<span style={{ marginLeft: 10 }} className={`myGuard ${showFilter ? 'selectedBtn' : ''}`} onClick={() => {
											setShowFilter(val => !val);
										}}>
											<FilterIcon />
											<span>筛选</span>
										</span>
									</Bubble>
								</>
							}
							right={
								<div className='rightWrap'>
									<Segment
										value={clickOption.toString()}
										onChange={(value) => {
											if (value !== `${clickOption}`) {
												if (value === '1') {
													setOffset(0);
													setStartTime(descParams.start_time);
													setEndTime(descParams.end_time);
												} else {
													setCalendarTime(moment());
													setShowFilter(false);
												}
												setClickOption(parseInt(value, 10));
											}
										}}
										options={moreOptions}
									/>
									<GuardListDownload
										ref={downloadRef}
										filterProps={{
											guardId,
											guardName,
											standardList,
											industryList,
											statusList,
											created_by,
											responser,
											customerName,
											appId,
											productList,
											originList,
											projectList,
											startTime,
											endTime,
											participants: showMyGuard ? ctxUser : '',
										}}
									/>
								</div>
							}
						/>
					</Table.ActionPanel>
					{showFilter && <Card>
						<Card.Body>
							<Row className="tea-guard-search">
								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        护航ID
											</Text>
										</Col>
										<Col span={18}>
											<Input
												value={guardId}
												onChange={(v) => {
													setGuardId(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        护航名称
											</Text>
										</Col>
										<Col span={18}>
											<Input
												value={guardName}
												onChange={(v) => {
													setGuardName(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        客户APPID
											</Text>
										</Col>
										<Col span={18}>
											<Input
												value={appId}
												onChange={(v) => {
													setAppId(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        客户名称
											</Text>
										</Col>
										<Col span={18}>
											<Input
												placeholder='支持按主、关联客户信息搜索'
												value={customerName}
												onChange={(v) => {
													setCustomerName(v);
												}}
												size="full" />
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        护航项目
											</Text>
										</Col>
										<Col span={18}>
											<SelectMultiple
												appearance="button"
												value={projectList}
												options={projectOptions}
												searchable
												allOption={{ value: 'ALL', text: 'ALL' }}
												onChange={(v) => {
													setProjectList(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        护航云产品
											</Text>
										</Col>
										<Col span={18}>
											<SelectMultiple
												listHeight={400}
												boxClassName='productListBox'
												appearance="button"
												options={productsOptions}
												value={productList}
												allOption={{ value: 'ALL', text: 'ALL' }}
												onChange={(v) => {
													setProductList(v);
												}}
												size="full"
												searchable
											/>
										</Col>
									</Row>
								</Col>

								{clickOption === 1 && <Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        护航时间
											</Text>
										</Col>
										<Col span={18}>
											<RangePicker
												clearable
												className="tea-guard-search-range-picker"
												style={{ float: 'right', width: '100%' }}
												value={[startTime, endTime]}
												onChange={(v) => {
													setStartTime(v[0]);
													setEndTime(v[1]);
												}}
											/>
										</Col>
									</Row>
								</Col>}

								{clickOption === 1 && <Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        状态
											</Text>
										</Col>
										<Col span={18}>
											<SelectMultiple
												appearance="button"
												value={statusList}
												// options={map2options(StatusDict)}
												options={getStatusOptions()}
												allOption={{ value: 'ALL', text: 'ALL' }}
												size="full"
												onChange={(v) => {
													setStatusList(v);
												}}
											/>
										</Col>
									</Row>
								</Col>}

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        提单人
											</Text>
										</Col>
										<Col span={18}>
											<Input
												value={created_by}
												onChange={(v) => {
													setCreated_by(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">
                        护航人员
											</Text>
										</Col>
										<Col span={18}>
											<Input
												placeholder='支持按TAM、专项人员搜索'
												value={responser}
												onChange={(v) => {
													setResponser(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6} >
											<Text theme="label" verticalAlign="middle">护航类型</Text>
										</Col>
										<Col span={18}>
											<SelectMultiple
												appearance="button"
												value={standardList}
												options={map2options(ShowStandardDict)}
												onChange={(v) => {
													setStandardList(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>

								<Col span={75}>
									<Row verticalAlign={'middle'}>
										<Col span={6} >
											<Text theme="label" verticalAlign="middle">行业</Text>
										</Col>
										<Col span={18}>
											<SelectMultiple
												appearance="button"
												value={industryList}
												options={industryOptions}
												onChange={(v) => {
													setIndustryList(v);
												}}
												size="full"
											/>
										</Col>
									</Row>
								</Col>
							</Row>
							<div style={{ margin: 10, textAlign: 'center' }}>
								<Button
									className='guard-search-button'
									style={{ margin: 10 }}
									type="primary"
									disabled={loading}
									onClick={() => {
										downloadRef.current.InitStatus();
										setOffset(0);
										getGuardSheet(0, limit);
										aegis.reportEvent({
											name: 'Click',
											ext1: 'guard-view-btn',
											ext2: ctxUser,
											ext3: '护航管理/护航查询',
										});
										window.removeEventListener('keydown', handleKeyDown);
									}}
								>
									查询
								</Button>
								<Button
									style={{ margin: 10 }}
									onKeyDown={(e) => {
										e.preventDefault();
									}}
									onClick={() => {
										downloadRef.current.InitStatus();
										setOffset(0);
										resetParams();
										setRestart(a => !a);
										window.removeEventListener('keydown', handleKeyDown);
									}}
								>
                  重置
								</Button>
							</div>
						</Card.Body>
					</Card>}
					{clickOption === 1 && <Card>
						<Card.Body>
							<div>
								<Table
									columns={guardColumns}
									records={guards}
									topTip={
										(loading || guards?.length === 0) && (
											<StatusTip status={loading ? 'loading' : 'empty'} />
										)
									}
									addons={[
										pageable({
											recordCount: total,
											pageSize: limit,
											pageIndex: offset / limit + 1,
											onPagingChange: (query) => {
												if (loading) {
													return;
												}
												setOffset((query.pageIndex - 1) * query.pageSize);
												setLimit(query.pageSize);
												getGuardSheet((query.pageIndex - 1) * query.pageSize, query.pageSize);
											},
										}),
									]}
									recordKey="GuardId"
								/>
							</div>
						</Card.Body>
					</Card>}
					{
						clickOption === 2
						&& <GuardCalendar
							calendarData={calendarData}
							guardData={guardData}
							projectDict={projectDict}
							timeChange={(time) => {
								setCalendarTime(time);
							}} />
					}
					{
						/* 下载报告弹窗 */
						<Modal size="auto" visible={visible} onClose={() => setVisible(false)} caption="评估报告下载">
							<Modal.Body>
								<span>请选择下载报告类型：</span>
								<div style={{ textAlign: 'center' }}>
									<br />
									<Segment
										value={reportType.toString()}
										onChange={value => setReportType(value)}
										options={[
											{
												text: (
													<>
														<Bubble error content={'内部报告不可直接透传客户'}>
															<div>内部评估报告EXCEL版</div>
														</Bubble>
													</>
												),
												value: 'PrivateCosUrl',
												style: { width: '200px', height: '80px', margin: '5px' },
											},
											{
												text: (
													<>
														<div>外部评估报告EXCEL版</div>
													</>
												),
												value: 'PublicCosUrl',
												style: { width: '200px', height: '80px', margin: '5px' },
											},
										]}
									/>
								</div>
							</Modal.Body>
							<Modal.Footer>
								<Button
									type="primary"
									onClick={(e) => {
										e.stopPropagation();
										window.open(currentReport[reportType]);
										setVisible(false);
									}}
								>
									<span>确定</span>
								</Button>
								<Button type="weak" onClick={() => setVisible(false)}>
									<span>取消</span>
								</Button>
							</Modal.Footer>
						</Modal>
					}
					{
						/* 删除护航单 */
						<Modal
							size="auto"
							visible={visibleDeleteConfirm}
							onClose={() => setVisibleDeleteConfirm(false)}
							caption="注意"
						>
							<Modal.Body>
								<div>
									<Text style={{ whiteSpace: 'pre-line' }}>
										{[
											'请确认要删除 ',
											<a>{currentDeleteMsg}</a>,
											' ? \n\n - 删除后不可恢复，本次护航活动终止。',
										]}
									</Text>
								</div>
							</Modal.Body>
							<Modal.Footer>
								<Button
									type="primary"
									onClick={() => {
										delGuardSheet();
										setVisibleDeleteConfirm(false);
									}}
								>
									<span>确定</span>
								</Button>
								<Button type="weak" onClick={() => setVisibleDeleteConfirm(false)}>
									<span>取消</span>
								</Button>
							</Modal.Footer>
						</Modal>
					}
					{/* 全屏遮罩 */}
					<Loading show={copyLoading} text={'复制中...'}></Loading>
				</Content.Body>
			</Content>
		</Body>
	);
}
