/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useContext } from 'react';
import { Card, Layout, message as tips, Menu, Text, Bubble, Icon } from '@tencent/tea-component';
import { useHistory, useAegisLog } from '@tea/app';
import { DescribeGuardSheet, DescribeGuardDashboard } from '@src/api/advisor/guard';
import { Filter, GuardParams } from '@src/types/advisor/guard';
import { GuardDescBaseInfo } from './GuardDescBaseInfo';
import { CollapsedContext } from '@src/components/collapsed-context';
import { ScanResult } from '@src/routes/advisor/pages/guard/components/ScanResult';
import { Report } from '@src/routes/advisor/pages/guard/Report';
import { setStorage } from '@src/utils/storage';
import GuardFinishInfoPanel from './guard-finish-panel';
import { cloneDeep } from 'lodash';
import GuardRightPanel from './GuardRightPanel';
import { fromAntool } from '@src/utils/common';
import { guardStatu } from '@src/routes/advisor/pages/guard/constants';
import moment from 'moment';
const { Body, Sider, Content } = Layout;

function getStatu(guard) {
	// 护航开始、结束时间
	const { StartTime, EndTime } = guard;
	const today = moment();
	const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
	const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
	if (today.isBefore(start)) {
		return 'prepare';
	} if (today.isBetween(start, end)) {
		return 'running';
	}
	return 'finish';
}

export function GuardSummary(match) {
	// 是否是通过antool访问
	const antool = fromAntool();
	const history = useHistory();
	const aegis = useAegisLog();
	const ctxUser = localStorage.getItem('engName');
	// 全局左侧菜单栏是否折叠，false表示折叠
	const { collapsed } = useContext(CollapsedContext);
	// 当前护航单数据
	const [currentGuard, setCurrentGuard] = useState<GuardParams>({});
	let tab = '';
	if (location.search?.split('tab')?.[1]?.split('=')?.[1]?.split('&')?.[0]) {
		tab = location.search.split('tab')[1].split('=')[1].split('&')[0];
		history.replace(`/advisor/guard/summary/${match.match.params.guardid}`);
	}
	// 菜单选中
	const [selected, setSelected] = useState(tab ? tab : 'detail');
	const [appids, setAppids] = useState([]);
	const [appidAuthorizedMap, setAppidAuthorizedMap] = useState(new Map<number | string, boolean>());
	const [clickedBack, setClickedBack] = useState(false);
	// 主账号授权
	const IsAuthorized = currentGuard?.ProductDesc?.length
		? (currentGuard?.ProductDesc?.filter(i => i.AppId === currentGuard.MainAppId)?.[0]?.IsAuthorized)
		: false;
	const isCanSeeReport = IsAuthorized && (!(currentGuard.Status != 48 && currentGuard.Status != 50));
	const isCanSeeResult = currentGuard.Status == 37 || currentGuard.Status == 48 || currentGuard.Status == 50;
	const grafanaExist = currentGuard.GuardDashboardExist;
	const dataMap = {
		detail: '护航管理/护航详情',
		result: '护航管理/巡检结果&应急预案',
		grafana: '护航管理/监控面板',
		report: '护航管理/日报/总结报告',
		arch: '护航管理/架构图',
	};

	useEffect(() => {
		getGuardSheet();
		return () => {
			if (!clickedBack) {
				setStorage('paramInfo', null);
			}
		};
	}, []);
	useEffect(() => {
		// 接入埋点
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: dataMap[selected],
			ext3: ctxUser,
		});
	}, [selected]);

	useEffect(() => {
		if (selected === 'grafana' && grafanaExist) {
			guardDashboard();
		}
	}, [selected, grafanaExist]);

	useEffect(() => {
		if (currentGuard.GuardId) {
			// 如果目标链接没有开启，则默认显示详情
			if ((selected === 'result' && !isCanSeeResult) || (selected === 'grafana' && !grafanaExist) || (selected === 'report' && !isCanSeeReport)) {
				setSelected('detail');
			}
		}
	}, [currentGuard]);

	// 监控面板自动授予登录人读权限
	const guardDashboard = async () => {
		try {
			const res = await DescribeGuardDashboard({
				Operator: ctxUser,
				GuardId: Number(match.match.params.guardid),
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询护航单详情
	const getGuardSheet = async () => {
		try {
			const filters: Array<Filter> = [
				{ Name: 'guard_id', Values: [match.match.params.guardid] },
			];
			const res = await DescribeGuardSheet({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset: 0,
				Limit: 10,
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const item = res.Guard[0] || {};
			// 回掉antool Iframe 通知护航单当前状态
			window.parent?.postMessage({
				Source: 'Guard',
				SourceId: item.GuardId,
				MsgType: 'GuardStatu',
				Submitted: item.Status !== 1,
			}, '*');
			// 适配antool，跳转草稿编辑页
			if (antool && item.Status === 1) {
				history.replace(`/advisor/guard/create?guardid=${match.match.params.guardid}&source=Antool`);
				return;
			}
			setStorage('guardItemInfo', {
				...cloneDeep(item),
				IsGuardDetail: true,
			});
			setCurrentGuard(item);

			const appids = [item.MainAppId].concat(item.RelatedAppId);
			setAppids(appids);
			const tmpMap = new Map();
			if ((item.ProductDesc || []).length) {
				item.ProductDesc.map((i) => {
					tmpMap.set(i.AppId, i.IsAuthorized);
				});
			}
			setAppidAuthorizedMap(tmpMap);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	return (
		<Body>
			{/* 全局的左侧菜单栏折叠状态，如果折叠，且当前选中监控面板时，整个Sider隐藏 */
				(!antool && (selected !== 'grafana' || !collapsed))
				&& <Sider>
					<Menu
						back={{
							title: <Bubble content={currentGuard.GuardName}>
								{
									currentGuard.GuardName
								}
							</Bubble>,
							description: `ID:${currentGuard.GuardId}`,
							onClick: () => {
								setClickedBack(true);
								history.push('/advisor/guard');
							},
						}}
						className={'guard-detail-menu'}
					>
						<Menu.Item
							title="护航详情"
							selected={selected === 'detail'}
							onClick={() => {
								setSelected('detail');
							}}
						/>
						{
							isCanSeeResult
								?	<Menu.Item
									title="巡检结果&应急预案"
									selected={selected === 'result'}
									onClick={() => {
										setSelected('result');
									}}
								/>
								:	<Menu.Item
									title={
										<div style={{ display: 'flex', alignItems: 'center' }}>
											<Text theme={'weak'}>巡检结果&应急预案</Text>
											<Bubble
												arrowPointAtCenter
												placement="right"
												content={'巡检在进行中，需要等“巡检结果审批（专项Owner）”完成后才可查看。'}
											>
												<Icon type="info" style={{ marginLeft: 4 }} />
											</Bubble>
										</div>
									}
									selected={selected === 'result'}
								/>
						}
						{grafanaExist ? (
							<Menu.Item
								title="监控面板"
								selected={selected === 'grafana'}
								onClick={() => {
									setSelected('grafana');
								}}
							/>
						) : (
							<Menu.Item
								title={
									<div style={{ display: 'flex', alignItems: 'center' }}>
										<Text theme={'weak'}>监控面板</Text>
										<Bubble
											arrowPointAtCenter
											placement="right"
											content={
												'如果账号未开通云顾问、对应产品未接入云护航或巡检任务未完成，则无法自动生成相应监控视图'
											}
										>
											<Icon type="info" style={{ marginLeft: 4 }} />
										</Bubble>
									</div>
								}
							/>
						)}
						{
							!isCanSeeReport
								?	<Menu.Item
									title={
										<div>
											<Text theme={'weak'}>日报/总结报告</Text>
											<Bubble
												arrowPointAtCenter
												placement="right"
												content={'已开通云顾问账号，需要等“巡检结果审批（专项Owner）”完成后：在护航期间才可查看日报，护航完成后才可查看总结报告。'}
											>
												<Icon type="info" style={{ marginLeft: 4 }} />
											</Bubble>
										</div>

									}
									selected={selected === 'report'}
									onClick={() => {
										if (!isCanSeeReport) {
											return;
										}
									}}
								/>
								:	<Menu.Item
									title='日报/总结报告'
									selected={selected === 'report'}
									onClick={() => {
										setSelected('report');
									}}
								/>
						}
					</Menu>
				</Sider>
			}
			{
				appids.length > 0 && <Content>
					{/* 根据菜单，显示不同页面 */}
					{/* 全局展示grafana */
						selected === 'grafana'
							? <iframe id="iframe_id" width="100%" height="100%" src={currentGuard.GrafanaDashboardUrl} ></iframe>
							:	selected === 'report'
								?	<div key={selected}><Report /></div>
								:	<Content.Body>
									<Card>
										<Card.Body>
											{
												selected === 'detail'
												&& <div key={selected}>
													<GuardDescBaseInfo
														appids={appids}
														guardId={match.match.params.guardid}
														guardBaseInfo={currentGuard}
														appidAuthorizedMap={appidAuthorizedMap}
													/>
												</div>
											}
											{
												selected === 'result' && <div key={selected}><ScanResult guardId={Number(match.match.params.guardid)} appid={Number(appids[0])} /></div>
											}
										</Card.Body>
									</Card>
									{
										selected === 'detail'
										&& getStatu(currentGuard) === guardStatu.FINISH
										&& <Card>
											<Card.Body>
												<GuardFinishInfoPanel
													guardBaseInfo={currentGuard}
													reloadGuardInfo={() => getGuardSheet()}
												 />
											</Card.Body>
										</Card>
									}
								</Content.Body>
					}
				</Content>
			}
			{
				(!antool && (selected === 'detail' && appids.length > 0))
				&& <Content className='RightContentWrap' style={{ width: 350, flex: 'none' }}>
					<Content.Body>
						{/* 右侧面板 */}
						<GuardRightPanel currentGuard={currentGuard}></GuardRightPanel>
					</Content.Body>
				</Content>
			}
		</Body>
	);
}
