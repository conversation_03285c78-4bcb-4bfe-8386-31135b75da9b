/* eslint-disable no-nested-ternary */
import React, { useState, useEffect, useRef } from 'react';
import { Row, Col, message, Collapse, Bubble, Icon, Text, Form } from '@tencent/tea-component';

import { GuardParams, ProductDescItem, ProductTemplateItem } from '@src/types/advisor/guard';
import { DescribeGuardMapGenStatus, DescribeSpecialAfterSaleArchApproval } from '@src/api/advisor/guard';
import { getProductsGroups } from '@src/api/advisor/estimate';
import DescInstanceTable from './DescInstanceTable';
import _ from 'lodash';
import { GENERATE_STATUS } from '@src/utils/constants';


interface Props {
	appid: number,
	authorized?: boolean,
	currentGuard: GuardParams;
}

export function ConfirmDetail({ appid, authorized, currentGuard }: Props) {
	// 是否是国际站
	const isAbroadSite = localStorage.getItem('site') === 'sinapore';
	// 是否是自助护航
	const isSelfEscort = currentGuard.Standard === 3;
	const createEnv = ['ISA', 'CONSOLE'];
	// eslint-disable-next-line max-len
	const arch = currentGuard?.CloudGuardBaseInfoOtherPlatform?.find(i => createEnv.includes(i.Platform) && i.AppId === appid) || {};
	const archUrl = `${location.origin}/advisor/new-architecture/architecture/${arch?.PlatformUniqueId}?appid=${appid}&plugin=cloud-escort-sdk`;

	// 特批原因
	const [specialReason, setSpecialReason] = useState('');
	// 生图失败原因
	const [failReason, setFailReason] = useState('');
	// 资源信息收集
	const [productDict, setProductDict] = useState({});                                 // 产品中文名称
	const [relateProducts, setRelateProducts] = useState<Array<string>>([]);            // 关联云产品
	const [relateZhProducts, setRelateZhProducts] = useState<Array<string>>([]);        // 关联云产品（中文名）
	const [activeIds, setActiveIds] = useState<Array<string>>([]);                      // 展开的云产品
	const [unSupportedProducts, setUnSupportedProducts] = useState<Array<string>>([]);  // 暂未支持的产品

	// 护航产品策略信息
	const [productTemplate, setProductTemplate] = useState<Array<ProductTemplateItem>>(currentGuard.ProductTemplate || []);
	// 实例维度的护航容量策略
	const [instancePolicyDict, setInstancePolicyDict] = useState<any>({});
	// 产品描述信息
	const [productDesc, setProductDesc] = useState<Array<ProductDescItem>>(currentGuard.ProductDesc);
	// 备注
	const [productComment, setProductComment] = useState({});
	// 手工实例列表（未开通账号的产品、开通账号&未接入云顾问或云护航的产品）
	const [productInstances, setProductInstances] = useState({});
	// 未填写手工实例
	const noSetInstances = <Text style={{ color: 'LightGray' }} verticalAlign="middle">未填写实例</Text>;
	// 未填写备注
	const noSetComment = <Text style={{ color: 'LightGray' }} verticalAlign="middle">未填写备注</Text>;
	// 未填写
	const noSet = <Text style={{ color: 'LightGray' }} verticalAlign="middle">未填写</Text>;
	// 已经展开过的产品
	const [requestedProducts, setRequestedProducts] = useState([]);
	// cdn是否需要展示实例信息
	const [cdnInstancePolicy, setCdnInstancePolicy] = useState(false);
	useEffect(() => {
		setRequestedProducts(Array.from(new Set([...requestedProducts, ...activeIds])));
	}, [activeIds]);
	// 获取云产品清单
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: appid,
				Env: 'all',
				TaskType: 'guardTaskType',
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const tmpProductsOptions = [];
			for (const i in res.ProductDict) {
				tmpProductsOptions.push({ value: i, text: res.ProductDict[i] });
			}
			setProductDict(res.ProductDict);

			// 获取产品列表
			const products = [];
			const productDesc = currentGuard.ProductDesc || [];
			if (productDesc) {
				productDesc.map((i) => {
					if (products.indexOf(i.Product) === -1 && i.AppId === appid) {
						products.push(i.Product);
					}
				});
			}
			setRelateProducts(products);

			// 获取未支持产品
			setUnSupportedProducts(res.UnSupportedProducts);

			// 获取产品中文列表
			const zhProducts = [];
			const productDict = res.ProductDict;
			products.map((i) => {
				if (zhProducts.indexOf(i) === -1) {
					zhProducts.push(productDict[i]);
				}
			});
			setRelateZhProducts(zhProducts);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 展示产品策略列表
	function getProductPolicyList(appid, product) {
		const leftCols = [];
		const rightCols = [];

		const target = productTemplate.filter(i => i.AppId == appid && i.Product == product);
		if (target.length == 0) {
			return <></>;
		}

		const ppItem = target[0];
		ppItem.Policy.map((policy, idx) => {
			// cdn 需求类型适配
			if (policy.MetricName === 'CDNRequirementType' && policy.Value === '突发护航需求' && !cdnInstancePolicy) {
				setCdnInstancePolicy(true);
			}
			const cell =                <section>
                	<Row verticalAlign={'middle'}>
                		<Col span={12} >
                			{policy.IsRequired && <Text style={{ color: 'red' }} verticalAlign="middle">*</Text>}
                			<Text theme="label" verticalAlign="middle">{policy.CNName || ''}</Text>
                		</Col>
                		<Col span={36}>
                			{policy.Value || noSet}
                		</Col>
                	</Row>
			</section>;
			if (idx % 2) {
				rightCols.push(cell);
			} else {
				leftCols.push(cell);
			}
		});

		const tmp =            <>
            	<Row gap={30}>
            		<Col>
            			{leftCols}
            		</Col>
            		<Col>
            			{rightCols}
            		</Col>
            	</Row>
            	<hr />
		</>;

		return tmp;
	}

	const resourceRef = useRef(null);

	// 查询审批原因
	async function getReason() {
		try {
			const params = {
				GuardID: currentGuard.GuardId,
				Operator: localStorage.getItem('engName') || '',
				AppId: appid,
			};
			const res = await DescribeSpecialAfterSaleArchApproval(params);
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setSpecialReason(res?.SpecialAfterSaleArchApproval?.Reason || '');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	}

	// 查询生图状态
	function getMapStatus() {
		DescribeGuardMapGenStatus({
			taskId: `${currentGuard.GuardId}`,
			status: '',
			AppId: appid,
		})
			.then((res) => {
				// 生图失败
				if (res.Status === GENERATE_STATUS.FAIL) {
					setFailReason(res.ErrorMessage || '创建架构图失败');
				}
			})
			.catch((err) => {
				console.log(err);
			});
	}

	// init
	useEffect(() => {
		getProductsGroupsInfo();
		// 非自助护航才查询
		if (!isSelfEscort && !isAbroadSite) {
			getReason();
			getMapStatus();
		}
	}, []);

	useEffect(() => {
		const currProductDesc = (productDesc || []).filter((i) => {
			if (i.AppId === appid) {
				return i;
			}
		});
		const tmpComment = {};
		const tmpInstances = {};
		currProductDesc.map((i) => {
			tmpComment[i.Product] = i.Comment;
			tmpInstances[i.Product] = i.InstanceIds;
		});
		setProductComment(tmpComment);
		setProductInstances(tmpInstances);
	}, [productDesc]);

	return (
		<div style={{ marginTop: 10 }}>
			<section>
				<Form readonly>
					{(!isSelfEscort && !isAbroadSite && (arch?.PlatformUniqueId || failReason)) && <Form.Item label="*已关联架构图">
						<Form.Text>{arch?.PlatformUniqueId
							? <Text theme="primary" style={{ cursor: 'pointer' }} onClick={ () => {
								window.open(archUrl);
							}}>{ arch?.PlatformUniqueName }</Text>
							: (failReason ? failReason : (specialReason ? `云架构协作授权未开通，申请特批，原因：（${specialReason}）` : ''))
						}
						</Form.Text>
					</Form.Item>}
					<Form.Item></Form.Item>
					<Form.Item></Form.Item>
					<Form.Item label="*涉及云产品">
						<Form.Text>{(relateZhProducts || []).join('、')}</Form.Text>
					</Form.Item>
					<Form.Item></Form.Item>
					<Form.Item></Form.Item>
					<Form.Item></Form.Item>
				</Form>
			</section>

			<Collapse className="intlc-assessment-tabitem__content" activeIds={activeIds} onActive={(v) => {
				setActiveIds(v);
			}} destroyInactivePanel={false}>
				{
					relateProducts.map((product) => {
						const bubbleNAProduct = < Bubble
							key={product}
							arrowPointAtCenter
							placement="right"
							content="该产品未接入云护航，仅分配护航人员"
						>
							{unSupportedProducts.includes(product) && < Icon type="info" style={{ marginLeft: 2 }} />}
						</Bubble>;
						// 全部实例数量
						const num = (currentGuard.InstanceTemplateCount && currentGuard.InstanceTemplateCount[appid])
							? (currentGuard.InstanceTemplateCount[appid][product] || 0)
							: 0;
						// 重点关注实例数量
						const importantNum = (currentGuard.InstanceImportantTemplateCount && currentGuard.InstanceImportantTemplateCount[appid])
							? (currentGuard.InstanceImportantTemplateCount[appid][product] || 0)
							: 0;
						const instanceLengthStr = authorized && !unSupportedProducts.includes(product) && <Text key={`${product}text`} style={{ color: 'gray' }}>{` - 全部 ${num} 个，重点关注 ${importantNum} 个`}</Text>;

						return <div key={product}>
							<Collapse.Panel
								style={{ marginTop: 10, display: unSupportedProducts.includes(product) ? 'inline-block' : '' }}
								key={product}
								id={product}
								title={[productDict[product], bubbleNAProduct, instanceLengthStr]}
							>
								{/* 场景一：授权账号、接入云顾问的产品 */}
								{(requestedProducts.includes(product) && authorized && !unSupportedProducts.includes(product))
                                    && <DescInstanceTable
                                    	cdnInstancePolicy={cdnInstancePolicy}
                                    	product={product}
                                    	instancePolicys={instancePolicyDict[product] || []}
                                    	key={product}
                                    	appid={appid}
                                    	ref={resourceRef}
                                    	guardId={currentGuard.GuardId}
                                    />
								}
								{/* 场景二：未授权账号，或已授权但未接入云顾问的产品 */}
								{(!authorized || unSupportedProducts.includes(product))
                                    && <section>
                                    	<Row verticalAlign="middle" style={{ marginTop: 10, marginBottom: 10 }}>
                                    		<Col span={12} >
                                    			<Text theme="label">重点护航实例</Text>
                                    		</Col>
                                    		<Col span={36} >
                                    			<Text style={{ whiteSpace: 'pre-line' }}>{productInstances[product] ? productInstances[product] : noSetInstances}</Text>
                                    		</Col>
                                    	</Row>
                                    </section>
								}
								<section>
									<Row verticalAlign="middle" style={{ marginTop: 10, marginBottom: 10 }}>
										<Col span={12} >
											<Text theme="label">备注</Text>
										</Col>
										<Col span={36} >
											<Text>{productComment[product] ? productComment[product] : noSetComment}</Text>
										</Col>
									</Row>
								</section>
								<section>
									{getProductPolicyList(appid, product)}
								</section>
							</Collapse.Panel>
						</div>;
					})
				}
			</Collapse>
		</div>
	);
}

