/* eslint-disable no-restricted-syntax */
import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef } from 'react';
import {
	Button,
	Card,
	message as tips,
	Select,
	SelectMultiple,
	message,
	Checkbox,
	Modal,
	Switch,
	Icon,
	TimeRangePicker,
	Tooltip,
	ExternalLink,
	Tree,
	Alert,
	Collapse,
	Row,
	Col,
	DatePicker,
	Text,
	Input,
	List,
	Tag,
	Bubble,
	Table,
	TagSearchBox,
	StatusTip,
} from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import moment, { isMoment, Moment } from 'moment';
import { getUserInfo, getAppIDByUin } from '@src/api/common';
import { getProductsGroups } from '@src/api/advisor/estimate';
import {
	DescribeGuardSheet,
	GetAccountInfoByFields,
	CreateGuardSheet,
	UpdateGuardSheet,
	DescribeGuardProjects,
	DescribeGuardServiceDetails,
	DescribeLastAdvisorTask,
	DescribeGuardArchList,
	BindGuardSheet,
	DescribeOnsiteGuardConfig,
	UpdateOnsiteGuardApprovalDirector,
	GetListPluginConfigByAdmin,
} from '@src/api/advisor/guard';
import MyInput from '@src/components/MyInput';
import AppIdsInput from '@src/components/AppIdsInput';
import { CustomerName } from '../components/CustomerName';
import { GuardParams } from '@src/types/advisor/guard';
import _ from 'lodash';
import { getProcessEnv } from '../../../../../../app/utils';
import { TextArea } from '@tea/component/input';
import { useHistory } from '@tea/app';
import RTXPicker from '@tencent/qmfe-yoa-react-ui/es/RTXPicker/index';
import { t } from '@tencent/tea-app/i18n';
const { radioable, pageable } = Table.addons;
const { RangePicker } = DatePicker;

interface Props {
	currentGuard?: GuardParams;
	handleUpdateSteps?: Function;
	handleUpdateStepsDisable?: Function;
	handleUpdateCurrentGuard?: Function;
	handleGetGuardSheet?: Function;
	isEdit?: Boolean;
	editList?: Array<string>,
	isOnSiteChange?: Function
}
// 紧急护航描述
const StandardDesc = { Standard: 3, Limit: 2, Current: 0 };
// 判断时间是否小于两个工作日（含当天、不计入周末）
function isLessThanTwoWorkingDays(time) {
	// 目标时间
	const targetDate = moment(moment(time).format('YYYY-MM-DD'));
	// 当前时间
	const currentDate = moment(moment().format('YYYY-MM-DD'));
	let workingDays = 0;
	while (currentDate.isSameOrBefore(targetDate, 'day')) {
		if (currentDate.day() !== 0 && currentDate.day() !== 6) {
			workingDays += 1;
		}
		currentDate.add(1, 'day');
	}
	// 记录当前剩余工作日
	StandardDesc.Current = workingDays;
	return workingDays <= 2;
}

const TIME_FORMAT_HM = 'HH:mm';
// 返回左闭右闭有序列表
const range = (s, t) => Array(t - s + 1)
	.fill(0)
	.map((_, i) => s + i);

// 把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
const simpleMarkdownToHTML = (input = ''): string => {
	const reg = new RegExp(
		'(\\[[\u4e00-\u9fa5_a-zA-Z0-9,、/-\\s]+\\])(\\((\\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\\))',
		'g'
	);
	let result = input.replace(reg, (match, text, link) => `<a href="${link.slice(1, -1)}" target="_blank">${text.slice(1, -1)}</a>`);

	// 无序列表markdown语法转换
	if (result.includes('- ')) {
		result = result
			.split('- ')
			.filter(r => r.length)
			.map((r, index) => (!index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`))
			.join('');
	}
	return result;
};
// 构造树形结构
function buildNestedArray(arr, idProp, parentIdProp) {
	const nestedArray = [];
	const map = arr.reduce((acc, item) => {
		acc[item[idProp]] = { ...item };
		return acc;
	}, {});
	for (const item of arr) {
		if (item[parentIdProp] in map) {
			const parent = map[item[parentIdProp]];
			parent.children = parent.children || [];
			parent.children.push(map[item[idProp]]);
		} else {
			nestedArray.push(map[item[idProp]]);
		}
	}
	return nestedArray;
}

// 根据id查找数据
const findObjectById = (tree, id) => {
	for (const node of tree) {
		if (node.id === id) {
			return node;
		}
		if (node.children) {
			const foundObject = findObjectById(node.children, id);
			if (foundObject) {
				return foundObject;
			}
		}
	}
	return null;
};

// 迁云护航单数量限制
const repeatGuardLimit = 3;

enum ConfirmAuthorStatus {
	'NONE' = 0,
	'CONFIRM' = 1,
	'NOAUTHOR'= 2,
}

let timeoutId: any = '';

function BaseInfo(
	{
		currentGuard,
		handleUpdateSteps,
		handleUpdateStepsDisable,
		handleUpdateCurrentGuard,
		handleGetGuardSheet,
		isEdit = false,
		editList = [],
		isOnSiteChange,
	}: Props,
	ref
) {
	const isConfirmAuthor = currentGuard?.CustomerAuthStatus === ConfirmAuthorStatus.CONFIRM;
	// 是否是紧急护航
	const [emergency, setEmergency] = useState(false);
	const StandardDict = [
		{ value: '0', text: '标准护航', disabled: emergency },
		{ value: '1', text: '售中迁云割接护航', tooltip: '迁云割接场景不支持填写多个APPID', disabled: emergency },
		{ value: '3', text: '自助护航' },
	];
	const history = useHistory();
	// 产品下拉框选项
	const [productsOptions, setProductsOptions] = useState([]);

	// 护航项目选项
	const [projectOptions, setProjectOptions] = useState([]);

	// 护航单id
	const [guardId, setGuardId] = useState<number>(currentGuard.GuardId || 0);

	// 新建护航单参数
	const [guardName, setGuardName] = useState<string>(currentGuard.GuardName || ''); // 护航名称
	// 护航重点时间
	const [keyTime, setKeyTime] = useState(currentGuard.ImportantGuardTime || []);
	// 护航开始时间 开始时间默认+2h、结束时间默认+1d  MM:SS 默认 00:00
	const [startTime, setStartTime] = useState<Moment>(currentGuard.StartTime ? moment(currentGuard.StartTime) : moment().add(2, 'hours')
		.startOf('hour'));
	const [startTimeIsBefore, setStartTimeIsBefore] = useState(false);
	const [endBeforeStart, setEndBeforeStart] = useState(false);
	const [endTime, setEndTime] = useState<Moment>(currentGuard.EndTime ? moment(currentGuard.EndTime) : moment().add(1, 'days')
		.startOf('hour')); // 护航结束时间
	useEffect(() => {
		// 是编辑状态并且开始时间或者结束时间能编辑，要判断下开始时间是否小于结束时间
		if (isEdit && (noEdit('startTime') === false || noEdit('endTime') === false)) {
			setEndBeforeStart(moment(endTime).isBefore(moment(startTime)));
		}
	}, [startTime, endTime]);
	const [onsiteDates, setOnsiteDates] = useState<Array<string>>(currentGuard.OnsiteTime || []); // 驻场日期列表
	const [startClock, setStartClock] = useState<Moment>(moment(currentGuard.StartClock ? currentGuard.StartClock : '20:00', TIME_FORMAT_HM)); // 播报开始时间
	const [endClock, setEndClock] = useState<Moment>(moment(currentGuard.EndClock ? currentGuard.EndClock : '23:59', TIME_FORMAT_HM)); // 播报结束时间

	const [onsiteProduct, setOnsiteProduct] = useState<Array<string>>(currentGuard.OnsiteProducts || []); // 需要驻场支持的云产品
	const [project, setProject] = useState<string>(currentGuard.Project >= 0 ? currentGuard.Project.toString() : '0'); // 护航项目
	const [appId, setAppId] = useState<string>(currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : ''); // 客户APPID
	const [relatedAppId, setRelatedAppId] = useState<Array<number>>([]); // 关联客户appid列表 避免多次刷新，初始设置为空
	const [standard, setStandard] = useState<string>(currentGuard.Standard >= 0 ? currentGuard.Standard.toString() : '0'); // 护航类型
	// 关联客户名称，顺序和appid列表一致
	// eslint-disable-next-line max-len
	const [relatedCustomerNames, setRelatedCustomerNames] = useState<Array<string>>(currentGuard.RelatedCustomerNames || []);
	const [relatedCustomerNamesDict, setRelatedCustomerNamesDict] = useState({}); // 用来标记appid对应客户名称
	const [customerName, setCustomerName] = useState<string>(currentGuard.CustomerName || ''); // 客户名称
	const [customerContacts, setCustomerContacts] = useState<string>(currentGuard.CustomerContacts || ''); // 客户接口人名称
	const [customerPhone, setCustomerPhone] = useState<string>(currentGuard.CustomerPhone || ''); // 客户接口人联系方式
	const [confirmAuthor, setConfirmAuthor] = useState<boolean>(isConfirmAuthor); // 确认客户授权巡检

	const [currentGuardRelatedAppId, setCurrentGuardRelatedAppId] = useState<string>(currentGuard.RelatedAppId ? currentGuard.RelatedAppId.join(' ') : ''); // 关联客户appid列表，从草稿中读取
	const [appIdNAList, setAppIdNAList] = useState<Array<number>>([]); // 未授权的APPID列表
	const [appIdNA, setAppIdNA] = useState<string>(''); // 未授权的主APPID
	const [relatedAppIdNA, setRelatedAppIdNA] = useState<Array<number>>([]); // 未授权的关联APPID
	const [appIdNANote, setAppIdNANote] = useState<string>(''); // 提示未授权APPID信息
	const [visibleAppIdNANote, setVisibleAppIdNANote] = useState<boolean>(false); // 提示未授权APPID信息
	const [residentSupport, setResidentSupport] = useState(false);
	const [uin, setUin] = useState<string>(''); // 转换源UIN
	const [appIdTransfered, setAppIdTransfered] = useState<number>(0); // 转换目标APPID
	const [visibleUinTransfer, setVisibleUinTransfer] = useState(false); // UIN转换

	const [appIdNoAfterSaleList, setAppIdNoAfterSaleList] = useState<Array<number>>([]); // 未转售后的APPID列表
	const [appIdNoAfterSale, setAppIdNoAfterSale] = useState<string>(''); // 未转售后的主APPID
	const [relatedAppIdNoAfterSale, setRelatedAppIdNoAfterSale] = useState<Array<number>>([]); // 未转售后的关联APPID
	const [noAfterSaleNote, setNoAfterSaleNote] = useState<string>(''); // 提示未转售后APPID信息
	const [visibleNoAfterSaleNote, setVisibleNoAfterSaleNote] = useState<boolean>(false); // 提示未转售后APPID信息
	// 新开通用户的信息
	const [notCreateInfo, setNotCreateInfo] = useState<any>({});
	// 新开通用户弹框
	const [notCreateVisible, setNotCreateVisible] = useState<boolean>(false);

	// 必选提示
	const [guardTimeVisible, setguardTimeVisible] = useState<boolean>(false); // 护航时间输入框
	const [appidsVisible, setAppidsVisible] = useState<string>(''); // 关联实例报错提示
	const [projectVisible, setProjectVisible] = useState<boolean>(false); // 护航项目勾选提示
	const [confirmAuthorVisible, setConfirmAuthorVisible] = useState<boolean>(false); // 确认授权勾选提示
	const [keyGuardVisible, setKeyGuardVisible] = useState<boolean>(false); // 护航重点时间勾选提示

	// 当前登录rtx
	const [rtx, setRtx] = useState<string>('');
	const [closeNetworkDemand, setCloseNetworkDemand] = useState(currentGuard.ClosedNetworkDemand || '');
	// 新增字段--护航背景及需求
	const [StatementOfNeeds, setStatementOfNeeds] = useState(currentGuard.StatementOfNeeds || '');
	// 服务目录
	const [treeData, setTreeData] = useState([]);
	// 默认勾选的服务
	const [initServiceId, setInitServiceId] = useState([]);
	// 必填的服务
	const [requiredServiceId, setRequiredServiceId] = useState([]);
	// 当前勾选的服务
	const [selectIds, setSelectIds] = useState([]);
	// 迁云重复的单子
	const [repeatGuard, setRepeatGuard] = useState([]);
	const InputRef = useRef(null);
	// 未开通云顾问的appid
	const [noList, setNoList] = useState([]);
	// 未转售后的appid
	const [noSaleList, setNoSaleList] = useState([]);
	// 自助护航提示appid
	const [noSelfService, setNoSelfService] = useState([]);
	// 云护航插件uin白名单
	const [isWhiteListUin, setIsWhiteListUin] = useState(false);
	// appId对应的uin
	const [userUin, setUserUin] = useState('');
	// 是否驻场护航
	const [isOnSite, setIsOnSite] = useState(false);
	const attributes: any = [
		{
		  type: 'input',
		  key: 'SearchKey',
		  name: '架构图标题',
		},
		{
		  type: 'input',
		  key: 'ArchId',
		  name: '架构ID',
		},
	  ];
	// 搜索条件
	const [filterInfo, setFilterInfo] = useState([]);
	// 架构图列表
	const [archList, setArchList] = useState([]);
	// 选择的架构图
	const [selectedArchId, setSelectedArchId] = useState('');
	// 架构图列表列名
	const archListColumns = [
		{
			key: 'ArchName',
			header: '架构图标题',
		},
		{
			key: 'ArchId',
			header: '架构ID',
		},
		{
			key: 'CreateSource',
			header: '来源',
			render: item => (item.CreateSource === 'console' ? '客户创建' : '运营端发送'),
		},
		{
			key: 'GuardStatus',
			header: '护航状态',
			render: item => initGuardStatu(item),
		},
		{
			key: 'IsRelateTaskNum',
			header: '关联任务单',
			render: item => (item.IsRelateTaskNum ? '已关联' : '未关联'),
		},
		{
			key: 'operate',
			header: '操作',
			// eslint-disable-next-line no-nested-ternary
			render: item => (item.IsRelateTaskNum
				? <Button
					style={{ textDecoration: 'underline', cursor: 'pointer' }}
					type="link"
					onClick={() => {
						window.open(`https://${(getProcessEnv() === 'production' || getProcessEnv() === 'production-abroad') ? '' : 'test-'}antool.woa.com/fe-base/antool-page/visit/info?taskId=${item.RelateTaskNum}&processInstanceId=${item.RelateUniqueId}`);
					}}>
				已关联任务单{item.RelateTaskNum}
			  </Button>
				: (item.GuardStatus === 0 || item.GuardStatus === 1) ?  <Text theme="primary">提交后将进入架构图完善护航信息</Text>
					:  <Text theme="success">可直接提交</Text>),
			width: 200,
		},
	];

	// 查询接口常规参数
	const [loading, setLoading] = useState<boolean>(false);
	const [total, setTotal] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(20);
	// 驻场护航配置
	const [onsiteGuardConfig, setOnsiteGuardConfig] = useState<any>({});
	// 总监
	const [leaderName, setLeaderName] = useState('');
	// 总监
	const [leaderNameVisible, setLeaderNameVisible] = useState(false);
	// 当前选择驻场天数 是否大于单次上限
	const [isMoreThanOne, setIsMoreThanOne] = useState(false);
	// 当前选择驻场天数 是否大于单次上限
	const [isMoreThanYear, setIsMoreThanYear] = useState(false);

	useEffect(() => {
		if (onsiteGuardConfig.TamDirector) {
			setIsMoreThanOne(onsiteDates?.length > (onsiteGuardConfig?.OneGuardMaxOnsiteDays || 0));
			setIsMoreThanYear((onsiteDates?.length || 0) + (onsiteGuardConfig?.AppidYearOnsiteDays || 0)
			  > (onsiteGuardConfig?.OneYearMaxOnsiteDays || 0) && onsiteDates?.length > 0);
		}
	}, [onsiteDates, onsiteGuardConfig]);

	useEffect(() => {
		// 发送请求
		isOnSite &&	getArchList();
	}, [isOnSite, filterInfo]);

	useEffect(() => {
		clearTimeout(timeoutId);
		if (rtx) {
			// 500ms 延迟
			timeoutId = setTimeout(() => {
			  getDescribeOnsiteGuardConfig();
			}, 500);
		  }
	}, [rtx, appId]);

	// 获取驻场护航配置
	async function getDescribeOnsiteGuardConfig() {
		setLeaderName('');
		try {
			const res = await DescribeOnsiteGuardConfig({
				AppId: parseInt(appId, 10) || 0,
				Operator: rtx,
				GuardId: guardId || 0,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setOnsiteGuardConfig(res.OnsiteGuardConfig || {});
			setLeaderName(res?.OnsiteGuardConfig?.CreateDirector);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 更新总监
	async function updateLeader(id) {
		if (!isMoreThanOne && !isMoreThanYear) return;
		try {
			const res = await UpdateOnsiteGuardApprovalDirector({
				AppId: parseInt(appId, 10) || 0,
				GuardId: id,
				CreateDirector: leaderName,
				TamDirector: onsiteGuardConfig?.TamDirector,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 获取总监审批提示
	function leaderTip() {
		const reason = t('- {{attr0}} {{attr1}}需发起人总监+售后总监审批', { attr0: isMoreThanOne ? t('驻场护航天数大于{{attr0}}天，', { attr0: onsiteGuardConfig?.OneGuardMaxOnsiteDays }) : '', attr1: isMoreThanYear ? t('驻场护航天数累计超{{attr0}}天，', { attr0: onsiteGuardConfig?.OneYearMaxOnsiteDays }) : '' });
		return <>
			<div>{reason}</div>
			<div>{t('- 审批人：{{attr0}};{{attr1}}', { attr0: onsiteGuardConfig.CreateDirector, attr1: onsiteGuardConfig.TamDirector })}</div>
			<div>{t('（发起人总监若有误可修改）')}</div>
		</>;
	}

	// 查询架构图列表
	async function getArchList(PageNumber = 1, PageSize = pageSize) {
		// 在重新查询前清空选中
		setSelectedArchId('');
		setLoading(true);
		// 搜索条件组装
		const Filters = [];
		filterInfo.forEach((i) => {
			const Name = i.attr?.key || 'SearchKey';
			const Values = [];
			i.values.forEach((j) => {
				if (Values.indexOf(j.name) === -1) {
					  Values.push(j.name);
				}
			});
			Filters.push({ Name, Values });
		});
		try {
			const res = await DescribeGuardArchList({
				AppId: parseInt(appId, 10),
				PageNumber,
				PageSize,
				Filters,
			});
			if (res.Error) {
				setLoading(false);
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setTotal(res.TotalCount);
			setArchList(res.GuardArchList || []);
			setLoading(false);
		} catch (err) {
			setLoading(false);
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 判断当前护航状态
	function initGuardStatu(guardInfo) {
		const strMap = {
			0: '未在护航',
			1: '护航前准备',
			2: '护航中',
		};
		const iconMap = {
			0: 'not-escorting',
			1: 'before-escorting',
			2: 'escorting',
		};
		// 护航开始、结束时间、护航单状态
		const { StartTime, EndTime, GuardStatus } = guardInfo;
		let status = 0;
		const today = moment();
		const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
		const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
		// 没有草稿单、护航单过期、有草稿单并未过期
		if (!GuardStatus || today.isAfter(end) || GuardStatus < 0 || (GuardStatus === 1 && !today.isAfter(end))) {
			status = 0;
		} else {
			if (today.isBefore(start)) {
				// 护航前准备
				status = 1;
			} else {
				// 护航中
				status =  2;
			}
		}
		return <>
			<div className='guardStatusWrap'><IconTsa type={iconMap[status]} /><span>{strMap[status]}</span></div>
		</>;
	}

	useEffect(() => {
		// 记录当前未开通云顾问的账号
		setNoSelfService(noList.filter((i: any) => [Number(appId), ...relatedAppId].includes(i)));
	}, [appId, relatedAppId, noList]);

	useEffect(() => {
		// 填写的appId中是否有售后的账号
		const hasAfterSale = [Number(appId), ...relatedAppId].some(element => !noSaleList.includes(element));
		if (standard === '3') {
			if (!hasAfterSale) {
				// 如果有未转售后，就要去掉护航服务群勾选
				setSelectIds(selectIds.filter(i => i !== '27'));
			} else {
				// 如果是紧急，就勾上护航服务群
				if (emergency) {
					setSelectIds([...selectIds, '27']);
				}
			}
			// 设置勾选框禁用状态
			setTreeData((val) => {
				const result = _.cloneDeep(val);
				const groupData = findObjectById(result, '27') || {};
				groupData.disableSelect = !hasAfterSale;
				return result;
			});
		}
	}, [appId, relatedAppId, noSaleList]);

	useEffect(() => {
		if (standard === '1') {
			// 迁云割接护航时清空关联AppId
			InputRef.current.setInputValue('');
		}
		// 护航类型变化，去查询对应的服务
		getEscortTypeService();
	}, [standard]);

	useEffect(() => {
		// 只有客户保存过服务并且当前护航类型和已保存的护航类型一致，才会展示客户保存过的服务
		if (currentGuard.GuardService?.length && currentGuard.Standard === parseInt(standard)) {
			// 客户保存的服务和必填服务并集
			const savedServiceId = currentGuard.GuardService.map(i => String(i.ID));
			setSelectIds(Array.from(new Set([...savedServiceId, ...requiredServiceId])));
		} else {
			setSelectIds(initServiceId);
		}
	}, [initServiceId]);

	useEffect(() => {
		// 判断是否是紧急护航,如果是，护航类型变成自助护航
		// const result = isLessThanTwoWorkingDays(startTime) || false
		const result = false;
		setEmergency(result);
		if (result) {
			message.warning({ content: '为保障服务人效，护航开始时间小于2个工作日（含当天），仅支持自助护航' });
			setStandard('3');
			// 如果全是未转售后就不勾选
			if ([Number(appId), ...relatedAppId].some(element => !noSaleList.includes(element))) {
				setSelectIds([...selectIds, '27']);
			}
		} else {
			// 如果是自助护航，去掉护航服务群勾选
			if (standard === '3') {
				setSelectIds(selectIds.filter(i => i !== '27'));
			}
		}

		// 编辑状态，并且开始时间不能编辑，不判断开始时间是否小于当前时间
		if (isEdit && noEdit('startTime')) {
			setStartTimeIsBefore(false);
		} else {
			const isBefore = moment(startTime).isBefore(moment());
			setStartTimeIsBefore(isBefore);
		}
	}, [startTime, JSON.stringify(editList)]);

	// 查询护航类型对应的服务
	async function getEscortTypeService() {
		setTreeData([]);
		try {
			const res = await DescribeGuardServiceDetails({
				AppId: **********, // 内部账号，产品信息最全
				Standard: parseInt(standard),
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const temp = (res.Data || []).map(i => ({
				id: String(i.Id),
				parentId: String(i.ParentId),
				content: <div style={{ display: 'flex' }}>
					<div>{i.ServiceName}</div>
					{i.ServiceDesc && (
						i.ServiceDescType === 'infologo'
							? <Bubble overlayStyle={{ width: 200 }} arrowPointAtCenter placement="right" content={i.ServiceDesc}>	<Icon type="info" style={{ marginLeft: 4, position: 'relative', top: -1 }} /></Bubble>
							:	<div dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(i.ServiceDesc) }}></div>
					)
					}
				</div>,
				disableSelect: i.ShowStatus === -1,
				selected: (i.Id === 27 && standard === '3') ? (!!emergency) : (i.ShowStatus === 1 || i.ShowStatus === -1),
				required: i.ShowStatus === -1,
			}));
			setTreeData(buildNestedArray(temp, 'id', 'parentId'));
			setRequiredServiceId(temp.filter(i => i.required).map(i => i.id));
			setInitServiceId(temp.filter(i => i.selected).map(i => i.id));
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 不能修改的字段判断
	function noEdit(name) {
		const editMap = {
			keyTime: 'ImportantGuardTime',
			guardName: 'GuardName',
			startTime: 'StartTime',
			endTime: 'EndTime',
			project: 'Project',
			appId: 'MainAppId',
			relatedAppId: 'RelatedAppId',
			customerContacts: 'CustomerContacts',
			customerPhone: 'CustomerPhone',
			StatementOfNeeds: 'StatementOfNeeds',
			startClock: 'StartClock',
			endClock: 'EndClock',
			onsiteDates: 'OnsiteTime',
			residentSupport: 'OnsiteProducts',
			showBlockModal: 'ClosedNetworkDemand',
			cronType: 'CronType',
			standard: 'Standard',
			selectIds: 'GuardServiceIds',
		};

		if (isEdit && editList?.length) {
			return !editList.includes(editMap[name]);
		}
		return false;
	}

	// 创建护航单 toNext表示是否需要跳转到下一步
	const createOrModifyGuard = async (toNext: boolean) => {
		const loading = message.loading({ content: '加载中...' });
		try {
			const params: GuardParams = {
				// 平台信息
				Platform: currentGuard.Platform || '',
				PlatformUniqueId: currentGuard.PlatformUniqueId || '',
				StatementOfNeeds,
				GuardId: guardId,
				GuardName: guardName,
				Standard: parseInt(standard),
				Project: parseInt(project),
				StartTime: startTime.format('YYYY-MM-DD HH:mm:ss'),
				EndTime: endTime.format('YYYY-MM-DD HH:mm:ss'),
				ImportantGuardTime: keyTime,
				StartClock: startClock.format(TIME_FORMAT_HM),
				EndClock: endClock.format(TIME_FORMAT_HM),
				Responser: '', // 待确认前端逻辑
				CreatedBy: currentGuard.CreatedBy ? currentGuard.CreatedBy : rtx, // 创建人，如果是新建护航单，创建人不变
				UpdatedBy: rtx, // 更新人
				AppId: parseInt(appId),
				MainAppId: parseInt(appId),
				CustomerName: customerName.trim(),
				CustomerPhone: customerPhone.trim(),
				CustomerContacts: customerContacts.trim(), // 客户接口人
				CustomerAuthStatus: confirmAuthor ? ConfirmAuthorStatus.CONFIRM : ConfirmAuthorStatus.NOAUTHOR,
				RelatedCustomerNames: relatedCustomerNames,
				Products: [], // 待确认前端逻辑
				Origin: 0, // 运营端
				Status: 1, // 草稿
				RelatedAppId: relatedAppId,
				ExpectedEnlargeDays: currentGuard.ExpectedEnlargeDays || 7,
				ExpectedEnlargeTimes: currentGuard.ExpectedEnlargeTimes || 1.5,
				LimitStrategy: currentGuard.LimitStrategy || '',
				PressureTestPlan: currentGuard.PressureTestPlan || '',
				BusinessEmergencyPlan: currentGuard.BusinessEmergencyPlan || '',
				ProductTemplate: currentGuard.ProductTemplate || [],
				TemplateId: currentGuard.TemplateId || '',
				ProductDesc: (currentGuard.ProductDesc || [])
					.filter(p => p.AppId === parseInt(appId) || relatedAppId.includes(p.AppId)) || [],
				OnsiteTime: onsiteDates,
				OnsiteProducts: onsiteProduct,
				ClosedNetworkDemand: closeNetworkDemand,
				GuardServiceIds: selectIds.map(i => Number(i)),
				CronType: cronType,
				StandardDesc: emergency ? JSON.stringify(StandardDesc) : '',
			};
			// 区别走保存接口还是编辑接口
			const methods = isEdit ? UpdateGuardSheet : CreateGuardSheet;
			const res = await methods(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				loading.hide();
				return;
			}
			// 编辑页面就直接跳转详情页
			if (isEdit) {
				message.success({ content: '护航单基本信息保存成功!' });
				history.push(`/advisor/guard/summary/${guardId}`);
			} else {
				updateLeader(res?.Id);
				// 回掉antool Iframe 通知保存成功
				window.parent?.postMessage({
					Source: 'Guard',
					SourceId: res.Id,
					MsgType: 'GuardSave',
					GuardName: params.GuardName,
					StartTime: params.StartTime,
					EndTime: params.EndTime,
					Operation: toNext ? 'next' : 'save',
				}, '*');
				params.GuardId = res.Id;
				loading.hide();
				handleUpdateStepsDisable(false);
				handleGetGuardSheet(res.Id.toString()); // 调用父组件接口，查询查询
				handleUpdateCurrentGuard(params);
				message.success({ content: '护航单基本信息保存成功!' });
				if (toNext) {
					handleUpdateSteps();
				} else {
					setGuardId(res.Id); // 保存按钮时，必须修改当前guardid，否则会重复创建
				}
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
			loading.hide();
			if (!isEdit) {
				handleUpdateStepsDisable(false);
			}
		}
	};

	// 获取云产品清单
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: **********, // 内部账号，产品信息最全
				Env: 'all',
				TaskType: 'guardTaskType',
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmpProductsOptions = [];
			for (const i in res.ProductDict) {
				tmpProductsOptions.push({ value: i, text: res.ProductDict[i] });
			}
			setProductsOptions(tmpProductsOptions);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 获取当前登录账号rtx
	const getCurrentOperator = async () => {
		try {
			const res = await getUserInfo();
			setRtx(res.data.EngName || '');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};
	// 查询护航项目清单
	const getProjects = async () => {
		try {
			const res = await DescribeGuardProjects({
				AppId: **********, // 接口必须传appid  为获取全量产品列表，因此传内部中心账号
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmp = [];
			const m = new Map();
			res.Projects.map((i) => {
				tmp.push({ value: i.Id.toString(), text: i.Name });
				m.set(i.Id, i.Name);
			});
			setProjectOptions(tmp);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询最近一次巡检
	const getCanCreate = async (appid) => {
		try {
			const res = await DescribeLastAdvisorTask({
				AppId: parseInt(appid),
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			//  当天开通云顾问 && 最近一次巡检未完成 弹框提示，不允许建单
			if (moment.unix(res.AppidAuthorizedTime).isSame(moment(), 'day') && !res.HasSuccessTask) {
				setNotCreateInfo({
					time: moment.unix(res.AppidAuthorizedTime).format('YYYY-MM-DD HH:mm:ss'),
					appId: appid,
				});
				setNotCreateVisible(true);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 根据主appid查询客户名称
	const getMainCustomerName = async (appid: string) => {
		try {
			const res = await GetAccountInfoByFields({ AppId: parseInt(appid) });
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setCustomerName(res.CustomerName); // 客户名称，实时查询
			// 保存客户uin，白名单校验
			setUserUin(res.Uin);
			// 迁云割接不判断售后
			if (standard !== '1') {
				if (!res.HasAfterSale) {
					setAppIdNoAfterSale(appid);
					getNoAfterSaleAppids(appid, relatedAppIdNoAfterSale);
				}
			}
			if (!res.IsAuthorized) {
				setAppIdNA(appid);
				getUnAuthorizedAppids(appid, relatedAppIdNA);
			} else {
				getCanCreate(appId);
			}
			if (standard === '1') {
				// 查询迁云的appid是否只有一单
				const res = await DescribeGuardSheet({
					Filters: [
						{ Name: 'appid', Values: [appid] },
						{ Name: 'standard', Values: [standard] },
					],
					Offset: 0,
					Limit: 10,
					AppId: **********, // 接口必须传appid
				});
				if (res.Error) {
					const msg = res.Error.Message;
					tips.error({ content: msg });
					return;
				}
				// 过滤掉状态为草稿&不是当前护航单的护航单
				setRepeatGuard((res.Guard || []).filter(i => i.Status > 1 && i.GuardId !== guardId));
			} else {
				setRepeatGuard([]);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 清除新授权appId
	const clearNotCreateAppId = () => {
		const targetRelatedAppid = relatedAppId.filter(item => item != notCreateInfo.appId);
		setRelatedAppId(targetRelatedAppid);
		setCurrentGuardRelatedAppId(targetRelatedAppid.length > 0 ? targetRelatedAppid.join(' ') : '');
		// 如果是主appId，清除主appId
		if (appId === notCreateInfo.appId) {
			setAppId('');
			setCustomerName('');
		}
	};

	// 删除未授权appid
	const clearUnAuthorizedAppid = async () => {
		const targetRelatedAppid = relatedAppId.filter(item => !appIdNAList.includes(item));
		setRelatedAppId(targetRelatedAppid);
		// currentGuard.RelatedAppId = targetRelatedAppid not work
		setCurrentGuardRelatedAppId(targetRelatedAppid.length > 0 ? targetRelatedAppid.join(' ') : '');
		if (appIdNAList.includes(Number(appId))) {
			setAppId('');
			setCustomerName('');
		}
	};

	// 删除未转售后appid
	const clearNoAfterSaleAppid = async () => {
		const targetRelatedAppid = relatedAppId.filter(item => !appIdNoAfterSaleList.includes(item));
		setRelatedAppId(targetRelatedAppid);
		setCurrentGuardRelatedAppId(targetRelatedAppid.length > 0 ? targetRelatedAppid.join(' ') : '');
		if (appIdNoAfterSaleList.includes(Number(appId))) {
			setAppId('');
			setCustomerName('');
		}
	};

	// 获取未授权appid
	const getUnAuthorizedAppids = async (appid: string, rAppids: Array<number>) => {
		let tmp = [];
		if (appid !== '') {
			tmp.push(Number(appid));
		}
		if (rAppids.length !== 0) {
			tmp = tmp.concat(rAppids);
		}
		setNoList(val => Array.from(new Set([...val, ...tmp])));
		setAppIdNAList(tmp);

		const uniqueItems = Array.from(new Set(tmp));
		if (uniqueItems.length !== 0) {
			setAppIdNANote(`该客户（APPID:${
				 uniqueItems.join(', ')
				 }）尚未开通云顾问，无法提供全面护航能力（自动巡检、监控面板生成、播报等），仅支持分配护航人员应急响应。`);
			setVisibleAppIdNANote(true);
		}
	};

	// 获取未转售后appid
	const getNoAfterSaleAppids = async (appid: string, nafAppids: Array<number>) => {
		let tmp = [];
		if (appid !== '') {
			tmp.push(Number(appid));
		}
		if (nafAppids.length !== 0) {
			tmp = tmp.concat(nafAppids);
		}
		setNoSaleList(val => Array.from(new Set([...val, ...tmp])));
		setAppIdNoAfterSaleList(tmp);

		const uniqueItems = Array.from(new Set(tmp));
		if (uniqueItems.length !== 0) {
			setNoAfterSaleNote(`该客户（APPID:${uniqueItems.join(', ')}）不属于已转售后大客户、腰部客户、自研上云客户，不支持建立标准护航单。`);
			// 如果是自助护航，不展示弹框
			if (standard !== '3') {
				setVisibleNoAfterSaleNote(true);
			}
		}
	};

	// 未授权客户刷新列表
	// （未生效，废弃方式：appId/relatedAppId -> setAppIdNA(), setAelatedAppIdNA() -> getUnAuthorizedAppids()）
	// useEffect(() => {
	//     if (appIdNA.trim() != "" || relatedAppIdNA.length != 0) {
	//         getUnAuthorizedAppids(appIdNA, relatedAppIdNA)
	//     }
	// }, [appIdNA, relatedAppIdNA])

	// UIN转换
	const transferUin = async (uin: string) => {
		if (uin.trim() === '') {
			tips.error({ content: 'uin为空，请输入合法uin值' });
			return;
		}

		try {
			const res = await getAppIDByUin({
				SrcUin: uin,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setAppIdTransfered(res.AppId);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 关联appid的客户名称刷新后，更新到relatedCustomerNames
	useEffect(() => {
		const l = [];
		relatedAppId.map((i) => {
			l.push(relatedCustomerNamesDict[i] || '');
		});
		setRelatedCustomerNames(l);
	}, [relatedCustomerNamesDict, relatedAppId]);

	// 更新关联appid的客户名称
	function updateCumstomerName(appid, name) {
		setRelatedCustomerNamesDict(pre => ({ ...pre, [appid]: name }));
	}

	// 更新未授权的关联appid（主appid,关联appid）
	function updateRelatedAppidNA(rAppid) {
		setRelatedAppIdNA([rAppid]);
		getUnAuthorizedAppids(appIdNA, [rAppid]);
	}

	// 更新未转售后的appid（主appid,关联appid）
	function updateNoAfterSaleAppids(nafAppid) {
		setRelatedAppIdNoAfterSale([nafAppid]);
		getNoAfterSaleAppids(appIdNoAfterSale, [nafAppid]);
	}

	// 获取运营管理中插件的白名单
	async function getWhitelist(uin) {
		const isAbroadSite = localStorage.getItem('site') === 'sinapore';
		if (isAbroadSite) return;
		if (!uin) return;
		try {
			const res = await GetListPluginConfigByAdmin({
				CustomerAppId: parseInt(appId),
				CustomerUin: uin,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			// 关闭灰度，之前保存的UinList不会清空
			const pluginWhiteList = (res?.Data || []).filter(i => i.PluginName === 'cloud-escort-sdk') || [];
			setIsWhiteListUin(!_.isEmpty(pluginWhiteList));
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// init 获取全量的产品信息名称--不依赖输入客户appid
	useEffect(() => {
		getProductsGroupsInfo();
		getCurrentOperator();
		getProjects();
	}, []);

	useEffect(() => {
		getWhitelist(userUin);
	}, [userUin]);

	// 调用接口，查询客户名称
	useEffect(() => {
		if (/^\d{10}$/.exec(appId)) {
			getMainCustomerName(appId);
		} else {
			setRepeatGuard([]);
		}
	}, [appId, standard]);

	// 必选输入框ref
	const guardNameRef = useRef(null);
	const appidRef = useRef(null);

	async function bindPlatform(archInfo) {
		const { EndTime, GuardCreateSource, ArchId, GuardStatus } = archInfo;
		try {
			const params = {
				MainAppId: parseInt(appId, 10),
				GuardID: guardId || 0,
				CustomerName: customerName?.trim() || '',
				GuardName: `${customerName}护航需求${moment().format('YYYYMMDDHH')}`,
				StartTime: moment().add(2, 'hours')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
			  	EndTime: moment().add(1, 'days')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
				OtherPlatforms: [
					{ Platform: GuardCreateSource || 'ISA', PlatformUniqueId: ArchId, AppId: currentGuard?.MainAppId },
					{ Platform: currentGuard.Platform || 'Antool', PlatformUniqueId: currentGuard.PlatformUniqueId, AppId: currentGuard?.MainAppId },
				],
				OpsType: 'add',
			};
			const res = await BindGuardSheet(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			// 如果未在护航，要跳转进入架构图
			if (!GuardStatus || moment().isAfter(EndTime) || (GuardStatus === 1 && !moment().isAfter(EndTime))) {
				window.open(`${location.origin}/advisor/new-architecture/architecture/${ArchId}?appid=${parseInt(appId, 10)}&plugin=cloud-escort-sdk&fromPlatform=Antool`);
			}
			// 回掉antool Iframe 通知保存成功
			window.parent?.postMessage({
				Source: 'Guard',
				SourceId: res.GuardId,
				MsgType: 'GuardSave',
				GuardName: params.GuardName,
				StartTime: params.StartTime,
				EndTime: params.EndTime,
				Operation: 'save',
			}, '*');
			history.push('/advisor/guard');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 驻场护航提交
	async function isOnSiteSave() {
		if (!selectedArchId) {
			message.error({ content: '请选择架构图' });
			return;
		}
		// 架构图信息
		const archInfo = archList.filter(i => i.ArchId === selectedArchId)?.[0] || {};
		const { EndTime, GuardId, GuardCreateSource, ArchId, GuardStatus } = archInfo;
		// 如果是草稿单且并未过期，需要先解绑架构图关系
		if ((GuardStatus === 1 && !moment().isAfter(moment(EndTime, 'YYYY-MM-DD HH:mm:ss')))) {
			try {
				const params = {
					MainAppId: parseInt(appId, 10),
					GuardID: GuardId,
					OtherPlatforms: [
						{ Platform: GuardCreateSource || 'ISA', PlatformUniqueId: ArchId, AppId: currentGuard?.MainAppId },
					],
					OpsType: 'del',
				};
				// 先解绑
				const res = await BindGuardSheet(params);
				if (res.Error) {
					const msg = res.Error.Message;
					tips.error({ content: msg });
					return;
				}
				// 再绑定
				bindPlatform(archInfo);
			} catch (err) {
				const msg = err.msg || err.toString() || '未知错误';
				tips.error({ content: msg });
			}
		} else {
			bindPlatform(archInfo);
		}
	}

	// 暴露回调函数给父组件
	useImperativeHandle(ref, () => ({
		// 校验入参，并调用接口创建或修改护航单基础信息
		OnClick: async (toNext) => {
			// 如果是驻场护航，就不走正常流程
			if (isOnSite) {
				isOnSiteSave();
				return;
			}
			let keyGuardFlag = true;
			const check1 = guardNameRef.current.Check();
			const check2 = appidRef.current.Check();
			// 护航时间输入框校验
			if (!startTime) {
				setguardTimeVisible(true);
			}
			// 护航项目勾选校验
			if (project === '0') {
				setProjectVisible(true);
			}
			if ((isMoreThanOne || isMoreThanYear) && !leaderName) {
				setLeaderNameVisible(true);
				return;
			}
			// 客户授权确认
			if (!confirmAuthor) {
				setConfirmAuthorVisible(true);
			}
			// 检查关联客户名称，不允许有空名称存在
			if (
				relatedCustomerNames.filter((i) => {
					if (i === '') {
						return i;
					}
				}).length > 0
			) {
				message.warning({ content: '关联客户名称未查询成功，请联系管理员或稍后重试' });
				return;
			}
			if (moment(endTime.format('YYYY-MM-DD')).diff(moment(startTime.format('YYYY-MM-DD')), 'days') >= 5 && _.isEmpty(keyTime)) {
				setKeyGuardVisible(true);
				keyGuardFlag = false;
			}
			if (relatedAppId.includes(Number(appId))) {
				return;
			}
			// 如果是迁云，并且有护航单，不允许建单
			if (repeatGuard.length >= repeatGuardLimit) {
				return;
			}
			if (
				check1
				&& check2
				&& !appidsVisible
				&& startTime
				&& project != '0'
				&& confirmAuthor
				&& keyGuardFlag
				&& !startTimeIsBefore
			) {
				if (!(isEdit && endBeforeStart)) {
					if (!isEdit) {
						handleUpdateStepsDisable(true);
					}
					createOrModifyGuard(toNext); // 调用接口创建或修改护航单基础信息
				}
			}
		},
	}));

	const [blockVisible, setBlockVisible] = useState(false);
	const [showBlockModal, setShowBlockModal] = useState(!!closeNetworkDemand);
	const [cronType, setCronType] = useState(currentGuard.CronType || 0);

	return (
		<Card key={'key'} style={{ margin: 6 }}>
			<Card.Body>
				{repeatGuard.length >= repeatGuardLimit
					&& <Alert type="error" style={{ marginTop: 8 }}>
						APPID {appId} 已有迁云护航单（{repeatGuard.map(i => i.GuardId).join('，')}），不支持继续新建。上述单子在护航期间可以在 [护航管理]-[操作]-[更多]-[护航实例变更] 变更所需护航资源。
					</Alert>}
				{(standard === '3' && noSelfService.length > 0)
					&& <Alert type="warning" style={{ marginTop: 8 }}>
						注意，账号（ {noSelfService.join(',')} ） 未开通云顾问，自助护航无法支持该账号的隐患报告、监控面板等大部分依赖云顾问功能的服务，如实例播报、变更通知等。
					</Alert>}
				{/* 允许灰度 */}
				{
					isWhiteListUin && !relatedAppId?.length
					&& <Row gap={60}>
						<Col span={12}>
							<section>
								<Row verticalAlign={'middle'}>
									<Col span={6}>
										<Text theme="label" verticalAlign="middle">
										是否驻场
										</Text>
									</Col>
									<Col span={18}>
										<Switch
											value={isOnSite}
											onChange={(val) => {
												setIsOnSite(val);
												isOnSiteChange(val);
											}}
										>驻场人员：<strong>售后一线</strong>。驻场护航基于客户架构图发起。</Switch>
									</Col>
								</Row>
							</section>
						</Col>
					</Row>}
				{isOnSite && <>
					<Alert style={{ marginTop: 20 }}>若未有架构图，请与客户沟通在
						<a href='https://console.cloud.tencent.com/advisor' target="_blank">租户端</a>
					绘制后开启“协作”（<a href='https://csig.lexiangla.com/teams/k100630/docs/91494b5462f011ef81d5427b769b846d?company_from=csig' target="_blank">说明</a>）。
					或在<a href={`${location.origin}/advisor/new-architecture`} target="_blank">运营端</a>绘制后发送到客户APPID，后可进入云护航应用发起护航。</Alert>
					<TagSearchBox
						tips='支持架构图标题、ID查询筛选'
						hideHelp
						minWidth={400}
						style={{ width: 400, marginBottom: 10 }}
						attributes={attributes}
						value={filterInfo}
						onChange={(value) => {
							setFilterInfo(value);
						}}
					/>
					<Table
						verticalTop
						records={archList}
						recordKey="ArchId"
						columns={archListColumns}
						bordered
						disableHoverHighlight
						// 草稿单且有任务单的可选择
						rowDisabled={i => i.IsRelateTaskNum && i.GuardStatus > 1 }
						rowClassName={i => (i.GuardStatus > 1 ? 'guardRow' : 'noGuardRow')}
						topTip={
							(loading || archList.length === 0) && (
							  <StatusTip status={loading ? 'loading' : 'empty'} />
							)
						  }
						addons={[
							pageable({
								recordCount: total,
								pageSize,
								pageIndex: pageNumber,
								onPagingChange: (query) => {
									if (loading) {
									  return;
									}
									setPageNumber(query.pageIndex);
									setPageSize(query.pageSize);
									getArchList(query.pageIndex, query.pageSize);
								  },
							  }),
							radioable({
								value: selectedArchId,
								onChange: (key) => {
									setSelectedArchId(key);
								},
								rowSelect: true,
								render: (element, { disabled }) => (disabled ? '' : element),
						  })]}
					/>
				</>
				}

				{!isOnSite && <Row gap={60}>
					<Col>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text style={{ color: 'red' }} verticalAlign="middle">
										*
									</Text>
									<Text theme="label" verticalAlign="middle">
										护航名称
									</Text>
								</Col>
								<Col span={18}>
									<MyInput
										disabled={noEdit('guardName')}
										ref={guardNameRef}
										CallBack={(v) => {
											setGuardName(v);
										}}
										Value={guardName}
										Need={true}
										RegxCheckGroup={[
											{ Regx: /^[^/:*?"<>|#%]*$/, Content: '名称不能包含 / : * ? " < > | # %' },
										]}
										Placeholder='名称不能包含 / : * ? " < > | # %'
										updateDeps={[repeatGuard]}
									/>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text style={{ color: 'red' }} verticalAlign="middle">
										*
									</Text>
									<Text theme="label" verticalAlign="middle">
										护航时间
									</Text>
								</Col>
								<Col span={18}>
									{isEdit
										? <Bubble
											updateDeps={[repeatGuard]}
											error
											visible={startTimeIsBefore || endBeforeStart}
											content={startTimeIsBefore
												? '护航日期必须大于等于当前时间，请填写有效的护航时间。'
												: '护航开始时间必须小于结束时间'}
										>
											<div className='editTimeWrap'>
												<DatePicker
													disabled={noEdit('startTime')}
													className='editDatePicker'
													showTime
													value={startTime}
													range={[moment(), null]}
													onChange={(value) => {
														setStartTime(value);
														setOnsiteDates([]);
														setKeyTime([]);
													}}
												/>
												<div className='separator'>~</div>
												<DatePicker
													disabled={noEdit('endTime')}
													className='editDatePicker'
													showTime
													value={endTime}
													range={[moment(), null]}
													onChange={(value) => {
														setEndTime(value);
														setOnsiteDates([]);
														setKeyTime([]);
													}}
												/>
											</div>
										</Bubble>
										:	<Bubble
											updateDeps={[repeatGuard]}
											error
											visible={guardTimeVisible || startTimeIsBefore}
											content={
												startTimeIsBefore
													? '护航日期必须大于等于当前时间，请填写有效的护航时间。'
													: '护航时间不能为空'
											}
										>
											<RangePicker
												className='RangePicker'
												showTime
												value={[startTime, endTime]}
												range={[moment(), null]}
												onChange={(v) => {
													setguardTimeVisible(false);
													setStartTime(v[0]);
													setEndTime(v[1]);
													setOnsiteDates([]);
													setKeyTime([]);
												}}
											/>
										</Bubble>}
								</Col>
							</Row>
						</section>
						{/* 护航时间大于等于5天才支持选择护航重点时间 */}
						{
							moment(endTime.format('YYYY-MM-DD')).diff(moment(startTime.format('YYYY-MM-DD')), 'days') >= 5
							&& <section>
								<Row>
									<Col span={6}>
										<Text style={{ color: 'red' }} verticalAlign="middle">*</Text>
										<Text theme="label" verticalAlign="middle">
										护航重点时间
										</Text>
									</Col>
									<Col span={18} className="broadcast-time-range-picker">
										<Bubble error visible={keyGuardVisible && _.isEmpty(keyTime)} content="重点护航不能为空">
											<DatePicker
												disabled={noEdit('keyTime')}
												range={[startTime, endTime]}
												onChange={(value) => {
													const tmp = _.cloneDeep(keyTime);
													const d = value.format('YYYY/MM/DD');
													if (tmp.indexOf(d) === -1) {
														tmp.push(d);
													}
													setKeyTime(tmp);
												}}
											/>
										</Bubble>
										{keyTime.length > 0 && (
											<div style={{ margin: '10px 0' }}>
												{keyTime.map((i, index) => (
													<Tag
														onClose={() => {
															!noEdit('keyTime') && setKeyTime(keyTime.filter((j) => {
																if (j != i) {
																	return j;
																}
															}));
														}}
														key={index}
													>
														{i}
													</Tag>
												))}
											</div>
										)}
									</Col>
								</Row>
							</section>}
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text style={{ color: 'red' }} verticalAlign="middle">
										*
									</Text>
									<Text theme="label" verticalAlign="middle">
										护航项目
									</Text>
								</Col>
								<Col span={18}>
									<Bubble error visible={projectVisible} content="护航项目必选勾选" updateDeps={[repeatGuard]}>
										<Select
											disabled={noEdit('project')}
											value={project}
											options={projectOptions}
											onChange={(v) => {
												setProject(v);
												setProjectVisible(false);
											}}
											size="full"
											appearance="button"
											searchable
										/>
									</Bubble>
								</Col>
							</Row>
						</section>
						<div style={{ height: 1, background: '#eee', margin: '20px 0' }}></div>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text style={{ color: 'red' }} verticalAlign="middle">
										*
									</Text>
									<Text theme="label" verticalAlign="middle">
										客户APPID
									</Text>
								</Col>
								<Col span={15}>
									<MyInput
										disabled={noEdit('appId')}
										ref={appidRef}
										Value={appId}
										CallBack={(v) => {
											setAppId(v);
										}}
										Need={true}
										RegxCheckGroup={[{ Regx: /^\d{10}$/, Content: 'AppId输入不合法' }]}
									/>
								</Col>
								<Col span={3}>
									<Button
										type="link"
										onClick={() => {
											setVisibleUinTransfer(true);
										}}
									>
										UIN转换
										<Icon type="arrowright" />
									</Button>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										客户名称
									</Text>
								</Col>
								<Col span={18}>
									<Text align="left">{customerName}</Text>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										关联客户APPID
									</Text>
								</Col>
								<Col span={18}>
									<AppIdsInput
										CallBack={(l, v) => {
											const tmp: Array<number> = [];
											l.map((i) => {
												tmp.push(parseInt(i));
											});
											setRelatedAppId(tmp);
											setAppidsVisible(v);
										}}
										Need={false}
										Value={currentGuardRelatedAppId}
										AppId={appId}
										Disabled={standard === '1' || noEdit('relatedAppId')}
										ref={InputRef}
									/>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										关联客户名称
									</Text>
								</Col>
								<Col span={18}>
									<div>
										{relatedAppId.map(i => (
											<div key={i}>
												<CustomerName
													getCanCreate={(appId) => {
														getCanCreate(appId);
													}}
													handleUpdate={(name) => {
														updateCumstomerName(i, name);
													}}
													handleUpdateAppidNA={(i) => {
														updateRelatedAppidNA(i);
													}}
													updateNoAfterSaleAppids={(i) => {
														updateNoAfterSaleAppids(i);
													}}
													appid={i}
												/>
											</div>
										))}
									</div>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										客户接口人
									</Text>
								</Col>
								<Col span={18}>
									<Input
										disabled={noEdit('customerContacts')}
										value={customerContacts}
										onChange={(v) => {
											setCustomerContacts(v);
										}}
										size="full"
									/>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										客户联系方式
									</Text>
								</Col>
								<Col span={18}>
									<Input
										disabled={noEdit('customerPhone')}
										value={customerPhone}
										onChange={(v) => {
											setCustomerPhone(v);
										}}
										size="full"
									/>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'top'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										护航背景及需求
									</Text>
								</Col>
								<Col span={18}>
									<TextArea
										disabled={noEdit('StatementOfNeeds')}
										onChange={(value) => {
											setStatementOfNeeds(value);
										}}
										value={StatementOfNeeds}
										placeholder="请说明护航的背景以及需要护航人员特别关注的需求"
										size={'full'}
									/>
								</Col>
							</Row>
						</section>
						<div style={{ height: 1, background: '#eee', margin: '20px 0' }}></div>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Tooltip title={'默认按小时频率、整点时刻播报'}>
										<Text theme="label" verticalAlign="middle">
											播报时段
										</Text>
									</Tooltip>
								</Col>
								<Col span={18} className="broadcast-time-range-picker">
									<TimeRangePicker
										disabled={noEdit('startClock') === true && noEdit('endClock') === true}
										value={[startClock, endClock]}
										format={TIME_FORMAT_HM}
										disableAutoAdjust={true}
										disabledTime={(time, partial) => {
											const [start] = time;
											if (partial === 'start') {
												return {
													disabledMinutes: () => range(1, 59),
												};
											}
											if (partial === 'end' && isMoment(start)) {
												return {
													disabledHours: () => range(0, start.hour()),
													disabledMinutes: () => range(0, 58),
												};
											}
											return {};
										}}
										onChange={(value) => {
											setStartClock(value[0]);
											setEndClock(value[1]);
										}}
									/>
								</Col>
							</Row>
						</section>
						{ !isWhiteListUin && <section>
							<Row>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										驻场护航时间
									</Text>
								</Col>
								<Col span={18} className="broadcast-time-range-picker">
									<DatePicker
										disabled={noEdit('onsiteDates')}
										range={[startTime, endTime]}
										onChange={(value) => {
											const tmp = _.cloneDeep(onsiteDates);
											const d = value.format('YYYY/MM/DD');
											if (tmp.indexOf(d) === -1) {
												tmp.push(d);
											}
											setOnsiteDates(tmp);
										}}
									/>
									{onsiteDates.length > 0 && (
										<div style={{ margin: '10px 0' }}>
											{onsiteDates.map((i, index) => (
												<Tag
													onClose={(v) => {
														setOnsiteDates(onsiteDates.filter((j) => {
															if (j != i) {
																return j;
															}
														}));
													}}
													key={index}
												>
													{i}
												</Tag>
											))}
										</div>
									)}
								</Col>
							</Row>
						</section>
						}
						{ (isMoreThanOne || isMoreThanYear) && <section>
							<Row verticalAlign="middle" gap={20}>
								<Col span={6}>
									<Text style={{ color: 'red' }} verticalAlign="middle">
										*
									</Text>
									<Text theme="label" verticalAlign="middle">
										总监审批
									</Text>
								</Col>
								<Col span={10}>
									<Bubble placement="top" content={t('不能为空')} visible={leaderNameVisible} error>
										<RTXPicker
											placeholder={t('请输入需要添加的协作人英文ID')}
											value={leaderName}
											onChange={(name: string) => {
												setLeaderName(name);
												setLeaderNameVisible(false);
											}}
										/>
									</Bubble>
								</Col>
								<Col span={6}>
									<div className='leaderWrap'>
										<Text theme="label" className='tamLabel'>{t('售后总监')}</Text>
										<Text>{onsiteGuardConfig?.TamDirector}</Text>
									</div>
								</Col>
								<Col span={1}>
									<Bubble
										dark
										overlayClassName='leaderBubble'
										arrowPointAtCenter
										content={leaderTip()}
									>
										<Icon type="info" />
									</Bubble>
								</Col>
							</Row>
						</section>}
						{ !isWhiteListUin && <section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										驻场护航产品
									</Text>
								</Col>
								<Col span={2}>
									<Switch
										disabled={noEdit('residentSupport')}
										onChange={(v) => {
											setResidentSupport(v);
										}}
										value={residentSupport}
									/>
								</Col>
								{/* <Col span={10}>
										<Text theme="label" verticalAlign="middle">需要驻场支持的云产品</Text>
								</Col> */}
								<Col span={16}>
									<SelectMultiple
										disabled={!residentSupport}
										placeholder={'需要驻场支持的云产品'}
										options={productsOptions}
										value={onsiteProduct}
										style={{ float: 'right' }}
										onChange={(v) => {
											setOnsiteProduct(v);
										}}
										size="full"
										appearance="button"
										searchable
									/>
								</Col>
							</Row>
						</section> }
						<section>
							<Row verticalAlign={'top'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										封网需求
									</Text>
								</Col>
								<Col span={2}>
									<Switch
										disabled={noEdit('showBlockModal')}
										value={showBlockModal}
										onChange={(val) => {
											if (val) {
												setBlockVisible(true);
											} else {
												setCloseNetworkDemand('');
												setShowBlockModal(false);
											}
										}}
									/>
								</Col>
								<Col span={16}>
									<TextArea
										disabled={!showBlockModal}
										onChange={(value) => {
											setCloseNetworkDemand(value);
										}}
										value={closeNetworkDemand}
										placeholder={'说明需要封网管控发布变更的地区和时间'}
										size={'full'}
									/>
								</Col>
							</Row>
						</section>
						<section>
							<Row verticalAlign={'top'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										每日巡检
									</Text>
									<Bubble
										arrowPointAtCenter
										placement="top-start"
										content="开启后，护航期间将自动发起护航巡检和生成新的护航报告"
									>
										<Icon type="info" style={{ marginLeft: 4 }} />
									</Bubble>
								</Col>
								<Col span={16}>
									<Switch
										disabled={noEdit('cronType')}
										value={cronType !== 0}
										onChange={(val) => {
											if (val) {
												setCronType(1);
											} else {
												setCronType(0);
											}
										}}
									/>
								</Col>
							</Row>
						</section>
					</Col>
					<Col>
						<section>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text style={{ color: 'red' }} verticalAlign="middle">
										*
									</Text>
									<Text theme="label" verticalAlign="middle">
										护航类型
									</Text>
								</Col>
								<Col span={16}>
									<Select
										disabled={noEdit('standard')}
										appearance="button"
										value={standard}
										options={StandardDict}
										onChange={(v) => {
											setStandard(v);
										}}
										size="full"
									/>
								</Col>
								<Col span={2}>
									<Bubble
										arrowPointAtCenter
										placement="top"
										content="护航类型选项说明"
									>
										<Button type="link" onClick={() => {
											window.open('https://iwiki.woa.com/p/4009269554');
										}}>
											说明
										</Button>
									</Bubble>
								</Col>
							</Row>
							{standard === '1' && <Row>
								<Col span={6}></Col>
								<Col span={18}>
									<Text theme="warning">
										注意：迁云割接护航适合售中、迁云场景，没有售后角色。
									</Text>
								</Col>
							</Row>}
							{standard === '3' && <Row>
								<Col span={6}></Col>
								<Col span={18}>
									<Text theme="warning">
										注意：自助护航会略过售后和专项等人员审批，<span style={{ textDecoration: 'underline' }}>快速自动生成巡检报告、监控、播报等服务</span>。此时，自助护航<span style={{ textDecoration: 'underline' }}>不会支持售后（如未勾选护航服务群）和专家等人员服务</span>，您可以自助提工单以获取对应服务。
									</Text>
								</Col>
							</Row>}
							{emergency && <Row>
								<Col span={6}></Col>
								<Col span={18}>
									<Text theme="danger">
										注意：护航开始时间( {moment(startTime).format('YYYY-MM-DD HH:mm:ss')} )小于提单时间2个工作日，按紧急护航处理。考虑人力，护航类型仅支持自助护航，对应问题提工单。如已转售后，建议选择护航服务群。
									</Text>
								</Col>
							</Row>}
						</section>
						<section style={{ marginTop: 6 }}>
							<Row verticalAlign={'top'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">
										护航阶段｜护航服务
									</Text>
								</Col>
								<Col span={18}>
									<div style={{ width: '100%' }} className='serviceWrap'>
										{
											treeData?.length > 0
											&& <Collapse defaultActiveIds={treeData.map(i => i.id)} icon={active => <Icon type={active ? 'arrowdown' : 'arrowright'} />}>
												{treeData.map(i => <>
													<Collapse.Panel id={i.id} title={i.content}>
														{i.children?.length > 0 && <Tree
															style={{ maxWidth: '100%' }}
															data={i.children}
															selectable={!(isEdit && noEdit('selectIds'))}
															selectedIds={selectIds}
															defaultExpandAll
															selectValueMode='onlyLeaf'
															onSelect={(value) => {
																setSelectIds(value);
															}}
														/>}
													</Collapse.Panel>
													<div style={{ height: 1, background: '#eee', margin: '10px 0 16px 0' }}></div>
												</>)}
											</Collapse>
										}
									</div>
								</Col>
							</Row>
						</section>
					</Col>
				</Row>}
				{!isOnSite && <Row>
					<Col span={10}>
						<section>
							<Bubble
								error
								visible={confirmAuthorVisible}
								content="必须确认客户已授权"
								updateDeps={[repeatGuard]}
								arrowPointAtCenter
								placement='left'
							>
								<Checkbox
									style={{ marginTop: 15 }}
									display="block"
									value={confirmAuthor}
									onChange={(value) => {
										setConfirmAuthor(value);
										setConfirmAuthorVisible(!value);
									}}
								>
									<Text style={{ color: 'red' }} verticalAlign="middle">
										*
									</Text>
									<Text style={{ color: 'blue' }}>
										我已与客户沟通本次护航巡检会对客户资源进行扫描，并获得客户授权。
									</Text>
								</Checkbox>
							</Bubble>
						</section>
					</Col>
				</Row>}
				{/* 当天开通云顾问 && 最近一次巡检没有完成 */}
				<Modal
					visible={notCreateVisible}
					disableCloseIcon
					onClose={() => {
						clearNotCreateAppId(); setNotCreateVisible(false);
					}}
				>
					<Modal.Body>
						<Modal.Message
							style={{ margin: 20 }}
							icon="infoblue"
							message="提示"
							description={<Text style={{ color: 'red' }}>账号 {notCreateInfo.appId} 于 {notCreateInfo.time} 开通云顾问，首次巡检任务尚未完成，请稍后再试</Text>}
						/>
					</Modal.Body>
					<Modal.Footer>
						<Button
							type="primary"
							onClick={() => {
								clearNotCreateAppId();
								setNotCreateVisible(false);
							}}
						>
							关闭
						</Button>
					</Modal.Footer>
				</Modal>
				{
					// 未授权账号信息提示
					<Modal
						visible={visibleAppIdNANote}
						disableCloseIcon
						onClose={() => {
							setVisibleAppIdNANote(false);
						}}
					>
						<Modal.Body>
							<Modal.Message
								style={{ margin: 20 }}
								icon="infoblue"
								message="提示"
								description={<Text style={{ color: 'red' }}>{appIdNANote}</Text>}
							/>
							<List style={{ marginLeft: 62 }}>
								<List.Item>建议
									<ExternalLink href='https://cloud.tencent.com/document/product/1264/46690' style={{ margin: '0 2px' }}

									>参考指引
									</ExternalLink>引导客户开通云顾问。
								</List.Item>
							</List>

						</Modal.Body>
						<Modal.Footer>
							<Button
								type="primary"
								onClick={() => {
									setVisibleAppIdNANote(false);
								}}
							>
								仍要继续
							</Button>
							<Button
								type="weak"
								onClick={() => {
									clearUnAuthorizedAppid();
									setVisibleAppIdNANote(false);
								}}
							>
								取消
							</Button>
						</Modal.Footer>
					</Modal>
				}
				{
					// 未转售后账号信息提示
					<Modal
						visible={visibleNoAfterSaleNote}
						disableCloseIcon
						onClose={() => {
							setVisibleNoAfterSaleNote(false);
						}}
					>
						<Modal.Body>
							<Modal.Message
								style={{ margin: 20 }}
								icon="infoblue"
								message="提示"
								description={<Text style={{ color: 'red' }}>{noAfterSaleNote}</Text>}
							/>
						</Modal.Body>
						<Modal.Footer>
							<Button
								type="primary"
								onClick={() => {
									clearNoAfterSaleAppid();
									setVisibleNoAfterSaleNote(false);
								}}
							>
								关闭
							</Button>
						</Modal.Footer>
					</Modal>
				}
				{
					// UIN转APPID弹窗
					<Modal
						size={'m'}
						visible={visibleUinTransfer}
						onClose={() => {
							setVisibleUinTransfer(false);
						}}
						caption="将 UIN 转换为 APPID"
					>
						<Modal.Body>
							<Card key={'uinTransfer'} style={{ margin: 2 }}>
								<Card.Body>
									<Row gap={30} verticalAlign={'middle'}>
										<Col span={4}>客户UIN</Col>
										<Col span={6}>
											<Input
												value={uin}
												onChange={(value) => {
													setUin(value);
												}}
												placeholder="请输入UIN，以转换输出APPID"
											/>
										</Col>
									</Row>
									<Row>
										<Col span={4}>
											<Button
												type="link"
												onClick={() => {
													transferUin(uin);
												}}
											>
												转换
												<Icon type="arrowright" />
											</Button>
										</Col>
										<Col span={6}>
											<Text align="left">{appIdTransfered ? appIdTransfered : ''}</Text>
										</Col>
									</Row>
								</Card.Body>
							</Card>
						</Modal.Body>
						{/* <Modal.Footer>
								<Button type="weak" onClick={() => { setVisibleUinTransfer(false); }} >
										关闭
								</Button>
						</Modal.Footer> */}
					</Modal>
				}
				<Modal
					visible={blockVisible}
					caption="特别提示"
					onClose={() => {
						setBlockVisible(false);
					}}
				>
					<Modal.Body>
						<div>
							<Text theme={'text'}>根据客户实际情况评估是否需要封网，如有需求：</Text>
						</div>
						<div>
							<Text theme={'text'}>
								1、请确认按照
								<a
									href="https://csig.lexiangla.com/teams/k100054/docs/3c4398280cd111eab7100a58ac1312dd?company_from=csig"
									target={'_blank'}
								>
									《腾讯云封网流程》
								</a>
								申请封网；
							</Text>
						</div>
						<div>
							<Text theme={'text'}>2、护航单中说明需要管控发布变更的地区和时间</Text>
						</div>
					</Modal.Body>
					<Modal.Footer>
						<Button
							type="primary"
							onClick={() => {
								setShowBlockModal(true);
								setBlockVisible(false);
							}}
						>
							确定
						</Button>
						<Button
							type="weak"
							onClick={() => {
								setBlockVisible(false);
							}}
						>
							取消
						</Button>
					</Modal.Footer>
				</Modal>
			</Card.Body>
		</Card>
	);
}

export default forwardRef(BaseInfo);
