import React, { useState, useEffect, useMemo } from 'react';
import { Icon, Bubble, List, Text, message, Button, Modal,Alert } from '@tencent/tea-component';
import { ApprovalInfo } from '@src/types/advisor/guard';
import { GenerateGuardSheetExport, GetGuardSheetDownload } from '@src/api/advisor/guard';
import _ from 'lodash';
interface ExportSheetProps {
	isListItem: boolean;
	currLoginUser: string;
	GuardId: number;
	Status: number; //护航单状态 根据护航单状态控制Disabled
	CreatedBy: string;
	Approvals: ApprovalInfo;
	Responsor:string;
}
let reportTimer: NodeJS.Timeout;

export function ExportGuardSheet({
	currLoginUser,
	GuardId,
	Status,
	CreatedBy,
	Approvals,
	isListItem,
	Responsor,
}: ExportSheetProps) {
	//是否loading
	const [loading, setLoading] = useState<boolean>(false);
	const [guardSheetUrl, setGuardSheetUrl] = useState<any>({});
	//时间记录状态
	const [overTime,setOverTime] = useState(false);
	//是否打开下载弹窗
	const [visibleConfirm,setVisibleConfirm] = useState(false);
	//创建一个超时判断期约函数
	let errorPromise:Promise<string>;
	// 请求Excel异步下载
	const handleGuardSheetAsync = async (params) => {
		errorPromise = new Promise((resolve)=>{
			setTimeout(()=>{resolve("Over-Time!")},30*1000)
		})
		setVisibleConfirm(true);
		setLoading(true);
		try {
			const res = await GenerateGuardSheetExport(params);
			if (res.Error) {
				message.error({ content: res.Error });
				return;
			}
			if (res.ResultId) {
				//aegis.reportEvent({
				//    name: 'Click',
				//    ext1: 'generate-guard-sheet',
				//    ext2: CreatedBy,
				//    ext3: '护航管理/护航查询'
				//})
				getGuardSheetDownloadTask(res.ResultId);
				reportTimer = setInterval(() => {
					getGuardSheetDownloadTask(res.ResultId);
				}, 2000);
			} else {
				setTimeout(()=>{
					setVisibleConfirm(false);
					setLoading(false);
				},1000);
				message.error({ content: '生成护航单错误，请联系平台管理员！' });
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};
	// 获取Excel异步下载结果
	const getGuardSheetDownloadTask = async (resultId: string) => {
		try {
			const resPromise = GetGuardSheetDownload({ ResultID: resultId, AppId: 1253985742 });
			const res = await Promise.race([errorPromise,resPromise]);
			if(res === "Over-Time!"){
				clearInterval(reportTimer);
				setOverTime(true);
				setTimeout(()=>{
					setVisibleConfirm(false);
					setLoading(false);
				},1000);
				message.error({ content: '下载护航单错误，请联系平台管理员！' });
				return
			}
			if (res.Error) {
				message.error({ content: res.Error.Message });
				clearInterval(reportTimer);
				return;
			}
			if (['success', 'failed'].includes(res.TaskStatus)) {
				clearInterval(reportTimer);
				if (res.TaskStatus === 'failed') {
					message.error({ content: '下载护航单错误，请联系平台管理员！' });
					return;
				}
				DownloadGuardSheet(res.CosUrl);
				setGuardSheetUrl({
					CosUrl: res.CosUrl || '',
					TaskStatus: res.TaskStatus || '',
				});
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
			clearInterval(reportTimer);
		}
	};
	const DownloadGuardSheet = (downloadUrl: string) => {
		if (downloadUrl) {
			//aegis.reportEvent({
			//    name: 'Click',
			//    ext1: 'download-list-btn',
			//    ext2: CreatedBy,
			//    ext3: '护航管理/护航查询'
			//})
			const link = document.createElement('a');
			link.href = downloadUrl;
			link.download = ''; // 可选，设置下载的文件名，如果为空则使用服务器返回的文件名
			document.body.appendChild(link);
			link.click(); // 触发下载

			// 下载完成后移除 <a> 元素
			setTimeout(() => {
				document.body.removeChild(link);
				window.URL.revokeObjectURL(link.href);
			}, 0);
		}
	};
	const inChargeList = useMemo(()=>{
		const creator = CreatedBy.trim();
		let tempList = [creator].concat(Approvals.AfterSalesStatus.Supporter.split(';')).concat(Responsor.split(';')).filter(i => { return i != "" });
		return Array.from(new Set(tempList));
	},[CreatedBy,Approvals.AfterSalesStatus.Supporter,Responsor])

	//查询是否有导出护航单的权限
	const isGuardSheetExportAllowed = useMemo(()=>{
		const creator = CreatedBy.trim();
		const editMode = Status == 1; //草稿状态 Id 值为1
		
		//建单人可以导出
		if (creator == currLoginUser) {
			return true;
		}
		//非草稿模式下护航负责人可以导出
		return !editMode && inChargeList.includes(currLoginUser)
	},[CreatedBy,Status,currLoginUser])
	
	useEffect(() => {
		if (guardSheetUrl.TaskStatus === 'success' || guardSheetUrl.TaskStatus === 'failed') {
			setLoading(false);
		}
	}, [guardSheetUrl]);

	return (
		<>
			<Modal size="auto" visible={visibleConfirm&&loading} caption="注意" disableCloseIcon>
				<Modal.Body>
					{!overTime?
					<Alert style={{ marginTop: 8 }}>
						{`护航单 ${GuardId} 信息导出中 ...`}
					</Alert>
					:
					<Alert type="error" style={{ marginTop: 8 }}>
        				{`导出超时，请联系平台同学或反馈`}
      				</Alert>}
				</Modal.Body>
			</Modal>
			{isListItem ? (
				<List.Item
					tooltip={
						!isGuardSheetExportAllowed ? (
							<Bubble trigger={!isGuardSheetExportAllowed ? 'hover' : 'empty'}>
								<Text
									style={{ whiteSpace: 'pre-line' }}
								>{`仅提单人、护航负责人支持导出\n${inChargeList.join(',')}`}</Text>
							</Bubble>
						) : (
							''
						)
					}
					disabled={!isGuardSheetExportAllowed || loading}
					onClick={() => {
						setOverTime(false);
						handleGuardSheetAsync({
							GuardId: GuardId,
							Name: currLoginUser,
							ListType: 2,
							AppId: 1253985742,
						});
					}}
				>
					<Icon type="download" style={{ marginRight: 3 }} />
					导出
					{loading && <Icon type="loading" style={{ marginLeft: 6 }} />}
				</List.Item>
			) : (
				<Button
					disabled={!isGuardSheetExportAllowed || loading}
					onClick={() => {
						setOverTime(false);
						handleGuardSheetAsync({
							GuardId: GuardId,
							Name: currLoginUser,
							ListType: 2,
							AppId: 1253985742,
						});
					}}
				>
					导出
					{loading && <Icon type="loading" style={{ marginLeft: 6 }} />}
				</Button>
			)}
		</>
	);
}
