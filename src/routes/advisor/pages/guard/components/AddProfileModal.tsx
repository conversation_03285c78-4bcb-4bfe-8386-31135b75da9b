import React, { useState, useEffect, useMemo } from 'react';
import {
	Modal,
	Input,
	Button,
	Form,
	message
} from '@tencent/tea-component';
import { useForm, useField } from 'react-final-form-hooks';
import { getStatus } from "@src/utils/form";
import { cloneDeep } from 'lodash';
import { createGuardReportSummary, updateGuardReportSummary } from "@src/api/advisor/report";
import { getStorage } from "@src/utils/storage";

export function AddProfileModal({visible, onClose, data}) {
	const guardItemInfo = JSON.parse(getStorage('guardItemInfo'));
	const [writeInfo, setWriteInfo] = useState<{
		Id?: number,
		Content?: string
	}>({});
	const [submitLoading, setSubmitLoading] = useState(false);
	const onSubmit = async (value)=>{
		setSubmitLoading(true);
		try {
			if (writeInfo.Id) {
				await updateGuardReportSummary({
					AppId: guardItemInfo.MainAppId,
					GuardId: guardItemInfo.GuardId,
					ReportSummaryList: [
						{
							Id: writeInfo.Id,
							Content: value.Content
						}
					],
					ShowError: true,
					OnlyData: true
				});
			} else {
				await createGuardReportSummary({
					AppId: guardItemInfo.MainAppId,
					GuardId: guardItemInfo.GuardId,
					ReportSummaryContentList: [value.Content],
					ShowError: true,
					OnlyData: true
				});
			}
			message.success({
				content: '操作成功'
			});
			setSubmitLoading(false);
			onClose(true);
		} catch (e) {
			setSubmitLoading(false);
		}
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {},
		validate: (formInfo) => {
			const validateFormInfo = {};
			validateFormInfo['Content'] = !formInfo['Content'] ? '请输入概要信息' : undefined;
			return validateFormInfo;
		},
	});
	
	const overviewField = useField('Content', form);
	
	useMemo(()=>{
		if (data) {
			form.initialize({
				Content: data.Content
			});
			setWriteInfo({
				...data
			});
		}
	}, [data]);
	
	return (
		<Modal
			caption={'概要信息'}
			visible={visible}
			onClose={()=>{
			onClose();
		}}>
			<form onSubmit={handleSubmit}>
			<Modal.Body>
					<Form>
						<Form.Item
							label={''}
							status={getStatus(overviewField.meta, validating)}
							message={getStatus(overviewField.meta, validating) === "error" && overviewField.meta.error}
						>
							<Input.TextArea
								placeholder={'请输入概要信息'}
								size={'full'}
								{...overviewField.input}
							/>
						</Form.Item>
					</Form>
			</Modal.Body>
			<Modal.Footer>
				<Button
					type={'primary'}
					htmlType="submit"
					loading={submitLoading}
				>
					确定
				</Button>
				<Button
					onClick={
						()=>{
							onClose();
						}
					}
				>
					取消
				</Button>
			</Modal.Footer>
			</form>
		</Modal>
	);
}
