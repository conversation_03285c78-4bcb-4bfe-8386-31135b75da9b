import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Card, Transfer, Table, SearchBox, Select, Form, Bubble, SelectMultiple, Input, message, StatusTip, Radio, Switch } from "@tencent/tea-component";
const { selectable, removeable, scrollable, pageable, autotip } = Table.addons;
import { InputNumber } from "@tencent/tea-component";
import { Text } from "@tencent/tea-component";
import { Modal, Button, Icon } from '@tencent/tea-component';
import { Row, Col } from "@tencent/tea-component";
import { FilterInfoItem } from "@src/types/advisor/guard";
import _ from 'lodash'
import { DescribeProductRegionAndZone, DescribeGuardProductInstances } from '@src/api/advisor/guard';
import { policyItem, InstanceItem } from '@src/types/advisor/guard';
import ResourceSearchBox from "../components/ResourceSearchBox";
import RightSearchBox from "../components/RightSearchBox";
import InstanceInput from './InstanceInput';
import Loading from './Loading';
import { useGuard } from "@src/routes/advisor/pages/guard/components/state/GuardContext";
interface Props {
    CallBack: Function,
    product: string,
    instancePolicys: Array<policyItem>, //实例维度容量策略
    instances: Array<InstanceItem>,//全量的实例维度数据 注意用产品过滤
    appid: number,
    expectedEnlargeDays: number,
    expectedEnlargeTimes: number,
    filterInfo: Array<FilterInfoItem>,
    instanceCol?: any,
    // 是否请求过
    requested?: Boolean,
    // cdn是否显示实例policy
    cdnInstancePolicy?: Boolean,
    currentGuard?: any,
    // 产品无区域信息
    noRegionInfo?: any,
    // 选择的架构图
    archInfo?: any,
    // 默认勾选实例
    allProductInstances?: any,
    // 架构图下的产品
    archProducts?: any
}

// 每批次查询和保存实例的限制
const Limit = 1000
// 页面实例数量超过限制提示
const OperationLimit = 3500
// 单个产品可保存实例限制
const productLimit = 6000
// 重点关注数量限制
const importantLimit = 50

// 组合列 实例ID
const comColumn: any = [
    {
        key: "Instance",
        header: "ID/实例名",
        render: resource => (
            <>
                <p>
                    <a>{resource.InstanceId}</a>
                </p>
                <div style={{ overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }} title={resource.InstanceName} >
                    {resource.InstanceName}
                </div>
            </>
        ),
    }
];

// 根据路径去找值
const findValueByPath = (obj, path) => {
    try {
        const value = path.split('.').reduce((acc, key) => acc?.[key], obj);
        // 如果值是数组，返回逗号拼接的字符串.如果值是字符串，返回本身
        return Array.isArray(value) ? value.join(',') : value
    } catch (error) {
        return '--'; // 如果发生错误，返回--
    }
}
// 根据路径去修改值
function updateNestedField(obj, fieldPath, newValue) {
    const parts = fieldPath.split('.');
    let currentObj = obj;

    for (let i = 0; i < parts.length - 1; i++) {
        if (!currentObj[parts[i]]) {
            currentObj[parts[i]] = {};
        }
        currentObj = currentObj[parts[i]];
    }
    currentObj[parts[parts.length - 1]] = newValue;
}


let inputId = ''
function ResourceSon({archProducts, allProductInstances = [], archInfo = null, CallBack, appid, product, instancePolicys,
    instances, expectedEnlargeDays, expectedEnlargeTimes, filterInfo, instanceCol, requested,
    cdnInstancePolicy, currentGuard, noRegionInfo }: Props, ref) {
    // 是否限制重点关注数量，目前只有常规项目限制
    const isLimit = currentGuard.Project === 2001
    const { validateChange } = useGuard() || {};
    // 获取的 resource id 列表
    const [resourceIdList, setResourceIdList] = useState([]);

    // 已选绑定的 Resource ID key 列表
    const [targetResourceIdKeys, setTargetResourceIdKeys] = useState([]);
    // 表格数据加载状态标志位
    const [loading, setLoading] = useState(false);
    // 表格数据，下方loading
    const [bottomTipLoading, setBottomTipLoading] = useState<boolean>(false)
    // 表格数据加载异常标志位
    const [error, setError] = useState(false);
    // 目标列表项配置
    const [columns, setColumns] = useState([])
    //目标列表记录
    const [records, setRecords] = useState<Array<InstanceItem>>([])
    const [regionOptions, setRegionOptions] = useState([]) //地区选项
    const [region, setRegion] = useState<string>(noRegionInfo ? noRegionInfo.SearchRegion : 'ap-guangzhou') //已选择地区
    //批量编辑--容量策略对象
    const [batchEditorPolicyItem, setBatchEditorPolicyItem] = useState<any>({})
    //批量编辑--当前值
    const [batchEditorValue, setBatchEditorValue] = useState<any>('')
    //批量编辑--弹窗
    const [batchEditorVisible, setBatchEditorVisible] = useState<boolean>(false)
    //默认放量倍数
    const [defaultEnlargeTimes, setDefaultEnlargeTimes] = useState(expectedEnlargeTimes)
    //弹窗：CDN填写
    const [visibleCDNMetricWrite, setVisibleCDNMetricWrite] = useState<boolean>(false)

    // 搜索条件
    const [filter, setFilter] = useState([])
    // 当前产品实例总数量
    const [total, setTotal] = useState<number>(0)
    // 当前产品实例搜索数量
    const [searchTotal, setSearchTotal] = useState<number>(0)
    // 全选弹框标志
    const [selectALL, setSelectALL] = useState(false)
    // 全选状态值
    const [allStatu, setAllStatu] = useState(false)
    // 全选的数据
    const [allData, setAllData] = useState([])
    // 全选的数据
    const [rightFilter, setRightFilter] = useState([])
    // 右侧过滤后的数据
    const [filterRecords, setFilterRecords] = useState([])
    // 重点关注资源个数弹框
    const [importantModal, setImportantModal] = useState(false)
    // 全部清除弹框
    const [removeAllModal, setRemoveAllModal] = useState(false)
    // 部分清除弹框
    const [removeSearchModal, setRemoveSearchModal] = useState(false)

    // 左边实例展示列
    const [leftColumns, setLeftColumns] = useState(_.cloneDeep(comColumn) || [])
    // 右边实例展示列
    const [rightColumns, setRightColumns] = useState(_.cloneDeep(comColumn) || [])
    // 右边实例搜索条件
    const [rightFilterInfo, setRightFilterInfo] = useState([])
    // 点击批量操作的policy的Type
    const [policyType, setPolicyType] = useState('')
    // 全屏loading显示
    const [showLoading, setShowLoading] = useState(false)
    const [tempData, setTempData] = useState([]);
    // 全屏loading显示文字
    const [loadingText, setLoadingText] = useState('加载中...')
    // 已经返回的数量
    const [returnNum, setRetunNum] = useState(0)
    // 已经返回的数量
    const [overLimit, setOverLimit] = useState(false)
    // 组装左右实例列和右侧搜索条件
    useEffect(() => {
        setLeftColumns([])
        setRightColumns([])
        setRightFilterInfo([])
        let newLeftColumns = _.cloneDeep(comColumn) || []
        let newRightColumns = (_.cloneDeep(comColumn).map(i => {
            i.width = 120
            return i
        })) || []
        newRightColumns.unshift({
            key: "removeCol",
            header: '',
            render: resource => (
                <>
                    <Icon onClick={() => {
                        // 实例过多会卡顿，添加loading
                        if (records.length > OperationLimit) {
                            setLoadingText('当前已选实例较多，请稍等...')
                            setShowLoading(true)
                            setTimeout(() => {
                                setTargetResourceIdKeys(targetResourceIdKeys.filter(i => i !== resource.InstanceId))
                                setShowLoading(false)
                            }, 0);
                        } else {
                            setTargetResourceIdKeys(targetResourceIdKeys.filter(i => i !== resource.InstanceId))
                        }
                    }} type="dismiss" style={{ cursor: 'pointer' }} />
                </>
            ),
            width: 30,
        })
        let newightFilterInfo = []
        let productCol = instanceCol.filter(i => i.Product === product)
        let colList = productCol.length ? productCol[0].FilterInfo : []
        // cdn 的Policy过滤
        if (!cdnInstancePolicy && product === 'cdn') {
            colList = colList.filter(i => i.DataFrom !== 'Policy')
        }
        // 重点关注资源个数
        const importantList = records.filter((i: any) => i.Important === 1)
        colList.map((i, index) => {
            // 列结构
            const colData: any = {
                key: i.FilterCorrespondName || i.Location || i.Uses + index,
                header: <>
                    {i.FilterCorrespondName === 'Important' ?
                        i.FilterName + `（${importantList.length || 0}）` :
                        i.FilterName}
                    {/* 描述信息 */}
                    {i.Desc.trim() !== "" && <Bubble
                        arrowPointAtCenter
                        placement="top"
                        content={i.Desc}
                    >
                        <Icon type="info" />
                    </Bubble>}
                    {
                        i.OperationType === "Multiple" && Boolean(records.length) &&
                        <Icon type="pencil" style={{ cursor: "pointer" }} onClick={() => {
                            //设置当前批量设置的容量策略
                            setBatchEditorPolicyItem(i)
                            // 设置当前值为空
                            setBatchEditorValue(i.Type === 'Switch' ? 0 : '')
                            //打开批量编辑框弹窗
                            setBatchEditorVisible(true)
                        }} />
                    }
                </>,
                render: (resource, recordKey, recordIndex) => {
                    // 实例列显示的值
                    let result: any = ''
                    if (i.Type === 'Switch') {
                        // 开关列考虑三种情况，DataFrom为Info、Policy、Extra
                        // 目前有Info、Policy
                        let value: any = false
                        if (i.DataFrom === 'Info') {
                            // 1:true表示勾选、0:false表示未勾选。
                            value = resource[i.FilterCorrespondName] === 1
                        } else if (i.DataFrom === 'Policy') {
                            resource.Policy.map(j => {
                                if (j.MetricName === i.Location) {
                                    // 0:true表示勾选、1:false表示未勾选。
                                    value = j.Value === 0
                                }
                            })
                        } else if (i.DataFrom === 'Extra') {
                            const extraData = resource.Extra ? JSON.parse(resource.Extra) : {}
                            value = findValueByPath(extraData, i.Location)
                        }
                        return <>
                            <Switch
                                onChange={(v) => {
                                    valueChange(resource, v, i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex)
                                }}
                                value={value}
                            />
                        </>
                    } else if (i.Type === 'Input') {
                        // 输入框考虑三种情况，DataFrom为Info、Policy、Extra
                        // 目前只有Policy一种情况
                        let value: any = 0
                        let unit = ''
                        let inputType = ''
                        let inputId = ''
                        let isRequired = false
                        let regRule: any = ''
                        let MetricName: any = ''
                        if (i.DataFrom === 'Info') {
                            value = resource[i.FilterCorrespondName]
                        } else if (i.DataFrom === 'Extra') {
                            const extraData = resource.Extra ? JSON.parse(resource.Extra) : {}
                            value = findValueByPath(extraData, i.Location)
                        } else if (i.DataFrom === 'Policy') {
                            resource.Policy.map(j => {
                                if (j.MetricName === i.Location) {
                                    unit = j.Unit
                                    inputType = j.FieldType
                                    inputId = resource.InstanceId + i.FilterCorrespondName + i.Location + i.DataFrom + j.Type + j.MetricName
                                    isRequired = j.IsRequired || false
                                    regRule = j.RegRule || null
                                    MetricName = j.MetricName
                                    if (j.Type === 'Other') {
                                        value = j.OtherValue
                                    } else {
                                        value = j.Value
                                        // 数值类型的policy要触发一次校验
                                        validateChange && validateChange({
                                            AppId: appid,
                                            Product: resource.Product,
                                            InstanceId: resource.InstanceId,
                                            MetricName: MetricName,
                                            PassValidate: value == 0 ? false : true
                                        })
                                    }
                                }
                            })
                        }
                        return <>
                            {inputType === 'string' ?
                                <InstanceInput
                                    inputId={inputId}
                                    value={value || ''}
                                    CallBack={(v) => {
                                        valueChange(resource, v, i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex, inputType, inputId)
                                    }}
                                    inputValidate={(passValidate) => validateChange && validateChange({
                                        AppId: appid,
                                        Product: resource.Product,
                                        InstanceId: resource.InstanceId,
                                        MetricName: MetricName,
                                        PassValidate: passValidate
                                    })}
                                    required={isRequired}
                                    regRule={regRule}
                                />
                                :
                                <>
                                    {/* 数值类型的policy的值为0无意义，要进行非0校验 */}
                                    <Form hideLabel>
                                        <Form.Item status={value == 0 ? 'error' : 'success'} message={value == 0 ? '该值不能为0' : ''} showStatusIcon={false}>
                                            <InputNumber
                                                size="m"
                                                onChange={(v) => {
                                                    valueChange(resource, v, i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex)
                                                    // 修改校验
                                                    validateChange && validateChange({
                                                        AppId: appid,
                                                        Product: resource.Product,
                                                        InstanceId: resource.InstanceId,
                                                        MetricName: MetricName,
                                                        PassValidate: v == 0 ? false : true
                                                    })
                                                }}
                                                min={0}
                                                step={inputType === 'int' ? 1 : 0.1}
                                                value={value}
                                                hideButton={true}
                                                unit={unit}
                                            />
                                        </Form.Item>
                                    </Form>
                                </>
                            }
                        </>
                    } else if (i.Type === 'Tag') {
                        // 左、右标签字段不一样
                        if (i.Uses === 'Left') {
                            // 标签列
                            const showTags = [];
                            (resource.Tag || []).map(i => {
                                let item = i.TagKey + ":" + i.TagValues
                                showTags.push(item)
                            });
                            result = [...showTags]
                        } else if (i.Uses === 'Right') {
                            result = resource.InstanceTag.split('；')
                        }

                        return <div style={{ overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }} title={result.join('\n')} >
                            {result.join('；')}
                        </div>
                    } else if (i.Type === 'SelectSingle' || i.Type === 'SelectMultiple') {
                        // 目前只考虑Policy一种情况
                        if (i.DataFrom === 'Policy') {
                            let value: any = ''
                            let isRequired = false
                            let options = (i.ExtraShowName || []).map(item => ({ text: item.Value, value: item.Key }))
                            resource.Policy.map(j => {
                                if (j.MetricName === i.Location) {
                                    if (j.Type === 'Other') {
                                        value = j.OtherValue || ''
                                    } else {
                                        value = j.Value || ''
                                    }
                                    isRequired = j.IsRequired || false
                                    if (isRequired) {
                                        validateChange && validateChange({
                                            AppId: appid,
                                            Product: resource.Product,
                                            InstanceId: resource.InstanceId,
                                            MetricName: i.Location,
                                            PassValidate: value == 0 ? false : true
                                        })
                                    }
                                }
                            })
                            let selectDom = i.Type === 'SelectSingle' ?
                                <Select
                                    style={{ width: '100%', verticalAlign: 'top' }}
                                    appearance="button"
                                    options={options}
                                    value={value}
                                    onChange={value => {
                                        valueChange(resource, value, i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex)
                                        if (isRequired) {
                                            // 修改校验
                                            validateChange && validateChange({
                                                AppId: appid,
                                                Product: resource.Product,
                                                InstanceId: resource.InstanceId,
                                                MetricName: i.Location,
                                                PassValidate: Boolean(value.length)
                                            })
                                        }
                                    }
                                    }
                                /> :
                                <SelectMultiple
                                    style={{ width: '100%', verticalAlign: 'top' }}
                                    clearable
                                    options={options}
                                    appearance="button"
                                    value={value ? value.split(';') : []}
                                    onChange={value => {
                                        valueChange(resource, value.join(';'), i.FilterCorrespondName, i.DataFrom, i.Location, i.Type, recordIndex)
                                        if (isRequired) {
                                            // 修改校验
                                            validateChange && validateChange({
                                                AppId: appid,
                                                Product: resource.Product,
                                                InstanceId: resource.InstanceId,
                                                MetricName: i.Location,
                                                PassValidate: Boolean(value.length)
                                            })
                                        }
                                    }}
                                />
                            let reultDom =
                                isRequired ?
                                    <Form hideLabel style={{ width: '100%' }}>
                                        <Form.Item status={Boolean(value.length) ? 'success' : 'error'} message={value.length ? '' : '此项为必填项'} showStatusIcon={false}>
                                            {selectDom}
                                        </Form.Item>
                                    </Form>
                                    :
                                    selectDom
                            return reultDom
                        }
                    }
                    else {
                        // 普通信息展示列
                        if (i.DataFrom === "Info") {
                            // 值从实例本身读取
                            result = resource[i.FilterCorrespondName]
                        } else {
                            // 值从实例Extra字段里读取
                            const extraData = resource.Extra ? JSON.parse(resource.Extra) : {}
                            result = findValueByPath(extraData, i.Location)
                        }
                        // 根据映射展示对应的值
                        if (i.ExtraShowName?.length) {
                            const colValueItem = i.ExtraShowName.filter(i => i.Key === result)
                            result = colValueItem.length ? colValueItem[0].Value : result
                        }
                        return <div style={{ overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }} title={result} >
                            {result}
                        </div>
                    }
                }
            }
            if (i.Uses === 'Left') {
                // 排除ID/实例名
                if (i.FilterCorrespondName !== 'InstanceId' && i.FilterCorrespondName !== 'InstanceName') {
                    newLeftColumns.push(colData)
                }
            } else if (i.Uses === 'Right') {
                // 排除ID/实例名
                if (i.FilterCorrespondName !== 'InstanceId' && i.FilterCorrespondName !== 'InstanceName') {
                    // 设置宽度
                    const data = _.cloneDeep(colData)
                    if (data.key === 'AggregateQuery') {
                        data.width = 210
                    } else if (data.key === 'Important') {
                        data.width = 146
                    } else {
                        data.width = 150
                    }
                    newRightColumns.push(data)
                }
            } else if (i.Uses === 'Search') {
                newightFilterInfo.push(i)
            }
        })
        // 更新表头
        setLeftColumns([...newLeftColumns])
        setRightColumns([...newRightColumns])
        setRightFilterInfo([...newightFilterInfo])
    }, [instanceCol, records, cdnInstancePolicy])

    /*
     resource:要修改的实例
     value：要修改的值
     FilterCorrespondName：DataFrom为Info时要修改的字段名
     DataFrom：数据来源，Info、Extra、Policy
     Location：DataFrom为Extra、Policy时要修改的字段名
     Type：数据的显示类型
     index：当前点击数据的下标
     inputType：输入框类型
     */
    function valueChange(resource, value, FilterCorrespondName, DataFrom, Location, Type, index, inputType?, inputId?) {
        let tmp: Array<InstanceItem> = _.cloneDeep(records)
        // 重点关注资源个数
        const importantList = tmp.filter((i: any) => i.Important === 1)
        if (FilterCorrespondName === 'Important' && importantList.length === importantLimit && value && isLimit) {
            setImportantModal(true)
            setRecords(tmp)
            return
        }
        tmp.map((i: any, index) => {
            if (i.InstanceId === resource.InstanceId) {
                if (DataFrom === 'Info') {
                    tmp[index][FilterCorrespondName] = value
                    if (Type === 'Switch') {
                        tmp[index][FilterCorrespondName] = value ? 1 : 0
                    }
                } else if (DataFrom === 'Extra') {
                    const extraData = tmp[index].Extra ? JSON.parse(tmp[index].Extra) : {}
                    // 修改Extra里对应的值
                    updateNestedField(extraData, Location, value);
                    if (Type === 'Switch') {
                        updateNestedField(extraData, Location, value ? 1 : 0);
                    }
                } else if (DataFrom === 'Policy') {
                    let policy = _.cloneDeep(tmp[index].Policy)
                    policy.map((j, index1) => {
                        if (j.MetricName === Location) {
                            if (j.Type === 'Other') {
                                policy[index1].OtherValue = value
                                // j.Value = 0
                            } else {
                                policy[index1].Value = value
                                // 如果输入类型是string，需要将值转换为string
                                if (i.inputType === 'string') {
                                    policy[index1].Value = value + ''
                                }
                                // 如果是开关，value要从Boolean转位number
                                if (Type === 'Switch') {
                                    policy[index1].Value = Number(!value);
                                }
                            }
                        }
                    })
                    tmp[index].Policy = policy
                }
            }
        })
        setTimeout(() => {
            setRecords(tmp)
            if (inputId && document.getElementById(inputId)) {
                document.getElementById(inputId).focus()
            }

        }, 0);
    }
    useImperativeHandle(ref, () => ({
        Check: () => { },
    }))

    //获取产品可用区选项
    const getProductRegionAndZone = async (product: string) => {
        setShowLoading(true)
        try {
            const res = await DescribeProductRegionAndZone(
                {
                    AppId: appid,
                    Product: product
                })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                setShowLoading(false)
                return
            } else {
                let tmp = []
                res.Regions.map(i => {
                    tmp.push({ text: i.RegionName, value: i.Region })
                })
                // 默认选择第一个地区
                if (tmp.length) {
                    setRegion(tmp[0].value)
                }
                setRegionOptions(tmp)
                setShowLoading(false)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
            setShowLoading(false)

        }
    }
    //批量修改容量策略
    function batchEditor() {
        let tmp: Array<InstanceItem> = _.cloneDeep(records)
        let intitList: Array<InstanceItem> = _.cloneDeep(records)
        tmp.map((i, index) => {
            let saveIndex = filterRecords.findIndex(j => i.InstanceId === j.InstanceId)
            if (saveIndex !== -1) {
                let policy = _.cloneDeep(tmp[index].Policy)
                if (batchEditorPolicyItem.DataFrom === 'Info') {
                    // @ts-ignore
                    tmp[index][batchEditorPolicyItem.FilterCorrespondName] = batchEditorValue
                } else if (batchEditorPolicyItem.DataFrom === 'Extra') {
                    const extraData = tmp[index].Extra ? JSON.parse(tmp[index].Extra) : {}
                    // 修改Extra里对应的值
                    updateNestedField(extraData, Location, batchEditorValue);
                } else if (batchEditorPolicyItem.DataFrom === 'Policy') {
                    policy.map((j, index1) => {
                        if (j.MetricName === batchEditorPolicyItem.Location) {
                            if (j.Type === 'Other') {
                                policy[index1].OtherValue = batchEditorValue
                            } else {
                                policy[index1].Value = batchEditorValue
                            }
                            if (batchEditorPolicyItem.Type === 'Switch') {
                                policy[index1].Value = Number(!batchEditorValue)
                            }
                            if (j.MetricName.trim() === "CDNBandwidth" && batchEditorValue <= 1.0) {
                                setVisibleCDNMetricWrite(true)
                            }
                        }
                    })
                    tmp[index].Policy = policy
                }
            }
        })
        if (batchEditorValue) {
            // 重点关注资源个数，超过50个弹框提醒
            const importantList = tmp.filter((i: any) => i.Important === 1)
            if (importantList.length > importantLimit && isLimit) {
                setImportantModal(true)
                tmp = intitList
                return
            }
        }
        setRecords(tmp)
    }

    // 页面初始化 把instances 已选择的记录传入目标记录
    useEffect(() => {
        let tmp = [];
        instances.map(i => {
            if (i.Product === product) {
                tmp.push(i.InstanceId);
            }
        });
        // 如果有传入的默认选中的id，就直接使用
        if (allProductInstances?.length && archInfo?.value) {
            setFilterRecords([]);
            setTargetResourceIdKeys(allProductInstances.map(i => i.InstanceId));
        } else {
            setTargetResourceIdKeys(tmp);
        }
    }, [JSON.stringify(instances), JSON.stringify(allProductInstances)]);

    useEffect(() => {
        // 已经展开过的产品，要请求数据
        if (requested && !noRegionInfo) {
            getProductRegionAndZone(product)
        }
    }, [requested])

    // 记录当前批量操作的列的输入类型是string、float
    useEffect(() => {
        if (batchEditorPolicyItem?.Location) {
            const policyItemInfo: any = instancePolicys.filter(i => i.MetricName === batchEditorPolicyItem.Location)[0]
            if (policyItemInfo) {
                // FieldType int flota string
                setPolicyType(policyItemInfo.FieldType)
            } else {
                setPolicyType('')
            }
        } else {
            setPolicyType('')
        }

    }, [batchEditorPolicyItem])

    //监听 targetResourceIdKeys 更新records 
    /*
        监听对象 targetResourceIdKeys 为已选绑定的实例ID（右侧框），
        监听对象 resourceIdList 为当前拉取的可供勾选的实例信息列表（左侧框）。
        目标对象 records 为已选已绑定的实例信息，通过勾选左侧框与配置右侧框更新。
    */
    useEffect(() => {
        let recordsTmp: any= _.cloneDeep(records)
        // 遍历keys 把不存在于records里面的记录，添加进去
        targetResourceIdKeys.map(key => {
             // 优先从instances获取
             let l = instances.filter(i => i.InstanceId === key);
            // 判断是否选择了架构图并且架构图中有该实例
            const inArch = archInfo?.value
                && allProductInstances.filter(j => j.InstanceId === key)?.length;
                if (inArch) {
                    l = [];
                }
            if (l.length) {
                if (recordsTmp.filter(i => { if (i.InstanceId === key) { return i } }).length === 0) {
                    const newRecord = _.cloneDeep(l[0]);
                    // 对已保存的policy进行兜底，policy的值为0时将值设置为整体值
                    (newRecord.Policy || []).map(i => {
                        // CDN默认值为0,需要用户手动填写
                        const cdnPolicy = i.MetricName.trim() == "CDNBandwidth" || i.MetricName.trim() == "CDNQPS"
                        i.Value = i.Value || (cdnPolicy ? 0.0 : defaultEnlargeTimes)
                        i.Days = i.Days || expectedEnlargeDays
                    })
                    recordsTmp.push(newRecord)
                }
            } else {
                let items = [];
                 // 当选择了架构图，并且产品在架构图产品列表中时，且架构图中有该实例，直接从架构图实例中过滤
                if (inArch) {
                    items = allProductInstances.filter(j => j.InstanceId === key);
                } else {
                    // 判断是从左侧数据过滤，还是从全部数据中过滤
                    items = (allStatu ? allData : resourceIdList)
                        .filter(j => j.InstanceId === key);
                }
                if (items.length) {
                    if (recordsTmp.filter(i => { if (i.InstanceId === key) { return i } }).length === 0) {
                        // 初始化实例维度容量策略  Value 初始值为 1，Days 初始值为 expectedEnlargeDays
                        let policys = _.cloneDeep(instancePolicys)
                        instancePolicys.map((i, index) => {
                            policys[index].Value = defaultEnlargeTimes
                            policys[index].Days = expectedEnlargeDays
                            // CDN默认值为1
                            if (policys[index].MetricName.trim() == "CDNBandwidth" || policys[index].MetricName.trim() == "CDNQPS") {
                                policys[index].Value = 0.0
                            }
                        })
                        // 标签
                        const showTags = [];
                        if (items[0].Tag?.length) {
                            (items[0].Tag || []).map(i => {
                                let item = i.TagKey + ":" + i.TagValues
                                showTags.push(item)
                            });
                        }
                        // 获取容量策略policy
                        recordsTmp.push({
                            AppId: appid,
                            Product: product,
                            Region: items[0]?.Region,
                            Zone: "",
                            InstanceId: key,
                            Extra: items[0].Extra,
                            InstanceName: items[0].InstanceName,
                            InstanceTag: showTags.length ? showTags.join("; ") : '',
                            Policy: policys,
                            NodeUuid: items[0].NodeUuid,
                            MapId: inArch ? archInfo?.value : '',
                            ProductType: items[0].ProductType
                        })
                    }
                }
            }
        })
        //遍历records，把不存在keys里面的记录删除，并重新设置
        setRecords(recordsTmp.filter(i => { if (targetResourceIdKeys.indexOf(i.InstanceId) > -1) { return i } }))
    }, [targetResourceIdKeys, resourceIdList])
    // 过滤右侧数据
    useEffect(() => {
        let result = [...records]
        if (rightFilter && rightFilter.length) {
            result = []
            rightFilter.forEach(i => {
                records.forEach(j => {
                    let insVal = ''
                    if (i.Location) {
                        const extraData = j.Extra ? JSON.parse(j.Extra) : {}
                        insVal = findValueByPath(extraData, i.Location)
                    } else {
                        insVal = j[i.FilterCorrespondName]
                    }
                    const filterItem = i.Values
                    for (const m of filterItem) {
                        const filterVal = i.Type === 'Tag' ? m.replace(/::/g, ":") : m
                        if (insVal && insVal.includes(filterVal)) {
                            result.push(j)
                            return
                        }
                    }

                })
            })
        }
        setFilterRecords([...result])
    }, [records, rightFilter])

    //监听 records，利用回调接口，回传父组件
    useEffect(() => {
        CallBack(appid, product, records)
    }, [records])

    //搜索框ref
    const searchboxRef = useRef(null)

    //CDN带宽填写弹出框
    const CDNPlsWriteRightModal = (
        < Modal visible={visibleCDNMetricWrite} disableCloseIcon onClose={() => { setVisibleCDNMetricWrite(false); close(); }
        }>
            <Modal.Body>
                <Modal.Message
                    style={{ margin: 20 }}
                    icon="infoblue"
                    message="提示：请如实填写 CDN 带宽"
                    description={
                        <Text style={{ color: "red", whiteSpace: "pre-line" }}>
                            {`不建议您将带宽填写低于1.0的值！\n\n - 如果确认该值已覆盖客户放量风险，请忽略本提示。\n - 如果不确认，请先保存草稿并沟通确认后填写，或指定备注说明。\n\n - 更多监控信息参考 https://yehe.woa.com/cdnadmin 或 barad.isd.com 。`}
                        </Text>
                    }
                />
            </Modal.Body>
            <Modal.Footer>
                <Button type="primary" onClick={() => { setVisibleCDNMetricWrite(false); }} >
                    关闭
                </Button>
            </Modal.Footer>
        </Modal >
    )

    // 子组件过滤查询返回总个数
    function totalChange(filter, total) {
        // 记录当前过滤条件
        setFilter(filter)
        if (filter.length === 0) {
            setSearchTotal(total)
            setTotal(total)
        } else {
            setSearchTotal(total)
        }
    }

    // 获取限制数量的实例
    function getProdecutLimit() {
        // 如果当前搜索实例数量超过限制，直接提示
        if (searchTotal > productLimit) {
            setOverLimit(true)
            return
        }
        setRetunNum(0)
        setLoadingText('实例加载中...')
        setShowLoading(true)
        // 分页查询结果
        let resultList = []
        // 总页数
        const pages = Math.ceil(searchTotal / Limit)
        for (let i = 1; i <= pages; i++) {
            setTimeout(() => {
                resultList.push(getPageInstances(i))
            }, 100);
        }
        setTimeout(async () => {
            await Promise.all(resultList).then(res => {
                if (res && res.length) {
                    // 是否有某次查询没有返回值
                    const hasErr = res.some(i => i.length === 0)
                    if (hasErr) {
                        message.error({
                            content: "当前选中实例过多，请重试，或者降低选中的实例数量",
                        })
                        setShowLoading(false)
                        return
                    }
                    // @ts-ignore
                    let list = res.flat() || []
                    // 保存全部数据
                    setAllData(list)
                    let totalList = [...list, ...records].map(i => i.InstanceId)
                    // 全选的实例和右侧已选实例合并去重后的数量超过限制就提示
                    if (Array.from(new Set(totalList)).length > productLimit) {
                        setOverLimit(true)
                    } else {
                        // 全选
                        setSelectALL(true)
                        setAllStatu(true)
                    }
                }
                setShowLoading(false)
            }).catch(err => {
                console.log(err);
                message.error({
                    content: "当前选中实例过多，请重试，或者减少选中的实例数量",
                })
                setShowLoading(false)
            })
        }, 200);
    }

    // 左侧资源选择
    function selectChange(keys, ctx) {
        // 点击全选框
        if (ctx.check.all) {
            if (ctx.check.value) {
                getProdecutLimit()
            } else {
                // 取消全选
                setSelectALL(true)
                setAllStatu(false)
            }
        } else {
            // 点击复选框
            setAllStatu(false)
            // 单个产品的数量超过限制就提示弹框
            if (keys.length > productLimit) {
                setOverLimit(true)
                return
            }
            // 实例过多会卡顿，添加loading
            if (records.length > OperationLimit) {
                setLoadingText('当前已选实例较多，请稍等...')
                setShowLoading(true)
                setTimeout(() => {
                    setTargetResourceIdKeys(keys)
                    setShowLoading(false)
                }, 0);
            } else {
                setTargetResourceIdKeys(keys)
            }
        }
    }

    // 分页查询所有实例
    async function getPageInstances(pageSize = 1) {
        try {
            const res = await DescribeGuardProductInstances(
                {
                    AppId: appid,
                    Products: [product],
                    Regions: region ? [region] : [],
                    Offset: (pageSize - 1) * Limit,
                    Limit: Limit,
                    Filter: filter
                })
            if (res.Error) {
                return Promise.reject(res.Error)
            } else {
                setRetunNum((val) => {
                    return val + (res.Instances?.[product]?.Instance?.length || 0)
                })
                return Promise.resolve(res.Instances?.[product]?.Instance || [])
            }
        } catch (err) {
            return Promise.reject(err)
        }
    }
    useEffect(() => {
        if (searchTotal > Limit) {
            setLoadingText(`实例较多，加载进度 ${returnNum} / ${searchTotal}`)
        }
    }, [returnNum])

    // 进行全选/取消全选的操作
    async function getAllInstances() {
        setSelectALL(false)
        let list = _.cloneDeep(allData) || []
        if (list.length) {
            if (allStatu) {
                // 全选
                const newList = list.filter(i => !targetResourceIdKeys.includes(i.InstanceId)).map(i => i.InstanceId)
                if (newList && newList.length) {
                    // 这里数据量过大时，页面加载状态较久，可优化？？？
                    setTargetResourceIdKeys([...targetResourceIdKeys, ...newList]);
                }
            } else {
                // 取消全选
                let leftSelect = [...targetResourceIdKeys]
                list.forEach(i => {
                    let index = leftSelect.findIndex(j => j === i.InstanceId)
                    if (index !== -1) {
                        leftSelect.splice(index, 1)
                    }
                });
                setTargetResourceIdKeys([...leftSelect])
            }
        }
    }
    // 关闭全选弹框
    function closeSelectAllModal() {
        setSelectALL(false)
        setAllStatu(false)
    }
    // 右侧搜索条件变化
    function rightFilterChange(filter) {
        let newFilter = []
        filter.forEach(i => {
            rightFilterInfo.forEach(j => {
                if (i.Name === j.FilterName) {
                    newFilter.push(Object.assign({}, i, j))
                }
            });
        });
        setRightFilter([...newFilter])
    }
    return (
        <div key={product}>
            <section>
                <Row style={{ marginTop: 10 }}>
                    {/* <Col style={{ marginTop: 10 }} span={2} >
                        <Text theme="label">重点护航实例</Text>
                    </Col> */}
                    <Col span={24}>
                        <Transfer
                            header={
                                <div className='resourceRefreshWrap'>
                                    {!noRegionInfo && <>
                                        <Form>
                                            <Form.Item label="地域">
                                                <Select
                                                    options={regionOptions}
                                                    value={region}
                                                    onChange={(v) => { setRegion(v) }}
                                                    appearance="button"
                                                    searchable
                                                    size="m"
                                                />
                                            </Form.Item>
                                        </Form>
                                        </>
                                    }
                                    <Button type='weak' loading={loading} onClick={() => { searchboxRef.current.search(false); }}>
                                        刷新列表
                                    </Button>
                                </div>
                            }
                            leftCell={
                                <Transfer.Cell
                                    style={{ width: '36%' }}
                                    scrollable={false}
                                    title={`选择资源（共${total}个，当前搜索${(filter?.length) ? searchTotal : 0}个）`}
                                    tip="如果筛选超过100个实例，请下拉加载完全。建议一次筛选不超500实例。"
                                    header={
                                        requested && <ResourceSearchBox
                                            CallBack={(v) => { setResourceIdList(v) }}
                                            filterInfo={filterInfo}
                                            appid={appid}
                                            region={region}
                                            product={product}
                                            updateLoading={(v) => { setLoading(v) }}
                                            updateBottomTipLoading={(v) => { setBottomTipLoading(v) }}
                                            ref={searchboxRef}
                                            totalChange={totalChange}
                                        />
                                    }
                                >
                                    <Table
                                        records={resourceIdList}
                                        disableTextOverflow={true}
                                        recordKey="InstanceId"
                                        rowDisabled={record => record.status === "stopped"}
                                        rowClassName={record => record.status}
                                        columns={leftColumns}
                                        // bottomTip={bottomTipLoading && <StatusTip status="loading"></StatusTip>}
                                        addons={[
                                            scrollable({
                                                virtualizedOptions: {
                                                    height: 310,
                                                    itemHeight: 70,
                                                    onScrollBottom: () => {
                                                        if (!bottomTipLoading) {
                                                            searchboxRef.current.search(true)
                                                        }
                                                    },
                                                },

                                            }),
                                            autotip({
                                                isLoading: loading,
                                                isError: error,
                                            }),
                                            selectable({
                                                value: targetResourceIdKeys,
                                                onChange: (keys, ctx) => { selectChange(keys, ctx) },
                                                rowSelect: true,
                                            }),
                                        ]}
                                    />
                                </Transfer.Cell>
                            }
                            rightCell={
                                <Transfer.Cell
                                    title={
                                        <>
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <h4>已选资源 (共{records.length}个，当前搜索{(rightFilter?.length) ? filterRecords.length : 0}个)</h4>
                                                <Button style={{ marginLeft: 20 }} type="primary" disabled={!records.length} onClick={() => { setRemoveAllModal(true) }}>
                                                    全部清除
                                                </Button>
                                                <Bubble
                                                    arrowPointAtCenter
                                                    placement="top"
                                                    content="按搜索结果清除实例"
                                                >
                                                    <Button style={{ marginLeft: 10 }} type="primary" disabled={(rightFilter?.length) ? !filterRecords.length : true} onClick={() => { setRemoveSearchModal(true) }}>
                                                        部分清除
                                                    </Button>
                                                </Bubble>

                                            </div>

                                        </>
                                    }
                                    header={
                                        requested && <RightSearchBox
                                            CallBack={rightFilterChange}
                                            filterInfo={rightFilterInfo}
                                            appid={appid}
                                            region={region}
                                            product={product}
                                        />
                                    }>
                                    <Table
                                        records={filterRecords}
                                        disableTextOverflow={true}
                                        recordKey="InstanceId"
                                        columns={rightColumns}
                                        addons={[
                                            scrollable({
                                                virtualizedOptions: {
                                                    height: 310,
                                                    itemHeight: 70,
                                                },
                                            }),
                                        ]}
                                    />
                                </Transfer.Cell>
                            }
                        />
                    </Col>
                </Row>
            </section >

            {
                /* 全部清除 */
                <Modal size="s" disableCloseIcon visible={removeAllModal} onClose={() => { setRemoveAllModal(false) }
                }>
                    <Modal.Body> <h3 style={{ textAlign: 'center' }}>{`您将清除全部已选择实例（共 ${records.length} 个）`}</h3></Modal.Body>
                    <Modal.Footer>
                        <Button type="primary" onClick={() => {
                            setTargetResourceIdKeys([])
                            setRemoveAllModal(false)
                        }}>
                            确认
                        </Button>
                        <Button type="weak" onClick={() => { setRemoveAllModal(false) }}>
                            取消
                        </Button>

                    </Modal.Footer>
                </Modal>
            }
            {
                /* 部分清除 */
                <Modal size="s" disableCloseIcon visible={removeSearchModal} onClose={() => { setRemoveSearchModal(false) }
                }>
                    <Modal.Body> <h3 style={{ textAlign: 'center' }}>{`您将保留当前搜索实例（共 ${filterRecords.length} 个），其它实例不再保留？`}</h3></Modal.Body>
                    <Modal.Footer>
                        <Button type="primary" onClick={() => {
                            setTargetResourceIdKeys((filterRecords || []).map(i => i.InstanceId))
                            setRemoveSearchModal(false)
                        }}>
                            确认
                        </Button>
                        <Button type="weak" onClick={() => { setRemoveSearchModal(false) }}>
                            取消
                        </Button>

                    </Modal.Footer>
                </Modal>
            }
            {
                /* 重点关注资源个数弹框 */
                <Modal size="s" disableCloseIcon visible={importantModal} onClose={() => { setImportantModal(false) }
                }>
                    <Modal.Body> <h3 style={{ textAlign: 'center' }}>{`已经达到重点关注资源上限 ${importantLimit} 个，无法勾选更多`}</h3></Modal.Body>
                    <Modal.Footer>
                        <Button type="primary" onClick={() => {
                            setImportantModal(false)
                        }}>
                            确认
                        </Button>

                    </Modal.Footer>
                </Modal>
            }

            {
                /* 单个产品可保存的实例数量上限弹框 */
                <Modal maskClosable disableCloseIcon size="auto" visible={overLimit} onClose={() => { setOverLimit(false) }}>
                    <Modal.Body>
                        <Modal.Message
                            icon="warning"
                            message={`单个产品可保存的实例数量上限为${productLimit}`}
                        />
                    </Modal.Body>
                    <Modal.Footer>
                        <Button type="primary" onClick={() => { setOverLimit(false) }}>
                            确定
                        </Button>
                    </Modal.Footer>
                </Modal>
            }

            {
                /* 资源全选弹框 */
                <Modal size="s" disableCloseIcon visible={selectALL} onClose={() => { closeSelectAllModal }}>
                    <Modal.Body> <h3 style={{ textAlign: 'center' }}>{`您要${allStatu ? '' : '取消'}选中${(filter?.length) ? '当前搜索' : '全部'}实例（共 ${searchTotal} 个）吗？`}</h3></Modal.Body>
                    <Modal.Footer>
                        <Button type="primary" onClick={() => {
                            getAllInstances()
                        }}>
                            {allStatu ? '确认全选' : '取消全选'}
                        </Button>
                        <Button type="weak" onClick={() => { closeSelectAllModal() }}>
                            取消
                        </Button>
                    </Modal.Footer>
                </Modal>
            }

            {
                /* 批量编辑实例维度容量策略弹窗 */
                batchEditorPolicyItem &&
                <Modal size="s" visible={batchEditorVisible} caption={"批量编辑" + ` 当前搜索资源（共 ${filterRecords.length} 个）` || ''} disableCloseIcon >
                    <Modal.Body>
                        <span style={{ marginRight: 20 }}> {batchEditorPolicyItem.FilterName}</span>
                        {batchEditorPolicyItem.Type === 'Switch' &&
                            <>
                                <Switch
                                    onChange={(v) => { setBatchEditorValue(Number(v)) }}
                                    value={!!batchEditorValue}
                                />
                            </>
                        }
                        {batchEditorPolicyItem.Type === 'SelectSingle' &&
                            <>
                                <Select
                                    style={{ width: 240 }}
                                    appearance="button"
                                    options={(batchEditorPolicyItem.ExtraShowName || []).map(item => ({ text: item.Value, value: item.Key }))}
                                    value={batchEditorValue}
                                    onChange={value => {
                                        setBatchEditorValue(value)
                                    }
                                    }
                                />
                            </>
                        }
                        {batchEditorPolicyItem.Type === 'SelectMultiple' &&
                            <>
                                <SelectMultiple
                                    style={{ width: 240 }}
                                    appearance="button"
                                    options={(batchEditorPolicyItem.ExtraShowName || []).map(item => ({ text: item.Value, value: item.Key }))}
                                    value={batchEditorValue ? batchEditorValue.split(';') : []}
                                    onChange={value => {
                                        setBatchEditorValue(value.join(';'))
                                    }
                                    }
                                />
                            </>
                        }
                        {
                            batchEditorPolicyItem.Type === 'Input' &&
                            (policyType === 'string' ?
                                <Input
                                    onChange={(v) => { setBatchEditorValue(v) }}
                                    value={batchEditorValue + ''}
                                /> :
                                <>
                                    <InputNumber
                                        size="m"
                                        onChange={(v) => { setBatchEditorValue(v) }}
                                        min={(product === 'cdn' && (batchEditorPolicyItem.Location === 'CDNQPS' || batchEditorPolicyItem.Location === 'CDNBandwidth')) ? 1 : 0}
                                        step={policyType === 'int' ? 1 : 0.1}
                                        value={batchEditorValue}
                                    /><Text>{batchEditorPolicyItem.Unit || ''}</Text>
                                </>
                            )

                        }

                    </Modal.Body>
                    <Modal.Footer>
                        <Button type="primary" onClick={() => {
                            //批量刷新
                            batchEditor()
                            setBatchEditorVisible(false)
                        }}>
                            确定
                        </Button>
                        <Button type="weak" onClick={() => { setBatchEditorVisible(false) }}>
                            取消
                        </Button>
                    </Modal.Footer>
                </Modal>
            }
            {
                // CDN填写放量值提醒弹窗
                CDNPlsWriteRightModal
            }
            {/* 全屏遮罩 */}
            <Loading show={showLoading} text={loadingText}></Loading>
        </div >
    )
}

export default forwardRef(ResourceSon)