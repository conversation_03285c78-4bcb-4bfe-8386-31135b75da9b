import React, { useState, useEffect } from 'react';
import { Card, message as tips, Row, Col, Layout, message, Tabs, TabPanel, Button, PopConfirm, Radio, Form, Input, Bubble, Alert, TagSelect, SelectOptionWithGroup } from '@tencent/tea-component';
import { Text } from "@tencent/tea-component";
import { CreateGuardAfterSalesApproval, DescribeGuardAfterSalesApproval, DescribeGuardProjects } from '@src/api/advisor/guard';
import { StandardDict, AfterSalesApproval } from '@src/types/advisor/guard';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { useHistory, useAegisLog } from '@tea/app';
import _ from 'lodash'
import { getUserInfo } from '@src/api/common';
import RTXPicker from '@tencent/qmfe-yoa-react-ui/es/RTXPicker/index';
const { Body, Content } = Layout;
const { TextArea } = Input;



export function ApprovalSales(match) {
    const history = useHistory();
    const aegis = useAegisLog();
    //售后审核信息
    const [apvlData, setApvlData] = useState<AfterSalesApproval>({} as AfterSalesApproval)
    //当前护航ID
    const [guardId, setGuardId] = useState(Number(match.match.params.guardid))
    //当前审核名称
    const [apvlName, setApvlName] = useState("")
    //当前审核状态，0-未处理、1-已处理
    const [apvlStatus, setApvlStatus] = useState(0)
    //护航项目选项
    const [projectDict, setProjectDict] = useState(new Map())
    //是否有审批权限
    const [hasOpsPermission, setHasOpsPermission] = useState(false)
    // 需要驻场支持的云产品
    const [onsiteProductZh, setOnsiteProductZh] = useState([])
    // 护航天数
    const [guardDay, setGuardDay] = useState(0)
    // 是否同意
    const [isAgree, setIsAgree] = useState(false)
    // 同意选项值
    const [agreeOption, setAgreeOption] = useState(true)
    // （同意）派遣支持人员
    const [supporter, setSupporter] = useState<Array<string>>([])
    // （不同意）理由 
    const [reason, setReason] = useState("")
    // （同意）派遣支持人员--提示
    const [visibleSupporterEdit, setVisibleSupporterEdit] = useState(false)
    // （不同意）理由--提示
    const [visibleReasonEdit, setVisibleReasonEdit] = useState(false)
    // 基本信息
    const apvlId = apvlData.ApprovalId             //任务ID
    const creator = apvlData.Creator               //提单人
    const guardName = apvlData.GuardName           //护航名称
    const project = apvlData.Project               //护航项目
    const standard = apvlData.Standard             //护航类型
    const startTime = apvlData.StartTime           //护航开始时间
    const endTime = apvlData.EndTime               //护航结束时间
    const keyTime = apvlData.ImportantGuardTime               //重点护航时间
    const onsiteTime = apvlData.OnsiteTime         //驻场时间
    const onsiteProducts = apvlData.OnsiteProducts //驻场产品
    const customerInfo = apvlData.CustomerInfo
    const customerInfoStr = (customerInfo || []).map((val) => {
        return <div><Text>{val.AppId + " | " + val.CustomerName}</Text></div>
    })
    const oneAppId = (customerInfo || []).length > 0 ? customerInfo[0].AppId : 0

    //未填写字段
    const noSet = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>
    //护航时间展示
    const startDate = (startTime || "")
    const endDate = (endTime || "")
    const guardTimeStr = (startTime && endTime) ? [(startDate === endDate ? startDate : (startDate + " ~ " + endDate)) + " 共 ", <Text style={{ color: "blue" }}>{guardDay}</Text>, " 天"] : [noSet]
    //重点护航时间展示
    const keyTimeLength = (keyTime || []).length
    const keyTimeStr = keyTimeLength ? [keyTime.join(","), " 共 ", <Text style={{ color: "blue" }}>{keyTimeLength}</Text>, " 天"] : [noSet]
    //驻场护航时间展示
    const onsiteTimeLength = (onsiteTime || []).length
    const onsiteTimeStr = onsiteTimeLength ? [onsiteTime.join(","), " 共 ", <Text style={{ color: "blue" }}>{onsiteTimeLength}</Text>, " 天"] : [noSet]
    //当前登录rtx
    const [rtx, setRtx] = useState<string>('')
    //获取当前登录账号rtx
    const getCurrentOperator = async () => {
        try {
            const res = await getUserInfo()
            const name = res.data.EngName || ''
            setRtx(name)
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //计算两个Y-m-d H:m:s格式的时间差
    const getGuardDay = async (startTime, endTime) => {
        var reggie = /(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/;
        var formatStartTime = reggie.exec(startTime);
        var formatEndTime = reggie.exec(endTime);
        var objStartTime = new Date()
        var objEndTime = new Date()
        if (formatStartTime && formatEndTime) {
            objStartTime = new Date(
                (+formatStartTime[1]),
                (+formatStartTime[2]) - 1,
                (+formatStartTime[3]),
                (+formatStartTime[4]),
                (+formatStartTime[5]),
                (+formatStartTime[6])
            );
            objEndTime = new Date(
                (+formatEndTime[1]),
                (+formatEndTime[2]) - 1,
                (+formatEndTime[3]),
                (+formatEndTime[4]),
                (+formatEndTime[5]),
                (+formatEndTime[6])
            );
        }
        const diffDay = ((objEndTime.getTime() - objStartTime.getTime()) / 1000 / 60 / 60 / 24).toFixed(0)
        setGuardDay(Number(diffDay))
    }

    //查询售后审核信息
    const getSalesApprovalInfo = async () => {
        try {
            const res = await DescribeGuardAfterSalesApproval({
                AppId: oneAppId || 1253985742,
                GuardId: Number(match.match.params.guardid),
                Operator: rtx,
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setSupporter(res.AfterSalesApproval.Supporter || [])               //同意内容
                setApvlData(res.AfterSalesApproval)

                setApvlStatus(res.AfterSalesApproval.Status)                 //审核状态
                setHasOpsPermission(res.AfterSalesApproval.HasOpsPermission) //是否有权限审批
                setIsAgree(res.AfterSalesApproval.IsAgree)                   //是否同意
                setApvlName(res.AfterSalesApproval.ApprovalName)             //审批名称
                setReason(res.AfterSalesApproval.Reason)                     //不同意内容
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 变更审核状态
    const modifyApprovalStatus = async (agreeOption) => {
        try {
            const res = await CreateGuardAfterSalesApproval({
                ApprovalId: apvlId,
                GuardId: guardId,
                AppId: oneAppId || 1253985742,
                IsAgree: agreeOption,
                Supporter: supporter,
                Reason: reason,
                Operator: rtx,
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "提交成功" })
            //更新审核状态，重载页面
            // location.reload()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //获取云产品中文描述
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: 1253985742, // CGI代码直接拉取advisor_strategy或advisor_product表，暂无appid指定。
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let onsiteProductDesc = []
                for (var key in onsiteProducts) {
                    let productEn = onsiteProducts[key]
                    onsiteProductDesc.push(res.ProductDict[productEn] || "")
                }
                setOnsiteProductZh(onsiteProductDesc)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //查询护航项目清单
    const getProjects = async () => {
        try {
            const res = await DescribeGuardProjects({
                AppId: 1253985742, //接口必须传appid  为获取全量产品列表，因此传内部中心账号
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                let tmp = []
                let m = new Map()
                res.Projects.map(i => {
                    tmp.push({ value: i.Id.toString(), text: i.Name })
                    m.set(i.Id, i.Name)
                })
                // setProjectOptions(tmp)
                setProjectDict(m)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //init
    useEffect(() => {
        getCurrentOperator()
        if (rtx) {
            getSalesApprovalInfo()
            // getGuardApprovalProgress()
        }
    }, [rtx])

    useEffect(() => {
        aegis.reportEvent({
            name: 'manual-PV',
            ext1: location.pathname,
            ext2: '护航审批/处理页面',
            ext3: localStorage.getItem('engName')
        })
    }, [])

    useEffect(() => {
        getProjects()
        getProductsGroupsInfo()
    }, [onsiteProducts])

    useEffect(() => {
        if (startTime !== "" && endTime !== "") {
            getGuardDay(startTime, endTime)
        }
    }, [startTime, endTime])

    return (
        <Body>
            {
                apvlId === 0 && <div style={{ margin: 20, color: "red" }}>
                    <Alert type="error" style={{ marginTop: 8 }}>审核ID不存在！请联系管理员。</Alert>
                </div>
            }
            {apvlId && <Content>
                <Content.Header
                    showBackButton
                    onBackButtonClick={() => { history.push('/advisor/approval') }}
                    title={apvlName || "售后审批"}>
                </Content.Header>
                <Content.Body>
                    <Card key={"apvl_base_info"} style={{ margin: 10 }}>
                        <Card.Body>
                            <Row gap={30}>
                                <Col>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">任务ID</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{apvlId}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">提单人</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{creator}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航ID | 名称</Text>
                                            </Col>
                                            <Col span={18}>
                                                <a href={"/advisor/guard/summary/" + guardId} target="_blank">{guardId} </a> | <a href={"/advisor/guard/summary/" + guardId} target="_blank">{guardName}</a>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">客户 APPID | 名称</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{customerInfoStr}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航项目</Text>
                                            </Col>
                                            <Col span={18}>
                                                {projectDict.get(project)}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航类型</Text>
                                            </Col>
                                            <Col span={18}>
                                                {StandardDict.get(standard)}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航时间</Text >
                                            </Col>
                                            <Col span={18}>
                                                {/* {startTime.split(" ")[0] + " ~ " + endTime.split(" ")[0] + " 共 "}<Text style={{ color: "blue" }}>{guardDay}</Text>{" 天"} */}
                                                {guardTimeStr}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航重点时间</Text >
                                            </Col>
                                            <Col span={18}>
                                                {keyTimeStr}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">驻场护航时间</Text >
                                            </Col>
                                            <Col span={18}>
                                                {onsiteTimeStr}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">驻场重点支持的云产品</Text >
                                            </Col>
                                            <Col span={18}>
                                                {(onsiteProductZh.length != 0) ? (onsiteProductZh || []).join("、") : noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">封网需求</Text >
                                            </Col>
                                            <Col span={18}>
                                                {apvlData.ClosedNetworkDemand || noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航背景及需求</Text >
                                            </Col>
                                            <Col span={18}>
                                                {apvlData.StatementOfNeeds || noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                    {/* <section>
                                        <Row verticalAlign={'middle'}>
                                            <Col span={6}>
                                                <Text theme="label" verticalAlign="middle">每日巡检</Text>
                                            </Col>
                                            <Col span={18}>
                                                {apvlData.CronType === 0 ? '未开启' : '开启'}
                                            </Col>
                                        </Row>
                                    </section> */}
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>

                    <Card key={"apvl_sales"} style={{ margin: 10 }}>
                        <Card.Body>
                            {apvlStatus == 0 && hasOpsPermission && <Radio.Group value={agreeOption ? "true" : "false"} onChange={value => setAgreeOption(value === "true")}>
                                <Radio name="true">同意</Radio>
                                <Radio name="false">不同意</Radio>
                            </Radio.Group>}

                            {/* 已审批，对应展示内容 */}
                            {apvlStatus == 1 && isAgree &&

                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    <Form.Item label="审批意见">
                                        <Form.Text>{"同意"}</Form.Text>
                                    </Form.Item>
                                    <Form.Item label="派遣支持人员">
                                        <Form.Text>{supporter.join("; ") || noSet}</Form.Text>
                                    </Form.Item>
                                </Form>
                            }
                            {apvlStatus == 1 && !isAgree &&
                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    <Form.Item label="审批意见">
                                        <Form.Text>{"不同意"}</Form.Text>
                                    </Form.Item>
                                    <Form.Item label="理由">
                                        <Form.Text>{reason || noSet}</Form.Text>
                                    </Form.Item>
                                </Form>
                            }

                            {/* 未审批，对应填写内容 */}
                            {apvlStatus == 0 && agreeOption &&
                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    <Form.Item label=" * 派遣支持人员" align='middle'>
                                        <Bubble error visible={visibleSupporterEdit} content={'请输入支持人员名称'}>
                                            {apvlData.ApprovalId && <RTXPicker
                                                style={{ "width": "75%" }}
                                                value={supporter}
                                                valueType="array"
                                                disabled={!hasOpsPermission}
                                                onChange={(tags: any) => {
                                                    // 限制选择一个人员
                                                    if (tags.length > 1) {
                                                        tips.warning({ content: '最多可选1个人员' });
                                                        setSupporter([tags[0]]);
                                                        return;
                                                    }
                                                    setSupporter(tags ? tags : [])
                                                    if (tags.length === 0) {
                                                        setVisibleSupporterEdit(true)
                                                    } else {
                                                        setVisibleSupporterEdit(false)
                                                    }
                                                }}
                                            />}
                                        </Bubble>
                                    </Form.Item>
                                </Form>
                            }
                            {apvlStatus == 0 && !agreeOption &&
                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    <Form.Item label=" * 理由">
                                        <Bubble error visible={visibleReasonEdit} content={'请输入理由'}>
                                            <TextArea size={'l'} value={reason} placeholder="请输入理由"
                                                disabled={!hasOpsPermission}
                                                onChange={(value) => {
                                                    setReason(value)
                                                    if (value.trim() === '') {
                                                        setVisibleReasonEdit(true)
                                                    } else {
                                                        setVisibleReasonEdit(false)
                                                    }
                                                }}
                                            />
                                        </Bubble>
                                    </Form.Item>
                                </Form>
                            }
                            <div style={{ margin: 20, textAlign: "center" }}>
                                {hasOpsPermission && !apvlStatus &&
                                    <Button type={"weak"} style={{ marginRight: 15 }} onClick={() => { history.push('/advisor/approval') }} >
                                        取消
                                    </Button>}
                                {hasOpsPermission && !apvlStatus &&
                                    <PopConfirm
                                        arrowPointAtCenter={true}
                                        title={"确认要提交审核结果吗？"}
                                        message={"提交后，审核内容不再支持修改!"}
                                        disabled={(agreeOption && supporter.length === 0) || (!agreeOption && reason === "")}
                                        footer={close => (
                                            <>
                                                <Button type={"primary"} onClick={() => { modifyApprovalStatus(agreeOption); history.push('/advisor/approval'); close(); }}>
                                                    确定
                                                </Button>
                                                <Button onClick={() => { close() }} type="text">{"取消"}</Button>
                                            </>
                                        )}
                                        placement={"top"}>
                                        <Button type={"primary"} disabled={(agreeOption && supporter.length === 0) || (!agreeOption && reason === "")}>
                                            提交
                                        </Button>
                                    </PopConfirm>
                                }
                            </div>
                        </Card.Body>
                    </Card>
                </Content.Body>
            </Content>}
        </Body >
    );
}
