import React, { useState, useEffect, useRef } from 'react';
import {  Card, message as tips, Row, Col, Layout, Button, Text, Alert, Tabs, message, Bubble, Input } from '@tencent/tea-component';
import {  ModifyGuardAfterSaleConfirmStatus, DescribeGuardAfterSalesConfirmApproval, BindGuardSheet, DescribeGuardProjects, DescribeGuardSheet } from '@src/api/advisor/guard';
import { ApvlStatusDict, ApvlTypeDict, Filter, GuardParams, StandardDict } from '@src/types/advisor/guard';
import { useHistory } from '@tea/app';
import './index.less';
import { Collapse } from 'tdesign-react';
import { ChevronDownDoubleIcon, ChevronUpDoubleIcon } from 'tdesign-icons-react';
import { CustomerName } from '../../CustomerName';
import { ArchList } from './components/ArchList';
import moment from 'moment';
import { getSearchParam } from '@src/utils/common';
import SpecialApprovalModal from '@src/routes/advisor/components/special-approval-modal';
import { authUrl } from '@src/utils/constants';
import cooperationImg from '@src/assets/cooperation.svg';
import { CheckAuthorization } from '@src/api/common';
import { getProcessEnv } from '../../../../../../../../app/utils';

const { Panel } = Collapse;
const { Body, Content } = Layout;

export function SalesConfirm(match) {
	const specialModalRef = useRef(null);
	const history = useHistory();
	const operator = localStorage.getItem('engName');
	// 是否是更新架构图
	const reSelect = getSearchParam('reSelect', location) === 'true';

	// 当前护航ID
	const guardId = Number(match.match.params.guardid);
	// 当前审批ID
	const apvlId = Number(match.match.params.apvlid);

	// 当前护航单数据
	const [currentGuardData, setCurrentGuardData] = useState<GuardParams>({});
	// 折叠面板状态
	const [expand, setExpand] = useState(false);
	// tabs
	const [tabs, setTabs] = useState([]);
	// 当前打开appid
	const [currentAppId, setCurrentAppId] = useState<string>(currentGuardData?.MainAppId?.toString());

	// 当前审批单数据
	const [currentApprovalData, setCurrentApprovalData] = useState<any>({});
	// 是否云架构协作权限
	const [hasAuth, setHasAuth] = useState(true);

	// 页面标题
	const pageTile = `任务ID:${apvlId} | ${currentApprovalData.ApprovalName}`;
	const [projectDict, setProjectDict] = useState(new Map());
	// 客户账号和名称
	const [customerInfo, setCustomerInfo] = useState([]);
	const createEnv = ['ISA', 'CONSOLE'];
	// 选择的架构图id
	// eslint-disable-next-line max-len
	const archId = currentGuardData?.CloudGuardBaseInfoOtherPlatform?.find(i => createEnv.includes(i.Platform) && i.AppId === currentGuardData?.MainAppId)?.PlatformUniqueId;
	// 护航详细字段
	const guardName = currentGuardData.GuardName;                                            // 护航名称
	const standard = currentGuardData.Standard;                                              // 护航类型
	const startDate = (currentGuardData.StartTime || '');                  // 护航开始时间
	const endDate = (currentGuardData.EndTime || '');                          // 护航结束时间
	const guardTime = (startDate === endDate) ? startDate : (`${startDate} ~ ${endDate}`);
	const project = currentGuardData.Project;                                                // 护航项目
	const expectedEnlargeDays = currentGuardData.ExpectedEnlargeDays;                        // 整体放量预估天数
	const expectedEnlargeTimes = currentGuardData.ExpectedEnlargeTimes;                      // 整体放量预估倍数
	const expectedEnlarge = `对比近 ${expectedEnlargeDays} 天业务放量 ${expectedEnlargeTimes} 倍`;
	const keyTime = (currentGuardData.ImportantGuardTime || []).join(', ');                             // 护航重点时间
	// 审核展示字段
	const apvlType = currentApprovalData.Type;    // 审核任务类别
	const apvlStatus = currentApprovalData.Status;    // 审核任务状态
	// 可以操作的条件，有权限并且未处理
	const canOperate = currentApprovalData.HasOpsPermission && currentApprovalData.Status === 0;

	// 全部的appid列表
	// 未填写字段
	const noSet = <Text style={{ color: 'LightGray' }} verticalAlign="middle">未填写</Text>;
	const [reSelectArch, setReSelectArch] = useState<any>({});
	// 特殊审批信息
	const [approvalData, setApprovalData] = useState(null);

	// 获取权限
	async function getAuth(guardData) {
		try {
			const res = await CheckAuthorization({
				CustomerAppId: guardData?.MainAppId,
				IsCheckTAM: false,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const auth = res?.AuthCode === 100;
			setHasAuth(auth);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	}

	// 查询护航项目清单
	const getProjects = async () => {
		try {
			const res = await DescribeGuardProjects({
				AppId: 1253985742, // 接口必须传appid  为获取全量产品列表，因此传内部中心账号
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmp = [];
			const m = new Map();
			res.Projects.map((i) => {
				tmp.push({ value: i.Id.toString(), text: i.Name });
				m.set(i.Id, i.Name);
			});
			setProjectDict(m);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询护航单详情
	const getGuardSheet = async () => {
		try {
			const filters: Array<Filter> = [
				{ Name: 'guard_id', Values: [match.match.params.guardid] },
			];
			const res = await DescribeGuardSheet({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset: 0,
				Limit: 10,
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const item = res.Guard[0] || {};
			setCurrentGuardData(item);
			// 对appId查询云架构协作权限,自助护航兜底判断
			if (item?.GuardId && item?.Standard !== 3 && !(item?.RelatedAppId?.length > 0) && getProcessEnv() !== 'production-abroad') {
				getAuth(item);
			}
			const appids = [item.MainAppId].concat(item.RelatedAppId);
			const customerNames = [item.CustomerName].concat(item.RelatedCustomerNames);
			const tempInfo = [];
			// 获取当前Tab页
			const tmpTabs = [];
			appids.forEach((i, index) => {
				tempInfo.push(`${i} | ${customerNames[index]}`);
				tmpTabs.push({
					id: i.toString(),
					label: <CustomerName appid={i} />,
				});
			});
			setCustomerInfo(tempInfo);
			setTabs(tmpTabs);
			setCurrentAppId(tmpTabs?.[0]?.id);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询审核单详情
	const getGuardApprovalData = async () => {
		try {
			const params = {
				ApprovalId: apvlId,
				Operator: operator,
			};
			const res = await DescribeGuardAfterSalesConfirmApproval(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setCurrentApprovalData(res || {});
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};


	// 操作护航单 操作类型；1-确认/2-驳回/3-跳过/4-关闭护航单
	async function operateGuard(type) {
		try {
			const params = {
				GuardId: guardId,
				ApprovalId: apvlId,
				OperateType: type,
				Operator: operator,
			};
			const res = await ModifyGuardAfterSaleConfirmStatus(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			message.success({ content: '提交成功' });
			history.push('/advisor/approval');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 绑定架构图
	async function bindPlatform() {
		const { GuardCreateSource, ArchId } = reSelectArch;
		try {
			const params = {
				MainAppId: parseInt(currentAppId, 10),
				GuardID: guardId,
				CustomerName: currentGuardData.CustomerName || '',
				GuardName: `${currentGuardData.CustomerName}护航需求${moment().format('YYYYMMDDHH')}`,
				StartTime: moment().add(2, 'hours')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
			  	EndTime: moment().add(1, 'days')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
				OtherPlatforms: [
					{ Platform: GuardCreateSource || 'ISA', PlatformUniqueId: ArchId, AppId: parseInt(currentAppId, 10) },
				],
				OpsType: 'add',
			};
			const res = await BindGuardSheet(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			message.success({ content: '架构图保存成功' });
			location.href = `/advisor/approval/sales-confirm/${guardId}/${apvlId}`;
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	useEffect(() => {
		// 如果有架构图就查询架构图信息
		if (archId && !reSelect) {
			history.push(`/advisor/new-architecture/architecture/${archId}?appid=${currentGuardData.MainAppId}&plugin=cloud-escort-sdk`);
		}
	}, [archId]);

	// init
	useEffect(() => {
		getGuardApprovalData();
		getProjects();
		getGuardSheet();
	}, []);

	return (
		<Body>
			<Content>
				<Content.Header title={pageTile} showBackButton onBackButtonClick={() => {
					history.push('/advisor/approval');
				}}></Content.Header>
				<Content.Body>
					<Collapse expandIconPlacement='right' onChange={() => {
						setExpand(!expand);
					}} >
						<Panel expandIcon={expand ? <ChevronUpDoubleIcon className='expandIcon' /> : <ChevronDownDoubleIcon className='expandIcon' />} className='taskPanel' header={
							 <div className='taskStatement'>
							 <Text theme="label">任务说明</Text>
							 <Text theme="text" className='text'>请为本次护航指定架构图，可选择或新增护航架构图。</Text>
						 </div>
						}>
							<Card key={'apvl_base_info'}>
								<Card.Body>
									<Row gap={30}>
										<Col>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">任务类别</Text>
													</Col>
													<Col span={18}>
														<Text>{ApvlTypeDict.get(apvlType)}</Text>
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">护航ID</Text>
													</Col>
													<Col span={18}>
														<a href={`/advisor/guard/summary/${guardId}`} target="_blank">{guardId} </a>
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">护航名称</Text>
													</Col>
													<Col span={18}>
														<Text>{guardName}</Text>
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">护航类型</Text>
													</Col>
													<Col span={18}>
														{StandardDict.get(standard)}
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">护航项目</Text>
													</Col>
													<Col span={18}>
														{projectDict.get(project)}
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">封网需求</Text>
													</Col>
													<Col span={18}>
														{currentGuardData.ClosedNetworkDemand || noSet}
													</Col>
												</Row>
											</section>
										</Col>
										<Col>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">任务状态</Text>
													</Col>
													<Col span={18}>
														<Text><a>{ApvlStatusDict.get(apvlStatus)}</a></Text>
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">护航时间</Text>
													</Col>
													<Col span={18}>
														{guardTime}
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6}>
														<Text theme="label" verticalAlign="middle">护航重点时间</Text>
													</Col>
													<Col span={18}>
														{keyTime ? keyTime : noSet}
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">客户 APPID | 名称</Text>
													</Col>
													<Col span={18}>
														{(customerInfo || []).map((val, i) => <div key={i}>
															<Text>{val}</Text>
														</div>)}
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6}>
														<Text theme="label" verticalAlign="middle">整体放量预估</Text>
													</Col>
													<Col span={18}>
														{expectedEnlarge}
													</Col>
												</Row>
											</section>
											<section>
												<Row verticalAlign={'middle'}>
													<Col span={6}>
														<Text theme="label" verticalAlign="middle">护航背景及需求</Text>
													</Col>
													<Col span={18}>
														{currentGuardData.StatementOfNeeds || noSet}
													</Col>
												</Row>
											</section>
										</Col>
									</Row>
								</Card.Body>
							</Card>
						</Panel>
					</Collapse>

					<Card key={'apvl_detail'} className='resourceCard' >
						<Card.Body>
							{!hasAuth && <Alert type={approvalData?.Reason ? 'warning' : 'error'} className='selectAlert'>
								<span>客户未打开云架构服务授权，请指引客户访问</span>
								<Text theme="primary" copyable className='authLink' onClick={() => {
									window.open(authUrl);
								}}>{authUrl}</Text>
						 		 <Bubble
									arrowPointAtCenter
									placement="top"
									overlayClassName='cooperationBubble'
									content={<img src={cooperationImg} />}
								>
									<span className='imgHover'>打开“云架构协作”。</span>
								</Bubble>
								<Bubble content={currentApprovalData.Status === 1 ? '护航已提交售后特殊审批' : null}>
									<span className='specialLink' onClick={() => {
										if (currentApprovalData.Status === 1) return;
										specialModalRef.current.showModal();
									}}>无法完成授权，申请特批</span>
									{approvalData?.Reason && <span style={{ marginLeft: 16 }}>- 已申请特批</span>}
								</Bubble>
							</Alert>}
							{hasAuth && <Alert>请选择架构图，或者新增架构图，并在架构图中完成下一步流程。</Alert>}
							<Tabs tabs={tabs} placement={'top'} activeId={currentAppId} onActive={(v) => {
								setCurrentAppId(v.id);
							}} destroyInactiveTabPanel={false}>
								{tabs.map(i => (
									<ArchList
										hideAddBtn={!hasAuth || currentApprovalData.Status === 1}
										currentGuardData={currentGuardData}
								 key={i.id} appId={Number(i.id)}
								 updateSelectArch={(arch) => {
											setReSelectArch(arch);
										}} />
								))}
							</Tabs>
						</Card.Body>
					</Card>
					<div className='btnWrap stickyBtn'>
						<Bubble content={currentApprovalData.Status === 1 ? '护航已提交售后特殊审批' : null}>
							<Button type={'primary'} onClick={bindPlatform} disabled={!reSelectArch?.ArchId || !canOperate}>
                            下一步
							</Button>
						</Bubble>
						<Bubble content={currentApprovalData.Status === 1 ? '护航已提交售后特殊审批' : null}>
							<Button type={'primary'} disabled={!canOperate} onClick={() => {
								operateGuard(4);
							}}>
                            关闭护航单
							</Button>
						</Bubble>
						<Bubble content={currentApprovalData.Status === 1 ? '护航已提交售后特殊审批' : null}>
							<Button type={'weak'} disabled={!canOperate} onClick={() => {
								operateGuard(2);
							}}>
                            驳回
							</Button>
						</Bubble>
					</div>
				</Content.Body>

				{/* 特殊审批弹框 */}
				{currentGuardData?.GuardId && <SpecialApprovalModal
					type={2}
					ref={specialModalRef}
					appId={currentGuardData.MainAppId}
					guardData={currentGuardData}
					approvalCallBack={() => {
						// 跳过上一步
						operateGuard(1);
					}}
					updateApprovalData={(data) => {
						setApprovalData(data);
					}}
				/>}
			</Content>
		</Body >
	);
}

