.taskPanel{
    .t-collapse-panel__content{
        padding: 0 !important; 
    }
    .expandIcon{
        width: 20px;
        height: 20px;
        color: rgba(0, 82, 217, 0.9)
    }
    .taskStatement{
        .text{
            font-weight: 800;
            margin-left: 60px;
        }
        .specialApproval{
            margin-left: 16px;
        }
    }
}

.resourceCard{
    margin: 10px 0;
    .resourceLabel{
        .label{
            margin-right: 30px;
        }
        .toBtn{
            margin: 0 10px;
        }
    }
    .archSelect{
        margin: 10px 0;
        .selectArchBtn{
            margin-left: 10px;
        }
    }
}


.btnWrap{
    margin-top: 20px;
    text-align: center;
    button{
        margin-right: 5px;
    }
}

.stickyBtn{
    position: sticky;
    bottom: 20px;
}

