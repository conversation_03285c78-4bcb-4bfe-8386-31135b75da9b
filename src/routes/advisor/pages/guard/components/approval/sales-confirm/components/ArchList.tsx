import React, { useState, useEffect } from 'react';
import { TagSearchBox, Table, Button, StatusTip, message as tips, Text, Icon, Modal } from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import moment from 'moment';
import { DescribeGuardArchList, DescribeGuardMapGenStatus } from '@src/api/advisor/guard';
import { getProcessEnv } from '../../../../../../../../../app/utils';
import './index.less';
import { useHistory } from '@tea/app';
const { radioable, pageable, scrollable, mergeable } = Table.addons;;
const attributes: any = [
	{
		type: 'input',
		key: 'SearchKey',
		name: '架构图标题',
	},
	{
		type: 'input',
		key: 'ArchId',
		name: '架构ID',
	},
];

interface Props {
	currentGuardData?: any,
	appId?: number,
	updateSelectArch?: Function,
	hideAddBtn?: Boolean
}

export function ArchList({ currentGuardData, appId, updateSelectArch = () => {}, hideAddBtn = true }: Props) {
	const history = useHistory();
	// 搜索条件
	const [filterInfo, setFilterInfo] = useState([]);
	// 架构图列表
	const [archList, setArchList] = useState([]);
	// 选择的架构图
	const [selectedArchId, setSelectedArchId] = useState('');
	// 查询接口常规参数
	const [loading, setLoading] = useState<boolean>(false);
	const [total, setTotal] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);
	const [pageSize, setPageSize] = useState<number>(20);
	// 新建按钮loading
	const [btnLoading, setBtnLoading] = useState<boolean>(false);

	// 架构图列表列名
	const archListColumns = [
		{
			key: 'ArchName',
			header: '架构图标题',
			render: (item) => {
				if (item.ArchId !== -1) {
					return item.ArchName;
				}
				return <>
					{
						btnLoading
							? <Icon type="loading" />
							: <div className='addArchWrap' onClick={goToArch}>
								<Icon type="plus" />
								<span className='addArchBtn'>新增架构图</span>
							</div>
					}
				</>;
			},
		},
		{
			key: 'ArchId',
			header: '架构ID',
		},
		{
			key: 'CreateSource',
			header: '来源',
			render: item => (item.CreateSource === 'console' ? '客户创建' : '运营端发送'),
		},
		{
			key: 'GuardStatus',
			header: '护航状态',
			render: item => initGuardStatu(item),
		},
		{
			key: 'IsRelateTaskNum',
			header: '关联任务单',
			render: item => (item.IsRelateTaskNum ? '已关联' : '未关联'),
		},
		{
			key: 'operate',
			header: '操作',
			// eslint-disable-next-line no-nested-ternary
			render: item => (item.IsRelateTaskNum
				? <Button
					style={{ textDecoration: 'underline', cursor: 'pointer' }}
					type="link"
					onClick={() => {
						window.open(`https://${(getProcessEnv() === 'production' || getProcessEnv() === 'production-abroad') ? '' : 'test-'}antool.woa.com/fe-base/antool-page/visit/info?taskId=${item.RelateTaskNum}&processInstanceId=${item.RelateUniqueId}`);
					}}>
				已关联任务单{item.RelateTaskNum}
			  </Button>
				: (item.GuardStatus === 0 || item.GuardStatus === 1) ?  <Text theme="primary">提交后将进入架构图完善护航信息</Text>
					:  <Text theme="success">可直接提交</Text>),
			width: 200,
		},
	];

    	// 判断当前护航状态
	function initGuardStatu(guardInfo) {
		const strMap = {
			0: '未在护航',
			1: '护航前准备',
			2: '护航中',
		};
		const iconMap = {
			0: 'not-escorting',
			1: 'before-escorting',
			2: 'escorting',
		};
		// 护航开始、结束时间、护航单状态
		const { StartTime, EndTime, GuardStatus } = guardInfo;
		let status = 0;
		const today = moment();
		const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
		const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
		// 没有草稿单、护航单过期、有草稿单并未过期、护航单终止等异常
		if (!GuardStatus || today.isAfter(end) || GuardStatus < 0 || (GuardStatus === 1 && !today.isAfter(end))) {
			status = 0;
		} else {
			if (today.isBefore(start)) {
				// 护航前准备
				status = 1;
			} else {
				// 护航中
				status =  2;
			}
		}
		return <>
			<div className='guardStatusWrap'><IconTsa type={iconMap[status]} /><span>{strMap[status]}</span></div>
		</>;
	}

    	// 查询架构图列表
	async function getArchList(PageNumber = 1, PageSize = pageSize, archId = null) {
		setArchList([]);
		// 在重新查询前清空选中
		setSelectedArchId('');
		updateSelectArch({});
		setLoading(true);
		// 搜索条件组装
		const Filters = [];
		filterInfo.forEach((i) => {
			const Name = i.attr?.key || 'SearchKey';
			const Values = [];
			i.values.forEach((j) => {
				if (Values.indexOf(j.name) === -1) {
					  Values.push(j.name);
				}
			});
			Filters.push({ Name, Values });
		});
		try {
			const res = await DescribeGuardArchList({
				AppId: appId,
				PageNumber,
				PageSize,
				Filters,
			});
			if (res.Error) {
				setLoading(false);
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			if (archId) {
				// 默认选中新增的架构图
				const selectArch = (res.GuardArchList || []).find(i => i.ArchId === archId);
				setSelectedArchId(archId || '');
				updateSelectArch(selectArch || {});
			}
			setTotal(res.TotalCount);
			setArchList([{ ArchId: -1 }].concat(res.GuardArchList || []));
			setLoading(false);
		} catch (err) {
			setLoading(false);
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 跳转去架构图，执行生图能力
	function goToArch() {
		if (hideAddBtn) return;
		setBtnLoading(true);
		// 护航单ID
		const taskId = currentGuardData.GuardId;
		// 生图后需要启动的插件
		const toStartPlugin = 'cloud-escort-sdk';
		// 架构图的名称
		const archName = `${currentGuardData.GuardName}-架构图-${moment().format('YYYYMMDDHHmmss')}`;
		// 架构图描述
		const archDesc = `架构图来源：云顾问运营端发送\n发送时间：${moment().format('YYYY/MM/DD/ HH:mm')}\n发送人：腾讯云架构师\n如有疑问，请联系您的售后服务经理`;
		// 记录开始生图
		DescribeGuardMapGenStatus({
			taskId: `${taskId}`,
			status: 'start',
			AppId: currentGuardData.MainAppId,
		})
			.catch((err) => {
				console.log(err);
			});

		// 生图链接
		history.push(`/advisor/new-architecture/architecture/autodraw?taskId=${taskId}&appid=${appId}&toStartPlugin=${toStartPlugin}&archName=${archName}&archDesc=${archDesc}`);
	}

	useEffect(() => {
		getArchList();
	}, [filterInfo]);

	return (<div className='archListWrap'>
		<TagSearchBox
			tips='支持架构图标题、ID查询筛选'
			hideHelp
			minWidth={400}
			style={{ width: 400, marginBottom: 10 }}
			attributes={attributes}
			value={filterInfo}
			onChange={(value) => {
				setFilterInfo(value);
			}}
		/>
		<Table
			className='archListTable'
			verticalTop
			records={archList}
			recordKey="ArchId"
			columns={archListColumns}
			bordered
			disableHoverHighlight
			// 草稿单且有任务单的可选择
			rowDisabled={i => i.IsRelateTaskNum && i.GuardStatus > 1 }
			rowClassName={i => (i.GuardStatus > 1 ? 'guardRow' : 'noGuardRow')}
			topTip={
				(loading || archList.length === 0) && (
					<StatusTip status={loading ? 'loading' : 'empty'} />
				)
			}
			addons={[
				pageable({
					recordCount: total,
					pageSize,
					pageIndex: pageNumber,
					onPagingChange: (query) => {
						if (loading) {
							return;
						}
						setPageNumber(query.pageIndex);
						setPageSize(query.pageSize);
						getArchList(query.pageIndex, query.pageSize);
					},
				}),
				radioable({
					value: selectedArchId,
					onChange: (key) => {
						if (Number(key) !== -1) {
							const arch = archList.find(i => i.ArchId === key) || {};
							updateSelectArch(arch);
							setSelectedArchId(key);
						}
					},
					rowSelect: true,
					render: (element, { disabled }) => (disabled ? '' : element),
				}),
				scrollable({
					maxHeight: 500,
				}),
				  mergeable({
					colSpan: (columnIndex, recordIndex) => {
					  if (recordIndex === 0) {
							if (columnIndex === 1) {
								return archListColumns.length + 1;
							}
							return 0;
					  }
					  return 1;
					},
				  }),
			]}
		/>

	</div>);
}

