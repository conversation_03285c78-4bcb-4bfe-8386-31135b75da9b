import React, { useState, useEffect, useMemo } from 'react';
import { Card, message as tips, Row, Col, Layout, message, Tabs, TabPanel, Button, PopConfirm, Form, Bubble, TagSelect, SelectOptionWithGroup, Timeline, Icon } from '@tencent/tea-component';
import { Text, Radio } from "@tencent/tea-component";
import { DescribeGuardApprovalProgress, DescribeGuardProjects, DescribeGuardScanResult, DescribeGuardSheet, ModifyGuardScanResultStatus, TransferGuardApproval, DescribeGuardSubApprovalDetails, AdditionGuardSubApproval, DescribeGuardSubApproval, CreateGuardSubApproval } from '@src/api/advisor/guard';
import { AppIdCustomerNameDict, ApprovalProgressParams, ApprovalResultParams, ApprovalResultRet, ApvlStatusDict, ApvlTypeDict, EmergencyPlanParams, EmergencyPlanResult, Filter, GuardParams, InstanceItem, policyI<PERSON>, ScanResult, StandardDict, Transfer, VolumeResult } from '@src/types/advisor/guard';
import { useHistory, useAegisLog } from '@tea/app';
import { CustomerName } from '../CustomerName';
import { ApprovalDetailInstance } from './ApprovalDetailInstance';
import _ from 'lodash'
import ApprovalDetailVolume from './ApprovalDetailVolume';
import ApprovalDetailScan from './ApprovalDetailScan';
import ApprovalDetailEmergency from './ApprovalDetailEmergency';
import { getUserInfo } from '@src/api/common';
import RTXPicker from '@tencent/qmfe-yoa-react-ui/es/RTXPicker/index';
import { Switch } from "@tencent/tea-component";
const { Body, Content } = Layout;
import { Select, Input } from 'tdesign-react';
import moment from "moment";

function MergeRelatedCustormer(appidArr, nameArr) {
    let concatArr = new Array()
    for (let i = 0; i < appidArr.length; ++i) {
        let appendName = "|" + nameArr[i]
        if (typeof nameArr[i] === 'undefined') {
            // 如果客户名称未定义，则 appid|name 变为 appid
            appendName = ""
        }
        concatArr.push(appidArr[i] + appendName)
    }
    return concatArr.join(", ")
}

export function ApprovalDetail(match) {
    const history = useHistory();
    const aegis = useAegisLog();
    //当前护航单数据
    const [currentGuardData, setCurrentGuardData] = useState<GuardParams>({})
    //当前审批单数据
    const [currentApprovalData, setCurrentApprovalData] = useState<ApprovalProgressParams>({})
    //当前审批结果
    const [currentApprovalResult, setCurrentApprovalResult] = useState<ApprovalResultRet>({})
    //当前护航ID
    const [guardId, setGuardId] = useState(Number(match.match.params.guardid))
    //当前审批ID
    const [apvlId, setApvlId] = useState(Number(match.match.params.apvlid))
    //当前审核名称
    const [apvlName, setApvlName] = useState("")
    //当前产品
    const [apvlProduct, setApvlProduct] = useState<string>("")
    //产品关联的appid
    const [productAppidsDict, setProductAppidsDict] = useState(new Map())
    const [productDesc, setProductDesc] = useState([]);
    //页面标题
    const pageTile = "任务ID:" + apvlId + " | " + currentApprovalData.TaskName
    //护航项目选项
    const [projectOptions, setProjectOptions] = useState([])
    const [projectDict, setProjectDict] = useState(new Map())
    //菜单选中
    const [hasOpsPermission, setHasOpsPermission] = useState(false)
    //客户账号和名称
    const [customerInfo, setCustomerInfo] = useState<Array<AppIdCustomerNameDict>>([])
    //护航详细字段
    const guardName = currentGuardData.GuardName                                            // 护航名称
    const standard = currentGuardData.Standard                                              // 护航类型
    const startDate = (currentGuardData.StartTime || "")                  // 护航开始时间
    const endDate = (currentGuardData.EndTime || "")                          // 护航结束时间
    const guardTime = (startDate === endDate) ? startDate : (startDate + " ~ " + endDate)
    const project = currentGuardData.Project                                                // 护航项目
    const mainAppId = currentGuardData.MainAppId                                            // 客户APPID
    const expectedEnlargeDays = currentGuardData.ExpectedEnlargeDays                        // 整体放量预估天数
    const expectedEnlargeTimes = currentGuardData.ExpectedEnlargeTimes                      // 整体放量预估倍数
    const expectedEnlarge = `对比近 ${expectedEnlargeDays} 天业务放量 ${expectedEnlargeTimes} 倍`
	const keyTime = (currentGuardData.ImportantGuardTime || []).join(", ")                             // 护航重点时间
    //审核展示字段
    const apvlType = currentApprovalData.TaskType    // 审核任务类别
    const apvlStatus = currentApprovalData.Status    // 审核任务状态
    //全部的appid列表
    const [appids, setAppids] = useState<Array<number>>([])
    //当前tab打开appid
    const [currentAppId, setCurrentAppId] = useState<string>("")
    //实例维度 护航容量策略
    const [instancePolicyDict, setInstancePolicyDict] = useState<any>({})
    //实例维度 护航容量策略是否查询完毕 --避免子组件重复刷新
    const [instancePolicyGot, setInstancePolicyGot] = useState<boolean>(false)
    //开启非空校验 CustomerName组件
    const [need, setNeed] = useState<boolean>(false)
    //未填写字段
    const noSet = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>
    //流转人员
    const [targetTransfers, setTargetTransfers] = useState<Array<string>>([])
    //历史流转人员节点
    const [tranferNodes, setTransferNodes] = useState([])
    //是否流转
    const [moveSelected, setMoveSelected] = useState<boolean>(false);
    //流转人员允许编辑
    const [visibleTargetTransferEdit, setVisibleTargetTransferEdit] = useState<boolean>(false)


    //当前登录rtx
    const [rtx, setRtx] = useState<string>('')
    //获取当前登录账号rtx
    const getCurrentOperator = async () => {
        try {
            const res = await getUserInfo()
            const name = res.data.EngName || ''
            setRtx(name)
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    useEffect(() => {
        // 新建子流程提示
        const AddSubProcessResult = localStorage.getItem('AddSubProcessResult')
        if (AddSubProcessResult) {
            tips.success({ content: AddSubProcessResult })
            localStorage.removeItem('AddSubProcessResult')
        }
        // 审批子流程提示
        const ApprovalSubProcessResult = localStorage.getItem('ApprovalSubProcessResult')
        if (ApprovalSubProcessResult) {
            tips.success({ content: ApprovalSubProcessResult })
            localStorage.removeItem('ApprovalSubProcessResult')
        }
    }, [])

    //查询护航项目清单
    const getProjects = async () => {
        try {
            const res = await DescribeGuardProjects({
                AppId: 1253985742, //接口必须传appid  为获取全量产品列表，因此传内部中心账号
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                let tmp = []
                let m = new Map()
                res.Projects.map(i => {
                    tmp.push({ value: i.Id.toString(), text: i.Name })
                    m.set(i.Id, i.Name)
                })
                setProjectOptions(tmp)
                setProjectDict(m)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //查询护航单详情
    const getGuardSheet = async () => {
        try {
            let filters: Array<Filter> = [
                { Name: 'guard_id', Values: [match.match.params.guardid] },
            ]
            const res = await DescribeGuardSheet({
                Filters: filters.filter(i => { if (i.Values.length) { return i } }),
                Offset: 0,
                Limit: 10,
                AppId: 1253985742, //接口必须传appid
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                let item = res.Guard[0] || {}
                setCurrentGuardData(item)

                let appids = [item.MainAppId].concat(item.RelatedAppId)
                setAppids(appids)

                let tmpDict = new Map()
                // 产品只添加关联了巡检实例的 APPID 
                item.ProductDesc.map(ins => {
                    if (tmpDict.has(ins.Product)) {
                        let t = tmpDict.get(ins.Product)
                        t.add(ins.AppId)
                        tmpDict[ins.Product] = t
                    } else {
                        tmpDict.set(ins.Product, new Set([ins.AppId]))
                    }
                })
                setProductDesc(item.ProductDesc)
                setProductAppidsDict(tmpDict)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //查询审核单详情
    const getGuardApprovalProgress = async () => {
        try {
            let filters: Array<Filter> = [
                { Name: 'id', Values: [match.match.params.apvlid] },
            ]
            const res = await DescribeGuardApprovalProgress({
                Filters: filters.filter(i => { if (i.Values.length) { return i } }),
                Offset: 0,
                Limit: 100,
                AppId: 1253985742, //接口必须传appid
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setCurrentApprovalData(res.Progress[0] || {})

                let apvlProduct = res.Progress[0].Product
                setApvlProduct(apvlProduct)

                let apvlName = res.Progress[0].TaskName
                setApvlName(apvlName)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //查询审核结果实例和解决方案
    const getGuardScanResult = async () => {
        try {
            const res = await DescribeGuardScanResult({
                AppId: 1253985742, //基础参数AppId
                AppIds: [],        //为空表示拉取全部
                GuardId: guardId,
                Product: apvlProduct,
                Operator: rtx,
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setCurrentApprovalResult(res)
                setCustomerInfo(res.CustomerInfo)
                setHasOpsPermission(res.HasOpsPermission)
                setInstancePolicyGot(true)
                // 当前流转环节
                updateTransferNodes(res.TransferRoute)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    const updateTransferNodes = (transferRoute) => {
        let nodes = []
        let nodeLength = transferRoute.length
        if (nodeLength == 0) {
            nodes.push(
                <Timeline.Item key={-1} label="暂无记录" style={{ height: 5 }} theme={"default"} ></Timeline.Item>
            )
        } else {
            transferRoute.map((item, idx) => {
                if (idx + 1 === nodeLength) {
                    nodes.push(
                        <Timeline.Item className='transferItem' key={idx} label={item.UpdateTime} icon={apvlStatus === 1 ? '' : <Icon type="loading" />}>
                            <div className='recordItemWrap'>
                                <div className='itemLabel'>{apvlStatus === 1 ? '提交' : '处理'}人员：</div>
                                <div className='itemValue'>{item.Handlers?.length ? item.Handlers.join("; ") : '--'}</div>
                            </div>
                        </Timeline.Item>
                    )
                } else {
                    nodes.push(
                        <Timeline.Item className='transferItem' key={idx} label={item.UpdateTime}>
                            <div className='recordItemWrap'>
                                <div className='itemLabel'>流转人员：</div>
                                <div className='itemValue'>{item.Handlers?.length ? item.Handlers.join("; ") : '--'}</div>
                            </div>
                            {item.Options &&
                                <div className='recordItemWrap'>
                                    <div className='itemLabel'>流转选项：</div>
                                    <div className='itemValue'>{item.Options || '--'}</div>
                                </div>
                            }
                            <div className='recordItemWrap'>
                                <div className='itemLabel'>流转备注：</div>
                                <div className='itemValue'>{item.Remark || '--'}</div>
                            </div>
                        </Timeline.Item>
                    )
                }
            })
        }
        setTransferNodes(nodes)
    }

    // 变更审核状态
    const modifyGuardScanResultStatus = async (guardId, mainAppId, product, state) => {
        try {
            const res = await ModifyGuardScanResultStatus({
                GuardId: guardId,
                AppId: mainAppId,
                Product: product,
                State: state,
                Operator: rtx,
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "提交成功" })
            //更新审核状态，重载页面
            //location.reload() 
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //流转
    const transferGuardApproval = async () => {
        // 如果选了子流程，就要调新建子流程接口
        if (selectSubprocess.length) {
            const CreateTime = moment().format('YYYY-MM-DD HH:mm:ss')
            try {
                const params = {
                    AppId: mainAppId,
                    GuardId: guardId,
                    ApprovalId: apvlId,
                    SubApprovalDetailsIds: selectSubprocess,
                    Operator: rtx,
                    Handlers: targetTransfers,
                    Remark: toOtherRemark,
                    CreateTime
                }
                const res = await AdditionGuardSubApproval(params)
                if (res.Error) {
                    let msg = res.Error.Message
                    tips.error({ content: msg });
                    return
                }

                // 新建成功的子流程名称
                const successList = (res.SuccessSubApprovalDetailsIDs || []).map(i => {
                    const processName = subProcessList.find(s => s.value === i)?.label || ''
                    return processName
                })
                // 新建失败的子流程名称
                const failList = (res.FailSubApprovalDetailsIDs || []).map(i => {
                    const processName = subProcessList.find(s => s.value === i)?.label || ''
                    return processName
                })
                const str = `${successList.length ? `${successList.join('、')} 流转成功。` : ''}${failList.length ? `${failList.join('、')} 流转失败。` : ''}`
                // 保存新建子流程的结果提示
                localStorage.setItem('AddSubProcessResult', str)

                location.reload()
            } catch (err) {
                const msg = err.msg || err.toString() || "未知错误"
                tips.error({ content: msg });
            }
        } else {
            // 正常走流转
            try {
                const params = {
                    GuardId: guardId,
                    AppId: mainAppId,
                    ApprovalId: apvlId,
                    Product: apvlProduct,
                    Handlers: targetTransfers,
                    Operator: rtx,
                    Remark: toOtherRemark,
                }
                const res = await TransferGuardApproval(params)
                if (res.Error) {
                    let msg = res.Error.Message
                    tips.error({ content: msg });
                    return
                }
                tips.success({ content: "流转成功" })
                //更新审核状态，重载页面
                location.reload()

            } catch (err) {
                const msg = err.msg || err.toString() || "未知错误"
                tips.error({ content: msg });
            }
        }
    }
    //init
    useEffect(() => {
        getCurrentOperator()
        if (rtx) {
            getGuardSheet()
            getGuardApprovalProgress()
        }
    }, [rtx])

    useEffect(() => {
        if (apvlProduct) {
            getGuardScanResult()
            // 获取产品可选的子流程
            getSubProcess()
            // 获取添加的子流程
            getAddSubProcess()
        }
    }, [apvlProduct])

    useEffect(() => {
        // setCurrentGuardData(guardBaseInfo)
        getProjects()
        aegis.reportEvent({
            name: 'manual-PV',
            ext1: location.pathname,
            ext2: '护航审批/处理页面',
            ext3: localStorage.getItem('engName')
        })
    }, [])


    const tabs: Array<{ id: string, label: React.ReactNode }> = useMemo(() => {
        let tmp: Array<{ id: string, label: React.ReactNode }> = []
        appids.forEach((item) => {
            let tgtResults = (currentApprovalResult.ScanResult || []).filter(ins => { if (ins.AppId === item) { return ins } }) || []
            if (tgtResults?.length == 0) {
                return;
            }
            tmp.push({
                id: item.toString(),
                label: <CustomerName appid={item} need={need} />
            })
        });
        if (tmp.length == 0) {
            const appId: any = customerInfo?.[0]?.['AppId']
            tmp.push({
                id: appId?.toString(),
                label: <CustomerName appid={appId || ''} need={need} />
            })
            setCurrentAppId(appId?.toString())
        } else {
            setCurrentAppId(tmp[0]?.id)
        }
        return tmp
    }, [productAppidsDict, apvlProduct, need, customerInfo, appids, currentApprovalResult])
    // 子流程列表
    const [initSubProcess, setInitSubProcess] = useState([])
    // 流转选项
    const [subProcessList, setSubProcessList] = useState([])
    // 当前选择的子流程
    const [selectSubprocess, setSelectSubprocess] = useState([]);
    // 流转给他人的备注
    const [toOtherRemark, setToOtherRemark] = useState('');
    // 子流程审批备注
    const [subProcessRemark, setSubProcessRemark] = useState('');
    // 已添加的子流程
    const [savedSubProcess, setSavedSubProcess] = useState([])
    // 待审批的子流程
    const [pendingSubProcess, setPendingSubProcess] = useState([])
    // 是否全部审批完
    const [finishSubProcess, setFinishSubProcess] = useState(false)
    useEffect(() => {
        // 子流程填充默认审批人
        if (!targetTransfers.length && selectSubprocess.length) {
            let personStr = subProcessList.filter(i => selectSubprocess.includes(i.value) && i.DefaultTransferor).map(i => i.DefaultTransferor).join(';')
            if (personStr) {
                setVisibleTargetTransferEdit(true)
                setTargetTransfers(Array.from(new Set(personStr.split(';'))))
            }
        }
    }, [selectSubprocess])
    // 获取产品下的子流程
    async function getSubProcess() {
        try {
            const res = await DescribeGuardSubApprovalDetails({
                AppId: 1253985742,
                Filters: [
                    { Name: 'product', Values: [apvlProduct] },
                    { Name: 'approval_type', Values: ['1'] },
                ],
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            const tempList = res.Data || []
            // 保存初始化的子流程
            setInitSubProcess(tempList)
            // 构造子流程选项
            setSubProcessList(tempList.map(i => {
                return {
                    label: i.ApprovalContent,
                    value: i.ID,
                    DefaultTransferor: i.DefaultTransferor || ''
                }
            }))
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 获取已添加的子流程
    async function getAddSubProcess() {
        try {
            const res = await DescribeGuardSubApproval({
                AppId: 1253985742,
                Filters: [
                    { Name: 'guard_id', Values: [guardId + ''] },
                    { Name: 'father_approval_id', Values: [apvlId + ''] },
                    { Name: 'product', Values: [apvlProduct] },
                ],
                Operator: rtx
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            // 保存护航单已添加的子流程
            setSavedSubProcess(res.SubApprovalInfoSlice || [])
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 审批子流程
    async function approvalSubProcess() {
        try {
            const IsAgreeFilter = pendingSubProcess.map(i => {
                return {
                    SubApprovalId: i.ID,
                    IsAgree: i.IsAgree
                }
            })
            const res = await CreateGuardSubApproval({
                AppId: 1253985742,
                GuardId: guardId,
                ApprovalId: apvlId,
                Operator: rtx,
                Remark: subProcessRemark,
                IsAgreeFilter,
                SubApprovalIds: pendingSubProcess.map(i => i.ID)
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }

            // 审批成功的子流程名称
            const successList = (res.SuccessSubApprovalIDs || []).map(i => {
                const processName = pendingSubProcess.find(s => s.ID === i)?.ApprovalContent || ''
                return processName
            })
            // 审批失败的子流程名称
            const failList = (res.FailSubApprovalIDs || []).map(i => {
                const processName = pendingSubProcess.find(s => s.ID === i)?.ApprovalContent || ''
                return processName
            })
            const str = `${successList.length ? `${successList.join('、')} 审批成功。` : ''}${failList.length ? `${failList.join('、')} 审批失败。` : ''}`
            // 保存审批子流程的结果提示
            localStorage.setItem('ApprovalSubProcessResult', str)
            location.reload()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 修改当前子流程
    function changePendingSubProcess(ID, name, value) {
        const updatedItems = pendingSubProcess.map(item => {
            if (item.ID === ID) {
                return { ...item, [name]: value };
            }
            return item;
        });
        setPendingSubProcess(updatedItems)
    }

    useEffect(() => {
        if (savedSubProcess.length && initSubProcess.length) {
            const pendingList = []
            savedSubProcess
                .filter(i => i.HasOpsPermission && i.Status === 0)
                .map(i => {
                    const item = initSubProcess.filter(j => i.SubApprovalDetailsID === j.ID)[0] || {}
                    const options = item.ApprovalContentOptions ? JSON.parse(item.ApprovalContentOptions) : []
                    const { IsAgree, ID } = i
                    pendingList.push({
                        ApprovalContent: item.ApprovalContent,
                        ApprovalContentOptions: options,
                        IsAgree,
                        ID
                    })
                })
            setPendingSubProcess(pendingList)
        }
    }, [savedSubProcess, initSubProcess])

    useEffect(() => {
        // 是否有某个子流程未审批
        const flag = pendingSubProcess.some(i => i.IsAgree === 0)
        setFinishSubProcess(!flag)
    }, [pendingSubProcess])

    return (
        <Body>
            <Content>
                <Content.Header
                    showBackButton
                    onBackButtonClick={() => { history.push('/advisor/approval') }}
                    title={pageTile}>
                </Content.Header>
                <Content.Body>
                    <Card key={"apvl_base_info"} style={{ margin: 10 }}>
                        <Card.Body>
                            <Row gap={30}>
                                <Col>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">任务类别</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{ApvlTypeDict.get(apvlType)}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航ID</Text>
                                            </Col>
                                            <Col span={18}>
                                                <a href={"/advisor/guard/summary/" + guardId} target="_blank">{guardId} </a>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航名称</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{guardName}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航类型</Text>
                                            </Col>
                                            <Col span={18}>
                                                {StandardDict.get(standard)}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航项目</Text>
                                            </Col>
                                            <Col span={18}>
                                                {projectDict.get(project)}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">封网需求</Text>
                                            </Col>
                                            <Col span={18}>
                                                {currentGuardData.ClosedNetworkDemand || noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                    {/* <section>
                                        <Row verticalAlign={'middle'}>
                                            <Col span={6}>
                                                <Text theme="label" verticalAlign="middle">每日巡检</Text>
                                            </Col>
                                            <Col span={18}>
                                                {currentGuardData.CronType === 0 ? '未开启' : '开启'}
                                            </Col>
                                        </Row>
                                    </section> */}
                                </Col>
                                <Col>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">任务状态</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text><a>{ApvlStatusDict.get(apvlStatus)}</a></Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航时间</Text>
                                            </Col>
                                            <Col span={18}>
                                                {guardTime}
                                            </Col>
                                        </Row>
                                    </section>
									<section>
										<Row verticalAlign={"middle"}>
											<Col span={6}>
												<Text theme="label" verticalAlign="middle">护航重点时间</Text>
											</Col>
											<Col span={18}>
												{keyTime ? keyTime : noSet}
											</Col>
										</Row>
									</section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">客户 APPID | 名称</Text>
                                            </Col>
                                            <Col span={18}>
                                                {(customerInfo || []).map((val, i) => {
                                                    return <div key={i}><Text>{val.AppId + " | " + val.CustomerName}</Text></div>
                                                }
                                                )}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6}>
                                                <Text theme="label" verticalAlign="middle">整体放量预估</Text>
                                            </Col>
                                            <Col span={18}>
                                                {expectedEnlarge}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6}>
                                                <Text theme="label" verticalAlign="middle">护航背景及需求</Text>
                                            </Col>
                                            <Col span={18}>
                                                {currentGuardData.StatementOfNeeds || noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                </Col>
                            </Row>
                        </Card.Body>
                    </Card>

                    <Card key={"apvl_detail"} style={{ margin: 10 }}>
                        <Card.Body>
                            {
                                // 如果标签页内容不为空，且实例放量策略通过异步接口拉取完毕，则展示内容。
                                (tabs.length > 0 && instancePolicyGot) &&
                                <Tabs tabs={tabs} placement={"top"} activeId={currentAppId} onActive={(v) => { setCurrentAppId(v.id) }} destroyInactiveTabPanel={false}>
                                    {
                                        appids.map(appid => {
                                            let tgtResults = (currentApprovalResult.ScanResult || []).filter(ins => { if (ins.AppId === appid) { return ins } }) || []
                                            let prdComment = ""
                                            let volumeResult = new Array<VolumeResult>()
                                            let scanResult = new Array<ScanResult>()
                                            let emergensyResult = new Array<EmergencyPlanResult>()
                                            let productTemplates = new Array<any>()
                                            let instanceResult = []
                                            tgtResults.map((r: any) => {
                                                volumeResult = volumeResult.concat(r.VolumeScanResult)
                                                scanResult = scanResult.concat(r.ScanResult)
                                                emergensyResult = emergensyResult.concat(r.EmergencyPlan)
                                                instanceResult = instanceResult.concat(r.Instance)
                                                r.ProductTemplates.forEach((item) => {
                                                    if (item.AppId == appid) {
                                                        productTemplates = productTemplates.concat(item.Policy);
                                                    }
                                                })
                                            })
                                            tgtResults.map(
                                                item => {
                                                    prdComment = prdComment.concat(item.Comment + " ")
                                                }
                                            )
                                            const instanceProductDesc = productDesc.filter(ins => { if (ins.AppId === appid && ins.Product === apvlProduct) { return ins } })

                                            return (
                                                <TabPanel key={appid} id={appid.toString()}>
                                                    <div>
                                                        {instanceProductDesc.length && <ApprovalDetailInstance
                                                            key={appid + "-guard-instance"}
                                                            appid={appid}
                                                            instancePolicyDict={instancePolicyDict}
                                                            apvlProduct={apvlProduct}
                                                            prdComment={prdComment}
                                                            productTemplates={productTemplates}
                                                            guardId={guardId}
                                                            productDesc={instanceProductDesc}
                                                            currentGuard={currentGuardData}
                                                        />}
                                                        <hr /><Row><Col></Col></Row>
                                                        <ApprovalDetailVolume
                                                            key={appid + "-result-volume"}
                                                            product={apvlProduct}
                                                            guardId={guardId}
                                                            appid={appid}
                                                            apvlId={apvlId}
                                                            apvlName={apvlName}
                                                            result={volumeResult}
                                                            opsPermission={hasOpsPermission && !apvlStatus}
                                                            rtx={rtx}
                                                        />
                                                        <hr /><Row><Col></Col></Row>
                                                        <ApprovalDetailScan
                                                            key={appid + "-result-scan"}
                                                            product={apvlProduct}
                                                            guardId={guardId}
                                                            appid={appid}
                                                            apvlId={apvlId}
                                                            apvlName={apvlName}
                                                            result={scanResult}
                                                            opsPermission={hasOpsPermission && !apvlStatus}
                                                            rtx={rtx}
                                                        />
                                                        <hr /><Row><Col></Col></Row>
                                                        <ApprovalDetailEmergency
                                                            key={appid + "-emergency-plan"}
                                                            product={apvlProduct}
                                                            appid={appid}
                                                            guardId={guardId}
                                                            apvlId={apvlId}
                                                            apvlName={apvlName}
                                                            result={emergensyResult}
                                                            opsPermission={hasOpsPermission && !apvlStatus}
                                                            rtx={rtx}
                                                        />
                                                        {/* <hr /><Row><Col></Col></Row> */}
                                                        {/* <hr /><Row><Col></Col></Row> */}
                                                    </div>
                                                </TabPanel>
                                            )
                                        })
                                    }
                                </Tabs>
                            }
                            {
                                (tabs.length == 0) &&
                                <Text style={{ marginLeft: 5 }}>
                                    该产品未勾选实例，或对应账号未授权，您可以选择提交，或联系管理员确认。
                                </Text>
                            }
                            {
                                (tabs.length > 0 && instancePolicyGot) &&
                                <section className='approvalWrap'>
                                    {pendingSubProcess.length <= 0 && <Row verticalAlign="middle" style={{ marginTop: 5, marginBottom: 5 }}>
                                        <Col span={3} >
                                            <Text theme="label">转他人审核</Text>
                                            <Switch
                                                style={{ marginLeft: 5 }}
                                                disabled={!hasOpsPermission || apvlStatus == 1 || !currentApprovalResult.SubApprovalFinished}
                                                onChange={(value) => { setMoveSelected(value) }}
                                                value={moveSelected}
                                            ></Switch>
                                        </Col>
                                        <Col span={21}>
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <RTXPicker
                                                    key={(targetTransfers || []).join(';')}
                                                    placeholder='请选择流转人员，如无请联系平台管理员'
                                                    style={{ "width": "60%" }}
                                                    valueType="array"
                                                    initRtx={targetTransfers}
                                                    disabled={!hasOpsPermission || !moveSelected}
                                                    onChange={(tags: any) => {
                                                        setTargetTransfers(tags ? tags : [])
                                                        if (tags.length === 0) {
                                                            setVisibleTargetTransferEdit(false)
                                                        } else {
                                                            setVisibleTargetTransferEdit(true)
                                                        }
                                                    }}
                                                />
                                                <Bubble
                                                    placement="right"
                                                    trigger="hover"
                                                    placementOffset="10 + 20%"
                                                    style={{ maxWidth: 600, maxHeight: 700, overflow: 'auto', overflowX: 'hidden', boxShadow: '0 0 16px 0 rgba(54, 58, 80, 0.16)' }}
                                                    content={
                                                        <Timeline mode="vertical">{tranferNodes}</Timeline>
                                                    }
                                                >
                                                    <Text style={{ marginLeft: 3, color: "blue" }}>{"当前流转环节"}</Text>
                                                </Bubble>
                                            </div>
                                        </Col>
                                    </Row>}
                                    {(moveSelected && subProcessList.length > 0) && <Row verticalAlign="middle" style={{ marginTop: 5, marginBottom: 5 }}>
                                        <Col span={3} >
                                            <Text theme="label">流转选项</Text>
                                        </Col>
                                        <Col span={21}>
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <Select style={{ width: '60%' }}
                                                    value={selectSubprocess}
                                                    placeholder='请选择子流程'
                                                    onChange={(value: any) => { setSelectSubprocess(value) }}
                                                    clearable filterable multiple options={subProcessList} />
                                            </div>
                                        </Col>
                                    </Row>}
                                    {moveSelected && <Row verticalAlign="middle" style={{ marginTop: 5, marginBottom: 5 }}>
                                        <Col span={3} >
                                            <Text theme="label">流转备注</Text>
                                        </Col>
                                        <Col span={21}>
                                            <div style={{ display: 'flex', alignItems: 'center' }}>
                                                <Input
                                                    style={{ width: '60%' }}
                                                    placeholder="如资源扩容多少、成本满足条件、人力支持事宜等"
                                                    value={toOtherRemark}
                                                    onChange={(value) => {
                                                        setToOtherRemark(value);
                                                    }}
                                                />
                                            </div>
                                        </Col>
                                    </Row>}
                                    {/* 有待审批的子流程才会显示 */}
                                    {pendingSubProcess.length > 0 &&
                                        <>
                                            <Row style={{ marginTop: 5, marginBottom: 5 }}>
                                                <Col span={3} >
                                                    <Text theme="label">审批内容</Text>
                                                </Col>
                                                <Col span={21}>
                                                    {
                                                        pendingSubProcess.map((i, index) =>
                                                            <div className='subProcessWrap' key={index}>
                                                                <div>
                                                                    <span style={{ marginRight: 20 }}>{i.ApprovalContent}</span>
                                                                    <Radio.Group
                                                                        key={index}
                                                                        value={i.IsAgree}
                                                                        onChange={(value) => { changePendingSubProcess(i.ID, 'IsAgree', value) }}
                                                                    >
                                                                        {i.ApprovalContentOptions.map((i, index1) => <Radio key={index + '-' + index1} name={i.Value}>{i.Key}</Radio>
                                                                        )}
                                                                    </Radio.Group>
                                                                </div>
                                                            </div>
                                                        )
                                                    }
                                                </Col>
                                            </Row>
                                            <Row verticalAlign="middle" style={{ marginTop: 5, marginBottom: 5 }}>
                                                <Col span={3} >
                                                    <Text theme="label">流转备注</Text>
                                                </Col>
                                                <Col span={21}>
                                                    <div style={{ display: 'flex', alignItems: 'center' }}>
                                                        <Input
                                                            style={{ width: '60%' }}
                                                            placeholder="如资源扩容多少、成本满足条件、人力支持事宜等"
                                                            value={subProcessRemark}
                                                            onChange={(value) => {
                                                                setSubProcessRemark(value);
                                                            }}
                                                        />
                                                        <Bubble
                                                            placement="right"
                                                            trigger="hover"
                                                            placementOffset="10 + 20%"
                                                            style={{ maxWidth: 600, maxHeight: 700, overflow: 'auto', overflowX: 'hidden', boxShadow: '0 0 16px 0 rgba(54, 58, 80, 0.16)' }}
                                                            content={
                                                                <Timeline mode="vertical">{tranferNodes}</Timeline>
                                                            }
                                                        >
                                                            <Text style={{ marginLeft: 3, color: "blue" }}>{"当前流转环节"}</Text>
                                                        </Bubble>
                                                    </div>
                                                </Col>
                                            </Row>
                                        </>
                                    }

                                </section>
                            }
                        </Card.Body>
                    </Card>
                    {pendingSubProcess.length <= 0 && <div style={{ margin: 10, textAlign: "center" }}>
                        <Button type={"weak"} onClick={() => { history.push('/advisor/approval') }} style={{ marginRight: 15 }} >
                            返回
                        </Button>
                        <PopConfirm
                            disabled={!visibleTargetTransferEdit}
                            arrowPointAtCenter={true}
                            title={"确认要流转吗？"}
                            message={selectSubprocess.length ? "您选择了子流程审批，该类审批结束后，流程将转回您确认结果。" : '巡检结果审批将流转给他人审批，是否继续？'}
                            footer={close => (
                                <>
                                    <Button type={"primary"} onClick={() => { transferGuardApproval(); close(); }}>
                                        确定
                                    </Button>
                                    <Button onClick={() => { close() }} type="text">{"取消"}</Button>
                                </>
                            )}
                            placement={"top"}>
                            {/* 被TAM审批后的提示 */}
                            <Bubble content={currentApprovalData?.IsAgree === 0 && currentApprovalData?.Status === 1 ? '流程已被TAM审批完成' : null }>
                                <Button type={"primary"} disabled={!visibleTargetTransferEdit} style={{ marginRight: 15 }} >
                                    流转
                                </Button>
                            </Bubble>
                        </PopConfirm>

                        {hasOpsPermission && !apvlStatus &&
                            <PopConfirm
                                disabled={moveSelected || !currentApprovalResult.SubApprovalFinished}
                                arrowPointAtCenter={true}
                                title={"确认要驳回吗？"}
                                message={"提交后，该产品将不在护航过程体现，请确认已与护航负责人沟通清楚。"}
                                footer={close => (
                                    <>
                                        <Button type={"primary"} onClick={() => { modifyGuardScanResultStatus(guardId, mainAppId, apvlProduct, -1); history.push('/advisor/approval'); close(); }}>
                                            确定
                                        </Button>
                                        <Button onClick={() => { close() }} type="text">{"取消"}</Button>
                                    </>
                                )}
                                placement={"top"}>
                                <Button type={"primary"} style={{ marginRight: 15 }} disabled={moveSelected || !currentApprovalResult.SubApprovalFinished}>
                                    驳回
                                </Button>
                            </PopConfirm>
                        }
                        {hasOpsPermission && !apvlStatus &&
                            <PopConfirm
                                disabled={moveSelected || !currentApprovalResult.SubApprovalFinished}
                                arrowPointAtCenter={true}
                                title={"确认要提交审核结果吗？"}
                                message={"提交后，审核内容不再支持修改!"}
                                footer={close => (
                                    <>
                                        <Button type={"primary"} onClick={() => { modifyGuardScanResultStatus(guardId, mainAppId, apvlProduct, 1); history.push('/advisor/approval'); close(); }}>
                                            确定
                                        </Button>
                                        <Button onClick={() => { close() }} type="text">{"取消"}</Button>
                                    </>
                                )}
                                placement={"top"}>
                                <Button type={"primary"} style={{ marginRight: 15 }} disabled={moveSelected || !currentApprovalResult.SubApprovalFinished}>
                                    提交
                                </Button>
                            </PopConfirm>
                        }
                    </div>}
                    {pendingSubProcess.length > 0 && <div style={{ margin: 10, textAlign: "center" }}>
                        <Button type={"primary"} disabled={!finishSubProcess} onClick={() => { approvalSubProcess() }} >
                            确定
                        </Button>
                    </div>}
                </Content.Body>
            </Content>
        </Body >
    );
}

