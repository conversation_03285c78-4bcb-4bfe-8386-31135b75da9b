import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Card, message as tips, Transfer, Table, SearchBox, Select, Form, Bubble, SelectMultiple, Input, message, StatusTip, Icon, Collapse, Button, PopConfirm, Modal, Justify } from "@tencent/tea-component";
const { selectable, removeable, scrollable, pageable, autotip } = Table.addons;
import { Text } from "@tencent/tea-component";
import { Row, Col } from "@tencent/tea-component";
import { getProductsGroups } from '@src/api/advisor/estimate';
import _ from 'lodash'
import { policyItem, InstanceItem, ApprovalResultParams, VolumeResult, ScanResultInstance, ScanResult, EvaluateWayDict, RiskLevelTypeDict, DeleteGuardScanResultParams } from '@src/types/advisor/guard';
import { CreateGuardScanResult, DeleteGuardScanResult, DescribeGuardInstanceInfo, DescribeGuardScanResult } from '@src/api/advisor/guard';
import ApprovalDetailInstanceRes from './ApprovalDetailInstanceRes';
const { TextArea } = Input;

interface Props {
    product: string,
    guardId: number,
    appid: number,
    apvlId: number,
    apvlName: string,
    result: Array<VolumeResult>,
    opsPermission: boolean,
    rtx: string,
}


function ApprovalDetailVolume({ product, guardId, appid, apvlId, apvlName, result, opsPermission, rtx }: Props, ref) {
    //实例记录
    const [columnData, setColumnData] = useState<any>()                   //表头字段
    const [activeIds, setActiveIds] = useState<Array<string>>([])         //Collapse展开内容
    const [productDict, setProductDict] = useState({})                    //产品中文名称
    const [strategyId, setStrategyId] = useState(-1)                      //策略ID
    const [itemId, setItemId] = useState(-1)                              //评估项ID（自定义，可以新建或从预定义策略修改而来）
    const [strategyName, setStrategyName] = useState("")                  //策略名称
    const [visibleStrategyName, setVisibleStrategyName] = useState(false) //策略名称-报错提示
    const [riskLevel, setRiskLevel] = useState(2)                         //风险等级
    const [strategyDesc, setStrategyDesc] = useState("")                  //策略描述
    const [visibleStrategyDesc, setVisibleStrategyDesc] = useState(false) //策略描述-报错提示
    const [affected, setAffected] = useState("")                          //影响实例
    const [repair, setRepair] = useState("")                              //修复建议
    const [visibleRepair, setVisibleRepair] = useState(false)             //修复建议-报错提示
    const [evaluateMethod, setEvaluateMethod] = useState(0)               //评估方式。0:人工，1:自动
    const [taskType, setTaskType] = useState("guardVolumeTaskType")       //任务类型。guardScanTaskType 或 guardVolumeTaskType
    const [env, setEnv] = useState("private")                             //是否对外
    const [comment, setComment] = useState("")                            //备注

    // 是否可以对外选项
    const envOptions = [
        { value: "public", text: "外部", tooltip: "对外部客户展示" },
        { value: "private", text: "内部", tooltip: "仅对内部展示" },
    ];
    // 风险等级选项
    const riskLevelOptions = [
        { value: "2", text: "中风险", tooltip: "中风险" },
        { value: "3", text: "高风险", tooltip: "高风险" },
    ];
    // 新建或编辑隐患结果，模态框状态
    const [showUpdateModal, setShowUpdateModal] = useState(false)
    // 应用可修改状态
    const [updateEnable, setUpdateEnable] = useState(true)
    // 隐患结果列表，用于内容变更后刷新展示
    const [currentRecords, setCurrentRecords] = useState<Array<VolumeResult>>(result)
    // 隐患结果按风险等级计数
    const [riskLevelCount, setRiskLevelCount] = useState(new Map())
    // 隐患结果条数描述
    const [tabSupplyStr, setTabSupplyStr] = useState("")
    // 隐患结果列表loding
    const [recordListLoading, setRecordListLoading] = useState(false)
    // 点击策略影响实例
    const [resultInstances, setResultInstances] = useState<ScanResultInstance>({} as ScanResultInstance)
    // 点击策略影响实例弹窗
    const [visibleResultInstances, setVisibleResultInstances] = useState(false);

    useImperativeHandle(ref, () => ({
        Check: () => { },
    }))

    //获取云产品清单 
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: appid,
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let tmpProductsOptions = []
                for (var i in res.ProductDict) {
                    tmpProductsOptions.push({ value: i, text: res.ProductDict[i] })
                }
                setProductDict(res.ProductDict)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //查询容量结果信息
    const getVolumeResult = async () => {
        try {
            const res = await DescribeGuardScanResult({
                AppId: 1253985742,      //基础参数AppId
                AppIds: [appid],        //为空表示拉取全部
                GuardId: guardId,
                Product: product,
                Operator: rtx,
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                let volumeResult = new Array<VolumeResult>()
                let tgtResults = (res.ScanResult || []).filter(ins => { if (ins.AppId === appid) { return ins } }) || []
                tgtResults.map((r) => {
                    volumeResult = volumeResult.concat(r.VolumeScanResult)
                })
                setCurrentRecords(volumeResult)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //查询容量风险数量
    const getRiskLevelCount = async () => {
        let tmp = new Map()
        tmp.set(2, 0)
        tmp.set(3, 0)
        currentRecords.map(item => {
            if (item.RiskLevel === 2) {
                let l2 = tmp.get(2)
                l2 = l2 + 1
                tmp.set(2, l2)
            }
            if (item.RiskLevel === 3) {
                let l3 = tmp.get(3)
                l3 = l3 + 1
                tmp.set(3, l3)
            }
        })
        setRiskLevelCount(tmp)
    }
    useEffect(() => {
        getRiskLevelCount()
    }, [currentRecords])

    //点击隐患结果实例
    const getResultInstancesShow = async (sId, insIds, level) => {
        if (sId > 0) {
            //自动
            getVolumeResultInstances(sId, level)
        } else {
            //人工
            setResultInstances(insIds || "")
        }
        //实例记录拉取回来后，才跳出弹窗使可见
        setVisibleResultInstances(true)
    }

    //查询容量结果实例
    const getVolumeResultInstances = async (sId, level) => {
        try {
            const res = await DescribeGuardInstanceInfo({
                AppId: appid,
                GuardId: guardId,
                StrategyId: sId,
                Offset: 0,
                Limit: 1000,
				RiskLevel: level
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setResultInstances(res)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 获取隐患记录，变更加载状态
    const getVolumeRecords = async () => {
        setRecordListLoading(true)
        try {
            //查询新的容量信息
            getVolumeResult()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        } finally {
            setRecordListLoading(false)
        }
    }

    //打开创建弹窗
    const initCreateRecord = async () => {
        //初始化参数
        setStrategyId(0)
        setItemId(0)
        setStrategyName("")
        setRiskLevel(2)
        setStrategyDesc("")
        setAffected("")
        setRepair("")
        setEvaluateMethod(0)
        setEnv("private")
        setComment("")
        //打开弹窗
        setShowUpdateModal(true)
    }

    //创建记录
    const createRecord = async (strategyId) => {
        let ItemParams = {
            ResultId: strategyId,
            AppId: appid,
            GuardId: guardId,
            Product: product,
            StrategyName: strategyName,
            RiskLevel: riskLevel,
            StrategyDesc: strategyDesc,
            InstanceIds: affected,
            Repair: repair,
            TaskType: taskType,
            EvaluateMethod: evaluateMethod,
            Env: env,
            Comment: comment,
        }
        //变更记录后，刷新展示
        updateVolumeItem(ItemParams)
    }

    //打开编辑弹窗
    const initUpdateRecord = async (item) => {
        //初始化参数
        setStrategyId(item.StrategyId)
        setItemId(item.ResultId)
        setStrategyName(item.StrategyName)
        setRiskLevel(item.RiskLevel)
        setStrategyDesc(item.StrategyDesc)
        setAffected(item.InstanceIds)
        setRepair(item.Repair)
        setEvaluateMethod(item.EvaluateMethod)
        setEnv(item.Env)
        setComment(item.Comment)

        setVisibleStrategyName(false)
        setVisibleStrategyDesc(false)
        setVisibleRepair(false)

        //打开弹窗
        setShowUpdateModal(true)
    }

    //编辑记录。暂与创建记录一致。
    const updateRecord = async (strategyId) => {
        let ItemParams = {
            ResultId: strategyId,
            AppId: appid,
            GuardId: guardId,
            Product: product,
            StrategyName: strategyName,
            RiskLevel: riskLevel,
            StrategyDesc: strategyDesc,
            InstanceIds: affected,
            Repair: repair,
            TaskType: taskType,
            EvaluateMethod: evaluateMethod,
            Env: env,
            Comment: comment,
        }
        //变更记录后，刷新展示
        updateVolumeItem(ItemParams)
    }

    // 新建或修改隐患信息
    const updateVolumeItem = async (ItemParams?) => {
        setUpdateEnable(false)
        if (ItemParams.StrategyName.trim() === '') {
            //评估项名称必填
            setVisibleStrategyName(true)
            return
        }
        if (ItemParams.StrategyDesc.trim() === '') {
            //评估项描述必填
            setVisibleStrategyDesc(true)
            return
        }
        if (ItemParams.Repair.trim() === '') {
            //优化建议必填
            setVisibleRepair(true)
            return
        }
        try {
            const updateInfos = await CreateGuardScanResult(ItemParams)
            //关闭弹窗
            setShowUpdateModal(false)
            if (updateInfos.Error) {
                let msg = updateInfos.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "保存成功" });
            //刷新记录
            getVolumeRecords()
            if (riskLevelCount.get(2) == 0 && riskLevelCount.get(3) == 0) {
                location.reload()
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 删除隐患信息
    const delRecord = async (resultId, strategyId) => {
        try {
            const params: DeleteGuardScanResultParams = {
                AppId: appid,
                GuardId: guardId,
                Product: product,
                Ids: [{
                    StrategyId: strategyId,
                    ResultId: resultId,
                }],
            }
            const res = await DeleteGuardScanResult(params)
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "删除成功" })
            //刷新记录
            getVolumeRecords()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //处理影响实例点击按钮动作
    const handleAffectedInstanceClick = (ins) => {

        setStrategyId(ins.StrategyId);
        setItemId(ins.ResultId);
        setAffected(ins.InstanceIds);

        getResultInstancesShow(ins.StrategyId, ins.InstanceIds, ins.RiskLevel);
    }

    //获取表格每行的展示表头及渲染数据，如果没有指定实例扩容策略的字段不展示
    function getColumnData() {
        let ret = []
        let newResult: Array<VolumeResult> = _.cloneDeep(result)
        // 任意取一条实例记录，剥离抽取表头字段
        if (newResult.length !== 0) {
            ret.push(
                {
                    key: "StrategyId",
                    header: "ID",
                    width: "5%",
                    render: ins => (
                        <>
                            <p>{ins.StrategyId + ins.ResultId}</p>
                        </>
                    ),
                })
            ret.push(
                {
                    key: "StrategyName",
                    header: "评估项名称",
                    render: ins => (
                        <>
                            <p>{ins.StrategyName}</p>
                        </>
                    ),
                })
            ret.push(
                {
                    key: "RiskLevel",
                    header: () => (
                        <>
                            风险等级
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {RiskLevelTypeDict.get(ins.RiskLevel)} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "StrategyDesc",
                    header: () => (
                        <>
                            评估项描述
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.StrategyDesc} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "ResultInstances",
                    header: () => (
                        <>
                            影响实例
                        </>
                    ),
                    render: ins => (
                        <>
                            <Button type={"link"} onClick={() => { handleAffectedInstanceClick(ins) }}>
                                点击
                            </Button>
                            {/* <Modal visible={visibleResultInstances} disableCloseIcon onClose={closeResultInstances}>
                                <Modal.Body>
                                    <Modal.Message
                                        icon="success"
                                        message="提示消息"
                                        description="详细描述"
                                    />
                                </Modal.Body>
                                <Modal.Footer>
                                    <Button type="primary" onClick={closeResultInstances}>
                                        确定
                                    </Button>
                                    <Button type="weak" onClick={closeResultInstances}>
                                        取消
                                    </Button>
                                </Modal.Footer>
                            </Modal> */}
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "Repair",
                    header: () => (
                        <>
                            优化建议
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.Repair} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "EvaluateMethod",
                    header: () => (
                        <>
                            评估方式
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {EvaluateWayDict.get(ins.EvaluateMethod) || ""} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "Env",
                    width: "10%",
                    header: () => (
                        <>
                            是否对外
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.Env} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "Comment",
                    header: () => (
                        <>
                            备注
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.Comment} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: 'Ops',
                    header: "操作",
                    width: "10%",
                    render: item => {
                        if (!opsPermission) {
                            return <>{"无"}</>
                        }
                        return <>
                            <Button onClick={() => { initUpdateRecord(item) }} type="link" disabled={item.EvaluateMethod === 1 ? true : false}>
                                编辑
                            </Button>
                            <PopConfirm
                                arrowPointAtCenter={true}
                                title={"确认要删除容量巡检结果吗？"}
                                message={"删除后，该容量巡检后续不再保存和展示!"}
                                footer={close => (
                                    <>
                                        <Button type="link" onClick={() => { delRecord(item.ResultId, item.StrategyId); close() }}>
                                            删除
                                        </Button>
                                        <Button onClick={() => { close() }} type="text">{"取消"}</Button>
                                    </>
                                )}
                                placement={"top"}>
                                <Button type="link">
                                    删除
                                </Button>
                            </PopConfirm>
                        </>
                    }
                }
            )
        }
        return ret
    }

    //init
    useEffect(() => {
        getProductsGroupsInfo();
    }, [])

    useEffect(() => {
        let ret = getColumnData()
        setColumnData(ret)
    }, [result])


    return (
        <div style={{ marginTop: 10 }}>
            <Collapse activeIds={activeIds} onActive={v => { setActiveIds(v) }} destroyInactivePanel={false}>
                {
                    <div key={apvlId + "-scan-result"}>
                        <Collapse.Panel style={{ marginTop: 10 }} key={apvlId + apvlName} id={apvlId + apvlName}
                            // title={(productDict[product] || "") + "容量评估结果" + "（" + (currentRecords.length || 0) + "条" + (currentRecords.length > 0 ? ("。中风险 " + riskLevelCount.get(2) + " 条，高风险 " + riskLevelCount.get(3) + " 条") : "") + "）"}>
                            title={
                                (currentRecords.length > 0) ?
                                    <div>{productDict[product] || ""}容量评估结果（{currentRecords.length || 0} 条&nbsp;&nbsp;<Text style={{ color: "Orange" }} >中风险 {riskLevelCount.get(2)} 条</Text>，<Text style={{ color: "Red" }}>高风险 {riskLevelCount.get(3)} 条</Text>）</div> :
                                    <div>{productDict[product] || ""}容量评估结果（{currentRecords.length || 0} 条）</div>
                            } >
                            <Table.ActionPanel style={{ marginTop: 20 }}>
                                {opsPermission &&
                                    <Justify
                                        left={<>
                                            <Button type={"primary"} onClick={() => { initCreateRecord() }}>{'新建'}</Button>
                                        </>}
                                        right={
                                            <>
                                                {/* {todo <SearchBox /} */}
                                            </>
                                        }
                                    />}
                            </Table.ActionPanel>
                            {
                                <section>
                                    <Row style={{ marginTop: 10 }}>
                                        <Col>
                                            <Card>
                                                <Table
                                                    verticalTop
                                                    records={currentRecords}
                                                    recordKey="StrategyId"
                                                    columns={columnData || []}
                                                    addons={[pageable()]}
                                                    topTip={(recordListLoading || currentRecords.length == 0) && <StatusTip status={recordListLoading ? "loading" : 'empty'}></StatusTip>}
                                                />
                                            </Card>
                                        </Col>
                                    </Row>
                                </section>
                            }
                            {/* 新建/编辑弹窗 */}
                            <Modal visible={showUpdateModal} size={"l"} caption={(strategyId > 0 || itemId > 0) ? '编辑容量评估记录' : '新建容量评估记录'} onClose={() => { setShowUpdateModal(false) }}>
                                <Modal.Body>
                                    <div>
                                        <Form style={{ marginTop: 20 }} layout={"default"}>
                                            <Form.Item label={"评估项名称"}>
                                                <Bubble error visible={visibleStrategyName} content={'请输入评估项名称'}>
                                                    <Input size={'l'} value={strategyName} placeholder="请输入评估项名称，注意不要和已有的评估项名称相同" onChange={(value) => {
                                                        setStrategyName(value)
                                                        if (value.trim() === '') {
                                                            setVisibleStrategyName(true)
                                                        } else {
                                                            setVisibleStrategyName(false)
                                                        }
                                                    }}></Input>
                                                </Bubble>
                                            </Form.Item>

                                            <Form.Item label={"评估项描述"}>
                                                <Bubble error visible={visibleStrategyDesc} content={'请输入评估项描述'}>
                                                    <TextArea size={'l'} value={strategyDesc} onChange={(value) => {
                                                        setStrategyDesc(value)
                                                        if (value.trim() === '') {
                                                            setVisibleStrategyDesc(true)
                                                        } else {
                                                            setVisibleStrategyDesc(false)
                                                        }
                                                    }} />
                                                </Bubble>
                                            </Form.Item>
                                            <Form.Item label={"影响实例"}>
                                                <TextArea size={'l'} value={affected} onChange={(value) => {
                                                    setAffected(value)
                                                }} />
                                            </Form.Item>
                                            <Form.Item label={"优化建议"}>
                                                <Bubble error visible={visibleRepair} content={'请输入优化建议'}>
                                                    <TextArea size={'l'} value={repair} onChange={(value) => {
                                                        setRepair(value)
                                                        if (value.trim() === '') {
                                                            setVisibleRepair(true)
                                                        } else {
                                                            setVisibleRepair(false)
                                                        }
                                                    }} />
                                                </Bubble>
                                            </Form.Item>
                                            <Form.Item label={"是否可以对外"}>
                                                <Select type="simulate" value={env} onChange={(value) => { setEnv(value) }} options={envOptions} ></Select>
                                            </Form.Item>
                                            <Form.Item label={"风险等级"}>
                                                <Select type="simulate" value={String(riskLevel)} onChange={(value) => { setRiskLevel(Number(value)) }} options={riskLevelOptions} ></Select>
                                            </Form.Item>
                                            <Form.Item label={"备注"}>
                                                <TextArea size={'l'} value={comment} onChange={(value) => {
                                                    setComment(value)
                                                }} />
                                            </Form.Item>
                                        </Form>
                                    </div>
                                </Modal.Body>
                                <Modal.Footer>
                                    <div>
                                        <Button
                                            style={{ marginRight: 5 }} type={"primary"} onClick={() => { (strategyId > 0 || itemId > 0) ? updateRecord(strategyId > 0 ? strategyId : itemId) : createRecord(strategyId > 0 ? strategyId : itemId) }}>
                                            {(strategyId > 0 || itemId > 0) ? '确认' : '保存'}
                                        </Button>
                                        <Button
                                            style={{ marginRight: 5 }} type={"weak"} onClick={() => { setShowUpdateModal(false) }}>
                                            {'取消'}
                                        </Button>
                                    </div>
                                </Modal.Footer>
                            </Modal>
                            {/* 影响实例弹窗 */}
                            <Modal visible={visibleResultInstances} size={"xl"} caption={"影响实例列表"} onClose={() => setVisibleResultInstances(false)}>
                                <Modal.Body>
                                    {
                                        // 人工（evaluateMethod:0 或 Id!=0 && StrategyId==0）、自动（evaluateMethod:1 或 Id==0 && StrategyId!=0）
                                        (itemId > 0 && strategyId === 0) ? <div>
                                            <section>
                                                <Row style={{ marginTop: 10 }}>
                                                    <Col>
                                                        {affected ? affected : "无"}
                                                    </Col>
                                                </Row>
                                            </section>
                                        </div> : <div>
                                            <ApprovalDetailInstanceRes
                                                strategyId={strategyId}
                                                result={resultInstances || {} as ScanResultInstance}
                                            />
                                        </div>
                                    }
                                </Modal.Body>
                            </Modal>
                        </Collapse.Panel>
                    </div>

                }
            </Collapse>
        </div>
    )
}

export default forwardRef(ApprovalDetailVolume)
