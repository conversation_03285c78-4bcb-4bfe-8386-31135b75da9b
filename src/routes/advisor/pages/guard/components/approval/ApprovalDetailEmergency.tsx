import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Card, message as tips, Transfer, Table, SearchBox, Select, Form, Bubble, SelectMultiple, Input, message, StatusTip, Icon, Collapse, Justify, Button, Modal, PopConfirm } from "@tencent/tea-component";
const { selectable, removeable, scrollable, pageable, autotip } = Table.addons;
import { Text } from "@tencent/tea-component";
import { Row, Col } from "@tencent/tea-component";
import { getProductsGroups } from '@src/api/advisor/estimate';
import _ from 'lodash'
import { policyItem, InstanceItem, ApprovalResultParams, VolumeResult, EmergencyPlanResult, EnvTypeDict } from '@src/types/advisor/guard';
import { Link } from 'react-router-dom';
import { CreateGuardEmergencyPlanItem, DeleteGuardEmergencyPlanItem, DescribeGuardScanResult } from '@src/api/advisor/guard';
import { app } from '@tencent/tea-app'; // 另有 import { app } from '@tea/app'
const { TextArea } = Input;

interface Props {
    product: string,
    // instancePolicys: Array<policyItem>, //实例容量策略
    // instances: Array<InstanceItem>,     //实例信息
    appid: number,
    guardId: number,
    apvlId: number,
    apvlName: string,
    result: Array<EmergencyPlanResult>,
    opsPermission: boolean,
    rtx: string,
}


function ApprovalDetailEmergency({ product, appid, guardId, apvlId, apvlName, result, opsPermission, rtx }: Props, ref) {
    const [columnData, setColumnData] = useState<any>()
    const [activeIds, setActiveIds] = useState<Array<string>>([]) //Collapse展开内容
    const [productDict, setProductDict] = useState({})            //产品中文名称

    // 应用可修改状态
    const [updateEnable, setUpdateEnable] = useState(true)
    // 应急预案列表
    const [currentRecords, setCurrentRecords] = useState<Array<EmergencyPlanResult>>(result)
    // 应急预案id: planId, itemId
    const [planId, setPlanId] = useState(-1)
    const [itemId, setItemId] = useState(-1)
    // 应急预案列表loding
    const [recordListLoading, setRecordListLoading] = useState(false)
    // 应急预案场景
    const [currentRiskScenario, setCurrentRiskScenario] = useState('')
    // 应急预案内容
    const [currentMeasure, setCurrentMeasure] = useState('')
    // 应急预案场景-报错提示
    const [visibleCurrentRiskScenario, setVisibleCurrentRiskScenario] = useState(false)
    // 应急预案内容-报错提示
    const [visibleCurrentMeasure, setVisibleCurrentMeasure] = useState(false)
    // 是否可以对外
    const [currentEnv, setCurrentEnv] = useState("private")
    // 是否可以对外选项
    const envOptions = [
        { value: "public", text: "外部", tooltip: "对外部客户展示" },
        { value: "private", text: "内部", tooltip: "仅对内部展示" },
    ];
    // 新建或编辑应急预案，模态框状态
    const [showUpdateModal, setShowUpdateModal] = useState(false)

    useImperativeHandle(ref, () => ({
        Check: () => { },
    }))

    //获取云产品清单 
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: appid,
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let tmpProductsOptions = []
                for (var i in res.ProductDict) {
                    tmpProductsOptions.push({ value: i, text: res.ProductDict[i] })
                }
                // setProductsOptions(tmpProductsOptions)
                setProductDict(res.ProductDict)
                // setYunapiFilterPolicy(res.YunapiFilterPolicy || [])
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //查询应急预案信息
    const getEmergencyPlanResult = async () => {
        try {
            const res = await DescribeGuardScanResult({
                AppId: 1253985742,      //基础参数AppId
                AppIds: [appid],        //为空表示拉取全部
                GuardId: guardId,
                Product: product,
                Operator: rtx,
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                let emergencyResult = new Array<EmergencyPlanResult>()
                let tgtResults = (res.ScanResult || []).filter(ins => { if (ins.AppId === appid) { return ins } }) || []
                tgtResults.map((r) => {
                    emergencyResult = emergencyResult.concat(r.EmergencyPlan)
                })
                setCurrentRecords(emergencyResult)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 获取预案记录，变更加载状态
    const getEmergencyPlanRecords = async () => {
        setRecordListLoading(true)
        try {
            //查询新的预案信息
            getEmergencyPlanResult()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        } finally {
            setRecordListLoading(false)
        }
    }

    //打开创建弹窗
    const initCreateRecord = async () => {
        //初始化参数
        setPlanId(0)
        setItemId(0)
        setCurrentRiskScenario("")
        setCurrentMeasure("")
        setVisibleCurrentRiskScenario(false)
        setVisibleCurrentMeasure(false)
        //打开弹窗
        setShowUpdateModal(true)
    }

    //创建应急预案信息
    const createRecord = async (planId, itemId) => {
        let ItemParams = {
            PlanId: planId,
            ItemId: itemId,
            RiskScenario: currentRiskScenario,
            Measure: currentMeasure,
            Product: product,
            Env: currentEnv,
        }
        updateEmergencyPlanItem(ItemParams)
    }

    //打开编辑弹窗
    const initUpdateRecord = async (item) => {
        //初始化参数
        setPlanId(item.PlanId)
        setItemId(item.ItemId)
        setCurrentRiskScenario(item.RiskScenario)
        setCurrentMeasure(item.Measure)
        setCurrentEnv(item.Env)
        setVisibleCurrentRiskScenario(false)
        setVisibleCurrentMeasure(false)
        //打开弹窗
        setShowUpdateModal(true)
    }

    //修改应急预案信息。暂与创建应急预案信息一致。
    const updateRecord = async (planId, itemId) => {
        let ItemParams = {
            PlanId: planId,
            ItemId: itemId,
            RiskScenario: currentRiskScenario,
            Measure: currentMeasure,
            Product: product,
            Env: currentEnv,
        }
        updateEmergencyPlanItem(ItemParams)
    }

    // 新建或修改应急预案信息
    const updateEmergencyPlanItem = async (ItemParams?) => {
        setUpdateEnable(false)
        if (ItemParams.RiskScenario.trim() === '') {
            setVisibleCurrentRiskScenario(true)
            return
        }
        try {
            const updateInfos = await CreateGuardEmergencyPlanItem({
                AppId: appid,
                GuardId: guardId,
                Item: [ItemParams],
            })
            //关闭弹窗
            setShowUpdateModal(false)
            if (updateInfos.Error) {
                let msg = updateInfos.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "保存成功" });
            //刷新记录
            getEmergencyPlanRecords()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 删除应急预案信息
    const delRecord = async (planId, itemId) => {
        try {
            const res = await DeleteGuardEmergencyPlanItem({
                AppId: appid,
                GuardId: guardId,
                Product: product,
                Ids: [{
                    PlanId: planId,
                    ItemId: itemId,
                }],
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "删除成功" })
            //刷新记录
            getEmergencyPlanRecords()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }


    //获取表格每行的展示表头及渲染数据，如果没有指定实例扩容策略的字段不展示
    function getColumnData() {
        let ret = []
        let newResult: Array<EmergencyPlanResult> = _.cloneDeep(result)
        // 任意取一条实例记录，剥离抽取表头字段
        // if (newResult.length !== 0) {
            let one = newResult[0]
            ret.push(
                {
                    key: "PlanId",
                    header: "ID",
                    width: "5%",
                    render: ins => (
                        <>
                            <p>{ins.PlanId}</p>
                        </>
                    ),
                })
            ret.push(
                {
                    key: "RiskScenario",
                    header: "风险场景",
                    render: ins => (
                        <>
                            <p>{ins.RiskScenario}</p>
                        </>
                    ),
                })
            ret.push(
                {
                    key: "Measure",
                    header: () => (
                        <>
                            应急措施或保障方案
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.Measure} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "Env",
                    width: "10%",
                    header: () => (
                        <>
                            是否可以对外
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {EnvTypeDict.get(ins.Env)} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: 'Ops',
                    header: "操作",
                    width: "10%",
                    render: item => {
                        if (!opsPermission) {
                            return <>{"无"}</>
                        }
                        return <>
                            <Button onClick={() => { initUpdateRecord(item) }} type="link">
                                编辑
                            </Button>
                            <PopConfirm
                                arrowPointAtCenter={true}
                                title={"确认要删除该应急预案吗？"}
                                message={"删除后，该应急预案后续不再保存和展示!"}
                                footer={close => (
                                    <>
                                        <Button type="link" onClick={() => { delRecord(item.PlanId, item.ItemId); close() }}>
                                            删除
                                        </Button>
                                        <Button onClick={() => { close() }} type="text">{"取消"}</Button>
                                    </>
                                )}
                                placement={"top"}>
                                <Button type="link" onClick={() => { }} >
                                    删除
                                </Button>
                            </PopConfirm>
                        </>
                    }
                }
            )
        // }
        return ret
    }

    //init
    useEffect(() => {
        getProductsGroupsInfo();
    }, [])


    useEffect(() => {
        let ret = getColumnData()
        setColumnData(ret)
    }, [result])

    return (
        <div style={{ marginTop: 10 }}>
            <Collapse activeIds={activeIds} onActive={v => { setActiveIds(v) }} destroyInactivePanel={false}>
                {
                    <div key={apvlId + "-emergency-result"}>
                        <Collapse.Panel style={{ marginTop: 10 }} key={apvlId + apvlName} id={apvlId + apvlName} title={(productDict[product] || "") + "应急预案（" + (currentRecords.length || 0) + "条）"}>
                            <Table.ActionPanel style={{ marginTop: 20 }}>
                                {opsPermission &&
                                    <Justify
                                        left={<>
                                            <Button type={"primary"} onClick={() => { initCreateRecord() }}>{'新建'}</Button>
                                        </>}
                                        right={
                                            <>
                                                {/* {todo <SearchBox /} */}
                                            </>
                                        }
                                    />}
                            </Table.ActionPanel>
                            {
                                <section>
                                    <Row style={{ marginTop: 10 }}>
                                        <Col>
                                            <Card>
                                                <Table
                                                    verticalTop
                                                    records={currentRecords}
                                                    recordKey="PlanId"
                                                    columns={columnData || []}
                                                    addons={[pageable()]}
													topTip={(recordListLoading || currentRecords.length == 0) && <StatusTip status={recordListLoading ? "loading" : 'empty'}></StatusTip>}
                                                />
                                            </Card>
                                        </Col>
                                    </Row>
                                </section>
                            }
                            {/* 新建/编辑弹窗 */}
                            <Modal visible={showUpdateModal} size={"l"} caption={(planId > 0 || itemId > 0) ? '编辑应急预案' : '新建应急预案'} onClose={() => { setShowUpdateModal(false) }}>
                                <Modal.Body>
                                    <div>
                                        <Form style={{ marginTop: 20 }} layout={"default"}>
                                            <Form.Item label={"风险场景"}>
                                                <Bubble error visible={visibleCurrentRiskScenario} content={'请输入应急预案风险场景'}>
                                                    <Input size={'l'} value={currentRiskScenario} placeholder="请注意不要和已有的应急预案风险场景名称相同" onChange={(value) => {
                                                        setCurrentRiskScenario(value)
                                                        if (value.trim() === '') {
                                                            setVisibleCurrentRiskScenario(true)
                                                        } else {
                                                            setVisibleCurrentRiskScenario(false)
                                                        }
                                                    }}></Input>
                                                </Bubble>
                                            </Form.Item>
                                            <Form.Item label={"应对措施或保障方案"}>
                                                <Bubble error visible={visibleCurrentMeasure} content={'请输入应急预案具体内容'}>
                                                    <TextArea size={'l'} value={currentMeasure} onChange={(value) => {
                                                        setCurrentMeasure(value)
                                                        if (value.trim() === '') {
                                                            setVisibleCurrentMeasure(true)
                                                        } else {
                                                            setVisibleCurrentMeasure(false)
                                                        }
                                                    }} />
                                                </Bubble>
                                            </Form.Item>
                                            <Form.Item label={"是否可以对外"}>
                                                <Select type="simulate" value={currentEnv} onChange={(value) => { setCurrentEnv(value) }} options={envOptions} ></Select>
                                            </Form.Item>
                                        </Form>
                                    </div>
                                </Modal.Body>
                                <Modal.Footer>
                                    <div>
                                        <Button
                                            style={{ marginRight: 5 }} type={"primary"} onClick={() => { (planId > 0 || itemId > 0) ? updateRecord(planId, itemId) : createRecord(planId, itemId) }}>
                                            {(planId > 0 || itemId > 0) ? '确认' : '保存'}
                                        </Button>
                                        <Button
                                            style={{ marginRight: 5 }} type={"weak"} onClick={() => { setShowUpdateModal(false) }}>
                                            {'取消'}
                                        </Button>
                                    </div>
                                </Modal.Footer>
                            </Modal>
                        </Collapse.Panel>
                    </div>
                }
            </Collapse >
        </div >
    )
}

export default forwardRef(ApprovalDetailEmergency)
