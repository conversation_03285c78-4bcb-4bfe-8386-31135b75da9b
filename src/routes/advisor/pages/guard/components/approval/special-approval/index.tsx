import React, { useState, useEffect } from 'react';
import { Card, message as tips, Row, Col, Layout, Button, Text } from '@tencent/tea-component';
import { UpdateSpecialAfterSaleArchApproval, DescribeSpecialAfterSaleArchApproval, DescribeGuardProjects, DescribeGuardSheet } from '@src/api/advisor/guard';
import { ApvlStatusDict, ApvlTypeDict, Filter, GuardParams, StandardDict } from '@src/types/advisor/guard';
import { useHistory } from '@tea/app';
import './index.less';
import { CustomerName } from '../../CustomerName';
const { Body, Content } = Layout;

export function SpecialApproval(match) {
	const history = useHistory();
	const operator = localStorage.getItem('engName');
	// 当前护航ID
	const guardId = Number(match.match.params.guardid);
	// 当前审批ID
	const apvlId = Number(match.match.params.apvlid);
	// 当前护航单数据
	const [currentGuardData, setCurrentGuardData] = useState<GuardParams>({});

	// 当前审批单数据
	const [currentApprovalData, setCurrentApprovalData] = useState<any>({});

	// 页面标题
	const pageTile = `任务ID:${apvlId} | ${currentApprovalData.ApprovalName}`;
	const [projectDict, setProjectDict] = useState(new Map());
	// 客户账号和名称
	const [customerInfo, setCustomerInfo] = useState([]);
	// 护航详细字段
	const guardName = currentGuardData.GuardName;                                            // 护航名称
	const standard = currentGuardData.Standard;                                              // 护航类型
	const startDate = (currentGuardData.StartTime || '');                  // 护航开始时间
	const endDate = (currentGuardData.EndTime || '');                          // 护航结束时间
	const guardTime = (startDate === endDate) ? startDate : (`${startDate} ~ ${endDate}`);
	const project = currentGuardData.Project;                                                // 护航项目
	const expectedEnlargeDays = currentGuardData.ExpectedEnlargeDays;                        // 整体放量预估天数
	const expectedEnlargeTimes = currentGuardData.ExpectedEnlargeTimes;                      // 整体放量预估倍数
	const expectedEnlarge = `对比近 ${expectedEnlargeDays} 天业务放量 ${expectedEnlargeTimes} 倍`;
	const keyTime = (currentGuardData.ImportantGuardTime || []).join(', ');                             // 护航重点时间
	// 审核展示字段
	const apvlType = currentApprovalData.TaskType;    // 审核任务类别
	const apvlStatus = currentApprovalData.Status;    // 审核任务状态
	// 可以操作的条件，有权限并且未处理
	const canOperate = currentApprovalData.HasOpsPermission && currentApprovalData.Status === 0;
	// 未填写字段
	const noSet = <Text style={{ color: 'LightGray' }} verticalAlign="middle">未填写</Text>;

	// 查询护航项目清单
	const getProjects = async () => {
		try {
			const res = await DescribeGuardProjects({
				AppId: 1253985742, // 接口必须传appid  为获取全量产品列表，因此传内部中心账号
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmp = [];
			const m = new Map();
			res.Projects.map((i) => {
				tmp.push({ value: i.Id.toString(), text: i.Name });
				m.set(i.Id, i.Name);
			});
			setProjectDict(m);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询护航单详情
	const getGuardSheet = async () => {
		try {
			const filters: Array<Filter> = [
				{ Name: 'guard_id', Values: [match.match.params.guardid] },
			];
			const res = await DescribeGuardSheet({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset: 0,
				Limit: 10,
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const item = res.Guard[0] || {};
			getGuardApprovalData(item?.MainAppId);
			setCurrentGuardData(item);
			const appids = [item.MainAppId].concat(item.RelatedAppId);
			const customerNames = [item.CustomerName].concat(item.RelatedCustomerNames);
			const tempInfo = [];
			// 获取当前Tab页
			const tmpTabs = [];
			appids.forEach((i, index) => {
				tempInfo.push(`${i} | ${customerNames[index]}`);
				tmpTabs.push({
					id: i.toString(),
					label: <CustomerName appid={i} />,
				});
			});
			setCustomerInfo(tempInfo);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询审核单详情
	const getGuardApprovalData = async (appId) => {
		try {
			const params = {
				GuardID: guardId,
				Operator: localStorage.getItem('engName') || '',
				AppId: appId,
			};
			const res = await DescribeSpecialAfterSaleArchApproval(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setCurrentApprovalData(res.SpecialAfterSaleArchApproval || {});
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};


	// 操作护航单 操作类型；1-确认/2-驳回
	async function operateGuard(IsAgree) {
		try {
			const params = {
				GuardId: guardId,
				ApprovalId: apvlId,
				IsAgree,
				Operator: operator,
			};
			const res = await UpdateSpecialAfterSaleArchApproval(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			tips.success({ content: '审批成功' });
			history.push('/advisor/approval');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// init
	useEffect(() => {
		getProjects();
		getGuardSheet();
	}, []);

	return (
		<Body>
			<Content>
				<Content.Header title={pageTile} showBackButton onBackButtonClick={() => {
					history.push('/advisor/approval');
				}}></Content.Header>
				<Content.Body>
					<Card key={'apvl_base_info'}>
						<Card.Body>
							<Row gap={30}>
								<Col>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">任务类别</Text>
											</Col>
											<Col span={18}>
												<Text>{ApvlTypeDict.get(apvlType)}</Text>
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">护航ID</Text>
											</Col>
											<Col span={18}>
												<a href={`/advisor/guard/summary/${guardId}`} target="_blank">{guardId} </a>
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">护航名称</Text>
											</Col>
											<Col span={18}>
												<Text>{guardName}</Text>
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">护航类型</Text>
											</Col>
											<Col span={18}>
												{StandardDict.get(standard)}
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">护航项目</Text>
											</Col>
											<Col span={18}>
												{projectDict.get(project)}
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">封网需求</Text>
											</Col>
											<Col span={18}>
												{currentGuardData.ClosedNetworkDemand || noSet}
											</Col>
										</Row>
									</section>
								</Col>
								<Col>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">任务状态</Text>
											</Col>
											<Col span={18}>
												<Text><a>{ApvlStatusDict.get(apvlStatus)}</a></Text>
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">护航时间</Text>
											</Col>
											<Col span={18}>
												{guardTime}
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6}>
												<Text theme="label" verticalAlign="middle">护航重点时间</Text>
											</Col>
											<Col span={18}>
												{keyTime ? keyTime : noSet}
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">客户 APPID | 名称</Text>
											</Col>
											<Col span={18}>
												{(customerInfo || []).map((val, i) => <div key={i}>
													<Text>{val}</Text>
												</div>)}
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6}>
												<Text theme="label" verticalAlign="middle">整体放量预估</Text>
											</Col>
											<Col span={18}>
												{expectedEnlarge}
											</Col>
										</Row>
									</section>
									<section>
										<Row verticalAlign={'middle'}>
											<Col span={6}>
												<Text theme="label" verticalAlign="middle">护航背景及需求</Text>
											</Col>
											<Col span={18}>
												{currentGuardData.StatementOfNeeds || noSet}
											</Col>
										</Row>
									</section>
								</Col>
							</Row>
						</Card.Body>
					</Card>
					<Card key={'apvl_detail'} >
						<Card.Body>
							<Row verticalAlign={'middle'}>
								<Col span={3} >
									<Text theme="label" verticalAlign="middle">任务说明</Text>
								</Col>
								<Col span={18}>
									<Text theme="text" className='text'>当前客户护航需求未绘制架构图，{currentApprovalData.AfterSaleReason ? `原因：${currentApprovalData.AfterSaleReason}。` : ''} 护航负责人 {currentApprovalData.Responser} 申请特殊审批，请您处理，谢谢！</Text>
								</Col>
							</Row>
						</Card.Body>
					</Card>
					 <div className='btnWrap'>
						<Button
							type={'primary'}
							disabled={!canOperate}
							onClick={() => {
								operateGuard(true);
							}}>
                            同意
						</Button>
						<Button
							type={'weak'}
							disabled={!canOperate}
							onClick={() => {
								operateGuard(false);
							}}>
                            驳回
						</Button>
					</div>
				</Content.Body>
			</Content>
		</Body >
	);
}

