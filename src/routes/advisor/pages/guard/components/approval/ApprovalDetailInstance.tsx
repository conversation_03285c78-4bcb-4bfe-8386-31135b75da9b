import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Bubble, Col, message, Row, Text } from '@tencent/tea-component';
import { Collapse } from "@tencent/tea-component";
import { getProductsGroups } from '@src/api/advisor/estimate';
import { YunapiFilterPolicyItem, InstanceItem, policyItem } from '@src/types/advisor/guard';
import DescInstanceTable from '../DescInstanceTable';

interface Props {
    appid: number,
    instancePolicyDict: any,
    apvlProduct: string,
    apvlInstances?: Array<InstanceItem>,
    prdComment: string,
    productTemplates: Array<any>,
    guardId?: number,
    productDesc?: any,
    currentGuard?: any
}

// 护航巡检实例
export function ApprovalDetailInstance({ appid, instancePolicyDict, apvlProduct, apvlInstances, prdComment, productTemplates, guardId, productDesc, currentGuard }: Props) {
    //资源信息收集
    const [productDict, setProductDict] = useState({}) //产品中文名称
    const [activeIds, setActiveIds] = useState<Array<string>>([]) //展开的云产品
    // cdn是否需要展示实例信息
    const [cdnInstancePolicy, setCdnInstancePolicy] = useState(false)
    let insTotal = (currentGuard.InstanceTemplateCount && currentGuard.InstanceTemplateCount[appid])
        ? (currentGuard.InstanceTemplateCount[appid][apvlProduct] || 0)
        : 0
    //获取云产品清单 
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: appid,
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let tmpProductsOptions = []
                for (var i in res.ProductDict) {
                    tmpProductsOptions.push({ value: i, text: res.ProductDict[i] })
                }
                setProductDict(res.ProductDict)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    const resourceRef = useRef(null);
    const noSetComment = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写备注</Text>

    //init
    useEffect(() => {
        productTemplates.map(policy=>{
            // cdn 需求类型适配
            if(policy.MetricName==='CDNRequirementType' && policy.Value==='突发护航需求' && !cdnInstancePolicy ){
                setCdnInstancePolicy(true)
            }
        })
        
        getProductsGroupsInfo();
    }, [])
    const [records, setRecords] = useState(productDesc)
    const isShowUnSupport = records?.[0]?.['IsProductSupported'] === false || records?.[0]?.['IsAuthorized'] === false

    return (
        <div style={{ marginTop: 10 }}>
            <Collapse activeIds={activeIds} onActive={v => { setActiveIds(v) }} destroyInactivePanel={false}>
                {
                    <div key={apvlProduct}>
                        <Collapse.Panel style={{ marginTop: 10 }} key={apvlProduct} id={apvlProduct} title={(productDict[apvlProduct] || "") + "资源信息（" + (isShowUnSupport ? (records?.[0]?.['InstanceIds'] ? records.length : 0) : insTotal) + "条）"}>
                            {
                                <>
                                    {/* 场景一：授权账号、接入云顾问的产品 */}
                                    {!isShowUnSupport && <DescInstanceTable
                                        cdnInstancePolicy={cdnInstancePolicy}
                                        product={apvlProduct}
                                        instancePolicys={instancePolicyDict[apvlProduct] || []}
                                        key={apvlProduct}
                                        appid={appid}
                                        ref={resourceRef}
                                        guardId={guardId}
                                        recordChange={records => { setRecords(records) }}
                                    />}
                                    {/* 场景二：未授权账号，或已授权但未接入云顾问的产品 */}
                                    {isShowUnSupport &&
                                        <section>
                                            <Row style={{ marginTop: 10 }}>
                                                <Col style={{ width: '12.5%' }} span={2} >
                                                    <Text theme="label">重点护航实例</Text>
                                                </Col>

                                                <Col>
                                                    {
                                                        records[0]['InstanceIds'] || <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写实例</Text>
                                                    }
                                                </Col>
                                            </Row>
                                        </section>
                                    }
                                    <section>
                                        <Row verticalAlign="middle" style={{ marginTop: 5, marginBottom: 5 }}>
                                            <Col span={12} style={
                                                {
                                                    width: '12.5%'
                                                }
                                            }>
                                                <Text theme="label">备注</Text>
                                            </Col>
                                            <Col span={36} >
                                                <Text>{prdComment.trim() ? prdComment : noSetComment}</Text>
                                            </Col>
                                        </Row>

                                        <Row verticalAlign="middle" style={{ marginTop: 5, marginBottom: 5 }}>
                                            {
                                                productTemplates.map((item, i) => {
                                                    return <Col span={12} key={i}>
                                                        <Row>
                                                            <Col span={6} >
                                                                {
                                                                    item.IsRequired && <Text style={{ color: "red" }} verticalAlign="middle">*</Text>
                                                                }
                                                                <Text theme="label">{item.CNName}</Text>
                                                            </Col>
                                                            <Col span={18} >
                                                                {
                                                                    item.Value !== ''
                                                                        ?
                                                                        <Text>{item.Value}</Text>
                                                                        :
                                                                        <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>
                                                                }
                                                            </Col>
                                                        </Row>
                                                    </Col>
                                                })
                                            }
                                        </Row>
                                    </section>
                                </>
                            }
                        </Collapse.Panel>
                    </div>
                }
            </Collapse>
        </div>
    )
}