import React, { useState, useEffect, forwardRef } from 'react';
import { Icon, Table } from '@tencent/tea-component';
import { Collapse, message, Radio } from "@tencent/tea-component";
import { ExpertApprovalInstanceFrontendField } from '@src/types/advisor/guard';
import _ from 'lodash'
import { getInstances } from '@src/api/advisor/estimate';
import { getProductsGroups } from '@src/api/advisor/estimate';

interface Props {
    product: string,
    instanceFields: Array<ExpertApprovalInstanceFrontendField>,
    appid?: number,
    guardId?: number,
    insTotal?: number
}

// 根据路径去找值
const findValueByPath = (obj, path) => {
    try {
        const value = path.split('.').reduce((acc, key) => acc?.[key], obj);
        // 如果值是数组，返回逗号拼接的字符串.如果值是字符串，返回本身
        return Array.isArray(value) ? value.join(',') : value
    } catch (error) {
        return '--'; // 如果发生错误，返回--
    }
}

// 固定展示列
const initColumn = [
    {
        key: 'AppId',
        header: 'APPID',
        render: (ins) => {
            return <div style={{ overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }} title={ins.AppId} >
                {ins.AppId}
            </div>
        }
    }
]

const { selectable, removeable, scrollable, pageable, autotip } = Table.addons;

// 护航巡检实例
function ApprovalExpertInstance({ product, instanceFields, appid, guardId, insTotal }: Props) {
    //资源信息收集
    const [productDict, setProductDict] = useState({}) //产品中文名称
    const [activeIds, setActiveIds] = useState<Array<string>>([]) //展开的云产品

    const [currentColumns, setCurrentColumns] = useState([])
    const [currentRecords, setCurrentRecords] = useState([])

    const [total, setTotal] = useState(0);
    const [pageIndex, setPageIndex] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [records, setRecords] = useState([]);
    const [loading, setLoading] = useState(true);
    const [important, setImportant] = useState('1')
    const [firstSearch, setFirstSearch] = useState(true)
    //实例列
    const [columnData, setColumnData] = useState<any>()

    useEffect(() => {
        // 修改单选，页码变为1
        setPageIndex(1)
        getProductInstances({ pageIndex: 1, pageSize })
    }, [important])

    // 获取选择的产品实例
    async function getProductInstances({ pageIndex = 1, pageSize = 10 } = {}) {
        try {
            setLoading(true)
            // setRecords([]);
            const params = {
                AllAppid: true,
                AppId: appid,
                GuardId: guardId,
                Limit: pageSize,
                Offset: (pageIndex - 1) * pageSize,
                Products: [product],
                Important: parseInt(important)
            }
            // 记录当前页
            setPageIndex(pageIndex)
            setPageSize(pageSize)
            const res = await getInstances(params)
            if (res.Error) {
                setRecords([]);
                setTotal(res.TotalCount)
                setLoading(false)
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                // 如果没有重点关注，直接展示全部
                if (firstSearch && res.TotalCount === 0 && important === '1') {
                    setImportant('0')
                    return
                }
                setFirstSearch(false)
                setRecords([...res.Instance])
                setTotal(res.TotalCount)
                setLoading(false)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    const getColumnData = () => {
        //定义标签 render
        const getTagColumn = (Item) => {
            return Item.Tags && Item.Tags.length ? (
                <>
                    {Item.Tags}
                </>
            ) : (
                <>{'无'}</>
            )
        }

        let columns: Array<any> = []
        if (_.isEmpty(records) || _.isEmpty(instanceFields) || instanceFields.length === 0) {
            return columns
        }

        instanceFields.filter(i => {
            if (i.FieldName) {
                // 过滤 FieldName 为空
                return i
            }
        }).map(j => {
            columns.push(
                {
                    key: j.Field,
                    header: <>{j.FieldName}</>,
                    render: Item => {
                        let str = ''
                        // FieldType：string, int, stringSlice, Tags 的判断
                        if (j.FieldType === 'string' || j.FieldType === 'int') {

                            if (j.FieldDict && j.FieldDict.length) {
                                // 尝试从FieldDict获取
                                let tmp = j.FieldDict.find(k => {
                                    let key = j.FieldType === 'int' ? Item[j.Field].toString() : Item[j.Field]
                                    if (k.Key === key) { return k }
                                })
                                if (tmp) {
                                    str = tmp.Value
                                } else {
                                    str = Item[j.Field]
                                }
                            } else {
                                str = Item[j.Field]
                            }
                        } else if (j.FieldType === 'stringSlice') {
                            if (Item[j.Field] && Item[j.Field].length) {
                                if (_.isArray(Item[j.Field])) {
                                    str = Item[j.Field].join(';')
                                } else {
                                    str = Item[j.Field]
                                }
                            }
                        } else if (j.FieldType === 'tags') {
                            if (Item.Tags && Item.Tags.length) {
                                str = Item[j.Field]
                            } else {
                                str = '无'
                            }
                        } else {
                            if (Item[j.Field]) {
                                str = Item[j.Field]
                            }
                        }
                        return <>{str}</>
                    }
                }
            )
        })
        setCurrentColumns(columns)
    }

    //获取实例展示列
    const getColumns = async () => {
        try {
            const res = await getProductsGroups({
                AppId: appid,
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                const columnData = res.FilterPolicy.filter(i => i.Product === product)
                let columns: any = columnData.length ? columnData[0].FilterInfo.filter(i => i.Uses === 'Right') : []

                // 最终的展示列
                let result: any = columns.map((i, index) => {
                    return {
                        key: index,
                        header: i.FilterName,
                        render: (ins) => {
                            let value: any = ''
                            if (i.DataFrom === 'Info') {
                                value = ins[i.FilterCorrespondName]
                            } else if (i.DataFrom === 'Extra') {
                                const extraData = ins.Extra ? JSON.parse(ins.Extra) : {}
                                value = findValueByPath(extraData, i.Location)
                            } else if (i.DataFrom === 'Policy') {
                                const policy = ins.Policy.filter(j => j.MetricName === i.Location)[0]
                                value = policy ? (policy.Type === 'Other' ? (policy.OtherValue || '未填写') : policy.Value) : ''
                            }
                            // 根据映射展示对应的值
                            if (value !== '' && i.ExtraShowName?.length) {
                                const list = String(value).split(';')
                                let result = []
                                list.map(val => {
                                    const colValueItem = i.ExtraShowName.filter(i => i.Key == val)
                                    const tempValue = colValueItem.length ? colValueItem[0].Value : val
                                    result.push(tempValue)
                                })
                                value = result.join(';')
                            }
                            // 是否有大时间跨度或大聚合查询展示优化
                            if (i.Location === "AggregateQuery") {
                                value = value === 0 ? '是' : '否'
                            }
                            return <div style={{ overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }} title={value} >
                                {value}
                            </div>
                        }
                    }
                })

                setColumnData([...initColumn, ...result])
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    useEffect(() => {
        // if (instanceFields.length > 0) {
        //     getColumnData()
        // }
        getColumns()
    }, [instanceFields, records])

    return (
        <div>
            <Collapse activeIds={activeIds} onActive={v => { setActiveIds(v) }} destroyInactivePanel={false} icon={active => <Icon type={active ? "minus" : "plus"} />}
                iconPosition="right">
                {
                    <div key={product}>
                        <Collapse.Panel key={product} id={product} title={(productDict[product] || "") + "重点护航实例（" + (insTotal || 0) + "条）"}>
                            {
                                <section style={{ margin: '12px 0px' }}>
                                    <Radio.Group value={important} onChange={value => setImportant(value)}>
                                        <Radio name='1'>仅展示重点关注资源</Radio>
                                        <Radio name='0'>展示全部资源</Radio>
                                    </Radio.Group>
                                </section>
                            }
                            {
                                <Table
                                    verticalTop
                                    records={records}
                                    recordKey="InstanceId"
                                    columns={columnData || []}
                                    addons={[
                                        pageable({
                                            pageIndex: pageIndex,
                                            recordCount: total,
                                            onPagingChange: query => getProductInstances(query),
                                        }),
                                        autotip({
                                            isLoading: loading,
                                        }),
                                        scrollable({ maxHeight: 480 }),
                                    ]}
                                />
                            }
                        </Collapse.Panel>
                    </div>
                }
            </Collapse>
        </div>
    )
}

export default ApprovalExpertInstance
