import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Card, Transfer, Table, SearchBox, Select, Form, Bubble, SelectMultiple, Input, message, StatusTip, Icon, Tag } from "@tencent/tea-component";
const { columnsResizable, selectable, removeable, scrollable, pageable, autotip } = Table.addons;
import { Row, Col } from "@tencent/tea-component";
import _ from 'lodash'
import { policyItem, InstanceItem, ScanResultInstance } from '@src/types/advisor/guard';


interface Props {
    strategyId: number,
    result: ScanResultInstance,
}

// 护航巡检结果实例
function ApprovalDetailInstanceRes({ strategyId, result }: Props, ref) {
    //实例记录
    const [columns, setColumns] = useState<any>()
    //表格Key
    const [recordKey, setRecordKey] = useState("InstanceId")

    useImperativeHandle(ref, () => ({
        Check: () => { },
    }))

    const getColumnData = () => {
        //定义标签 render
        const getTagColumn = (Item) => {
            return Item.Tags && Item.Tags.length ? (
                <>
                    {Item.Tags}
                </>
            ) : (
                <>{'无'}</>
            )
        }

        let columns: Array<any> = []
        if (_.isEmpty(result) || _.isEmpty(result.FrontendField) || result.FrontendField.length === 0) {
            return columns
        }

        result.FrontendField.filter(i => {
            if (i.FieldName) {
                // 过滤 FieldName 为空
                return i
            }
        }).map(j => {
            columns.push(
                {
                    key: j.Field,
                    header: <>{j.FieldName}</>,
                    render: Item => {
                        let str = ''
                        // FieldType：string, int, stringSlice, Tags 的判断
                        if (j.FieldType === 'string' || j.FieldType === 'int') {
                            if (j.FieldDict && j.FieldDict.length) {
                                // 尝试从FieldDict获取
                                let tmp = j.FieldDict.find(k => {
                                    let key = j.FieldType === 'int' ? Item[j.Field].toString() : Item[j.Field]
                                    if (k.Key === key) { return k }
                                })
                                if (tmp) {
                                    str = tmp.Value
                                } else {
                                    str = Item[j.Field]
                                }
                            } else {
                                str = Item[j.Field]
                            }
                        } else if (j.FieldType === 'stringSlice') {
                            if (Item[j.Field] && Item[j.Field].length) {
                                if (_.isArray(Item[j.Field])) {
                                    str = Item[j.Field].join(';')
                                } else {
                                    str = Item[j.Field]
                                }
                            }
                        } else if (j.FieldType === 'tags') {
                            if (Item.Tags && Item.Tags.length) {
                                str = Item[j.Field]
                            } else {
                                str = '无'
                            }
                        } else {
                            if (Item[j.Field]) {
                                str = Item[j.Field]
                            }
                        }
                        return <>{str}</>
                    }
                }
            )
        })
        return columns
    }

    useEffect(() => {
        let ret = getColumnData()
        setColumns(ret)
    }, [result])


    return (
        <div key={strategyId}>
            <section>
                <Row style={{ marginTop: 10 }}>
                    <Col>
                        <Card>
                            <Table
                                compact={true}
                                verticalTop
                                records={result.Instance || []}
                                recordKey="InstanceId"
                                columns={columns || []}
                                addons={[
                                    pageable(),
                                    autotip({
                                        emptyText: '没有数据',
                                    }),
                                    columnsResizable({
                                        onResizeEnd: columns => {
                                            setColumns(columns);
                                        },
                                        minWidth: 100,
                                        maxWidth: 1000,
                                    })]}
                            />
                        </Card>
                    </Col>
                </Row>
            </section>
        </div>
    )
}

export default forwardRef(ApprovalDetailInstanceRes)