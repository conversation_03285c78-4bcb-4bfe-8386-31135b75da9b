import React, { useState, useEffect, useMemo } from 'react';
import { Card, message as tips, Row, Col, Layout, message, Button, PopConfirm, Radio, Text, Form, Input, Bubble, Alert, Tabs, TabPanel } from '@tencent/tea-component';
import { CreateGuardExpertApproval, DescribeGuardExpertApproval, DescribeGuardProjects, DescribeGuardSheet } from '@src/api/advisor/guard';
import { StandardDict, ExpertApproval, ExpertApprovalParams, ExpertApprovalInstanceFrontendField } from '@src/types/advisor/guard';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { useHistory, useAegisLog } from '@tea/app';
import _ from 'lodash'
import { getUserInfo } from '@src/api/common';
import RTXPicker from '@tencent/qmfe-yoa-react-ui/es/RTXPicker/index';
import ApprovalExpertInstance from './ApprovalExpertInstance';
import { useCheckIfAuthed, NoPermission, PermissionLoading } from '../../../broadcast/hooks/checkIfAuthed';
const { Body, Content } = Layout;
const { TextArea } = Input;
import { DescInstancePanel } from "@src/routes/advisor/pages/guard/components/DescInstancePanel";
import { CustomerName } from "@src/routes/advisor/pages/guard/components/CustomerName";
//驳回信息
const rejectMsg = "驳回后，该产品将不会进行护航。请与行业确认清楚后驳回。如有修改请联系护航负责人。"
//审批结果列表
const approvalStatus = {
    agreeOnResidence: 1,
    agreeOnlyOnline: 2,
    disagree: -1,
    agreeOnResidenceStr: "1",
    agreeOnlyOnlineStr: "2",
    disagreeStr: "-1",
}

export function ApprovalExpert(match) {
    const history = useHistory();
    const aegis = useAegisLog();
    const { isAuth, loading: authLoading } = useCheckIfAuthed({
      pageStatus: 'guard-approval',
      key: 'TaskId',
      value: match.match.params.apvlid,
    });
    //售后审核信息
    const [apvlData, setApvlData] = useState<ExpertApproval>({} as ExpertApproval)
    //实例描述字段
    const [field, setField] = useState<Array<ExpertApprovalInstanceFrontendField>>([])
    //实例列表
    const [instanceList, setInstanceList] = useState([])
    //当前护航ID
    const [guardId, setGuardId] = useState(Number(match.match.params.guardid))
    const [apvlId, setApvlId] = useState(Number(match.match.params.apvlid))
    //当前审核名称
    const [apvlName, setApvlName] = useState("")
    //当前审核状态，0-未处理、1-已处理
    const [apvlStatus, setApvlStatus] = useState(0)
    //审核产品
    const [apvlProduct, setApvlProduct] = useState("")
    //护航项目选项
    const [projectDict, setProjectDict] = useState(new Map())
    //是否有审批权限
    const [hasOpsPermission, setHasOpsPermission] = useState(false)
    // 需要驻场支持的云产品
    const [onsiteProductZh, setOnsiteProductZh] = useState([])
    // 审批产品是否需要驻场
    const [productNeedOnSite, setProductNeedOnSite] = useState(false)
    // 护航天数
    const [guardDay, setGuardDay] = useState(0)
    // 是否同意
    const [isAgree, setIsAgree] = useState(false)
    // 审批结果：同意（线上或驻场）、驳回
    const [agreeState, setAgreeState] = useState<number>(0)
    // 审批选项
    const [selected, setSelected] = useState<string>("2")
    // 派遣支持人员
    const [supporter, setSupporter] = useState<Array<string>>([])
    // 关注人
    const [follower, setFollower] = useState<Array<string>>([])
    // 理由
    const [reason, setReason] = useState("")
    // 派遣支持人员--提示
    const [visibleSupporterEdit, setVisibleSupporterEdit] = useState(false)
    // 理由--提示
    const [visibleReasonEdit, setVisibleReasonEdit] = useState(false)
    // 实例总数
    const [insTotal, setInsTotal] = useState(0)
    // 基本信息
    const creator = apvlData.Creator               //提单人
    const guardName = apvlData.GuardName           //护航名称
    const project = apvlData.Project               //护航项目
    const standard = apvlData.Standard             //护航类型
    const startTime = apvlData.StartTime           //护航开始时间
    const endTime = apvlData.EndTime               //护航结束时间
    const keyTime = apvlData.ImportantGuardTime               //重点护航时间
    const onsiteTime = apvlData.OnsiteTime         //驻场时间
    const onsiteProducts = apvlData.OnsiteProducts //驻场产品
    const customerInfo = apvlData.CustomerInfo
    const customerInfoStr = (customerInfo || []).map((val) => {
        return <div><Text>{val.AppId + " | " + val.CustomerName}</Text></div>
    })
    const oneAppId = (customerInfo || []).length > 0 ? customerInfo[0].AppId : 0

    //未填写字段
    const noSet = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>
    //护航时间展示
    const startDate = (startTime || "")
    const endDate = (endTime || "")
    const guardTimeStr = (startTime && endTime) ? [(startDate === endDate ? startDate : (startDate + " ~ " + endDate)) + " 共 ", <Text style={{ color: "blue" }}>{guardDay}</Text>, " 天"] : [noSet]
     //重点护航时间展示
     const keyTimeLength = (keyTime || []).length
     const keyTimeStr = keyTimeLength ? [keyTime.join(","), " 共 ", <Text style={{ color: "blue" }}>{keyTimeLength}</Text>, " 天"] : [noSet]
    //驻场护航时间展示
    const onsiteTimeLength = (onsiteTime || []).length
    const onsiteTimeStr = onsiteTimeLength ? [onsiteTime.join(","), " 共 ", <Text style={{ color: "blue" }}>{onsiteTimeLength}</Text>, " 天"] : [noSet]

    //当前登录rtx
    const [rtx, setRtx] = useState<string>('')
    // 护航单详情
    const [currentGuard, setCurrentGuard] = useState({})
    const [appidAuthorizedMap, setAppidAuthorizedMap] = useState(new Map<number | string, boolean>());
    const [productMap, setProductMap] = useState(new Map());
    const [activeId, setActiveId] = useState('');
    const [tabs, setTabs] = useState([]);

    useEffect(() => {
        if (productMap.has(apvlProduct)) {
            const appids = productMap.get(apvlProduct)
            setTabs((appids || []).map((item, i) => {
                return {
                    id: item,
                    label: <CustomerName appid={Number(item)} need={false} />
                }
            }))
            setActiveId(appids[0]);
        }
    }, [productMap, apvlProduct])

    //获取当前登录账号rtx
    const getCurrentOperator = async () => {
        try {
            const res = await getUserInfo()
            const name = res.data.EngName || ''
            setRtx(name)
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //计算两个Y-m-d H:m:s格式的时间差
    const getGuardDay = async (startTime, endTime) => {
        var reggie = /(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})/;
        var formatStartTime = reggie.exec(startTime);
        var formatEndTime = reggie.exec(endTime);
        var objStartTime = new Date()
        var objEndTime = new Date()
        if (formatStartTime && formatEndTime) {
            objStartTime = new Date(
                (+formatStartTime[1]),
                (+formatStartTime[2]) - 1,
                (+formatStartTime[3]),
                (+formatStartTime[4]),
                (+formatStartTime[5]),
                (+formatStartTime[6])
            );
            objEndTime = new Date(
                (+formatEndTime[1]),
                (+formatEndTime[2]) - 1,
                (+formatEndTime[3]),
                (+formatEndTime[4]),
                (+formatEndTime[5]),
                (+formatEndTime[6])
            );
        }
        const diffDay = ((objEndTime.getTime() - objStartTime.getTime()) / 1000 / 60 / 60 / 24).toFixed(0)
        setGuardDay(Number(diffDay))
    }

    //查询售后审核信息
    const getExpertApprovalInfo = async () => {
        try {
            const res = await DescribeGuardExpertApproval({
                AppId: oneAppId || 1253985742,
                GuardId: Number(match.match.params.guardid),
                ApprovalId: Number(match.match.params.apvlid),
                Operator: rtx,
                AllAppid: true
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setSupporter(res.ExpertApproval.Supporter)               //同意派遣人员
                setFollower(res.ExpertApproval.Follower)                 //同意关注人
                setApvlData(res.ExpertApproval)

                setField(res.FrontendField)
                setInstanceList(res.Instance)

                setHasOpsPermission(res.ExpertApproval.HasOpsPermission) //是否有权限审批
                setAgreeState(res.ExpertApproval.IsAgree)                //同意（驻场或线上）、驳回
                setProductNeedOnSite(res.ExpertApproval.OnsiteProducts.includes(res.ExpertApproval.Product))
                setApvlProduct(res.ExpertApproval.Product)               //审核产品
                setApvlStatus(res.ExpertApproval.Status)                 //审核状态
                setApvlName(res.ExpertApproval.ApprovalName)             //审批名称
                setReason(res.ExpertApproval.Reason)                     //不同意内容
                setInsTotal(res.InstanceTotalCount || 0)                  //实例总数
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 变更审核状态
    const modifyApprovalStatus = async (selected: string) => {
        try {
            let params: ExpertApprovalParams = {
                ApprovalId: apvlId,
                GuardId: guardId,
                AppId: oneAppId || 1253985742,
                Product: apvlProduct,
                IsAgree: Number(selected),
                Supporter: supporter,
                Reason: reason,
                Follower: follower,
                Operator: rtx,
            }
            const res = await CreateGuardExpertApproval(params)
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "提交成功" })
            //更新审核状态，重载页面
            //location.reload()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //获取云产品中文描述
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: 1253985742, // CGI代码直接拉取advisor_strategy或advisor_product表，暂无appid指定。
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let onsiteProductDesc = []
                for (var key in onsiteProducts) {
                    let productEn = onsiteProducts[key]
                    onsiteProductDesc.push(res.ProductDict[productEn] || "")
                }
                setOnsiteProductZh(onsiteProductDesc)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //查询护航项目清单
    const getProjects = async () => {
        try {
            const res = await DescribeGuardProjects({
                AppId: 1253985742, //接口必须传appid  为获取全量产品列表，因此传内部中心账号
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                let tmp = []
                let m = new Map()
                res.Projects.map(i => {
                    tmp.push({ value: i.Id.toString(), text: i.Name })
                    m.set(i.Id, i.Name)
                })
                // setProjectOptions(tmp)
                setProjectDict(m)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    // 查询护航单详情
    const getGuardSheet = async () => {
        try {
            const filters = [
                { Name: 'guard_id', Values: [match.match.params.guardid] },
            ];
            const res = await DescribeGuardSheet({
                Filters: filters.filter((i) => {
                    if (i.Values.length) {
                        return i;
                    }
                }),
                Offset: 0,
                Limit: 10,
                AppId: 1253985742, // 接口必须传appid
            });
            if (res.Error) {
                const msg = res.Error.Message;
                tips.error({ content: msg });
                return;
            }
            const item = res.Guard[0] || {};
            setCurrentGuard(item);
            const tmpMap = new Map();
            const tempProductMap = new Map()
            if ((item.ProductDesc || []).length) {
                const sortList = [item.MainAppId].concat(item.RelatedAppId);
                item.ProductDesc.map((i) => {
                    tmpMap.set(i.AppId, i.IsAuthorized);
                    if (tempProductMap.has(i.Product)) {
                        tempProductMap.set(i.Product, [...tempProductMap.get(i.Product), i.AppId])
                    } else {
                        tempProductMap.set(i.Product, [i.AppId])
                    }
                    tempProductMap.forEach((value, key) => {
                        tempProductMap.set(key, value.sort((x, y) => {
                            return sortList.indexOf(x) - sortList.indexOf(y);
                        }));
                    });
                });
            }
            setProductMap(tempProductMap)
            setAppidAuthorizedMap(tmpMap);
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            tips.error({ content: msg });
        }
    };

    //init
    useEffect(() => {
        getCurrentOperator()
        if (rtx) {
            getExpertApprovalInfo()
            // getGuardApprovalProgress()
        }
    }, [rtx])

    useEffect(() => {
        aegis.reportEvent({
            name: 'manual-PV',
            ext1: location.pathname,
            ext2: '护航审批/处理页面',
            ext3: localStorage.getItem('engName')
        })
        getGuardSheet()
    }, [])

    useEffect(() => {
        getProjects()
        getProductsGroupsInfo()
    }, [onsiteProducts])

    useEffect(() => {
        if (startTime !== "" && endTime !== "") {
            getGuardDay(startTime, endTime)
        }
    }, [startTime, endTime])

    const [confirmVisible, setConfirmVisible] = useState(false);
    useMemo(() => {
        if (selected == approvalStatus.agreeOnlyOnlineStr && productNeedOnSite && reason.length == 0) {
            setConfirmVisible(false);
            return;
        }
        if (supporter.length === 0 && (selected == approvalStatus.agreeOnlyOnlineStr || selected == approvalStatus.agreeOnResidenceStr)) {
            setConfirmVisible(false);
            return;
        };
        if (apvlStatus == 0 && selected == approvalStatus.disagreeStr) {
            if (reason.trim() === '') {
                setConfirmVisible(false);
                return;
            }
        }
        setConfirmVisible(true);
    }, [supporter, apvlStatus, approvalStatus, reason, selected]);

    if (authLoading) {
      return <PermissionLoading />;
    }
    if (isAuth === false) {
      return <NoPermission />;
    }
    return (
        <Body>
            {
                apvlId === 0 && <div style={{ margin: 20, color: "red" }}>
                    <Alert type="error" style={{ marginTop: 8 }}>审核ID不存在！请联系管理员。</Alert>
                </div>
            }
            {(apvlId || apvlName) && <Content>
                <Content.Header
                    showBackButton
                    onBackButtonClick={() => { history.push('/advisor/approval') }}
                    title={apvlName || "售后审批"}>
                </Content.Header>
                <Content.Body>
                    <Card key={"apvl_base_info"} style={{ margin: 10 }}>
                        <Card.Body>
                            <Row gap={30}>
                                <Col>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">任务ID</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{apvlId}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">提单人</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{creator}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航ID | 名称</Text>
                                            </Col>
                                            <Col span={18}>
                                                <a href={"/advisor/guard/summary/" + guardId} target="_blank">{guardId} </a> | <a href={"/advisor/guard/summary/" + guardId} target="_blank">{guardName}</a>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">客户 APPID | 名称</Text>
                                            </Col>
                                            <Col span={18}>
                                                <Text>{customerInfoStr}</Text>
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航项目</Text>
                                            </Col>
                                            <Col span={18}>
                                                {projectDict.get(project)}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航类型</Text>
                                            </Col>
                                            <Col span={18}>
                                                {StandardDict.get(standard)}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航时间</Text >
                                            </Col>
                                            <Col span={18}>
                                                {/* {startTime.split(" ")[0] + " ~ " + endTime.split(" ")[0] + " 共 "}<Text style={{ color: "blue" }}>{guardDay}</Text>{" 天"} */}
                                                {guardTimeStr}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航重点时间</Text >
                                            </Col>
                                            <Col span={18}>
                                                {keyTimeStr}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">驻场护航时间</Text >
                                            </Col>
                                            <Col span={18}>
                                                {onsiteTimeStr}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">驻场重点支持的云产品</Text >
                                            </Col>
                                            <Col span={18}>
                                                {(onsiteProductZh.length != 0) ? (onsiteProductZh || []).join("、") : noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">封网需求</Text >
                                            </Col>
                                            <Col span={18}>
                                                {apvlData.ClosedNetworkDemand || noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                    <section>
                                        <Row verticalAlign={"middle"}>
                                            <Col span={6} >
                                                <Text theme="label" verticalAlign="middle">护航背景及需求</Text >
                                            </Col>
                                            <Col span={18}>
                                                {apvlData.StatementOfNeeds || noSet}
                                            </Col>
                                        </Row>
                                    </section>
                                    {/* <section>
                                        <Row verticalAlign={'middle'}>
                                            <Col span={6}>
                                                <Text theme="label" verticalAlign="middle">每日巡检</Text>
                                            </Col>
                                            <Col span={18}>
                                                {apvlData.CronType === 0 ? '未开启' : '开启'}
                                            </Col>
                                        </Row>
                                    </section> */}
                                </Col>
                            </Row>
                            <Tabs
                                tabs={tabs}
                                activeId={activeId}
                                onActive={(tab) => { setActiveId(tab.id) }}
                                style={{ marginTop: '35px' }}>
                                {tabs.map(tab => (
                                    <TabPanel id={tab.id} key={tab.id}>
                                        <DescInstancePanel
                                            appid={Number(tab.id)}
                                            authorized={appidAuthorizedMap.get(Number(tab.id))}
                                            currentGuard={currentGuard}
                                            showProduct={apvlProduct}
                                        />
                                    </TabPanel>
                                ))}
                            </Tabs>
                        </Card.Body>
                    </Card>
                    <Card key={"apvl_sales"} style={{ margin: 10 }}>
                        <Card.Body>
                            {/* 已审批，对应同意或不同意选项的展示内容 */}
                            {apvlStatus == 1 && agreeState == approvalStatus.agreeOnResidence &&
                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    {productNeedOnSite && <Form.Item label="审批意见">
                                        <Form.Text>{"同意驻场"}</Form.Text>
                                    </Form.Item>}
                                    <Form.Item label="派遣支持人员">
                                        <Form.Text>{supporter.join("; ") || noSet}</Form.Text>
                                    </Form.Item>
                                    <Form.Item label="关注人">
                                        <Form.Text>{follower.join("; ") || noSet}</Form.Text>
                                    </Form.Item>
                                </Form>
                            }
                            {apvlStatus == 1 && agreeState == approvalStatus.agreeOnlyOnline &&
                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    <Form.Item label="审批意见">
                                        <Form.Text>{"仅在线支持"}</Form.Text>
                                    </Form.Item>
                                    <Form.Item label="理由">
                                        <Form.Text>{reason || noSet}</Form.Text>
                                    </Form.Item>
                                    <Form.Item label="派遣支持人员">
                                        <Form.Text>{supporter.join("; ") || noSet}</Form.Text>
                                    </Form.Item>
                                    <Form.Item label="关注人">
                                        <Form.Text>{follower.join("; ") || noSet}</Form.Text>
                                    </Form.Item>
                                </Form>
                            }
                            {apvlStatus == 1 && agreeState == approvalStatus.disagree &&
                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    <Form.Item label="审批意见">
                                        <Form.Text>{"驳回"}</Form.Text>
                                    </Form.Item>
                                    <Form.Item label="理由">
                                        <Form.Text>{reason || noSet}</Form.Text>
                                    </Form.Item>
                                </Form>
                            }

                            {/* 未审批，对应同意或不同意选项的填写内容 */}
                            {apvlStatus == 0 && hasOpsPermission && (productNeedOnSite
                                ?
                                <Radio.Group value={selected} onChange={value => { setSelected(value) }}>
                                    <Radio name={approvalStatus.agreeOnResidenceStr} disabled={!productNeedOnSite}>{"同意驻场"}</Radio>
                                    <Radio name={approvalStatus.agreeOnlyOnlineStr}>{"仅在线支持"}</Radio>
                                    <Radio name={approvalStatus.disagreeStr}>{"驳回"}</Radio>
                                </Radio.Group>
                                :
                                <Radio.Group value={selected} onChange={value => { setSelected(value) }}>
                                    <Radio name={approvalStatus.agreeOnlyOnlineStr}>{"同意"}</Radio>
                                    <Radio name={approvalStatus.disagreeStr}>{"驳回"}</Radio>
                                </Radio.Group>)
                            }
                            {apvlStatus == 0 &&
                                <Form style={{ marginTop: 20 }} layout={"default"}>
                                    {(selected == approvalStatus.agreeOnlyOnlineStr && productNeedOnSite) &&
                                        <Form.Item label=" * 仅在线支持理由">
                                            <Bubble error visible={visibleReasonEdit} content={'请输入仅在线支持理由'}>
                                                <TextArea size={'l'} value={reason} placeholder="请输入理由"
                                                    disabled={!hasOpsPermission}
                                                    onChange={(value) => {
                                                        setReason(value)
                                                        if (value.trim() === '') {
                                                            setVisibleReasonEdit(true)
                                                        } else {
                                                            setVisibleReasonEdit(false)
                                                        }
                                                    }}
                                                />
                                            </Bubble>
                                        </Form.Item>}
                                    {selected == approvalStatus.disagreeStr &&
                                        <Form.Item label=" * 驳回请求原因">
                                            <Bubble error visible={visibleReasonEdit} content={'请输入驳回请求原因'}>
                                                <TextArea size={'l'} value={reason} placeholder={rejectMsg}
                                                    disabled={!hasOpsPermission}
                                                    onChange={(value) => {
                                                        setReason(value)
                                                        if (value.trim() === '') {
                                                            setVisibleReasonEdit(true)
                                                        } else {
                                                            setVisibleReasonEdit(false)
                                                        }
                                                    }}
                                                />
                                            </Bubble>
                                        </Form.Item>}
                                    {selected != approvalStatus.disagreeStr &&
                                        <Form.Item label=" * 派遣支持人员" align='middle'>
                                            <Bubble error visible={visibleSupporterEdit} content={'请输入支持人员名称'}>
                                                {apvlData.ApprovalId && <RTXPicker
                                                    style={{ "width": "75%" }}
                                                    initRtx={supporter}
                                                    valueType="array"
                                                    disabled={!hasOpsPermission}
                                                    onChange={(tags: any) => {
                                                        setSupporter(tags.length ? tags : [])
                                                        if (tags.length === 0) {
                                                            setVisibleSupporterEdit(true)
                                                        } else {
                                                            setVisibleSupporterEdit(false)
                                                        }
                                                    }}
                                                />}
                                            </Bubble>
                                        </Form.Item>}
                                    {selected != approvalStatus.disagreeStr &&
                                        <Form.Item label="关注人" align='middle'>
                                            {apvlData.ApprovalId && <RTXPicker
                                                style={{ "width": "75%" }}
                                                valueType="array"
                                                initRtx={follower}
                                                disabled={!hasOpsPermission}
                                                onChange={(tags: any) => {
                                                    setFollower(tags ? tags : [])
                                                }}
                                            />}
                                        </Form.Item>}
                                </Form>
                            }
                            <div style={{ margin: 20, textAlign: "center" }}>
                                {hasOpsPermission && !apvlStatus &&
                                    <Button type={"weak"} style={{ marginRight: 15 }} onClick={() => { history.push('/advisor/approval') }}>
                                        取消
                                    </Button>}
                                {hasOpsPermission && !apvlStatus &&
                                    <PopConfirm
                                        disabled={!confirmVisible}
                                        arrowPointAtCenter={true}
                                        title={"确认要提交审核结果吗？"}
                                        message={selected == approvalStatus.disagreeStr ? rejectMsg : "提交后，审核内容不再支持修改!"}
                                        // disabled={(selected == AgreeOnResidenceStr && supporter.length === 0) || ([AgreeOnlyOnlineStr, DisagreeStr].includes(selected) && reason === "")}
                                        footer={close => (
                                            <>
                                                <Button type={"primary"} onClick={() => { modifyApprovalStatus(selected); history.push('/advisor/approval'); close(); }}>
                                                    确定
                                                </Button>
                                                <Button onClick={() => { close() }} type="text">{"取消"}</Button>
                                            </>
                                        )}
                                        placement={"top"}>
                                        <Button
                                            type={"primary"}
                                            disabled={
                                                !confirmVisible ||
                                                (selected == approvalStatus.agreeOnResidenceStr && supporter.length === 0) ||
                                                (selected == approvalStatus.agreeOnlyOnlineStr && productNeedOnSite && reason === "") ||
                                                (selected == approvalStatus.disagreeStr && reason === "")}
                                        >
                                            提交
                                        </Button>
                                    </PopConfirm>
                                }
                            </div>
                        </Card.Body>
                    </Card>
                </Content.Body>
            </Content>}
        </Body >
    );
}
