import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { TagSearchBox, message } from "@tencent/tea-component";
import { AttributeValue } from "@tencent/tea-component/src/tagsearchbox/AttributeSelect";
import { FilterInfoItem } from "@src/types/advisor/guard";
import _ from 'lodash'
import { TagItem } from '@src/types/common';
import { getTagsOption } from '@src/api/architecture/architecture';

interface Props {
    filterInfo: Array<FilterInfoItem>,
    tagInfo?: Array<TagItem>,
    CallBack: Function,
    appid: number,
    product: string,
    region: string
}

interface Filter {
    Name: string,
    Values: Array<string>,
}

interface TagSearchItem {
    key: string,
    name: string,
}

function RightSearchBox({ CallBack, filterInfo, appid, product, region }: Props, ref) {
    //tag
    const [tags, setTags] = useState([])
    const [currenttags, setcurrenttags] = useState([])
    // filter
    const [filter, setFilter] = useState<Array<Filter>>([])
    //标签选项
    const [tagInfo, setTagInfo] = useState<Array<TagItem>>([])

    //把filterInfo 转换为 attributes
    const attributes: Array<AttributeValue> = useMemo(() => {
        let tagOptions = getTagOptions(tagInfo)
        let tmp: Array<AttributeValue> = []
        filterInfo.map(i => {
            if (i.Type == "Tag") {
                tmp.push({
                    type: ["multiple", { all: false, searchable: true }],
                    key: i.FilterName,
                    name: i.FilterName,
                    values: tagOptions,
                })
            } else {
                tmp.push({
                    type: "input",
                    key: i.FilterName,
                    name: i.FilterName,
                })
            }
        })
        return tmp
    }, [filterInfo, tagInfo])

    //查询产品标签信息
    const getTags = async (product: string) => {
        try {
            const res = await getTagsOption(
                {
                    AppId: appid,
                    Product: product,
                    Regions: region ? [region] : [],
                })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                if (_.isEmpty(res.Tags)) {
                    setTagInfo([])
                } else {
                    setTagInfo(res.Tags)
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //获取标签下拉选项
    function getTagOptions(tagInfo: Array<TagItem>) {
        let tmpList: Array<TagSearchItem> = [];
        (tagInfo || []).map(t => {
            let tk = t.TagKey
            t.TagValues.map(tv => {
                let tmp: TagSearchItem = {
                    key: tk,
                    name: tk + "::" + tv, //指定分隔符
                }
                tmpList.push(tmp)
            })
        });

        return tmpList
    }


    //监听tags变化
    useEffect(() => {
        let Filter = []
        let tagsTmp = []
        tags.map(i => {
            let tmp = _.cloneDeep(i)
            if (i.attr) {
                let Name = i.attr.key
                let Values = []
                //判断是否只支持单个，如果只支持单个，则默认取第一个
                if (filterInfo.filter(item => { if (item.FilterName === Name) { return item } }).length) {
                    let item = filterInfo.filter(item => { if (item.FilterName === Name) { return item } })[0]
                    if (item.Type === 'Single') {
                        tmp.values = [i.values[0]]
                        Values.push(i.values[0].name)
                        if (i.values.length > 1) {
                            message.warning({ content: item.FilterAlias + "只支持单个实例搜索" })
                        }
                    } else {
                        i.values.map(j => {
                            if (Values.indexOf(j.name) === -1) {
                                Values.push(j.name)
                            }
                        })
                    }
                }
                Filter.push({
                    Name: Name,
                    Values: Values
                })
                tagsTmp.push(tmp)
            }
        })
        CallBack(Filter)
        setFilter(Filter)
        setcurrenttags(tagsTmp)
    }, [tags])

    //当地域变化时，重新搜索标签
    useEffect(() => {
        getTags(product)
    }, [region])

    return (
        <>
            <TagSearchBox
                attributes={attributes}
                  minWidth={'100%'}
                value={currenttags}
                onChange={tags => { setTags(tags) }}
            />
        </>
    )
}

export default RightSearchBox