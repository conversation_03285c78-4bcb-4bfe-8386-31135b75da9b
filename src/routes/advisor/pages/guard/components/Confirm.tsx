import React, { useState, useEffect, useMemo, useImperativeHandle, forwardRef } from 'react';
import { useRef } from "react";
import { Table, Justify, Button, SearchBox, Card, Layout, message as tips, Select, SelectMultiple, StatusTip, message, Icon, TabPanel, Tabs } from '@tencent/tea-component';
import { formatJson } from '@src/utils/formatJson';
import moment, { Moment } from 'moment';
import { Link } from 'react-router-dom';
import { Row, Col } from "@tencent/tea-component";
import { DatePicker, Form } from "@tencent/tea-component";
import { Text, Input } from "@tencent/tea-component";
import { Dropdown, List } from "@tencent/tea-component";
import { Tag } from "@tencent/tea-component";
import { Bubble } from "@tencent/tea-component";
const { RangePicker } = DatePicker;
const { Body, Content } = Layout;
const { selectable, scrollable, removeable, pageable, injectable } = Table.addons;
import { CreateGuardSheet } from '@src/api/advisor/guard';
import { GuardParams, map2options, InstanceItem } from '@src/types/advisor/guard';
import _ from 'lodash'
import { useHistory } from '@tea/app';
import { ConfirmBaseInfo } from './ConfirmBaseInfo';
import { ConfirmDetail } from './ConfirmDetail';
import { CustomerName } from './CustomerName';
interface Props {
    currentGuard?: GuardParams,
    handleUpdateStepsDisable: Function,
}

function Confirm({ currentGuard, handleUpdateStepsDisable }: Props, ref) {
    const history = useHistory();

    //当前Tab列表
    const [currentTabs, setCurrentTabs] = useState<Array<{ id: string, label: React.ReactNode }>>([])
    //全部的appid列表
    const [appids, setappids] = useState<Array<number>>([])
    //未授权appid列表
    const [appIdNAList, setAppIdNAList] = useState<Array<number>>([])
    //当前打开appid
    const [currentAppId, setCurrentAppId] = useState<string>(currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : '')
    //当前护航实例信息
    const [currentInstanceTemplate, setCurrentInstanceTemplate] = useState([])

    //创建护航单 toNext表示是否需要跳转到下一步
    const createOrModifyGuard = async () => {
        handleUpdateStepsDisable(true)
        let loading = message.loading({ content: '加载中...' })
        try {
            let params: GuardParams = {
                ...currentGuard,
            }
            // params.InstanceTemplate = currentInstanceTemplate
            params.Status = 2 //转换为 2  订单已提交 
            params.AppId = currentGuard.MainAppId || 1253985742
            const res = await CreateGuardSheet(params)
            loading.hide()
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                // 回掉antool Iframe 通知提交成功
                window.parent?.postMessage({
                    Source: 'Guard',
                    SourceId: currentGuard.GuardId,
                    MsgType: 'GuardSave',
                    GuardName: currentGuard.GuardName,
                    StartTime: currentGuard.StartTime,
                    EndTime: currentGuard.EndTime,
                    Operation: 'submit'
                }, '*')
                message.success({ content: "提交成功!" })
                history.push(`/advisor/guard`)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //更新未授权的appid
    function updateAppidNA(appidParams) {
        if (appidParams) {
            setAppIdNAList(i => i.concat([appidParams]))
        }
    }

    //暴露回调函数给父组件 
    useImperativeHandle(ref, () => ({
        OnClick: createOrModifyGuard
    }))

    useEffect(() => {
        //获取当前APPID列表
        let tmpAppids: Array<number> = []
        tmpAppids.push(currentGuard.MainAppId)
        currentGuard.RelatedAppId.map(i => {
            if (tmpAppids.indexOf(i) === -1) {
                tmpAppids.push(i)
            }
        })
        setappids(tmpAppids)

        //获取当前Tab页
        let tmpTabs: Array<{ id: string, label: React.ReactNode }> = []
        tmpAppids.map(i => {
            tmpTabs.push({
                id: i.toString(),
                label: <CustomerName appid={i} need={false} handleUpdateAppidNA={(i) => { updateAppidNA(i) }} />
            })
        })
        setCurrentTabs(tmpTabs)
    }, [])

    return (
        <Card style={{ margin: 10 }}>
            <Card.Body>
                <div>
                    <ConfirmBaseInfo guardBaseInfo={currentGuard} />
                </div>
                <hr />
                <div style={{ marginTop: 30 }}>
                    <Tabs tabs={currentTabs} placement={"top"} activeId={currentAppId} onActive={(v) => { setCurrentAppId(v.id) }} destroyInactiveTabPanel={false}>
                        {
                            appids.map(i => {
                                return (
                                    <TabPanel key={i} id={i.toString()}>
                                        <ConfirmDetail
                                            appid={i}
                                            authorized={!appIdNAList.includes(i)}
                                            currentGuard={currentGuard}
                                        />
                                    </TabPanel>
                                )
                            })
                        }
                    </Tabs>
                </div>
                <hr />
                <div>
                    <Icon type="infoblue" style={{ marginRight: 3, marginBottom: 1 }} />
                    <Text style={{ color: "blue" }}>{"提交后，后台会自动发起审批、建群等护航巡检流程，请确认填写信息准确完整。"}</Text>
                </div>
            </Card.Body>

        </Card>
    );
}

export default forwardRef(Confirm)
