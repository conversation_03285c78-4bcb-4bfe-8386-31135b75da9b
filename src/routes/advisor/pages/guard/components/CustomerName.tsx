import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { GetAccountInfoByFields } from '@src/api/advisor/guard';
import { Bubble, Icon, message, Text } from '@tencent/tea-component';
import { InstanceItem } from '@src/types/advisor/guard';
import { useGuard } from "@src/routes/advisor/pages/guard/components/state/GuardContext";

interface Props {
    appid: number,
    instanceTemplate?: Array<InstanceItem>,
    need?: boolean,
    allUnAuthProducts?: boolean
    defaultName?: string,
    handleUpdate?: Function,
    handleUpdateAppidNA?: Function,
    updateNoAfterSaleAppids?: Function,
    getCanCreate?: Function
}

export function CustomerName({ appid, instanceTemplate, need = false, allUnAuthProducts, defaultName = '', handleUpdate = () => { }, handleUpdateAppidNA = () => { }, updateNoAfterSaleAppids = () => { }, getCanCreate = () => { } }: Props, ref) {
    const [name, setName] = useState<string>('');
    const [visible, setVisible] = useState<boolean>(false);
    const [isAppidAuthorized, setIsAppidAuthorized] = useState<boolean>(true);
    const useGuardInfo = useGuard();

    //根据主appid查询客户名称
    const getMainCustomerName = async (appid: number) => {
        try {
            const res = await GetAccountInfoByFields({ AppId: appid })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                setName(res.CustomerName)
                //回调函数，刷新父组件的值
                handleUpdate(res.CustomerName)
                if (!res.IsAuthorized) {
                    handleUpdateAppidNA(appid)
                    setIsAppidAuthorized(false)
                } else {
                    getCanCreate(appid)
                }
                if (!res.HasAfterSale) {
                    updateNoAfterSaleAppids(appid)
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //输入文本，或输入文本合法性变化，进行回调，把结果返回
    useEffect(() => {
        //如果defaultName为空，则调用接口获取
        if (defaultName === '') {
            getMainCustomerName(appid)
        } else {
            setName(defaultName)
        }
    }, [])

    //刷新visible
    useEffect(() => {
        if (need && instanceTemplate.filter(i => { if (i.AppId === appid) { return i } }).length === 0) {
            if (useGuardInfo?.onlyConatainUnSupportProduct?.[appid]) {
                //未授权账号允许实例为空、全是兜底产品允许实例为空
                setVisible(false)
            } else {
                setVisible(true)
            }
        } else {
            setVisible(false)
        }
    }, [need, instanceTemplate, useGuardInfo])

    return (
        <div key={appid}>
            <Bubble visible={visible} error content={isAppidAuthorized ? "未勾选任何护航实例" : "未勾选任何护航产品"}>
                <Text>{appid.toString()}|{name}</Text>
                < Bubble
                    arrowPointAtCenter
                    placement="top"
                    error
                    content={"账号未授权"}
                >
                    {isAppidAuthorized === false && <Icon type="error" style={{ marginLeft: 1, marginBottom: 2 }} />}
                </Bubble>
            </Bubble>
        </div>
    )
}
