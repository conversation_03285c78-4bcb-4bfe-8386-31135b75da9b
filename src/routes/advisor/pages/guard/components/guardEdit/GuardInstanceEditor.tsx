import React, { useState, useEffect, useMemo, useRef, useCallback } from 'react';
import { AUTO_SAVE_TIME } from "@src/utils/constants";
import { StatusTip, Table, Justify, Button, SearchBox, Card, Layout, message as tips, message, Modal, PopConfirm } from '@tencent/tea-component';
import { DatePicker, Form } from "@tencent/tea-component";
import { Tabs, TabPanel, Switch } from "@tencent/tea-component";
import { Bubble } from "@tencent/tea-component";
const { RangePicker } = DatePicker;
const { Body, Content } = Layout;
const { selectable, scrollable, removeable, pageable, injectable } = Table.addons;
import {
    DescribeGuardProductPolicy,
    DescribeGuardSheet,
    modifyGuardInstances,
    BindGuardInstance,
    DescribeGuardNoRegionProduct,
    BindGuardSheet
} from '@src/api/advisor/guard';
import { CustomerName } from '../CustomerName';
import { Filter, GuardParams, InstanceItem, ModifyGuardInstancesParams, ModifiedProductItem, policyItem, ProductDescItem, ProductTemplateItem, ProductPolicyItem } from '@src/types/advisor/guard';
import _ from 'lodash'
import { Resource } from '../Resource';
import { useHistory } from '@tea/app';
import { getUserInfo } from '@src/api/common';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { getInstances } from '@src/api/advisor/estimate';
import Loading from '../Loading';
import moment from 'moment';
import uuid from "react-uuid";
import { GuardContext } from "@src/routes/advisor/pages/guard/components/state/GuardContext";

interface Props {
    guardId: string,
    handleUpdateSteps: Function,
    handleUpdateStepsDisable: Function,
    handleUpdateCurrentGuard: Function,
    handleGetGuardSheet: Function,
}


function arrayContainObject(obj, list) {
    for (let i = 0; i < list.length; i++) {
        if (_.isEqual(list[i], obj)) {
            return true;
        }
    }

    return false;
}
// 自动保存定时器标识
let autoSaveTimer = null;
let timerOut;
const Limit = 1000
const TipLimit = 1000
export function GuardInstanceEditor(match) {
    const history = useHistory();
    //护航单id
    const [guardId, setGuardId] = useState<number>(Number(match.match.params.guardid) || 0)
    //客户APPID
    const [mainAppId, setMainAppId] = useState<number>(0)
    //整体放量预估天数  ExpectedEnlargeDays
    const [expectedEnlargeDays, setExpectedEnlargeDays] = useState<number>(7)
    //整体放量预估倍数 ExpectedEnlargeTimes
    const [expectedEnlargeTimes, setExpectedEnlargeTimes] = useState<number>(1.5)
    //护航状态
    const [status, setStatus] = useState<number>(0)

    //护航实例信息
    const [instanceTemplate, setInstanceTemplate] = useState([])
    //当前护航实例信息
    const [currentInstanceTemplate, setCurrentInstanceTemplate] = useState([])
    //护航实例dict
    const [instanceTemplateDict, setInstanceTemplateDict] = useState<Map<string, Array<InstanceItem>>>({} as Map<string, Array<InstanceItem>>)
    //原来护航实例dict
    const [sourceInstanceTemplateDict, setSourceInstanceTemplateDict] = useState({})
    //护航产品策略信息
    const [productTemplate, setProductTemplate] = useState<Array<ProductTemplateItem>>([])
    //当前护航产品策略信息
    const [currentProductTemplate, setCurrentProductTemplate] = useState<Array<ProductTemplateItem>>([])
    //护航产品策略值dict
    const [productTemplateDict, setProductTemplateDict] = useState({})
    //产品维度 护航策略
    const [productPolicyDict, setProductPolicyDict] = useState<any>({})

    //当前打开appid
    const [currentAppId, setCurrentAppId] = useState<string>('')
    //产品描述信息
    const [productDesc, setProductDesc] = useState<Array<ProductDescItem>>([])
    //当前产品描述信息
    const [currentProductDesc, setCurrentProductDesc] = useState<Array<ProductDescItem>>([])
    //加载状态
    const [loading, setLoading] = useState<boolean>(false)
    //当前选择产品字典
    const [currentProductDescDict, setCurrentProductDescDict] = useState({})
    //当前所有选择产品
    const [selectedProducts, setSelectedProducts] = useState<Array<string>>([])
    //原来所有选择产品
    const [sourceProducts, setSourceProducts] = useState<Array<string>>([])
    //兜底产品列表
    const [unSupportedProducts, setUnSupportedProducts] = useState<Array<string>>([])
    //增删产品
    const [addOrDelProducts, setAddOrDelProducts] = useState<Array<string>>([])
    //修改产品(选择实例发生变更)
    const [modifiedProducts, setModifiedProducts] = useState<Array<ModifiedProductItem>>([])
    //授权账号、非兜底产品未勾选实例
    const [existProductWithoutInstSelected, setExistProductWithoutInstSelected] = useState<boolean>(false)
    //全部都是兜底产品，无论是否授权账号
    const [existProductAllUnauthorized, setExistProductAllUnauthorized] = useState<boolean>(false)
    //实例模板ID
    const [templateId, setTemplateId] = useState<string>('')

    //全部的appid列表
    const [appids, setappids] = useState<Array<number>>([])
    //实例维度 护航容量策略
    const [instancePolicyDict, setInstancePolicyDict] = useState<any>({})
    //实例维度 护航容量策略是否查询完毕 --避免子组件重复刷新
    const [instancePolicyGot, setInstancePolicyGot] = useState<boolean>(false)
    //开启非空校验 CustomerName组件
    const [need, setNeed] = useState<boolean>(true)
    //未授权appid列表
    const [appIdNAList, setAppIdNAList] = useState<Array<number>>([])
    //已驳回产品
    const [rejectedProducts, setRejectedProducts] = useState<Array<string>>([])

    //当前护航单信息
    const [currentGuard, setCurrentGuard] = useState<GuardParams>({})
    //护航单参数
    const [params, setParams] = useState<GuardParams>({})
    //直接URL访问，guardId为空，弹窗提醒
    const [visibleDirectVisitModal, setVisibleDirectVisitModal] = useState<boolean>(false)
    //当前Tab列表
    // const [currentTabs, setCurrentTabs] = useState<Array<{ id: string, label: React.ReactNode }>>([])
    //审批人列表
    const [approverList, setApproverList] = useState<Array<string>>([])
    // 无地域的产品
    const [noRegionProduct, setNoRegionProduct] =useState(null)
    const onNoteId = 1               // 草稿状态ID
    const onRunningId = 32           // 正在巡检中状态ID
    const instanceAltering = 40      // 实例修改中ID
    const instanceAlteredRun = 41    // 实例修改后运行中ID

    //当前登录rtx
    const [rtx, setRtx] = useState<string>('')
    // 全屏loading显示
    const [showLoading, setShowLoading] = useState(false)
    // 已经返回的数量
    const [returnNum, setRetunNum] = useState(0)
    // 全屏loading显示文字
    const [loadingText, setLoadingText] = useState('加载中...')

    // 记录 currentInstanceTemplate 最新引用
    const currentInstanceTemplateRef = useRef([]);
    // 记录 currentInstanceTemplate 最新引用
    const currentGuardRef = useRef({});
    // 自动保存loading
	const [autoSaveLoading, setAutoSaveLoading] = useState<boolean>(false);
    // 选择的架构图信息
	const [archInfoList, setArchInfoList] = useState<any>([]);


    //获取当前登录账号rtx
    const getCurrentOperator = async () => {
        try {
            const res = await getUserInfo()
            setRtx(res.data.EngName || '')
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //查询护航单
    const getGuardSheet = async (Offset, Limit) => {
        setLoading(true)
        try {
            let filters: Array<Filter> = [
                { Name: 'guard_id', Values: [match.match.params.guardid] },
            ]
            const res: any = await DescribeGuardSheet({
                Filters: filters.filter(i => { if (i.Values.length) { return i } }),
                Offset: Offset,
                Limit: Limit,
                AppId: 1253985742,
            })
            setLoading(false)
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                // 指定 guardId 过滤，仅返回一条记录
                if (res.TotalCount != 1) {
                    tips.error({ content: "返回记录数据数量错误 " + res.TotalCount + "，请联系管理员确认" });
                    return
                }

                let guardInfo = res?.Guard?.[0] || {};
                // 查询localStorage中是否有自动保存的数据
                const autoSaveGuard = localStorage.getItem('autoSaveGuard') ? JSON.parse(localStorage.getItem('autoSaveGuard')) : null;
                let isLoad = false;
                if (autoSaveGuard && autoSaveGuard?.guard?.GuardId === guardInfo.GuardId) {
                    isLoad = await Modal.confirm({
                        message: "有未提交的草稿，是否恢复？",
                        okText: "确认",
                        cancelText: "取消",
                        size: 's',
                        disableCloseIcon: true
                      });
                    if (isLoad) {
                        guardInfo = autoSaveGuard?.guard || {};
                    }
                }

                setCurrentGuard(guardInfo);
                setParams(guardInfo);

                // 获取每个产品下的选择的实例
                // eslint-disable-next-line max-len
                await getAllProductInstances(res.Guard[0].ProductDesc || [], res.Guard[0].InstanceTemplateCount || {}, isLoad);

                updateCurrentValue(guardInfo);

                getGuardProductPolicy(guardInfo.MainAppId);

                // 开启自动保存
                autoSaveTimer = setInterval(() => {
                    autoSave();
                }, AUTO_SAVE_TIME);
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }
    // 分页查询产品下的实例
    async function getPageInstances(pageSize = 1, appid, product) {
        try {
            const params = {
                AppId: appid,
                GuardId: guardId,
                Offset: (pageSize - 1) * Limit,
                Limit: Limit,
                Products: [product]
            }
            const res = await getInstances(params)
            if (res.Error) {
                return Promise.reject(res.Error)
            } else {
                return Promise.resolve(res.Instance || [])
            }
        } catch (err) {
            return Promise.reject(err)
        }
    }

    // 获取所有产品的实例
    async function getAllProductInstances(ProductDesc, instanceMap, isLoad = false) {
        setLoadingText('加载中...')
        setShowLoading(true)
        if (ProductDesc.length) {
            let resultList = []
            ProductDesc.forEach((i) => {
                if (!i.InstanceIds) {
                    // 单个产品的实例总数
                    const instancesNum = instanceMap?.[i.AppId]?.[i.Product] || 0
                    // 单个产品的实例总页数
                    const pages = Math.ceil(instancesNum / Limit)
                    for (let j = 1; j <= pages; j++) {
                        setTimeout(() => {
                            resultList.push(getPageInstances(j, i.AppId, i.Product))
                        }, 100);
                    }
                }
            });
            setTimeout(async () => {
                await Promise.all(resultList).then(res => {
                    if (res?.length) {
                        // 是否有某次查询没有返回值
                        const hasErr = res.some(i => i.length === 0)
                        if (hasErr) {
                            message.error({
                                content: "返回数据有误，请刷新页面",
                            })
                            setShowLoading(false)
                            return;
                        }
                        // @ts-ignore
                        const instances = res.flat() || [];

                        const savedInstances = localStorage.getItem('autoSaveInstances') ? JSON.parse(localStorage.getItem('autoSaveInstances'))?.instances : [];
                        // 加载缓存实例还是护航单的实例
                        setInstanceTemplate(isLoad ? savedInstances : instances);

                        // 获取产品对应实例
                        const tmpInstDict = {};
                        instances.map(i => {
                            const key = `${i.AppId}|${i.Product}`;
                            if (!tmpInstDict[key]) {
                                tmpInstDict[key] = [];
                            }
                            tmpInstDict[key].push(i);
                        });
                        setSourceInstanceTemplateDict(tmpInstDict);
                        setCurrentInstanceTemplate(isLoad ? savedInstances : instances);
                    }
                    setShowLoading(false)
                }).catch(err => {
                    console.log(err);
                    message.error({
                        content: "返回数据有误，请刷新页面",
                    })
                    setShowLoading(false)
                })
            }, 200);
        } else {
            setShowLoading(false)
        }
    }
    function updateCurrentValue(currentGuard: GuardParams) {

        setMainAppId(currentGuard.MainAppId > 0 ? currentGuard.MainAppId : 0)
        setStatus(currentGuard.Status)
        setExpectedEnlargeDays(currentGuard.ExpectedEnlargeDays)
        setExpectedEnlargeTimes(currentGuard.ExpectedEnlargeTimes)
        setTemplateId(currentGuard.TemplateId)
        setCurrentAppId(currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : '')
        setProductDesc(currentGuard.ProductDesc)
        setCurrentProductDesc(currentGuard.ProductDesc)
        setSourceProducts(currentGuard.Products || [])
        setRejectedProducts(currentGuard.RejectedProducts || [])

        setProductTemplate(currentGuard.ProductTemplate || [])
        setCurrentProductTemplate(currentGuard.ProductTemplate || [])

        //获取APPID列表
        let tmpAppids: Array<number> = []
        tmpAppids.push(currentGuard.MainAppId)
        currentGuard.RelatedAppId.map(i => {
            if (tmpAppids.indexOf(i) === -1) {
                tmpAppids.push(i)
            }
        })
        setappids(tmpAppids)

        //获取审批人、建单人列表
        let instanceEditor = getInstanceEditor(currentGuard)
        setApproverList(instanceEditor)


        //获取当前Tab页
        // let tmpTabs: Array<{ id: string, label: React.ReactNode }> = []
        // tmpAppids.map(i => {
        //     tmpTabs.push({
        //         id: i.toString(),
        //         label: <CustomerName appid={i} need={need} instanceTemplate={currentInstanceTemplate} handleUpdateAppidNA={(i) => { updateAppidNA(i) }} />
        //     })
        // })
        // setCurrentTabs(tmpTabs)
    }
	const tabs: Array<{ id: string, label: React.ReactNode }> = useMemo(() => {
		let tmp: Array<{ id: string, label: React.ReactNode }> = []
		appids.map(i => {
			tmp.push({
				id: i.toString(),
				label: <CustomerName appid={i} need={need} instanceTemplate={currentInstanceTemplate} handleUpdateAppidNA={(i) => { updateAppidNA(i) }} />
			})
		})
		return tmp
	}, [appids, need, currentInstanceTemplate]);
    useEffect(() => {
        let totalNum = filterInstancesNoSetProduct(currentInstanceTemplate).length
        if (totalNum >= TipLimit) {
            setLoadingText(`护航实例较多，保存进度 ${returnNum} / ${totalNum}`)
        }
    }, [returnNum])
    // 分页保存所有实例的函数
    async function savePageInstances(Timestamp, TemplateId, InstanceTemplate) {
        try {
            const res = await BindGuardInstance(
                {
                    GuardId: currentGuard.GuardId,
                    AppId: currentGuard.MainAppId || 1253985742,
                    Timestamp,
                    TemplateId,
                    InstanceTemplate
                })
            if (res.Error) {
                return Promise.reject(res.Error)
            } else {
                setRetunNum((val) => {
                    return val + (InstanceTemplate?.length || 0)
                })
                return Promise.resolve(res)
            }
        } catch (err) {
            return Promise.reject(err)
        }
    }

    	// 账号下架构图选择
	function archInfoChange(archInfo, appId) {
		setArchInfoList((oldValue) => {
			const oldList = _.cloneDeep(oldValue || []);
			// 在已选择的架构图列表中找到对应appId的数据
			const index = oldList.findIndex(item => item.AppId === appId);
			if (index === -1) {
				if (archInfo) {
					oldList.push({ ...archInfo, AppId: appId });
				}
			} else {
				if (archInfo) {
					oldList[index] = { ...archInfo, AppId: appId };
				} else {
					oldList.splice(index, 1);
				}
			}
			return [...oldList];
		});
	}

    const onBindGuardSheet = useCallback(async () => {
      const params = {
				MainAppId: parseInt(currentGuard.MainAppId.toString(), 10),
				GuardID: guardId || 0,
				CustomerName: currentGuard.CustomerName || '',
				GuardName: `${currentGuard.CustomerName}护航需求${moment().format('YYYYMMDDHH')}`,
				StartTime: moment().add(2, 'hours')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
			  	EndTime: moment().add(1, 'days')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
                OtherPlatforms: (archInfoList || []).map(i => ({
                    Platform: i?.GuardCreateSource || 'ISA',
                    PlatformUniqueId: i?.ArchId,
                    AppId: i?.AppId,
                })),
				OpsType: 'add',
			};
			const res = await BindGuardSheet(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
    }, [archInfoList]);
    // 保存护航单
    const saveGuard = async () => {
        setRetunNum(0)
        setLoadingText('护航实例保存中...')
        // 显示加载状态
        setShowLoading(true)
        // 总页数
        let newInstanceTemplate = filterInstancesNoSetProduct(currentInstanceTemplate)
        const pages = Math.ceil(newInstanceTemplate.length / Limit)
        // 当前时间戳
        const Timestamp = moment().valueOf()
        const TemplateId = uuid()
        let resultList = []
        for (let i = 1; i <= pages; i++) {
            setTimeout(() => {
                // 实例起止下标
                let startIndex = (i - 1) * Limit
                let endIndex = ((i - 1) * Limit) + Limit
                let data = newInstanceTemplate.slice(startIndex, endIndex)
                resultList.push(savePageInstances(Timestamp, TemplateId, data))
            }, 100);
        }
        if (pages == 0) {
            // 没有选择兜底产品或者是兜底账号 直接调用modifyGuard保存
            modifyGuard(TemplateId).then(saveRes => {
                setShowLoading(false)
                message.success({ content: guardId ? "护航资源修改成功！如有修改产品实例，则巡检任务结束后将支持新的巡检报告和监控面板等内容。" : "护航单创建成功!" })
                history.push("/advisor/guard/summary/" + guardId)
            }).catch(err => {
                console.log(err);
                message.error({
                    content: "护航单实例信息修改失败，请刷新重试",
                })
                setShowLoading(false)
            })
        } else {
            setTimeout(async () => {
                await Promise.all([...resultList]).then(async res => {
                    if (res && res.length) {
                        // 是否有某次查询没有返回值
                        const hasErr = res.some(i => i.length === 0)
                        if (hasErr) {
                            message.error({
                                content: "当前绑定实例过多，请重试，或者减少绑定的实例数量",
                            })
                            setShowLoading(false)
                            return
                        }
                        modifyGuard(TemplateId).then(saveRes => {
                            setShowLoading(false)
                            message.success({ content: guardId ? "护航资源修改成功！如有修改产品实例，则巡检任务结束后将支持新的巡检报告和监控面板等内容。" : "护航单创建成功!" })
                            history.push("/advisor/guard/summary/" + guardId)
                        }).catch(err => {
                            return Promise.reject(err)
                        })
                    } else {
                        setShowLoading(false)
                    }
                }).catch(err => {
                    console.log(err);
                    message.error({
                        content: "当前绑定实例过多，请重试，或者减少绑定的实例数量",
                    })
                    setShowLoading(false)
                })
            }, 100);
        }
        onBindGuardSheet();
    }

    //修改护航单实例
    const modifyGuard = async (TemplateId) => {
        let modifiedPolicyProducts = getModifiedPolicyProducts(currentProductTemplate)
        try {
            let params: ModifyGuardInstancesParams = {
                TemplateId: TemplateId,
                GuardId: guardId,
                AppId: mainAppId,
                ModifiedProduct: modifiedProducts,             // 修改了选择实例的产品
                ModifiedPolicyProduct: modifiedPolicyProducts, // 修改了产品策略的产品
                ProductDesc: currentProductDesc,
                // InstanceTemplate: newInstanceTemplate,
                ProductTemplate: currentProductTemplate,
                Operator: rtx,
            }

            const res = await modifyGuardInstances(params)
            if (res.Error) {
                return Promise.reject(res.Error)
            } else {
                const autoSaveGuard = localStorage.getItem('autoSaveGuard') ? JSON.parse(localStorage.getItem('autoSaveGuard')) : null;
                if (autoSaveGuard?.guard?.GuardId === currentGuard.GuardId) {
                    // 提交成功之后清空缓存数据
                    localStorage.setItem('autoSaveGuard', null);
                    localStorage.setItem('autoSaveInstances', null);
                }
                return Promise.resolve(res)
            }
        } catch (err) {
            return Promise.reject(err)
        }
    }

    function filterInstancesNoSetProduct(currentInstanceTemplate: Array<InstanceItem>) {
        let tmpInstList = []
        currentProductDesc.map(i => {
            currentInstanceTemplate.map(ins => {
                if (i.Product == ins.Product) {
                    tmpInstList.push(ins)
                }
            })
        })
        return tmpInstList
    }

    // 获取修改了产品粒度策略的产品
    function getModifiedPolicyProducts(cpTemplate: Array<ProductTemplateItem>) {
        let tmp = []
        cpTemplate.map(i => {
            let item: ModifiedProductItem = {
                AppId: i.AppId,
                Product: i.Product
            }
            tmp.push(item)
        })
        return tmp
    }

    //查询是否有修改护航单实例权限
    function isInstanceModifyAllowed() {
        let stateAllowed = true
        let guysAllowed = true

        //草稿状态和计算状态不允许修改
        if (status <= onNoteId ||
            [onRunningId, instanceAltering, instanceAlteredRun].includes(status)) {
            stateAllowed = false
        }
        //护航负责人审批状态，允许修改。该状态较特殊
        if (onRunningId == status && !currentGuard.Approvals.AfterSalesStatus.IsConfirm) {
            stateAllowed = true
        }

        //有权限修改的人员
        if (approverList.indexOf(rtx) == -1) {
            guysAllowed = false
        }

        return stateAllowed && guysAllowed
    }

    //查询是有修改护航单实例的人员：建单人、改单人、APPID负责人、审批人及售后指派人
    function getInstanceEditor(currentGuard: GuardParams) {
        let guys = []

        guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Handler.split(";"))
        guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Supporter.split(";"))
        if (currentGuard.Approvals.ExpertStatus) {
            currentGuard.Approvals.ExpertStatus.map(i => {
                guys = guys.concat(i.Handler.split(";"))
            })
        }
        if (currentGuard.Approvals.ScanResultStatus) {
            currentGuard.Approvals.ScanResultStatus.map(i => {
                guys = guys.concat(i.Handler.split(";"))
            })
        }

        guys = guys.concat(currentGuard.CreatedBy.trim())
        guys = guys.concat(currentGuard.UpdatedBy.trim())
        guys = guys.concat(currentGuard.Responser.split(";"))

        return Array.from(new Set(guys)).filter(i => { return i != "" });
    }

    //获取容量策略
    const getGuardProductPolicy = async (value) => {
        try {
            const res = await DescribeGuardProductPolicy(
                {
                    AppId: parseInt(value)
                })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setProductPolicyDict(res.ProductPolicy)
                setInstancePolicyDict(res.InstancePolicy)
                setInstancePolicyGot(true)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //获取云产品清单
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: 1253985742, //公共参数
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setUnSupportedProducts(res.UnSupportedProducts)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //更新未授权的appid
    function updateAppidNA(appidParams) {
        if (appidParams) {
            setAppIdNAList(i => i.concat([appidParams]))
        }
    }

    //当前产品描述信息
    function updateProductDesc() {
        let copyProductDesc = _.cloneDeep(currentProductDesc)
        let tmpProducts = []

        //无论有没勾选实例的产品
        copyProductDesc.map(k => {
            if (tmpProducts.indexOf(k.Product) === -1) {
                tmpProducts.push(k.Product)
            }
        })
        setSelectedProducts(tmpProducts)
    }

    // 检查非兜底账号、非兜底产品是否没有勾选实例
    function checkProductWithoutInstance() {
        let selectProducts = []                  //已选产品：非兜底
        let selectProductsWithInst = []          //已择产品：非兜底、有选择实例
        let selectProductsWithoutInstance = []   //已择产品：非兜底、没选实例
        for (let key in currentProductDescDict) {
            // key is in "appid|product" way
            if (!key.includes('|')) {
                continue
            }
            let appid = key.split('|')[0]
            let product = key.split('|')[1]
            if (appIdNAList.includes(parseInt(appid))) {
                //忽略兜底账号
                continue
            }
            if (unSupportedProducts.includes(product)) {
                //忽略兜底产品
                continue
            }
            selectProducts.push(key)
        }

        for (let key in instanceTemplateDict) {
            if (!key.includes('|')) {
                continue
            }
            let appid = key.split('|')[0]
            let product = key.split('|')[1]
            if (appIdNAList.includes(parseInt(appid))) {
                continue
            }
            if (unSupportedProducts.includes(product)) {
                continue
            }
            if (instanceTemplateDict[key].length == 0) {
                continue
            }
            selectProductsWithInst.push(key)
        }

        selectProducts.map(i => {
            if (!selectProductsWithInst.includes(i)) {
                selectProductsWithoutInstance.push(i)
            }
        })

        if (selectProductsWithoutInstance.length > 0) {
            setExistProductWithoutInstSelected(true)
        } else {
            setExistProductWithoutInstSelected(false)
        }
    }

    //监听expectedEnlargeDays，同步刷新currentInstanceTemplate
    useEffect(() => {
        let tmp: Array<InstanceItem> = _.cloneDeep(currentInstanceTemplate)
        tmp.map((i, index) => {
            let policy: Array<policyItem> = _.cloneDeep(i.Policy)
            policy.map((j, index1) => {
                policy[index1].Days = expectedEnlargeDays
            })
            tmp[index].Policy = policy
        })
        setCurrentInstanceTemplate(tmp)
    }, [expectedEnlargeDays])

    //监听instanceTemplate，初始化生成instanceTemplateDict
    /* 该监听似未生效，setInstanceTemplateDict()每次拿取并不实时，旧代码逻辑暂保留。*/
    useEffect(() => {
        let tmp = {} as Map<string, Array<InstanceItem>>
        if (appids.length && instanceTemplate.length) {
            appids.map(appid => {
                let tmpList = []
                instanceTemplate.map(ins => {
                    if (tmpList.indexOf(ins.Product) === -1 && ins.AppId === appid) {
                        tmpList.push(ins.Product)
                        let key = ins.AppId + "|" + ins.Product
                        tmp[key] = (instanceTemplate || []).filter(j => { if (j.AppId === appid && j.Product === ins.Product) { return j } })
                    }
                })
            })
        }
        setInstanceTemplateDict(tmp)
    }, [appids, instanceTemplate])

    //监听productTemplate 初始化生成productTemplateDict
    useEffect(() => {
        let tmp = {}
        if (appids.length && productTemplate.length) {
            appids.map(item => {
                let l = []
                productTemplate.map(i => {
                    if (l.indexOf(i.Product) === -1 && i.AppId === item) {
                        l.push(i.Product)
                        let key = i.AppId + "^" + i.Product
                        tmp[key] = i
                    }
                })
            })
        }
        setProductTemplateDict(tmp)
    }, [appids, productTemplate])
	const [productsInResourceAll, setProductsInResourceAll] = useState({});
    //监听instanceTemplateDict，刷新currentInstanceTemplate
    useEffect(() => {
        // let l = []
        // for (let i in instanceTemplateDict) {
        //     l = l.concat(instanceTemplateDict[i])
        // }
        // setCurrentInstanceTemplate(l)
		const l = [];
		for (const i in instanceTemplateDict) {
			if (instanceTemplateDict[i].length > 0) {
				instanceTemplateDict[i].forEach((item) => {
					l.push(_.cloneDeep(item));
				})
			}
		}
		l.forEach((item, j) => {
			let contain = false;
			let isSame = false;
			if (productsInResourceAll[item.AppId] != undefined) {
				isSame = true;
			}
			productsInResourceAll[item.AppId]?.forEach((el) => {
				if (item.Product == el.Product) {
					contain = true;
				}
			});
			if ((!contain && isSame || productsInResourceAll[item.AppId]?.length == 0)) {
				l.splice(j, 1, null);
			}
		});
		setCurrentInstanceTemplate(l.filter((item) => { return item !== null }))
    }, [instanceTemplateDict, productsInResourceAll])

    //监听productTemplateDict 刷新currentProductTemplate
    useEffect(() => {
        let l = []
        for (let i in productTemplateDict) {
            l = l.concat(productTemplateDict[i])
        }
        setCurrentProductTemplate(l)
    }, [productTemplateDict])

    //监听勾选实例，变更对应产品
    useEffect(() => {
        //1)更新产品描述字段
        updateProductDesc()
        //2)判断：非兜底账号、非兜底产品没有勾选实例。
        checkProductWithoutInstance()
    }, [instanceTemplateDict, currentProductDescDict, appIdNAList, unSupportedProducts])

    //监听勾选产品，变更对应实例。如删除产品，对应实例列表也要更新。
    // useEffect(() => {
    //     let tmpInstList = []
    //     currentProductDesc.map(i => {
    //         instanceTemplate.map(ins => {
    //             if (i.Product == ins.Product) {
    //                 tmpInstList.push(ins)
    //             }
    //         })
    //     })
    //     setInstanceTemplate(tmpInstList)
    //     /* InstanceTemplateDict(B)监听InstanceTemplate(A)变更、CurrentInstanceTemplate(C)监听B变更，最后将C传给CGI变更*/
    // }, [currentProductDesc])

    //子组件刷新 instanceTemplate，传递实例是否被选择的信息，不会传递产品是否被选的信息
    function updateInstanceTemplateDict(appid, product, records) {
        let tmp = _.cloneDeep(instanceTemplateDict)
        let key = appid + '|' + product
        tmp[key] = records
        setInstanceTemplateDict(tmp)
    }

    //子组件刷新 productTemplate
    function updateDetailProductTemplateDict(appid: string, product: string, records: Array<ProductPolicyItem>) {
        let tmp = _.cloneDeep(productTemplateDict)
        let key = appid + '^' + product
        let item: ProductTemplateItem = {
            AppId: Number(appid),
            Product: product,
            Regions: [],
            Policy: records,
        }
        tmp[key] = item
        setProductTemplateDict(tmp)
    }

	const [fieldInfo, setFieldInfo] = useState({
		appid: '',
		val: ''
	});
	const [validateResult, setValidateResult] = useState([])
	const [oneFieldInfo, setOneFieldInfo] = useState(null);
	const [providerVal, setProviderVal] = useState({
		onlyConatainUnSupportProduct: {},
		unSupportProValidate: {},
		field: {},
		changeFieldVal: (appid, val) => {
			setFieldInfo({
				appid,
				val
			});
		},
		changeOneField: (field) => {
			setOneFieldInfo(_.cloneDeep(field));
		},
		currentAppId: currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : '',
		// 保存所有实例的校验信息
		validateChange: (validateInfo) => {
			if (validateInfo.Product) {
				setValidateResult((list) => {
					// 校验信息中是否有当前校验信息
					let index = list.findIndex(i => (i.InstanceId === validateInfo.InstanceId && i.MetricName === validateInfo.MetricName))
					if (index === -1) {
						list.push(validateInfo)
					} else {
						list[index] = validateInfo
					}
					return list || []
				})
			}
		}
	});
	useMemo(() => {
		if (!timerOut) {
			timerOut = setTimeout(()=>{
				providerVal['validateResult'] = validateResult;
				setProviderVal(_.cloneDeep(providerVal));
				clearTimeout(timerOut);
				timerOut = null;
			}, 20);
		}
	}, [validateResult]);
	useMemo(() => {
		if (currentGuard) {
			providerVal['currentAppId'] = currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : '';
			setProviderVal(_.cloneDeep(providerVal));
		}
	}, [currentGuard]);
	useMemo(() => {
		if (fieldInfo.appid) {
			providerVal['field'][fieldInfo.appid] = fieldInfo.val;
			setProviderVal(_.cloneDeep(providerVal));
		}
	}, [fieldInfo]);
	useMemo(() => {
		if (oneFieldInfo) {
			providerVal['field'] = _.cloneDeep(oneFieldInfo);
			setProviderVal(_.cloneDeep(providerVal));
		}
	}, [oneFieldInfo]);
	useMemo(() => {
		if (productTemplate?.length > 0) {
			productTemplate.forEach((item) => {
				let containProduct = false;
				currentProductDesc.forEach((el) => {
					if (item.AppId === el.AppId && el.Product === item.Product) {
						containProduct = true;
					}
				})
				if (!containProduct) {
					return;
				}
				if (providerVal['field'][item.AppId] == undefined) {
					providerVal['field'][item.AppId] = {};
					providerVal['field'][item.AppId][item.Product] = {};
				} else {
					providerVal['field'][item.AppId][item.Product] = {};
				}
				if (item.Policy?.length > 0 && currentGuard.Products?.includes(item.Product)) {
					item.Policy.forEach((el) => {
						if (el.IsRequired && currentGuard.Standard !== 3) {
							// providerVal['field'][item.AppId][item.Product][el.MetricName] = el.Value ? false : 'init';
							providerVal['field'][item.AppId][item.Product][el.MetricName] = el.Value ? false : true;
						}
					});
				};
			});
			setProviderVal(_.cloneDeep(providerVal));
		}
	}, [productTemplate, currentGuard]);

	useMemo(() => {
		for (const appId in productsInResourceAll) {
			if (providerVal['unSupportProValidate'][appId] == undefined) {
				providerVal['unSupportProValidate'][appId] = {};
			}
			if (providerVal['field'][appId] == undefined) {
				providerVal['field'][appId] = {};
			}
			const currentUnsupportProduct = [];
			let num = 0;
			productsInResourceAll[appId]?.map(i => {
				if (unSupportedProducts.includes(i.Product) || appIdNAList.includes(i.AppId)) {
					num++;
					if (providerVal['unSupportProValidate'][i.AppId]?.[i.Product] == undefined && i.InstanceIds) {
						providerVal['unSupportProValidate'][i.AppId][i.Product] = false;
					} else if (providerVal['unSupportProValidate'][i.AppId]?.[i.Product] == undefined) {
						// providerVal['unSupportProValidate'][i.AppId][i.Product] = 'init';
						providerVal['unSupportProValidate'][i.AppId][i.Product] = true;
					} else if (providerVal['unSupportProValidate'][i.AppId]?.[i.Product] == 'init' && !i.InstanceIds) {
						// providerVal['unSupportProValidate'][i.AppId][i.Product] = 'init';
						providerVal['unSupportProValidate'][i.AppId][i.Product] = true;
					} else if (providerVal['unSupportProValidate'][i.AppId]?.[i.Product] === false && i.InstanceIds == undefined) {
						providerVal['unSupportProValidate'][i.AppId][i.Product] = false;
					} else {
						providerVal['unSupportProValidate'][i.AppId][i.Product] = i.InstanceIds ? false : true;
					}
					currentUnsupportProduct.push(i.Product);
				}
			});
			for (const key in providerVal['unSupportProValidate'][appId]) {
				if (!currentUnsupportProduct.includes(key)) {
					providerVal['unSupportProValidate'][appId][key] = undefined;
				}
			}
			if (productsInResourceAll?.[appId]?.length == num && num > 0) {
				providerVal['onlyConatainUnSupportProduct'][appId] = true;
			} else {
				providerVal['onlyConatainUnSupportProduct'][appId] = false;
			}
		}
		setProviderVal(_.cloneDeep(providerVal));
	}, [unSupportedProducts, productsInResourceAll, appids, appIdNAList]);

    //子组件(APPID粒度)刷新 ProductDesc，传递产品是否被选择的信息
    function handleUpdateProductDescInDetail(appid: number, productsInResource: Array<ProductDescItem>) {
        //1)重置覆盖该APPID的产品选项
		const desc = _.cloneDeep(currentProductDesc.filter(i => { return appid == i.AppId }));
		const newProductsInResource = [];
		//1)重置覆盖该APPID的产品选项
		let tmp = _.cloneDeep(currentProductDesc.filter(i => { return appid != i.AppId }));
		productsInResource.map(i => {
			let obj = null;
			desc.forEach((item) => {
				if (i.Product == item.Product) {
					obj = {
						...item,
						InstanceIds: i.InstanceIds !== undefined ? i.InstanceIds : item.InstanceIds,
						Comment: i.Comment !== undefined ? i.Comment : item.Comment,
					};
				};
			});

			if (obj) {
				newProductsInResource.push(_.cloneDeep(obj));
				tmp.push(_.cloneDeep(obj));
			} else {
				newProductsInResource.push({
					AppId: appid,
					Product: i.Product,
					InstanceIds: i.InstanceIds,
					Comment: i.Comment,
				});
				tmp.push({
					AppId: appid,
					Product: i.Product,
					InstanceIds: i.InstanceIds,
					Comment: i.Comment,
				});
			}
		});

		productsInResourceAll[appid] = newProductsInResource;
		if (productDesc?.length > 0) {
			productDesc.forEach((item) => {
				if (productsInResourceAll[item.AppId] === undefined) {
					productsInResourceAll[item.AppId] = [_.cloneDeep(item)];
				} else {
					let had = false;
					productsInResourceAll[item.AppId]?.forEach((el) => {
						if (el.Product == item.Product) {
							had = true;
						}
					});
					if (!had) {
						productsInResourceAll[item.AppId].push(_.cloneDeep(item));
					}
				}
			});
			setProductDesc([]);
		}
		setProductsInResourceAll(_.cloneDeep(productsInResourceAll));

        setCurrentProductDesc(tmp)

        //2)更新当前产品描述信息
        let pd = {}
        tmp.map(i => {
            pd[i.AppId + "|" + i.Product] = {}
        })
        setCurrentProductDescDict(pd) // useEffect: productDescDict -> updateProductDesc() ->

        //3)如果删除产品，对应的实例信息也清除。
        /*放在监听CurrentProductDesc实现，父子组件交互内容过多易崩*/

        //4)全部都是兜底产品
        let flag = true
        tmp.map(i => {
            if (!unSupportedProducts.includes(i.Product)) {
                flag = false
            }
        })
        if (tmp.length > 0) {
            setExistProductAllUnauthorized(flag)
        }
    }

    //监听增删产品
    useEffect(() => {
        let tmp = []
        //新增产品
        selectedProducts.map(i => {
            if (sourceProducts.indexOf(i) == -1 && tmp.indexOf(i) == -1) {
                tmp.push(i)
            }
        })
        //删除产品
        sourceProducts.map(i => {
            if (selectedProducts.indexOf(i) == -1 && tmp.indexOf(i) == -1) {
                tmp.push(i)
            }
        })
        setAddOrDelProducts(tmp)
    }, [selectedProducts])

    //监听修改产品
    useEffect(() => {
        let tmp = []
        for (let key in instanceTemplateDict) {
            let src = (sourceInstanceTemplateDict[key] || []).sort()
            let target = (instanceTemplateDict[key] || []).sort()
            if (!key.includes('|')) {
                continue
            }
            let appid = key.split('|')[0]
            let product = key.split('|')[1]
            let obj = {
                AppId: Number(appid),
                Product: product,
            }
            if (!(_.isEqual(src, target)) && !arrayContainObject(obj, tmp)) {
                tmp.push(obj)
            }
        }
        setModifiedProducts(tmp)
    }, [instanceTemplateDict])

    // 获取无地域的产品
    const getDescribeGuardNoRegionProduct = async () => {
        try {
            const res = await DescribeGuardNoRegionProduct({ AppId: 1253985742 })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setNoRegionProduct(res.NoRegionProducts||[])
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //页面初始化
    useEffect(() => {
        getCurrentOperator();
        getDescribeGuardNoRegionProduct()
        return () => {
			autoSaveTimer && clearInterval(autoSaveTimer);
		};
    }, [])

    //页面初始化
    useEffect(() => {
        if (rtx) {
            getGuardSheet(0, 10);
            getProductsGroupsInfo();
        }
    }, [rtx])

	const checkCondition = () => {
		let flag = false
		appids.map(i => {
			const unAuthorized = appIdNAList.includes(i);
			// 判断是否勾选实例(未授权APPID允许实例为空)
			if ((currentInstanceTemplate.filter(j => { if (j.AppId === i) { return j } }).length === 0 || existProductWithoutInstSelected) && !providerVal['onlyConatainUnSupportProduct'][i]) {
				if (!unAuthorized || existProductWithoutInstSelected) {
					flag = true;
					// 允许授权APPID选择的产品全是兜底的情况下保存或下一步
					// if (providerVal['onlyConatainUnSupportProduct'][i]) {
					// 	num++;
					// }
				};
				if (unAuthorized) {
					flag = true;
				}
			}
			for (const key in providerVal.unSupportProValidate[i]) {
				if (providerVal.unSupportProValidate[i][key] == 'init' || providerVal.unSupportProValidate[i][key] === true) {
					flag = true;
					providerVal.unSupportProValidate[i][key] = true;
				}
			};
			for (const key in providerVal.field[i]) {
				const proVal = providerVal.field[i][key];
				for (const prop in proVal) {
					if (proVal[prop] == 'init' || proVal[prop] === true) {
						flag = true;
						proVal[prop] = true;
					};
				};
			};
		});
		// if (num == appids.length) {
		// 	flag = false;
		// }
		// setProviderVal(_.cloneDeep(providerVal));
		// console.log(providerVal)
		// return
		if (flag) {
			return false
		} else {
			// 找到第一个没有过校验的的实例，进行提示
			let noPass = (validateResult || []).find(i => i.PassValidate === false)
			if (noPass) {
				return false
			}
			return true
		}
	}
	// 移除指定appId，product的校验结果
	function removeValidate(val, appId, product) {
		if (val) {
			// 先判断校验中是否存在 将要移除的，避免死循环
			const judge = validateResult.find((item) => item.AppId === appId && item.Product === product)
			judge && setValidateResult(list => {
				let result = list.filter((item) => !(item.AppId === appId && item.Product === product))
				return result
			})
		}
	}

    // 自动保存信息
    function autoSave() {
        setAutoSaveLoading(true)
        // 将自动保存的实例保存到 localStorage 中
        localStorage.setItem('autoSaveGuard',JSON.stringify({operator: rtx, guard: currentGuardRef.current}));
        localStorage.setItem('autoSaveInstances',JSON.stringify({operator: rtx, instances: currentInstanceTemplateRef.current}));
        setTimeout(() => {
            setAutoSaveLoading(false)
        }, 1000);
    }

    useEffect(()=>{
         // 记录 护航单信息
         currentGuardRef.current = {
            ...currentGuard,
            ProductDesc: currentProductDesc,
            ProductTemplate: currentProductTemplate
         }
    },[currentGuard, currentProductDesc, currentProductTemplate])

	useEffect(() => {
        // 记录 currentInstanceTemplate
        currentInstanceTemplateRef.current = currentInstanceTemplate;
		setValidateResult(list => {
			// 校验信息只保存当前已选择的实例
			let result = list.filter((item) => {
				return currentInstanceTemplate.some((obj) => obj.InstanceId === item.InstanceId);
			});
			return result
		})
	}, [currentInstanceTemplate])

    return (
		<GuardContext.Provider value={
			providerVal
		}>
			<Body>
				<Content>
					<Content.Header
						showBackButton
						onBackButtonClick={() => {
                            history.go(-1);
						}}
						title="护航实例变更"></Content.Header>
					<Content.Body>
						<Card>
							<Card.Body title="资源信息收集" style={{ height: 'calc(100vh - 212px)',overflow:'scroll'}}>
								<div>
									{
										// (tabs.length > 0 && instancePolicyGot) &&
										<Tabs tabs={tabs} placement={"top"} activeId={currentAppId} onActive={(v) => { setCurrentAppId(v.id); providerVal['currentAppId'] = v.id; setProviderVal(_.cloneDeep(providerVal)); }} destroyInactiveTabPanel={false}>
											{
												appids.map(i => {
													return (
														<TabPanel key={i} id={i.toString()}>
															<Resource
                                                                isModify={true}
																removeValidate={removeValidate}
                                                                updateArchInfo={(archInfo, appId) => archInfoChange(archInfo, appId)}
																noRegionProduct={noRegionProduct}
																currentGuard={currentGuard}
																key={i}
																appid={i}
																isAppidAuthorized={!appIdNAList.includes(i)}
																productDesc={currentProductDesc}
																rejectedProducts={rejectedProducts}
																instanceTemplate={instanceTemplate}
																instancePolicyDict={instancePolicyDict}
																productTemplate={productTemplate}
																productPolicyDict={productPolicyDict}
																expectedEnlargeDays={expectedEnlargeDays}
																expectedEnlargeTimes={expectedEnlargeTimes}
																updateInstanceTemplateDict={(i, product, records) => updateInstanceTemplateDict(i, product, records)}
																updateProductTemplateDict={(i, product, records) => { updateDetailProductTemplateDict(i, product, records) }}
																updateProductDesc={(i, productsInResource) => handleUpdateProductDescInDetail(i, productsInResource)}
																currentInstanceTemplate={currentInstanceTemplate}
																guardTime={[currentGuard.StartTime, currentGuard.EndTime]}
															/>
														</TabPanel>
													)
												})
											}
										</Tabs>
									}
								</div>
							</Card.Body>
							<Card.Footer>
								<div style={{ padding: 20, textAlign: "center", position: 'relative' }}>
									<PopConfirm
										arrowPointAtCenter={true}
										title={"确认要提交修改结果吗？"}
										message={"提交后，变更实例将发起新的【巡检】与【审批】。"}
										// disabled={(agreeOption && supporter.length === 0) || (!agreeOption && reason === "")}
										footer={close => (
											<>
												<Button type={"primary"} onClick={() => { saveGuard(); close(); }}>
													确定
												</Button>
												<Button onClick={() => { close() }} type="text">{"取消"}</Button>
											</>
										)}
										placement={"top"}>
										<Bubble placement={"top"} content={"某个产品没有选择护航实例"} visible={existProductWithoutInstSelected && !existProductAllUnauthorized} error>
											<Button
												type={"primary"}
												// disabled={!isInstanceModifyAllowed() ||
												// 	(existProductWithoutInstSelected && !existProductAllUnauthorized) ||
												// 	currentProductDesc.length == 0}
												disabled={!isInstanceModifyAllowed() || !checkCondition()}
											>
												保存
											</Button>
										</Bubble>
									</PopConfirm>
									<Button type={"weak"} style={{ marginLeft: 15 }} onClick={() => { history.push('/advisor/guard'); }} >
										返回
									</Button>
                                    {autoSaveLoading && <StatusTip
										style={{ verticalAlign: 'middle', marginLeft: 10, position: 'absolute', top: 25 }}
										status='loading'
										loadingText='已保存草稿' />
									}
								</div>
							</Card.Footer>
						</Card>


						<Modal visible={visibleDirectVisitModal} caption="-" onClose={close}>
							<Modal.Body>护航ID为空</Modal.Body>
							<Modal.Footer>
								{/* <Route exact path="/advisor/guard" component={Guard} /> */}
								<Button type="primary" onClick={() => { setVisibleDirectVisitModal(false); close(); }}>
									确定
								</Button>
							</Modal.Footer>
						</Modal>
					</Content.Body>
				</Content>
				{/* 全屏遮罩 */}
				<Loading show={showLoading} text={loadingText}></Loading>
			</Body >
		</GuardContext.Provider>
    );
}
