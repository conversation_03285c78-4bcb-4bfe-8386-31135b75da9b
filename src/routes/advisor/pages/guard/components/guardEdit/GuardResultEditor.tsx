import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Card, message as tips, Row, Col, Layout, Tabs, TabPanel, Button, PopConfirm, Form, Bubble, TagSelect, SelectOptionWithGroup, Timeline, Icon, Collapse } from '@tencent/tea-component';
import { Text } from "@tencent/tea-component";
import { DescribeGuardScanResults, DescribeGuardSheet, ModifyGuardScanResultStatus, TransferGuardApproval } from '@src/api/advisor/guard';
import { DescribeGuardScanResultParams, EmergencyPlanParams, Filter, GuardParams, GuardResultItem, GuardScanResult, InstanceItem, policyItem, ScanResult, StandardDict, Transfer, VolumeResult } from '@src/types/advisor/guard';
import { useHistory } from '@tea/app';
import { CustomerName } from '../CustomerName';
import _ from 'lodash'
import { getUserInfo } from '@src/api/common';
import ScanResultTable from './ScanResultTable';
import { getProductsGroups } from '@src/api/advisor/estimate';
import TamConfirm from './TamConfirm';
const { Body, Content } = Layout;


export function GuardResultEditor(match) {
    const history = useHistory();

    //当前护航ID
    const [guardId, setGuardId] = useState(Number(match.match.params.guardid))
    //当前主APPID
    const [appid, setAppid] = useState(Number(match.match.params.appid))
    //当前APPID列表
    const [appids, setAppids] = useState<Array<number>>([])
    //当前Tab打开的Appid
    const [currentAppId, setCurrentAppId] = useState<number>(appid)
    //Tab列表
    const [tabs, setTabs] = useState<Array<{ id: string, label: React.ReactNode }>>([])
    //Collapse展开内容
    const [activeIds, setActiveIds] = useState<Array<string>>([])
    //产品中文名称
    const [productDict, setProductDict] = useState({})
    //未填写字段
    const noSet = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>
    //是否有处理权限
    const [hasOpsPermission, setHasOpsPermission] = useState(false)
    // tam是否确认过
    const [isScanApprovalSubmit, setIsScanApprovalSubmit] = useState(false)
    //巡检结果
    const [guardResult, setGuardResult] = useState<Array<GuardResultItem>>([])
    // 是否需要tam确认
    const [isNeedConfirm, setIsNeedConfirm] = useState(false);
    const tamConfirmRef = useRef(null);
    //当前登录rtx
    const [rtx, setRtx] = useState<string>('')


    //获取当前登录账号rtx
    const getCurrentOperator = async () => {
        try {
            const res = await getUserInfo()
            const name = res.data.EngName || ''
            setRtx(name)
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //获取全量巡检结果
    const getGuardResults = async () => {
        try {
            const params: DescribeGuardScanResultParams = {
                AppId: appid,
                GuardId: guardId,
                Operator: rtx,
            }
            const res = await DescribeGuardScanResults(params)
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setIsScanApprovalSubmit(res.IsScanApprovalSubmit === 1)
                setHasOpsPermission(res.HasOpsPermission)
                setGuardResult(res.ScanResult)

                let tmpAppids = []
                res.ScanResult.map(i => {
                    tmpAppids.push(i.AppId)
                })
                let uniqAppids = Array.from(new Set(tmpAppids))
                setAppids(uniqAppids)

                let tmpTabs: Array<{ id: string, label: React.ReactNode }> = []
                uniqAppids.forEach((appid) => {
                    tmpTabs.push({
                        id: appid.toString(),
                        label: <CustomerName appid={appid} />
                    })
                })
				setCurrentAppId(uniqAppids[0])
                setTabs(tmpTabs)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //获取云产品清单 
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: appid,
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setProductDict(res.ProductDict)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //result ref
    const resultRef = useRef(null);

    //init
    useEffect(() => {
        getCurrentOperator()
        if (rtx) {
            getGuardResults()
        }
    }, [rtx])

    useEffect(() => {
        // tam是否有未处理的风险
        const hasUnFinishRisk = guardResult.some(obj => {
            // 检查ScanResult数组
            if (obj.ScanResult && Array.isArray(obj.ScanResult)) {
              if (obj.ScanResult.some(item => item.ProcessStatus === 0)) {
                return true;
              }
            }
            // 检查VolumeScanResult数组
            if (obj.VolumeScanResult && Array.isArray(obj.VolumeScanResult)) {
              if (obj.VolumeScanResult.some(item => item.ProcessStatus === 0)) {
                return true;
              }
            }
            return false;
          });
        // 没有tam待处理的风险并且tam未确认过，则需要显示提交按钮
        const isNeed = !hasUnFinishRisk && !isScanApprovalSubmit;
        setIsNeedConfirm(isNeed);
    }, [guardResult]);


    useEffect(() => {
        getProductsGroupsInfo()
    }, [])

    return (
        <Body>
            <Content>
                <Content.Header
                    showBackButton
                    onBackButtonClick={() => { history.push('/advisor/guard') }}
                    title={"护航巡检结果处理"}
                    subtitle={<a
                        style={{ marginLeft: 5, marginRight: 5, color: 'blue' }}
                        href={'/advisor/guard/summary/' + guardId}
                        target="_blank" rel="noopener noreferrer">护航详情</a>
                    }
                >
                </Content.Header>
                <Content.Body>

                    <Card key={"guard_result" + guardId + appid} style={{ margin: 10 }}>
                        <Card.Body>
                            {
                                (rtx && tabs.length > 0 && hasOpsPermission) &&
                                <Tabs tabs={tabs} placement={"top"} activeId={currentAppId.toString()} onActive={(v) => { setCurrentAppId(Number(v.id)) }} destroyInactiveTabPanel={false}>

									{
										appids.map(appid => {
											let targetResults = guardResult.filter(res => { if (res.AppId === appid) { return res } })
											let volumeResult = new Array<GuardScanResult>()
											let scanResult = new Array<GuardScanResult>()
											targetResults.map((r) => {
												volumeResult = volumeResult.concat(r.VolumeScanResult)
												scanResult = scanResult.concat(r.ScanResult)
											})
											let tabPanelList = <TabPanel key={appid.toString()} id={appid.toString()}>
												<div style={{ marginTop: 10, marginLeft: 5, marginBottom: 10 }}>
													<Button
														type='link'
														onClick={() => {
															let tmp = []
															guardResult.map(resultItem => {
																tmp.push(resultItem.AppId + resultItem.Product)
															})
															setActiveIds(tmp)
														}}
													>{"全部展开"}</Button>
													<Button
														style={{ marginLeft: 15 }}
														type='link'
														onClick={() => {
															// resultRef.current.OnClick([]);
															setActiveIds([])
														}}
													>{"全部折叠"}</Button>
												</div>
												{
													targetResults.map(item => {
														if (item.ScanResult.length > 0 || item.VolumeScanResult.length > 0 || item.EmergencyPlan.length > 0) {
															return (
																<Collapse style={{ paddingBottom: '10px' }} activeIds={activeIds} onActive={v => { setActiveIds(v) }} destroyInactivePanel={false}>
																	<ScanResultTable
																		key={item.Product}
																		guardId={guardId}
																		result={item}
																		productDict={productDict}
																		opsPermission={hasOpsPermission}
																		rtx={rtx}
																		ref={resultRef}
                                                                        isScanApprovalSubmit={isScanApprovalSubmit}
                                                                        submitScanApproval = {() => {
                                                                            setIsScanApprovalSubmit(true);
                                                                        }}
                                                                        processSuccess = {() => {
                                                                            getGuardResults();
                                                                        }}
																	/>
																</Collapse>
															)
														}
													})
												}
											</TabPanel>

											return tabPanelList
										})
									}
                                </Tabs>
                            }
                            {
                                (rtx && tabs.length == 0 && hasOpsPermission) &&
                                <Text style={{ marginLeft: 5 }}>
                                    内容加载中。
                                    如长时间未更新，可能是因为该护航单的产品未勾选实例，或对应账号未授权。
                                </Text>
                            }
                            {
                                (rtx && !hasOpsPermission) &&
                                <Text style={{ marginLeft: 5 }}>
                                    当前您没有权限处理巡检结果。
                                </Text>
                            }
                            {
                                (rtx && tabs.length > 0 && hasOpsPermission) &&
                                <div style={{ margin: 20, textAlign: "center" }}>
                                    <Button 
                                        type={"primary"} 
                                        onClick={() => { 
                                            if (isNeedConfirm) {
                                                tamConfirmRef?.current?.showModal();
                                            } else {
                                                history.push('/advisor/guard');
                                            }
                                        }}
                                        style={{ marginRight: 15 }} 
                                    >
                                        {isNeedConfirm ? '提交' : '完成'}
                                    </Button>
                                </div>
                            }
                        </Card.Body>
                    </Card>
                </Content.Body>
            </Content>
            <TamConfirm 
                guardId={guardId} 
                appId={appid}
                ref={tamConfirmRef}
                confirmSuccess={()=>{
                    history.push('/advisor/guard')
                }}   
            />
        </Body >
    );
}

