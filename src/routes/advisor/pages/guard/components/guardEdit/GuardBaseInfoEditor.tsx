import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Card, Layout, message } from '@tencent/tea-component';
const { Body, Content } = Layout;
import { useHistory } from '@tea/app';
import { DescribeGuardSheet, DescribeGuardBaseConfig, DescribeGuardProjects } from '@src/api/advisor/guard';
import { GuardParams } from '@src/types/advisor/guard';
import BaseInfo from '../BaseInfo';

export function GuardBaseInfoEditor(match) {
    const history = useHistory();
    const baseinfoRef = useRef(null);
    // 当前护航单详情
    const [currentGuard, setCurrentGuard] = useState<GuardParams>({});
    // 当前护航单可修改字段列表
    const [editList, setEditList] = useState([]);

    useEffect(() => {
        getGuardSheet()
        getDescribeGuardBaseConfig()
    }, [])

    // 查询护航单详情
    const getGuardSheet = async () => {
        try {
            const filters = [
                { Name: 'guard_id', Values: [match.match.params.guardid] },
            ];
            const res = await DescribeGuardSheet({
                Filters: filters.filter((i) => {
                    if (i.Values.length) {
                        return i;
                    }
                }),
                Offset: 0,
                Limit: 10,
                AppId: 1253985742, // 接口必须传appid
            });
            if (res.Error) {
                const msg = res.Error.Message;
                message.error({ content: msg });
                return;
            }
            const item = res.Guard[0] || {};
            setCurrentGuard(item);
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    // 查询护航可编辑的信息
    const getDescribeGuardBaseConfig = async () => {
        try {
            const res = await DescribeGuardBaseConfig({
                AppId: 1253985742, // 接口必须传appid
                GuardId: Number(match.match.params.guardid)
            });
            if (res.Error) {
                const msg = res.Error.Message;
                message.error({ content: msg });
                return;
            }
            setEditList((res.GuardBaseConfig || []).filter(i => i.IsSupportModify).map(i => i.Field))
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    }

    return (
        <Body>
            <Content>
                <Content.Header
                    showBackButton
                    onBackButtonClick={() => {
                        history.go(-1);
                    }}
                    title="护航基本信息变更"></Content.Header>
                <Content.Body>
                    <Card>
                        <Card.Body style={{ height: 'calc(100vh - 212px)', overflow: 'scroll' }}>
                            {currentGuard.GuardId && <BaseInfo
                                key={'base'}
                                currentGuard={currentGuard}
                                isEdit={true}
                                editList={editList}
                                ref={baseinfoRef}
                            />}
                        </Card.Body>
                        <Card.Footer>
                            <div style={{ padding: 20, textAlign: "center" }}>
                                <Button type={"primary"} onClick={() => { baseinfoRef.current.OnClick(false) }}>
                                    保存
                                </Button>
                                <Button type={"weak"} style={{ marginLeft: 15 }} onClick={() => { history.push('/advisor/guard'); }} >
                                    返回
                                </Button>
                            </div>
                        </Card.Footer>
                    </Card>
                </Content.Body>
            </Content>
        </Body >
    );
}
