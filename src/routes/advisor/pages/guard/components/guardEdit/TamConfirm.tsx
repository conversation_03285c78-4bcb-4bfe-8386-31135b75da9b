import React, { useState, useImperativeHandle, forwardRef } from 'react';
import {  message as tips, Input, Icon, Button, Modal, Text } from '@tencent/tea-component';

import { TamConfirmParams } from '@src/types/advisor/guard';
import { submitGuardScanApproval } from '@src/api/advisor/guard';

interface Props {
	guardId: number,
	appId: number
	confirmSuccess?: Function
}


function TamConfirm({ guardId, appId, confirmSuccess }: Props, ref) {
	const rtx = localStorage.getItem('engName');
	// tam审批确认弹框
	const [confirmModal, setConfirmModal] = useState(false);
	// tam审批弹框确认信息
	const [confirmText, setConfirmText] = useState('');
	// tam审批弹框loading
	const [confirmLoading, setConfirmLoading] = useState(false);

	async function tamConfrim() {
		setConfirmLoading(true);
		try {
			const params: TamConfirmParams = {
				GuardId: guardId,
				AppId: appId,
				UserName: rtx,
				Reason: confirmText,
			};
			const res = await submitGuardScanApproval(params);
			if (res.Error) {
				setConfirmModal(false);
				setConfirmLoading(false);
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			tips.success({ content: '确认成功' });
			setConfirmModal(false);
			setConfirmLoading(false);
			// 通知上层函数
			confirmSuccess?.();
		} catch (err) {
			setConfirmModal(false);
			setConfirmLoading(false);
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 暴露回调函数给父组件
	useImperativeHandle(ref, () => ({
		showModal: () => {
			setConfirmModal(true);
		},
	}));
	return (
		<Modal
			visible={confirmModal}
			className='scanResultTamConfirm'
			caption={'是否确认架构巡检风险已处理？'}
			disableCloseIcon
		>
			<Modal.Body>
				<div>
					<Text theme="text">当前有部分风险未完成专项审批，请确认已与客户沟通确认风险处置情况，或确认风险对本次护航无影响。</Text>
					<Input
						style={{ marginTop: 12 }}
						size='full'
						value={confirmText}
						onChange={(value) => {
							setConfirmText(value);
						}}
						placeholder="请填写确认情况"
					/>
				</div>
				<div style={{ float: 'right', marginTop: 20 }}>
					{
						confirmLoading ? <Icon type="loading" />
							: <Button
								disabled={!confirmText}
								style={{ marginRight: 10 }}
								type='link'
								onClick={() => {
									tamConfrim();
								}}
							>
                        确认提交
							</Button>
					}
					{
						confirmLoading ? <Icon type="loading" style={{ marginLeft: 26 }} />
							: <Text style={{ cursor: 'pointer' }} theme="text" onClick={() => {
								setConfirmModal(false);
							}}>取消</Text>
					}
				</div>
			</Modal.Body>
		</Modal>

	);
}

export default forwardRef(TamConfirm);
