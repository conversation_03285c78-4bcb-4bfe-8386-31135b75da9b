import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Card, message as tips, Table, SearchBox, Select, Form, Bubble, SelectMultiple, Input, message, StatusTip, Icon, Collapse, Button, Modal, Tag } from "@tencent/tea-component";
const { selectable, removeable, scrollable, pageable } = Table.addons;
import { Text } from "@tencent/tea-component";
import { Row, Col } from "@tencent/tea-component";
import TamConfirm from './TamConfirm';
import _ from 'lodash'
import { RiskLevelTypeDict, ScanResultInstance, EvaluateWayDict, GuardScanResult, GuardResultItem, TamConfirmParams, HandleGuardScanResultParams, DescribeGuardScanResultParams, GuardEmergencyPlan } from '@src/types/advisor/guard';
import { DescribeGuardInstanceInfo, DescribeGuardScanResults, submitGuardScanApproval, HandleGuardScanResult } from '@src/api/advisor/guard';
import ApprovalDetailInstanceRes from '../approval/ApprovalDetailInstanceRes';
const { TextArea } = Input;


interface Props {
    guardId: number,
    productDict: Object,
    result: GuardResultItem,
    opsPermission: boolean,
    rtx: string,
	isDetail?: boolean
    isScanApprovalSubmit?: boolean
    submitScanApproval?: Function
    processSuccess?: Function
}

function ScanResultTable({ guardId, result, productDict, opsPermission, rtx, isDetail, isScanApprovalSubmit, submitScanApproval, processSuccess }: Props, ref) {
    const tamConfirmRef = useRef(null);
    const [currentResult, setCurrentResult] = useState<GuardResultItem>(result)                       //当前结果
    const [appid, setAppid] = useState<number>(result.AppId)                                          //APPID
    const [product, setProduct] = useState<string>(result.Product)                                    //产品
    const [doneCount, setDoneCount] = useState<number>(0)                                             //已处理个数
    const [todoCount, setTodoCount] = useState<number>(0)                                             //待处理个数
    const [allCount, setAllCount] = useState<number>(0)                                               //总个数
    const [currentItem, setCurrentItem] = useState({} as GuardScanResult)                             //当前更新记录
    const [columnData, setColumnData] = useState<any>()                                               //表头字段
    const [currentProcessComment, setCurrentProcessComment] = useState<string>('')                    //处理说明
    const noSet = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>            //未填写字段

    // const [productZhDict, setProductZhDict] = useState(productDict)       //产品中文名称映射
    const [strategyId, setStrategyId] = useState(-1)                      //策略ID（预定义）。值为 0 的时候表示该项巡检是人工定义，此时 itemId 值生效，作为策略ID标识。
    const [itemId, setItemId] = useState(-1)                              //评估项ID（自定义，可以新建或从预定义策略修改而来）
    const [affected, setAffected] = useState("")                          //影响实例

    // 新建或编辑隐患结果，模态框状态
    const [showUpdateModal, setShowUpdateModal] = useState(false)
    // 当前隐患结果列表，用于内容变更后刷新展示
    const [currentScanRecords, setCurrentScanRecords] = useState<Array<GuardScanResult>>(result.ScanResult)
    // 当前容量结果列表，用于内容变更后刷新展示
    const [currentVolumeRecords, setCurrentVolumeRecords] = useState<Array<GuardScanResult>>(result.VolumeScanResult)
	// 应急预案
	const [currentEmergencyPlan, setCurrentEmergencyPlan] = useState<Array<GuardEmergencyPlan>>(result.EmergencyPlan)
	const [emergencyColumn, setEmergencyColumn] = useState([])
    // 隐患风险等级计数
    const [scanRiskLevelCount, setScanRiskLevelCount] = useState(new Map())
    // 容量风险等级计数
    const [volumeRiskLevelCount, setVolumeRiskLevelCount] = useState(new Map())
    // 隐患结果列表loding
    const [recordListLoading, setRecordListLoading] = useState(false)
    // 点击策略影响实例
    const [resultInstances, setResultInstances] = useState<ScanResultInstance>({} as ScanResultInstance)
    // 点击策略影响实例弹窗
    const [visibleResultInstances, setVisibleResultInstances] = useState(false);

    useImperativeHandle(ref, () => ({
        OnClick: (passParams: Array<string>) => {
            // DO SOMETHING
        },
    }))

    //获取全量巡检结果
    const getGuardResults = async () => {
        try {
            const params: DescribeGuardScanResultParams = {
                AppId: appid,
                GuardId: guardId,
                Operator: rtx,
            }
            const res = await DescribeGuardScanResults(params)
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                //过滤指定APPID和产品
                let tmp = res.ScanResult.filter(i => { return (i.AppId == appid && i.Product == product) })
                if (tmp.length == 0) {
                    tips.error({ content: "获取记录为空" });
                    return
                }
                setCurrentResult(tmp[0])
                processSuccess?.();
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }


    //点击隐患结果实例
    const getResultInstancesShow = async (sId, insIds, level) => {
        if (sId > 0) {
            //自动
            getScanResultInstances(sId, level)
        } else {
            //人工
            setResultInstances(insIds || "")
        }
        //实例记录拉取回来后，才跳出弹窗使可见
        setVisibleResultInstances(true)
    }

    //查询隐患结果实例
    const getScanResultInstances = async (sId, level) => {
        try {
            const res = await DescribeGuardInstanceInfo({
                AppId: appid,
                GuardId: guardId,
                StrategyId: sId,
                Offset: 0,
                Limit: 1000,
				RiskLevel: level
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                setResultInstances(res)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //获取全量巡检结果，变更加载状态
    const updateGuardResults = async () => {
        setRecordListLoading(true)
        try {
            //查询新的隐患信息
            getGuardResults()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        } finally {
            setRecordListLoading(false)
        }
    }

    // 打开编辑弹窗、初始化弹窗待编辑或待保存内容
    const initItem = (item: GuardScanResult) => {
        // 初始化参数
        setCurrentItem(item);
        // tam是否确认过
		if (!isScanApprovalSubmit && result?.IsScanApproval === 0) {
            // 打开tam确认弹框
			tamConfirmRef?.current?.showModal();
		} else {
			// 打开弹窗
			setShowUpdateModal(true);
		}
    };

    // 更新处理说明
    const updateGuardScanResult = async () => {
        try {
            const params: HandleGuardScanResultParams = {
                GuardId: guardId,
                AppId: appid,
                ScanResult: [{
                    ResultId: currentItem.ResultId,
                    StrategyId: currentItem.StrategyId,
                    AppId: appid,
                    Product: product,
                    TaskType: currentItem.TaskType,
                    EvaluateMethod: currentItem.EvaluateMethod,
                    Env: currentItem.Env,
                    Comment: currentItem.Comment,
                    ProcessComment: currentProcessComment
                }],
            }

            const res = await HandleGuardScanResult(params)
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            }
            tips.success({ content: "更新成功" })
            //刷新记录
            updateGuardResults()
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //获取表格每行的展示表头及渲染数据，如果没有指定实例扩容策略的字段不展示
    function getColumnData() {
        let ret = []
        let newResult: Array<GuardResultItem> = _.cloneDeep(result)
        // 任意取一条实例记录，剥离抽取表头字段
        if (newResult.length !== 0) {
            ret.push(
                {
                    key: "StrategyId",
                    header: "ID",
                    width: "5%",
                    render: ins => (
                        <>
                            <p>{ins.StrategyId + ins.ResultId}</p>
                        </>
                    ),
                })
            ret.push(
                {
                    key: "StrategyName",
                    header: "评估项名称",
                    render: ins => (
                        <>
                            <p>{ins.StrategyName}</p>
                        </>
                    ),
                })
            ret.push(
                {
                    key: "RiskLevel",
                    width: "6%",
                    header: () => (
                        <>
                            风险等级
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {RiskLevelTypeDict.get(ins.RiskLevel)} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "StrategyDesc",
                    header: () => (
                        <>
                            评估项描述
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.StrategyDesc} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "ResultInstances",
                    width: "6%",
                    header: () => (
                        <>
                            影响实例
                        </>
                    ),
                    render: ins => (
                        <>
                            <Button type={"link"} onClick={() => { setStrategyId(ins.StrategyId); setItemId(ins.ResultId); setAffected(ins.InstanceIds); getResultInstancesShow(ins.StrategyId, ins.InstanceIds, ins.RiskLevel); }}>
                                点击
                            </Button>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "Repair",
                    width: "20%",
                    header: () => (
                        <>
                            优化建议
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.Repair} </p>
                        </>
                    ),
                }
            )
            ret.push(
				{
					key: "Env",
					width: "12%",
					header: () => (
						<>
							<Text>是否对外</Text>
							<Bubble
        					  content="巡检报告仅支持“对外”巡检项，“对内”巡检内容不支持下载"
        					>
          						<Icon type="info" />
        					</Bubble>
						</>
					),
					render: ins => (
						<>
							{ ins.Env === 'public'
							? <Tag theme="primary">{envMap[ins.Env] || '-'}</Tag>
							: <p>{envMap[ins.Env] || '-'}</p> }
						</>
					),
				}
            )
            ret.push(
                {
                    key: "Comment",
                    header: () => (
                        <>
                            备注
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.Comment} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "ProcessStatus",
                    width: "6%",
                    header: () => (
                        <>
                            处理状态
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.ProcessStatus
                                ? <Text style={{ color: "green" }}><Icon type="success" /> 已处理</Text>
                                : <Text style={{ color: "blue" }}><Icon type="infoblue" /> 待处理</Text>} </p>
                        </>
                    ),
                }
            )
            ret.push(
                {
                    key: "ProcessComment",
                    header: () => (
                        <>
                            处理说明
                        </>
                    ),
                    render: ins => (
                        <>
                            <p> {ins.ProcessComment ? ins.ProcessComment : noSet} </p>
                        </>
                    ),
                }
            )
			if (!isDetail) {
				ret.push(
					{
						key: 'Ops',
						header: "操作",
						width: "10%",
						render: item => {
							if (!opsPermission) {
								return <>{"无"}</>
							}
							return <>
								<Button onClick={() => { initItem(item) }} type="link" disabled={item.ProcessStatus == 1}>
									处理
								</Button>
							</>;
						}
					}
				);
			};
        }
        return ret;
    }

    //查询容量风险数量
    const getVolumeRiskLevelCount = async () => {
        let tmp = new Map()
        tmp.set(2, 0)
        tmp.set(3, 0)
        currentResult.VolumeScanResult.map(item => {
            if (item.RiskLevel === 2) {
                let l2 = tmp.get(2)
                l2 = l2 + 1
                tmp.set(2, l2)
            }
            if (item.RiskLevel === 3) {
                let l3 = tmp.get(3)
                l3 = l3 + 1
                tmp.set(3, l3)
            }
        })
        setVolumeRiskLevelCount(tmp)
    }

    //查询隐患风险数量
    const getScanRiskLevelCount = async () => {
        let tmp = new Map()
        tmp.set(2, 0)
        tmp.set(3, 0)
        currentResult.ScanResult.map(item => {
            if (item.RiskLevel === 2) {
                let l2 = tmp.get(2)
                l2 = l2 + 1
                tmp.set(2, l2)
            }
            if (item.RiskLevel === 3) {
                let l3 = tmp.get(3)
                l3 = l3 + 1
                tmp.set(3, l3)
            }
        })
        setScanRiskLevelCount(tmp)
    }

    //更新已处理和未处理数量
    const updateProcessCount = () => {
        let tmpDoneCount = 0
        let tmpTodoCount = 0
        let tmpAllCount = 0

        currentResult.VolumeScanResult.map(v => {
            // 处理状态，0:未处理，1:已处理
            if (v.ProcessStatus) {
                tmpDoneCount += 1
            } else {
                tmpTodoCount += 1
            }
        })

        currentResult.ScanResult.map(v => {
            // 处理状态，0:未处理，1:已处理
            if (v.ProcessStatus) {
                tmpDoneCount += 1
            } else {
                tmpTodoCount += 1
            }
        })

        tmpAllCount = currentResult.VolumeScanResult.length + currentResult.ScanResult.length

        setDoneCount(tmpDoneCount)
        setTodoCount(tmpTodoCount)
        setAllCount(tmpAllCount)
    }

    //init
    useEffect(() => {
        let ret = getColumnData()
        setColumnData(ret)
    }, [isScanApprovalSubmit])

    useEffect(() => {
        getScanRiskLevelCount()
        getVolumeRiskLevelCount()
        updateProcessCount()

        setCurrentScanRecords(currentResult.ScanResult)
        setCurrentVolumeRecords(currentResult.VolumeScanResult)
		setCurrentEmergencyPlan(currentResult.EmergencyPlan)
    }, [currentResult]);
	const envMap = {
		public: '对外',
		private: '对内'
	};

    return (
        <Collapse.Panel
            style={{ marginTop: 1 }}
            key={appid + product}
            id={(appid + product).toString()}
            title={
                <Text>{productDict[product] || ""}巡检结果&应急预案 {<Text style={{ color: "gray" }}> 已处理 {doneCount}/{allCount} 条 </Text>} {(doneCount == allCount) && <Icon type="success" />} {result?.IsScanApproval === 0 && <Text style={{ marginLeft: 10 }} theme="danger">专项未完成审批</Text>}</Text>
            }
        >
            {(currentScanRecords.length > 0) &&
                <section>
                    <Row style={{ marginTop: 10, marginLeft: 10 }}>
                        <Col>
                            <Text>
                                {
                                    (currentScanRecords.length > 0)
                                        ?
                                        <div>
                                            <Text style={{ fontWeight: 'bold' }}>隐患巡检结果</Text>
                                            <>（<Text style={{ color: "Orange" }} >中风险 {scanRiskLevelCount.get(2)} 条</Text>，<Text style={{ color: "Red" }}>高风险 {scanRiskLevelCount.get(3)} 条</Text>）
                                            </>
                                        </div>
                                        :
                                        <div><Text style={{ fontWeight: 'bold' }}>隐患巡检结果</Text><>（{currentScanRecords.length || 0} 条）</></div>
                                }
                            </Text>

                        </Col>
                    </Row>
                    <Row style={{ marginTop: 10, marginLeft: 15 }}>
                        <Col>
                            <Card>
                                <Table
                                    records={currentScanRecords}
                                    recordKey="StrategyId"
                                    columns={columnData || []}
                                    addons={
                                        [
                                            pageable(),
                                        ]
                                    }
                                    topTip={recordListLoading && <StatusTip status="loading"></StatusTip>}
                                />
                            </Card>
                        </Col>
                    </Row>
                </section>}
            {(currentVolumeRecords.length > 0) &&
                <section>
                    <Row style={{ marginTop: 15, marginLeft: 10 }}>
                        <Col>
                            <Text>
                                {
                                    (currentVolumeRecords.length > 0)
                                        ?
                                        <div>
                                            <Text style={{ fontWeight: 'bold' }}>容量巡检结果</Text>
                                            <>（<Text style={{ color: "Orange" }} >中风险 {volumeRiskLevelCount.get(2)} 条</Text>，<Text style={{ color: "Red" }}>高风险 {volumeRiskLevelCount.get(3)} 条</Text>）
                                            </>
                                        </div>
                                        :
                                        <div><Text style={{ fontWeight: 'bold' }}>容量巡检结果</Text><>（{currentVolumeRecords.length || 0} 条）</></div>
                                }
                            </Text>
                        </Col>
                    </Row>
                    <Row style={{ marginTop: 10, marginLeft: 15 }}>
                        <Col>
                            <Card>
                                <Table
                                    records={currentVolumeRecords}
                                    recordKey="StrategyId"
                                    columns={columnData || []}
                                    addons={
                                        [
                                            pageable()
                                        ]
                                    }
                                    topTip={recordListLoading && <StatusTip status="loading"></StatusTip>}
                                />
                                <>
                                </>
                            </Card>
                        </Col>
                    </Row>
                </section>}
			{(currentEmergencyPlan.length > 0) &&
				<section>
					<Row style={{ marginTop: 15, marginLeft: 10 }}>
						<Col>
							<Text>
								<div><Text style={{ fontWeight: 'bold' }}>应急预案</Text><>（{currentEmergencyPlan.length || 0} 条）</></div>
							</Text>
						</Col>
					</Row>
					<Row style={{ marginTop: 10, marginLeft: 15 }}>
						<Col>
							<Card>
								<Table
									records={currentEmergencyPlan}
									recordKey="PlanId"
									columns={[
										{
											key: "RiskScenario",
											width: "20%",
											header: () => (
												<>
													风险场景
												</>
											),
											render: ins => (
												<>
													<p> {ins.RiskScenario || '-'} </p>
												</>
											),
										},
										{
											key: "Measure",
											width: "20%",
											header: () => (
												<>
													应急措施或保障方案
												</>
											),
											render: ins => (
												<>
													<p> {ins.Measure || '-'} </p>
												</>
											),
										},
										{
											key: "Env",
											width: "20%",
											header: () => (
												<>
													<Text>是否对外</Text>
													<Bubble
													  content="巡检报告仅支持“对外”巡检项，“对内”巡检内容不支持下载"
													>
														  <Icon type="info" />
													</Bubble>
												</>
											),
											render: ins => (
												<>
													{ ins.Env === 'public'
													? <Tag theme="primary">{envMap[ins.Env] || '-'}</Tag>
													: <p>{envMap[ins.Env] || '-'}</p> }
												</>
											),
										},
									]}
									addons={
										[
											pageable()
										]
									}
									topTip={recordListLoading && <StatusTip status="loading"></StatusTip>}
								/>
								<>
								</>
							</Card>
						</Col>
					</Row>
				</section>}
            <section>
                <hr />
            </section>

            <TamConfirm
                guardId={guardId}
                appId={appid}
                ref={tamConfirmRef}
                confirmSuccess={()=>{
                    setShowUpdateModal(true);
                    // 回调上层函数更新值
                    submitScanApproval?.();
                }}
            />

            {/* 处理说明弹窗 */}
            <Modal visible={showUpdateModal} size={"m"} caption={"编辑处理说明"} onClose={() => { setShowUpdateModal(false) }}>
                <Modal.Body>
                    <div>
                        <Form style={{ marginTop: 20 }} layout={"default"}>
                            <Form.Item label={"处理说明"}>
                                <TextArea
                                    size={'l'}
                                    value={currentProcessComment}
                                    onChange={(value) => {
                                        setCurrentProcessComment(value)
                                    }} />
                            </Form.Item>
                        </Form>
                    </div>
                </Modal.Body>
                <Modal.Footer>
                    <div>
                        <Button
                            style={{ marginRight: 10 }}
                            type={"primary"}
                            onClick={() => { updateGuardScanResult(); setShowUpdateModal(false); }}
                        >
                            {'确认'}
                        </Button>
                        <Button
                            style={{ marginRight: 10 }} type={"weak"} onClick={() => { setShowUpdateModal(false) }}>
                            {'取消'}
                        </Button>
                    </div>
                </Modal.Footer>
            </Modal>

            {/* 影响实例弹窗 */}
            <Modal visible={visibleResultInstances} size={"xl"} caption={"影响实例列表"} onClose={() => setVisibleResultInstances(false)}>
                <Modal.Body>
                    {
                        // 人工（evaluateMethod:0 或 Id!=0 && StrategyId==0）、自动（evaluateMethod:1 或 Id==0 && StrategyId!=0）
                        (itemId > 0 && strategyId === 0)
                            ? <div>
                                <section>
                                    <Row style={{ marginTop: 10 }}>
                                        <Col>
                                            {affected ? affected : "无"}
                                        </Col>
                                    </Row>
                                </section>
                            </div>
                            : <div>
                                <ApprovalDetailInstanceRes
                                    strategyId={strategyId}
                                    result={resultInstances || {} as ScanResultInstance}
                                />
                            </div>
                    }
                </Modal.Body>
            </Modal>
        </Collapse.Panel>
    )
}

export default forwardRef(ScanResultTable)