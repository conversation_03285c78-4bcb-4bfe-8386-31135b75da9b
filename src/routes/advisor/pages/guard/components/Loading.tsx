import React from 'react';
import { StatusTip } from '@tencent/tea-component';

function Loading({ show = false, text = '加载中...' }) {
	return (
		<>
			{show && <div style={{
				position: 'fixed', zIndex: 9999, left: 0, right: 0, top: 0, bottom: 0, background: 'rgba(255, 255, 255, 0.9)', display: 'flex',
				justifyContent: 'center', alignItems: 'center',
			}}>
				<StatusTip status='loading' style={{ fontSize: 14 }} loadingText={text} />
			</div>}
		</>


	);
}

export default Loading;
