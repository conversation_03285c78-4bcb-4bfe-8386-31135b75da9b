import React, { useState, useEffect, useMemo } from 'react';
import {
	Layout,
	Modal,
	Button,
	Table,
	Input,
	Icon,
	StatusTip,
	Text,
	Form,
	Bubble,
	Radio,
	message
} from '@tencent/tea-component';
import { cloneDeep } from 'lodash';
import {
	getDescribeGuardReportSubscription,
	createGuardReportSubscription
} from "@src/api/advisor/report";
import { getStorage } from "@src/utils/storage";

const { Body, Content } = Layout;
const { autotip, pageable, scrollable, filterable } = Table.addons;

export function SubModal({ config, visible, onClose, subInfo }) {
	const guardItemInfo = JSON.parse(getStorage('guardItemInfo'));
	// 邮箱地址编辑
	const [editVisible, setEditVisible] = useState(false);
	// 待编辑邮箱地址集合
	const [editList, setEditList] = useState([]);
	// 待编辑邮箱原始地址集合
	const [originList, setOriginList] = useState([]);
	// 接收人邮箱地址最大数量
	const EMAIL_MAX_NUMBER = 10;
	// 邮箱地址可添加条数
	const addNum = useMemo(() => Math.abs(editList.length - EMAIL_MAX_NUMBER), [editList]);
	// 邮件地址不能为空
	const [VisibleEmails, setVisibleEmails] = useState(false);
	// 应用可修改状态
	const [updateEnable, setUpdateEnable] = useState(true);
	// 邮箱地址可编辑状态
	const editEnable = useMemo(() => {
		let isEnable = true;
		editList.forEach((edit) => {
			if (!edit.status || !edit.address.length) {
				isEnable = false;
			}
		});

		return isEnable;
	}, [editList]);
	// 接收人邮箱地址集合
	const [emailList, setEmailList] = useState([]);
	// 设置邮箱地址
	const [emptyVisible, setEmptyVisible] = useState(false);
	const [sendType, setSendType] = useState('0');
	const [submitLoading, setSubmitLoading] = useState(false);
	const ADDRESS_CHECK_TIP = {
		error: '邮箱地址错误，仅支持@tencent.com后缀',
		dupilcate: '邮箱地址重复',
		empty: '邮箱地址不能为空',
	};
	const setSubInfo = (subInfo)=>{
		if (subInfo && Object.keys(subInfo).length > 0) {
			const emaiData = subInfo.Subscription.Receiver.split(';');
			setEmailList(emaiData);
			setSendType(subInfo.Subscription.ReceiveTime == 'current' ? '0' : '1');
			let id = 0;
			const edits = emaiData.map(email => ({
				id: ++id,
				address: email,
				status: true,
				tip: '',
			}));
			setEditList(edits);
			setOriginList(edits);
		} else {
			setEmailList([]);
			setSendType('0');
			setEditList([]);
			setOriginList([]);
		}
	};
	useMemo(()=>{
		setSubInfo(subInfo);
	}, [subInfo]);
	const handleSubmit = async ()=>{
		// if (!emailList[0]) {
		// 	setVisibleEmails(true);
		// 	return;
		// }
		setSubmitLoading(true);
		try {
			const res = await createGuardReportSubscription({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				Subscription: {
					Receiver: emailList.join(';'),
					ReceiveTime: sendType == '0' ? 'current' : '#0#10#*#*#*'
				},
				ShowError: true,
				OnlyData: true
			});
			message.success({
				content: '操作成功'
			});
			onClose(true);
			setSubmitLoading(false);
		} catch (e) {
			setSubmitLoading(false);
		}
	};
	return (
		<Body>
			<Content>
				<Modal
					visible={visible}
					caption={'护航报告发送与订阅'}
					onClose={
						()=>{
							// setVisibleEmails(false);
							onClose();
							setTimeout(()=>{
								setSubInfo(subInfo);
							}, 500);
						}
					}
				>
					<Modal.Body>
						<Form>
							<Form.Item label={'接收人邮箱'} align="middle">
								<Bubble error visible={VisibleEmails} placement={"top-start"} content={'请设置邮箱地址'}>
									<div style={{
										padding: 5,
										fontSize: 12,
										display: 'flex',
										alignItems: 'center',
										flexWrap: 'wrap'
									}}>
										{emailList.length > 0 && emailList.every(email => email.length > 0) ? (
											<>
												{emailList.map(email => (
													<div key={email} style={{ display: 'inline-block' }}>
														<Text style={{ marginRight: 10 }}>
															{email}
														</Text>
														<Text style={{ marginRight: 10 }}>|</Text>
													</div>
												))}
												<div style={{ cursor: 'pointer' }}>
													<Button type="link" onClick={() => setEditVisible(true)}>
														管理邮箱地址
													</Button>
												</div>
											</>
										) : (
											<>
												{'尚未设置邮箱'}
												<Button type="link" onClick={() => {
													setEditVisible(true);
													// setVisibleEmails(false);
												}} style={{ marginLeft: 10 }}>
													去添加
												</Button>
											</>
										)}
									</div>
									{emptyVisible && (
										<div style={{ color: '#e1504a', fontSize: 12, marginTop: 5 }}>
											请设置邮箱地址
										</div>
									)}
								</Bubble>
							</Form.Item>
							<Form.Item
								label={'发送类型'}
							>
								<Radio.Group value={sendType} onChange={value => setSendType(value)}>
									<Radio name="0">当次</Radio>
									<Radio name="1">每日10:00</Radio>
								</Radio.Group>
							</Form.Item>
						</Form>
					</Modal.Body>
					<Modal.Footer>
						<Button
							type={'primary'}
							onClick={handleSubmit}
							loading={submitLoading}
						>
							确定
						</Button>
						<Button
							onClick={
								()=>{
									// setVisibleEmails(false);
									onClose();
									setTimeout(()=>{
										setSubInfo(subInfo);
									}, 500);
								}
							}
						>
							取消
						</Button>
					</Modal.Footer>
				</Modal>
				{/* 邮箱地址编辑框 */}
				<Modal
					className={'report-sub-modal'}
					visible={editVisible}
					caption={'邮箱地址'}
					onClose={() => {
					const currentEditList = cloneDeep(originList); setEditList(currentEditList); setEditVisible(false);
				}}>
					<Modal.Body>
						<Table
							bordered
							records={editList}
							recordKey="id"
							columns={[
								{
									key: 'id',
									header: '序号',
									width: 80,
									align: 'center',
								},
								{
									key: 'address',
									header: '邮箱地址',
									render: edit => (
										<>
											<Input
												size="l"
												value={edit.address}
												style={!edit.status ? { border: '1px solid #e1504a' } : {}}
												onChange={(value) => {
													const currentEditList = [...editList];
													const currentEdit = currentEditList.find(cur => cur.id === edit.id) || {};
													currentEdit.address = value;

													if (!value) {
														// 邮箱地址不能为空
														currentEdit.status = false;
														currentEdit.tip = ADDRESS_CHECK_TIP.empty;
													} else if (
														!/^([A-Za-z0-9_\-.])+(@tencent.com)$/.test(value)
													) {
														// 邮箱地址输入错误
														currentEdit.status = false;
														currentEdit.tip = ADDRESS_CHECK_TIP.error;
													} 
													// 如果报告配置中 ‘护航人员’开启，那么就无法输入非 @tencent.com 的邮箱
													// else if (config['0']?.['State'] == 1 && !/^([A-Za-z0-9_\-.])+(@tencent.com)$/.test(value)) {
													// 	currentEdit.status = false;
													// 	currentEdit.tip = ADDRESS_CHECK_TIP.error;
													// }
													else if (
														currentEditList.find(cur => cur.address === value && cur.id !== edit.id)
													) {
														// 邮箱地址不能重复
														currentEdit.status = false;
														currentEdit.tip = ADDRESS_CHECK_TIP.dupilcate;
													} else {
														currentEdit.status = true;
														currentEdit.tip = '';
													}

													setEditList(currentEditList);
												}}
												placeholder={'当前报告仅支持发送内部邮箱，请输入****************'}
											/>
											{!edit.status && (
												<div style={{ color: '#e1504a', fontSize: 12, marginTop: 5 }}>{edit.tip}</div>
											)}
										</>
									),
								},
								{
									key: 'operation',
									header: '操作',
									width: 80,
									render: edit => (
										<Button
											type="link"
											onClick={() => {
												let currentEditList = [...editList];
												currentEditList = currentEditList.filter(cur => cur.id !== edit.id);
												setEditList(currentEditList);
											}}
										>
											删除
										</Button>
									),
								},
							]}
							topTip={
								!editList.length && (
									<StatusTip
										// @ts-ignore
										status="empty"
										emptyText={'请添加邮箱地址'}
									/>
								)
							}
							addons={[
								// 支持表格滚动，高度超过6行开始显示滚动条
								scrollable({
									minHeight: 356,
									maxHeight: 356,
								}),
							]}
						/>
						<Button
							type="link"
							style={{ display: 'flex', alignItems: 'center', marginTop: 10 }}
							onClick={() => {
								let currentEditList = [...editList];
								const lastEdit = currentEditList[currentEditList.length - 1];
								let lastId = lastEdit ? lastEdit.id : 0;
								const newEdit = [
									{
										id: ++lastId,
										address: '',
										status: true,
										tip: '',
									},
								];
								currentEditList = [...currentEditList, ...newEdit];
								setEditList(currentEditList);
							}}
							disabled={editList.length >= EMAIL_MAX_NUMBER}
						>
							<Icon type="plus"></Icon>
							{'添加一条'}
							<Text style={{ color: '#bbb' }}>{`（还可以添加${addNum}条）`}</Text>
						</Button>
					</Modal.Body>
					<Modal.Footer>
						<Button
							type="primary"
							disabled={!editEnable}
							onClick={() => {
								const currentEmailList = editList.map(edit => edit.address);
								const currentOriginList = cloneDeep(editList);
								setEmailList(currentEmailList);
								setOriginList(currentOriginList);
								setEditVisible(false);
								if (currentEmailList.length) {
									setEmptyVisible(false);
									// setVisibleEmails(false);
								} else {
									// setVisibleEmails(true);
								}
								setUpdateEnable(true);
							}}
						>
							确定
						</Button>
						<Button
							type="weak"
							onClick={() => {
								const currentEditList = cloneDeep(originList);
								setEditList(currentEditList);
								setEditVisible(false);
							}}
						>
							取消
						</Button>
					</Modal.Footer>
				</Modal>
			</Content>
		</Body>
	);
}
