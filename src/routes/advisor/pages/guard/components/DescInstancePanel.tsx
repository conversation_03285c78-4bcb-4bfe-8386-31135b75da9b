import React, { useState, useEffect, useRef } from 'react';
import { Card, message as tips, Row, Col, message, SelectMultiple, Collapse, Bubble, Icon } from '@tencent/tea-component';
import { Text, Form } from "@tencent/tea-component";
import { GuardParams, InstanceItem, ProductDescItem, ProductTemplateItem, StandardDict } from '@src/types/advisor/guard';
import { getProductsGroups } from '@src/api/advisor/estimate';
import DescInstanceTable from './DescInstanceTable';
import _ from 'lodash'


interface Props {
    appid: number,
    authorized?: boolean,
    currentGuard: GuardParams;
    showProduct?: string
}

function DescInstancePanel({ appid, authorized, currentGuard, showProduct }: Props) {
    //资源信息收集
    const [productDict, setProductDict] = useState({})                           //产品中文名称
    const [relateProducts, setRelateProducts] = useState<Array<string>>([])      //关联云产品
    const [relateZhProducts, setRelateZhProducts] = useState<Array<string>>([])  //关联云产品（中文名）
    const [activeIds, setActiveIds] = useState<Array<string>>([])                //展开的云产品
    const [unSupportedProducts, setUnSupportedProducts] = useState<Array<string>>([])  //暂未支持的产品
    //护航产品策略信息
    const [productTemplate, setProductTemplate] = useState<Array<ProductTemplateItem>>(currentGuard.ProductTemplate || [])
    //实例维度的护航容量策略
    const [instancePolicyDict, setInstancePolicyDict] = useState<any>({})
    //产品描述信息
    const [productDesc, setProductDesc] = useState<Array<ProductDescItem>>(currentGuard.ProductDesc)
    const [productComment, setProductComment] = useState({}) //备注
    const [productInstances, setProductInstances] = useState({}) //手工实例列表（未开通账号的产品、开通账号&未接入云顾问或云护航的产品）
    const noSetInstances = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写实例</Text> //未填写手工实例
    const noSetComment = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写备注</Text> //未填写备注
    const noSet = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text> //未填写备注
    // 已经展开过的产品
    const [requestedProducts, setRequestedProducts] = useState([])
    // cdn是否需要展示实例信息
    const [cdnInstancePolicy, setCdnInstancePolicy] = useState(false)

    // 是否是国际站
	const isAbroadSite = localStorage.getItem('site') === 'sinapore';
	// 是否是自助护航
	const isSelfEscort = currentGuard.Standard === 3;
    const createEnv = ['ISA', 'CONSOLE'];
    const arch = currentGuard?.CloudGuardBaseInfoOtherPlatform?.find(i => createEnv.includes(i.Platform) && i.AppId === appid) || {};
    const archUrl = `${location.origin}/advisor/new-architecture/architecture/${arch?.PlatformUniqueId}?appid=${appid}&plugin=cloud-escort-sdk`;

    useEffect(() => {
        setRequestedProducts(Array.from(new Set([...requestedProducts, ...activeIds])))
    }, [activeIds])
    //获取云产品清单 
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: appid,
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let tmpProductsOptions = []
                for (var i in res.ProductDict) {
                    tmpProductsOptions.push({ value: i, text: res.ProductDict[i] })
                }
                setProductDict(res.ProductDict)

                //获取产品列表
                let products = []
                let productDesc = currentGuard.ProductDesc || []
                if (productDesc) {
                    productDesc.map(i => {
                        if (products.indexOf(i.Product) === -1 && i.AppId === appid) {
                            products.push(i.Product)
                        }
                    })
                }
                setRelateProducts(products)

                //获取未支持产品
                setUnSupportedProducts(res.UnSupportedProducts)

                //获取产品中文列表
                let zhProducts = []
                let productDict = res.ProductDict
                products.map(i => {
                    if (zhProducts.indexOf(i) === -1) {
                        zhProducts.push(productDict[i])
                    }
                })
                setRelateZhProducts(zhProducts)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    // 展示产品策略列表
    function getProductPolicyList(appid, product) {
        let leftCols = []
        let rightCols = []

        let target = productTemplate.filter(i => { return i.AppId == appid && i.Product == product })
        if (target.length == 0) {
            return <></>
        }

        let ppItem = target[0]
        ppItem.Policy.map((policy, idx) => {
            // cdn 需求类型适配
            if(policy.MetricName==='CDNRequirementType' && policy.Value==='突发护航需求' && !cdnInstancePolicy ){
                setCdnInstancePolicy(true)
            }
            let cell =
                <section>
                    <Row verticalAlign={"middle"}>
                        <Col span={12} >
                            {policy.IsRequired && <Text style={{ color: "red" }} verticalAlign="middle">*</Text>}
                            <Text theme="label" verticalAlign="middle">{policy.CNName || ''}</Text>
                        </Col>
                        <Col span={36}>
                            {policy.Value || noSet}
                        </Col>
                    </Row>
                </section>
            if (idx % 2) {
                rightCols.push(cell)
            } else {
                leftCols.push(cell)
            }
        })

        let tmp =
            <>
                <Row gap={30}>
                    <Col>
                        {leftCols}
                    </Col>
                    <Col>
                        {rightCols}
                    </Col>
                </Row>
                <hr />
            </>

        return tmp
    }

    const resourceRef = useRef(null);

    //init
    useEffect(() => {
        getProductsGroupsInfo();
    }, [])

    useEffect(() => {
        let currProductDesc = (productDesc || []).filter(i => { if (i.AppId === appid) { return i } })
        let tmpComment = {}
        let tmpInstances = {}
        currProductDesc.map(i => {
            tmpComment[i.Product] = i.Comment
            tmpInstances[i.Product] = i.InstanceIds
        })
        setProductComment(tmpComment)
        setProductInstances(tmpInstances)
    }, [productDesc])

    return (
        <div style={{ marginTop: 10 }}>
            {
                showProduct ?
                    <>
                        {/* 授权账号、接入云顾问的产品 */}
                        {(authorized && !unSupportedProducts.includes(showProduct)) &&
                            <DescInstanceTable
                                cdnInstancePolicy={cdnInstancePolicy}
                                product={showProduct}
                                instancePolicys={instancePolicyDict[showProduct] || []}
                                key={showProduct}
                                appid={appid}
                                ref={resourceRef}
                                guardId={currentGuard.GuardId}
                            />
                        }
                        {/* 未授权账号，或已授权但未接入云顾问的产品 */}
                        {(!authorized || unSupportedProducts.includes(showProduct)) &&
                            <section>
                                <Row verticalAlign="middle" style={{ marginTop: 10, marginBottom: 10 }}>
                                    <Col span={12} >
                                        <Text theme="label">重点护航实例</Text>
                                    </Col>
                                    <Col span={36} >
                                        <Text style={{ whiteSpace: "pre-line" }}>{productInstances[showProduct] ? productInstances[showProduct] : noSetInstances}</Text>
                                    </Col>
                                </Row>
                            </section>
                        }
                        <section>
                            <Row verticalAlign="middle" style={{ marginTop: 10, marginBottom: 10 }}>
                                <Col span={12} >
                                    <Text theme="label">备注</Text>
                                </Col>
                                <Col span={36} >
                                    <Text>{productComment[showProduct] ? productComment[showProduct] : noSetComment}</Text>
                                </Col>
                            </Row>
                        </section>
                        <hr />
                        <section>
                            {getProductPolicyList(appid, showProduct)}
                        </section>
                    </>
                    :
                    <>
                        <section>
                            <Form readonly>
                            {(!isSelfEscort && !isAbroadSite && arch?.PlatformUniqueId) && <Form.Item label="*已关联架构图">
                                    <Form.Text>
                                        <Text theme="primary" style={{ cursor: 'pointer' }} onClick={ () => {
                                                window.open(archUrl);
                                            }}>
                                            { arch?.PlatformUniqueName || ''}
                                        </Text>
                                    </Form.Text>
                                </Form.Item>}
                                <Form.Item></Form.Item>
                                <Form.Item></Form.Item>
                                <Form.Item label="*涉及云产品">
                                    <Form.Text>{(relateZhProducts || []).join("、")}</Form.Text>
                                </Form.Item>
                                <Form.Item></Form.Item>
                                <Form.Item></Form.Item>
                                <Form.Item></Form.Item>
                            </Form>
                        </section>

                        <Collapse className="intlc-assessment-tabitem__content" activeIds={activeIds} onActive={v => { setActiveIds(v) }} destroyInactivePanel={false}>
                            {
                                relateProducts.map(product => {
                                    let bubbleNAProduct = < Bubble
                                        key={product}
                                        arrowPointAtCenter
                                        placement="right"
                                        content="该产品未接入云护航，仅分配护航人员"
                                    >
                                        {unSupportedProducts.includes(product) && < Icon type="info" style={{ marginLeft: 2 }} />}
                                    </Bubble>

                                    return <div key={product}>
                                        <Collapse.Panel style={{ marginTop: 10, width: '100%', display: unSupportedProducts.includes(product) ? 'inline-block' : '' }} key={product} id={product} title={[productDict[product], bubbleNAProduct]} >
                                            {/* 授权账号、接入云顾问的产品 */}
                                            {(requestedProducts.includes(product) && authorized && !unSupportedProducts.includes(product)) &&
                                                <DescInstanceTable
                                                    cdnInstancePolicy={cdnInstancePolicy}
                                                    product={product}
                                                    instancePolicys={instancePolicyDict[product] || []}
                                                    key={product}
                                                    appid={appid}
                                                    ref={resourceRef}
                                                    guardId={currentGuard.GuardId}
                                                />
                                            }
                                            {/* 未授权账号，或已授权但未接入云顾问的产品 */}
                                            {(!authorized || unSupportedProducts.includes(product)) &&
                                                <section>
                                                    <Row verticalAlign="middle" style={{ marginTop: 10, marginBottom: 10 }}>
                                                        <Col span={12} >
                                                            <Text theme="label">重点护航实例</Text>
                                                        </Col>
                                                        <Col span={36} >
                                                            <Text style={{ whiteSpace: "pre-line" }}>{productInstances[product] ? productInstances[product] : noSetInstances}</Text>
                                                        </Col>
                                                    </Row>
                                                </section>
                                            }
                                            <section>
                                                <Row verticalAlign="middle" style={{ marginTop: 10, marginBottom: 10 }}>
                                                    <Col span={12} >
                                                        <Text theme="label">备注</Text>
                                                    </Col>
                                                    <Col span={36} >
                                                        <Text>{productComment[product] ? productComment[product] : noSetComment}</Text>
                                                    </Col>
                                                </Row>
                                            </section>
                                            <hr />
                                            <section>
                                                {getProductPolicyList(appid, product)}
                                            </section>
                                        </Collapse.Panel>
                                        {/* < Bubble
                                    arrowPointAtCenter
                                    placement="right"
                                    content="该产品未接入云护航，仅分配护航人员"
                                >
                                    {unSupportedProducts.includes(product) && < Icon type="info" style={{ marginLeft: 2 }} />}
                                </Bubble> */}
                                    </div>
                                })
                            }
                        </Collapse>
                    </>
            }
        </div>
    );
}

export { DescInstancePanel };
