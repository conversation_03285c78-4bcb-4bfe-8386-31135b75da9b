import React, { useMemo, useState } from 'react';
import { InputNumber, InputAdornment, message } from 'tdesign-react';
import {
	Form,
	Button,
	Bubble,
} from '@tencent/tea-component';
import './index.less';
import { isNumber } from 'lodash';
import { GuardParams } from '@src/types/advisor/guard';
import { ModifyGuardActualTime } from '@src/api/advisor/guard';
interface IProps {
	guardBaseInfo: GuardParams
	reloadGuardInfo: () => void;
}

enum StatusType {
	FAIL = 'FAIL',
	SUCCESS = 'SUCCESS',
}
/** Root
 * @param props
 * @returns React.ReactElement
 */
export default function GuardFinishPanel({ guardBaseInfo, reloadGuardInfo }: IProps): React.ReactElement {
	const {
		CreatedBy: createdBy,
		Responser: Responser,
		ActualDays: actualDays,
		GuardId: guardId,
	} = guardBaseInfo || {};
	const [isloading, setIsloading] = useState(false);

	const [guardUseDay, setGuardUseDay] = useState<any>(actualDays);
	const ctxUser = localStorage.getItem('engName');
	const isTimeModified = useMemo(() => ((createdBy === ctxUser || Responser === ctxUser)), []);
	const modifyUseDay = () => {
		if (!isNumber(guardUseDay)) return;
		setIsloading(true);
		ModifyGuardActualTime({
			AppId: 1253985742, // 接口必须传appid
			GuardId: guardId,
			ActualDays: guardUseDay,
			Operator: ctxUser,
		}).then((res) => {
			const { Message: messageText = '', Status: status } = res;
			if (status === StatusType.SUCCESS) {
				message.success({ content: '修改护航投入天数成功' });
				reloadGuardInfo();
			} else if (status === StatusType.FAIL) {
				message.error({ content: messageText });
			}
		})
			.catch((e) => {
				console.log(e);
				message.error({ content: '修改护航投入天数失败' });
			})
			.finally(() => {
				setIsloading(false);
			});
	};
	return (
		<>
			<div className='guard-finish-panel'>
				<h4>TAM护航后确认</h4>
				<hr />
				<div>
					<Form>
						<Form.Item label="实际护航投入天数" required>
							<InputAdornment append="天">
								<InputNumber
									size='small'
									value={guardUseDay}
									onChange={value => setGuardUseDay(value)}
									theme="normal"
									min={0}
									decimalPlaces={2}
								/>
							</InputAdornment>
						</Form.Item>
					</Form>
				</div>
				<div className='guard-finish-panel-btn'>
					<Bubble
						arrowPointAtCenter
						placement="auto"
						trigger="hover"
						dark
						content={
							isTimeModified
								? ''
								: <p>
									<p>-什么时候修改？护航结束状态下可修改</p>
									<p>{`-谁能修改？建单人、负责人：${createdBy}; ${Responser}`}</p>
								</p>
						}
					>
						<Button
							type="weak"
							disabled={!isTimeModified}
							loading={isloading}
							onClick={modifyUseDay}
						>
							修改
						</Button>
					</Bubble>
				</div>
			</div>
		</>
	);
}
