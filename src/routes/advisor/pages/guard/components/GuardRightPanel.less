.RightContentWrap{
  .tea-layout__content-body{
    padding-left: 0;
  }
}
.GuardRightPanelWrap{
  .statu{
    color:rgb(0, 110, 255);
    text-align: center;
    font-size: 16px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e4e7ec;
    font-weight: bold;
  }
  .statuInfo{
    margin: 12px 0;
    .infoItem{
      margin-bottom: 6px;
      display: flex;
      .infoLabel{
        display: flex;
        width: 110px;
        .label{
          margin-right: 8px;
        }
      }
      .infoValue{
        display: inline-block;
        width: calc(100% - 110px);
        overflow-wrap: break-word;
      }
    }
  }
  .entranceWrap{
    display: flex;
    align-items: center;
    flex-direction: column;
    cursor: pointer;
    & > span{
      text-align: center;
    }
    .imgWrap{
      width: 32px;
      height: 32px;
      margin-bottom: 12px;
    }
  }
  .personItem{
    display: flex;
    border-top: 1px solid #e4e7ec;
    padding-top: 6px;
    margin-bottom: 6px;
    .personLabel{
      display: inline-block;
      width:130px;
      margin-right: 20px;
    }
    .noPerson{
      color: #f3a657;
     }
  }

  .dutyItem{
    border: none;
    padding-top: 0;
  }
  
  .questionItem{
    margin-bottom: 12px;
    .listWrap{
      &>div{
        margin: 8px 0 0 26px;
      }
     
    }
  }
  
  
}