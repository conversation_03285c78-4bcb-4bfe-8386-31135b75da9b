import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { TagSearchBox, message } from "@tencent/tea-component";
import { AttributeValue } from "@tencent/tea-component/src/tagsearchbox/AttributeSelect";
import { FilterInfoItem } from "@src/types/advisor/guard";
import { DescribeGuardProductInstances } from '@src/api/advisor/guard';
import _ from 'lodash'
import { TagItem } from '@src/types/common';
import { getTagsOption } from '@src/api/architecture/architecture';

interface Props {
    filterInfo: Array<FilterInfoItem>,
    tagInfo?: Array<TagItem>,
    CallBack: Function,
    appid: number,
    product: string,
    region: string,
    updateLoading: Function,
    updateBottomTipLoading: Function,
    totalChange?: Function
}

interface Filter {
    Name: string,
    Values: Array<string>,
}

interface TagSearchItem {
    key: string,
    name: string,
}

function ResourceSearchBox({CallBack, filterInfo, appid, product, region, updateLoading, updateBottomTipLoading, totalChange }: Props, ref) {
    //查询实例的Offset Limit 暂时不支持分页
    const [offset, setOffset] = useState<number>(0)
    const [limit, setLimit] = useState<number>(100)
    const [total, setTotal] = useState<number>(0)
    //当前实例记录，包含分页逻辑，不断累加
    const [records, setRecords] = useState([])
    //tag
    const [tags, setTags] = useState([])
    const [currenttags, setcurrenttags] = useState([])
    // filter
    const [filter, setFilter] = useState<Array<Filter>>([])
    //当前查询任务是否完成，避免下拉触发频繁调用
    const [finish, setFinish] = useState<boolean>(true)
    //标签选项
    const [tagInfo, setTagInfo] = useState<Array<TagItem>>([])

    //把filterInfo 转换为 attributes
    const attributes: Array<AttributeValue> = useMemo(() => {
        let tagOptions = getTagOptions(tagInfo)
        let tmp: Array<AttributeValue> = []
        filterInfo.map(i => {
            if (i.Type == "Tag") {
                tmp.push({
                    type: ["multiple", { all: false, searchable: true }],
                    key: i.FilterName,
                    name: i.FilterAlias,
                    values: tagOptions,
                })
            } else {
                tmp.push({
                    type: "input",
                    key: i.FilterName,
                    name: i.FilterAlias,
                })
            }
        })
        return tmp
    }, [filterInfo, tagInfo])



    //暴露方法给父组件调用，下拉拖动触发
    useImperativeHandle(ref, () => ({
        search: scrollTriggle => { getInstances(scrollTriggle) },
    }))

    //查询实例信息
    const getInstances = async (scrollTriggle?: boolean) => {
        if (!finish) {
            return
        }
        let Offset = offset //偏移量
        //scrollTriggle true表示由下拉拖动触发  false表示由搜索动作触发
        if (scrollTriggle) {
            //判断Offset 不为零，且大于 total 则说明已经到底,直接返回
            if (Offset > 0 && Offset >= total) {
                return
            }
            Offset = Offset + limit  //下拉触发，偏移量累加
            setOffset(Offset)
            updateBottomTipLoading(true) //下拉触发，启动下方loading
        } else {
            Offset = 0
            setOffset(0)
            updateLoading(true)  //搜索动作触发，启动上方loading
        }
        try {
            const res = await DescribeGuardProductInstances(
                {
                    AppId: appid,
                    Products: [product],
                    Regions: region ? [region] : [],
                    Offset: Offset,
                    Limit: limit, //固定值，目前穿梭选择框不支持配置分页
                    Filter: filter,
                });
            updateLoading(false)
            updateBottomTipLoading(false)
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                if (_.isEmpty(res.Instances)) {
                    setTotal(0)
                    totalChange(filter, 0)
                    setRecords([])
                } else {
                    setTotal(res.Instances[product].TotalCount || 0)
                    totalChange(filter || 0, res.Instances[product].TotalCount || 0)
                    //下拉触发，进行分页累加
                    if (scrollTriggle) {
                        setRecords(records.concat(res.Instances[product].Instance || []))
                    } else {
                        setRecords(res.Instances[product].Instance || [])
                    }
                }
                setFinish(true)
            }
        } catch (err) {
            updateLoading(false)
            updateBottomTipLoading(false)
            setFinish(true)
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //查询产品标签信息
    const getTags = async (product: string) => {
        try {
            const res = await getTagsOption(
                {
                    AppId: appid,
                    Product: product,
                    Regions: region ? [region] : [],
                })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                if (_.isEmpty(res.Tags)) {
                    setTagInfo([])
                } else {
                    setTagInfo(res.Tags)
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //获取标签下拉选项
    function getTagOptions(tagInfo: Array<TagItem>) {
        let tmpList: Array<TagSearchItem> = [];

        (tagInfo || []).map(t => {
            let tk = t.TagKey
            t.TagValues.map(tv => {
                let tmp: TagSearchItem = {
                    key: tk,
                    name: tk + "::" + tv, //指定分隔符
                }
                tmpList.push(tmp)
            })
        });

        return tmpList
    }


    //监听tags变化
    useEffect(() => {
        let Filter = []
        let tagsTmp = []
        tags.map(i => {
            let tmp = _.cloneDeep(i)
            if (i.attr) {
                let Name = i.attr.key
                let Values = []
                //判断是否只支持单个，如果只支持单个，则默认取第一个
                if (filterInfo.filter(item => { if (item.FilterName === Name) { return item } }).length) {
                    let item = filterInfo.filter(item => { if (item.FilterName === Name) { return item } })[0]
                    if (item.Type === 'Single') {
                        tmp.values = [i.values[0]]
                        Values.push(i.values[0].name)
                        if (i.values.length > 1) {
                            message.warning({ content: item.FilterAlias + "只支持单个实例搜索" })
                        }
                    } else {
                        i.values.map(j => {
                            if (Values.indexOf(j.name) === -1) {
                                Values.push(j.name)
                            }
                        })
                    }
                }
                Filter.push({
                    Name: Name,
                    Values: Values
                })
                tagsTmp.push(tmp)
            }
        })
        setFilter(Filter)
        setcurrenttags(tagsTmp)
    }, [tags])

    //当搜索条件或地区变化时，重新搜索
    useEffect(() => {
        getInstances()
    }, [filter, region])

    //当地域变化时，重新搜索标签
    useEffect(() => {
        getTags(product)
    }, [region])

    //当records变化时，返回父组件
    useEffect(() => {
        CallBack(records)
    }, [records])
    return (
        <>
            <TagSearchBox
                attributes={attributes}
                minWidth={'100%'}
                value={currenttags}
                onChange={tags => { setTags(tags) }}
                onSearchButtonClick={() => {
                    //清空
                    getInstances()
                }}
            />
        </>
    )
}

export default forwardRef(ResourceSearchBox)