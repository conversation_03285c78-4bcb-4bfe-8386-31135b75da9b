import React, { useState, useEffect, useMemo } from 'react';
import {
	Modal,
	Button,
	Table,
	Switch,
	message,
	Bubble,
	Icon
} from '@tencent/tea-component';
import {
	createGuardReportModel
} from "@src/api/advisor/report";
import { cloneDeep } from 'lodash';
import { TitleBubble } from "@src/routes/advisor/pages/guard/components/TitleBubble";
import { getStorage } from "@src/utils/storage";

export function ReportConfigModal({ subInfo, visible, onClose, config, set }) {
	const { draggable } = Table.addons;
	const guardItemInfo = JSON.parse(getStorage('guardItemInfo'));
	const [records, setRecords] = useState([]);
	const [submitLoading, setSubmitLoading] = useState(false);
	useMemo(()=>{
		const configList = [];
		for (const key in config) {
			configList.push({
				...config[key]
			});
		}
		setRecords(configList);
	}, [config]);
	const columns = [
		{
			header: '',
			key: 'Name',
			render: item => <>
				<span>{item.Name}</span>
				{item.ModelDesc && <Bubble
					arrowPointAtCenter
					placement="top"
					content={item.ModelDesc}
				>
					<Icon style={{ marginLeft: 4 }} type="info" />
				</Bubble>}
			</>
		},
		{
			header: '',
			key: '',
			render(item) {
				return <div style={
					{
						textAlign: 'right'
					}
				}>
					<Switch
						value={item.State == 1 ? true : false}
						onChange={
							(val) => {
								// 如果点击的是护航人员，需要判断邮箱
								if (val && item.Id === 0 && subInfo.Subscription?.Receiver) {
									const emailArray = subInfo.Subscription.Receiver.split(';');
									// 判断邮箱是否都是 @tencent.com 结尾的内部邮箱
									let result = emailArray.every(email => email.endsWith('@tencent.com'));
									if (!result) {
										message.error({ content: "接收人邮箱全是 @tencent.com 的内部邮箱才能开启" })
										return
									}
								}
								item['State'] = val ? 1 : -1;
								setRecords(cloneDeep(records));
							}
						}
					></Switch>
				</div>;
			}
		}
	];
	const handleSubmit = async ()=>{
		setSubmitLoading(true);
		try {
			const cureentConfigInfo = {};
			const ModelWorkList = [
				{
					State: 1,
					List: []
				},
				{
					State: -1,
					List: []
				},
			];
			const ModelSortList = records.map(item => item.Id);
			records.forEach((item, index)=>{
				cureentConfigInfo[index] = {
					...item
				};
				if (item.State == 1) {
					ModelWorkList[0].List.push(item.Id);
				} else if (item.State == -1) {
					ModelWorkList[1].List.push(item.Id);
				}
			});
			const res = await createGuardReportModel({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ModelWorkList,
				ModelSortList,
				ShowError: true,
				OnlyData: true
			});
			message.success({
				content: '设置成功'
			});
			set(cureentConfigInfo);
			onClose();
			setSubmitLoading(false);
		} catch (e) {
			setSubmitLoading(false);
		}
	};

	return (
		<Modal
			caption={<TitleBubble
				title={'护航报告配置'}
				content={'您可以按需配置对应模块是否展示在邮件报告中'}
				style={
					{
						fontWeight: 700,
						fontSize: '14px'
					}
				}
			/>}
			visible={visible}
			onClose={()=>{
				onClose();
			}}>
			<Modal.Body>
				<Table
					className={'report-config-tab'}
					columns={
						columns
					}
					records={
						records
					}
					recordKey={'Id'}
					addons={[
						draggable({
							onDragEnd: (records, context) => {
								const start = parseInt(context.dragKey.split('_')[1]);
								const end = parseInt(context.dropKey.split('_')[1]);
								if (start !== end) {
									setRecords(records);
								}
							},
							draggableRowKeys: records.filter(item => item.Id && item.Id !== 0)
								.map(item => item.Id.toString())
						}),
					]}
				/>
			</Modal.Body>
			<Modal.Footer>
				<Button
					type={'primary'}
					onClick={handleSubmit}
					loading={submitLoading}
				>
					确定
				</Button>
				<Button
					onClick={()=>{
						onClose();
					}}
				>
					取消
				</Button>
			</Modal.Footer>
		</Modal>
	);
}
