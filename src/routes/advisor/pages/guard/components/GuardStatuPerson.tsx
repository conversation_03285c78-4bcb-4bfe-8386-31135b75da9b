import React, { useState, useEffect } from 'react';
import { Button, message, Bubble } from '@tencent/tea-component';
import { CreateGuardApprovalUrge } from '@src/api/advisor/guard';
import moment from 'moment'
//流程状态ID
const processState = {
  onNoteId: 1,              // 草稿状态ID
  submitId: 2,              // 订单已提交状态ID
  onSaleApprovalId: 31,     // 售后审批状态ID
  onRunningId: 32,          // 正在巡检中状态ID
  onExpertApprovalId: 33,   // 专项分配人员状态ID
  onResultApprovalId: 34,   // 巡检结果审核状态ID
  onReportGenerating: 36,   // 巡检报告生成中
  onReportGenerated: 37,    // 巡检报告已完成
  instanceAltering: 40,     // 实例修改中ID
  instanceAlteredRun: 41,   // 实例修改后运行中ID
  tamCheckResultId: 48,     // 巡检风险审批（TAM）
  onFinishId: 50,           // 护航巡检完成状态ID
  processStopId: -1,        // 流程中止状态ID
  scanFailedId: -2,         // 巡检异常状态ID
  approvalFailedId: -3,     // 审核异常状态ID
  deletedId: -50,           // 已删除
}

export default function GuardStatuPerson({ item }) {
  const isConfirm = item.Approvals?.AfterSalesStatus?.IsConfirm
  // 售后审批（TAM）
  const afterSalesCheck = item.Approvals?.AfterSalesStatus?.Handler
  // eslint-disable-next-line max-len
  const { Handler, IsConfirm: afterSaleIsConfirm, IsNeedConfirm, IsSpecial, SpecialHandler } = item.Approvals?.AfterSaleConfirmStatus || {};
  // 护航负责人
  const supporterCheck = item.Approvals?.AfterSalesStatus?.Supporter
  // 专项人员分配（专项接口人）
  const expertDispatchCheck = (item.Approvals?.ExpertStatus || [])
    .filter(i => !i.IsFinished)
    .map(ap => {
      let apHandler = (ap.Handler ? ap.Handler : "")
      return [
        <div><strong>{ap.ProductName}</strong></div>,
        apHandler,
        <div></div>
      ]
    })
  // 巡检结果审批（专项Owner）
  const expertWorkCheck = (item.Approvals?.ScanResultStatus || [])
    .filter(i => !i.IsFinished)
    .map(ap => {
      let apHandler = (ap.Handler ? ap.Handler : "")
      return [
        <div><strong>{ap.ProductName}</strong></div>,
        apHandler,
        <div></div>
      ]
    })
  // 巡检风险审批（TAM）
  const tamResultCheck = getResultEditor(item).join(",")

  //查询是有修改护航单巡检结果的人员
  function getResultEditor(currentGuard) {
    let guys = []
    guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Supporter.split(";"))
    guys = guys.concat(currentGuard.Responser.split(";"))
    return Array.from(new Set(guys)).filter(i => { return i != "" });
  }

  // 当前环节处理人员
  function getPerson() {
    if (item.Status == processState.onSaleApprovalId) {
      return afterSalesCheck
    }
    // 售后确认架构图审批处理人
    if (item.Status == processState.onRunningId && IsNeedConfirm && !afterSaleIsConfirm) {
      return Handler;
    }
     // 无架构特殊审批处理人
     if (item.Status == processState.onRunningId && SpecialHandler && !IsSpecial) {
      return SpecialHandler;
    }
    if (item.Status == processState.onRunningId && !isConfirm
      && (!IsNeedConfirm || (afterSaleIsConfirm && IsSpecial))) {
      return supporterCheck;
    }
    if (item.Status == processState.onExpertApprovalId) {
      return expertDispatchCheck
    }
    if (item.Status == processState.onResultApprovalId) {
      return expertWorkCheck
    }
    if (item.Status == processState.tamCheckResultId) {
      return tamResultCheck
    }
    return '无'
  }
  const loginUser = localStorage.getItem('engName');
  //  前处理人不为“无” & 护航状态 > 32 & 登陆者为护航负责人 & 提单时间 < 当前时间 < 结束时间
  const isShow = (getPerson() != '无') && (item.Status > 32) && moment().isBetween(moment(item.SubmitTime), moment(item.EndTime)) && (supporterCheck === loginUser)

  // 催单功能
  async function remind() {
    try {
      const res = await CreateGuardApprovalUrge({
        GuardId: item.GuardId,
        User: loginUser
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      message.success({ content: '催单成功' });
    } catch (err) {
      const msg = err.msg || err.toString() || '未知错误';
      message.error({ content: msg });
    }
  }
  
  return (
    <>
      <div>{getPerson()}</div>
      {isShow &&
        <Bubble
          placement='left'
          content={'点击后，护航群会对当前未审批人员@提醒关注'}
        >
          <Button onClick={() => remind()} type="weak" style={{ borderColor: '#006eff', marginTop: 10 }}>我要催单</Button>
        </Bubble>
      }
    </>
  )
}