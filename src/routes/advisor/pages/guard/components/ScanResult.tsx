import React, { useState, useEffect, useRef } from 'react';
import { message as tips, Tabs, TabPanel, Button, Collapse, StatusTip } from '@tencent/tea-component';
import { DescribeGuardScanResults } from '@src/api/advisor/guard';
import { DescribeGuardScanResultParams, GuardResultItem, GuardScanResult } from '@src/types/advisor/guard';
import { CustomerName } from './CustomerName';
import ScanResultTable from './guardEdit/ScanResultTable';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { getStorage } from '@src/utils/storage';
import TamConfirm from './guardEdit/TamConfirm';


export function ScanResult({ guardId, appid }) {
	// 当前APPID列表
	const [appids, setAppids] = useState<Array<number>>([]);
	// 当前Tab打开的Appid
	const [currentAppId, setCurrentAppId] = useState<number>(appid);
	// Tab列表
	const [tabs, setTabs] = useState<Array<{ id: string, label: React.ReactNode }>>([]);
	// Collapse展开内容
	const [activeIds, setActiveIds] = useState<Array<string>>([]);
	// 产品中文名称
	const [productDict, setProductDict] = useState({});
	// 是否有处理权限
	const [hasOpsPermission, setHasOpsPermission] = useState(false);
	// 巡检结果
	const [guardResult, setGuardResult] = useState<Array<GuardResultItem>>([]);
	// 当前登录rtx
	const rtx = getStorage('engName');
	// tam是否确认过
	const [isScanApprovalSubmit, setIsScanApprovalSubmit] = useState(false);
	// 是否需要tam确认
	const [isNeedConfirm, setIsNeedConfirm] = useState(false);
	const tamConfirmRef = useRef(null);

	const [resultLoading, setResultLoading] = useState(false);
	// 获取全量巡检结果
	const getGuardResults = async () => {
	  setResultLoading(true);
		try {
			const params: DescribeGuardScanResultParams = {
				AppId: appid,
				GuardId: guardId,
				Operator: getStorage('engName'),
			};
			const res = await DescribeGuardScanResults(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
		  setResultLoading(false);
				return;
			}
			setIsScanApprovalSubmit(res.IsScanApprovalSubmit === 1);
			setHasOpsPermission(res.HasOpsPermission);
			setGuardResult(res.ScanResult);

			const tmpAppids = [];
			res.ScanResult.map((i) => {
				tmpAppids.push(i.AppId);
			});
			const uniqAppids = Array.from(new Set(tmpAppids));
			setAppids(uniqAppids);

			const tmpTabs: Array<{ id: string, label: React.ReactNode }> = [];
			uniqAppids.forEach((appid) => {
				tmpTabs.push({
					id: appid.toString(),
					label: <CustomerName appid={appid} />,
				});
			});
			setCurrentAppId(uniqAppids[0]?.toString());
			setTabs(tmpTabs);
			setResultLoading(false);
		} catch (err) {
			setResultLoading(false);
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 获取云产品清单
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: appid,
				Env: 'all',
				TaskType: 'guardTaskType',
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setProductDict(res.ProductDict);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// result ref
	const resultRef = useRef(null);

	// init
	useEffect(() => {
		getGuardResults();
	}, []);

	useEffect(() => {
		// tam是否有未处理的风险
		const hasUnFinishRisk = guardResult.some((obj) => {
			// 检查ScanResult数组
			if (obj.ScanResult && Array.isArray(obj.ScanResult)) {
				if (obj.ScanResult.some(item => item.ProcessStatus === 0)) {
					return true;
				}
			}
			// 检查VolumeScanResult数组
			if (obj.VolumeScanResult && Array.isArray(obj.VolumeScanResult)) {
				if (obj.VolumeScanResult.some(item => item.ProcessStatus === 0)) {
					return true;
				}
			}
			return false;
		});
		// 没有tam待处理的风险并且tam未确认过，则需要显示提交按钮
		const isNeed = !hasUnFinishRisk && !isScanApprovalSubmit;
		setIsNeedConfirm(isNeed);
	}, [guardResult]);


	useEffect(() => {
		getProductsGroupsInfo();
	}, []);

	return (
	  <>
		  {
			  guardResult?.length == 0 || resultLoading
				  ?				  <div style={
					  {
						  textAlign: 'center',
						  padding: '50px 0',
					  }
				  }><StatusTip status={resultLoading ? 'loading' : 'empty'} loadingText={'正在加载中，请耐心等待...'}/></div>
				  :	<div>
						<Tabs tabs={tabs} placement={'top'} activeId={currentAppId?.toString()} onActive={(v) => {
							setCurrentAppId(Number(v.id));
						}} destroyInactiveTabPanel={false}>
							<div style={{ marginTop: 10, marginLeft: 5 }}>
								<Button
									type='link'
									onClick={() => {
										const tmp = [];
										guardResult.map((resultItem) => {
											tmp.push(resultItem.AppId + resultItem.Product);
										});
										setActiveIds(tmp);
									}}
								>{'全部展开'}</Button>
								<Button
									style={{ marginLeft: 15 }}
									type='link'
									onClick={() => {
										// resultRef.current.OnClick([]);
										setActiveIds([]);
									}}
								>{'全部折叠'}</Button>
							</div>

							{
								appids.map((appid) => {
									const targetResults = guardResult.filter((res) => {
										if (res.AppId === appid) {
											return res;
										}
									});
									let volumeResult = new Array<GuardScanResult>();
									let scanResult = new Array<GuardScanResult>();
									targetResults.map((r) => {
										volumeResult = volumeResult.concat(r.VolumeScanResult);
										scanResult = scanResult.concat(r.ScanResult);
									});
									const tabPanelList = <TabPanel
										key={appid.toString()}
										id={appid.toString()}
									>
										{
											targetResults.map((item) => {
												if (item.ScanResult.length > 0 || item.VolumeScanResult.length > 0 || item.EmergencyPlan.length > 0) {
													return (
														<Collapse style={{ paddingBottom: '10px' }} activeIds={activeIds} onActive={(v) => {
															setActiveIds(v);
														}} destroyInactivePanel={false}>
															<ScanResultTable
																key={item.Product}
																guardId={guardId}
																result={item}
																productDict={productDict}
																opsPermission={hasOpsPermission}
																rtx={rtx}
																ref={resultRef}
																isScanApprovalSubmit={isScanApprovalSubmit}
																submitScanApproval = {() => {
																	setIsScanApprovalSubmit(true);
																}}
																processSuccess = {() => {
																	getGuardResults();
																}}
															/>
														</Collapse>
													);
												}
											})
										}
									</TabPanel>;

									return tabPanelList;
								})
							}
						</Tabs>
						{
							(rtx && tabs.length > 0 && hasOpsPermission && isNeedConfirm)
                  && <div style={{ margin: 20, textAlign: 'center' }}>
                  	<Button
                  		type={'primary'}
                  		onClick={() => {
                  			tamConfirmRef?.current?.showModal();
                  		}}
                  		style={{ marginRight: 15 }}
                  	>
                        提交
                  	</Button>
                  </div>
						}
						<TamConfirm
							guardId={guardId}
							appId={appid}
							ref={tamConfirmRef}
							confirmSuccess={() => {
								getGuardResults();
							}}
						/>
					</div>
		  }
	  </>
	);
}

