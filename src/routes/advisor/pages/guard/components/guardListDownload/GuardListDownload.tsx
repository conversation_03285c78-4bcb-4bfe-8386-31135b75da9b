import React, { useState, useEffect, FC, useImperativeHandle, forwardRef } from 'react';
import './style.less';
import { Button, message, Icon, Text, Bubble, Alert, Modal } from '@tencent/tea-component';
import { IconTsa } from '@src/components/IconTsa';
import { isEmpty } from 'lodash';
import { DownloadListReport, GetDownloadTask } from '@src/api/architecture/architecture';
import { DescribeGuardFilter } from '@src/types/advisor/guard';
import { useHistory, useAegisLog } from '@tea/app';
import { getProcessEnv } from '../../../../../../../app/utils'
interface GuardListDownloadProps {
    filterProps: DescribeGuardFilter
}
let reportTimer;
const GuardListDownload = ({ filterProps }: GuardListDownloadProps, ref) => {
    // 是否是国际站
    const isAbroad = getProcessEnv() === 'production-abroad'
    const URL = `https://isa${isAbroad ? '-intl' : ''}.woa.com/advisor/privilege/apply-editor`
    const aegis = useAegisLog();
    const ctxUser = localStorage.getItem('engName');
    const operateName = localStorage.getItem('engName');
    const [guardListReport, setGuardListReport] = useState<any>({});
    const [showLoading, setShowLoading] = useState(false);
    const [showModal, setShowModal] = useState(false)
    const [showErrorModal, setShowErrorModal] = useState(false)

    //暴露回调函数给父组件
    useImperativeHandle(ref, () => ({
        InitStatus: () => {
            // 初始化下载
            reportTimer && clearInterval(reportTimer);
            reportTimer = ''
            setGuardListReport({})
            setShowLoading(false)
        }
    }));

    // 请求Excel异步下载
    const handleReportAsync = async () => {
        setShowLoading(true);
        const { startTime, endTime, guardId, guardName, standardList, industryList, statusList, created_by, responser, customerName, appId, productList, originList, projectList, participants } = filterProps;
        const filters = [
            { Name: 'guard_id', Values: guardId ? [guardId.trim()] : [] },
            { Name: 'guard_name', Values: guardName ? [guardName.trim()] : [] },
            { Name: 'standard', Values: standardList },
            { Name: 'industry', Values: industryList },
            { Name: 'status', Values: statusList },
            { Name: 'submitted_by', Values: created_by ? [created_by.trim()] : [] },
            { Name: 'responsible_person', Values: responser ? [responser.trim()] : [] },
            { Name: 'customer_name', Values: customerName ? [customerName.trim()] : [] },
            { Name: 'appid', Values: appId ? [appId.trim()] : [] },
            { Name: 'product', Values: productList },
            { Name: 'origin', Values: originList },
            { Name: 'project', Values: projectList },
            { Name: 'start_time', Values: startTime ? [startTime.format('YYYY-MM-DD 00:00:00')] : [] },
            { Name: 'end_time', Values: endTime ? [endTime.format('YYYY-MM-DD 23:59:59')] : [] }
        ];
        if (participants) {
            filters.push({ Name: 'participants', Values: [participants] })
        }
        try {
            const res = await DownloadListReport({
                AppId: 1253985742,
                ListType: 1,  // 0:map list; 1:guard list;
                Name: operateName,
                Filters: filters.filter(i => { if (i.Values.length) { return i } })
            });
            if (res.Error) {
                message.error({ content: res.Error?.Message || '生成列表错误，请联系平台管理员！' });
                setShowLoading(false)
                return;
            }
            if (res.ResultId) {
                aegis.reportEvent({
                    name: 'Click',
                    ext1: 'generate-list-btn',
                    ext2: ctxUser,
                    ext3: '护航管理/护航查询'
                })
                getDownloadTask(res.ResultId);
                reportTimer = setInterval(() => {
                    getDownloadTask(res.ResultId);
                }, 2000);
            } else {
                if (res.IdSetLength === 0) {
                    setShowErrorModal(true)
                } else {
                    message.error({ content: '生成列表错误，请联系平台管理员！' });
                }
                setShowLoading(false)
            }
        } catch (err) {
            const message = err.msg || err.toString() || '未知错误';
            message.error({ content: message });
        }
    };

    // 获取Excel异步下载结果
    const getDownloadTask = async (resultId: string) => {
        try {
            const res = await GetDownloadTask({ ResultID: resultId, AppId: 1253985742 });
            if (res.Error) {
                message.error({ content: res.Error.Message });
                return;
            }
            if (['success', 'failed'].includes(res.TaskStatus)) {
                if (res.TaskStatus === 'failed') {
                    message.error({ content: '生成列表错误，请联系平台管理员！' });
                }
                clearInterval(reportTimer);
                setGuardListReport({
                    CosUrl: res.CosUrl || '',
                    TaskStatus: res.TaskStatus || '',
                });
            }
        } catch (err) {
            const message = err.msg || err.toString() || '未知错误';
            message.error({ content: message });
        }
    };

    const handleListReportDownload = () => {
        if (guardListReport.CosUrl) {
            aegis.reportEvent({
                name: 'Click',
                ext1: 'download-list-btn',
                ext2: ctxUser,
                ext3: '护航管理/护航查询'
            })
            window.open(guardListReport.CosUrl);
        }
    }

    useEffect(() => {
        if (guardListReport.TaskStatus === 'success' || guardListReport.TaskStatus === 'failed') {
            setShowLoading(false);
        }
    }, [guardListReport]);

    return (
        <>
            {
                showLoading
                    ? <Button
                        type="text"
                        className='btn__report'
                    >
                        <Icon type="loading" />
                        <span className='text-bt'>列表生成中</span>
                    </Button>
                    :
                    !isEmpty(guardListReport) && guardListReport.TaskStatus === 'success'
                        ? <Button
                            type="text"
                            className='btn__report'
                            onClick={handleListReportDownload}
                        >
                            <div className="item-svg__box">
                                <IconTsa type='icon-export__red' className='icon-svg' />
                                <span className='item-ht color__red'>下载列表</span>
                            </div>
                        </Button>
                        :
                        <Button
                            type="text"
                            className='btn__report'
                            onClick={() => { setShowModal(true) }}
                        >
                            <div className="item-svg__box">
                                <IconTsa type='icon-export__black' className='icon-svg' />
                                <span className='item-ht'>生成列表</span>
                            </div>
                        </Button>
            }
            <Modal visible={showModal} caption="导出提示" onClose={() => { setShowModal(false) }}>
                <Modal.Body>
                    <div style={{ lineHeight: '24px' }}>
                        <div>1. 按当前筛选条件，下载非草稿状态报告及实例列表。其中，</div>
                        <div style={{ textIndent: '2em' }}>1）护航负责人及APPID负责人，仅能下载本人名下的护航实例列表</div>
                        <div style={{ textIndent: '2em' }}>2）专项角色，可以下载全部实例列表</div>
                        <div>2. 如果目标护航单的APPID没有权限，请先到[权限管理]-<Button type="link" onClick={() => { window.open(URL) }}>[我的权限]</Button>进行申请，否则导出报告可能为空。</div>
                        <div>3. 如果需要下载某个护航单信息，请在[操作]-[更多]-[导出]进行下载。</div>
                    </div>

                </Modal.Body>
                <Modal.Footer>
                    <Button type="primary" onClick={() => {
                        setShowModal(false)
                        handleReportAsync()
                    }}>
                        确定
                    </Button>
                    <Button type="weak" onClick={() => { setShowModal(false) }}>
                        取消
                    </Button>
                </Modal.Footer>
            </Modal>
            <Modal visible={showErrorModal} disableCloseIcon onClose={() => { setShowErrorModal(false) }}>
                <Modal.Body>
                    <Modal.Message icon="warning" message="导出提示" description={<span style={{ color: "#000c" }}>当前筛选条件导出列表为空，请确认您有目标护航单 APPID 的权限；如无请先到[权限管理]-[我的权限]进行申请。</span>} />
                </Modal.Body>
                <Modal.Footer>
                    <Button type="primary" onClick={() => { setShowErrorModal(false) }}>
                        确定
                    </Button>
                </Modal.Footer>
            </Modal >
        </>

    );
};
export default forwardRef(GuardListDownload);
