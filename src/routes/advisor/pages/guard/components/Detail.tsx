import React, { useState, useEffect, useMemo, useImperative<PERSON>andle, forwardRef } from 'react';
import {  Modal, Button, Card, message as tips, Select, message, TextArea, Row, Col, Text, List, Tabs, TabPanel, Bubble, InputNumber, Alert } from '@tencent/tea-component';
import moment from 'moment';
import { getInstances, getProductsGroups } from '@src/api/advisor/estimate';
import {
	CreateGuardSheet,
	DescribeGuardProductPolicy,
	BindGuardInstance,
	DescribeGuardNoRegionProduct,
	BindGuardSheet,
	DescribeArchNotSupportProduct,
} from '@src/api/advisor/guard';
import { CustomerName } from '../components/CustomerName';
import { GuardParams, InstanceItem, policyItem, ProductDescItem, ProductPolicyItem, ProductTemplateItem } from '@src/types/advisor/guard';
import _, { cloneDeep } from 'lodash';
import { Resource } from './Resource';
import { GuardContext } from '@src/routes/advisor/pages/guard/components/state/GuardContext';
import { getProcessEnv } from '../../../../../../app/utils';
import VideoBtn from '@src/components/VideoBtn';
import Loading from './Loading';
import uuid from 'react-uuid';
const Limit = 1000;
const TipLimit = 1000;
interface Props {
	currentGuard?: GuardParams,
	handleUpdateSteps: Function,
	handleUpdateStepsDisable: Function,
	handleUpdateCurrentGuard: Function,
	handleGetGuardSheet: Function,
	instanceMap?: any,
	saveFinish?: any,
	handleAutoMap?: Function
	updateHasAuthAppId?: Function
	updateApprovalData?: Function;
	autoSaveFinish?: Function
}
// 保存时的参数
let saveParams: any = {};

let timerOut;
function Detail({ currentGuard, handleUpdateSteps, handleUpdateStepsDisable,
	handleUpdateCurrentGuard, handleGetGuardSheet, instanceMap, saveFinish,
	handleAutoMap, updateHasAuthAppId, updateApprovalData, autoSaveFinish }: Props, ref) {
	// 是否是国际站
	const isAbroadSite = localStorage.getItem('site') === 'sinapore';
	// 风险评估地址
	const advisorurl = `https://console.${getProcessEnv() === 'production-abroad' ? 'intl.' : ''}cloud.tencent.com/advisor/assess`;
	// 护航单id
	const [guardId, setGuardId] = useState<number>(currentGuard.GuardId || 0);
	// 客户APPID
	// eslint-disable-next-line
	const [appId, setAppId] = useState<string>(currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : '');
	// 整体放量预估天数  ExpectedEnlargeDays
	const [expectedEnlargeDays, setExpectedEnlargeDays] = useState<number>(currentGuard.ExpectedEnlargeDays || 7);
	const expectedEnlargeDaysOptions = [{ text: '近3天', value: '3' }, { text: '近7天', value: '7' }, { text: '近15天', value: '15' }, { text: '近30天', value: '30' }];
	// 整体放量预估倍数 ExpectedEnlargeTimes
	const [expectedEnlargeTimes, setExpectedEnlargeTimes] = useState<number>(currentGuard.ExpectedEnlargeTimes || 1.5);
	// 柔性限流降级策略 LimitStrategy
	const [limitStrategy, setLimitStrategy] = useState<string>(currentGuard.LimitStrategy || '');
	// 业务压测计划 PressureTestPlan
	const [pressureTestPlan, setPressureTestPlan] = useState<string>(currentGuard.PressureTestPlan || '');
	// 业务主链路应急预案 BusinessEmergencyPlan
	const [businessEmergencyPlan, setBusinessEmergencyPlan] = useState<string>(currentGuard.BusinessEmergencyPlan || '');

	// 护航实例信息
	const [instanceTemplate, setInstanceTemplate] = useState([]);
	// 当前护航实例信息
	const [currentInstanceTemplate, setCurrentInstanceTemplate] = useState([]);
	// 护航实例dict
	const [instanceTemplateDict, setInstanceTemplateDict] = useState({});

	// 护航产品策略信息
	// eslint-disable-next-line
	const [productTemplate, setProductTemplate] = useState<Array<ProductTemplateItem>>(currentGuard.ProductTemplate || []);
	// 当前护航产品策略信息
	// eslint-disable-next-line
	const [currentProductTemplate, setCurrentProductTemplate] = useState<Array<ProductTemplateItem>>(currentGuard.ProductTemplate || []);
	// 护航产品策略值dict
	const [productTemplateDict, setProductTemplateDict] = useState({});
	// 产品维度 护航策略
	const [productPolicyDict, setProductPolicyDict] = useState<any>({});

	// 全部的appid列表
	const [appids, setappids] = useState<Array<number>>([]);
	// 当前打开appid
	const [currentAppId, setCurrentAppId] = useState<string>(currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : ''); // 默认主appid
	// 实例维度 护航容量策略
	const [instancePolicyDict, setInstancePolicyDict] = useState<any>({});
	// 实例维度 护航容量策略是否查询完毕 --避免子组件重复刷新
	const [instancePolicyGot, setInstancePolicyGot] = useState<boolean>(false);
	// 开启非空校验 CustomerName组件
	const [need, setNeed] = useState<boolean>(false);
	// 未授权appid列表
	const [appIdNAList, setAppIdNAList] = useState<Array<number>>([]);
	// 产品描述信息
	const [productDesc, setProductDesc] = useState<Array<ProductDescItem>>(currentGuard.ProductDesc || []);
	// 当前产品描述信息
	// eslint-disable-next-line
	const [currentProductDesc, setCurrentProductDesc] = useState<Array<ProductDescItem>>(currentGuard.ProductDesc || []);
	const [currentProductDescDict, setCurrentProductDescDict] = useState({});
	// 当前所有选择产品
	const [selectedProducts, setSelectedProducts] = useState([]);
	// 产品中文名称
	const [productDict, setProductDict] = useState({});
	// 兜底产品列表
	const [unSupportedProducts, setUnSupportedProducts] = useState<Array<string>>([]);
	// 授权账号、非兜底产品未勾选实例
	const [existProductWithoutInstSelected, setExistProductWithoutInstSelected] = useState<boolean>(false);
	// 全部都是兜底产品，无论是否授权账号
	const [existProductAllUnauthorized, setExistProductAllUnauthorized] = useState<boolean>(false);
	// 产品策略信息
	// const [procuctTemplate, setProcuctTemplate] = useState<Array<ProductTemplateItem>>([])

	// 全屏loading显示
	const [showLoading, setShowLoading] = useState(false);
	// 全屏loading显示文字
	const [loadingText, setLoadingText] = useState('加载中...');
	// 已经返回的数量
	const [returnNum, setRetunNum] = useState(0);
	// 无地域的产品
	const [noRegionProduct, setNoRegionProduct] = useState(null);
	// 选择的架构图信息
	const [archInfoList, setArchInfoList] = useState<any>([]);
	// 是否云架构协作权限
	const [hasAuthAppId, setHasAuthAppId] = useState([]);
	// 云架构不支持产品
	const [archNotSupportProductList, setArchNotSupportProductList] = useState([]);
	// 创建护航单
	const createOrModifyGuard = async (TemplateId) => {
		try {
			const params = {
				...currentGuard,
			};
			params.TemplateId = TemplateId;
			params.ExpectedEnlargeDays = expectedEnlargeDays;         // 整体放量预估天数
			params.ExpectedEnlargeTimes = expectedEnlargeTimes;       // 整体放量预估倍数
			params.PressureTestPlan = pressureTestPlan;               // 业务压测计划
			params.BusinessEmergencyPlan = businessEmergencyPlan;     // 业务主链路应急预案
			params.LimitStrategy = limitStrategy;                     // 柔性限流降级策略
			// params.InstanceTemplate = newInstanceTemplate            // 护航实例信息更新
			params.ProductTemplate = currentProductTemplate;          // 护航产品策略
			params.ProductDesc = currentProductDesc;                  // 产品描述信息
			params.Products = selectedProducts;                       // 所有选择产品
			params.AppId = currentGuard.MainAppId || 1253985742;
			// 保存参数
			saveParams = _.cloneDeep(params);
			const autoMapAppId = getAutoMapAppId();
			if (autoMapAppId?.length) {
				params.CloudGuardBaseInfoOtherPlatform = [];
				saveParams.CloudGuardBaseInfoOtherPlatform = [];
			}
			const res = await CreateGuardSheet(params);
			if (res.Error) {
				return Promise.reject(res.Error);
			}
			return Promise.resolve(res);
		} catch (err) {
			return Promise.reject(err);
		}
	};
	useEffect(() => {
		const totalNum = filterInstancesNoSetProduct(currentInstanceTemplate).length;
		if (totalNum >= TipLimit) {
			setLoadingText(`护航实例较多，保存进度 ${returnNum} / ${totalNum}`);
		}
	}, [returnNum]);
	// 分页保存所有实例的函数
	async function savePageInstances(Timestamp, TemplateId, InstanceTemplate) {
		try {
			const res = await BindGuardInstance({
				GuardId: currentGuard.GuardId,
				AppId: currentGuard.MainAppId || 1253985742,
				Timestamp,
				TemplateId,
				InstanceTemplate,
			});
			if (res.Error) {
				return Promise.reject(res.Error);
			}
			// 成功的数量
			setRetunNum(val => val + (InstanceTemplate?.length || 0));
			return Promise.resolve(res);
		} catch (err) {
			return Promise.reject(err);
		}
	}

	// 账号下架构图选择
	function archInfoChange(archInfo, appId) {
		setArchInfoList((oldValue) => {
			const oldList = cloneDeep(oldValue || []);
			// 在已选择的架构图列表中找到对应appId的数据
			const index = oldList.findIndex(item => item.AppId === appId);
			if (index === -1) {
				if (archInfo) {
					oldList.push({ ...archInfo, AppId: appId });
				}
			} else {
				if (archInfo) {
					oldList[index] = { ...archInfo, AppId: appId };
				} else {
					oldList.splice(index, 1);
				}
			}
			return [...oldList];
		});
	}

	// 护航单绑定架构图
	async function bindPlatform() {
		try {
			const params = {
				MainAppId: parseInt(appId, 10),
				GuardID: guardId || 0,
				CustomerName: currentGuard.CustomerName || '',
				GuardName: `${currentGuard.CustomerName}护航需求${moment().format('YYYYMMDDHH')}`,
				StartTime: moment().add(2, 'hours')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
			  	EndTime: moment().add(1, 'days')
					.startOf('hour')
					.format('YYYY-MM-DD HH:mm:ss'),
				OtherPlatforms: (archInfoList || []).map(i => ({
					Platform: i?.GuardCreateSource || 'ISA',
					PlatformUniqueId: i?.ArchId,
					AppId: i?.AppId,
				})),
				OpsType: 'add',
			};
			const res = await BindGuardSheet(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			// 调用父组件接口，重新查询
			handleGetGuardSheet(guardId.toString());
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 返回要生图的AppId
	function getAutoMapAppId() {
		// 自动生图的appId
		const autoMapAppId = [];
		hasAuthAppId.forEach((appId) => {
			// 当前appId对应的全部产品
			const appIdProduct = (saveParams?.ProductDesc || []).filter(i => i.AppId === appId)?.map(m => m.Product);
			const hasSupportedProduct = (appIdProduct || []).some(product => !unSupportedProducts.includes(product)
						   && !archNotSupportProductList.includes(product));
			const hasArch = archInfoList.findIndex(item => item.AppId === appId) !== -1;
			  if (hasSupportedProduct && !hasArch) {
				autoMapAppId.push(appId);  // 将符合条件的appId加入自动映射列表
			  }
		});
		return autoMapAppId;
	}

	// 保存接口函数
	function saveFunc(TemplateId, toPrev, toNext, noInstance = true) {
		createOrModifyGuard(TemplateId).then((saveRes) => {
			// 架构图绑定操作
			if (!isAbroadSite && archInfoList?.length) {
				bindPlatform();
			}
			// 回掉antool Iframe 通知保存成功
			window.parent?.postMessage({
				Source: 'Guard',
				SourceId: saveRes.Id,
				MsgType: 'GuardSave',
				GuardName: currentGuard.GuardName,
				StartTime: currentGuard.StartTime,
				EndTime: currentGuard.EndTime,
				Operation: (toPrev ? 'previous' : (toNext ? 'next' : 'save')),
			}, '*');
			const { Id } = saveRes;
			// 是否返回上一个页面
			if (toPrev) {
				saveFinish();
			}
			setShowLoading(false);
			message.success({ content: '护航单详细信息保存成功!' });
			saveParams.GuardId = Id;
			handleUpdateStepsDisable(false);
			// 调用父组件接口，重新查询
			handleGetGuardSheet(Id.toString());
			handleUpdateCurrentGuard(saveParams);
			if (toNext) {
				const autoMapAppId = getAutoMapAppId();
				if (autoMapAppId?.length) {
					// 如果需要自动生图,调用父组件函数，去自动生图
					handleAutoMap(autoMapAppId);
				} else {
					handleUpdateSteps();
				}
			} else {
				// 保存按钮时，必须修改当前guardid，否则会重复创建
				setGuardId(Id);
			}
		})
			.catch((err) => {
				if (noInstance) {
					console.log(err);
					message.error({
						content: '护航单保存失败，请刷新重试',
					});
					handleUpdateStepsDisable(false);
					setShowLoading(false);
				} else {
					return Promise.reject(err);
				}
			});
	}

	// 保存护航单
	const saveGuard = async (toNext: boolean, toPrev = false) => {
		setRetunNum(0);
		setLoadingText('护航实例保存中...');
		// 显示加载状态
		setShowLoading(true);
		setTimeout(() => {
			const newInstanceTemplate = filterInstancesNoSetProduct(currentInstanceTemplate);
			// 总页数
			const pages = Math.ceil(newInstanceTemplate.length / Limit);
			// 当前时间戳
			const Timestamp = moment().valueOf();
			const TemplateId = uuid();
			const resultList = [];
			for (let i = 1; i <= pages; i++) {
				setTimeout(() => {
					// 实例起止下标
					const startIndex = (i - 1) * Limit;
					const endIndex = ((i - 1) * Limit) + Limit;
					const data = newInstanceTemplate.slice(startIndex, endIndex);
					resultList.push(savePageInstances(Timestamp, TemplateId, data));
				}, 100);
			}
			if (pages === 0) {
				// 没有选择兜底产品或者是兜底账号 直接调用createOrModifyGuard保存
				saveFunc(TemplateId, toPrev, toNext, true);
			} else {
				setTimeout(async () => {
					await Promise.all([...resultList]).then(async (res) => {
						if (res?.length) {
							// 是否有某次查询没有返回值
							const hasErr = res.some(i => i.length === 0);
							if (hasErr) {
								message.error({
									content: '当前绑定实例过多，请重试，或者减少绑定的实例数量',
								});
								setShowLoading(false);
								return;
							}
							saveFunc(TemplateId, toPrev, toNext, false);
						} else {
							setShowLoading(false);
						}
					})
						.catch((err) => {
							console.log(err);
							message.error({
								content: '当前绑定实例过多，请重试，或者减少绑定的实例数量',
							});
							handleUpdateStepsDisable(false);
							setShowLoading(false);
						});
				}, 100);
			}
		}, 0);
	};

	// 过滤未选产品的实例
	function filterInstancesNoSetProduct(currentInstanceTemplate: Array<InstanceItem>) {
		const tmpInstList = [];
		currentProductDesc.map((i) => {
			currentInstanceTemplate.map((ins) => {
				if (i.Product == ins.Product) {
					let contain = false;
					tmpInstList.forEach((item) => {
						if (item.InstanceId == ins.InstanceId) {
							contain = true;
						}
					});
					if (!contain) {
						tmpInstList.push(ins);
					}
				}
			});
		});
		return tmpInstList;
	}

	// 获取容量策略
	const getGuardProductPolicy = async () => {
		try {
			const res = await DescribeGuardProductPolicy({
				AppId: parseInt(appId),
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setProductPolicyDict(res.ProductPolicy);
			setInstancePolicyDict(res.InstancePolicy);
			setInstancePolicyGot(true);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 获取云产品清单
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: 1253985742, // 公共参数
				Env: 'all',
				TaskType: 'guardTaskType',
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setUnSupportedProducts(res.UnSupportedProducts);
			setProductDict(res.ProductDict);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 获取云架构不支持的产品
	const getArchNotProduct = async () => {
		try {
			const res = await DescribeArchNotSupportProduct();
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setArchNotSupportProductList(res?.NotSupportProductList || []);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 更新未授权的appid
	function updateAppidNA(appidParams) {
		if (appidParams) {
			setAppIdNAList(i => i.concat([appidParams]));
		}
	}

	// 当前产品描述信息
	function updateProductDesc() {
		const copyProductDesc = _.cloneDeep(currentProductDesc);
		const tmpProducts = [];

		// 无论有没勾选实例的产品
		copyProductDesc.map((k) => {
			if (tmpProducts.indexOf(k.Product) === -1) {
				tmpProducts.push(k.Product);
			}
		});

		setSelectedProducts(tmpProducts);
	}

	// 检查是否存在非兜底账号的某个非兜底产品没有勾选实例
	function checkProductWithoutInstance() {
		const selectProducts = [];                  // 已选产品：非兜底
		const selectProductsWithInst = [];          // 已择产品：非兜底、有选择实例
		const selectProductsWithoutInstance = [];   // 已择产品：非兜底、没选实例
		for (const key in currentProductDescDict) {
			// key is in "appid|product" way
			if (!key.includes('|')) {
				continue;
			}
			const appid = key.split('|')[0];
			const product = key.split('|')[1];
			if (appIdNAList.includes(parseInt(appid))) {
				// 忽略兜底账号
				continue;
			}
			if (unSupportedProducts.includes(product)) {
				setExistProductAllUnauthorized(true);
				// 忽略兜底产品
				continue;
			}
			selectProducts.push(key);
		}
		for (const key in instanceTemplateDict) {
			if (!key.includes('|')) {
				continue;
			}
			const appid = key.split('|')[0];
			const product = key.split('|')[1];
			if (appIdNAList.includes(parseInt(appid))) {
				continue;
			}
			if (unSupportedProducts.includes(product)) {
				continue;
			}
			if (instanceTemplateDict[key].length == 0) {
				continue;
			}
			selectProductsWithInst.push(key);
		}
		selectProducts.map((i) => {
			if (!selectProductsWithInst.includes(i)) {
				selectProductsWithoutInstance.push(i);
			}
		});
		if (selectProductsWithoutInstance.length > 0) {
			setExistProductWithoutInstSelected(true);
		} else {
			setExistProductWithoutInstSelected(false);
		}
	}

	// 分页查询产品下的实例
	async function getPageInstances(pageSize = 1, appid, product) {
		try {
			const params = {
				AppId: appid,
				GuardId: guardId,
				Offset: (pageSize - 1) * Limit,
				Limit,
				Products: [product],
			};
			const res = await getInstances(params);
			if (res.Error) {
				return Promise.reject(res.Error);
			}
			return Promise.resolve(res.Instance || []);
		} catch (err) {
			return Promise.reject(err);
		}
	}

	// 获取所有产品的实例
	async function getAllProductInstances() {
		setLoadingText('加载中...');
		setShowLoading(true);
		const ProductDesc: any = currentGuard.ProductDesc || [];
		if (ProductDesc.length) {
			const resultList = [];
			ProductDesc.forEach((i) => {
				if (!i.InstanceIds) {
					// 单个产品的实例总数
					const instancesNum = instanceMap?.[i.AppId]?.[i.Product] || 0;
					// 单个产品的实例总页数
					const pages = Math.ceil(instancesNum / Limit);
					for (let j = 1; j <= pages; j++) {
						setTimeout(() => {
							resultList.push(getPageInstances(j, i.AppId, i.Product));
						}, 100);
					}
				}
			});
			setTimeout(async () => {
				await Promise.all(resultList).then((res) => {
					if (res?.length) {
						// 是否有某次查询没有返回值
						const hasErr = res.some(i => i.length === 0);
						if (hasErr) {
							message.error({
								content: '返回数据有误，请刷新页面',
							});
							setShowLoading(false);
							return;
						}
						// @ts-ignore
						const instances = res.flat() || [];
						setInstanceTemplate(instances);
						setCurrentInstanceTemplate(instances);
					}
					setShowLoading(false);
				})
					.catch((err) => {
						console.log(err);
						message.error({
							content: '返回数据有误，请刷新页面',
						});
						setShowLoading(false);
					});
			}, 200);
		} else {
			setShowLoading(false);
		}
	}
	// 监听expectedEnlargeDays 同步刷新currentInstanceTemplate
	useEffect(() => {
		const tmp: Array<InstanceItem> = _.cloneDeep(currentInstanceTemplate);
		tmp.map((i, index) => {
			const policy: Array<policyItem> = _.cloneDeep(i.Policy);
			policy.map((j, index1) => {
				policy[index1].Days = expectedEnlargeDays;
			});
			tmp[index].Policy = policy;
		});
		setCurrentInstanceTemplate(tmp);
	}, [expectedEnlargeDays]);

	// 监听instanceTemplate 初始化生成instanceTemplateDict
	useEffect(() => {
		const tmp = {};
		if (appids.length && instanceTemplate.length) {
			appids.map((item) => {
				const l = [];
				instanceTemplate.map((i) => {
					if (l.indexOf(i.Product) === -1 && i.AppId === item) {
						l.push(i.Product);
						const key = `${i.AppId}|${i.Product}`;
						tmp[key] = instanceTemplate.filter((j) => {
							if (j.AppId === item && j.Product === i.Product) {
								return j;
							}
						});
					}
				});
			});
		}
		setInstanceTemplateDict(tmp);
	}, [appids, instanceTemplate]);

	// 监听productTemplate 初始化生成productTemplateDict
	useEffect(() => {
		const tmp = {};
		if (appids.length && productTemplate.length) {
			appids.map((item) => {
				const l = [];
				productTemplate.map((i) => {
					if (l.indexOf(i.Product) === -1 && i.AppId === item) {
						l.push(i.Product);
						const key = `${i.AppId}^${i.Product}`;
						tmp[key] = i;
					}
				});
			});
		}
		setProductTemplateDict(tmp);
	}, [appids, productTemplate]);
	const [productsInResourceAll, setProductsInResourceAll] = useState({});
	// 监听instanceTemplateDict 刷新currentInstanceTemplate
	useEffect(() => {
		const l = [];
		for (const i in instanceTemplateDict) {
			if (instanceTemplateDict[i].length > 0) {
				instanceTemplateDict[i].forEach((item) => {
					l.push(_.cloneDeep(item));
				});
			}
		}
		l.forEach((item, j) => {
			let contain = false;
			let isSame = false;
			if (productsInResourceAll[item.AppId] != undefined) {
				isSame = true;
			}
			productsInResourceAll[item.AppId]?.forEach((el) => {
				if (item.Product == el.Product) {
					contain = true;
				}
			});
			if ((!contain && isSame || productsInResourceAll[item.AppId]?.length == 0)) {
				l.splice(j, 1, null);
			}
		});
		setCurrentInstanceTemplate(l.filter(item => item !== null));
	}, [instanceTemplateDict, productsInResourceAll]);

	useEffect(() => {
		setValidateResult((list) => {
			// 校验信息只保存当前已选择的实例
			const result = list.filter(item => currentInstanceTemplate.some(obj => obj.InstanceId === item.InstanceId));
			return result;
		});
	}, [currentInstanceTemplate]);

	// 监听productTemplateDict 刷新currentProductTemplate
	useEffect(() => {
		let l = [];
		for (const i in productTemplateDict) {
			l = l.concat(productTemplateDict[i]);
		}
		setCurrentProductTemplate(l);
	}, [productTemplateDict]);

	// 监听勾选实例或产品
	useEffect(() => {
		// 1)更新产品描述字段
		updateProductDesc();
		// 2)判断：非兜底账号、非兜底产品没有勾选实例。
		checkProductWithoutInstance();
		// 如果直接使用 currentProductDesc 会一直监听与执行状态
	}, [instanceTemplateDict, currentProductDescDict, appIdNAList, unSupportedProducts]);

	// 子组件刷新 instanceTemplate
	function updateInstanceTemplateDict(appid, product, records) {
		setInstanceTemplateDict((oldValue) => {
			const tmp = _.cloneDeep(oldValue);
			const key = `${appid}|${product}`;
			tmp[key] = records;
			return tmp;
		});
	}

	// 子组件刷新 productTemplate
	function updateDetailProductTemplateDict(appid: string, product: string, records: Array<ProductPolicyItem>) {
		const tmp = _.cloneDeep(productTemplateDict);
		const key = `${appid}^${product}`;
		const item: ProductTemplateItem = {
			AppId: Number(appid),
			Product: product,
			Regions: [],
			Policy: records,
		};
		tmp[key] = item;
		setProductTemplateDict(tmp);
	}
	const [fieldInfo, setFieldInfo] = useState({
		appid: '',
		val: '',
	});
	const [validateResult, setValidateResult] = useState([]);
	const [oneFieldInfo, setOneFieldInfo] = useState(null);
	const [providerVal, setProviderVal] = useState({
		onlyConatainUnSupportProduct: {},
		unSupportProValidate: {},
		field: {},
		changeFieldVal: (appid, val) => {
			setFieldInfo({
				appid,
				val,
			});
		},
		changeOneField: (field) => {
			setOneFieldInfo(_.cloneDeep(field));
		},
		currentAppId: currentGuard.MainAppId > 0 ? currentGuard.MainAppId.toString() : '',
		validateResult: [],
		// 保存所有实例的校验信息
		validateChange: (validateInfo) => {
			if (validateInfo.Product) {
				setValidateResult((list) => {
					// 校验信息中是否有当前校验信息
					const index = list.findIndex(i => (i.InstanceId === validateInfo.InstanceId
                        && i.MetricName === validateInfo.MetricName));
					if (index === -1) {
						list.push(validateInfo);
					} else {
						list[index] = validateInfo;
					}
					return list || [];
				});
			}
		},
	});
	useMemo(() => {
		if (!timerOut) {
			timerOut = setTimeout(() => {
				providerVal.validateResult = validateResult;
				setProviderVal(_.cloneDeep(providerVal));
				clearTimeout(timerOut);
				timerOut = null;
			}, 20);
		}
	}, [validateResult]);
	useMemo(() => {
		if (fieldInfo.appid) {
			providerVal.field[fieldInfo.appid] = fieldInfo.val;
			setProviderVal(_.cloneDeep(providerVal));
		}
	}, [fieldInfo]);
	useMemo(() => {
		if (oneFieldInfo) {
			providerVal.field = _.cloneDeep(oneFieldInfo);
			setProviderVal(_.cloneDeep(providerVal));
		}
	}, [oneFieldInfo]);
	useMemo(() => {
		if (productTemplate?.length > 0) {
			productTemplate.forEach((item) => {
				let containProduct = false;
				currentProductDesc.forEach((el) => {
					if (item.AppId === el.AppId && el.Product === item.Product) {
						containProduct = true;
					}
				});
				if (!containProduct) {
					return;
				}
				if (providerVal.field[item.AppId] == undefined) {
					providerVal.field[item.AppId] = {};
					providerVal.field[item.AppId][item.Product] = {};
				} else {
					providerVal.field[item.AppId][item.Product] = {};
				}
				if (item.Policy?.length > 0 && currentGuard.Products?.includes(item.Product)) {
					item.Policy.forEach((el) => {
						// 如果是自助护航，则不校验
						if (el.IsRequired && currentGuard.Standard !== 3) {
							providerVal.field[item.AppId][item.Product][el.MetricName] = !el.Value;
						}
					});
				};
			});
			setProviderVal(_.cloneDeep(providerVal));
		}
	}, [productTemplate, currentGuard]);

	useMemo(() => {
		for (const appId in productsInResourceAll) {
			if (providerVal.unSupportProValidate[appId] == undefined) {
				providerVal.unSupportProValidate[appId] = {};
			}
			if (providerVal.field[appId] == undefined) {
				providerVal.field[appId] = {};
			}
			const currentUnsupportProduct = [];
			let num = 0;
			productsInResourceAll[appId]?.map((i) => {
				if (unSupportedProducts.includes(i.Product) || appIdNAList.includes(i.AppId)) {
					num++;
					if (providerVal.unSupportProValidate[i.AppId]?.[i.Product] == undefined && i.InstanceIds) {
						providerVal.unSupportProValidate[i.AppId][i.Product] = false;
					} else if (providerVal.unSupportProValidate[i.AppId]?.[i.Product] == undefined) {
						// providerVal['unSupportProValidate'][i.AppId][i.Product] = 'init';
						providerVal.unSupportProValidate[i.AppId][i.Product] = true;
					} else if (providerVal.unSupportProValidate[i.AppId]?.[i.Product] == 'init' && !i.InstanceIds) {
						// providerVal['unSupportProValidate'][i.AppId][i.Product] = 'init';
						providerVal.unSupportProValidate[i.AppId][i.Product] = true;
					} else if (providerVal.unSupportProValidate[i.AppId]?.[i.Product] === false
                        && i.InstanceIds == undefined) {
						providerVal.unSupportProValidate[i.AppId][i.Product] = false;
					} else {
						providerVal.unSupportProValidate[i.AppId][i.Product] = !i.InstanceIds;
					}
					currentUnsupportProduct.push(i.Product);
				}
			});
			for (const key in providerVal.unSupportProValidate[appId]) {
				if (!currentUnsupportProduct.includes(key)) {
					providerVal.unSupportProValidate[appId][key] = undefined;
				}
			}
			if (productsInResourceAll?.[appId]?.length == num && num > 0) {
				providerVal.onlyConatainUnSupportProduct[appId] = true;
			} else {
				providerVal.onlyConatainUnSupportProduct[appId] = false;
			}
		}
		setProviderVal(_.cloneDeep(providerVal));
	}, [unSupportedProducts, productsInResourceAll, appids, appIdNAList]);

	// 子组件(APPID粒度)刷新 ProductDesc
	function handleUpdateProductDescInDetail(appid: number, productsInResource: Array<ProductDescItem>) {
		const desc = _.cloneDeep(currentProductDesc.filter(i => appid == i.AppId));
		const newProductsInResource = [];
		// 1)重置覆盖该APPID的产品选项
		const tmp = _.cloneDeep(currentProductDesc.filter(i => appid != i.AppId));
		productsInResource.map((i) => {
			let obj = null;
			desc.forEach((item) => {
				if (i.Product == item.Product) {
					obj = {
						...item,
						InstanceIds: i.InstanceIds !== undefined ? i.InstanceIds : item.InstanceIds,
						Comment: i.Comment !== undefined ? i.Comment : item.Comment,
					};
				};
			});

			if (obj) {
				newProductsInResource.push(_.cloneDeep(obj));
				tmp.push(_.cloneDeep(obj));
			} else {
				newProductsInResource.push({
					AppId: appid,
					Product: i.Product,
					InstanceIds: i.InstanceIds,
					Comment: i.Comment,
				});
				tmp.push({
					AppId: appid,
					Product: i.Product,
					InstanceIds: i.InstanceIds,
					Comment: i.Comment,
				});
			}
		});

		productsInResourceAll[appid] = newProductsInResource;
		if (productDesc?.length > 0) {
			productDesc.forEach((item) => {
				if (productsInResourceAll[item.AppId] === undefined) {
					productsInResourceAll[item.AppId] = [_.cloneDeep(item)];
				} else {
					let had = false;
					productsInResourceAll[item.AppId]?.forEach((el) => {
						if (el.Product == item.Product) {
							had = true;
						}
					});
					if (!had) {
						productsInResourceAll[item.AppId].push(_.cloneDeep(item));
					}
				}
			});
			setProductDesc([]);
		}
		setProductsInResourceAll(_.cloneDeep(productsInResourceAll));
		setCurrentProductDesc(tmp);
		// 2)更新当前产品描述信息
		const pd = {};
		tmp.map((i) => {
			pd[`${i.AppId}|${i.Product}`] = {};
		});
		setCurrentProductDescDict(pd); // useEffect: productDescDict -> updateProductDesc() ->

		// 3)全部都是兜底产品
		let flag = true;
		tmp.map((i) => {
			if (!unSupportedProducts.includes(i.Product)) {
				flag = false;
			}
		});
		if (tmp.length > 0) {
			setExistProductAllUnauthorized(flag);
		}
	}

	// 获取无地域的产品
	const getDescribeGuardNoRegionProduct = async () => {
		try {
			const res = await DescribeGuardNoRegionProduct({ AppId: 1253985742 });
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setNoRegionProduct(res.NoRegionProducts || []);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 自动保存护航单
	function autoSaveGuard(TemplateId) {
		createOrModifyGuard(TemplateId)
			.then((saveRes) => {
				handleGetGuardSheet(saveRes.Id.toString());
			})
			.catch(() => {
				message.error({
					content: '护航单保存失败，请刷新重试',
				});
			})
			.finally(() => {
				autoSaveFinish();
			});
	}

	// 自动保存
	function autoSaveFunc() {
		setTimeout(() => {
			const newInstanceTemplate = filterInstancesNoSetProduct(currentInstanceTemplate);
			// 总页数
			const pages = Math.ceil(newInstanceTemplate.length / Limit);
			// 当前时间戳
			const Timestamp = moment().valueOf();
			const TemplateId = uuid();
			const resultList = [];
			for (let i = 1; i <= pages; i++) {
				setTimeout(() => {
					// 实例起止下标
					const startIndex = (i - 1) * Limit;
					const endIndex = ((i - 1) * Limit) + Limit;
					const data = newInstanceTemplate.slice(startIndex, endIndex);
					resultList.push(savePageInstances(Timestamp, TemplateId, data));
				}, 100);
			}
			if (pages === 0) {
				// 没有选择兜底产品或者是兜底账号 直接调用createOrModifyGuard保存
				autoSaveGuard(TemplateId);
			} else {
				setTimeout(async () => {
					await Promise.all([...resultList]).then(async (res) => {
						if (res?.length) {
							// 是否有某次查询没有返回值
							const hasErr = res.some(i => i.length === 0);
							if (hasErr) {
								message.error({
									content: '当前绑定实例过多，请重试，或者减少绑定的实例数量',
								});
								// 自动保存成功，回掉外层
								autoSaveFinish();
								return;
							}
							autoSaveGuard(TemplateId);
						} else {
							// 自动保存成功，回掉外层
							autoSaveFinish();
						}
					})
						.catch(() => {
							message.error({
								content: '当前绑定实例过多，请重试，或者减少绑定的实例数量',
							});
							// 自动保存成功，回掉外层
							autoSaveFinish();
						});
				}, 100);
			}
		}, 0);
	}

	// 页面初始化
	useEffect(() => {
		getAllProductInstances();
		getGuardProductPolicy();
		getProductsGroupsInfo();
		getDescribeGuardNoRegionProduct();
		// 国内站才调用
		if (getProcessEnv() !== 'production-abroad') {
			getArchNotProduct();
		}
	}, []);

	// 暴露回调函数给父组件
	useImperativeHandle(ref, () => ({
		OnClick: async (toNext, toPrev, autoSave) => {
			if (autoSave) {
				autoSaveFunc();
				return;
			}
			// 开启非空校验
			setNeed(true);
			// 进行非空校验
			let flag = false;
			appids.map((i) => {
				const unAuthorized = appIdNAList.includes(i);
				// 判断是否勾选实例(未授权APPID允许实例为空)
				if ((currentInstanceTemplate.filter((j) => {
					if (j.AppId === i) {
						return j;
					}
				}).length === 0 || existProductWithoutInstSelected) && !providerVal.onlyConatainUnSupportProduct[i]) {
					if (!unAuthorized || existProductWithoutInstSelected) {
						flag = true;
						// 允许授权APPID选择的产品全是兜底的情况下保存或下一步
						// if (providerVal['onlyConatainUnSupportProduct'][i]) {
						// 	num++;
						// }
					};
					if (unAuthorized) {
						flag = true;
					}
				}
				for (const key in providerVal.unSupportProValidate[i]) {
					if (providerVal.unSupportProValidate[i][key] == 'init' || providerVal.unSupportProValidate[i][key] === true) {
						flag = true;
						providerVal.unSupportProValidate[i][key] = true;
					}
				};
				for (const key in providerVal.field[i]) {
					const proVal = providerVal.field[i][key];
					for (const prop in proVal) {
						if (proVal[prop] == 'init' || proVal[prop] === true) {
							flag = true;
							proVal[prop] = true;
						};
					};
				};
			});
			// if (num == appids.length) {
			// 	flag = false;
			// }
			setProviderVal(_.cloneDeep(providerVal));
			// console.log(providerVal)
			// return
			if (flag) {
				message.error({ content: '必填资源信息未填写' });
				return false;
			}
			// 找到第一个没有过校验的的实例，进行提示
			const noPass = (validateResult || []).find(i => i.PassValidate === false);
			if (noPass) {
				message.error({
					content: `${noPass.AppId} 的 ${productDict[noPass.Product]} 中实例ID为 ${noPass.InstanceId} 的实例信息未完善`,
				});
				return false;
			}
			// 非国际站，或者自助护航，或者上一步，不展示确认弹框，直接保存
			if (isAbroadSite || currentGuard.Standard === 3 || toPrev) {
				saveGuard(toNext, toPrev);// 调用接口创建或修改护航单基础信息
			} else {
				saveGuard(toNext, toPrev);// 调用接口创建或修改护航单基础信息
			}
			handleUpdateStepsDisable(true);
			return true;
		},
	}));

	// 页面初始化--根据appids生成tabs
	useEffect(() => {
		const tmp: Array<number> = [];
		tmp.push(currentGuard.MainAppId);
		currentGuard.RelatedAppId.map((i) => {
			if (tmp.indexOf(i) === -1) {
				tmp.push(i);
			}
		});
		setappids(tmp);
	}, []);

	const tabs: Array<{ id: string, label: React.ReactNode }> = useMemo(() => {
		const tmp: Array<{ id: string, label: React.ReactNode }> = [];
		appids.map((i) => {
			tmp.push({
				id: i.toString(),
				label: <CustomerName appid={i} need={need} allUnAuthProducts={existProductAllUnauthorized}
					instanceTemplate={currentInstanceTemplate} handleUpdateAppidNA={(i) => {
						updateAppidNA(i);
					}} />,
			});
			// getAppIdIsAuthorized(i)
		});
		return tmp;
	}, [appids, need, currentInstanceTemplate]);

	// 移除指定appId，product的校验结果
	function removeValidate(val, appId, product) {
		if (val) {
			// 先判断校验中是否存在 将要移除的，避免死循环
			const judge = validateResult.find(item => item.AppId === appId && item.Product === product);
			judge && setValidateResult((list) => {
				const result = list.filter(item => !(item.AppId === appId && item.Product === product));
				return result;
			});
		}
	}

	return (
		<GuardContext.Provider value={
			providerVal
		}>
			<div>
				<Card style={{ margin: 10 }}>
					<Card.Body>
						<Row gap={30}>
							<Col>
								<section>
									<Row verticalAlign={'middle'}>
										<Col span={6} >
											<Text style={{ color: 'red' }} verticalAlign="middle">*</Text><Text theme="label" verticalAlign="middle">放量预估</Text>
										</Col>
										<Col span={18}>
											<Row verticalAlign={'middle'}>
												<Text style={{ padding: '6px 10px' }}>相比</Text>
												<Select
													size="s"
													appearance='button'
													options={expectedEnlargeDaysOptions}
													value={expectedEnlargeDays.toString()}
													onChange={(v) => {
														setExpectedEnlargeDays(parseInt(v));
													}}
												/>
												<Text style={{ padding: '6px 10px' }}>增加</Text>
												<InputNumber
													style={{ margin: 5 }}
													onChange={(value) => {
														setExpectedEnlargeTimes(value);
													}}
													value={expectedEnlargeTimes}
													min={1}
													step={0.1}
													size="m"
												/>
												<Text>倍</Text>
											</Row>
										</Col>
									</Row>
								</section>
								<section>
									<Row verticalAlign={'middle'} style={{ marginTop: '81px' }}>
										<Col span={6} style={{ height: '120px' }}>
											<Text theme="label">业务压测计划</Text>
										</Col>
										<Col span={18}>
											<TextArea
												size="full"
												value={pressureTestPlan}
												onChange={(v) => {
													setPressureTestPlan(v);
												}}
											/>
										</Col>
									</Row>
								</section>
							</Col>
							<Col>
								<section>
									<Row verticalAlign={'middle'}>
										<Col span={6} style={{ height: '120px' }}>
											<Text theme="label">柔性限流降级策略</Text>
										</Col>
										<Col span={18}>
											<TextArea
												size="full"
												value={limitStrategy}
												onChange={(v) => {
													setLimitStrategy(v);
												}}
											/>
										</Col>
									</Row>
								</section>
								<section>
									<Row verticalAlign={'middle'}>
										<Col span={6} style={{ height: '120px' }}>
											<Text theme="label">业务主链路应急预案</Text>
										</Col>
										<Col span={18}>
											<TextArea
												size="full"
												value={businessEmergencyPlan}
												onChange={(v) => {
													setBusinessEmergencyPlan(v);
												}}
											/>
										</Col>
									</Row>
								</section>
							</Col>
						</Row>
					</Card.Body>
				</Card>

				<Card>
					<Card.Body title={
						<div>
							<Bubble
								escapeWithReference
								placement={'top'}
								content={'某个产品没有选择护航实例'}
								visible={existProductWithoutInstSelected} error>
								<Text>资源信息收集</Text>
							</Bubble>
						</div>
					}>

						<Alert>
							<h4>重点说明</h4>
							{isAbroadSite ? <List>
								 <List.Item>1、新购买的实例搜不到？新购买实例默认次日可以搜到；或者在云顾问控制台的 <Button type="link"
									 onClick={() => {
										window.open(advisorurl);
									}}
									 style={{
										 verticalAlign: 'baseline',
										 textDecoration: 'underline',
									 }}>风险评估</Button> 中点击 [开始评估] 触发巡检，在巡检结束后可以搜到</List.Item>
								 <List.Item>2、目标护航产品不在选项列表？说明产品暂未接入云顾问，请反馈在 <Button type="link"
									 onClick={() => {
										window.open('https://iwiki.woa.com/p/4007660459');
									}}
									 style={{
										 verticalAlign: 'baseline',
										 textDecoration: 'underline',
									 }}>需求反馈</Button>，后续推动接入</List.Item>
								 <List.Item>3、无法确切知道用户放量倍数或峰值？护航专项人员可能因此无法提供具体资源 buffer，请与客户确认后补充，或者备注说明情况</List.Item>
								 <List.Item>更多请查看 <Button type="link"
									 onClick={() => {
										window.open('https://iwiki.woa.com/pages/viewpage.action?pageId=**********#%E8%B5%84%E6%BA%90%E4%BF%A1%E6%81%AF%E6%94%B6%E9%9B%86');
									}}
									 style={{
										 verticalAlign: 'baseline',
										 textDecoration: 'underline',
									 }}>资源信息收集操作指引</Button> </List.Item>
							 </List>
							 : <List>
									<List.Item>1、<span className='suggesstion'>强烈建议</span> 基于客户架构图发起护航，无需逐个选择产品/实例，节点监控随时查看。
										<VideoBtn></VideoBtn>
									</List.Item>
									<List.Item>2、新购实例搜不到？可请客户在云顾问控制台“概览”中点击[开始评估]触发巡检，从架构图读取的资源则先更新架构图。</List.Item>
									<List.Item>3、无法确切知道客户业务放量倍数或峰值？护航专项人员可能因此无法提供具体资源 buffer，请与客户确认后补充，或者备注说明情况</List.Item>
									<List.Item>更多请查看
										<Button type="link"
											className='tipLinkBtn'
											onClick={() => {
												window.open('https://csig.lexiangla.com/teams/k100630/docs/91494b5462f011ef81d5427b769b846d');
											}}
										>如何在架构图发起护航
										</Button>
										<Button type="link"
											className='tipLinkBtn'
											onClick={() => {
												window.open('https://iwiki.woa.com/pages/viewpage.action?pageId=**********#%E8%B5%84%E6%BA%90%E4%BF%A1%E6%81%AF%E6%94%B6%E9%9B%86');
											}}
										>资源信息收集操作指引
										</Button>
									</List.Item>
								</List>}
						</Alert>

						{
							(tabs.length > 0 && instancePolicyGot && noRegionProduct)
                            && <Tabs tabs={tabs} placement={'top'} activeId={currentAppId} onActive={(v) => {
                            	setCurrentAppId(v.id);
                            	providerVal.currentAppId = v.id;
                            	setProviderVal(_.cloneDeep(providerVal));
                            }} destroyInactiveTabPanel={false}>
                            	{
                            		appids.map(i => (
                            			<TabPanel key={i} id={i.toString()} forceRender={true}>
                            				<Resource
                            					noRegionProduct={noRegionProduct}
                            					currentGuard={currentGuard}
                            					removeValidate={removeValidate}
                            					key={i}
                            					appid={i}
                            					isAppidAuthorized={!appIdNAList.includes(i)}
                            					productDesc={currentProductDesc}
                            					instanceTemplate={instanceTemplate}
                            					instancePolicyDict={instancePolicyDict}
                            					productTemplate={productTemplate}
                            					productPolicyDict={productPolicyDict}
                            					expectedEnlargeDays={expectedEnlargeDays}
                            					expectedEnlargeTimes={expectedEnlargeTimes}
                            					// eslint-disable-next-line max-len
                            					updateInstanceTemplateDict={(i, product, records) => updateInstanceTemplateDict(i, product, records)}
                            					updateProductTemplateDict={(i, product, records) => {
                            						updateDetailProductTemplateDict(i, product, records);
                            					}}
                            					// eslint-disable-next-line max-len
                            					updateProductDesc={(i, productsInResource) => handleUpdateProductDescInDetail(i, productsInResource)}
                            					currentInstanceTemplate={currentInstanceTemplate}
                            					guardTime={[currentGuard.StartTime, currentGuard.EndTime]}
                            					updateArchInfo={(archInfo, appId) => archInfoChange(archInfo, appId)}
                            					updateHasAuthAppId={(appId) => {
                            						setHasAuthAppId(oldaVal => [...oldaVal, appId]);
                            						updateHasAuthAppId(appId);
                            					}}
                            					updateApprovalData={approvalData => updateApprovalData(approvalData)}
                            				/>
                            			</TabPanel>))
                            	}
                            </Tabs>
						}
					</Card.Body>
				</Card>
			</div >
			{/* 全屏遮罩 */}
			<Loading show={showLoading} text={loadingText}></Loading>
		</GuardContext.Provider>
	);
}

export default forwardRef(Detail);
