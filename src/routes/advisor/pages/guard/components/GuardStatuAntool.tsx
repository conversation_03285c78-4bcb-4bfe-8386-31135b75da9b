import React, { useState, useEffect } from 'react';
import { Bubble, Timeline, Text, Icon, Button, message } from '@tencent/tea-component';
import { StatusDict } from '@src/types/advisor/guard';
import { fromAntool, getUrlParamFromLocation } from '@src/utils/common';
import { DescribeGuardSheet } from '@src/api/advisor/guard';
//流程状态ID
const processState = {
  onNoteId: 1,              // 草稿状态ID
  submitId: 2,              // 订单已提交状态ID
  onSaleApprovalId: 31,     // 售后审批状态ID
  onRunningId: 32,          // 正在巡检中状态ID
  onExpertApprovalId: 33,   // 专项分配人员状态ID
  onResultApprovalId: 34,   // 巡检结果审核状态ID
  onReportGenerating: 36,   // 巡检报告生成中
  onReportGenerated: 37,    // 巡检报告已完成
  instanceAltering: 40,     // 实例修改中ID
  instanceAlteredRun: 41,   // 实例修改后运行中ID
  tamCheckResultId: 48,     // 巡检风险审批（TAM）
  onFinishId: 50,           // 护航巡检完成状态ID
  processStopId: -1,        // 流程中止状态ID
  scanFailedId: -2,         // 巡检异常状态ID
  approvalFailedId: -3,     // 审核异常状态ID
  deletedId: -50,           // 已删除
}
const notInProcessList = [processState.instanceAltering, processState.instanceAlteredRun]

export default function GuardStatu({ currentGuard }) {
  // 是否是通过antool访问
  const antool = fromAntool()
  const [item, setItem] = useState(antool ? {} : (currentGuard || {}))
  const isConfirm = item.Approvals?.AfterSalesStatus?.IsConfirm
  const afterSalesCheck = [
    item.Approvals?.AfterSalesStatus?.Handler || '',
    item.Approvals?.AfterSalesStatus?.IsApproved ? " - 已审批" : " - 审批中", <div></div>
  ]
  const supporterCheck = [
    item.Approvals?.AfterSalesStatus?.Supporter || '',
    item.Approvals?.AfterSalesStatus?.IsConfirm ? " - 已审批" : " - (MyOA)审批中", <div></div>
  ]
  const expertDispatchCheck = (item.Approvals?.ExpertStatus || []).map(ap => {
    let apHandler = (ap.Handler ? "(" + ap.Handler + ")" : "")
    return [<strong>{ap.ProductName}</strong>, apHandler, ap.IsFinished ? (ap.State == -1 ? " - 已驳回" : " - 已审批") : " - 审批中", <div></div>]
  })
  const expertWorkCheck = (item.Approvals?.ScanResultStatus || []).map(ap => {
    let apHandler = (ap.Handler ? "(" + ap.Handler + ")" : "")
    return [<strong>{ap.ProductName}</strong>, apHandler, ap.IsFinished ? (ap.State == -1 ? " - 已驳回" : " - 已审批") : " - 审批中", <div></div>]
  })
  const tamResultCheck = [
    getResultEditor(item).join(","),
    (item.Status == processState.onFinishId && item.RiskData?.HighRiskStrategyCount === 0 && item.RiskData?.MediumRiskStrategyCount === 0) ? " - 已审批" : " - 审批中（在 [操作]-[更多]-[处理巡检结果] 实施）",
    <div></div>
  ]

  useEffect(() => {
    // 如果是antool，就需要查询详情
    if (antool) {
      getGuardSheet()
    }
  }, [])

  //查询是有修改护航单巡检结果的人员
  function getResultEditor(guard) {
    let guys = []
    guys = guys.concat(guard.Approvals?.AfterSalesStatus?.Supporter?.split(";") || [])
    guys = guys.concat(guard.Responser?.split(";") || [])

    return Array.from(new Set(guys)).filter(i => { return i != "" });
  }

  // 查询护航单详情
  const getGuardSheet = async () => {
    try {
      const { guardId }: any = getUrlParamFromLocation(['guardId'], location);
      const res = await DescribeGuardSheet({
        Filters: [{ Name: 'guard_id', Values: [guardId] }],
        Offset: 0,
        Limit: 10,
        AppId: 1253985742, // 接口必须传appid
      });
      if (res.Error) {
        const msg = res.Error.Message;
        message.error({ content: msg });
        return;
      }
      const item = res.Guard[0] || {};
      setItem(item);
    } catch (err) {
      const msg = err.msg || err.toString() || '未知错误';
      message.error({ content: msg });
    }
  };

  return (
    <>
      {(item.Status > 0 && !notInProcessList.includes(item.Status)) &&
        <Timeline mode="vertical" dashedLine style={{ height: "auto" }}>
          <Timeline.Item
            label="草稿"
            style={{ height: 35 }}
            theme={item.Status > processState.onNoteId ? "success" : "default"}
            icon={item.Status == processState.onNoteId
              ? <Icon type="loading" />
              : item.Status > processState.onNoteId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label="订单已提交"
            style={{ height: 35 }}
            theme={item.Status > processState.submitId ? "success" : "default"}
            icon={item.Status == processState.submitId
              ? <Icon type="loading" />
              : item.Status > processState.submitId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label={
              <>
                <Text>{"售后审批（TAM）"}</Text>
              </>
            }
            style={{ height: 35 }}
            theme={item.Status > processState.onSaleApprovalId ? "success" : "default"}
            icon={item.Status == processState.onSaleApprovalId
              ? <Icon type="loading" />
              : item.Status > processState.onSaleApprovalId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label={
              <>
                <Text>{"护航负责人审批"}</Text>
              </>
            }
            style={{ height: 35 }}
            theme={isConfirm ? "success" : "default"}
            icon={isConfirm ? <Icon type="success" /> : item.Status > processState.onSaleApprovalId ? <Icon type="loading" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label="正在巡检中"
            style={{ height: 35 }}
            theme={item.Status > processState.onRunningId ? "success" : "default"}
            icon={(item.Status == processState.onRunningId && isConfirm)
              ? <Icon type="loading" />
              : item.Status > processState.onRunningId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label={
              <>
                <Text>{"专项人员分配（专项接口人）"}</Text>
              </>
            }
            style={{ height: 35 }}
            theme={item.Status > processState.onExpertApprovalId ? "success" : "default"}
            icon={item.Status == processState.onExpertApprovalId
              ? <Icon type="loading" />
              : item.Status > processState.onExpertApprovalId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label={
              <>
                <Text>{"巡检结果审批（专项Owner）"}</Text>
              </>
            }
            style={{ height: 35 }}
            theme={item.Status > processState.onResultApprovalId ? "success" : "default"}
            icon={item.Status == processState.onResultApprovalId
              ? <Icon type="loading" />
              : item.Status > processState.onResultApprovalId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label={
              <>
                <Text>{"巡检风险审批（TAM）"}</Text>
              </>
            }
            style={{ height: 35 }}
            theme={item.Status > processState.tamCheckResultId ? "success" : "default"}
            icon={item.Status == processState.tamCheckResultId
              ? <Icon type="loading" />
              : item.Status > processState.tamCheckResultId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
          <Timeline.Item
            label="护航巡检已完成"
            style={{ height: 5 }}
            theme={item.Status == processState.onFinishId ? "success" : "default"}
            icon={item.Status == processState.onFinishId ? <Icon type="success" /> : <Icon type="cur" />}>
          </Timeline.Item>
        </Timeline >
      }
      {
        [processState.instanceAltering, processState.instanceAlteredRun].includes(item.Status) &&
        <Timeline.Item label=" 护航实例变更巡检中 ... " style={{ height: 5 }} theme={"default"} icon={<Icon type="loading" />}></Timeline.Item>
      }
      {
        item.Status == processState.processStopId &&
        <Timeline.Item label=" 流程中止 :( " style={{ height: 5 }} theme={item.Status === processState.processStopId ? "danger" : "default"}></Timeline.Item>
      }
      {
        item.Status == processState.scanFailedId &&
        <Timeline.Item label=" 巡检任务异常，请联系管理员 :( " style={{ height: 5 }} theme={item.Status === processState.scanFailedId ? "danger" : "default"}></Timeline.Item>
      }
      {
        item.Status == processState.approvalFailedId &&
        <Timeline.Item label=" 审核任务异常，请联系管理员 :( " style={{ height: 5 }} theme={item.Status === processState.approvalFailedId ? "danger" : "default"}></Timeline.Item>
      }
    </>
  )
}