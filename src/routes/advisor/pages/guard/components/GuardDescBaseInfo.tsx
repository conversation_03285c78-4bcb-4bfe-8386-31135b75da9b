import React, { useState, useEffect, useMemo } from 'react';
import {
	message as tips,
	Row,
	Col,
	message,
	Icon,
	Tabs,
	TabPanel
} from '@tencent/tea-component';
import { Text } from "@tencent/tea-component";
import { DescribeGuardProjects } from '@src/api/advisor/guard';
import { GuardParams, StandardDict, StatusDict } from '@src/types/advisor/guard';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { DescInstancePanel } from "@src/routes/advisor/pages/guard/components/DescInstancePanel";
import { CustomerName } from "@src/routes/advisor/pages/guard/components/CustomerName";
import { useCheckIfAuthed, NoPermission, PermissionLoading } from '../../broadcast/hooks/checkIfAuthed';


interface Props {
	guardBaseInfo: GuardParams;
	appids: Array<string>,
	appidAuthorizedMap: any,
	guardId?: string,
}

function MergeRelatedCustormer(appidArr, nameArr) {
	let concatArr = new Array()
	for (let i = 0; i < appidArr.length; ++i) {
		let appendName = "|" + nameArr[i]
		if (typeof nameArr[i] === 'undefined') {
			// 如果客户名称未定义，则 appid|name 变为 appid
			appendName = ""
		}
		concatArr.push(appidArr[i] + appendName)
	}
	return concatArr.join(", ")
}

function GuardDescBaseInfo({ guardBaseInfo, appids, appidAuthorizedMap, guardId }: Props) {
	//护航项目选项
	const [projectOptions, setProjectOptions] = useState([])
	const [projectDict, setProjectDict] = useState(new Map());
	const { isAuth, loading: authLoading } = useCheckIfAuthed({
		pageStatus: 'guard-summary',
		key: 'GuardId',
		value: guardId,
	});
	// 护航详细字段
	const [onsiteProductZh, setOnsiteProductZh] = useState([])                           // 需要驻场支持的云产品
	const guardName = guardBaseInfo.GuardName                                            // 护航名称
	const standard = guardBaseInfo.Standard                                              // 护航类型
	const startDate = (guardBaseInfo.StartTime || "")                     // 护航开始时间
	const endDate = (guardBaseInfo.EndTime || "")                         // 护航结束时间
	const guardTime = (startDate === endDate) ? startDate : (startDate + " ~ " + endDate)
	const keyTime = (guardBaseInfo.ImportantGuardTime || []).join(", ")                             // 护航重点时间
	const onsiteDates = (guardBaseInfo.OnsiteTime || []).join(", ")                      // 驻场日期列表
	const project = guardBaseInfo.Project                                                // 护航项目
	const appId = guardBaseInfo.MainAppId                                                // 客户APPID
	const relatedAppId = guardBaseInfo.RelatedAppId || []                                // 关联客户appid列表
	const relatedCustomerNames = guardBaseInfo.RelatedCustomerNames || []                // 关联客户名称，顺序和appid列表一致
	const relatedCustomer = MergeRelatedCustormer(relatedAppId, relatedCustomerNames)
	const customerName = guardBaseInfo.CustomerName                                      // 客户名称
	const customerContacts = guardBaseInfo.CustomerContacts                              // 客户接口人名称
	const customerPhone = guardBaseInfo.CustomerPhone                                    // 客户接口人联系方式
	const businessEmergencyPlan = guardBaseInfo.BusinessEmergencyPlan                    // 业务主链路应急预案
	const pressureTestPlan = guardBaseInfo.PressureTestPlan                              // 业务压测计划
	const limitStrategy = guardBaseInfo.LimitStrategy                                    // 柔性限流降级策略
	const expectedEnlargeDays = guardBaseInfo.ExpectedEnlargeDays                        // 整体放量预估天数
	const expectedEnlargeTimes = guardBaseInfo.ExpectedEnlargeTimes                      // 整体放量预估倍数
	const expectedEnlarge = `对比近 ${expectedEnlargeDays} 天业务放量 ${expectedEnlargeTimes} 倍`

	//获取未授权账号
	const [appidNASet, setAppidNASet] = useState([])
	//护航未填写字段
	const noSet = <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>
	const naText = ["客户（APPID:", <Text>{appidNASet.join(" ")}</Text>, "）在建单时候尚未开通云顾问，当前无法提供全面护航能力（自动巡检、监控面板生成、播报等），仅支持护航人员分配。"]

	//查询护航项目清单
	const getProjects = async () => {
		try {
			const res = await DescribeGuardProjects({
				AppId: appId || 1253985742, //接口必须传appid  为获取全量产品列表，因此传内部中心账号
			})
			if (res.Error) {
				let msg = res.Error.Message
				tips.error({ content: msg });
				return
			} else {
				let tmp = []
				let m = new Map()
				res.Projects.map(i => {
					tmp.push({ value: i.Id.toString(), text: i.Name })
					m.set(i.Id, i.Name)
				})
				setProjectOptions(tmp)
				setProjectDict(m)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	//获取云产品中文描述
	const getProductsGroupsInfo = async (onsiteProduct) => {
		try {
			const res = await getProductsGroups({
				AppId: 1253985742, // CGI代码直接拉取advisor_strategy或advisor_product表，暂无appid指定。
				Env: 'all',
				TaskType: 'guardTaskType',
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				let onsiteProductDesc = []
				for (var key in onsiteProduct) {
					let productEn = onsiteProduct[key]
					onsiteProductDesc.push(res.ProductDict[productEn] || "")
				}
				setOnsiteProductZh(onsiteProductDesc)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	useEffect(() => {
		getProjects()
	}, [])

	useEffect(() => {
		if ((guardBaseInfo.OnsiteProducts || []).length) {
			getProductsGroupsInfo(guardBaseInfo.OnsiteProducts)
		}
		if ((guardBaseInfo.ProductDesc || []).length) {
			let appidNASet = []
			guardBaseInfo.ProductDesc.map(i => { if (appidNASet.indexOf(i.AppId) == -1 && i.IsAuthorized === false) { appidNASet.push(i.AppId) } })
			setAppidNASet(appidNASet)
		}
	}, [guardBaseInfo])

	const tabs = appids.map((item, i) => {
		return {
			id: item,
			label: <CustomerName appid={Number(item)} need={false} />
		};
	});
	const [activeId, setActiveId] = useState('');
	useMemo(() => {
		if (appids?.length > 0) {
			setActiveId(appids[0]);
		}
	}, [appids]);

	if (authLoading) {
		return <PermissionLoading />;
	}
	if (isAuth === false) {
		return <NoPermission />;
	}

	return (
		<>
			<Row gap={30}>
				<Col>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} ><Text theme="label" verticalAlign="middle">提单人</Text></Col>
							<Col span={18}>
								<Text>{guardBaseInfo.SubmittedBy}</Text>
							</Col>
						</Row>
					</section>
				</Col>
				<Col>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} ><Text theme="label" verticalAlign="middle">提单时间</Text></Col>
							<Col span={18}>
								<Text>{guardBaseInfo.SubmitTime}</Text>
							</Col>
						</Row>
					</section>
				</Col>
			</Row>
			{appidNASet.length > 0 &&
				<Row>
					<Icon type="info" style={{ marginLeft: 10, marginTop: 6 }} />
					<Text style={{ color: "red", marginLeft: 2, marginTop: 5, background: "lightpink" }} > {naText} </Text>
				</Row>
			}
			<hr />
			<Row gap={30}>
				<Col>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text style={{ color: "red" }} verticalAlign="middle">*</Text><Text theme="label" verticalAlign="middle">护航名称</Text>
							</Col>
							<Col span={18}>
								<Text>{guardName}</Text>
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text style={{ color: "red" }} verticalAlign="middle">*</Text><Text theme="label" verticalAlign="middle">护航类型</Text>
							</Col>
							<Col span={18}>
								{StandardDict.get(standard)}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text style={{ color: "red" }} verticalAlign="middle">*</Text><Text theme="label" verticalAlign="middle">护航时间</Text>
							</Col>
							<Col span={18}>
								{guardTime}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text theme="label" verticalAlign="middle">护航重点时间</Text>
							</Col>
							<Col span={18}>
								{keyTime ? keyTime : noSet}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text theme="label" verticalAlign="middle">驻场护航时间</Text>
							</Col>
							<Col span={18}>
								{onsiteDates ? onsiteDates : noSet}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">驻场支持产品</Text>
							</Col>
							<Col span={18}>
								{
									(onsiteProductZh.length != 0) ? (onsiteProductZh || []).join(", ") : noSet
								}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text theme="label" verticalAlign="middle">护航项目</Text>
							</Col>
							<Col span={18}>
								{projectDict.get(project)}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text theme="label" verticalAlign="middle">封网需求</Text>
							</Col>
							<Col span={18}>
								{guardBaseInfo.ClosedNetworkDemand || noSet}
							</Col>
						</Row>
					</section>
				</Col>
				<Col>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text style={{ color: "red" }} verticalAlign="middle">*</Text><Text theme="label" verticalAlign="middle">客户APPID</Text>
							</Col>
							<Col span={18}>
								{appId}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">客户名称</Text>
							</Col>
							<Col span={18}>
								<Text align="left">{customerName ? customerName : noSet}</Text>
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">关联客户信息</Text>
							</Col>
							<Col span={18}>
								{relatedCustomer ? relatedCustomer : noSet}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">客户接口人</Text>
							</Col>
							<Col span={18}>
								{customerContacts ? customerContacts : noSet}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">客户联系方式</Text>
							</Col>
							<Col span={18}>
								{customerPhone ? customerPhone : noSet}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">护航背景及需求</Text>
							</Col>
							<Col span={18}>
								{guardBaseInfo.StatementOfNeeds || noSet}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6} >
								<Text theme="label" verticalAlign="middle">每日巡检</Text>
							</Col>
							<Col span={18}>
								{guardBaseInfo.CronType === 0 ? '未开启' : '开启'}
							</Col>
						</Row>
					</section>
				</Col>
			</Row>
			<hr />
			<Row>
				<Col>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text style={{ color: "red" }} verticalAlign="middle">*</Text><Text theme="label" verticalAlign="middle">整体放量预估</Text>
							</Col>
							<Col span={18}>
								{expectedEnlarge}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">业务压测计划</Text>
							</Col>
							<Col span={18}>
								{pressureTestPlan ? pressureTestPlan : noSet}
							</Col>
						</Row>
					</section>
				</Col>
				<Col>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">柔性限流降级策略</Text>
							</Col>
							<Col span={18}>
								{limitStrategy ? limitStrategy : noSet}
							</Col>
						</Row>
					</section>
					<section>
						<Row verticalAlign={"middle"}>
							<Col span={6}>
								<Text theme="label" verticalAlign="middle">业务主链路应急预案</Text>
							</Col>
							<Col span={18}>
								{businessEmergencyPlan ? businessEmergencyPlan : noSet}
							</Col>
						</Row>
					</section>
				</Col>
			</Row>
			<Tabs
				tabs={tabs}
				activeId={activeId}
				onActive={(tab) => {
					setActiveId(tab.id);
				}
				}
				style={
					{
						marginTop: '35px'
					}
				}>
				{tabs.map(tab => (
					<TabPanel id={tab.id} key={tab.id}>
						<DescInstancePanel
							appid={Number(tab.id)}
							authorized={appidAuthorizedMap.get(Number(tab.id))}
							currentGuard={guardBaseInfo}
						/>
					</TabPanel>
				))}
			</Tabs>
		</>
	);
}

export { GuardDescBaseInfo };
