import React, { useState, useEffect, useRef } from 'react';
import { Button, Card, Layout, message as tips, Stepper, StatusTip } from '@tencent/tea-component';

import { useHistory } from '@tea/app';
import { ExportGuardSheet } from '../components/ExportGuardSheet';
import { DescribeGuardSheet, DescribeGuardMapGenStatus, DescribeArchGuardInstanceSync, DescribeArchGuardAddedInstance } from '@src/api/advisor/guard';
import { Filter, GuardParams } from '@src/types/advisor/guard';

import BaseInfo from './BaseInfo';
import Detail from './Detail';
import Confirm from './Confirm';
import { getUserInfo } from '@src/api/common';
import { fromAntool } from '@src/utils/common';
import { getProcessEnv } from '../../../../../../app/utils';
import { GENERATE_STATUS, AUTO_SAVE_TIME } from '@src/utils/constants';
import moment from 'moment';
import uuid from 'react-uuid';
import { cloneDeep } from 'lodash';
const { Body, Content } = Layout;

// 轮询生图进度标识
const intervalId = null;
// 自动保存定时器标识
let autoSaveTimer = null;

export function GuardSteps() {
	// 是否是国际站
	const isAbroad = getProcessEnv() === 'production-abroad';
	const isTest = getProcessEnv() === 'test';
	// 是否是通过antool访问
	const antool = fromAntool();
	// BaseInfo 组件更新初始化值
	const [refreshValue, setRefreshValue] = useState(0);
	// 新建按钮loading
	const [btnLoading, setBtnLoading] = useState<boolean>(false);
	// 自动保存loading
	const [autoSaveLoading, setAutoSaveLoading] = useState<boolean>(false);
	useEffect(() => {
		const handleMessage = (e) => {
			if (antool && e?.data?.MsgType === 'GuardCreateInit') {
				// 填充antool参数
				const { Source, SourceId, MainAppId, StatementOfNeeds, StartTime, EndTime } = e.data || {};
				let antoolStartTime = '';
				let antoolEndTime = '';
				if (EndTime) {
					 antoolStartTime = moment(StartTime).set({
						hour: moment().hour(),
					})
						.add(2, 'hours')
						.startOf('hour')
						.format('YYYY-MM-DD HH:mm:ss');
					 antoolEndTime = moment(EndTime).set({
						hour: moment().hour(),
					})
						.startOf('hour')
						.format('YYYY-MM-DD HH:mm:ss');
				}
				setCurrentGuard(pre => ({
					...pre,
					Platform: Source,
					PlatformUniqueId: SourceId,
					MainAppId,
					StatementOfNeeds,
					StartTime: antoolStartTime,
					EndTime: antoolEndTime,
				}));
				setRefreshValue(pre => pre + 1);
			}
		};
		window.addEventListener('message', handleMessage);
		return () => {
			window.removeEventListener('message', handleMessage);
		};
	}, []);
	// 当前登录rtx
	const [rtx, setRtx] = useState<string>(localStorage.getItem('engName') || '');
	const history = useHistory();
	const steps = [
		{ id: 'base', label: '基本信息' },
		{ id: 'detail', label: '详细信息' },
		{ id: 'confirm', label: '确认护航信息' },
	];

	const [current, setCurrent] = useState('base');
	const currentIndex = current ? steps.findIndex(x => x.id === current) : -1;
	const next = current && steps[currentIndex + 1];
	const prev = current ? steps[currentIndex - 1] : steps[steps.length - 1];

	// 是否显示编辑框
	const [showEditor, setShowEditor] = useState<boolean>(false);

	// 下一步按钮状态
	const [nextDisable, setNextDisable] = useState<boolean>(false);

	// 当前护航单数据
	const [currentGuard, setCurrentGuard] = useState<GuardParams>({});

	// 当前护航单已选实例
	const [instanceMap, setInstanceMap] = useState({});
	// 是否驻场护航
	const [isOnSite, setIsOnSite] = useState(false);
	// baseinfo ref
	const baseinfoRef = useRef(null);
	// detail ref
	const detailRef = useRef(null);
	// confirm ref
	const confirmRef = useRef(null);
	// 是否云架构协作权限
	const [hasAuthAppId, setHasAuthAppId] = useState([]);
	// 特殊审批信息
	const [approvalDataList, setApprovalDataList] = useState([]);

	// 下一步禁用的情况，非国际站，非多账号，非自助护航，没有授权，在第二步，没有填写特殊审批信息
	const disabledConditon = (isAbroad || currentGuard.Standard === 3 || current !== 'detail') ? false : getAuthDisabled();

	function getAuthDisabled() {
		const authorizedApps = new Set(hasAuthAppId);
		return approvalDataList.some(item => !authorizedApps.has(item.AppId) && item.Reason === '');
	}

	// 子组件调用，用于进行下一步
	function handleUpdateSteps() {
		setCurrent(next ? next.id : null);
	}

	// 子组件调用，用于禁用下一步按钮
	function handleUpdateStepsDisable(v: boolean) {
		setNextDisable(v);
	}

	// 子组件调用，覆盖护航单基础数据当前信息  用户上一步的按钮效果
	function handleUpdateCurrentGuard(v: GuardParams) {
		setCurrentGuard(v);
	}

	// 子组件调用，用于当子组件create完毕后，调用父组件函数，重新查询实例详情。保证数据和后端一致性
	function handleGetGuardSheet(v: string) {
		getGuardSheet(v);
	}

	// 查询指定id的guard
	const getGuardSheet = async (guardId) => {
		try {
			const filters: Array<Filter> = [
				{ Name: 'guard_id', Values: [guardId] },
			];
			const res = await DescribeGuardSheet({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset: 0,
				Limit: 10,
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			// 判断是否是草稿状态，只有草稿状态可以编辑
			const item = res.Guard[0] || {};
			if (item.Status && item.Status === 1) {
				setCurrentGuard(item);
				// 保存已选实例数量
				setInstanceMap(item.InstanceTemplateCount);
			} else {
				tips.warning({ content: '草稿状态才允许编辑' });
				history.push('/advisor/guard');
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};
	// 获取当前登录账号rtx
	const getCurrentOperator = async () => {
		try {
			const currentUser = (await getUserInfo()).data.EngName;
			if (currentUser !== rtx) setRtx(currentUser || '');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 生成一个隐藏的iframe，用于打开生图链接
	function openUrlInHiddenIframe(url) {
		// 创建一个新的iframe元素
		const iframe = document.createElement('iframe');
		// 设置iframe的属性
		iframe.style.width = '0px';
		iframe.style.height = '0px';
		iframe.style.position = 'absolute';
		iframe.style.left = '-9999px'; // 移到视窗外
		iframe.style.border = 'none';
		// 设置目标URL
		iframe.src = url;
		// 添加到body中
		document.body.appendChild(iframe);
	  }

	// 自动生图
	const handleAutoMap = async (autoMapAppId = []) => {
		// 清空自动生图
		autoSaveTimer && clearInterval(autoSaveTimer);
		// 护航单ID
		const taskId = currentGuard.GuardId;
		// 生图后需要启动的插件
		const toStartPlugin = 'cloud-escort-sdk';
		// 架构图的名称
		const archName = `${currentGuard.GuardName}-架构图-${moment().format('YYYYMMDDHHmmss')}`;
		// 架构图描述
		const archDesc = `架构图来源：云顾问运营端发送\n发送时间：${moment().format('YYYY/MM/DD/ HH:mm')}\n发送人：腾讯云架构师\n如有疑问，请联系您的售后服务经理`;
		autoMapAppId.forEach((appId) => {
			// 记录开始生图
			DescribeGuardMapGenStatus({
				taskId: `${taskId}`,
				status: 'start',
				AppId: appId,
			})
				.catch((err) => {
					console.log(err);
				});
			// 生图链接
			openUrlInHiddenIframe(`/advisor/new-architecture/architecture/autodraw?taskId=${taskId}&appid=${appId}&toStartPlugin=${toStartPlugin}&archName=${archName}&archDesc=${archDesc}`);
		});
		pollingProgress(autoMapAppId);
	};

	// 轮询生图进度
	function pollingProgress(autoMapAppId) {
		tips.loading({
			content: '正在生成架构图，请稍候...',
		});
		setBtnLoading(true);
		const result = (Promise as any).allSettled(autoMapAppId.map(appId => intervalIdPromise(appId)));
		result.then(() => {
			setBtnLoading(false);
			handleUpdateSteps();
			handleGetGuardSheet(currentGuard.GuardId.toString());
		});
	}

	function intervalIdPromise(appId) {
		return new Promise((reslove, reject) => {
			let intervalId = setInterval(async () => {
				DescribeGuardMapGenStatus({
					taskId: `${currentGuard.GuardId}`,
					status: '',
					AppId: appId,
				})
					.then((res) => {
						// 生图成功
						if (res.Status === GENERATE_STATUS.SUCCESS) {
							clearInterval(intervalId);
							intervalId = null;
							// 执行同步
							DescribeArchGuardInstanceSync({
								AppId: appId,
								MapId: res?.MapId,
								TemplateId: uuid(),
							  })
								.then(() => {
									DescribeArchGuardAddedInstance({
										GuardId: currentGuard.GuardId,
										AppId: appId,
									})
										.then(() => {
											reslove(appId);
										})
										.catch((err) => {
											const msg = err.msg || err.toString() || '同步失败';
											tips.error({ content: msg });
											reject(appId);
										});
								})
								.catch((err) => {
									  const msg = err.msg || err.toString() || '同步失败';
									  tips.error({ content: msg });
									  reject(appId);
								});
						}
						// 生图失败
						if (res.Status === GENERATE_STATUS.FAIL) {
							clearInterval(intervalId);
							intervalId = null;
							tips.error({ content: res?.ErrorMessage || `${appId} 生成架构图失败` });
							reject(appId);
						}
					})
					.catch((err) => {
						clearInterval(intervalId);
						intervalId = null;
						setBtnLoading(false);
						console.log(err);
					});
			  }, 1000);
		});
	}

	// 获取当前用户登录账号
	useEffect(() => {
		getCurrentOperator();
	}, [rtx]);

	// 监听currentGuard
	useEffect(() => {
		if (Object.keys(currentGuard).length) {
			setShowEditor(true);
		}
	}, [currentGuard]);

	useEffect(() => {
		if (current === 'detail') {
			autoSaveTimer = setInterval(() => {
				setAutoSaveLoading(true);
				// 每 30s 自动保存
				detailRef?.current?.OnClick(false, false, true);
			 }, AUTO_SAVE_TIME);
		} else {
			// 去其他步骤，清空自动保存定时器
			autoSaveTimer && clearInterval(autoSaveTimer);
		}
	}, [current]);

	// 监控url参数，是否存在有效的guardid
	useEffect(() => {
		const id = window.location.search.split('?guardid=')[1] || '0';
		if (parseInt(id) > 0) {
			getGuardSheet(id);
		} else {
			if (!isTest && !antool) {
				// 除测试环境外，其他环境不能走云护航新建
				tips.warning({ content: '请点击新建按钮创建护航！' });
				history.push('/advisor/guard');
				return;
			}
			setShowEditor(true); // 没有guardid，显示编辑框
		}
		return () => {
			intervalId && clearInterval(intervalId);
			autoSaveTimer && clearInterval(autoSaveTimer);
		};
	}, []);

	return (
		<Body>
			<Content>
				{!antool && <Content.Header
					showBackButton={true}
					onBackButtonClick={() => {
						history.push('/advisor/guard');
					}}
					title={window.location.search.split('?guardid=').length > 1 ? '编辑护航单' : '新建护航单'}
				/>}
				<Content.Body>
					<Card style={{ overflow: 'hidden' }}>
						<Card.Body style={{ height: `calc(100vh - ${antool ? '112' : '212'}px)`, overflow: 'scroll' }}>
							{
								// 是否显示
								showEditor && (
									<>
										<div>
											<Stepper steps={steps} current={current} />
											{current === 'base' && (
												<BaseInfo
													isOnSiteChange={value => setIsOnSite(value)}
													key={`base${refreshValue}`}
													currentGuard={currentGuard}
													handleUpdateSteps={handleUpdateSteps}
													handleUpdateStepsDisable={handleUpdateStepsDisable}
													handleUpdateCurrentGuard={handleUpdateCurrentGuard}
													handleGetGuardSheet={handleGetGuardSheet}
													ref={baseinfoRef}
												/>
											)}
											{current === 'detail' && (
												<Detail
													currentGuard={currentGuard}
													handleUpdateSteps={handleUpdateSteps}
													handleUpdateStepsDisable={handleUpdateStepsDisable}
													handleUpdateCurrentGuard={handleUpdateCurrentGuard}
													handleGetGuardSheet={handleGetGuardSheet}
													ref={detailRef}
													instanceMap={instanceMap}
													saveFinish={() => setCurrent(prev.id)}
													handleAutoMap={handleAutoMap}
													updateHasAuthAppId={(appId) => {
														setHasAuthAppId(oldaVal => [...oldaVal, appId]);
													}}
													updateApprovalData={(data) => {
														setApprovalDataList((oldData) => {
															const newData = cloneDeep(oldData);
															// eslint-disable-next-line max-len
															const index = newData.findIndex(item => item.AppId === data.AppId);
															if (index === -1) {
																newData.push(data);
															} else {
																newData[index] = { ...data };
															}
															return newData;
														});
													}}
													autoSaveFinish={() => {
														setAutoSaveLoading(false);
													}}
												/>
											)}
											{current === 'confirm' && (
												<Confirm
													currentGuard={currentGuard}
													handleUpdateStepsDisable={handleUpdateStepsDisable}
													ref={confirmRef}
												/>
											)}
										</div>
									</>
								)
							}
						</Card.Body>
						<Card.Footer style={{ padding: '20px 0px' }}>
							{isOnSite && current === 'base'
								? <section style={{ textAlign: 'center' }}>
									<Button type="primary" onClick={() => {
										baseinfoRef.current.OnClick(false);
									}}>提交
									</Button>
								</section>
								: <section style={{ textAlign: 'center', position: 'relative' }}>
									{
										current === 'confirm'
											? (<ExportGuardSheet
												Responsor={currentGuard.Responser}
												isListItem={false}
												currLoginUser={rtx}
												GuardId={currentGuard.GuardId}
												Status={currentGuard.Status} // 护航单状态
												CreatedBy={currentGuard.CreatedBy}
												Approvals={currentGuard.Approvals}

											/>)
											: (<Button
												loading={btnLoading}
												onClick={() => {
												// baseinfo click
													if (current === 'base') {
													// 校验入参，并调用接口创建或修改护航单基础信息
														baseinfoRef.current.OnClick(false); // 区别在于是否需要到下一步
													} else if (current === 'detail') {
														detailRef.current.OnClick(false); // 区别在于是否需要到下一步
													}
												}}
											>保存
											</Button>)
									}

									<Button
										loading={btnLoading}
										disabled={!prev}
										onClick={() => {
											if (current === 'detail') {
											// 点击上一步，当前页面保存内容
												detailRef.current.OnClick(false, true);
											} else {
												setCurrent(prev.id);
											}
										}}
										style={{ marginLeft: 10 }}
									>
                                        上一步
									</Button>

									<Button
										type="primary"
										loading={btnLoading}
										disabled={(!next && !current) || nextDisable
											|| disabledConditon}
										onClick={() => {
										// baseinfo click
											if (current === 'base') {
											// 校验入参，并调用接口创建或修改护航单基础信息
												baseinfoRef.current.OnClick(true);
											} else if (current === 'detail') {
												detailRef.current.OnClick(true); // 区别在于是否需要到下一步
											} else {
												confirmRef.current.OnClick(); // 提交
											}
										}}
										style={{ marginLeft: 10 }}
									>
										{next ? '下一步' : '确认提交'}
									</Button>
									{autoSaveLoading && <StatusTip
										style={{ verticalAlign: 'middle', marginLeft: 10, position: 'absolute', top: 8 }}
										status='loading'
										loadingText='已保存草稿' />
									}
								</section>
							}
						</Card.Footer>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}
