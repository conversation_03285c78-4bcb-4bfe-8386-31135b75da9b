import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Card, Transfer, Table, SearchBox, Select, Form, Bubble, SelectMultiple, Input, message, StatusTip, Icon } from "@tencent/tea-component";
const { selectable, removeable, scrollable, pageable, autotip } = Table.addons;
import { Text } from "@tencent/tea-component";
import { Row, Col, Radio } from "@tencent/tea-component";
import _ from 'lodash'
import { policyItem, InstanceItem } from '@src/types/advisor/guard';
import { getInstances } from '@src/api/advisor/estimate';
import { getProductsGroups } from '@src/api/advisor/estimate';

interface Props {
	product: string,
	instancePolicys: Array<policyItem>, //实例容量策略
	instances?: Array<InstanceItem>,     //实例信息
	appid?: number,
	guardId?: number
	recordChange?: any
	cdnInstancePolicy?: boolean
}

// 根据路径去找值
const findValueByPath = (obj, path) => {
	try {
			const value = path.split('.').reduce((acc, key) => acc?.[key], obj);
			// 如果值是数组，返回逗号拼接的字符串.如果值是字符串，返回本身
			return Array.isArray(value) ? value.join(',') : value
	} catch (error) {
			return '--'; // 如果发生错误，返回--
	}
}

function DescInstanceTable({ appid, product, instancePolicys, instances, guardId, recordChange = null, cdnInstancePolicy = false }: Props, ref) {

	const [total, setTotal] = useState(0);
	const [pageIndex, setPageIndex] = useState(1);
	const [pageSize, setPageSize] = useState(10);
	const [records, setRecords] = useState([]);
	const [loading, setLoading] = useState(true);
	const [important, setImportant] = useState('1')
	const [firstSearch, setFirstSearch] = useState(true)
	//实例列
	const [columnData, setColumnData] = useState<any>()
	const isShowUnSupport = records?.[0]?.['IsProductSupported'] === false || records?.[0]?.['IsAuthorized'] === false

	useImperativeHandle(ref, () => ({
		Check: () => { },
	}))

	useEffect(() => {
		// 修改单选，页码变为1
		setPageIndex(1)
		getProductInstances({ pageIndex: 1, pageSize })
	}, [important])

	// 获取选择的产品实例
	async function getProductInstances({ pageIndex = 1, pageSize = 10 } = {}) {
		try {
			setLoading(true)
			// setRecords([]);
			const params = {
				AppId: appid,
				GuardId: guardId,
				Limit: pageSize,
				Offset: (pageIndex - 1) * pageSize,
				Products: [product],
				Important: parseInt(important)
			}
			// 记录当前页
			setPageIndex(pageIndex)
			setPageSize(pageSize)
			const res = await getInstances(params)
			if (res.Error) {
				setRecords([]);
				setLoading(false)
				recordChange && recordChange([])
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				// 如果没有重点关注，直接展示
				if (firstSearch && res.TotalCount === 0 && important === '1') {
					setImportant('0')
					return
				}
				setFirstSearch(false)
				setRecords([...res.Instance])
				setTotal(res.TotalCount)
				recordChange && recordChange([...res.Instance])
				setLoading(false)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	//获取表格每行的展示表头及渲染数据，如果没有指定实例扩容策略的字段不展示
	function getColumnData() {
		let ret = []
		let newInstances: Array<InstanceItem> = _.cloneDeep(records)
		// 任意取一条实例记录，剥离抽取表头字段
		if (newInstances?.length !== 0) {
			let one = newInstances[0]
			ret.push(
				{
					key: "InstanceId",
					header: "ID",
					render: ins => (
						<>
							<p>{ins.InstanceId}</p>
						</>
					),
				})
			ret.push(
				{
					key: "InstanceName",
					header: "实例名",
					render: ins => (
						<>
							<p>{ins.InstanceName}</p>
						</>
					),
				})
			ret.push(
				{
					key: "region",
					header: () => (
						<>
							地域
						</>
					),
					render: ins => (
						<>
							<p> {ins.Region} </p>
						</>
					),
				}
			)
			one.Policy?.map((val, index) => {
				ret.push(
					{
						key: "policy" + index,
						header: () => (
							<>
								{val.CNName}
							</>
						),
						render: ins => {
							let currentPolicy = ins.Policy[index] //合并产品中，存在部分产品的policy为空的情况，需做判断
							let tmp = ""
							if (typeof (currentPolicy) !== "undefined") {
								// tmp = "近 " + (currentPolicy.Days || "") + " 天" + currentPolicy.CNName + "为 " + currentPolicy.Value + ""
								tmp = currentPolicy.Value
							}
							// 是否有大时间跨度或大聚合查询展示优化
							if (currentPolicy.MetricName === "AggregateQuery") {
								tmp = currentPolicy.Value === 0 ? '是' : '否'
							}
							// 是否是extra列，展示对应字段
							if (currentPolicy.Type === 'Extra') {
								const extraData = ins.Extra ? JSON.parse(ins.Extra) : {}
								const value = findValueByPath(extraData, currentPolicy.Unit)
								tmp = value
							}

							return <><p>{tmp}</p></>;
						},
					}
				)
			})
			ret.push(
				{
					key: "Important",
					header: () => (
						<>
							重点关注资源
						</>
					),
					render: ins => (
						<>
							<p>{ins.Important === 1 ? '是' : '否'}</p>
						</>
					)
					,
				}
			)
		}
		return ret
	}

	//获取实例展示列
	const getColumns = async () => {
		try {
			const res = await getProductsGroups({
				AppId: appid,
				Env: 'all',
				TaskType: 'guardTaskType',
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				const columnData = res.FilterPolicy.filter(i => i.Product === product)
				let columns: any = columnData.length ? columnData[0].FilterInfo.filter(i => i.Uses === 'Right') : []
				 // cdn 的Policy过滤
				 if(!cdnInstancePolicy && product === 'cdn'){
					columns = columns.filter(i=>i.DataFrom !=='Policy' )
				}
				// 最终的展示列
				let result: any = columns.map((i, index) => {
					return {
						key: index,
						header: i.FilterName,
						render: (ins) => {
							let value: any = ''
							if (i.DataFrom === 'Info') {
								value = ins[i.FilterCorrespondName]
							} else if (i.DataFrom === 'Extra') {
								const extraData = ins.Extra ? JSON.parse(ins.Extra) : {}
								value = findValueByPath(extraData, i.Location)
							} else if (i.DataFrom === 'Policy') {
								const policy = ins.Policy.filter(j => j.MetricName === i.Location)[0]
								value = policy ? (policy.Type === 'Other' ? (policy.OtherValue || '未填写') : policy.Value) : ''
							}
							// 根据映射展示对应的值
							if (value !== '' && i.ExtraShowName?.length) {
								const list = String(value).split(';')
								let result = []
								list.map(val => {
									const colValueItem = i.ExtraShowName.filter(i => i.Key == val)
									const tempValue = colValueItem.length ? colValueItem[0].Value : val
									result.push(tempValue)
								})
								value = result.join(';')
							}
							// 是否有大时间跨度或大聚合查询展示优化
							if (i.Location === "AggregateQuery") {
								value = value === 0 ? '是' : '否'
							}
							return <div style={{ overflow: "hidden", whiteSpace: "nowrap", textOverflow: "ellipsis" }} title={value} >
								{value}
							</div>
						}
					}
				})

				setColumnData(result)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	useEffect(() => {
		getColumns()
	}, [product, records])


	return (
		<div key={product}>
			<section>
				{
					isShowUnSupport
						?
						<Row style={{ marginTop: 10 }}>
							<Col style={{ width: '12.5%' }} span={2} >
								<Text theme="label">重点护航实例</Text>
							</Col>

							<Col>
								{
									records[0]['InstanceIds'] || <Text style={{ color: "LightGray" }} verticalAlign="middle">未填写实例</Text>
								}
							</Col>
						</Row>
						:
						<Row style={{ marginTop: 10 }}>
							<Col style={{ width: '12.5%' }} span={2} >
								<Text theme="label">重点护航实例</Text>
							</Col>
							<Col>
								<section style={{ marginBottom: 20 }}>
									<Radio.Group value={important} onChange={value => setImportant(value)}>
										<Radio name='1'>仅展示重点关注资源</Radio>
										<Radio name='0'>展示全部资源</Radio>
									</Radio.Group>
								</section>
								<Card>
									<Table
										verticalTop
										records={records}
										recordKey="InstanceId"
										columns={columnData || []}
										addons={[
											pageable({
												recordCount: total,
												pageIndex: pageIndex,
												onPagingChange: query => getProductInstances(query),
											}),
											autotip({
												isLoading: loading,
											}),
											scrollable({ maxHeight: 480 }),
										]}
										topTip={
											records?.length == 0 && <StatusTip status={'empty'} />
										}
									/>
								</Card>
							</Col>
						</Row>
				}
			</section>
		</div>
	)
}

export default forwardRef(DescInstanceTable)