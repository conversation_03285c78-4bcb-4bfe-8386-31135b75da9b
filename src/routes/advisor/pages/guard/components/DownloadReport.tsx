import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Button, message } from "@tencent/tea-component";
import { Icon } from "@tencent/tea-component";

import { DownloadGuardReport, DescribeGuardDownloadTask } from '@src/api/advisor/guard';
import _ from 'lodash'

interface Props {
    GuardInfo: any,
    GuardId: number,
    MainAppId?: number,
    Status: number, //护航单状态 根据护航单状态控制Disabled
    ReportDict: any, //记录报告地址
    handleCurrentReport: Function, //指定当前报告地址
    UpdateReportDict?: Function,  //生成报告成功，回调接口，刷新 ReportDict
    OpenReportModal?: Function,  //打开下载报告探测
}


export function DownloadReport({GuardInfo, GuardId, MainAppId, Status, ReportDict, handleCurrentReport, UpdateReportDict, OpenReportModal }: Props, ref) {

    //是否loading
    const [loading, setLoading] = useState<boolean>(false)

    //持续查询任务
    let timer = null;

    //计数器，避免持续查询任务死循环 最多查1分钟 12次
    let counter = 0;

    //根据主appid查询客户名称
    const downloadGuardReport = async () => {
        setLoading(true)
        try {
            const res = await DownloadGuardReport({
                GuardId: GuardId,
                Language: 'zh-CN',
                Env: 'public',
                AppId: MainAppId,
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                //开启持续查询任务 
                timer = setInterval(() => {
                    getReportResult(res.ResultId, res.PrivateResultId)
                }, 3000)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    //下载报告：去除生成动作(DownloadGuardReport)，直接下载
    const downloadGuardReportNew = async () => {
        setLoading(true)
        getReportResult(GuardId + "#zh-CN#public", GuardId + "#zh-CN#private")
        //开启持续查询任务 
        // timer = setInterval(() => {
        //     getReportResult(GuardId + "#zh-CN#public", GuardId + "#zh-CN#private")
        // }, 1000)
    }

    //查询报告结果
    const getReportResult = async (PublicResultId: string, PrivateResultId: string) => {
        try {
            const res = await DescribeGuardDownloadTask({
                AppId: MainAppId, //接口一定要传appid，实际无意义,
                PublicResultId: PublicResultId,
                PrivateResultId: PrivateResultId,
                GuardId: GuardId
            });
            if (res.Error) {
                const msg = res.Error.Message || '';
                message.error({ content: msg });
                return;
            } else {
                //执行完毕，刷新结果，并关闭定时任务
                if (res.PublicTaskStatus === 'success') {
                    setLoading(false)
                    handleCurrentReport(res)
                    // let tmp = _.cloneDeep(ReportDict)
                    // tmp[GuardId] = res
                    // UpdateReportDict && UpdateReportDict(tmp)
                    if (timer) {
                        clearInterval(timer)
                    }
                }
                counter = counter + 1
                if (counter >= 12) {
                    setLoading(false)
                    if (timer) {
                        clearInterval(timer)
                    }
                }
            }

        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    };

    //页面退出时，关闭持续查询任务
    useEffect(() => {
        return () => {
            if (timer) {
                clearInterval(timer)
            }
        }
    }, [])

    //报告是否存在
    const reportExists: boolean = useMemo(() => {
        //默认报告已生成
        return true

        if (ReportDict[GuardId] && Object.keys(ReportDict[GuardId]).length) {
            return true
        } else {
            return loading //loading 的时候，也认为是报告存在，此时显示 “下载报告”，但按钮是灰色的
        }
    }, [ReportDict, loading])

    //按钮是否禁用
    const disable: boolean = useMemo(() => {
        // 专项审批是否完成
        const scanResultFinish = GuardInfo?.Approvals?.ScanResultStatus?.some(item => !item?.IsFinished);
        /* 如果专项审批未完成，则禁用 */
        if (Status < 48 || scanResultFinish) {
            return true;
        } else {
            //否则返回loading，loading时禁用  
            return loading
        }
    }, [Status, loading])

    return (
        <div key={GuardId}>
            <Button
                type="link"
                onClick={() => {
                    /* 报告是否存在，如果报告存在，则调用查看报告接口 OpenReportModal */
                    if (reportExists) {
                        downloadGuardReportNew() //获取已生成报告的链接等内容。异步方式调用，暂无返回延迟处理
                        // handleCurrentReport(ReportDict[GuardId] || {})
                        // OpenReportModal(true) //打开modal框
                    } else {
                        downloadGuardReport()
                    }
                }}
                disabled={disable}
            >
                {reportExists ? '下载报告' : '生成报告'}
            </Button>
            {
                loading && <Icon type="loading" />
            }
        </div>
    )
}

