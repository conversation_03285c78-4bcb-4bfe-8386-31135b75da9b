
.calendarWrap{
  .t-calendar--full .t-calendar__table-body-cell {
    height: 13.5vh;
    line-height: 16px;
    .t-calendar__table-body-cell-content {
      height: calc(100% - 22px);
      overflow-y: auto;
      overflow-x: hidden;
    }
  }
  
  .cellWrap{
    font-size: 12px;
    font-weight: normal;
    .divider{
      margin: 8px 0;
      border-top: 1px solid #e8e8e870;
    }
    .cellTitle{
      font-weight: bold;
      margin-bottom: 6px;
    }
    .labelText{
      flex-grow: 0;
      width: 40px;
      .tea-grid__box{
        width: 40px;
      }
    }
  }
}

.drawerWrap {
  .label{
    font-size: 12px;
    padding-left: 40px;
  }
  .guardTitle{
    width: 100%;
    display: flex;
    align-items: center;
    .linkBtn{
      white-space: normal;
      text-align: left;
    }
    .tag {
      color: #202427;
      margin-left: 20px;
    }
  }
  .noProductInfoTitle{
    padding: 12px 40px;
  }
  .productInfo{
    font-size: 12px;
    .productName{
      color: #000000E6;
      font-weight: bold;
    }
  }
}