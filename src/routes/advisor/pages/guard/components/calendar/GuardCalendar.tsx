import React, { useState, useEffect } from 'react';
import { Calendar, Drawer, Tag, Collapse, Divider } from 'tdesign-react';
import { Button, Text, Row, Col } from '@tencent/tea-component';
const { Panel } = Collapse;
import './style.less'
import moment from 'moment';
import { StatusDict } from '@src/types/advisor/guard';

// 项目颜色
const colorList = ["#C6E2FF", "#D8CDBE", "#FFDAB9", "#D9D9B3", "#FFC0A8", "#FFF5EE", "#DFFFD9", "#D6FFFA", "#D6D6D6", "#FFE5D9", "#D9D9D9", "#FFE8C4", "#F5F3C0", "#FFFFE0", "#FCE9D3", "#FFFFCC", "#F5E6D6", "#E6E6FF"]

const StatuMap = {
  'prepare': '护航前准备',
  'running': '护航中',
  'finish': '护航完成',
}

export function GuardCalendar({ calendarData = [], guardData = [], projectDict = new Map(), timeChange = (time) => { } }) {
  // 侧边栏标题
  const [title, setTitle] = useState('')
  // 侧边栏数据
  const [currentData, serCurrentData] = useState<any>({})
  // 项目-颜色映射
  const [colorMap, setColorMap] = useState({})

  // 网格点击
  function cellClick(options) {
    const data = calendarData.find(i => i.OnDay === options.cell.formattedDate) || {}
    if (data) {
      let tempList = []
      data.GuardId.map(id => {
        // 在guardData中找到护航单信息
        const info = (guardData || []).find(i => i.GuardId === id) || {}
        // 统计全部客户
        // const customerList = [info.CustomerName, ...(info.RelatedCustomerNames || [])]
        // 只统计主客户
        const customerList = [info.CustomerName]
        tempList = [...tempList, ...customerList]
      })
      // 去重后的客户列表
      const customerNameList: any = Array.from(new Set(tempList))
      setTitle(data ? `${data.OnDay}（共 ${customerNameList.length} 家客户，${data.GuardId?.length || 0} 个护航单）` : '')
      serCurrentData(data)
    }
  }

  // 判断当前护航状态
  function getStatu(data) {
    // 护航开始、结束时间
    let { StartTime, EndTime } = data
    const today = moment();
    const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
    const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
    if (today.isBefore(start)) {
      return 'prepare';
    } else if (today.isBetween(start, end)) {
      return 'running';
    } else {
      return 'finish';
    }
  }

  useEffect(() => {
    const tempMap = {}
    projectDict.forEach((value, key) => {
      const index = Array.from(projectDict.keys()).indexOf(key);
      tempMap[key] = colorList[index] || '#e8e8e8'
    });
    setColorMap(tempMap)
  }, [projectDict])

  return (
    <>
      <div className='calendarWrap'>
        <Calendar
          firstDayOfWeek={1}
          preventCellContextmenu
          controllerConfig={{
            mode: { visible: false },
            year: { visible: true },
            month: { visible: true },
            weekend: { visible: false },
            current: { visible: false }
          }}
          onControllerChange={(data) => { timeChange(data.formattedFilterDate) }}
          fillWithZero={false}
          onCellClick={cellClick}
          cellAppend={(cellData) => {
            // 先从calendarData找到日期对应的护航单id
            const data = (calendarData || []).find(i => cellData.formattedDate === i.OnDay)
            if (data && data.GuardId?.length) {
              // 项目对应的客户名称列表
              const projectNumMap = new Map()
              let tempList = []
              data.GuardId.map(id => {
                // 在guardData中找到护航单信息
                const info = (guardData || []).find(i => i.GuardId === id) || {}
                // 统计全部客户
                // const customerList = [info.CustomerName, ...(info.RelatedCustomerNames || [])]
                // 只统计主客户
                const customerList = [info.CustomerName]
                tempList = [...tempList, ...customerList]
                if (projectNumMap.has(info.Project)) {
                  projectNumMap.set(info.Project, [...projectNumMap.get(info.Project), ...customerList])
                } else {
                  projectNumMap.set(info.Project, customerList)
                }
              })
              // 项目id列表
              const keys = Array.from(projectNumMap.keys())
              // 按照项目名称升序排序
              keys.sort((a, b) => {
                return projectDict.get(a).localeCompare(projectDict.get(b));
              })
              // 日历标题
              const titleList = []
              keys.forEach(key => {
                titleList.push(<div style={{ background: colorMap[key], padding: 2 }}>{projectDict.get(key)}（{Array.from(new Set(projectNumMap.get(key))).length}家）</div>)
              })
              // 客户名称列表
              const customerNameList: any = Array.from(new Set(tempList))
              // 按照客户名称升序排序
              customerNameList.sort((a, b) => {
                return a.localeCompare(b);
              })
              return (
                <div className='cellWrap' key={cellData.formattedDate}>
                  <div className='cellTitle'>
                    <Row>
                      <Col className='labelText'><Text theme="weak">项目：</Text></Col>
                      <Col>{titleList}</Col>
                    </Row>
                  </div>
                  <Divider className='divider'></Divider>
                  <Row>
                    <Col className='labelText'><Text theme="weak">客户：</Text></Col>
                    <Col>
                      {(customerNameList || []).slice(0, 5).map(customerName => {
                        return <div style={{ paddingLeft: 2 }}>{customerName}</div>
                      })}
                      {customerNameList.length > 5 && <Button type="link">查看更多</Button>}
                    </Col>
                  </Row>
                </div>
              )
            } else {
              return ''
            }
          }
          }
        />
      </div>

      <Drawer className='drawerWrap' size='50%' footer={false} header={title} visible={currentData.OnDay} onClose={() => serCurrentData({})}>
        <Text theme="label" className='label'>护航ID｜行业｜护航名称｜客户名称｜护航当前状态｜驻场</Text>
        {(currentData.GuardId || []).map(id => {
          const data = (guardData || []).find(i => i.GuardId === id) || {}
          const guardTitle = (<div className='guardTitle'>
            <Button type="link" className='linkBtn' onClick={() => { window.open(`/advisor/guard/summary/${data.GuardId}`) }}>{data.GuardId}｜{data.Industry ? data.Industry.replace(/^、+|、+$/g, '') : ''}｜{data.GuardName}｜{data.CustomerName}｜{StatuMap[getStatu(data)]}｜{data.OnsiteTime?.length > 0 ? '驻场' : '线上'}</Button>
            <Tag className='tag' style={{ background: colorMap[data.Project] }}>{projectDict.get(data.Project)}</Tag>
          </div>)
          return data.ProductDetail?.length > 0 ?
            <Collapse borderless expandOnRowClick={false}>
              <Panel key={data.GuardId} header={guardTitle}>
                {data.ProductDetail.map((n, index) => <div key={index} className='productInfo'><span className='productName'>{n.Name}：</span> {n.Desc}</div>)}
              </Panel>
            </Collapse>
            :
            <div className='noProductInfoTitle'>{guardTitle}</div>
        }
        )}
      </Drawer >
    </>
  )
}