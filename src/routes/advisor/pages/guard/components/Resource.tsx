import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Alert, Bubble, Col, Icon, Input, message, Row, Text, Collapse, Form, SelectMultiple, TextArea, Select, LoadingTip, Modal, Button } from '@tencent/tea-component';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { ProductDescItem, ProductTemplateItem, YunapiFilterPolicyItem, InstanceItem } from '@src/types/advisor/guard';
import ResourceSon from './ResoureSon';
import _ from 'lodash';
import ResourceProductPolicy from './ResourceProductPolicy';
import { useGuard } from '@src/routes/advisor/pages/guard/components/state/GuardContext';
import { DescribeGuardArchList, DescribeArchGuardInstanceSync, DescribeGuardProductInstances } from '@src/api/advisor/guard';
import VideoBtn from '@src/components/VideoBtn';
import uuid from 'react-uuid';
import Loading from './Loading';
import cooperationImg from '@src/assets/cooperation.svg';
import SpecialApprovalModal from '@src/routes/advisor/components/special-approval-modal';
import { CheckAuthorization } from '@src/api/common';
import { authUrl } from '@src/utils/constants';
interface Props {
	appid: number,
	isAppidAuthorized: boolean,
	productDesc?: Array<ProductDescItem>,
	rejectedProducts?: Array<string>,
	instanceTemplate?: Array<InstanceItem>,
	productTemplate: Array<ProductTemplateItem>,
	instancePolicyDict: any,
	productPolicyDict: any,
	expectedEnlargeDays: number,
	expectedEnlargeTimes: number,
	updateInstanceTemplateDict: Function,
	updateProductTemplateDict: Function,
	updateProductDesc: Function,
	currentInstanceTemplate?: Array<InstanceItem>,
	guardTime?: Array<any>,
	removeValidate?: Function,
	currentGuard?: any,
	noRegionProduct?: any,
	updateArchInfo?: any,
	updateHasAuthAppId?: Function
	updateApprovalData?: Function;
	isModify?: Boolean
}

export function Resource({ isModify = false, updateArchInfo = () => {}, appid, isAppidAuthorized, productDesc,
	rejectedProducts, instanceTemplate, productTemplate, instancePolicyDict, productPolicyDict, expectedEnlargeDays,
	expectedEnlargeTimes, updateInstanceTemplateDict, updateProductTemplateDict, updateProductDesc,
	currentInstanceTemplate, guardTime, removeValidate, currentGuard, noRegionProduct,
	updateHasAuthAppId = () => {}, updateApprovalData = () => {} }: Props) {
	// 是否是自助护航
	const isSelfEscort = currentGuard.Standard === 3;
	const specialModalRef = useRef(null);
	// 是否是国际站
	const isAbroadSite = localStorage.getItem('site') === 'sinapore';
	// 资源信息收集
	const [productDict, setProductDict] = useState({}); // 产品中文名称
	const [currentProducts, setCurrentProducts] = useState<Array<string>>((productDesc || [])
		.filter(item => item.AppId == appid).map(item => item.Product)); // 已选产品
	const [unSupportedProducts, setUnSupportedProducts] = useState<Array<string>>([]);  // 暂未支持的产品
	const [supportedProducts, setSupportedProducts] = useState<Array<string>>([]);  // 支持的产品
	const [activeIds, setActiveIds] = useState<Array<string>>([]); // 展开的云产品
	const [YunapiFilterPolicy, setYunapiFilterPolicy] = useState<Array<YunapiFilterPolicyItem>>([]);
	const [productComment, setProductComment] = useState({}); // 备注
	const [productInstances, setProductInstances] = useState({}); // 手工实例列表（未开通账号的产品、开通账号&未接入云顾问或云护航的产品）
	const [isCurrentAppidAuthorized, setIsCurrentAppidAuthorized] = useState<boolean>(isAppidAuthorized);
	const { unSupportProValidate, field, changeFieldVal,
		currentAppId, onlyConatainUnSupportProduct, validateResult } = useGuard() || {};
	// 实例列
	const [instanceCol, setInstanceCol] = useState([]);
	// 是否云架构协作权限
	const [hasAuth, setHasAuth] = useState(true);
	// 特殊审批信息
	const [approvalData, setApprovalData] = useState(null);

	// 产品下拉框选项
	const [productsOptions, setProductsOptions] = useState([]);
	// 已经展开过的产品
	const [requestedProducts, setRequestedProducts] = useState([]);
	// cdn是否需要填写实例信息
	const [cdnInstancePolicy, setCdnInstancePolicy] = useState(false);

	// 架构图列表相关数据
	const [first, setFirst] = useState(true);
	// 加载
	const [loading, setLoading] = useState(false);
	// 下拉选项
	const [options, setOptions] = useState([]);
	// 搜索关键字
	const [keyword, setKeyword] = useState('');
	// 当前页码
	const [page, setPage] = useState(1);
	const PageSize = 20;
	const [totalCount, setTotalCount] = useState(0);
	const timerRef = useRef(null);
	// 选中架构图信息
	const [archInfo, setArchInfo] = useState<any>(null);
	// 架构图弹框显示
	const [svgVisible, setSvgVisible] = useState(false);
	// 是否有架构图列表
	const [hasArch, setHasArch] = useState(false);
	// 全屏loading显示
	const [showLoading, setShowLoading] = useState(false);
	// 架构图下的全部实例
	const [allInstances, setAllInstances] = useState([]);
	// 架构图下的产品
	const [archProducts, setArchProducts] = useState([]);


	useEffect(() => {
		setRequestedProducts(Array.from(new Set([...requestedProducts, ...activeIds])));
	}, [activeIds]);
	// 获取云产品清单
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: appid,
				Env: 'all',
				TaskType: 'guardTaskType',
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const tmpProductsOptions = [];
			// eslint-disable-next-line no-restricted-syntax
			for (const i in res.ProductDict) {
				tmpProductsOptions.push({ value: i, text: res.ProductDict[i] });
			}
			setProductsOptions(tmpProductsOptions);
			setProductDict(res.ProductDict);
			setUnSupportedProducts(res.UnSupportedProducts);
			setSupportedProducts(res.Products);
			setYunapiFilterPolicy(res.YunapiFilterPolicy || []);
			setInstanceCol(res.FilterPolicy);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 更新产品备注
	function updateProductComment(product: string, comment: string) {
		const tmp = _.cloneDeep(productComment);
		tmp[product] = comment;
		setProductComment(tmp);
	}

	// 更新产品手工实例列表
	function updateProductInstances(product: string, instances: string) {
		const tmp = _.cloneDeep(productInstances);
		tmp[product] = instances;
		setProductInstances(tmp);
	}

	// 查看折叠项是否存在已驳回产品
	function existRejectedProducts(activeIds: Array<string>) {
		let exist = false;
		activeIds.map((ap) => {
			(rejectedProducts || []).map((rp) => {
				if (ap == rp) {
					exist = true;
				}
			});
		});
		return exist;
	}
	const changeField = (l) => {
		if (field?.[appid] !== undefined) {
			l.forEach((item) => {
				if (field[appid]?.[item] == undefined) {
					field[appid][item] = {};
				}
				let isInTemplate = false;
				productTemplate.forEach((el) => {
					if (el.AppId == appid && el.Product == item) {
						if (el.Policy.length > 0) {
							el.Policy.forEach((val) => {
								if (val.IsRequired && currentGuard.Standard !== 3) {
									isInTemplate = true;
									// field[appid][item][val['MetricName']] = val.Value ? false : 'init';
									field[appid][item][val.MetricName] = !val.Value;
								}
							});
						}
					}
				});
				if (!isInTemplate) {
					productPolicyDict[item]?.forEach((el) => {
						if (el.IsRequired && field[appid][item][el.MetricName]
                            === undefined && currentGuard.Standard !== 3) {
							field[appid][item][el.MetricName] = true;
						}
					});
				}
			});
			currentProducts.forEach((val) => {
				let isNeed = false;
				l.forEach((el) => {
					if (el === val) {
						isNeed = true;
					};
				});
				if (!isNeed) {
					field[appid][val] = {};
				}
			});
			changeFieldVal(appid, field[appid]);
		}
		setCurrentProducts(l);
	};
	// 根据护航实例清单，筛选出关联云产品列表
	useEffect(() => {
		const l = [];
		instanceTemplate.map((i) => {
			if (l.indexOf(i.Product) === -1 && i.AppId === appid) {
				l.push(i.Product);
			}
		});
		currentProducts.map((p) => {
			if (l.indexOf(p) === -1) {
				l.push(p);
			}
		});
		changeField(l);
		setCurrentProducts(l);
	}, [instanceTemplate]);

	// 刷新APPID对应的产品选项
	useEffect(() => {
		const tmp = [];
		currentProducts.map((product) => {
			tmp.push({
				AppId: appid,
				Product: product,
				InstanceIds: productInstances[product],
			});
		});
		updateProductDesc(appid, tmp);
	}, [currentProducts]);

	// 刷新产品备注及手工实例
	useEffect(() => {
		if (Object.keys(productInstances).length > 0 || Object.keys(productComment).length > 0) {
			const tmp = [];
			currentProducts.map((product) => {
				tmp.push({
					AppId: appid,
					Product: product,
					InstanceIds: productInstances[product],
					Comment: productComment[product],
				});
			});
			updateProductDesc(appid, tmp);
		}
	}, [productInstances, productComment]);

	// 加载产品备注及手工实例
	useEffect(() => {
		const currentProductDesc = (productDesc || []).filter(i => i.AppId === appid);
		const tmpComment = {};
		const tmpInstances = {};
		currentProductDesc.map((item) => {
			tmpComment[item.Product] = item.Comment;
			tmpInstances[item.Product] = item.InstanceIds;
		});
		setProductComment(tmpComment);
		setProductInstances(tmpInstances);
	}, []);

	useEffect(() => {
		setIsCurrentAppidAuthorized(isAppidAuthorized);
	}, [isAppidAuthorized]);

	const resourceRef = useRef(null);

	// init
	useEffect(() => {
		getProductsGroupsInfo();
	}, []);

	const checkErrorVisible = (product) => {
		// eslint-disable-next-line max-len
		if (currentInstanceTemplate?.filter(item => item.AppId == appid && item.Product == product).length === 0 && !onlyConatainUnSupportProduct[appid] && supportedProducts.includes(product)) {
			return true;
		}
		//  && !activeIds.includes(product)
		if (unSupportProValidate?.[appid]?.[product] === true) {
			return true;
		}
		for (const key in field?.[appid]?.[product]) {
			if (field[appid][product][key] === true) {
				return true;
			}
		}
		// eslint-disable-next-line max-len
		const noPass = (validateResult || [])?.find(i => i.Product == product && i.AppId == appid && i.PassValidate === false);
		if (noPass) {
			return true;
		}
		return false;
	};

	// 滚动获取架构图列表
	const fetch = useCallback(() => {
		if (timerRef.current) {
			clearTimeout(timerRef.current);
			timerRef.current = null;
		}
		const id = setTimeout(getArchList, 300);
		timerRef.current = id;
		// 查询架构图列表
		async function getArchList() {
			if (id !== timerRef.current) {
				setLoading(false);
				return;
			}
			try {
				const res = await DescribeGuardArchList({
					AppId: appid,
					PageNumber: page,
					PageSize,
					Filters: [{ Name: 'SearchKey', Values: [keyword] }],
				});
				if (res.Error) {
					setLoading(false);
					const msg = res.Error.Message;
					message.error({ content: msg });
					return;
				}
				// 记录总条数
				setTotalCount(res.TotalCount || 0);
				const newOptions = (res.GuardArchList || [])
					.map(i => ({
						...i,
						value: i.ArchId,
						text: i.ArchName,
						// 沿用驻场护航限制
						disabled: i.IsRelateTaskNum && i.GuardStatus > 1,
					}));
				setOptions((options) => {
					const mergedOptions = [...options, ...newOptions].reduce((acc, option) => {
						if (!acc[option.value]) {
						  acc[option.value] = option;
						}
						return acc;
					  }, {});
					const uniqueOptions = Object.values(mergedOptions);
					return uniqueOptions;
				});
				setLoading(false);
			} catch (err) {
				setLoading(false);
				const msg = err.msg || err.toString() || '未知错误';
				message.error({ content: msg });
			}
		}
		setLoading(true);
	}, [keyword, page]);

	// 已选择架构图回显
	async function initSavedArch() {
		const createEnv = ['ISA', 'CONSOLE'];
		// 选择的架构图id
		const archId = currentGuard?.CloudGuardBaseInfoOtherPlatform?.
			find(i => createEnv.includes(i.Platform) && i.AppId === appid)?.PlatformUniqueId;
		if (archId) {
			try {
				const res = await DescribeGuardArchList({
					AppId: appid,
					PageNumber: 1,
					PageSize: 10,
					Filters: [{ Name: 'ArchId', Values: [archId] }],
				});
				if (res.Error) {
					const msg = res.Error.Message;
					message.error({ content: msg });
					return;
				}
				const newOptions = (res.GuardArchList || [])
					.map(i => ({
						...i,
						value: i.ArchId,
						text: i.ArchName,
						// 沿用驻场护航限制
						disabled: i.IsRelateTaskNum && i.GuardStatus > 1,
					}));
				setOptions(newOptions);
				setArchInfo(newOptions?.[0]);
				updateArchInfo(newOptions?.[0], appid);
			} catch (err) {
				const msg = err.msg || err.toString() || '未知错误';
				message.error({ content: msg });
			}
		}
	}

	useEffect(() => {
		first && initSavedArch();
	}, [currentGuard]);

	useEffect(() => {
		if (!first) {
			fetch();
		}
	}, [fetch, first, keyword, page]);

	// 初始化权限
	async function initPermission() {
		try {
			const res = await DescribeGuardArchList({
				AppId: appid,
				PageNumber: 1,
				PageSize,
				Filters: [],
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			// 记录是否有架构图列表
			setHasArch(res?.TotalCount > 0);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	}

    	// 查询架构图下产品绑定的实例
	async function getPageInstances(product, mapId) {
		try {
			const params = {
				AppId: appid,
				Products: [product],
				Offset: 0,
				// 查全部实例
				Limit: -1,
				// 从架构图获取选择的架构图id
				MapId: mapId,
			};
			const res = await DescribeGuardProductInstances(params);
			if (res.Error) {
				return Promise.reject(res.Error);
			}
			const instances = (res?.Instances?.[product]?.Instance || []).map((i) => {
				i.Product = product;
				return i;
			});
			return Promise.resolve(instances || []);
		} catch (err) {
			return Promise.reject(err);
		}
	}

	// 同步架构图资源
	function initArchResource(MapId) {
		if (MapId) {
			setShowLoading(true);
			const params = {
				AppId: appid,
				MapId,
				TemplateId: uuid(),
			};
			// 获取架构图产品
			DescribeArchGuardInstanceSync(params)
				.then((res) => {
					if (res?.GuardProducts) {
						setCurrentProducts(res.GuardProducts);
						setArchProducts(res.GuardProducts);
						// 更新产品报备信息校验
						changeField(res.GuardProducts);
						// 全部产品都请求实例
						const resultList = [];
						(res.GuardProducts || []).forEach((product) => {
							setTimeout(() => {
								// 查架构图下每个产品的实例
								resultList.push(getPageInstances(product, MapId));
							}, 100);
						});
						setTimeout(async () => {
							await Promise.all(resultList).then((res1) => {
								if (res1?.length) {
									// @ts-ignore
									const resultInstance = (res1?.flat() || []).map((i) =>  {
										const instancePolicys = instancePolicyDict[i.Product] || [];
										const policys = _.cloneDeep(instancePolicys);
										instancePolicys.map((i, index) => {
											policys[index].Value = expectedEnlargeTimes;
											policys[index].Days = expectedEnlargeDays;
											// CDN默认值为1
											if (policys[index].MetricName.trim() == 'CDNBandwidth' || policys[index].MetricName.trim() == 'CDNQPS') {
												policys[index].Value = 0.0;
											}
										});
										// 标签
										const showTags = [];
										if (i.Tag?.length) {
											(i.Tag || []).map((i) => {
												const item = `${i.TagKey}:${i.TagValues}`;
												showTags.push(item);
											});
										}
										return {
											...i,
											Extra: i.Extra,
											AppId: appid,
											Product: i.Product,
											Important: 0,
											Policy: policys,
											InstanceTag: showTags.length ? showTags.join('; ') : '',
										};
									});
									setAllInstances(resultInstance);
									// 非兜底实例对象
									const instanceMap = {};
									resultInstance.forEach((i) => {
										if (supportedProducts.includes(i.Product)) {
											if (instanceMap[i.Product]) {
												instanceMap[i.Product].push(i);
											} else {
												instanceMap[i.Product] = [i];
											}
										}
									});
									// 更新上层组件实例
									// eslint-disable-next-line no-restricted-syntax
									for (const key in instanceMap) {
										setTimeout(() => {
											updateInstanceTemplateDict(appid, key, instanceMap[key]);
										}, 10);
									}
									resultInstance.forEach((i) => {
										// 如果是兜底产品，要填充实例到输入框
										if (unSupportedProducts.includes(i.Product)) {
											const oldValue = productInstances[i.Product];
											updateProductInstances(i.Product, oldValue ? `${oldValue}/n${i.InstanceId}` : i.InstanceId);
										}
									});
								}
								setShowLoading(false);
							})
								.catch((err) => {
									console.log(err);
									message.error({
										content: '返回数据有误，请刷新页面',
									});
									setShowLoading(false);
								});
						}, 200);
					} else {
						message.error({ content: '架构图没有绑定实例的产品' });
					}
					setShowLoading(false);
				})
				.catch((err) => {
					setShowLoading(false);
					const msg = err.msg || err.toString() || '未知错误';
					message.error({ content: msg });
				});
		}
	}

	// 权限提示
	function permissionTip() {
		// 没有协作的架构图
		if (!hasArch) {
			return <div className='tipBubble'>
				<Text theme="text">客户未有协作状态架构图，建议联系客户进行绘制、并在租户端发起护航。也可从运营端协助客户完成绘制等操作</Text>
				<div className='bubbleFooter'>
					<Button type="link" style={{ textDecoration: 'underline' }} onClick={() => {
						window.open(`${location.origin}/advisor/new-architecture`);
					}}>运营端</Button>
					<VideoBtn></VideoBtn>
				</div>
			</div>;
		}
		return null;
	}

	// 获取权限
	async function getAuth() {
		try {
			const res = await CheckAuthorization({
				CustomerAppId: appid,
				IsCheckTAM: false,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			// 1：不是铁三角 10：铁三角但用户没授权 100：铁三角且用户已经授权 20：AppId不存在
			const auth = res?.AuthCode === 100;
			setHasAuth(auth);
			// 将权限传递给上层组件。控制下一步是否可点击
			if (auth) {
				updateHasAuthAppId(appid);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	}

	useEffect(() => {
		if (!isAbroadSite) {
			initPermission();
			if (!isSelfEscort) {
				getAuth();
			}
		}
	}, []);

	return (
		<div style={{ marginTop: 10 }}>
			<div>
				<Form layout='inline' className='selectForm'>
					{!hasAuth && !isModify && <Alert type={approvalData?.Reason ? 'warning' : 'error'} className='selectAlert'>
						<span>客户未打开云架构服务授权，请指引客户访问</span>
						<Text theme="primary" copyable className='authLink' onClick={() => {
							window.open(authUrl);
						}}>{authUrl}</Text>
						  <Bubble
							arrowPointAtCenter
							placement="top"
							overlayClassName='cooperationBubble'
							content={<img src={cooperationImg} />}
						>
							<span className='imgHover'>打开“云架构协作”。</span>
						</Bubble>
						<span className='specialLink' onClick={() => {
							specialModalRef.current.showModal();
						}}>无法完成授权，申请特批。</span>
						{approvalData?.Reason && <span style={{ marginLeft: 16 }}>- 已申请特批</span>}
					</Alert>}
					{/* 特殊审批弹框 */}
					{currentGuard.Standard !== 3 && <SpecialApprovalModal
						type={1}
						ref={specialModalRef}
						guardData={currentGuard}
						appId={appid}
						updateApprovalData={(data) => {
							setApprovalData(data);
							updateApprovalData({ ...data, AppId: appid });
						}}
					/>}
					<Text style={{ fontWeight: 700, marginBottom: 10 }} parent='div' theme="text">若客户未有绘制架构图，可以先选择右侧"涉及云产品"发起护航。</Text>
					{!isAbroadSite &&  <Form.Item label="从架构图获取" className='archItem'>
						<Bubble content={permissionTip()}>
							<Select
								disabled={!hasArch}
								// eslint-disable-next-line no-nested-ternary
								placeholder={(!hasArch ? '客户未有协作状态架构图，请直接在右侧选择护航涉及云产品' : '请选择架构图')}
								clearable
								searchable
								matchButtonWidth
								size="m"
								appearance="button"
								autoClearSearchValue={false}
								onOpen={() => {
									setFirst(false);
								}}
								onSearch={(value) => {
									setKeyword(value);
									setPage(1);
									setOptions([]);
								}}
								onScrollBottom={() => {
									if (!loading && page < Math.ceil(totalCount / PageSize)) {
										setPage(page => page + 1);
									}
								}}
								options={options || []}
								value={archInfo?.ArchId || null}
								onChange={(value) => {
									// 如果选择了架构图，就展示架构图资源
									if (value) {
										const item = options.find(i => i.value === value) || {};
										setArchInfo(item);
										updateArchInfo(item, appid);
										setActiveIds([]);
										initArchResource(item?.value);
									} else {
										// 清空选择的架构图
										setArchInfo(null);
										updateArchInfo(null, appid);
										// 清空保存的架构图实例
										setAllInstances([]);
										// 未选择架构图，就展示已保存资源
										const initProducts = (currentGuard?.ProductDesc || [])
											.filter(item => item.AppId === appid)
											.map(item => item.Product);
										setCurrentProducts(initProducts);
									}
								}}
								tips={`共${totalCount}条`}
								bottomTips={loading && <LoadingTip />}
							/>
						</Bubble>
						{/* 全屏遮罩 */}
						<Loading show={showLoading}></Loading>
					</Form.Item>}
					{archInfo?.ArchSvg && <Bubble
						arrowPointAtCenter
						placement="top"
						content="点击预览架构图"
					>
						<Icon type="infoblue" className='archTip' onClick={() => {
							 setSvgVisible(true);
						}} />
					</Bubble>}
					<Form.Item label="涉及云产品">
						<SelectMultiple
							listHeight={400}
							boxClassName='productListBox'
							options={productsOptions}
							value={currentProducts}
							onChange={(v) => {
								changeField(v);
							}}
							appearance="button"
							searchable
							size="m"
						/>
					</Form.Item>
				</Form>
			</div>
			<Modal visible={svgVisible} className='archSvgModal' disableCloseIcon maskClosable onClose={() => {
				setSvgVisible(false);
			}}>
				<Modal.Body>
					<div className='archSvgWrap'>
						<Icon type="close" className='closeBtn' onClick={() => {
							setSvgVisible(false);
						}} />
						{/* 架构图片 */}
						{archInfo?.ArchSvg && <div className='svgWrap' dangerouslySetInnerHTML={{ __html: archInfo?.ArchSvg }}></div>}
						<Button type="primary" className='goBtn' onClick={() => {
							window.open(`${location.origin}/advisor/new-architecture/architecture/${archInfo?.value}?appid=${appid}`);
						}}>去编辑架构图</Button>
					</div>
				</Modal.Body>
			</Modal>
			<Collapse
				className="intlc-assessment-tabitem__content"
				activeIds={activeIds}
				onActive={(v) => {
					if (existRejectedProducts(v)) {
						setActiveIds([]);
					} else {
						setActiveIds(v);
					}
				}}
				destroyInactivePanel={false}
			>
				{
					// relateProducts.map(product => {
					currentProducts.map(product => <div key={product} >
						<Collapse.Panel
							style={{ marginTop: 10 }}
							key={product}
							id={product}
							title={[
								productDict[product],
								< Bubble
									key={`unSupported${product}`}
									arrowPointAtCenter
									placement="right"
									content="该产品未接入云护航，仅分配护航人员"
								>
									{unSupportedProducts.includes(product)
                                            && !(rejectedProducts || []).includes(product)
                                            && <Icon type="info" style={{ marginLeft: 2 }} />}
								</Bubble>,
								< Bubble
									key={`rejected${product}`}
									arrowPointAtCenter
									placement="right"
									content="该产品已驳回，不支持修改"
								>
									{(rejectedProducts || []).includes(product) && <Icon type="dismiss" style={{ marginLeft: 2 }} />}
								</Bubble>,
								checkErrorVisible(product)
                                    && <Text key={product} style={{
                                    	marginLeft: '5px',
                                    }} theme={'danger'}>
                                        必填资源信息未填写
                                    </Text>,
							]}
						>

							{/* 场景一：已授权账号、已接入云顾问的产品，通过穿梭框勾选实例 */}
							{(isCurrentAppidAuthorized && supportedProducts.includes(product))
                                    && <ResourceSon
                                    	noRegionInfo={(noRegionProduct || [])
                                    		.filter(i => i.Product === product)?.[0] || null}
                                    	currentGuard={currentGuard}
                                    	cdnInstancePolicy={cdnInstancePolicy}
                                    	requested={requestedProducts.includes(product)}
                                    	// eslint-disable-next-line max-len
                                    	CallBack={(appid, product, records) => updateInstanceTemplateDict(appid, product, records)}
                                    	product={product}
                                    	instancePolicys={instancePolicyDict[product] || []}
                                    	key={product}
                                    	instances={instanceTemplate.filter((i) => {
                                    		if (i.AppId === appid) {
                                    			return i;
                                    		}
                                    	})}
                                    	appid={appid}
                                    	expectedEnlargeDays={expectedEnlargeDays}
                                    	expectedEnlargeTimes={expectedEnlargeTimes}
                                    	ref={resourceRef}
                                    	filterInfo={
                                    		YunapiFilterPolicy.filter((i) => {
                                    			if (i.Product === product) {
                                    				return i;
                                    			}
                                    		}).length > 0
                                    			? YunapiFilterPolicy.filter((i) => {
                                    				if (i.Product === product) {
                                    					return i;
                                    				}
                                    			})[0].FilterInfo : []
                                    	}
                                    	instanceCol={instanceCol}
                                    	archInfo={archInfo}
                                    	archProducts={archProducts}
                                    	allProductInstances ={allInstances
                                    		.filter(i => supportedProducts.includes(i.Product) && i.Product === product)
                                    	}
                                    />
							}
							{/* 场景二：1）已授权账号、未接入云顾问的产品，通过文本框填写实例。2）或：未授权账号的产品，通过文本框填写实例 */}
							{(!isCurrentAppidAuthorized
                            || (isCurrentAppidAuthorized && unSupportedProducts.includes(product)))
                                    && <section>
                                    	<Row verticalAlign="middle" style={{ marginTop: 10 }}>
                                    		<Col span={2} >
                                    			<Text style={{ color: 'red' }} verticalAlign="middle">*</Text>
                                    			<Text theme="label">重点护航实例</Text>
                                    		</Col>
                                    		<Col span={22}>
                                    			<Bubble
                                    				error
                                    				visible={!!(activeIds.includes(product)
                                                        && unSupportProValidate?.[appid]?.[product] === true
                                                        && currentAppId == String(appid))}
                                    				content={'不能为空'}
                                    			>
                                    				<TextArea
                                    					size="l"
                                    					value={productInstances[product]}
                                    					onChange={(v) => {
                                    						updateProductInstances(product, v);
                                    					}}
                                    					placeholder="客户未开通云顾问，或产品未接入云顾问。请手动输入护航实例，每行一个实例。如果是特殊场景没有实例列表，请说明具体情况。"
                                    				/>
                                    			</Bubble>
                                    		</Col>
                                    	</Row>
                                    </section>}

							<section>
								<Row verticalAlign="middle" style={{ marginTop: 15, marginBottom: 10 }}>
									<Col span={2} >
										<Text theme="label">备注</Text>
									</Col>
									<Col span={22} >
										<Input
											size={(unSupportedProducts.includes(product) || !isCurrentAppidAuthorized) ? 'l' : 'full'}
											value={productComment[product]}
											onChange={(value) => {
												updateProductComment(product, value);
											}}
											placeholder="护航特殊需求备注"
										/>
									</Col>
								</Row>
							</section>

							<section>
								{productPolicyDict[product] && <hr />}
								{productPolicyDict[product]
                                        && <ResourceProductPolicy
                                        	currentGuard={currentGuard}
                                        	cdnPolicyChange={(val) => {
                                        		setCdnInstancePolicy(val);
                                        		removeValidate?.(!val, appid, product);
                                        	}}
                                        	activeIds={activeIds}
                                        	appid={appid}
                                        	product={product}
                                        	// eslint-disable-next-line max-len
                                        	productTemplateData={productTemplate.filter(pt => pt.AppId == appid && pt.Product == product)[0] || {} as ProductTemplateItem}
                                        	// eslint-disable-next-line max-len
                                        	productPolicy={(currentProducts.includes(product) && productPolicyDict[product]) || []}
                                        	updateProductTemplateDict={(appid, product, policySet) => {
                                        		updateProductTemplateDict(appid, product, policySet);
                                        	}}
                                        	guardTime={guardTime}
                                        />
								}
							</section>
						</Collapse.Panel>
					</div>)
				}
			</Collapse >
		</div >
	);
}
