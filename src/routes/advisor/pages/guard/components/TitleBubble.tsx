import React, { useState, useEffect, useMemo } from 'react';
import {
	Bubble
} from '@tencent/tea-component';

export function TitleBubble({title, content, style}: {
	title: string,
	content: string,
	style?: any
}) {
	return (
		<div
			style={
				{
					display: 'flex',
					alignItems: 'center',
					...(style || {})
				}
			}
		>
			{title}
			<Bubble
				arrowPointAtCenter
				placement="top"
				content={content}
			>
				<svg
					style={
						{
							marginLeft: '3px',
							cursor: 'pointer'
						}
					}
					xmlns="http://www.w3.org/2000/svg"
					width="16"
					height="16"
					viewBox="0 0 24 24"
					fill="none"
				>
					<g id="ç³»ç»å¸®å©-System-help">
						<path id="stroke3 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM12 8.55556C11.325 8.55556 10.7778 9.10276 10.7778 9.77778V10.7778H8.77778V9.77778C8.77778 7.99819 10.2204 6.55556 12 6.55556C13.7796 6.55556 15.2222 7.99819 15.2222 9.77778C15.2222 10.65 14.8744 11.4429 14.3121 12.0221L13 13.398V14.1111H11V12.5972L12.8755 10.6306C13.091 10.4093 13.2222 10.1099 13.2222 9.77778C13.2222 9.10276 12.675 8.55556 12 8.55556ZM11 15.4444H13.0043V17.4488H11V15.4444Z" fill="black" fill-opacity="0.4"/>
					</g>
				</svg>
			</Bubble>
		</div>
	);
}
