import React, { useState, useEffect, forwardRef, useRef } from 'react';
import { Col, Row, Text, TextArea, Bubble, InputNumber, DatePicker, Tag, Select, SelectMultiple, Icon, Form, message } from '@tencent/tea-component';
import { ProductPolicyItem, ProductTemplateItem } from "@src/types/advisor/guard";
import _ from 'lodash';
import { useGuard } from "@src/routes/advisor/pages/guard/components/state/GuardContext";
const { RangePicker } = DatePicker;
import moment from "moment";
// 直播播放协议
const protocol = [
    { value: "hls", text: "hls" },
    { value: "flv", text: "flv" },
    { value: "rtmp", text: "rtmp" },
    { value: "webrtc(快直播)", text: "webrtc(快直播)" },
    { value: "quic", text: "quic" },
]
interface Props {
    appid: number,
    product: string,
    productTemplateData: any, // 首次加载、初始化输入框值
    productPolicy: Array<ProductPolicyItem>   // 从父组件同步有修改的策略值(通过updateProductTemplateDict更新productTemplateDict然后读取该值)
    updateProductTemplateDict: Function,      // 往父组件传递有修改的策略值
    activeIds: Array<string>,
    guardTime?: Array<any>,
    cdnPolicyChange?: Function,
    currentGuard?: any
}

function ResourceProductPolicy({ appid, product, productTemplateData, productPolicy, updateProductTemplateDict, activeIds, guardTime,cdnPolicyChange, currentGuard }: Props) {
    const resourceRef = useRef(null);
    const [productPolicyValueMap, setProductPolicyValueMap] = useState(new Map<string, string>())
    const [currentProductPolicy, setCurrentProductPolicy] = useState<Array<ProductPolicyItem>>(_.cloneDeep(productPolicy))
    const { field, changeOneField, currentAppId } = useGuard() || {};
    useState<Array<string>>([]);

    // 动态变量名
    const [dynamicVariables, setDynamicVariables] = useState({});

    // 更新动态变量
    const updateDynamicVariable = (variableName, value) => {
        setDynamicVariables((prevVariables) => ({
            ...prevVariables,
            [variableName]: value,
        }));
    };

    // 增加变量，防止死循环
    const [policyChanged, setPolicyChanged] = useState(false);

    // 初始化数据
    function initData() {
        currentProductPolicy.map((policy: any) => {
            // 格式化信息
            let FormatType = policy.FormatType?.[0] || {}
            if (FormatType.Type === 'DatetimeMultiple' || FormatType.Type === 'DatetimeSingle' || FormatType.Type === 'SelectSingle' || FormatType.Type === 'SelectMultiple') {
                updateDynamicVariable(policy.MetricName, (policy.Value ? policy.Value.split(';') : []))
            }
        })
    }

    useEffect(() => {
        if (policyChanged) {
            initData();
            setPolicyChanged(false);
        }
    }, [policyChanged]);

    useEffect(() => {
        setPolicyChanged(true);
    }, [currentProductPolicy]);

    // 更新policy值和校验信息
    useEffect(() => {
        for (let key in dynamicVariables) {
            let value = dynamicVariables[key].join(';')
            if (field && changeOneField && field?.[appid]?.[product] && field[appid][product].hasOwnProperty(key)) {
                field[appid][product][key] = value ? false : true;
                changeOneField(field);
            }
            updateProductPolicyValue(key, value)
        }
    }, [dynamicVariables])


    // 根据输入框填写值更新productPolicyValueDict变量
    function updateProductPolicyValue(metric, value) {
        let key = appid + "^" + product + "^" + metric
        setProductPolicyValueMap(preValue => {
            let tmp = _.cloneDeep(preValue)
            tmp.set(key, value)
            return tmp
        })
    }

    // 展示策略编辑列表
    function getProductEditList() {
        let leftCols = []
        let rightCols = []
        // 兼容存量，判断是使用保存过的产品信息，还是最新的产品信息
        let isSave = !_.isEmpty(productTemplateData) && productTemplateData.Policy.length != 0
        let data = isSave ? productTemplateData.Policy : currentProductPolicy
        data.map((policy: any, idx) => {
            // 格式化信息
            let FormatType = policy.FormatType?.[0] || {}
            let inputDom = <TextArea
                style={{ width: '100%' }}
                rows={4}
                placeholder={policy.Desc || ''}
                value={policy.Value || ''}
                onChange={(value) => {
                    if (field && changeOneField && field?.[appid]?.[product] && field[appid][product].hasOwnProperty(policy.MetricName)) {
                        field[appid][product][policy.MetricName] = value ? false : true;
                        changeOneField(field);
                    }
                    updateProductPolicyValue(policy.MetricName, value)
                }}
            />
            if (policy.FormatType) {
                if (FormatType.Type === 'DatetimeMultiple' || FormatType.Type === 'DatetimeSingle') {
                    // 当前产品信息的值
                    let list = dynamicVariables[policy.MetricName] || []
                    // 显示的值	
                    const [LiveTimeShow, setLiveTimeShow] = useState<any>('');
                    inputDom = <div>
                        <Bubble
                            arrowPointAtCenter
                            placement="bottom"
                            content="请选择到小时"
                        >
                            <RangePicker
                                range={[moment(guardTime[0]), moment(guardTime[1])]}
                                disabled={FormatType.Type === 'DatetimeSingle' && list.length >= 1}
                                value={LiveTimeShow}
                                format="YYYY-MM-DD HH:mm"
                                showTime={{ format: "HH:mm" }}
                                onChange={(value) => {
                                    setLiveTimeShow('')
                                    let tmp = _.cloneDeep(list);
                                    let d = value[0].format('YYYY/MM/DD HH:mm') + '~' + value[1].format('YYYY/MM/DD HH:mm');
                                    if (tmp.indexOf(d) === -1) {
                                        tmp.push(d);
                                    }
                                    // 时间排序
                                    tmp.sort((a, b) => {
                                        const dateA = new Date(a.split('~')[0]);
                                        const dateB = new Date(b.split('~')[0]);
                                        // @ts-ignore
                                        return dateA - dateB;
                                    });
                                    updateDynamicVariable(policy.MetricName, tmp)
                                }}
                            />
                        </Bubble>
                        {list.length > 0 && (
                            <div>
                                {list.map((i, index) => {
                                    return (
                                        <Tag
                                            onClose={(v) => {
                                                updateDynamicVariable(policy.MetricName,
                                                    list.filter((j) => {
                                                        if (j != i) {
                                                            return j;
                                                        }
                                                    })
                                                );
                                            }}
                                            key={index}
                                        >
                                            {i}
                                        </Tag>
                                    );
                                })}
                            </div>
                        )}
                    </div>
                } else if (FormatType.Type === 'SelectSingle' || FormatType.Type === 'SelectMultiple') {
                    let selectDom = <></>
                    // 临时保存当前值
                    let temp = _.cloneDeep(dynamicVariables[policy.MetricName] || [])
                    let lastOne = ''
                    let relatedValue = ''
                    if (temp.length) {
                        // 下拉关联的最后一个值
                        lastOne = temp[temp.length - 1]
                        let list = lastOne ? lastOne.split(/::(.+)/) : []
                        temp[temp.length - 1] = list[0] || ''
                        relatedValue = list.slice(1).join('')
                    }
                    // 构造下拉选项
                    let options = FormatType.Value.map(i => ({ value: i })) || []
                    if (FormatType.Type === 'SelectSingle') {
                        // 当前产品信息的值
                        let Value = temp?.[0] || ''
                        // 初始化实例Policy是否展示
                        if(policy.MetricName === "CDNRequirementType"){
                            cdnPolicyChange(Value==='突发护航需求')
                        }
                        selectDom = <Select
                            style={{ width: policy.MetricName === "CDNConverageDemand" ? 160 : '100%', verticalAlign: 'top' }}
                            appearance="button"
                            options={options}
                            value={Value}
                            onChange={value => { 
                                updateDynamicVariable(policy.MetricName, [value]) 
                                if(policy.MetricName === "CDNRequirementType"){
                                    if(value==='突发护航需求'){
                                        message.warning({ content: '如果选择“突发护航需求”，请完善“已选资源”的报备信息' })
                                    }
                                    cdnPolicyChange(value==='突发护航需求')
                                }
                            }}
                        />
                    } else {
                        // 当前产品信息的值
                        let Value = temp || []
                        selectDom = <SelectMultiple
                            style={{ width: '100%', verticalAlign: 'top' }}
                            clearable
                            options={options}
                            staging={false}
                            appearance="button"
                            value={Value}
                            onChange={value => { updateDynamicVariable(policy.MetricName, value) }}
                        />
                    }
                    // 关联的类型
                    let relatedDom = <></>
                    if (policy.FormatType?.length > 1) {
                        let relatedData = policy.FormatType[1] || {}
                        if (relatedData.Type === 'SelectRelated' && relatedData.Value?.[0]?.RelatedType === 'Text') {
                            relatedDom = <TextArea
                                disabled={!temp.includes(relatedData.Value?.[0]?.RelatedValidWithValue)}
                                style={{ marginLeft: 20, width: 'calc(100% - 180px)' }}
                                rows={4}
                                value={relatedValue || ''}
                                onChange={(value) => {
                                    if (value) {
                                        temp[temp.length - 1] += '::' + value
                                    } else {
                                        temp[temp.length - 1] = temp[temp.length - 1]
                                    }
                                    updateDynamicVariable(policy.MetricName, [...temp])
                                }}
                            />
                        }
                    }
                    inputDom = <div>{selectDom}{relatedDom}</div>
                } else if (FormatType.Type === 'Num') {
                    if ((policy.Value !== '' && policy.Value !== null)) {
                        // 这里组件的value绑定有问题，无法满足校验，要拆成两个
                        inputDom = <InputNumber
                            key={'has' + product + idx}
                            allowEmpty={true}
                            value={Number(policy.Value)}
                            onChange={(value: any) => {
                                if (field && changeOneField && field?.[appid]?.[product] && field[appid][product].hasOwnProperty(policy.MetricName)) {
                                    field[appid][product][policy.MetricName] = (value !== '' && value !== null) ? false : true;
                                    changeOneField(field);
                                }
                                updateProductPolicyValue(policy.MetricName, String(value))
                            }}
                            min={FormatType.Value[0] || 0}
                            max={FormatType.Value[1]}
                            step={1}
                        />
                    } else {
                        inputDom = <InputNumber
                            key={'no' + product + idx}
                            allowEmpty={true}
                            value={null}
                            onChange={(value: any) => {
                                if (field && changeOneField && field?.[appid]?.[product] && field[appid][product].hasOwnProperty(policy.MetricName)) {
                                    field[appid][product][policy.MetricName] = (value !== '' && value !== null) ? false : true;
                                    changeOneField(field);
                                }
                                updateProductPolicyValue(policy.MetricName, String(value))
                            }}
                            min={FormatType.Value[0] || 0}
                            max={FormatType.Value[1]}
                            step={1}
                        />
                    }
                }
            }
            // 是否为空
            let emptyFlag = activeIds.includes(product) && field?.[appid]?.[product]?.[policy.MetricName] === true && currentAppId == String(appid) ? true : false;
            let cell =
                <section>
                    <Row verticalAlign={"top"}>
                        <Col span={6} >
                            {policy.IsRequired && currentGuard.Standard !== 3 && <Text style={{ color: "red" }} verticalAlign="middle">*</Text>}
                            <Text theme="label" verticalAlign="middle">{policy.CNName || ''}</Text>
                            {
                                policy.Desc && <Bubble
                                    arrowPointAtCenter
                                    placement="top"
                                    content={policy.Desc}
                                >
                                    <Icon style={{ marginLeft: 4 }} type="info" />
                                </Bubble>
                            }
                        </Col>
                        <Col span={18}>
                            <Form.Control showStatusIcon={false} status={(emptyFlag) ? 'error' : 'success'} message={emptyFlag ? '此项为必填项' : ''}>
                                {inputDom}
                            </Form.Control>
                        </Col>
                    </Row>
                </section>
            if (idx % 2) {
                rightCols.push(cell)
            } else {
                leftCols.push(cell)
            }
        })

        let tmp =
            <>
                <Row gap={100} className='productPolicyWrap'>
                    <Col>
                        {leftCols}
                    </Col>
                    <Col>
                        {rightCols}
                    </Col>
                </Row>
            </>

        return tmp
    }

    // 根据输入框填写值更新ProductTemplate。输入框->productPolicyValueMap->updateProductTemplateDict->父组件
    useEffect(() => {
        let productTemplateList = []
        currentProductPolicy.map((policy, idx) => {
            let newPolicy = policy

            productPolicyValueMap.forEach((value, key) => {
                // appid + "^" + product + "^" + metric
                let words = key.split('^')
                if (words.length == 3) {
                    let metric = words[2]
                    if (policy.MetricName == metric) {
                        newPolicy.Value = value
                    }
                }
            });

            productTemplateList.push(newPolicy)
        })
        setCurrentProductPolicy(productTemplateList)
        updateProductTemplateDict(appid, product, productTemplateList)
    }, [productPolicyValueMap])


    // init
    useEffect(() => {
        if (!_.isEmpty(productTemplateData) && productTemplateData.Policy.length != 0) {
            setCurrentProductPolicy(productTemplateData.Policy)                    // 初始化当前编辑与查看的值
            updateProductTemplateDict(appid, product, productTemplateData.Policy)  // 往父组件传值
        }
    }, []);
    return (
        <div style={{ marginTop: 10 }}>
            <div>
                {getProductEditList()}
            </div>
        </div >
    )
}

export default ResourceProductPolicy