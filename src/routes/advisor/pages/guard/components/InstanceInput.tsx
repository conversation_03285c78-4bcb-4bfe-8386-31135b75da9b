import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Bubble } from "@tencent/tea-component";
import { Input, Form, } from "@tencent/tea-component";
import _ from 'lodash'
interface Props {
    inputId?: string,
    value?: string,
    CallBack?: Function,
    placeholder?: string,
    required?: Boolean,
    regRule?: RegExp,
    inputValidate?: Function
}

function MyInput({ inputId, value, CallBack, placeholder = '', required = false, regRule, inputValidate }: Props, ref) {
    // 输入框状态
    const [status, setStatus] = useState<any>('success')
    // 输入框提示文字
    const [message, setMessage] = useState('')

    useEffect(() => {
        // 必填校验
        if (required && value === '') {
            setStatus('error')
            setMessage('此项为必填项')
            return
        }
        // 正则校验
        if (regRule) {
            if (regRule.test(value)) {
                setStatus('success')
                setMessage('')
            } else {
                setStatus('error')
                setMessage('请输入正确的值')
            }
            return
        }
        // 通过必填校验或没有必填校验
        setStatus('success')
        setMessage('')
    }, [value])

    useEffect(() => {        
        inputValidate(status === 'success')
    }, [status])

    return (
        <>
            <Bubble placement={"top"} visible={false}>
                <Form hideLabel>
                    <Form.Item status={status} message={message} showStatusIcon={false}>
                        <Input id={inputId}
                            onChange={(value) => {
                                CallBack(value.trim())
                            }}
                            defaultValue={value}
                            placeholder={placeholder}
                            size="full" />
                    </Form.Item>
                </Form>
            </Bubble>
        </>
    )
}

export default forwardRef(MyInput)