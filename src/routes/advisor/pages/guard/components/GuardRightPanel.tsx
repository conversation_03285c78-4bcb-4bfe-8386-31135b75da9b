import React, { useState, useEffect } from 'react';
import { Icon, Card, Text, Row, Col, message as tips, Bubble, Button, List, Modal, ExternalLink } from '@tencent/tea-component';
import moment from 'moment';
import './GuardRightPanel.less';
import GuardStatu from './GuardStatu';
import GuardStatuPerson from './GuardStatuPerson';
import { PushGuardChat, UpdateGuardToTAMApproval, GetAccountInfoByFields } from '@src/api/advisor/guard';
import { useAegisLog, useHistory } from '@tea/app';
import { IconTsa } from '@src/components/IconTsa';
import { DescribeBroadcastLists } from '@src/api/advisor/broadcast';
import { baseInfoModifyAllowed, getBaseInfoEditor, isInstanceModifyAllowed, getInstanceEditor } from '@src/utils/business';

export default function GuardRightPanel({ currentGuard }) {
	const history = useHistory();
	// 是否是自助护航
	const isSelfService = currentGuard.Standard === 3;
	// 自助护航常见问题
	const commonProblem = [
		{ type: 'list', label: '自助护航服务目录有哪些？', value: ['支持：隐患巡检、播报、监控面板等。如账号未接云顾问或某产品未接云护航，则部分支持。', '不支持：售后、专项人员等群服务。如已选“护航服务群”则支持售后人员服务。'] },
		{ type: 'link', label: '怎么查看隐患报告？', value: 'https://iwiki.woa.com/p/**********#1-%E6%9F%A5%E7%9C%8B%E9%9A%90%E6%82%A3%E6%8A%A5%E5%91%8A' },
		{ type: 'link', label: '怎么配置播报？', value: 'https://iwiki.woa.com/p/**********#2-%E5%8A%A0%E5%85%A5%E6%92%AD%E6%8A%A5%E7%BE%A4%E5%8F%8A%E9%85%8D%E7%BD%AE%E6%92%AD%E6%8A%A5%E7%AD%96%E7%95%A5' },
		{ type: 'link', label: '怎么查看监控面板？', value: 'https://iwiki.woa.com/p/**********#3-%E6%9F%A5%E7%9C%8B%E7%9B%91%E6%8E%A7%E9%9D%A2%E6%9D%BF' },
		{ type: 'link', label: '当前支持哪些巡检项和播报项？', value: 'https://iwiki.woa.com/p/**********#6-%E5%BD%93%E5%89%8D%E6%94%AF%E6%8C%81%E7%9A%84%E6%8A%A4%E8%88%AA%E5%B7%A1%E6%A3%80%E5%8F%8A%E6%8A%A4%E8%88%AA%E6%92%AD%E6%8A%A5' },
	];
	// 播报订阅信息
	const [broadcastInfo, setBroadcastInfo] = useState<any>({});
	// 当前账号信息
	const [userInfo, setUserInfo] = useState<any>({});
	// 仅护航负责人审批之后才有权限
	const hasPermission = (currentGuard.Status > 32)
    || (currentGuard.Status === 32
    && currentGuard.Approvals.AfterSalesStatus.IsConfirm);
	const aegis = useAegisLog();
	const ctxUser = localStorage.getItem('engName');
	// 护航负责人
	const resultList = [];
	// 总负责人
	// const tamPerson = currentGuard.Approvals?.AfterSalesStatus?.Supporter
	// 适配迁云割接
	const tamPerson = currentGuard.Responser;
	resultList.push({ name: '总负责人', value: tamPerson });
	// 组装各个产品的负责人
	(currentGuard.Approvals?.ExpertStatus || []).map((i) => {
		// 产品对应的负责人
		const product = (currentGuard.Approvals?.ScanResultStatus || []).filter(j => j.Product === i.Product);
		let person = '';
		if (product.length) {
			person = product[0].Handler;
		}
		resultList.push({ name: i.ProductName, value: person, dutyName: i.CurrentAndonDutyName || '', dutySupporter: i.CurrentAndonDutySupporter });
	});
	const resultListDom = [];
	resultList.map((i, index) => {
		resultListDom.push(<>
			<div className='personItem'>
				<Text theme="label" className='personLabel' overflow tooltip>{i.name}</Text>
				{i.value
					? <Text>{i.value.split(';').map((item, i) => <p key={i}>{item}</p>)}</Text>
					: <>
						<Text className='noPerson'>待分配</Text>
						{/* 在总负责人后展示，并且要当前状态是订单已提交且没有售后的情况才显示 */}
						{index === 0 && (!userInfo?.SalesSupportor && currentGuard?.Status === 2) && <Button style={{ marginLeft: 16 }} type="link" onClick={() => {
							claim();
						}}>认领</Button>}
					</>
				}
			</div>
			{i.dutyName && index !== 0 && <div className='personItem dutyItem'>
				<Text theme="label" className='personLabel'>安灯值班队列</Text>
				<div>
					<div>{i.dutyName}</div>
					<div>{i.dutySupporter?.split(',').map((item, i) => <p key={i}>{item}</p>)}</div>
				</div>
			</div>}
		</>);
	});
	const StatuMap = {
		prepare: '护航前准备',
		running: '护航中',
		finish: '护航完成',
	};

	// 查询客户售后
	async function getUserInfo() {
		try {
			const res = await GetAccountInfoByFields({ AppId: currentGuard.MainAppId });
			if (res.Error) {
				tips.error({ content: res.Error.Message });
			} else {
				setUserInfo(res || {});
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}

	// 认领确认弹框
	async function claim() {
		const yes = await Modal.confirm({
			message: '确认认领当前护航单？',
			okText: '确认',
			cancelText: '取消',
			icon: 'infoblue',
		  });

		if (yes) {
			try {
				const params = {
					GuardId: currentGuard.GuardId,
					Operator: ctxUser,
				};
				const res = await UpdateGuardToTAMApproval(params);
				if (res.Error) {
					tips.error({ content: res.Error.Message });
				} else {
					tips.success({ content: '护航单认领成功，即将刷新页面' });
					location.reload();
				}
			} catch (err) {
				const msg = err.msg || err.toString() || '未知错误';
				tips.error({ content: msg });
			}
		}
	}

	// 判断当前护航状态
	function getStatu() {
		// 护航开始、结束时间
		const { StartTime, EndTime } = currentGuard;
		const today = moment();
		const start = moment(StartTime, 'YYYY-MM-DD HH:mm:ss');
		const end = moment(EndTime, 'YYYY-MM-DD HH:mm:ss');
		if (today.isBefore(start)) {
			return 'prepare';
		} if (today.isBetween(start, end)) {
			return 'running';
		}
		return 'finish';
	}
	// 加入护航群/播报群
	const joinChat = async (Type?: string) => {
		try {
			const params: any = {
				GuardId: currentGuard.GuardId,
				AppId: currentGuard.MainAppId,
				User: [ctxUser],
			};
			if (Type) {
				params.Type = Type;
			}
			const res = await PushGuardChat(params);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			tips.success({ content: res.Message });
			if (Type) {
				aegis.reportEvent({
					name: 'Click',
					ext1: 'broadcast-to-group-btn',
					ext2: ctxUser,
					ext3: '护航管理/护航详情',
				});
			} else {
				aegis.reportEvent({
					name: 'Click',
					ext1: 'to-group-btn',
					ext2: ctxUser,
					ext3: '护航管理/护航详情',
				});
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};
	// 查询播报订阅ID
	async function getBroadcastId() {
		try {
			const filters = [
				{ Name: 'broadcast_id', Values: [] },
				{ Name: 'guard_id', Values: [`${currentGuard.GuardId}`] },
				{ Name: 'guard_name', Values: [] },
				{ Name: 'appid', Values: [] },
				{ Name: 'customer_name', Values: [] },
				{ Name: 'online', Values: [] },
			];

			const params = {
				AppId: 1253985742,
				Filters: filters,
				Offset: 0,
				Limit: 10,
			};
			const res = await DescribeBroadcastLists(params);
			if (res.Error) {
				tips.error({ content: res.Error.Message });
			} else {
				setBroadcastInfo(res.BroadcastLists.length ? res.BroadcastLists?.[0] : {});
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	}
	// 进入播报订阅编辑
	function toBroadcastEditor() {
		if (broadcastInfo?.BroadcastId) {
			window.open(broadcastInfo.BroadcastUrl ? broadcastInfo.BroadcastUrl : `/advisor/broadcast-editor/${broadcastInfo?.BroadcastId}/1`);
		} else {
			tips.error({ content: '当前护航产品暂不支持播报' });
		}
	}

	// 跳转进入审批任务
	function jumpApprovalTask() {
		// 状态值小于48表示还有审批任务
		const taskStatu = currentGuard.Status < 48 ? '0' : '1';
		window.open(`/advisor/approval?searchGuardId=${currentGuard.GuardId}&searchTaskStatu=${taskStatu}`);
	}

	useEffect(() => {
		getBroadcastId();
		getUserInfo();
	}, []);

	return (
		<div className='GuardRightPanelWrap'>
			<Card>
				<Card.Body title="当前状态">
					<div className='statu'>{StatuMap[getStatu()]}</div>
					<div className='statuInfo'>
						{!isSelfService && <div className='infoItem'>
							<Text theme="label" className='infoLabel'>护航准备情况</Text>
							<Text>{currentGuard.GuardId && <GuardStatu item={currentGuard}></GuardStatu>}</Text>
						</div>}
						<div className='infoItem'>
							<Text theme="label" className='infoLabel'>当前处理人</Text>
							<div className='infoValue'>
								{isSelfService
									? currentGuard.SubmittedBy || '无'
									: (currentGuard.GuardId
                    && <GuardStatuPerson item={currentGuard}></GuardStatuPerson>
									)
								}
							</div>
						</div>
						<div className='infoItem'>
							<Text theme="label" className='infoLabel'>审批任务</Text>
							<ExternalLink onClick={jumpApprovalTask}>查看审批任务</ExternalLink>
						</div>
						<div className='infoItem'>
							<span className='infoLabel'>
								<Text theme="label" className='label'>信息变更</Text>
								<Bubble
									arrowPointAtCenter
									placement="top"
									content={<Text style={{ whiteSpace: 'pre-line' }}>{` - 什么时候修改？护航期间，非草稿状态\n - 谁能修改？护航负责人、TAM Leader \n${getBaseInfoEditor(currentGuard).join(',')}`}</Text>}
								>
									<Icon type="info" />
								</Bubble>
							</span>
							{
								baseInfoModifyAllowed(currentGuard)
									? <ExternalLink onClick={() => {
										history.push(`/advisor/guard/baseInfo/${currentGuard.GuardId}`);
									}}>护航信息变更 </ExternalLink>
							 		: <Button type="link" disabled>护航信息变更</Button>
							}
						</div>
						<div className='infoItem'>
							<span className='infoLabel'>
								<Text theme="label" className='label'>实例变更</Text>
								<Bubble
									arrowPointAtCenter
									placement="top"
									content={<Text style={{ whiteSpace: 'pre-line' }}>{` - 什么时候修改？护航期间，在草稿或计算外的状态\n - 谁能修改？建单人、负责人、审批人\n${getInstanceEditor(currentGuard).join(',')}`}</Text>}
								>
									<Icon type="info" />
								</Bubble>
							</span>
							{
								isInstanceModifyAllowed(currentGuard)
									? <ExternalLink onClick={() => {
										history.push(`/advisor/guard/instances/${currentGuard.GuardId}`);
									}}>护航实例变更</ExternalLink>
							 		: <Button type="link" disabled>护航实例变更</Button>
							}
						</div>
					</div>
				</Card.Body>
			</Card>
			<Card>
				<Card.Body title="快捷入口">
					<Row>
						<Col span={8}>
							<Bubble
								arrowPointAtCenter
								placement="top"
								content={hasPermission ? '' : '护航负责人审批后创建护航群'}
							>
								<div className='entranceWrap' onClick={() => {
									hasPermission && joinChat();
								}}>
									<IconTsa type={hasPermission ? 'icon-joinChat' : 'icon-joinChat-no'} className='imgWrap' />
									<Text theme='label'>加入护航群</Text>
								</div>
							</Bubble>
						</Col>
						<Col span={8}>
							<Bubble
								arrowPointAtCenter
								placement="top"
								content={hasPermission ? '' : '护航负责人审批后创建播报群'}
							>
								<div className='entranceWrap' onClick={() => {
									hasPermission && joinChat('broadcast');
								}}>
									<IconTsa type={hasPermission ? 'icon-joinChat' : 'icon-joinChat-no'} className='imgWrap' />
									<Text theme='label'>加入播报群</Text>
								</div>
							</Bubble>
						</Col>
						<Col span={8}>
							<Bubble
								arrowPointAtCenter
								placement="top"
								content={hasPermission ? '' : '护航负责人审批后创建播报订阅'}
							>
								<div className='entranceWrap' onClick={() => {
									hasPermission && toBroadcastEditor();
								}}>
									<IconTsa type={hasPermission ? 'icon-broadcast' : 'icon-broadcast-no'} className='imgWrap' />
									<Text theme='label'>进入播报订阅</Text>
								</div>
							</Bubble>
						</Col>
						{isSelfService && <Col span={8}>
							<Bubble
								arrowPointAtCenter
								placement="top-end"
								content='引导使用巡检报告、监控、播报、日报服务，点击查看'
							>
								<div className='entranceWrap' onClick={() => {
									window.open('https://iwiki.woa.com/p/**********');
								}}>
									<IconTsa type='icon-selfService' className='imgWrap' />
									<Text theme='label'>自助护航<br />服务指引</Text>
								</div>
							</Bubble>
						</Col>}
					</Row>
				</Card.Body>
			</Card>
			<Card>
				{isSelfService
					? <Card.Body title="常见问题">
						<List type="bullet">
							{commonProblem.map((i: any) => {
								const dom = i.type === 'list'
									? <div className='questionItem'>
										<div>{i.label}</div>
										<div className='listWrap'>
											{i.value.map(j => <div>- {j}</div>)}
										</div>
									</div>
									: <div className='questionItem'>
										<Button type="link" onClick={() => window.open(i.value)}>{i.label}</Button>
									</div>;
								return <List.Item>{dom}</List.Item>;
							})}
						</List>
					</Card.Body>
					: <Card.Body title="护航负责人">
						{resultListDom}
					</Card.Body>
				}
			</Card>
		</div>
	);
}
