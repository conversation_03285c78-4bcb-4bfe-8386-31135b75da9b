import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Table, Justify, Button, SearchBox, Card, Layout, message as tips, Select, SelectMultiple, StatusTip, Row, Col } from '@tencent/tea-component';
import { Link } from 'react-router-dom';
import { Menu } from "@tencent/tea-component";
import { Text, Input, Icon } from "@tencent/tea-component";
const { Body, Sider, Content } = Layout;
import { useHistory } from '@tea/app';

import { DescribeGuardProjects, DescribeGuardSheet } from '@src/api/advisor/guard';
import { DescribeGuardEmergencyPlan } from '@src/api/advisor/guard';
import { GuardItem, Filter, GuardParams, EmergencyPlanParams, StandardDict } from '@src/types/advisor/guard';

const { sortable } = Table.addons;


function GuardDescEmergency(match) {
    //应急预案数据
    const [emergencyPlans, setEmergencyPlans] = useState<Array<EmergencyPlanParams>>([])

    // 查询应急预案
    const getEmergencyPlans = async () => {
        try {
            const res = await DescribeGuardEmergencyPlan({
                AppId: 1253985742, // 接口必须传appid
                SearchWord: "",
            })
            if (res.Error) {
                let msg = res.Error.Message
                tips.error({ content: msg });
                return
            } else {
                let item = res.EmergencyPlan || {}
                setEmergencyPlans(item)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    useEffect(() => {
        getEmergencyPlans()
    }, [])

    // 当前排序列
    const [sorts, setSorts] = useState([]);
    return (
        <>
            <Table
                // 如果要在前端排序，可以用 sortable.comparer 生成默认的排序方法
                records={[...emergencyPlans].sort(sortable.comparer(sorts))}
                recordKey="Id"
                columns={[
                    { key: "Id", header: "预案ID" },
                    { key: "Product", header: "产品" },
                    { key: "RiskScenarioCategory", header: "标题" },
                    { key: "RiskScenario", header: "场景" },
                    { key: "Measure", header: "预案措施" },
                ]}
                addons={[
                    sortable({
                        columns: [
                            "Id",
                            "Product",
                            {
                                key: "RiskScenarioCategory",
                                prefer: "desc",
                            },
                            "RiskScenario",
                            {
                                key: "Measure",
                                prefer: "desc",
                            },
                        ],
                        value: sorts,
                        onChange: value => { setSorts(value); },
                    }),
                ]}
            />
        </>
    );
}

export { GuardDescEmergency };
