/* eslint-disable no-nested-ternary */
import React from 'react';
import { Bubble, Timeline, Text, Icon, Button, message } from '@tencent/tea-component';
import { StatusDict } from '@src/types/advisor/guard';
import { restartGuardScanTask } from '@src/api/advisor/guard';
// 流程状态ID
const processState = {
	onNoteId: 1,              // 草稿状态ID
	submitId: 2,              // 订单已提交状态ID
	onSaleApprovalId: 31,     // 售后审批状态ID
	onRunningId: 32,          // 正在巡检中状态ID
	onExpertApprovalId: 33,   // 专项分配人员状态ID
	onResultApprovalId: 34,   // 巡检结果审核状态ID
	onReportGenerating: 36,   // 巡检报告生成中
	onReportGenerated: 37,    // 巡检报告已完成
	instanceAltering: 40,     // 实例修改中ID
	instanceAlteredRun: 41,   // 实例修改后运行中ID
	tamCheckResultId: 48,     // 巡检风险审批（TAM）
	onFinishId: 50,           // 护航巡检完成状态ID
	processStopId: -1,        // 流程中止状态ID
	scanFailedId: -2,         // 巡检异常状态ID
	approvalFailedId: -3,     // 审核异常状态ID
	deletedId: -50,           // 已删除
};

const notInProcessList = [processState.instanceAltering, processState.instanceAlteredRun];

export default function GuardStatu({ item, showReInspection = false, reInspectionCallback = () => {} }) {
	// 迁云要隐藏售后审批和护航负责人审批
	const isQianYun = item.Standard === 1;
	// 自助护航要隐藏审批环节
	const isSelfService = item.Standard === 3;

	const isConfirm = item.Approvals.AfterSalesStatus.IsConfirm;
	const afterSalesCheck = [
		item.Approvals.AfterSalesStatus.Handler,
		item.Approvals.AfterSalesStatus.IsApproved ? ' - 已审批' : ' - 审批中', <div></div>,
	];
	const supporterCheck = [
		item.Approvals.AfterSalesStatus.Supporter,
		item.Approvals.AfterSalesStatus.IsConfirm ? ' - 已审批' : ' - (MyOA)审批中', <div></div>,
	];

	// 总监审批信息
	const createDirectorInfo = (item?.Approvals?.DirectorStatus || []).find(i => i.DirectorType === 'CreateDirector') || {};

	// 发起人总监审批信息
	const tamDirectorInfo = (item?.Approvals?.DirectorStatus || []).find(i => i.DirectorType === 'TamDirector') || {};
	// 总监审批是否通过
	const isDirectorApproval = item.IsDirectorApproval === 2;

	const expertDispatchCheck = (item.Approvals.ExpertStatus || []).map((ap) => {
		const apHandler = (ap.Handler ? `(${ap.Handler})` : '');
		return [<strong>{ap.ProductName}</strong>, apHandler, ap.IsFinished ? (ap.State == -1 ? ' - 已驳回' : ' - 已审批') : ' - 审批中', <div></div>];
	});
	const expertWorkCheck = (item.Approvals.ScanResultStatus || []).map((ap) => {
		const apHandler = (ap.Handler ? `(${ap.Handler})` : '');
		return [<strong>{ap.ProductName}</strong>, apHandler, ap.IsFinished ? (ap.State == -1 ? ' - 已驳回' : ' - 已审批') : ' - 审批中', <div></div>];
	});
	const tamResultCheck = [
		getResultEditor(item).join(','),
		(item.Status == processState.onFinishId && item.RiskData.HighRiskStrategyCount == 0 && item.RiskData.MediumRiskStrategyCount == 0) ? ' - 已审批' : ' - 审批中（在 [操作]-[更多]-[处理巡检结果] 实施）',
		<div></div>,
	];
	// eslint-disable-next-line max-len
	const { Handler, IsConfirm: afterSaleIsConfirm, IsNeedConfirm, IsSpecial, SpecialHandler }	= 	item.Approvals?.AfterSaleConfirmStatus || {};
	const isAfterSaleConfirmMap = IsNeedConfirm && afterSaleIsConfirm && IsSpecial;

	// 查询是有修改护航单巡检结果的人员
	function getResultEditor(currentGuard) {
		let guys = [];
		guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Supporter.split(';'));
		guys = guys.concat(currentGuard.Responser.split(';'));

		return Array.from(new Set(guys)).filter(i => i != '');
	}

	// 当前登录人员
	const rtx = localStorage.getItem('engName') || '';
	// 是否有重新发起的权限
	const hasReInspectionAuth = rtx ? item.Responser.split(';').concat([item.SubmittedBy])
		.includes(rtx) : false;

	// 重试失败任务
	async function reInspection() {
		if (!hasReInspectionAuth) {
			return;
		}
		try {
			const params  = {
				GuardId: item?.GuardId,
				Operator: rtx,
			};
			const res = await restartGuardScanTask(params);
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			message.success({ content: '重新发起失败任务成功' });
			reInspectionCallback?.();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	}

	// 专项巡检结果
	function expertApprovalIcon(data) {
		if (data.Status < processState.onResultApprovalId) {
			return <Icon type="cur" />;
		}
		if (data.Status >= processState.onResultApprovalId &&  data.Status <= processState.tamCheckResultId) {
			// 是否未审批完
			const noFinish =  data?.Approvals?.ScanResultStatus?.some(i => !i?.IsFinished);
			return noFinish ? <Icon type="loading" /> : <Icon type="success" />;
		}
		return <Icon type="success" />;
	}

	return (
		<Bubble
			placement="right"
			trigger="hover"
			placementOffset="10 + 20%"
			style={{ maxWidth: 600 }}
			content={<>
				{(item.Status > 0 && !notInProcessList.includes(item.Status))
          && <Timeline mode="vertical" dashedLine style={{ height: 'auto' }}>
          	<Timeline.Item
          		label="草稿"
          		style={{ height: 35 }}
          		theme={item.Status > processState.onNoteId ? 'success' : 'default'}
          		icon={item.Status == processState.onNoteId
          			? <Icon type="loading" />
          			: item.Status > processState.onNoteId ? <Icon type="success" /> : <Icon type="cur" />}>
          	</Timeline.Item>
          	<Timeline.Item
          		label="订单已提交"
          		style={{ height: 35 }}
          		theme={item.Status > processState.submitId ? 'success' : 'default'}
          		icon={item.Status == processState.submitId
          			? <Icon type="loading" />
          			: item.Status > processState.submitId ? <Icon type="success" /> : <Icon type="cur" />}>
          	</Timeline.Item>
          	{!isQianYun && !isSelfService && <Timeline.Item
          		label={
          			<>
          				<Text>{'售后审批（TAM）'}</Text>
          				<Bubble content={<Text>{afterSalesCheck}</Text>} placement={'right-end'}>
          					<Icon style={{ marginLeft: 4 }} type="info" />
          				</Bubble>
          			</>
          		}
          		style={{ height: 35 }}
          		theme={item.Status > processState.onSaleApprovalId ? 'success' : 'default'}
          		icon={item.Status == processState.onSaleApprovalId
          			? <Icon type="loading" />
          			: item.Status > processState.onSaleApprovalId ? <Icon type="success" /> : <Icon type="cur" />}>
          	</Timeline.Item>}
			  {!isQianYun && !isSelfService && IsNeedConfirm && <Timeline.Item
          			label={
          				<>
          					<Text>{'售后确认架构图'}</Text>
          					<Bubble content={
          					<div>
          						<div className='leaderItem'><span>售后确认架构图审批</span>{Handler} {afterSaleIsConfirm ? ' - 已审批' : ' - 审批中'}</div>
          						{SpecialHandler && <div className='leaderItem'><span>护航无架构图特殊审批</span>{SpecialHandler}{IsSpecial ? ' - 已审批' : ' - 审批中'}</div>}
          					</div>
          				} placement={'right-end'}>
          						<Icon style={{ marginLeft: 4 }} type="info" />
          					</Bubble>
          				</>
          			}
          			style={{ height: 35 }}
          			theme={isAfterSaleConfirmMap ? 'success' : 'default'}
          			icon={isAfterSaleConfirmMap
          				? <Icon type="success" />
          				: item.Status > processState.onSaleApprovalId
          					? <Icon type="loading" />
          					: <Icon type="cur" />}>
          		</Timeline.Item>}
				  {!isQianYun && !isSelfService && <Timeline.Item
          		label={
          			<>
          				<Text>{'护航负责人确认'}</Text>
          				<Bubble content={<Text>{supporterCheck}</Text>} placement={'right-end'}>
          					<Icon style={{ marginLeft: 4 }} type="info" />
          				</Bubble>
          			</>
          		}
          		style={{ height: 35 }}
          		theme={isConfirm ? 'success' : 'default'}
          		icon={isConfirm
          			? <Icon type="success" />
          			: item.Status > processState.onSaleApprovalId && (isAfterSaleConfirmMap || !IsNeedConfirm)
          				? <Icon type="loading" />
          				: <Icon type="cur" />}>
          		  </Timeline.Item>}
			  			{
          			(item.IsDirectorApproval !== undefined && item.IsDirectorApproval !== 0)
								&& <Timeline.Item
									label={
										<>
											<Text>{'总监审批'}</Text>
											<Bubble content={
												<div>
													<div className='leaderItem'><span>发起人总监</span>{createDirectorInfo?.Handler} {createDirectorInfo?.IsApproved ? ' - 已审批' : ' - 审批中'}</div>
													<div className='leaderItem'><span>售后总监</span>{tamDirectorInfo?.Handler}{tamDirectorInfo?.IsApproved ? ' - 已审批' : ' - 审批中'}</div>
												</div>
											}
											placement={'right-end'}>
												<Icon style={{ marginLeft: 4 }} type="info" />
											</Bubble>
										</>
          		}
          		style={{ height: 35 }}
          		theme={isDirectorApproval ? 'success' : 'default'}
          		icon={isDirectorApproval ? <Icon type="success" /> : (item.Status > processState.onSaleApprovalId && isConfirm) ? <Icon type="loading" /> : <Icon type="cur" />}>
          	</Timeline.Item>}
          	<Timeline.Item
          		label="正在巡检中"
          		style={{ height: 35 }}
          		theme={item.Status > processState.onRunningId ? 'success' : 'default'}
          		icon={(item.Status == processState.onRunningId && item.IsDirectorApproval !== 1 && isConfirm)
          			? <Icon type="loading" />
          			: item.Status > processState.onRunningId ? <Icon type="success" /> : <Icon type="cur" />}>
          	</Timeline.Item>
          	{!isSelfService && <Timeline.Item
          		label={
          			<>
          				<Text>{'专项人员分配（专项接口人）'}</Text>
          				<Bubble content={<Text>{expertDispatchCheck}</Text>} placement={'right-end'} >
          					<Icon style={{ marginLeft: 4 }} type="info" />
          				</Bubble>
          			</>
          		}
          		style={{ height: 35 }}
          		theme={item.Status > processState.onExpertApprovalId ? 'success' : 'default'}
          		icon={item.Status == processState.onExpertApprovalId
          			? <Icon type="loading" />
          			: item.Status > processState.onExpertApprovalId ? <Icon type="success" /> : <Icon type="cur" />}>
          	</Timeline.Item>}
          	{!isSelfService && <Timeline.Item
          		label={
          			<>
          				<Text>{'巡检结果审批（专项Owner）'}</Text>
          				<Bubble content={<Text>{expertWorkCheck}</Text>} placement={'right-end'}>
          					<Icon style={{ marginLeft: 4 }} type="info" />
          				</Bubble>
          			</>
          		}
          		style={{ height: 35 }}
          		// theme={item.Status > processState.onResultApprovalId ? 'success' : 'default'}
          		icon={expertApprovalIcon(item)}>
          	</Timeline.Item>}
          	<Timeline.Item
          		label={
          			<>
          				<Text>{'巡检风险审批（护航负责人）'}</Text>
          				<Bubble content={<Text>{tamResultCheck}</Text>} placement={'right-end'}>
          					<Icon style={{ marginLeft: 4 }} type="info" />
          				</Bubble>
          			</>
          		}
          		style={{ height: 35 }}
          		theme={item.Status > processState.tamCheckResultId ? 'success' : 'default'}
          		icon={item.Status == processState.tamCheckResultId
          			? <Icon type="loading" />
          			: item.Status > processState.tamCheckResultId ? <Icon type="success" /> : <Icon type="cur" />}>
          	</Timeline.Item>
          	<Timeline.Item
          		label="护航巡检已完成"
          		style={{ height: 5 }}
          		theme={item.Status == processState.onFinishId ? 'success' : 'default'}
          		icon={item.Status == processState.onFinishId ? <Icon type="success" /> : <Icon type="cur" />}>
          	</Timeline.Item>
          </Timeline >
				}
				{
					[processState.instanceAltering, processState.instanceAlteredRun].includes(item.Status)
          && <Timeline.Item label=" 重新巡检中 ... " style={{ height: 5 }} theme={'default'} icon={<Icon type="loading" />}></Timeline.Item>
				}
				{
					item.Status == processState.processStopId
          && <Timeline.Item label=" 流程中止 :( " style={{ height: 5 }} theme={item.Status === processState.processStopId ? 'danger' : 'default'}></Timeline.Item>
				}
				{
					item.Status == processState.scanFailedId
          && <Timeline.Item label=" 巡检任务异常，请联系管理员 :( " style={{ height: 5 }} theme={item.Status === processState.scanFailedId ? 'danger' : 'default'}></Timeline.Item>
				}
				{
					item.Status == processState.approvalFailedId
          && <Timeline.Item label=" 审核任务异常，请联系管理员 :( " style={{ height: 5 }} theme={item.Status === processState.approvalFailedId ? 'danger' : 'default'}></Timeline.Item>
				}
			</>
			}
		>
			<Button type="link">{
				// eslint-disable-next-line max-len
				(item.Status == processState.onRunningId && !isConfirm && (!IsNeedConfirm || (afterSaleIsConfirm && IsSpecial)))
					? '护航负责人确认'
					: IsNeedConfirm && (!afterSaleIsConfirm || !IsSpecial) && item.Status === processState.onRunningId ? '售后架构图确认'
						: (item.Status == processState.onFinishId
							? <div style={{ color: '#20b455' }}>
								<Icon type="success" style={{ marginRight: 4, verticalAlign: 'sub' }} />
								{StatusDict.get(item.Status)}
							</div>
						// 总监审批额外状态展示
							: (item.IsDirectorApproval === 1 && item.Status === processState.onRunningId && isConfirm)
								? '总监审批'
								: <>
									{StatusDict.get(item.Status)}
									{ item.Status === processState.scanFailedId && showReInspection
									 && <Bubble
									 arrowPointAtCenter
									 placement="top"
									 content={hasReInspectionAuth ? '重试失败任务' : '仅护航负责人/提单人可以重新发起巡检'}
								   >
									 	<Icon style={{ marginLeft: 4, verticalAlign: 'top' }} type={hasReInspectionAuth ? 'refresh-blue' : 'refresh'} onClick={reInspection} />
								   </Bubble>
									}
								</>
						)
			}</Button>
		</Bubble >
	);
}
