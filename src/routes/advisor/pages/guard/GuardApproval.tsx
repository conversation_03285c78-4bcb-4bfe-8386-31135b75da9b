import React, { useState, useEffect } from 'react';
import { Table, Button, Card, Layout, message as tips, SelectMultiple, StatusTip, Row, Col, Text, Input } from '@tencent/tea-component';
import moment from 'moment';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { DescribeGuardProjects, DescribeGuardApprovalProgress } from '@src/api/advisor/guard';
import { Filter, OriginDict, map2options, ApprovalProgressParams, ApvlStatusDict, ApvlTypeDict } from '@src/types/advisor/guard';
import { getUserInfo } from '@src/api/common';
import { reportVisitPage } from '@src/utils/report';
import { useHistory, useAegisLog } from '@tea/app';
import { useCheckIfAuthed } from '../../pages/broadcast/hooks/checkIfAuthed';
import GuideLinks from './components/GuideLinks';
import './style.less';
import { queryStringObj } from '@src/utils/common';
const { Body, Content } = Layout;
const { pageable } = Table.addons;

// 结构查询参数
const { searchGuardId, searchTaskStatu } = queryStringObj(location.search);

// 护航单查询条件合集对象初始化值 也用户重置输入
export const descParams = {
	apvlId: '',           // 任务ID
	apvlType: [],         // 任务类别
	apvlStatus: [searchTaskStatu || '0'],    // 任务状态。默认未处理
	handler: '',          // 处理人
	apvlName: '',         // 任务名称
	guardId: searchGuardId || '',          // 护航ID
	guardName: '',        // 护航名称
	project: [],          // 护航项目
	appid: '',            // 客户APPID
	startTime: moment().subtract(3, 'months'), // 护航开始时间
	endTime: moment().subtract(-3, 'months'),  // 护航结束时间
	origin: ['0'],        // 护航来源。默认运营端
	standard: ['0'],      // 默认标准版
	status: [],           // 默认全选
	created_by: '',
	responser: '',
	product: [],          // 护航云产品
};

// 持续查询任务
let timer;
const interval = 10000;

export function GuardApproval() {
	// 护航项目选项
	const [projectOptions, setProjectOptions] = useState([]);
	const [projectDict, setProjectDict] = useState(new Map());
	// 护航云产品选项
	const [productsOptions, setProductsOptions] = useState([]);
	// 护航单查询条件参数清单
	const [apvlId, setApvlId] = useState<string>(descParams.apvlId);                    // 任务ID
	const [apvlType, setApvlType] = useState<Array<string>>(descParams.apvlType);       // 任务类型
	const [apvlStatus, setApvlStatus] = useState<Array<string>>(descParams.apvlStatus); // 任务状态
	const [apvlName, setApvlName] = useState<string>(descParams.apvlName);              // 任务名称
	const [handler, setHandler] = useState<string>(descParams.handler);                 // 处理人
	const [guardId, setGuardId] = useState<string>(descParams.guardId);                 // 护航ID
	const [guardName, setGuardName] = useState<string>(descParams.guardName);           // 护航名称
	const [projectList, setProjectList] = useState<Array<string>>(descParams.project);  // 护航项目。多选
	const [productList, setProductList] = useState<Array<string>>(descParams.product); // 护航云产品 多选
	const [appid, setAppid] = useState<string>(descParams.appid);                       // 客户APPID

	const { isAuth } = useCheckIfAuthed({
		pageStatus: '',
		key: '',
		value: '',
	});

	// 未填写字段
	const noSet = <Text style={{ color: 'LightGray' }} verticalAlign="middle">未填写</Text>;
	// 护航单查询接口常规参数
	const [offset, setOffset] = useState<number>(0);
	const [limit, setLimit] = useState<number>(10);
	const [loading, setLoading] = useState<boolean>(false);
	const history = useHistory();
	const aegis = useAegisLog();

	const ctxUser = localStorage.getItem('engName');

	// 护航单记录
	const [approvals, setApprovals] = useState<Array<ApprovalProgressParams>>([]);

	const [total, setTotal] = useState<number>(0);

	// 当前登录rtx
	const [rtx, setRtx] = useState<string>('');
	// 获取当前登录账号rtx
	const getCurrentOperator = async () => {
		try {
			const res = await getUserInfo();
			setRtx(res.data.EngName || '');
			// 如果是链接跳转过来，默认不填充当前登陆用户
			if (!searchGuardId) {
				setHandler(res.data.EngName || '');
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 重置查询条件
	function resetParams() {
		setApvlId(descParams.apvlId);
		setApvlType(descParams.apvlType);
		setApvlStatus(descParams.apvlStatus);
		setApvlName(descParams.apvlName);
		setHandler(descParams.handler);
		setGuardId(descParams.guardId);
		setGuardName(descParams.guardName);
		setProjectList(descParams.project);
		setAppid(descParams.appid);
		setProductList(descParams.product);
	}

	// 重置状态和监听重置状态进行查询
	const [reStart, setRestart] = useState(true);
	// 监听是否被重置，并在重置后删除事件监听器
	useEffect(() => {
		getGuardApprovalProgress(offset, limit, false);
	}, [reStart]);

	const handleDealApproval = (item) => {
		aegis.reportEvent({
			name: 'Click',
			ext1: 'approval-deal-btn',
			ext2: ctxUser,
			ext3: '护航审批',
		});
		switch (item.TaskType) {
		case 1:
			history.push(`/advisor/approval/detail/${item.GuardId}/${item.Id}`);
			break;
		case 2:
			history.push(`/advisor/approval/sales/${item.GuardId}`);
			break;
		case 3:
			history.push(`/advisor/approval/expert/${item.GuardId}/${item.Id}`);
			break;
			// 售后确认架构图
		case 7:
			history.push(`/advisor/approval/sales-confirm/${item.GuardId}/${item.Id}`);
			break;
			// 售后特殊审批
		case 8:
			history.push(`/advisor/approval/special-approval/${item.GuardId}/${item.Id}`);
			break;
		default:
			history.push('/advisor/approval');
		}
	};

	// 审批单表头，对应 approvals 变量、ApprovalProgressParams 类型、DescribeGuardApprovalProgress 返回值
	const approvalColumns = [
		{
			key: 'Id',
			header: '任务ID',
			width: '5%',
			render: item => <Button type="link" disabled={!item?.Handler?.includes(ctxUser) && !isAuth} onClick={() => handleDealApproval(item)}>{item.Id}</Button>,
		},
		{
			key: 'TaskName',
			header: '任务名称',
			render: (item) => {
				if (item.TaskName !== '') {
					return <Text>{item.TaskName}</Text>;
				}
				return <Text>{noSet}</Text>;
			},
		},
		{
			key: 'TaskType',
			header: '任务类别',
			width: '15%',
			render: item => <Text>{ApvlTypeDict.get(item.TaskType)}</Text>,
		},
		{
			key: 'Status',
			header: '任务状态',
			render: item => <Text>{ApvlStatusDict.get(item.Status)}</Text>,
		},
		{
			key: 'Handler',
			header: '处理人',
			render: item => <Text>{item.Handler}</Text>,
		},
		{
			key: 'GuardId',
			header: '护航ID',
			width: '5%',
			render: item => <a onClick={() => {
				history.push(`/advisor/guard/summary/${item.GuardId}`);
			}}>{item.GuardId}</a>,
		},
		{
			key: 'GuardName',
			header: '护航名称',
			render: item => <Text>{item.GuardName}</Text>,
		},
		{
			key: 'Project',
			header: '护航项目',
			render: item => <Text>{projectDict.get(item.Project)}</Text>,
		},
		{
			key: 'Origin',
			header: '护航来源',
			render: item => <Text>{OriginDict.get(item.Origin)}</Text>,
		},
		{
			key: 'MainAppId',
			header: '客户APPID',
			render: item => <Text>{item.MainAppId}</Text>,
		},
		{
			key: 'StartTime',
			header: '护航开始时间',
			render: item => <Text>{item.StartTime}</Text>,
		},
		{
			key: 'EndTime',
			header: '护航结束时间',
			render: item => <Text>{item.EndTime}</Text>,
		},
		{
			key: 'Ops',
			header: '操作',
			render: (item) => {
				if (item.Handler.split(';').map(i => i.split('(')[0])
					.map(i => i.trim())
					.includes(rtx)) {
					// 审批人
					if (item.Status === 1) {
						return <Text style={{ color: 'LightGray' }}>{'已处理'}</Text>;
					}
					return < Button type="link" onClick={() => handleDealApproval(item)}>处理</Button >;
				}
				// 非审批人
				return <Text style={{ color: 'LightGray' }}>无</Text>;
			},
		},
	];

	// 查询审核单
	const getGuardApprovalProgress = async (Offset, Limit, Continue = false) => {
		setLoading(true);
		try {
			const filters: Array<Filter> = [
				{ Name: 'id', Values: apvlId ? [apvlId.trim()] : [] },
				{ Name: 'approval_type', Values: apvlType },
				{ Name: 'approval_name', Values: apvlName ? [apvlName.trim()] : [] },
				{ Name: 'status', Values: apvlStatus },
				{ Name: 'handler', Values: handler ? [handler.trim()] : [] },
				{ Name: 'guard_id', Values: guardId ? [guardId.trim()] : [] },
				{ Name: 'guard_name', Values: guardName ? [guardName.trim()] : [] },
				{ Name: 'project', Values: projectList },
				{ Name: 'product', Values: productList },
				{ Name: 'appid', Values: appid ? [appid.trim()] : [] },
			];
			const res = await DescribeGuardApprovalProgress({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset,
				Limit,
				AppId: 1253985742,
			});
			setLoading(false);
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			setApprovals(res.Progress);
			// setGuards(res.Guard)
			setTotal(res.TotalCount);
			// 开启持续查询
			if (!timer && Continue) {
				timer = setInterval(() => {
					getGuardApprovalProgress(Offset, Limit, Continue);
				}, interval);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 查询护航项目清单
	const getProjects = async () => {
		try {
			const res = await DescribeGuardProjects({
				AppId: 1253985742, // 接口必须传appid  为获取全量产品列表，因此传内部中心账号
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmp = [];
			const m = new Map();
			res.Projects.map((i) => {
				tmp.push({ value: i.Id.toString(), text: i.Name });
				m.set(i.Id, i.Name);
			});
			setProjectOptions(tmp);
			setProjectDict(m);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 获取护航云产品清单
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: 1253985742, // 接口必须传appid  为获取全量产品列表，因此传内部中心账号
				Env: 'all',
				TaskType: 'guardTaskType',
			});
			if (res.Error) {
				const msg = res.Error.Message;
				tips.error({ content: msg });
				return;
			}
			const tmpProductsOptions = [];
			for (const i in res.ProductDict) {
				tmpProductsOptions.push({ value: i, text: res.ProductDict[i] });
			}
			setProductsOptions(tmpProductsOptions);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};

	// 页面加载初始化
	useEffect(() => {
		getCurrentOperator();
		getProjects();
		getProductsGroupsInfo();
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: '护航审批',
			ext3: ctxUser,
		});
		reportVisitPage({
			isaReportMeunName: '护航审批',
		});
		return () => {
			if (timer) {
				clearInterval(timer);
			}
		};
	}, []);

	// 页面加载初始化。按默认处理人（如果手动填写为登陆人rtx会自动触发查询）
	useEffect(() => {
		if (handler && handler === rtx) {
			getGuardApprovalProgress(offset, limit);
		}
	}, [handler]);

	return (
		<Body>
			<Content>
				<Content.Header title="护航任务审批" operation={<GuideLinks />}></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body>
							<div>
								<Row gap={20}>
									<Col>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">任务ID</Text>
												</Col>
												<Col span={18}>
													<Input value={apvlId} onChange={(v) => {
														setApvlId(v);
													}} size="full" />
												</Col>
											</Row>
										</section>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">任务名称</Text>
												</Col>
												<Col span={18}>
													<Input value={apvlName} onChange={(v) => {
														setApvlName(v);
													}} size="full" />
												</Col>
											</Row>
										</section>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6}>
													<Text theme="label" verticalAlign="middle">客户APPID</Text>
												</Col>
												<Col span={18}>
													<Input value={appid} onChange={(v) => {
														setAppid(v);
													}} size="full" />
												</Col>
											</Row>
										</section>
									</Col>
									<Col>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">任务类别</Text>
												</Col>
												<Col span={18}>
													<SelectMultiple
														appearance="button"
														value={apvlType}
														// allOption={{ value: 'ALL', text: 'ALL' }}
														options={map2options(ApvlTypeDict)}
														onChange={(v) => {
															setApvlType(v);
														}}
														size="full"
													/>
												</Col>
											</Row>
										</section>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">护航ID</Text>
												</Col>
												<Col span={18}>
													<Input value={guardId} onChange={(v) => {
														setGuardId(v);
													}} size="full" />
												</Col>
											</Row>
										</section>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">护航云产品</Text>
												</Col>
												<Col span={18}>
													<SelectMultiple
														listHeight={400}
														boxClassName='productListBox'
														appearance="button"
														value={productList}
														options={productsOptions}
														searchable
														allOption={{ value: 'ALL', text: 'ALL' }}
														onChange={(v) => {
															setProductList(v);
														}}
														size="full"
													/>
												</Col>
											</Row>
										</section>
									</Col>
									<Col>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6}>
													<Text theme="label" verticalAlign="middle">任务状态</Text>
												</Col>
												<Col span={18}>
													<SelectMultiple
														appearance="button"
														value={apvlStatus}
														options={map2options(ApvlStatusDict)}
														allOption={{ value: 'ALL', text: 'ALL' }}
														size="full"
														onChange={(v) => {
															setApvlStatus(v);
														}}
													/>
												</Col>
											</Row>
										</section>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">护航名称</Text>
												</Col>
												<Col span={18}>
													<Input value={guardName} onChange={(v) => {
														setGuardName(v);
													}} size="full" />
												</Col>
											</Row>
										</section>
									</Col>
									<Col>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">处理人</Text>
												</Col>
												<Col span={18}>
													<Input value={handler} onChange={(v) => {
														setHandler(v);
													}} size="full" />
												</Col>
											</Row>
										</section>
										<section>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">护航项目</Text>
												</Col>
												<Col span={18}>
													<SelectMultiple
														appearance="button"
														value={projectList}
														options={projectOptions}
														searchable
														allOption={{ value: 'ALL', text: 'ALL' }}
														onChange={(v) => {
															setProjectList(v);
														}}
														size="full"
													/>
												</Col>
											</Row>
										</section>
									</Col>
								</Row>
							</div>
							<div style={{ margin: 10, textAlign: 'center' }}>
								<Button type="primary" disabled={loading} onClick={() => {
									setOffset(0);
									// 每次查询，都清理上一次的持续任务
									if (timer) {
										clearInterval(timer);
										timer = null;
									}
									getGuardApprovalProgress(0, 10);
									aegis.reportEvent({
										name: 'Click',
										ext1: 'approval-view-btn',
										ext2: ctxUser,
										ext3: '护航审批',
									});
								}}>
									查询
								</Button>
								&nbsp;&nbsp;
								<Button onClick={() => {
									resetParams();
									setRestart(a => !a);
								}}>
									重置
								</Button>
							</div>
						</Card.Body>
					</Card>
					<Card>
						<Card.Body>
							<div>
								<Table
									columns={approvalColumns}
									records={approvals}
									topTip={
										(loading || approvals.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
									}
									addons={[
										pageable({
											recordCount: total,
											pageSize: limit,
											pageIndex: offset / limit + 1,
											onPagingChange: (query) => {
												if (loading) {
													return;
												}
												setOffset((query.pageIndex - 1) * query.pageSize);
												setLimit(query.pageSize);
												if (timer) {
													clearInterval(timer);
													timer = null;
												}
												getGuardApprovalProgress(
													(query.pageIndex - 1) * query.pageSize,
													query.pageSize,
												);
											},
										}),
									]}
									recordKey="Id"
								/>
							</div>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}
