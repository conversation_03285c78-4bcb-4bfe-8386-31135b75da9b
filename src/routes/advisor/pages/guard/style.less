.scanResultTamConfirm{
	.tea-dialog__inner{
		width: 340px;
		min-width: 340px;
	}
}

.resourceRefreshWrap{
	display: flex;
	align-items: center;
}

.selectAlert{
	color: #000000E5;
	.authLink{
		margin: 0 10px;
		cursor: pointer;
	}
	.specialLink{
		cursor: pointer;
		text-decoration: underline;
		color: #B42B3F;
	}
	.imgHover{
		cursor: pointer;
	}
}

.cooperationBubble{
	.tea-bubble{
		display: flex;
	}
  }

.iframe-card {
	padding: 0;
}

.overview-tab {
	margin-top: 10px;

	.tea-table__header {
		display: none;
	}
}

.report-config-tab {
	.tea-table__header {
		display: none;
	}
}

.report-justify {
	width: 80%;
	padding-top: 10px;

	.tea-justify-grid__col {
		font-size: 12px;

		.trend-wrap {
			display: inline-flex;
			align-items: center;
			white-space: nowrap;
		}
	}

	.tea-justify-grid__col--left {
		width: 58%;
		padding-right: 5%;
		word-break: break-all;
	}

	.tea-justify-grid__col--right {
		width: 42%;
		text-align: left;
	}
}
.report-board {
	.tea-metrics-board__number {
		h2 {
			//white-space: nowrap;
		}
	}
}
.load-row {
	margin-top: 20px;
}

.report-t-btn {
	display: inline-flex;
	align-items: center;

	svg {
		margin-right: 2px;
	}
}

.report-pro-tag {
	max-width: 100% !important;

	span {
		max-width: 100% !important;
		white-space: nowrap !important;
	}
}

.report-status-wrap {
	margin-top: 200px;
	text-align: center;
}

.report-sub-modal {
	.tea-dialog__inner {
		width: 560px;
	}
}

.report-search-tag.is-active {
	width: 80% !important;
}

.report-guard-row {
	.tea-grid__item-6 {
		width: 22%;
	}
}

.bubble-pro {
	left: -15px !important;
}

.guard-detail-menu {
	&.tea-menu {
		width: 170px;
	}

	.tea-menu__text {
		font-size: 12px;
		max-width: 124px;
	}
}
.RangePicker{
	width: 100%;
	.tea-datetimepicker__input.tea-datepicker__input--range{
		width: 100%;
	}
}

@media screen and (min-width: 900px){
	.tea-guard-search{
		.tea-grid__item-75{
			width: 50%;
		}
		.tea-grid__item-6{
			width: 30%;
		}
		.tea-grid__item-18{
			width: 70%;
		}
	}
}
@media screen and (min-width: 1200px){
	.tea-guard-search{
		.tea-grid__item-75{
			width: 33%;
		}
		
		.tea-grid__item-6{
			width: 27%;
		}
		.tea-grid__item-18{
			width: 72%;
		}
	}
}
@media screen and (min-width: 1545px){
	.tea-guard-search{
		.tea-grid__item-75{
			width: 25%;
		}
		
		.tea-grid__item-6{
			width: 25%;
		}
		.tea-grid__item-18{
			width: 70%;
		}
	}
}
.tea-guard-search-range-picker{
	.tea-datepicker__input--range{
		width:100%;
	}
}
.broadcast-time-range-picker{
	.tea-grid__box{
		width:100%;
		.tea-datepicker{
			width:100%;
			.tea-datepicker__input.tea-datepicker__input--range{
				width:100%;
			}
		}
		.tea-timepicker{
			width:100%;
			.tea-timepicker__input.size-l{
				width:100%;
				.tea-input{
					width:100%;
				}
			}
		}
		.tea-datepicker{
			width:100%;
			.tea-datepicker__input{
				width:100%;
				.tea-input{
					width:100%;
				}
			}
		}
	}
	
}

.productPolicyWrap{
	.tea-inputnum{
		width: 100%;
		.tea-input{
			width: calc(100% - 60px);
		}
	}
	.tea-datetimepicker{
		width: 100%;
		.tea-datetimepicker__input{
			width: 100%;
		}
	}
}

.transferItem{
	.tea-timeline-item__card{
		width: 100%;
	}
}

.recordItemWrap{
	display: flex;
	.itemLabel{
		white-space: nowrap;
	}
	.itemValue{
		color: rgba(0, 0, 0, 0.9);
	}
}
		
.approvalWrap{
	margin-top: 20px;
	.t-input__inner{
		font-size: 12px !important;
	}
}
.subProcessWrap{
	display: flex;
	align-items: center;
	font-size: 12px;
	width: 60%;
	&>div{
		display: flex;
		align-items: center;
	}
}
.subProcessWrap:not(:last-child){
	margin-bottom: 10px;
}

.serviceWrap{
	.tea-accordion{
		display: flex;
		.tea-accordion__header{
			width: 30%;
			font-weight: 600;
		}
		.tea-accordion__body{
			flex: 1;
			overflow: hidden;
			position: relative;
			top:-4px
		}
	}
}

.editTimeWrap{
	display: flex;
	align-items: center;
	.editDatePicker{
		width: calc((100% - 20px)/2);
		.tea-datepicker__input,.tea-input{
			width: 100%;
		}
	}
	.separator{
		margin: 0 20px;
	}
}

.productListBox{
	width: 800px;
	.tea-list--option{
		display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
		li{
			width: 32%;
		}
	}
}

.iconBtn{
	display: flex;
	align-items: center;
	&>svg{
		font-size: 16px;
		margin-right: 4px;
	}
}

.myGuard{
	height: 30px;
	font-size: 18px;
	vertical-align: top;
	cursor: pointer;
	padding: 0 10px;
  display: inline-block;
	color: #152646 ;
	&>span{
		margin-left: 4px;
		font-size: 12px;
	}
}
.selectedBtn{
	background:rgba(53,130,251,.1) !important;
  color: #3582fb !important;
	border-radius: 4px;
}

.myGuardWrap{
	width: 110px;
	text-align: center;	
	padding: 0;
}

.approvalGuardTime{
	width: 100%;
	.tea-datepicker__input--range, .tea-timepicker__input--range{
		width: 100%;
	}
}

.guardMonthPicker{
	width: 100%;
	.tea-monthpicker__input,.tea-input{
		width: 100%;
	}
}

.noGuardRow.is-selected{
	background-color: #f0f2f7 !important;
}

.guardRow{
	background-color: #e7f7ef !important;
}

.guardStatusWrap{
	display: flex;
	align-items: center;
	& > span{
		margin-left: 4px;
	}
}

.architectureIconWrap{
	display: flex;
	align-items: center;
	.architectureIcon{
		width: 16px;
		height: 16px;
		margin-right: 4px;
	}
}

.leaderWrap{
	display: flex;
	align-items: center;
	.tamLabel{
	  margin-right: 10px;
	}
	.labelWrap{
	  display: inline-block;
	  width: 82px;
	  flex-shrink: 0;
	}
	.ant-select-selector{
	  border-radius: 0 !important;
	}
  }

  .leaderItem{
	& > span{
		font-weight: 800;
		margin-right: 8px;
	}
  }

  .leaderBubble{
	.tea-bubble{
		max-width: 330px;
	}
  }

  .suggesstion{
	font-size: 14px;
	font-weight: 700;
  }

  .tipLinkBtn{
	margin-left: 10px;
	vertical-align: baseline;
	text-decoration: underline;
	&:hover{
	  text-decoration: underline;
	}
  }

  .archTip{
	margin-left: -4px;
    margin-top: 6px;
	cursor: pointer;
	margin-right: 20px;
  }

  .selectForm{
	.archItem{
		.tea-form__controls--text{
			padding-top: 0 !important;
		}
	}
	.tea-dropdown-btn{
		width: 346px !important;
	}
  }

  .archSvgModal{
	.tea-dialog__inner{
		width: 880px;
		height: 600px;
		padding: 0;
		.tea-dialog__body{
			width: 100%;
			height: 100%;
		}
		.archSvgWrap{
			width: 100%;
			height: 100%;
			overflow: hidden !important;
			position: relative;
			.svgWrap{
				width: 100%;
				height: 100%;
				svg:not(:root) {
					overflow: visible !important;
				}
				svg{
					width: 100%;
					height: 100%;
				}
			}
			.goBtn{
				position: absolute;
				bottom: 20px;
				left: 20px;
			}
			.closeBtn{
				position: absolute;
				top: 20px;
				right: 20px;
				cursor: pointer;
			}
		  }
	}
  }

  .tipBubble{
	.bubbleFooter{
		text-align: right;
		Button{
			margin-left: 10px;
		}
	}
  }

  .messageWrap{
	font-size: 12px;
	font-weight: 200;
	.firstLine{
		display: flex;
		align-items: center;
		margin-bottom: 20px;
	}
	.secondLine{
		margin-bottom: 4px;
	}
  }


  .cloud-resource-warning-title {
	span {
	  color: #E54545;
	}
  }
  
  .cloud-resource-warning-list {
	margin-top: 4px;
	font-size: 12px;
	padding-left: 16px;
  
	li {
	  line-height: 2;
	  list-style: disc;
	}
  }
  .cloud-resource-warning-bd {
	color: #E54545;
	margin-top: 20px;
  }
  
  .noArchModalWrap {
	.tea-dialog__btnwrap{
		text-align: right;
	}
  }
  

