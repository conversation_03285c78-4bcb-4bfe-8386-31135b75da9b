import React, { useState, useMemo, useEffect } from 'react';
import {
	Layout,
	Card,
	Justify,
	H2,
	H3,
	H4,
	Button,
	Avatar,
	Text,
	Table,
	MetricsBoard,
	Row,
	Col,
	message,
	TagSearchBox,
	Switch,
	Tooltip,
	Tag,
	DatePicker,
	SelectMultiple,
	Icon,
	ImagePreview,
	Modal,
	Form,
	Input,
	Radio,
} from '@tencent/tea-component';
const { TextArea } = Input;
import { TitleBubble } from "@src/routes/advisor/pages/guard/components/TitleBubble";
import moment from 'moment';
import { cloneDeep, sumBy, merge } from "lodash";
import { BasicPie } from '@tencent/tea-chart/lib/basicpie';
import { BasicLine } from '@tencent/tea-chart/lib/basicline';
import { AddProfileModal } from './components/AddProfileModal';
import { ReportConfigModal } from './components/ReportConfigModal';
import { SubModal } from './components/SubModal';
import { getProductsGroups } from '@src/api/advisor/estimate';
import {
	DeleteGuardReportEmergencyPlan,
	UpdateGuardReportEmergencyPlan,
	DescribeGuardReportEmergencyPlan,
	DescribeGuardNoRiskStrategies,
	CreateGuardOtherRiskItem,
	CreateGuardReportShow,
	DescribeGuardReportShowTask,
	getDescribeGuardReportSummary,
	deleteGuardReportSummary,
	getDescribeGuardReportDashboard,
	createGuardReportDashboard,
	getDescribeGuardScanRiskSummary,
	updateGuardReportStrategyState,
	getDescribeGuardReportAppidOverView,
	getDescribeGuardReportModel,
	getDescribeGuardReportTitle,
	getDescribeGuardReportAppidRsTrend,
	getDescribeGuardReportRsLoad,
	modifyGuardAlarmState,
	getDescribeGuardReportSubscription,
	updateGuardReportResult,
	updateGuardReportStrategySort
} from "@src/api/advisor/report";
import { getDescribeAlarmLists } from "@src/api/advisor/touch";
import { getDescribeProductList } from "@src/api/advisor/faultNotification";
import { getViewPort } from "@src/routes/architecture/utils";
import { getStorage } from "@src/utils/storage";
import { StatusTip } from "@tea/component";
import { useHistory } from "@tea/app";
import { isEmpty } from 'lodash';
const { Body, Content } = Layout;
const initParams = {
	Limit: 5,
	Offset: 0,
	Filters: [],
	OnlyData: true,
	ShowError: true,
};

export function Report() {
	const guardItemInfo = JSON.parse(getStorage('guardItemInfo'));
	// 权限控制
	const hasPermissions = isReportModifyAllowed(guardItemInfo);
	//查询是否有处理日报权限
	function isReportModifyAllowed(currentGuard) {
		// 仅“巡检风险审批（TAM）”、“护航巡检已完成”状态
		if (currentGuard.Status != 48 && currentGuard.Status != 50) {
			return false
		}
		//有权限修改的人员
		const resEditorList = getResultEditor(currentGuard)
		// 添加海明权限，方便现网验证日报
		resEditorList.push('hemingzhang')
		if (resEditorList.indexOf(getStorage('engName')) == -1) {
			return false
		}
		return true
	}

	//查询是有修改护航单巡检结果的人员
	function getResultEditor(currentGuard) {
		let guys = []
		guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Supporter.split(";"))
		guys = guys.concat(currentGuard.Responser.split(";"))
		guys = guys.concat(currentGuard.AppIdOwner.split(";"))
		return Array.from(new Set(guys)).filter(i => { return i != "" });
	}

	// 应急预案数据
	const [emergencyPlan, setEmergencyPlan] = useState([])
	const [total, setTotal] = useState<number>(0)
	//护航单查询接口常规参数
	const [offset, setOffset] = useState<number>(0)
	const [limit, setLimit] = useState<number>(5)
	// 应急预案加载
	const [emergencyPlanLoading, setEmergencyPlanLoading] = useState<boolean>(false)
	// 当前应急预案
	const [curEmergencyPlan, setCurEmergencyPlan] = useState<any>({})
	// 应急预案修改弹框
	const [emergencyPlanModal, setEmergencyPlanModal] = useState<boolean>(false);
	// 应急预案删除确认弹框
	const [deleteModal, setDeleteModal] = useState<boolean>(false);
	// 无风险巡检项
	const [noRiskRecords, setNoRiskRecords] = useState([]);
	const [noRiskLoading, setNoRiskLoading] = useState<boolean>(false)
	const [noRiskTotal, setNoRiskTotal] = useState<number>(0)
	//护航单查询接口常规参数
	const [noRiskOffset, setNoRiskOffset] = useState<number>(0)
	const [noRiskLimit, setNoRiskLimit] = useState<number>(5)

	const history = useHistory();
	const [addProfileVisible, setAddProfileVisible] = useState(false);
	const [reportConfigVisible, setReportConfigVisible] = useState(false);
	const [subVisible, setSubVisible] = useState(false);
	const [overviewRecords, setOverviewRecords] = useState([]);
	const [riskRecords, setRiskRecords] = useState([]);
	// 巡检日期
	const [guardRiskDate, setGuardRiskDate] = useState('');
	const [riskLoading, setRiskLoading] = useState(true);
	const [troubleRecords, setTroubleRecords] = useState([]);
	const [riskPieData, setRiskPieData] = useState([]);
	const [troubleLoading, setTroubleLoading] = useState(true);
	const [selOverviewItem, setSelOverviewItem] = useState(null);
	const [touchRecords, setTouchRecords] = useState({
		TotalCount: 0,
		AlarmLists: []
	});
	const [touchLoading, setTouchLoading] = useState(true);
	const [filterParams, setFilterParams] = useState({
		...cloneDeep(initParams),
		Filters: [
			{
				Name: 'guard_id',
				Values: [guardItemInfo.GuardId.toString()]
			}
		]
	});
	const [summaryLoading, setSummaryLoading] = useState(true);
	// 监控大屏Url
	const [screenUrl, setScreenUrl] = useState('');
	const [screenLoading, setScreenLoading] = useState(true);
	// 日报title
	const [title, setTitle] = useState('');
	// 页面加载loading
	const [pageLoading, setPageLoading] = useState(true);
	// 趋势选择时间区间
	const [riskTrendDate, setRiskTrendDate] = useState([moment().subtract(14, 'd')
		.format('YYYY-MM-DD'), moment().format('YYYY-MM-DD')]);
	const [subInfo, setSubInfo] = useState<any>({});
	const [productInfo, setProductInfo] = useState([]);
	// 产品下拉框选项
	const [productsOptions, setProductsOptions] = useState([])
	// 勾选的云产品
	const [productList, setProductList] = useState([])
	// 兜底产品
	const [unSupportedProducts, setUnSupportedProducts] = useState([]);
	const [showLoading, setShowLoading] = useState(false);
	const [guardListReport, setGuardListReport] = useState<any>({});

	// 护航负责人
	const resultList = [];
	// 组装各个产品的负责人
	(guardItemInfo.Approvals?.ExpertStatus || []).map(i => {
		// 产品对应的负责人
		let product = (guardItemInfo.Approvals?.ScanResultStatus || []).filter(j => j.Product === i.Product)
		let person = ''
		if (product.length) {
			person = product[0].Handler
		}
		resultList.push({ name: i.ProductName, value: person })
	})

	let reportTimer;
	const operateName = localStorage.getItem('engName');
	useEffect(() => {
		getUnSupportedProducts()
	}, [])

	useEffect(() => {
		// 产品下拉选项去除兜底产品
		if (unSupportedProducts?.length && productsOptions?.length) {
			setProductsOptions(productsOptions.filter(i => !unSupportedProducts.includes(i.value)))
		}
	}, [unSupportedProducts, JSON.stringify(productsOptions)])

	// 查询护航单的无风险巡检项
	const getDescribeGuardNoriskStrategies = async (Offset?, Limit?) => {
		setNoRiskLoading(true)
		try {
			const res = await DescribeGuardNoRiskStrategies({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				Offset: Offset,
				Limit: Limit,
			})
			setNoRiskLoading(false)
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				setNoRiskRecords(res?.data?.Response?.Strategies || [])
				setNoRiskTotal(res?.data?.Response?.Count)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}
	// 加入到终点关注风险项
	const addRiskItem = async (info, value) => {
		try {
			await CreateGuardOtherRiskItem({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				StrategyId: info.ID,
				OpsType: value ? 'add' : 'del'
			});
			info.InOtherRiskItem = value;
			info.loading = false;
			setNoRiskRecords(cloneDeep(noRiskRecords));
			getDescribeGuardScanRiskSummaryList();
		} catch (e) {
			info.loading = false;
			setNoRiskRecords(cloneDeep(noRiskRecords));
		}
	};

	//获取兜底产品
	const getUnSupportedProducts = async () => {
		try {
			const res = await getProductsGroups({
				AppId: 1253985742,
				Env: 'all',
				TaskType: 'guardTaskType',
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				setUnSupportedProducts(res.UnSupportedProducts || [])
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	// 获取订阅信息
	const getDescribeGuardReportSubscriptionInfo = async () => {
		try {
			const res = await getDescribeGuardReportSubscription({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ShowError: true,
				OnlyData: true
			});
			setSubInfo(res);
		} catch (e) { }
	};
	// 获取概览信息
	const getDescribeGuardReportSummaryList = async () => {
		setSummaryLoading(true)
		try {
			const res = await getDescribeGuardReportSummary({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ShowError: true,
				OnlyData: true
			});
			setOverviewRecords(res.ReportSummaryList || []);
			setSummaryLoading(false);
		} catch (e) {
			setSummaryLoading(false);
		}
	};
	// 获取主动服务信息
	const getDescribeAlarmList = async (filterParams) => {
		setTouchLoading(true);
		try {
			const res = await getDescribeAlarmLists(filterParams);
			setTouchRecords(res);
			setTouchLoading(false);
		} catch (e) {
			setTouchLoading(false);
		}
	};
	// 更新主动服务显示信息
	const handleModifyGuardAlarmState = async (info, State) => {
		try {
			await modifyGuardAlarmState({
				GuardId: guardItemInfo.GuardId,
				AlarmId: info.Id,
				State,
				ShowError: true,
				OnlyData: true
			});
			info.State = State;
			info.loading = false;
			setTouchRecords(cloneDeep(touchRecords));
			if (subInfo?.Subscription?.Receiver?.split(';')?.[0] != '') {
				updateGuardReportResultInfo(touchRecords);
			}
		} catch (e) {
			info.loading = false;
			setTouchRecords(cloneDeep(touchRecords));
		}
	};
	// 获取重点隐患风险信息
	const getDescribeGuardScanRiskSummaryList = async () => {
		setRiskLoading(true);
		try {
			const res = await getDescribeGuardScanRiskSummary({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ShowError: true,
				OnlyData: true
			});
			setGuardRiskDate(res.GuardRiskDate || '')
			setRiskRecords(res.GuardRiskList || []);
			setRiskLoading(false);
		} catch (e) {
			setRiskLoading(false);
		};
	};
	// 更新隐患风险显示信息
	const handleUpdateGuardReportStrategyState = async (info, State) => {
		try {
			await updateGuardReportStrategyState({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				State,
				StrategyId: info.StrategyId,
				RiskLevel: info.RiskLevel,
				ShowError: true,
				OnlyData: true
			});
			info.State = State;
			info.loading = false;
			setRiskRecords(cloneDeep(riskRecords));
			if (subInfo?.Subscription?.Receiver?.split(';')?.[0] != '') {
				getDescribeGuardScanRiskSummary({
					AppId: guardItemInfo.MainAppId,
					GuardId: guardItemInfo.GuardId,
					ShowError: true,
					OnlyData: true
				});
			}
		} catch (e) {
			info.loading = false;
			setRiskRecords(cloneDeep(riskRecords));
		}
	};
	// 更新重点关注风险项排序
	const handleUpdateGuardReportStrategySort = async (SortStrategyIdS) => {
		try {
			await updateGuardReportStrategySort({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				SortStrategyIdS,
			});
		} catch (e) {
			setRiskRecords(cloneDeep(riskRecords));
		}
	};
	// 获取APPID下全量资源隐患
	const getDescribeGuardReportAppidOverViewInfo = async (productInfo) => {
		setTroubleLoading(true);
		try {
			const res = await getDescribeGuardReportAppidOverView({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				RiskTop: 5,
				ShowError: true,
				OnlyData: true
			});
			setTroubleRecords(res.StrategySummaries || []);
			const productSummariesTransformData = res.ProductSummaries.map((val) => {
				return {
					type: productInfo[val.Product],
					value: val.HighRiskCount + val.MediumRiskCount
				};
			});
			const productSummariesTransformDataSlice = productSummariesTransformData.slice(0, 5);
			const otherValue = sumBy(productSummariesTransformData.slice(5), 'value');
			productSummariesTransformDataSlice.push({
				type: '其它',
				value: otherValue
			});
			setRiskPieData(productSummariesTransformDataSlice);
			setTroubleLoading(false);
		} catch (e) {
			setTroubleLoading(false);
		}
	};
	// 获取报告模板
	const getDescribeGuardReportModelInfo = async () => {

		try {
			const res = await getDescribeGuardReportModel({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ShowError: true,
				OnlyData: true
			});
			const modelMap: any = {};
			res.ReportModelList.forEach((item, index) => {
				modelMap[index] = {
					...item
				};
			});
			setReportConfigMap(modelMap);
		} catch (e) { }
	};
	// 获取邮件标题
	const getTitle = async () => {
		try {
			const res = await getDescribeGuardReportTitle({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ShowError: true,
				OnlyData: true
			});
			setTitle(res.ReportTitle);
		} catch (e) { }
	};
	const [riskLineData, setRiskLineData] = useState([]);
	// 获取云资源趋势
	const getDescribeGuardReportAppidRsTrendInfo = async (riskTrendDate, productInfo) => {
		try {
			const res = await getDescribeGuardReportAppidRsTrend({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				StartDate: riskTrendDate[0],
				EndDate: riskTrendDate[1],
				TaskType: "allTaskType",
				ShowError: true,
				OnlyData: true,
				Products: productList
			});
			const list = [];
			res.TrendMap.ProductData.forEach((item) => {
				item.List.forEach((val) => {
					list.push({
						time: val.Date,
						value: val.Count,
						instance: productInfo[item.Name]
					});
				});
			});
			setRiskLineData(list);
		} catch (e) { }
	};
	// 历史风险趋势--日期选择范围限制 选择跨度不大于30天
	function disabledDate(date, start) {
		const isAfterToday = date.isAfter(moment(), 'day');
		if (moment.isMoment(start)) {
			return (
				!isAfterToday
				&& !(
					moment(date)
						.subtract(14, 'day')
						.isAfter(start, 'day')
					|| moment(date)
						.add(14, 'day')
						.isBefore(start, 'day')
				)
			);
		}
		return !isAfterToday;
	}
	const [productLoadList, setProductLoadList] = useState([]);
	// 获取云资源负载水位信息
	const getDescribeGuardReportRsLoadInfo = async () => {
		try {
			const res = await getDescribeGuardReportRsLoad({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ShowError: true,
				OnlyData: true
			});
			const list = [];
			for (const key in res.ProductLoadInfo) {
				list.push({
					name: key,
					list: res.ProductLoadInfo[key]
				});
			}
			setProductLoadList(list);
		} catch (e) { }
	};
	// 查询护航单的应急预案
	const getDescribeGuardEmergencyPlans = async (Offset?, Limit?) => {
		setEmergencyPlanLoading(true)
		try {
			const res = await DescribeGuardReportEmergencyPlan({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				Offset: Offset,
				Limit: Limit,
				OnlyData: true
			})
			setEmergencyPlanLoading(false)
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			setEmergencyPlan(res.GuardReportEmergencyPlanList || [])
			setTotal(res.TotalCount)
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}
	// 编辑应急预案
	async function modifyEmergencyPlan() {
		try {
			const res = await UpdateGuardReportEmergencyPlan({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				GuardReportEmergencyPlanList: [curEmergencyPlan]
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				setEmergencyPlanModal(false);
				message.success({ content: '编辑成功' })
				getDescribeGuardEmergencyPlans(offset, limit)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}
	// 删除应急预案
	async function deleteEmergencyPlan() {
		try {
			const res = await DeleteGuardReportEmergencyPlan({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				GuardReportEmergencyPlanIds: [curEmergencyPlan.Id],
				Operator: operateName || ''
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				setDeleteModal(false)
				message.success({ content: '删除成功' })
				getDescribeGuardEmergencyPlans(offset, limit)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}
	useMemo(() => {
		// 查询应急预案
		getDescribeGuardEmergencyPlans(0, limit)
		// 查询无风险巡检项
		getDescribeGuardNoriskStrategies(0, noRiskLimit)
		getDescribeGuardReportSubscriptionInfo();
		getTitle();
		getDescribeGuardReportSummaryList();
		getDescribeGuardScanRiskSummaryList();
		getDescribeGuardReportRsLoadInfo();
		getDescribeAlarmList(filterParams);
		getDescribeGuardReportModelInfo();
	}, []);
	// 无风险巡检项列
	const noRiskColums: any = [
		{
			key: "Product",
			header: "云产品",
			width: 220,
			render(item) {
				return <Tooltip title={productInfo[item.Product]}>
					<Tag className={'report-pro-tag'} theme={'primary'}>
						<span className={'tea-text-overflow'}>
							{
								productInfo[item.Product]
							}
						</span>
					</Tag>
				</Tooltip>;
			}
		},
		{
			key: "Name",
			header: "风险项",
			render(item) {
				return <Tooltip title={item.Name}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Name
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "Desc",
			header: "风险项描述",
			render(item) {
				return <Tooltip title={item.Desc}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Desc
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "Repair",
			header: "优化建议",
			render(item) {
				return <Tooltip title={item.Repair}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Repair
						}
					</span>
				</Tooltip>;
			}
		},
	];
	if (hasPermissions) {
		noRiskColums.push({
			key: "jiyi",
			header: <TitleBubble
				title={'是否加入"重点关注风险项"展示'}
				content={'只有护航负责人才有权限修改报告内容'}
			/>,
			width: 220,
			render: (item) => {
				return <Switch
					loading={item.loading ? true : false}
					value={item.InOtherRiskItem}
					onChange={(value) => {
						item.loading = true;
						setNoRiskRecords(cloneDeep(noRiskRecords));
						addRiskItem(item, value);
					}
					}
				></Switch>;
			}
		});
	}

	const riskColums: any = [
		{
			key: "Id",
			header: "序号",
			width: 60,
			render: (item, key) => {
				return <div style={{
					marginBottom: '-6px'
				}}>
					<Avatar text={`${parseInt(key.split('_')[1]) + 1}`} color={Avatar.Color.Blue} width={20} height={20}></Avatar>
				</div>;
			}
		},
		{
			key: "Uin",
			header: "UIN",
			width: 120,
		},
		{
			key: "GroupType",
			header: "分类",
			width: 120,
			render(item) {
				return Object.entries(item.GroupType).filter(([key, value]) => value).map(([key, value]) => (
					<Tooltip title={value}>
						<Tag className={'report-pro-tag'} theme={key === "TypeOne" ? 'primary' : key === "TypeTwo" ? 'success' : 'warning'}>
							<span className={'tea-text-overflow'}>
								{value}
							</span>
						</Tag>
					</Tooltip>)
				);
			}
		},
		{
			key: "Product",
			header: "云产品",
			width: 220,
			render(item) {
				return <Tooltip title={productInfo[item.Product]}>
					<Tag className={'report-pro-tag'} theme={'primary'}>
						<span className={'tea-text-overflow'}>
							{
								productInfo[item.Product]
							}
						</span>
					</Tag>
				</Tooltip>;
			}
		},
		{
			key: "StrategyName",
			header: "风险项",
			render(item) {
				return <Tooltip title={item.StrategyName}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.StrategyName
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "StrategyDesc",
			header: "风险项描述",
			render(item) {
				return <Tooltip title={item.StrategyDesc}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.StrategyDesc
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "num",
			header: "实例数",
			width: 100,
			align: 'center',
			render(item) {
				return <>
					{
						item.RiskCount == 0 ? '-' : item.RiskCount
					}
				</>;
			}
		},
		{
			key: "grade",
			header: "风险等级",
			width: 120,
			render(item) {
				return item.RiskLevel == 3
					?
					<Text theme={'danger'}>高风险</Text>
					:
					item.RiskLevel == 2
						?
						<Text theme={'warning'}>中风险</Text>
						:
						<Text theme="success">健康</Text>
			}
		},
		{
			key: "StrategyRepair",
			header: "优化建议",
			render(item) {
				return <Tooltip title={item.StrategyRepair}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.StrategyRepair
						}
					</span>
				</Tooltip>;
			}
		},
	];
	if (hasPermissions) {
		riskColums.push({
			key: "jiyi",
			header: <TitleBubble
				title={'是否展示'}
				content={'您可以按需配置是否展示在邮件报告中'}
			/>,
			width: 100,
			render: (item) => {
				return <Switch
					loading={item.loading ? true : false}
					value={item.State == -1 ? false : true}
					onChange={(value) => {
						item.loading = true;
						setRiskRecords(cloneDeep(riskRecords));
						handleUpdateGuardReportStrategyState(item, value ? 1 : -1);
					}
					}
				></Switch>;
			}
		});
	}
	// 应急预案列
	const emergencyPlanColums: any = [
		{
			key: "Product",
			header: "云产品",
			width: 220,
			render(item) {
				return <Tooltip title={productInfo[item.Product]}>
					<Tag className={'report-pro-tag'} theme={'primary'}>
						<span className={'tea-text-overflow'}>
							{
								productInfo[item.Product]
							}
						</span>
					</Tag>
				</Tooltip>;
			}
		},
		{
			key: "RiskScenarioCategory",
			header: "风险场景分类",
			width: 220,
			render(item) {
				return <Tooltip title={item.RiskScenarioCategory}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.RiskScenarioCategory
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "RiskScenario",
			header: "风险场景",
			render(item) {
				return <Tooltip title={item.RiskScenario}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.RiskScenario
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "Measure",
			header: "应对措施或保障方案",
			render(item) {
				return <Tooltip title={item.Measure}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Measure
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "IsPlan",
			header: "是否演练",
			width: 100,
			render(item) {
				return <Tooltip title={item.IsPlan === 1 ? '是' : '否'}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.IsPlan === 1 ? '是' : '否'
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "PlanResult",
			header: <TitleBubble
				title={'演练结果'}
				content={'如果演练需要描述演练结果；如果未演练需要标注计划或原因'}
			/>,
			render: (item) => {
				return item.PlanResult ?
					<Tooltip title={item.PlanResult}>
						<span
							className={'tea-text-overflow'}
						>
							{
								item.PlanResult
							}
						</span>
					</Tooltip> :
					<Text style={{ color: "LightGray" }} verticalAlign="middle">未填写</Text>
			}
		}
	];

	if (hasPermissions) {
		emergencyPlanColums.push({
			key: "emergencyPlanOperate",
			header: <TitleBubble
				title={'操作'}
				content={'只有护航负责人才有权限修改报告内容'}
			/>,
			width: 120,
			render: (item) => {
				return <>
					<Button type="link" onClick={() => { setCurEmergencyPlan(cloneDeep(item)); setEmergencyPlanModal(true) }}>编辑</Button>
					<Button type="link" onClick={() => { setCurEmergencyPlan(cloneDeep(item)); setDeleteModal(true) }}>删除</Button>
				</>;
			}
		});
	}
	const touchColums: any = [
		{
			key: "Id",
			header: "序号",
			width: 60,
			render: (item, key) => {
				return <div style={{
					marginBottom: '-6px'
				}}>
					<Avatar text={`${parseInt(key.split('_')[1]) + 1}`} color={Avatar.Color.Blue} width={20} height={20}></Avatar>
				</div>;
			}
		},
		{
			key: "Uin",
			header: "UIN",
			width: 120,
		},
		{
			key: "FullPolicyName",
			header: "告警策略",
			render(item) {
				return <Tooltip title={item.FullPolicyName}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.FullPolicyName
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "AlarmTime",
			header: "异常时间",
		},
		{
			key: "AlarmObject",
			header: "告警对象",
			render(item) {
				return <Tooltip title={item.AlarmObject}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.AlarmObject
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "Content",
			header: "告警内容",
			render(item) {
				return <Tooltip title={item.Content}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Content
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "Advice",
			header: "处理建议",
			render(item) {
				return <Tooltip title={item.Advice}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Advice
						}
					</span>
				</Tooltip>;
			}
		}
	];
	if (hasPermissions) {
		touchColums.push({
			key: "jiyi",
			header: <TitleBubble
				title={'是否展示'}
				content={'您可以按需配置是否展示在邮件报告中'}
			/>,
			render: (item) => {
				return <Switch
					loading={item.loading ? true : false}
					value={item.State == -1 ? false : true}
					onChange={(value) => {
						item.loading = true;
						setTouchRecords(cloneDeep(touchRecords));
						handleModifyGuardAlarmState(item, value ? 1 : -1);
					}
					}
				></Switch>;
			}
		});
	}
	const troubleColums = [
		{
			key: "Id",
			header: "序号",
			width: 60,
			render: (item, key) => {
				return <div style={{
					marginBottom: '-6px'
				}}>
					<Avatar text={`${parseInt(key.split('_')[1]) + 1}`} color={Avatar.Color.Blue} width={20} height={20}></Avatar>
				</div>;
			}
		},
		{
			key: "Product",
			header: "云产品",
			render(item) {
				return <Tooltip title={productInfo[item.Product]}>
					<Tag className={'report-pro-tag'} theme={'primary'}>
						<span className={'tea-text-overflow'}>
							{
								productInfo[item.Product]
							}
						</span>
					</Tag>
				</Tooltip>;
			}
		},
		{
			key: "StrategyName",
			header: "风险项",
			render(item) {
				return <Tooltip title={item.StrategyName}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.StrategyName
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "num",
			header: "影响实例数",
			render(item) {
				return <>{
					item.HighRiskCount + item.MediumRiskCount
				}</>;
			}
		},
		{
			key: "grade",
			header: "风险等级",
			render(item) {
				return item.HighRiskCount > 0
					?
					<Text theme={'danger'}>高风险</Text>
					:
					item.MediumRiskCount > 0
						?
						<Text theme={'warning'}>中风险</Text>
						:
						<Text>无风险</Text>;
			}
		}
	];
	const overviewColumns: any = [
		{
			key: 'Id',
			header: '',
			width: 40,
			render: (item, key) => {
				return <div style={{
					marginBottom: '-6px'
				}}>
					<Avatar text={`${parseInt(key.split('_')[1]) + 1}`} color={Avatar.Color.Blue} width={20} height={20}></Avatar>
				</div>;
			}
		},
		{
			key: 'Content',
			header: ''
		},
	]
	if (hasPermissions) {
		overviewColumns.push({
			key: 'Con',
			header: '',
			render: (item) => {
				return <div style={{
					textAlign: 'right'
				}}>
					<Button
						type={'link'}
						onClick={
							() => {
								setSelOverviewItem(item);
								setAddProfileVisible(true);
							}
						}
					>编辑</Button>
					<Button
						type={'link'}
						onClick={
							async () => {
								try {
									await deleteGuardReportSummary({
										AppId: guardItemInfo.MainAppId,
										GuardId: guardItemInfo.GuardId,
										ReportSummaryIds: [item.Id]
									});
									message.success({
										content: '删除成功'
									});
									getDescribeGuardReportSummaryList();
								} catch (e) { }
							}
						}
					>
						删除
					</Button>
				</div>;
			}
		})
	}
	// 报告模版信息
	const [reportConfigMap, setReportConfigMap] = useState({
		0: {},
		1: {},
		2: {},
		3: {},
		4: {},
		5: {},
		6: {},
		7: {}
	});
	const viewPortHeight = getViewPort('height');
	const [attributes, setAttributes] = useState([]);
	const [searchValue, setSearchValue] = useState([]);
	const getDescribeGuardReportDashboardInfo = async () => {
		setPageLoading(true);
		try {
			const resPro = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742
			});
			let tmpProductsOptions = []
			for (var i in resPro.ProductDict) {
				tmpProductsOptions.push({ value: i, text: resPro.ProductDict[i] })
			}
			setProductsOptions(tmpProductsOptions)
			setProductInfo(resPro.ProductDict);
			setPageLoading(false);
			getDescribeGuardReportAppidOverViewInfo(resPro.ProductDict);
			getDescribeGuardReportAppidRsTrendInfo(riskTrendDate, resPro.ProductDict);
			const res = await getDescribeGuardReportDashboard({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ProductList: [
					...(guardItemInfo.Products || [])
				],
				ShowError: true,
				OnlyData: true
			});
			const attributesList = [];
			const defaultList = [];
			for (const key in res.ProductPanelMap) {
				const values = res.ProductPanelMap[key].map((item) => {
					return {
						key: item.Id.toString(),
						name: item.Title
					};
				});
				if (values.length > 0) {
					attributesList.push({
						type: "multiple",
						key,
						name: resPro.ProductDict[key],
						values
					});
					defaultList.push({
						attr: {
							type: "multiple",
							key,
							name: resPro.ProductDict[key],
							values
						},
						values
					});
				};
			};
			setAttributes(attributesList);
			setSearchValue(defaultList);
			if (defaultList.length > 0) {
				getScreenUrlBySearch(defaultList);
			} else {
				setScreenLoading(false);
			};
		} catch (e) {
			setPageLoading(false);
		};
	};
	useMemo(() => {
		getDescribeGuardReportDashboardInfo();
	}, []);
	const { pageable, scrollable, draggable } = Table.addons;
	const getScreenUrlBySearch = async (searchList) => {
		setScreenLoading(true);
		try {
			const productPanelIdMap = {};
			searchList.forEach((item) => {
				productPanelIdMap[item.attr.key] = item.values.map((el) => {
					return parseInt(el.key);
				});
			});
			const res = await createGuardReportDashboard({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ProductPanelIdMap: productPanelIdMap,
				ShowError: true,
				OnlyData: true
			});
			setScreenLoading(false);
			setScreenUrl('');
			setScreenUrl(res.DashboardUrl);
		} catch (e) {
			setScreenLoading(false);
		}
	};
	const { RangePicker } = DatePicker;
	const updateGuardReportResultInfo = async (touchRecords) => {
		const resultData = {
			AlarmListResult: {
				AlarmList: touchRecords.AlarmLists.map((item) => {
					return {
						Id: item.Id,
						AlarmPolicy: item.PolicyName,
						AlarmTime: item.AlarmTime,
						AlarmObject: item.AlarmObject,
						AlarmContent: item.Content,
						Suggestion: item.Advice,
						State: item.State,
						AppId: item.AppId,
						Uin: item.Uin + ''
					};
				})
			}
		};
		try {
			await updateGuardReportResult({
				AppId: guardItemInfo.MainAppId,
				GuardId: guardItemInfo.GuardId,
				ResultType: 'DescribeAlarmLists',
				ResultData: JSON.stringify(resultData),
				ShowError: true,
				OnlyData: true
			});
		} catch (e) { };
	};
	useMemo(async () => {
		if (subInfo?.Subscription?.Receiver?.split(';')?.[0] != '' && !touchLoading) {
			updateGuardReportResultInfo(touchRecords);
		}
	}, [subInfo, touchLoading]);

	// 请求Excel异步下载
	const handleReportAsync = async () => {
		setShowLoading(true);
		try {
			const res = await CreateGuardReportShow({
				AppId: 1253985742,
				GuardId: guardItemInfo.GuardId,
				Name: operateName || '',
				OnlyData: true
			});
			if (res.Error) {
				message.error({ content: res.Error?.Message || '生成预览错误，请联系平台管理员！' });
				setShowLoading(false)
				return;
			}
			if (res.ResultId) {
				getDownloadTask(res.ResultId);
				reportTimer = setInterval(() => {
					getDownloadTask(res.ResultId);
				}, 2000);
			} else {
				message.error({ content: '生成预览错误，请联系平台管理员！' });
				setShowLoading(false)
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};
	// 获取Excel异步下载结果
	const getDownloadTask = async (resultId: string) => {
		try {
			const res = await DescribeGuardReportShowTask({ ResultID: resultId, AppId: 1253985742, OnlyData: true });
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			if (['success', 'failed'].includes(res.TaskStatus)) {
				if (res.TaskStatus === 'failed') {
					message.error({ content: '生成预览错误，请联系平台管理员！' });
				}
				clearInterval(reportTimer);
				setGuardListReport({
					CosUrl: res.CosUrl || '',
					TaskStatus: res.TaskStatus || '',
				});
			}
		} catch (err) {
			const message = err.msg || err.toString() || '未知错误';
			message.error({ content: message });
		}
	};

	useEffect(() => {
		if (guardListReport.TaskStatus === 'success' || guardListReport.TaskStatus === 'failed') {
			setShowLoading(false);
		}
	}, [guardListReport]);
 
	return (
		<Body>
			{
				pageLoading
					?
					<div className={'report-status-wrap'}>
						<StatusTip status={"loading"} />
					</div>
					:
					<Content>
						{
							hasPermissions
							&&
							<Content.Header
								title={title}
								operation={
									<>
										{showLoading ?
											<Button type="text" className={'report-t-btn'}>
												<Icon type="loading" />
												<span style={{ color: '#006eff', marginLeft: 4 }}>预览生成中</span>
											</Button>
											:
											!isEmpty(guardListReport) && guardListReport.TaskStatus === 'success' ?
												<ImagePreview previewSrc={guardListReport.CosUrl}  previewTitle="护航报告预览">
													{open => <a onClick={open}>
														<Button type="text" className={'report-t-btn'}>
															<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path d="M300.8 179.2c70.4 0 134.4 57.6 134.4 134.4C435.2 384 377.6 448 300.8 448c-70.4-6.4-134.4-64-134.4-140.8 0-70.4 64-128 134.4-128zM51.2 57.6h921.6c19.2 0 38.4 19.2 38.4 38.4v774.4c0 19.2-19.2 38.4-38.4 38.4H51.2c-19.2 0-38.4-19.2-38.4-38.4V96c0-19.2 19.2-38.4 38.4-38.4z m883.2 76.8H89.6v550.4l179.2-179.2c6.4-6.4 25.6-6.4 32 0L441.6 640l204.8-204.8c6.4-6.4 25.6-6.4 32 0l256 256V134.4zM89.6 748.8V832h844.8v-70.4l-275.2-275.2-185.6 185.6 64 64c6.4 6.4 6.4 25.6 0 32-6.4 6.4-25.6 6.4-32 0l-83.2-83.2-134.4-134.4-198.4 198.4z m211.2-524.8c-44.8 0-83.2 38.4-83.2 83.2 0 51.2 38.4 89.6 89.6 89.6 51.2 0 89.6-38.4 89.6-89.6-6.4-44.8-44.8-83.2-96-83.2z" fill="#d71111"></path></svg>
															<span style={{ color: '#d71111', marginLeft: 2 }}>预览</span>
														</Button>
													</a>}
												</ImagePreview>
												:
												<Button
													type={'link'}
													className={'report-t-btn'}
													onClick={
														() => {
															handleReportAsync();
														}
													}
												>
													<svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" width="16" height="16"><path d="M300.8 179.2c70.4 0 134.4 57.6 134.4 134.4C435.2 384 377.6 448 300.8 448c-70.4-6.4-134.4-64-134.4-140.8 0-70.4 64-128 134.4-128zM51.2 57.6h921.6c19.2 0 38.4 19.2 38.4 38.4v774.4c0 19.2-19.2 38.4-38.4 38.4H51.2c-19.2 0-38.4-19.2-38.4-38.4V96c0-19.2 19.2-38.4 38.4-38.4z m883.2 76.8H89.6v550.4l179.2-179.2c6.4-6.4 25.6-6.4 32 0L441.6 640l204.8-204.8c6.4-6.4 25.6-6.4 32 0l256 256V134.4zM89.6 748.8V832h844.8v-70.4l-275.2-275.2-185.6 185.6 64 64c6.4 6.4 6.4 25.6 0 32-6.4 6.4-25.6 6.4-32 0l-83.2-83.2-134.4-134.4-198.4 198.4z m211.2-524.8c-44.8 0-83.2 38.4-83.2 83.2 0 51.2 38.4 89.6 89.6 89.6 51.2 0 89.6-38.4 89.6-89.6-6.4-44.8-44.8-83.2-96-83.2z" fill="#006eff"></path></svg>
													<span style={{ marginLeft: 2 }}>生成预览</span>
												</Button>
										}
										<Button
											type={'link'}
											className={'report-t-btn'}
											onClick={
												() => {
													setSubVisible(true);
												}
											}
										>
											<svg fill="#006eff" height="16px" width="16px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">    <g>        <path fill="none" d="M0 0h24v24H0z" />        <path d="M22 20.007a1 1 0 0 1-.992.993H2.992A.993.993 0 0 1 2 20.007V19h18V7.3l-8 7.2-10-9V4a1 1 0 0 1 1-1h18a1 1 0 0 1 1 1v16.007zM4.434 5L12 11.81 19.566 5H4.434zM0 15h8v2H0v-2zm0-5h5v2H0v-2z" />    </g></svg>
											发送和订阅
										</Button>
										<Button
											type={'link'}
											className={'report-t-btn'}
											onClick={
												() => {
													setReportConfigVisible(true);
												}
											}
										>
											<svg fill="#006eff" height="16px" width="16px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16" version="1.1">
												<title>系统设置</title>
												<g id="页面-2" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
													<g id="设计资源" transform="translate(-473.000000, -46.000000)">
														<g id="系统设置" transform="translate(473.000000, 46.000000)">
															<polygon id="Path" points="0 0 16 0 16 16 0 16" />
															<polygon id="Path" stroke="#006eff" stroke-width="1.5" points="14.848 8 11.42 2 4.58 2 1.152 8 4.58 14 11.42 14" />
															<path d="M8,11.031 C6.343,11.031 5,9.674 5,8 C5,6.326 6.343,4.969 8,4.969 C9.657,4.969 11,6.326 11,8 C11,9.674 9.657,11.031 8,11.031 Z M8,9.011 C8.552,9.011 9,8.558 9,8 C9,7.442 8.552,6.99 8,6.99 C7.448,6.99 7,7.442 7,8 C7,8.558 7.448,9.01 8,9.01 L8,9.011 Z" id="Shape" fill="#006eff" fill-rule="nonzero" />
														</g>
													</g>
												</g>
											</svg>
											报告配置
										</Button>
									</>
								}
							></Content.Header>
						}
						<Content.Body>
							<Card>
								<Card.Body>
									<H3 style={
										{
											marginBottom: '25px'
										}
									}>护航单信息</H3>
									<Row className={'report-guard-row'}>
										<Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">护航ID：</Text>
												</Col>
												<Col>
													<Text>{guardItemInfo.GuardId}</Text>
												</Col>
											</Row>
										</Col>
										<Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">护航名称：</Text>
												</Col>
												<Col>
													<Text>{guardItemInfo.GuardName}</Text>
												</Col>
											</Row>
										</Col>
										<Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">护航时间：</Text>
												</Col>
												<Col>
													<Text>{`${guardItemInfo.StartTime}~${guardItemInfo.EndTime}`}</Text>
												</Col>
											</Row>
										</Col>
										<Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">客户APPID：</Text>
												</Col>
												<Col>
													<Text>{guardItemInfo.MainAppId}</Text>
												</Col>
											</Row>
										</Col>
										<Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">客户UIN：</Text>
												</Col>
												<Col>
													<Text>{guardItemInfo.Uin}</Text>
												</Col>
											</Row>
										</Col>
										<Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">客户名称：</Text>
												</Col>
												<Col>
													<Text>{guardItemInfo.CustomerName}</Text>
												</Col>
											</Row>
										</Col>
										{(guardItemInfo.RelatedAppId && guardItemInfo.RelatedAppId.length > 0) && <Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">关联客户APPID：</Text>
												</Col>
												<Col>
													<Text>{guardItemInfo.RelatedAppId.join(',')}</Text>
												</Col>
											</Row>
										</Col>}
										{(guardItemInfo.RelatedUin && guardItemInfo.RelatedUin.length > 0) && <Col span={8}>
											<Row>
												<Col span={7}>
													<Text theme="label">关联客户UIN：</Text>
												</Col>
												<Col>
													<Text>{guardItemInfo.RelatedUin.join(',')}</Text>
												</Col>
											</Row>
										</Col>}
									</Row>
								</Card.Body>
								{
									Object.keys(reportConfigMap).map(Number).map(
										key => reportConfigMap[key].State === 1 && reportConfigMap[key].Id === 0 && (
											<Card.Body>
												<H3 style={
													{
														marginBottom: '25px'
													}
												}>护航人员</H3>
												<Row className={'report-guard-row'}>
													<Col span={8}>
														<Row>
															<Col span={7}>
																<Text theme="label">护航负责人：</Text>
															</Col>
															<Col>
																<Text style={{ wordWrap: 'break-word' }}>{guardItemInfo.Approvals.AfterSalesStatus.Supporter}</Text>
															</Col>
														</Row>
													</Col>
													<Col span={8}>
														<Row>
															<Col span={7}>
																<Text theme="label">售后人员：</Text>
															</Col>
															<Col>
																<Text style={{ wordWrap: 'break-word' }}>{guardItemInfo.Approvals.AfterSalesStatus.Handler}</Text>
															</Col>
														</Row>
													</Col>
													<Col span={8}>
														<Row>
															<Col span={7}>
																<Text theme="label">巡检风险审批人：</Text>
															</Col>
															<Col>
																<Text style={{ wordWrap: 'break-word' }}>{getResultEditor(guardItemInfo).join(";")}</Text>
															</Col>
														</Row>
													</Col>
												</Row>
												<Row className={'report-guard-row'}>
													<Col span={24}>
														<Row>
															<Col style={{ width: '9.5%', flexGrow: 0 }}>
																<Text theme="label">专项负责人：</Text>
															</Col>
															<Col>
																{resultList && resultList.map((item) => (
																	<span style={{ marginRight: 20 }}>
																		<Text theme="text">{item.name}：</Text>
																		<Text theme='label' style={{ wordWrap: 'break-word' }}>{item.value}</Text>
																	</span>
																))}
															</Col>
														</Row>
													</Col>
												</Row>
											</Card.Body>
										)
									)
								}
							</Card>
							{
								Object.keys(reportConfigMap).map(Number).map(key => {
									const item = reportConfigMap[key];
									if (item.State !== 1) {
										return null;
									}
									switch (item.Id) {
										case 1:
											return (
												<Card>
													<Card.Body>
														<Justify
															left={
																<H3>总体概览</H3>
															}
															right={
																hasPermissions
																&&
																<Button
																	onClick={() => {
																		setAddProfileVisible(true);
																		setSelOverviewItem(null);
																	}}
																	style={{
																		display: 'inline-flex',
																		alignItems: 'center'
																	}}>
																	<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width={16} height={16} fill={'#006eff'}>    <g>        <path fill="none" d="M0 0h24v24H0z" />        <path d="M11 11V5h2v6h6v2h-6v6h-2v-6H5v-2z" />    </g></svg>
																	添加
																</Button>
															}
														/>
														<Table
															topTip={
																(summaryLoading || overviewRecords.length == 0) && <StatusTip status={summaryLoading ? "loading" : "empty"} />
															}
															className={'overview-tab'}
															columns={
																overviewColumns
															}
															records={
																overviewRecords
															}
															addons={
																[
																	scrollable({
																		maxHeight: 245
																	})
																]
															}
														/>
													</Card.Body>
												</Card>
											);
										case 3:
											return (
												<Card>
													<Card.Body>
														<H3>重点关注风险项 {guardRiskDate && <span style={{ fontSize: 12, color: '#006eff', marginLeft: 6 }}>{guardRiskDate} </span>}</H3>
														{/* <H4 style={{ */}
														{/* 	marginTop: '20px' */}
														{/* }}>Top 5 风险项</H4> */}
														<Table
															style={
																{
																	marginTop: '20px'
																}
															}
															topTip={
																(riskLoading || riskRecords.length == 0) && <StatusTip status={riskLoading ? "loading" : "empty"} />
															}
															records={riskRecords}
															columns={riskColums}
															addons={[
																draggable({
																	onDragEnd: (records, context) => {
																		const start = parseInt(context.dragKey.split('_')[1]);
																		const end = parseInt(context.dropKey.split('_')[1]);
																		if (start !== end) {
																			setRiskRecords(records);
																			handleUpdateGuardReportStrategySort(
																				records.map(item => item.StrategyId)
																			);
																			getDescribeGuardScanRiskSummaryList();
																			setGuardListReport({
																				CosUrl: '',
																				TaskStatus: '',
																			});
																		}
																	}
																}),
															]}
														/>
													</Card.Body>
												</Card>
											);
										case 8:
											return (
												<Card>
													<Card.Body>
														<H3>护航巡检项</H3>
														<Table
															style={{ marginTop: '20px' }}
															columns={noRiskColums}
															records={noRiskRecords}
															topTip={
																(noRiskLoading || noRiskRecords.length === 0) && (
																	<StatusTip status={noRiskLoading ? 'loading' : 'empty'} />
																)
															}
															addons={[
																pageable({
																	recordCount: noRiskTotal,
																	pageSize: noRiskLimit,
																	pageIndex: noRiskOffset / noRiskLimit + 1,
																	onPagingChange: (query) => {
																		if (noRiskLoading) {
																			return;
																		}
																		setNoRiskOffset((query.pageIndex - 1) * query.pageSize);
																		setNoRiskLimit(query.pageSize);
																		getDescribeGuardNoriskStrategies((query.pageIndex - 1) * query.pageSize, query.pageSize);
																	},
																	pageSizeOptions: [5, 10, 20, 30, 50, 100, 200],
																}),
															]}
															recordKey="ID"
														/>
													</Card.Body>
												</Card>
											)
										case 4:
											return (
												<Card>
													<Card.Body>
														<TitleBubble
															title={'云资源负载水位'}
															content={'支持部分云产品水位概览'}
															style={
																{
																	fontWeight: 700,
																	fontSize: '14px'
																}
															}
														/>
														<Row className={'load-row'}>
															{
																productLoadList.length > 0
																	?
																	productLoadList?.map((item, i) => {
																		return <Col span={6} key={i}>
																			<Card style={{
																				height: '100%'
																			}}>
																				<Card.Body>
																					<MetricsBoard
																						className={'report-board'}
																						title={''}
																						value={<H2>{productInfo[item.name]}</H2>}
																						infos={
																							item?.list?.map((val, j) => {
																								return <Justify
																									key={j}
																									className={'report-justify'}
																									left={
																										<>
																											{
																												val.MetricZh
																											}&nbsp;({val.Unit})
																											{
																												val.CurrentValue
																											}
																										</>
																									}
																									right={
																										<div className={'trend-wrap'}>
																											环比
																											{
																												val.CurrentValue >= val.ComparativeValue
																													?
																													<svg xmlns="http://www.w3.org/2000/svg"
																														className="icon icon-tabler icon-tabler-arrow-narrow-up"
																														width="16" height="16" viewBox="0 0 24 24"
																														stroke-width="2" stroke="#0abf5b"
																														fill="none" stroke-linecap="round"
																														stroke-linejoin="round">
																														<path stroke="none" d="M0 0h24v24H0z"
																															fill="none" />
																														<line x1="12" y1="5" x2="12" y2="19" />
																														<line x1="16" y1="9" x2="12" y2="5" />
																														<line x1="8" y1="9" x2="12" y2="5" />
																													</svg>
																													:
																													<svg xmlns="http://www.w3.org/2000/svg"
																														className="icon icon-tabler icon-tabler-arrow-narrow-down"
																														width="16" height="16" viewBox="0 0 24 24"
																														stroke-width="2" stroke="#e54545"
																														fill="none" stroke-linecap="round"
																														stroke-linejoin="round">
																														<path stroke="none" d="M0 0h24v24H0z"
																															fill="none" />
																														<line x1="12" y1="5" x2="12" y2="19" />
																														<line x1="16" y1="15" x2="12" y2="19" />
																														<line x1="8" y1="15" x2="12" y2="19" />
																													</svg>
																											}
																											<Text theme={val.CurrentValue >= val.ComparativeValue ? 'success' : 'danger'}>{Math.abs(val.DayOnDayValue)}%</Text>
																										</div>
																									}
																								/>;
																							})
																						}
																					></MetricsBoard>

																				</Card.Body>
																			</Card>
																		</Col>;
																	})
																	:
																	<div style={{
																		textAlign: 'center',
																		width: '100%'
																	}}>
																		<StatusTip status={'empty'} />
																	</div>
															}
														</Row>
													</Card.Body>
												</Card>
											);
										case 5:
											return (
												<Card>
													<Card.Body>
														<Justify
															left={
																<H3>云资源监控大屏</H3>
															}
															right={
																<>
																	<TitleBubble
																		title={'重点关注指标'}
																		content={'您可以按需筛选重点关注指标'}
																		style={
																			{
																				// color: '#006eff',
																				fontSize: '12px',
																				display: 'inline-flex',
																				verticalAlign: 'middle',
																				marginRight: '10px'
																			}
																		}
																	/>
																	<TagSearchBox
																		minWidth={400}
																		hideHelp={true}
																		value={searchValue}
																		attributes={attributes}
																		tips={'支持按云产品过滤，多个关键字用竖线 “|” 分隔'}
																		onChange={(value) => {
																			setSearchValue(value);
																			getScreenUrlBySearch(value);
																		}}
																		className={'report-search-tag'}
																	></TagSearchBox>
																</>
															}
														/>
														{
															(screenLoading)
																?
																<div style={
																	{
																		height: viewPortHeight - 120,
																		display: 'flex',
																		alignItems: 'center',
																		justifyContent: 'center'
																	}
																}>
																	<StatusTip status={"loading"} />
																</div>
																:
																!screenUrl
																	?
																	<div style={
																		{
																			height: viewPortHeight - 120,
																			display: 'flex',
																			alignItems: 'center',
																			justifyContent: 'center'
																		}
																	}>
																		<StatusTip status={"empty"} />
																	</div>
																	:
																	<iframe
																		width="100%"
																		height={viewPortHeight - 120}
																		src={screenUrl}
																		style={
																			{
																				marginTop: '25px'
																			}
																		}
																	></iframe>
														}
													</Card.Body>
												</Card>
											);
										case 6:
											return (
												<Card>
													<Card.Body>
														<TitleBubble
															title={'业务健康服务'}
															content={'护航期间的客户APPID下全量业务健康服务'}
															style={
																{
																	fontWeight: 700,
																	fontSize: '14px',
																	marginBottom: '15px'
																}
															}
														/>
														<Table
															records={touchRecords.AlarmLists}
															columns={touchColums}
															topTip={
																(touchLoading || touchRecords.AlarmLists.length == 0) && <StatusTip status={touchLoading ? "loading" : "empty"} />
															}
															addons={[
																pageable({
																	recordCount: touchRecords.TotalCount ? touchRecords.TotalCount : 0,
																	onPagingChange: ({ pageIndex, pageSize }) => {
																		setFilterParams({
																			...filterParams,
																			Limit: pageSize,
																			Offset: (pageIndex - 1) * pageSize
																		});
																		getDescribeAlarmList({
																			...filterParams,
																			Limit: pageSize,
																			Offset: (pageIndex - 1) * pageSize
																		});
																	},
																	pageSizeOptions: [5, 10, 20, 30, 50, 100, 200],
																	pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
																	pageSize: filterParams.Limit
																})
															]}
														/>
													</Card.Body>
												</Card>
											);
										case 7:
											return (
												<Card>
													<Card.Body>
														<TitleBubble
															title={'全量云资源风险概况'}
															content={'护航单主账号客户APPID下全量云资源的风险巡检概况，详细风险巡检信息请下载服务报告'}
															style={
																{
																	fontWeight: 700,
																	fontSize: '14px'
																}
															}
														/>
														<Row>
															<Col span={10}>
																<H4 style={{
																	marginTop: '20px'
																}}>Top 5 风险项</H4>
																<Table
																	topTip={
																		(troubleLoading || troubleRecords.length == 0) && <StatusTip status={troubleLoading ? "loading" : "empty"} />
																	}
																	records={troubleRecords}
																	columns={troubleColums}
																/>
															</Col>
															<Col span={14}>
																<H4 style={{
																	marginTop: '20px'
																}}>风险项</H4>
																<BasicPie
																	circle
																	height={300}
																	dataSource={riskPieData}
																	position="value"
																	color="type"
																	dataLabels={{
																		enable: true,
																		formatter: (value, index, data) => `${data.serieName}: ${data.percent}%`,
																	}}
																	legend={{
																		align: 'right',
																	}}
																/>
															</Col>
														</Row>
													</Card.Body>
												</Card>
											);
										case 2:
											return (
												<Card>
													<Card.Body>
														<TitleBubble
															title={'全量云资源趋势'}
															content={'护航单主账号客户APPID下全量云资源的数量趋势'}
															style={
																{
																	fontWeight: 700,
																	fontSize: '14px'
																}
															}
														/>
														<div style={
															{
																marginTop: '20px'
															}
														}>
															<RangePicker
																disabledDate={disabledDate}
																defaultValue={[moment().subtract(14, 'd'), moment()]}
																onChange={value => setRiskTrendDate([
																	value[0].format('YYYY-MM-DD'),
																	value[1].format('YYYY-MM-DD'),
																])
																}
															/>
															<SelectMultiple
																style={{ width: 200, marginLeft: 5 }}
																appearance="button"
																searchable
																clearable
																options={productsOptions}
																value={productList}
																onChange={(v) => {
																	setProductList(v);
																}}
															/>
															<Button type="primary" onClick={() => {
																getDescribeGuardReportAppidRsTrendInfo(riskTrendDate, productInfo);
															}} style={{ marginLeft: 5 }}>{'搜索'}</Button>
														</div>
														<BasicLine
															smooth
															height={500}
															position="time*value"
															dataSource={riskLineData}
															color="instance"
															tooltip={{
																enableSort: true,
																header: { typeText: '类型', valueText: '实例数' },
																height: 300,
															}}
														/>
													</Card.Body>
												</Card>
											);
										case 9:
											return (
												<Card>
													<Card.Body>
														<H3>应急预案</H3>
														<Table
															style={{ marginTop: '20px' }}
															columns={emergencyPlanColums}
															records={emergencyPlan}
															topTip={
																(emergencyPlanLoading || emergencyPlan.length === 0) && (
																	<StatusTip status={emergencyPlanLoading ? 'loading' : 'empty'} />
																)
															}
															addons={[
																pageable({
																	recordCount: total,
																	pageSize: limit,
																	pageIndex: offset / limit + 1,
																	onPagingChange: (query) => {
																		if (emergencyPlanLoading) {
																			return;
																		}
																		setOffset((query.pageIndex - 1) * query.pageSize);
																		setLimit(query.pageSize);
																		getDescribeGuardEmergencyPlans((query.pageIndex - 1) * query.pageSize, query.pageSize);
																	},
																	pageSizeOptions: [5, 10, 20, 30, 50, 100, 200],
																}),
															]}
															recordKey="Id"
														/>
													</Card.Body>
												</Card>
											)
										default:
											return null;
									}
								})
							}
							{/* 添加概览弹窗*/}
							{
								addProfileVisible && <AddProfileModal
									data={selOverviewItem}
									visible={addProfileVisible}
									onClose={(needReq) => {
										if (needReq) {
											getDescribeGuardReportSummaryList();
										}
										setAddProfileVisible(false);
									}}
								></AddProfileModal>
							}
							{/* 报告配置弹窗*/}
							<ReportConfigModal
								set={
									(val) => {
										setReportConfigMap({
											...val
										});
										getDescribeGuardReportModelInfo();
										setGuardListReport({
											CosUrl: '',
											TaskStatus: '',
										});
									}
								}
								subInfo={subInfo}
								config={reportConfigMap}
								visible={reportConfigVisible}
								onClose={() => {
									setReportConfigVisible(false);
								}}
							></ReportConfigModal>
							{/* 发送订阅弹窗*/}
							<SubModal
								subInfo={subInfo}
								config={reportConfigMap}
								visible={subVisible}
								onClose={
									(needReq) => {
										if (needReq) {
											getDescribeGuardReportSubscriptionInfo();
										}
										setSubVisible(false);
									}
								}
							></SubModal>
							{/* 应急预案编辑弹框 */}
							<Modal visible={emergencyPlanModal} caption={productInfo[curEmergencyPlan.Product]} onClose={() => { setEmergencyPlanModal(false) }}>
								<Modal.Body>
									<Form>
										<Form.Item label="风险场景" required showStatusIcon={false} status={curEmergencyPlan.RiskScenario ? 'success' : 'error'} message={curEmergencyPlan.RiskScenario ? '' : '风险场景为必填项'}>
											<Input
												size='full'
												value={curEmergencyPlan.RiskScenario}
												onChange={(value) => { setCurEmergencyPlan({ ...curEmergencyPlan, RiskScenario: value }) }}
												placeholder="请输入风险场景"
											/>
										</Form.Item>
										<Form.Item label="应对措施或保障方案" required showStatusIcon={false} status={curEmergencyPlan.Measure ? 'success' : 'error'} message={curEmergencyPlan.Measure ? '' : '应对措施或保障方案为必填项'}>
											<TextArea
												size='full'
												value={curEmergencyPlan.Measure}
												onChange={(value) => { setCurEmergencyPlan({ ...curEmergencyPlan, Measure: value }) }}
												placeholder="请输入应对措施或保障方案"
											/>
										</Form.Item>
										<Form.Item label="是否演练">
											<Radio.Group
												value={String(curEmergencyPlan.IsPlan)}
												onChange={(value) => { setCurEmergencyPlan({ ...curEmergencyPlan, IsPlan: Number(value) }) }}
											>
												<Radio name="1">是</Radio>
												<Radio name="0">否</Radio>
											</Radio.Group>
										</Form.Item>
										<Form.Item label="演练结果">
											<TextArea
												size='full'
												value={curEmergencyPlan.PlanResult}
												onChange={(value) => { setCurEmergencyPlan({ ...curEmergencyPlan, PlanResult: value }) }}
												placeholder="请输入演练结果"
											/>
										</Form.Item>
									</Form>
								</Modal.Body>
								<Modal.Footer>
									<Button type="primary" disabled={!curEmergencyPlan.RiskScenario || !curEmergencyPlan.Measure} onClick={() => { modifyEmergencyPlan() }}>
										确定
									</Button>
									<Button type="weak" onClick={() => { setEmergencyPlanModal(false) }}>
										取消
									</Button>
								</Modal.Footer>
							</Modal>
							{/* 应急预案删除确认弹框 */}
							<Modal visible={deleteModal} caption='删除提示' onClose={() => { setDeleteModal(false) }}>
								<Modal.Body>确认删除该应急预案吗？</Modal.Body>
								<Modal.Footer>
									<Button type="primary" onClick={() => { deleteEmergencyPlan() }}>
										确定
									</Button>
									<Button type="weak" onClick={() => { setDeleteModal(false) }}>
										取消
									</Button>
								</Modal.Footer>
							</Modal>
						</Content.Body>
					</Content>
			}
		</Body>
	);
}
