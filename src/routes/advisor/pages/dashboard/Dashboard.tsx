import React, { useState, useEffect } from 'react';
import { Card, Layout, Form, Button, Input } from '@tencent/tea-component';
const { Body, Content } = Layout;
import { useForm, useField } from 'react-final-form-hooks';
import { useHistory } from '@tea/app';
import { NotPermission } from '@src/routes/NotPermission';

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return 'validating';
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? 'error' : 'success';
}

export function Dashboard({ history, location }) {
	//页面权限状态
	//const history = useHistory();
	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	const onSubmit = (values) => {
		history.push({
			pathname: `/advisor/dashboard/result/${values.appid}`,
		});
	};
	const { form, handleSubmit, validating, submitting } = useForm({
		onSubmit,
		/**
		 * 默认为 shallowEqual
		 * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
		 * useEffect(() => form.initialize({ }), []);
		 */
		initialValuesEqual: () => true,
		initialValues: {
			uin: (location.state && location.state.uin) || '',
			appid: (location.state && location.state.appid) || '',
		},
		validate: ({ uin, appid }) => ({
			//uin: !uin.trim() ? "请输入UIN" : undefined,
			appid: !appid.trim() ? '请输入AppId' : undefined,
		}),
	});

	//const uin = useField("uin", form);
	const appid = useField('appid', form);
	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
			<Content>
				<Content.Header title="产品信息大盘"></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body>
							<div className="empty-card-body">
								<form onSubmit={handleSubmit} className="assess-search-form">
									<Form layout="inline">
										{/* <Form.Item
                                            status={getStatus(uin.meta, validating)}
                                            className="item"
                                            label="UIN"
                                        >
                                            <Input
                                                size="m"
                                                placeholder="输入UIN"
                                                {...uin.input}
                                            />
                                        </Form.Item> */}
										<Form.Item
											status={getStatus(appid.meta, validating)}
											className="item"
											label="AppId"
										>
											<Input size="m" placeholder="请输入Appid" {...appid.input} />
										</Form.Item>
									</Form>
									<Form.Action className="action">
										<Button
											type="primary"
											htmlType="submit"
											loading={submitting}
											disabled={validating}
										>
											查询
										</Button>
									</Form.Action>
								</form>
							</div>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>}</div >}
		</Body >
	);
}