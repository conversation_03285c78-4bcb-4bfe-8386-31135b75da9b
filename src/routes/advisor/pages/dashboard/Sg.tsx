
import React, { useEffect, useMemo, useState } from 'react';
import { Card, Layout, Form, Button, Input } from '@tencent/tea-component';
const { Body, Content } = Layout;
import { message } from '@tea/component/message';
import { useForm, useField } from 'react-final-form-hooks';
import { Row, Col, MetricsBoard, Icon, Text } from "@tencent/tea-component";
import { Bubble } from "@tencent/tea-component";
import { BasicBar } from "@tencent/tea-chart/lib/basicbar";
import { StackBar } from "@tencent/tea-chart/lib/stackbar";
import { BasicPie } from "@tencent/tea-chart/lib/basicpie";
import { Select } from "@tencent/tea-component";
import { Radio } from "@tencent/tea-component";
import { Table } from '@tencent/tea-component/lib/table';
import { autotip } from '@tencent/tea-component/lib/table/addons';
const { pageable, filterable, sortable } = Table.addons;
import { useHistory } from '@tea/app';
import { InnerDescribeProductDetail, InnerDescribeRegionAndZone, InnerDescribeProductStrategy, InnerDescribeProductRiskData } from '@src/api/advisor/dashboard';
import { CompareDesc } from '@src/routes/advisor/pages/dashboard/PublicFuncions';
import { NotPermission } from '@src/routes/NotPermission';

export function SgOverview({ match }) {
	const history = useHistory();
	//页面权限状态
	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	//Region中英文对照字典
	const [RegionDict, setRegionDict] = useState({})
	//Zone中英文对照字典
	const [ZoneDict, setZoneDict] = useState({})
	//页面刷新状态
	const [PageLoading, setPageLoading] = useState(true)

	//Sg资源详情
	const [SgDetail, setSgDetail] = useState<any>({})

	//Sg地域分布数据
	const [SgRegionDis, setSgRegionDis] = useState([])

	//Sg机型分布--地区选项
	const [RegionOptionsForTypeDis, setRegionOptionsForTypeDis] = useState([])
	//Sg机型分布--当前选择地区
	const [CurrentRegionForTypeDis, setCurrentRegionForTypeDis] = useState('')
	//Sg机型分布--排序后的数据
	const [CurrentSortForTypeDis, setCurrentSortForTypeDis] = useState([])

	//Sg产品使用列表--记录
	const [SgProductDetail, setSgProductDetail] = useState([])
	//Sg产品使用列表--地区选项
	const [RegionOptionsForProductDetail, setRegionOptionsForProductDetail] = useState([])
	//Sg产品使用列表--已选择的地区列表
	const [ChoosedRegionForProductDetail, setChoosedRegionForProductDetail] = useState([])
	//Sg产品使用列表--排序后的记录
	const [SortSgProductDetail, setSortSgProductDetail] = useState([])

	//Sg产品涉及评估策略--隐患选项
	const [StrategyOptions, setStrategyOptions] = useState([])
	//Sg隐患列表--选择的评估项ID
	const [StrategyId, setStrategyId] = useState('0')
	//Sg隐患列表--详情
	const [SgRiskDetail, setSgRiskDetail] = useState<any>({})
	//Sg隐患列表--表头
	const [SgRiskColumns, setSgRiskColumns] = useState([])
	//Sg隐患列表--隐患明细数据
	const [SgRiskList, setSgRiskList] = useState([])
	//Sg隐患列表--已选择的风险等级列表
	const [ChoosedRiskForRiskList, setChoosedRiskForRiskList] = useState([])
	//Sg隐患列表--Offset
	//Sg隐患列表--Limit

	// 页码
	const [productDetailPageIndex, setProductDetailPageIndex] = useState(1);
	const [riskDetailPageIndex, setRiskDetailPageIndex] = useState(1);

	//筛选项全选值
	const ALL_VALUE = "__ALL__";

	//机型分布列表--表头
	const TypeInsColumns = [
		{
			key: 'CNName',
			header: '类型',
		},
		{
			key: 'Count',
			header: '数量',
		},
		{
			key: 'Proportion',
			header: '占比',
			render: item => {
				return <Text>{item.Proportion.toString()}{item.ProportionUnit}</Text>
			},
		}
	]

	//产品使用列表--表头
	const ProductDetailColumns = [
		{
			key: 'Region',
			header: '地域',
			render: item => {
				return <Text>{RegionDict[item.Region].Region}</Text>
			},
		},
		{
			key: 'InstanceCount',
			header: '实例数',
		},
		{
			key: 'RiskRate',
			header: () => (
				<>
					{'资源风险率'}
					<Bubble content={'风险率=当前风险数/(资源数*已开启资源型巡检项)'}>
						<Icon type="help" />
					</Bubble>
				</>
			),
			render: item => {
				return <div>
					<span>{item.RiskRate}%</span>
				</div>

			},
		},
	]

	// 获取产品资源大盘详情
	const getProductDetail = async () => {
		try {
			// 访问后端接口，获取产品详情数据
			const res = await InnerDescribeProductDetail({
				AppId: Number(match.params.appid),
				Product: 'sg',
			})
			// 结束加载状态
			setPageLoading(false)

			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				// 记录产品详细数据
				setSgDetail(res.Data || {})

				// 生成地区分布数据，按各地域数量由高到低按地域排序
				let tmpSgRegionDis = []
				for (let i in res.Data.region_ins_count) {
					tmpSgRegionDis.push({ Region: RegionDict[i].Region, 数量: res.Data.region_ins_count[i], Name: i })
				}
				let tmpSortSgRegionDis = []
				tmpSortSgRegionDis = tmpSgRegionDis.sort(CompareDesc('数量'))
				setSgRegionDis(tmpSortSgRegionDis)

				// 机型分布
				// 生成机型分布的地域选择列表
				let tmpRegionOptionsForTypeDis = [{ value: 'total', text: '所有地域' }]
				for (let i in res.Data.ins_type_count) {
					if (i != 'total') {
						tmpRegionOptionsForTypeDis.push({ value: i, text: RegionDict[i].Region })
					}
				}
				setRegionOptionsForTypeDis(tmpRegionOptionsForTypeDis)
				// 默认选择所有地域
				setCurrentRegionForTypeDis('total')

				// 获取产品使用列表数据
				setSgProductDetail(res.Data.product_detail)
				// 根据地域数量的排序，生成产品使用列表的地域选择列表
				let tmpRegionOptionsForProductDetail = []
				if (tmpSortSgRegionDis.length > 0) {
					tmpSortSgRegionDis.map(item => {
						tmpRegionOptionsForProductDetail.push({ value: item.Name, text: item.Region })
					})
				}
				setRegionOptionsForProductDetail(tmpRegionOptionsForProductDetail)
			}
		} catch (err) {
			setPageLoading(false)
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	// 获取地区和可用区的中英文对照表
	const getRegionAndZone = async () => {
		try {
			const res = await InnerDescribeRegionAndZone({
				AppId: Number(match.params.appid),
			})
			if (res.Error) {
				setPageLoading(false)
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				setRegionDict(res.Region)
				setZoneDict(res.Zone)
			}
		} catch (err) {
			setPageLoading(false)
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	// 隐患列表获取所有的评估项列表
	const getStrategy = async () => {
		try {
			const res = await InnerDescribeProductStrategy({
				AppId: Number(match.params.appid),
				Product: 'sg',
			})
			if (res.Error) {
				setPageLoading(false)
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				let tmp = []
				if (res.Strategy.length > 0) {
					res.Strategy.map(item => {
						tmp.push({ text: item.Name + "  -  高风险：" + item.HighRiskCount + " -  总风险：" + item.AllRiskCount, value: item.ID.toString() })
					})
				}
				setStrategyOptions(tmp)
				if (tmp.length > 0) {
					setStrategyId(tmp[0].value)
				} else {
					setStrategyId('0')
				}
			}
		} catch (err) {
			setPageLoading(false)
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	// 隐患列表获取指定评估项的隐患详细数据
	const getStrategyInstances = async () => {
		try {
			const res = await InnerDescribeProductRiskData({
				Product: 'sg',
				AppId: Number(match.params.appid),
				Offset: 0,
				Limit: 10,
				StrategyId: Number(StrategyId),
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				// 获取该评估项的全部数据
				setSgRiskDetail(res.Detail || {})
				// 首次拉取，提取表头
				if (!SgRiskColumns.length) {
					setSgRiskColumns(res.Detail.RiskFieldsDesc)
				}
				// 拉取风险明细数据
				setSgRiskList(res.Detail.Risks)
				// 重新选择评估项后，重置页面所选择的风险等级
				setChoosedRiskForRiskList([])
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	// 生成机型分布明细数据，根据所选地区变化，刷新内容
	const CurrentTypeIns = useMemo(() => {
		let tmp = []
		if (CurrentRegionForTypeDis) {
			tmp = SgDetail.ins_type_count[CurrentRegionForTypeDis].filter(j => { if (j.Count > 0) { return j } })
		}
		return tmp
	}, [CurrentRegionForTypeDis])

	// 机型分布，排序，刷新内容
	CurrentTypeIns.sort(sortable.comparer(CurrentSortForTypeDis));

	// 生成产品使用明细数据，根据所选地区变化，刷新内容
	useMemo(() => {
		let tmp = []
		if (ChoosedRegionForProductDetail.length) {
			tmp = SgDetail.product_detail.filter(i => {
				if (ChoosedRegionForProductDetail.indexOf(i.Region) > -1) {
					return i
				}
			})
		} else {
			tmp = SgDetail.product_detail || []
		}
		setSgProductDetail(tmp)
	}, [ChoosedRegionForProductDetail])

	// 生成隐患列表明细数据，根据所选评估项变化，刷新查询，并根据地域、可用区的字典渲染结果
	useMemo(() => {
		if (StrategyId != '0' && Object.keys(RegionDict).length && Object.keys(ZoneDict).length) {
			getStrategyInstances();
		}
	}, [StrategyId, RegionDict, ZoneDict])

	// 隐患列表，生成并转换表头
	const RiskColumns = useMemo(() => {
		let tmp = []
		SgRiskColumns.map(i => {
			let c = {
				key: i.Field,
				header: i.FieldName,
				render: item => {
					if (Object.keys(i.FieldDict).length) {
						// 根据 FieldDict 内容渲染字段
						return <Text>{i.FieldDict[item[i.Field]]}</Text>
					} else {
						if (i.Field === 'Tags') {
							// Tags 字段显示格式为 key:value 列表
							if (item[i.Field]) {
								return <>
									{item[i.Field].map(i => {
										return <div>
											<span>{i.Key}:{i.Value}</span>
										</div>
									})}
								</>
							} else {
								return <></>
							}
						} else if (i.Field === 'PrivateIPAddresses' || i.Field === 'PublicIPAddresses') {
							// 该两字段显示为 ip 列表
							if (item[i.Field]) {
								return <>
									{item[i.Field].map(i => {
										return <div>
											<span>{i}</span>
										</div>
									})}
								</>
							} else {
								return <></>
							}
						} else if (i.Field === 'Region') {
							// 地域字段根据 Region Dict 翻译为中文
							if (item[i.Field]) {
								if (RegionDict[item[i.Field]]) {
									return <Text>{RegionDict[item[i.Field]].Region}</Text>
								} else {
									return <Text>{item[i.Field]}</Text>
								}
							} else {
								return <></>
							}
						} else if (i.Field === 'Zone') {
							// 可用区字段根据 Zone Dict 翻译为中文
							if (item[i.Field]) {
								if (ZoneDict[item[i.Field]]) {
									return <Text>{ZoneDict[item[i.Field]]}</Text>
								} else {
									return <Text>{item[i.Field]}</Text>
								}
							} else {
								return <></>
							}
						} else {
							return <Text>{item[i.Field]}</Text>
						}
					}
				}
			}
			tmp.push(c)
		})
		return tmp
	}, [SgRiskColumns])

	// 隐患列表，根据页面所选风险等级变化，刷新内容
	useMemo(() => {
		let tmp = []
		if (ChoosedRiskForRiskList.length) {
			tmp = SgRiskDetail.Risks.filter(i => {
				if (ChoosedRiskForRiskList.indexOf(i.Level) > -1) {
					return i
				}
			})
		} else {
			// 不选默认为全选
			tmp = SgRiskDetail.Risks || []
		}
		setSgRiskList(tmp)
	}, [ChoosedRiskForRiskList])

	// 初始化接口调用，注意接口有相互依赖，需要顺序执行
	// step1：拉取地区和可用区中英文对照字典，拉取隐患列表里所有的评估项列表
	useEffect(() => {
		getRegionAndZone();
		getStrategy();
	}, [])

	//step2：拉取产品资源详情
	useEffect(() => {
		if (Object.keys(RegionDict).length) {
			getProductDetail();
		}
	}, [RegionDict])

	//监听页面loading
	useEffect(() => {
		let loading = null
		if (PageLoading) {
			loading = message.loading({ content: '加载中', duration: 0 })
		} else {
			if (loading) {
				loading.hide()
			}
		}
		return () => {
			if (loading) {
				loading.hide()
			}
		}
	}, [PageLoading])

	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
			<Content className="intlc-survey-content intlc-stack-fullscreen intlc-stack-has-min-width">
				<Content.Header
					showBackButton
					onBackButtonClick={() => {
						history.push(`/advisor/dashboard/result/${match.params.appid}`);
					}}
				/>
				<Content.Body>
					<Card>
						<Card.Body title={<>
							<Text>SG 总览&nbsp;&nbsp;</Text>
							<Text style={{ fontSize: 5 }} theme="label">
								<span>{'AppID'}：</span>
								<span>{match.params.appid}</span>
							</Text>
						</>}>
							<hr />
							<Row showSplitLine>
								<Col>
									<MetricsBoard
										title="地域"
										value={<><Text theme="success">{SgDetail.total_region_count}</Text></>}
									/>
								</Col>
								<Col>
									<MetricsBoard
										title="SG 实例数量"
										value={<><Text theme="success">{SgDetail.total_ins_count}</Text></>}
									/>
								</Col>
								<Col>
									<MetricsBoard
										title="巡检隐患数"
										value={<><Text theme={SgDetail.total_risk_count > 0 ? "danger" : "success"}>{SgDetail.total_risk_count}</Text></>}
										onClick={() => { history.push(`/advisor/assess/result/${match.params.appid}`) }}
										infos={[
											<>
												点击跳转服务报告查看详情
											</>,
										]}
									/>
								</Col>
							</Row>
						</Card.Body>
					</Card>

					<Card>
						<Card.Body title="SG 规模/地域">
							<BasicBar
								height={250}
								position="Region*数量"
								dataSource={SgRegionDis}
							/>
						</Card.Body>
					</Card>

					<div className="intlc-survey-summary">
						<Card className="app-international-code-card">
							<Card.Body title={<><Text>类型比例</Text><Select style={{ marginLeft: 10 }}
								searchable
								boxSizeSync
								size="m"
								type="simulate"
								appearance="button"
								options={RegionOptionsForTypeDis}
								value={CurrentRegionForTypeDis}
								onChange={value => setCurrentRegionForTypeDis(value)}
							/></>}>
								<BasicPie
									circle
									height={300}
									dataSource={CurrentTypeIns}
									position="Count"
									color="CNName"
									dataLabels={{
										enable: true,
										formatter: (value, index, data) => {
											return `${data.serieName}: ${data.percent}%`;
										},
									}}
									legend={{
										align: 'right',
									}}
								/>
							</Card.Body>
						</Card>
						<Card>
							<Card.Body>
								<Table
									records={CurrentTypeIns}
									columns={TypeInsColumns}
									addons={[
										autotip({
											emptyText: '没有数据',
										}),
										sortable({
											columns: ['Count'],
											value: CurrentSortForTypeDis,
											onChange: value => setCurrentSortForTypeDis(value),
										})
									]}
								></Table>
							</Card.Body>
						</Card>
					</div>
					<Card>
						<Card.Body title="产品使用">
							<Table
								columns={ProductDetailColumns}
								records={[...SgProductDetail].sort(sortable.comparer(SortSgProductDetail))}
								addons={[
									pageable({
										pageIndex: productDetailPageIndex,
										onPagingChange: (pagingQuery) => {
											setProductDetailPageIndex(pagingQuery.pageIndex);
										},
									}),
									filterable({
										type: "multiple",
										column: "Region",
										all: {
											value: ALL_VALUE,
											text: "全部",
										},
										value: ChoosedRegionForProductDetail,
										onChange: value => {
											setChoosedRegionForProductDetail(value);
											setProductDetailPageIndex(1);
										},
										options: RegionOptionsForProductDetail,
									}),
									sortable({
										columns: [
											{
												key: "InstanceCount",
												prefer: "desc",
											},
											{
												key: 'RiskRate',
												prefer: "desc",
											},
										],
										value: SortSgProductDetail,
										onChange: value => {
											setSortSgProductDetail(value);
											setProductDetailPageIndex(1);
										},
									}),
								]}
							/>
						</Card.Body>
					</Card>
					<Card>
						<Card.Body title={<>隐患列表
							<Select style={{ marginLeft: 10 }}
								searchable
								boxSizeSync
								size="l"
								type="simulate"
								appearance="button"
								options={StrategyOptions}
								value={StrategyId}
								onChange={value => {
									setStrategyId(value);
									setRiskDetailPageIndex(1);
								}}
							/></>}>
							<Table
								columns={RiskColumns}
								records={SgRiskList}
								addons={[
									pageable({
										pageIndex: riskDetailPageIndex,
										onPagingChange: (pagingQuery) => {
											setRiskDetailPageIndex(pagingQuery.pageIndex);
										},
									}),
									filterable({
										type: "multiple",
										column: "Level",
										value: ChoosedRiskForRiskList,
										onChange: value => {
											setChoosedRiskForRiskList(value);
											setRiskDetailPageIndex(1);
										},
										all: {
											value: ALL_VALUE,
											text: "全部",
										},
										options: [
											{ value: '3', text: "高风险" },
											{ value: '2', text: "中风险" },
										]
									}),
								]}
							/>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>}</div>}
		</Body>
	);
}