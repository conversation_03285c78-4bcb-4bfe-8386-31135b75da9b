import React, { useEffect, useState } from 'react';
import { Card, Layout, Form, Button, Input } from '@tencent/tea-component';
import { Select } from "@tencent/tea-component";
import { H1, H2, H3, H4, H5, H6 } from "@tencent/tea-component";
const { Body, Content } = Layout;
import { message } from '@tea/component/message';
import { useForm, useField } from 'react-final-form-hooks';
import { Row, Col, MetricsBoard, Icon, Text } from "@tencent/tea-component";
import { Bubble } from "@tencent/tea-component";
import { Table } from '@tencent/tea-component/lib/table';
import { Progress } from "@tencent/tea-component";
import { InputAdornment } from "@tencent/tea-component";
const { pageable, sortable } = Table.addons;
import { useHistory, useAegisLog } from '@tea/app';
import { InnerDescribeProductOverview, InnerDescribeProductDetail, InnerDescribeRegionAndZone, InnerDescribeCustomerRegionAndZone } from '@src/api/advisor/dashboard';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { NotPermission } from '@src/routes/NotPermission';
import { DatePicker } from "@tencent/tea-component";
import moment from 'moment';
import { reportVisitPage } from '@src/utils/report';

export function ProductOverview({ match }) {
	//页面权限状态
	const history = useHistory();
	const aegis = useAegisLog();

	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	const [currentDate, setCurrentDate] = useState('')

	//页面loading
	const [PageLoading, setPageLoading] = useState(true)
	//资源总览--相关变量
	const [ProductCount, setProductCount] = useState(0);
	const [CustomerUsedProductCount, setCustomerUsedProductCount] = useState(0);
	const [ScanProductCount, setScanProductCount] = useState(0);
	const [SecurityInfo, setSecurityInfo] = useState({ waf: 0 });
	const [RegionCount, setRegionCount] = useState(0);
	const [ZoneCount, setZoneCount] = useState(0);
	const [RiskCount, setRiskCount] = useState(0);
	const [HighRiskStrategyCount, setHighRiskStrategyCount] = useState(0);
	const [HighRiskInstanceCount, setHighRiskInstanceCount] = useState(0);
	const [MediumRiskStrategyCount, setMediumRiskStrategyCount] = useState(0);
	const [MediumRiskInstanceCount, setMediumRiskInstanceCount] = useState(0);

	const [UpdateTime, setUpdateTime] = useState('--');
	const [CustomerName, setCustomerName] = useState('--');
	//维度列表
	const [Groups, setGroups] = useState<Array<{ Id: number, GroupName: string }>>([])
	//产品列表
	const [Products, setProducts] = useState([])
	//产品列表--下拉框
	const [ProductsOptions, setProductsOptions] = useState([])
	//产品映射表
	const [ProductsMap, setProductsMap] = useState(new Map())
	//Region中英文对照字典
	const [RegionDict, setRegionDict] = useState({})
	//Zone中英文对照字典
	const [ZoneDict, setZoneDict] = useState({})
	//资源大盘-概览数据 
	const [ProductInfo, setProductInfo] = useState({})
	//资源大盘-详情清单
	const [ProductInfoList, setProductInfoList] = useState([])
	//资源大盘-排序后详情清单
	const [SortProductInfoList, setSortProductInfoList] = useState([])

	const [ZoneOptions, setZoneOptions] = useState([])
	const [SelectedZone, setSelectedZone] = useState('')

	const [RegionOptions, setRegionOptions] = useState([])
	const [SelectedRegion, setSelectedRegion] = useState('')

	// 页码
	const [productInfoPageIndex, setProductInfoPageIndex] = useState(1);

	//安全隐患--表头
	const SecurityRiskColumns = []

	useEffect(() => {
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: '产品信息大盘',
			ext3: localStorage.getItem('engName')
		})
		reportVisitPage({
			isaReportMeunName: '产品信息大盘',
		});
	}, []);

	//产品地区分布浮窗-表头
	const ProductRegionDisColumns = [
		{
			key: 'RegionName',
			header: '地区名称',
		},
		{
			key: 'Region',
			header: '地区ID',
		},
		{
			key: 'Num',
			header: '资源数',
		}
	]

	//产品地区分布浮窗-表头
	const ProductZoneDisColumns = [
		{
			key: 'ZoneName',
			header: '可用区名称',
		},
		{
			key: 'Zone',
			header: '可用区ID',
		},
		{
			key: 'Num',
			header: '资源数',
		}
	]

	//资源大盘--表头
	const ProductInfoColumns = [
		{
			key: 'Product',
			header: '产品',
			render: item => {
				let str = ProductsMap.get(item.Product) || ''

				if (item.Product === 'Elasticsearch Service') {
					return <><a href={`/advisor/dashboard/es/${match.params.appid}`}>{str}</a></>
				} else {
					return <><a href={`/advisor/dashboard/${item.Product}/${match.params.appid}`}>{str}</a></>
				}
			},
		},
		{
			key: 'RegionCount',
			header: '地区数',
			render: item => {
				if (item.Product === 'cdn' || item.Product === 'live') {
					return '--'
				} else {
					return item.RegionCount
				}
			}
		},
		{
			key: 'ZoneCount',
			header: '可用区数',
			render: item => {
				if (item.Product === 'clb' || item.Product === 'vpc' || item.Product === 'cdn' || item.Product === 'eip' || item.Product === 'cos' || item.Product === 'live' || item.Product === 'sg' || item.Product === 'acl' || item.Product === 'tdmq') {
					return '--'
				} else {
					return item.ZoneCount
				}
			},
		},
		{
			key: 'InstanceCount',
			header: '实例数量',
		},
		{
			key: 'RiskCount',
			header: '隐患数量',
		},
		{
			key: 'RiskRate',
			header: () => (
				<>
					{'资源风险率'}
					<Bubble content={'风险率=当前风险数/(资源数*已开启资源型巡检项)'}>
						<Icon type="help" />
					</Bubble>
				</>
			),
			render: item => {
				return <div>
					<span>{item.RiskRate}%</span>
				</div>

			},
		},
		{
			key: 'index1',
			header: '配额',
			width: 400,
			render: item => {
				let tmp = item.Quota.filter(j => { if (j.Total > 0) { return j } })
				if (tmp.length === 0) {
					return <Text>#</Text>
				} else {
					return <>
						{
							tmp.map(i => {
								let percent = (i.Used / i.Total) * 100 || 0
								return <div>
									<Progress
										percent={percent}
										strokeWidth={16}
										theme="success"
										text={
											percent => `${i.Name}  总量：${i.Total}  使用量：${i.Used}  使用率：${percent.toFixed(2)} %`
										}
									/>
								</div>
							})
						}
					</>
				}
			}
		}
	]

	//获取资源大盘概览
	const getProductOverview = async () => {
		try {
			const res = await InnerDescribeProductOverview({
				AppId: Number(match.params.appid),
				Date: currentDate,
				region: SelectedRegion,
				zone: SelectedZone,
				env: "",
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				setPageLoading(false)
				return
			} else {
				//更新数据
				setCustomerName(res.CustomerName)
				setProductCount(res.ProductCount)
				setCustomerUsedProductCount(res.CustomerUsedProductCount)
				setScanProductCount(res.ScanProductCount)
				setSecurityInfo(res.SecurityInfo)
				setRegionCount(res.RegionCount)
				setZoneCount(res.ZoneCount)
				setRiskCount(res.RiskCount)
				setHighRiskInstanceCount(res.HighRiskInstanceCount)
				setHighRiskStrategyCount(res.HighRiskStrategyCount)
				setMediumRiskInstanceCount(res.MediumRiskInstanceCount)
				setMediumRiskStrategyCount(res.MediumRiskStrategyCount)

				setUpdateTime(res.UpdateTime)
				// setProductInfo(res.ProductInfo)
				setProductInfoList(res.ProductInfo)
				// console.log(res)
				setPageLoading(false)
			}
		} catch (err) {
			setPageLoading(false)
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	//获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: Number(match.params.appid),
			})
			if (res.Error) {
				setPageLoading(false)
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				setProducts(res.Products)
				setGroups(res.Groups)
				let tmpProductsOptions = []
				let tmpProductsMap = new Map()
				for (var i in res.ProductDict) {
					tmpProductsOptions.push({ value: i, text: res.ProductDict[i] })
					tmpProductsMap.set(i, res.ProductDict[i])
				}
				setProductsOptions(tmpProductsOptions)// 产品下拉框
				setProductsMap(tmpProductsMap)// 产品映射表
			}
		} catch (err) {
			setPageLoading(false)
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	//获取地区和可用区的中英文对照表
	const getRegionAndZone = async () => {
		try {
			const res = await InnerDescribeRegionAndZone({
				AppId: Number(match.params.appid),
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				setPageLoading(false)
				return
			} else {
				setRegionDict(res.Region)
				setZoneDict(res.Zone)
			}
		} catch (err) {
			setPageLoading(false)
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	//获取客户的地区和可用区列表
	const getCustomerRegionAndZone = async () => {
		try {
			const res = await InnerDescribeCustomerRegionAndZone({
				AppId: Number(match.params.appid),
				Date: currentDate,
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				setPageLoading(false)
				return
			} else {
				let tmpZoneOptions = []
				tmpZoneOptions.push({ text: "全部", value: "" })
				if (res.ZoneList.length > 0) {
					res.ZoneList.map(i => {
						tmpZoneOptions.push({ text: ZoneDict[i], value: i })
					})
				}
				setZoneOptions(tmpZoneOptions)
			}
		} catch (err) {
			setPageLoading(false)
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	//初始化接口调用，注意接口有相互依赖，需要顺序执行
	//step1  拉取产品和地区的配置信息
	useEffect(() => {
		getProductsGroupsInfo();
		getRegionAndZone();
	}, [])

	useEffect(() => {
		getCustomerRegionAndZone();
	}, [ZoneDict, currentDate])

	//step2  拉取资源概览
	useEffect(() => {
		if (ProductsMap.size && Object.keys(RegionDict).length) {
			getProductOverview();
		}
	}, [ProductsMap, RegionDict, SelectedZone, currentDate])

	//step3 拉取资源详情
	/*
	useEffect(() => {
		if (Object.keys(ProductInfo).length) {
			let tmpList = [];
			for (let i in ProductInfo) {
				let tmpDict = ProductInfo[i]
				tmpDict["product"] = i
				tmpList.push(tmpDict)
			}
			//console.log(tmpList);
			setProductInfoList(tmpList);
		}
	}, [ProductInfo])*/

	//监听页面loading
	useEffect(() => {
		let loading = null
		if (PageLoading) {
			loading = message.loading({ content: '加载中', duration: 0 })
		} else {
			if (loading) {
				loading.hide()
			}
		}
		return () => {
			if (loading) {
				loading.hide()
			}
		}
	}, [PageLoading])

	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
			<Content className="intlc-survey-content intlc-stack-fullscreen intlc-stack-has-min-width">
				<Content.Header
					showBackButton
					onBackButtonClick={() => {
						history.push('/advisor/dashboard');
					}}
				/>
				<Content.Body>
					<Card>
						<Card.Body
							title={
								<>
									<div>
										<Form style={{ marginTop: 20 }} layout={"inline"}>
											<Form.Item label={
												<>
													日期
													<Bubble content={'时间选择范围为30天内；若日期为空，则默认拉取最近一次的扫描数据。'}>
														<Icon type="help" />
													</Bubble>
													：
												</>}>
												<DatePicker
													// defaultValue={moment(reportDate)}
													range={[
														moment()
															.subtract(30, "d")
															.startOf("d"),
														moment().endOf("d"),
													]}
													onChange={
														value => {
															if (value) {
																setCurrentDate(value.format("YYYY-MM-DD"))
															} else {
																setCurrentDate('')
															}
														}
													}
													onOpenChange={open => console.log(open ? "open" : "close")}
													clearable
												/>
											</Form.Item>
											<Form.Item label="可用区：">
												<Select style={{ marginLeft: 10 }}
													searchable
													boxSizeSync
													size="m"
													type="simulate"
													appearance="button"
													options={ZoneOptions}
													value={SelectedZone}
													onChange={value => setSelectedZone(value)}
												/>
											</Form.Item>
										</Form>
									</div>
								</>
							}
						>
						</Card.Body>
					</Card>

					<Card>
						<Card.Body title={<>
							<Text>产品信息总览&nbsp;&nbsp;</Text>
							<Text style={{ fontSize: 5 }} theme="label">
								<span>{'AppID'}：</span>
								<span>{match.params.appid}</span>
								<span>&nbsp;&nbsp;</span>
								<span>{'客户名称'}：</span>
								<span>{CustomerName}</span>
								<span>&nbsp;&nbsp;</span>
								<span>{'更新时间'}：</span>
								{
									<span>{UpdateTime}</span>
								}
							</Text>
						</>}>
							<Row showSplitLine>
								<Col>
									<MetricsBoard
										title={<>
											<Text theme="text">重点关注的产品数</Text>
											<Bubble
												arrowPointAtCenter
												placement="top-end"
												content="仅包括关注的部分核心产品"
											>
												<Icon type="info" />
											</Bubble></>}
										value={<>
											<Text theme="success">{ProductCount}</Text>
										</>}
										infos={[
											<>
												用户使用产品数：{CustomerUsedProductCount}
												<br />
												巡检覆盖产品数：{ScanProductCount}
											</>,
										]}
									/>
								</Col>
								<Col>
									<MetricsBoard
										title="地域数量"
										value={<><Text theme="success">{RegionCount}</Text></>}
									/>
								</Col>
								<Col>
									<MetricsBoard
										title="可用区数量"
										value={<><Text theme="success">{ZoneCount}</Text></>}
									/>
								</Col>
								<Col>
									<MetricsBoard
										title="高风险隐患"
										value={
											<></>
										}
										onClick={() => { history.push(`/advisor/assess/result/${match.params.appid}`) }}
										infos={[
											<>
												<br />
												<H3>
													<Text theme={HighRiskStrategyCount > 0 ? "danger" : "success"}>高风险项目数：{HighRiskStrategyCount}</Text>
													<br />
													<Text theme={HighRiskInstanceCount > 0 ? "danger" : "success"}>高风险隐患数：{HighRiskInstanceCount}</Text>
												</H3>
												<br />
												点击跳转服务报告查看详情
											</>,
										]}
									/>
								</Col>
								<Col>
									<MetricsBoard
										title="中风险隐患"
										value={
											<>
											</>
										}
										// onClick={() => { history.push(`/advisor/assess/result/${match.params.appid}`) }}
										infos={[
											<>
												<br />
												<H3>
													<Text theme={MediumRiskStrategyCount > 0 ? "danger" : "success"}>中风险项目数：{MediumRiskStrategyCount}</Text>
													<br />
													<Text theme={MediumRiskInstanceCount > 0 ? "danger" : "success"}>中风险隐患数：{MediumRiskInstanceCount}</Text>
												</H3>
											</>,
										]}
									/>
								</Col>
							</Row>

						</Card.Body>
					</Card>

					<Card>
						<Card.Body title="关键产品使用信息">
							<>
								是否开启WAF：<b><Text theme={SecurityInfo.waf > 0 ? "success" : "danger"}>{SecurityInfo.waf > 0 ? "已开启" : "未开启"}</Text></b>
							</>
						</Card.Body>
					</Card>

					<Card>
						<Card.Body title="产品信息大盘">
							<Table
								columns={ProductInfoColumns}
								records={[...ProductInfoList].sort(sortable.comparer(SortProductInfoList))}
								addons={[
									pageable({
										pageIndex: productInfoPageIndex,
										onPagingChange: (pagingQuery) => {
											setProductInfoPageIndex(pagingQuery.pageIndex);
										},
										pageSize: 100,
									}),
									sortable({
										columns: [
											{
												key: "Product",
												prefer: "desc",
											},
											{
												key: "RegionCount",
												prefer: "desc",
											},
											{
												key: "ZoneCount",
												prefer: "desc",
											},
											{
												key: "InstanceCount",
												prefer: "desc",
											},
											{
												key: "RiskCount",
												prefer: "desc",
											},
											{
												key: "RiskRate",
												prefer: "desc",
											},
										],
										value: SortProductInfoList,
										onChange: value => {
											setSortProductInfoList(value);
											setProductInfoPageIndex(1);
										},
									}),
								]}
							/>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>}</div>}
		</Body>
	);
}