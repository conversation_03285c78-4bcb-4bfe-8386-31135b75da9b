import React, { useState, useEffect, useContext, useMemo } from 'react';
import { useHistory } from '@tea/app';
import { app } from '@tencent/tea-app';
import moment from 'moment';
import { DatePicker, Form, Input, Table, Card, Button, Modal, message as tips } from '@tencent/tea-component';
import { Layout } from '@tea/component/layout';
import { Bubble } from '@tea/component/bubble';
import { Icon } from '@tea/component/icon';
import { reportVisitPage } from '@src/utils/report';

import { insertCSS } from '@src/utils/insertCSS';
import { NotPermission } from '@src/routes/NotPermission';
import { describeAlarmResult } from '@src/api/advisor/alarm';
import _ from 'lodash';
const { RangePicker } = DatePicker;

insertCSS(
	'AdvisorLable',
	`
.app-advisor-layout__content-body-inner {
	max-width: 100% !important;
	}

`
);
const { Body, Content } = Layout;
const { autotip, pageable, scrollable, filterable } = Table.addons;

export function Alarm({ history }) {
	// 页面权限状态
	const history1 = useHistory();
	const [permission, setPermission] = useState(0);  // 0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	// 从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		const menuItems = JSON.parse(localStorage.getItem('menuItems'));
		if (menuItems) {
			// 判断是否存在
			const tmp = menuItems.filter((i) => {
				if (history1.location.pathname.includes(i.route)) {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
		// 临时测试
		setPermission(1);
	};
	// 持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => {
			CheckPermission();
		}, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	useEffect(() => {
		reportVisitPage({
			isaReportMeunName: '告警管理',
		});
	}, []);
	const [startTime, setStartTime] = useState(moment().subtract(6, 'd')
		.startOf('d')
		.format('YYYY-MM-DD'));
	const [endTime, setEndTime] = useState(moment().endOf('d')
		.format('YYYY-MM-DD'));
	const [receiver, setReceiver] = useState(localStorage.getItem('engName'));
	const [product, setProduct] = useState('');
	const [AppId, setAppId] = useState('');
	const [queryFlag, setQueryFlag] = useState(true);

	// 告警明细内容
	const [alarmDetails, setAlarmDetails] = useState([]);

	// 数据加载状态
	const [isLoading, setIsLoading] = useState(true);
	// 数据加载时显示的内容
	const [loadingMsg, setLoadingMsg] = useState('');

	// 页码
	const [alarmDetailsPageIndex, setAlarmDetailsPageIndex] = useState(1);

	// 拉取评估项列表数据
	const getAlarmDetails = async () => {
		setIsLoading(true);
		setLoadingMsg('数据加载中...');
		setAlarmDetails([]);
		try {
			let tmpAppId = 0;
			if (AppId === '') {
				tmpAppId = 0;
			} else {
				tmpAppId = parseInt(AppId);
			}

			const res = await describeAlarmResult({
				AppId: tmpAppId,
				Receiver: receiver,
				Product: product,
				StartTime: startTime,
				EndTime: endTime,
			});
			if (res.Error) {
				// 判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: res.Error.Message });
				}
				setLoadingMsg(res.Error.Message);
				return;
			}
			// console.log(res);
			if (res.AlarmResults) {
				setAlarmDetails(res.AlarmResults);
			} else {
				setAlarmDetails([]);
			}

			setIsLoading(false);
		} catch (err) {
			const message = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setLoadingMsg(message);
		}
	};

	useEffect(() => {
		getAlarmDetails();
		setAlarmDetailsPageIndex(1);
	}, [queryFlag]);

	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission />
			: <Content className="intlc-survey-content intlc-stack-fullscreen intlc-stack-has-min-width">
				<Content.Header title="告警查询"></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body
							title={
								<>
									<div>
										<span style={{ fontSize: 20 }}>告警查询条件</span>
										<hr />
										<Form style={{ marginTop: 20 }} layout={'inline'}>
											<Form.Item label="AppID：">
												<Input
													size='m'
													value={AppId}
													onChange={(value, context) => {
														setAppId(value);
													}}
													placeholder="请输入AppID"
												/>
											</Form.Item>
											<Form.Item label="接收人：">
												<Input
													size='m'
													value={receiver}
													onChange={(value, context) => {
														setReceiver(value);
													}}
													placeholder="请输入接收人英文名"
												/>
											</Form.Item>
											<Form.Item label="时间范围：">
												<RangePicker
													defaultValue={[moment(startTime), moment(endTime)]}
													range={[
														moment()
															.subtract(7, 'd')
															.startOf('d'),
														moment().endOf('d'),
													]}
													onChange={(value) => {
														/*
														var startTimeStamp = new Date(Date.parse(value[0].format("YYYY-MM-DD").replace(/-/g, "/"))).getTime();
														var endTimeStamp = new Date(Date.parse(value[1].format("YYYY-MM-DD").replace(/-/g, "/"))).getTime();
														var dates = Math.abs((startTimeStamp - endTimeStamp)) / (1000 * 60 * 60 * 24);
														if (dates > 30) {
															// console.log("error");
															initDate();
														} else {
															setStartTime(value[0].format("YYYY-MM-DD"));
															setEndTime(value[1].format("YYYY-MM-DD"));
														}*/
														// console.log(startTime, endTime)
														setStartTime(value[0].format('YYYY-MM-DD'));
														setEndTime(value[1].format('YYYY-MM-DD'));
													}}
												// onOpenChange={open => console.log(open ? "open" : "close")}
												/>
											</Form.Item>
											<Button
												type="primary"
												onClick={(e) => {
													setQueryFlag(!queryFlag);
												}}
											>
												查询
											</Button>
										</Form>
									</div>
								</>
							}
						>
						</Card.Body>
					</Card>

					{isLoading ? (<div><br /><Card><Card.Body title={loadingMsg}></Card.Body></Card></div>) : (
						<div>
							<br />
							<Card>
								<Card.Body title={'告警明细'}>
									<Table
										records={alarmDetails}
										columns={[
											{
												key: 'AppId',
												header: 'AppId',
											},
											{
												key: 'CustomerName',
												header: '客户名称',
											},
											{
												key: 'Product',
												header: '产品',
											},
											{
												key: 'AlarmObj',
												header: '告警对象',
											},
											{
												key: 'Region',
												header: '地域',
											},
											{
												key: 'AlarmContent',
												header: '告警信息',
											},
											{
												key: 'OccurTime',
												header: '首次告警时间',
											},
											{
												key: 'UpdateTime',
												header: '最新告警时间',
											},
											{
												key: 'RecoveryTime',
												header: '恢复时间',
											},
											{
												key: 'Receiver',
												header: '接收人',
											},
											/*
											{
												key: 'MediumRiskCount',
												header: () => (
													<p>
														<span style={{ color: '#ff9d00' }}>{'中风险'}</span>
													</p>
												),
											},
											{
												key: 'Count',
												header: '资源数',
											},
											{
												key: 'RiskRate',
												header: () => (
													<>
														{'风险率'}
														<Bubble content={'风险率=当前风险数/(资源数*已开启资源型巡检项)'}>
															<Icon type="help" />
														</Bubble>
													</>
												),
												render: (x) => {
													return (x.RiskRate * 100).toString().match(/^\d+(?:\.\d{0,2})?/) + '%';
												},
											},
											{
												key: 'StrategyCount',
												header: '已开启评估策略数',
											},*/
										]}
										addons={[
											autotip({
												emptyText: '没有数据',
											}),
											pageable({
												pageIndex: alarmDetailsPageIndex,
												onPagingChange: (pagingQuery) => {
													setAlarmDetailsPageIndex(pagingQuery.pageIndex);
												},
												pageSize: 100,
											}),
										]}
									></Table>
								</Card.Body>
							</Card>
						</div>)
					}
				</Content.Body>
			</Content>}</div>}
		</Body>
	);
}
