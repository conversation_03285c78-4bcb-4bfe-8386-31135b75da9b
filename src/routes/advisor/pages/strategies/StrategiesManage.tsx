import React, { useState, useEffect, useContext, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Layout } from '@tea/component/layout';
import { Table, Card, Justify, Button, SearchBox, Modal, message as tips, message, Form, SelectMultiple } from '@tencent/tea-component';
import { Strategy } from '@src/types/advisor/strategies';
import { getStrategiesConfig, deleteStrategiesConfig } from '@src/api/advisor/strategies';
import { describeReportTemplate, describeReportTemplateList, getProductsGroups } from '@src/api/advisor/estimate';
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';
import { current } from '@tencent/tea-app/lib/bridge/user/current';
import { reportVisitPage } from '@src/utils/report';
const { Body, Content } = Layout;
const { autotip, pageable, scrollable, filterable } = Table.addons;

export function StrategiesManage({  }) {
	const history = useHistory();
	const path = location.pathname;
	if (path.indexOf('strategies-manage-origin') !== -1) {
		history.push('/advisor/strategies-manage');
	}
	// 筛选项全选值
	const ALL_VALUE = "__ALL__";

	// 表格数据
	const [strategyList, setStrategyList] = useState<Array<Strategy>>([]);
	// 输入框筛选之后的表格数据
	const [filterStrategyList, setFilterStrategyList] = useState<Array<Strategy>>([]);

	// 类别名映射字典
	const [groupDict, setGroupDict] = useState({});
	// 产品名映射字典
	const [productDict, setProductDict] = useState({});

	// 当前模板
	const [currentTemplate, setCurrentTemplate] = useState('0')
	// 系统模板列表--下拉框
	const [systemTemplateOptions, setSystemTemplateOptions] = useState([])
	// 系统模板对应策略
	const [templateStrategyIds, setTemplateStrategyIds] = useState([])
	// 类别已筛选值
	const [choosedGroups, setChoosedGroups] = useState<Array<string>>([]);
	// 自定义阈值已筛选值
	const [choosedThreshold, setChoosedThreshold] = useState(ALL_VALUE);
	// 产品名已筛选值
	const [choosedProducts, setChoosedProducts] = useState<Array<string>>([]);
	// 内外部巡检项已筛选值
	const [choosedEnv, setChoosedEnv] = useState<Array<string>>([]);

	// 类别下拉框选项
	const [groupsOptions, setGroupsOptions] = useState([]);
	// 产品名下拉框选项
	const [productsOptions, setProductsOptions] = useState([]);
	// 内外部巡检项下拉框选项
	const [envOptions, setEnvOptions] = useState([]);

	// online 筛选值
	const [online, setOnline] = useState<Array<string>>([]);
	// 输入框筛选值
	const [searchInput, setSearchInput] = useState('');
	// 加载条
	const [loading, setLoading] = useState(false);
	// 加载错误
	const [error, setError] = useState(false);
	// 页码
	const [pageIndex, setPageIndex] = useState(1);
	// 模态框
	const [visiable, setVisiable] = useState(false);
	//页面权限状态
	const history1 = useHistory();
	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history1.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	// 获取筛选后的数据条目
	let filteredRecords: Array<Strategy> = filterStrategyList.slice();
	if (choosedGroups.length > 0) {
		filteredRecords = filteredRecords.filter((record) => choosedGroups.indexOf(record.groupId.toString()) > -1);
	}
	if (choosedThreshold !== ALL_VALUE) {
		filteredRecords = filteredRecords.filter(record => String(record.isSupportCustom || false) === choosedThreshold);
	}
	if (choosedProducts.length > 0) {
		filteredRecords = filteredRecords.filter((record) => choosedProducts.indexOf(record.product) > -1);
	}
	if (choosedEnv.length > 0) {
		filteredRecords = filteredRecords.filter((record) => choosedEnv.indexOf(record.env) > -1);
	}

	// 输入框值变化时过滤表数据
	useEffect(() => {
		setFilterStrategyList(
			strategyList.filter(
				(strategy) =>
					strategy.name.toLowerCase().indexOf(searchInput.toLowerCase()) > -1 ||
					strategy.strategyId.toString().indexOf(searchInput.toLowerCase()) > -1
			)
		);
	}, [searchInput]);

	// 模板策略改变，表数据改变
	useEffect(() => {
		setFilterStrategyList(
			strategyList.filter(
				(strategy) => templateStrategyIds.includes(strategy.strategyId)
			)
		);
	}, [templateStrategyIds]);

	// 模板选中，模板策略改变
	// useEffect(() => {
	// 	if (currentTemplate) {
	// 		getTemplateStrategyIds(currentTemplate)
	// 	}
	// }, [currentTemplate])

	useEffect(() => {
		getProductsGroupsInfo();
		getSystemReportTemplateList();
		reportVisitPage({
			isaReportMeunName: '策略管理',
		});
	}, []);

	useEffect(() => {
		getStrategies();
	}, [productDict]);

	// 获取系统模板列表信息
	const getSystemReportTemplateList = async () => {
		try {
			const res = await describeReportTemplateList({
				AppId: 1253985742, //入参APPID在拉取系统模板时候没用上，任意值返回结果相同
				Type: "system"
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				let tmp = []
				if (res.TemplateList) {
					res.TemplateList.map((item) => {
						tmp.push({ value: item.Id, text: item.Name })
					})
				}
				setSystemTemplateOptions(tmp)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: 1253985742,
				Env: 'all',
			})
			if (res.Error) {
				let msg = res.Error.Message

				tips.error({ content: msg });
				return
			} else {
				// 获取id-名称映射表
				setProductDict(res.ProductDict)
				setGroupDict(res.GroupDict)

				let tmpProductsOptions = []
				for (var i in res.ProductDict) {
					tmpProductsOptions.push({ value: i, text: res.ProductDict[i] })
				}
				setProductsOptions(tmpProductsOptions)// 产品下拉框

				let tmpGroupsOptions = []
				for (var i in res.GroupDict) {
					tmpGroupsOptions.push({ value: i.toString(), text: res.GroupDict[i] })
				}
				setGroupsOptions(tmpGroupsOptions)// 维度下拉框
				// 内外部巡检项下拉框
				setEnvOptions([
					{ value: 'public', text: '外部巡检项' },
					{ value: 'private', text: '内部巡检项' }
				])
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	//拉取评估项列表数据
	const getStrategies = async () => {
		setSearchInput('');
		setLoading(true);
		setError(false);
		setStrategyList([]);
		setFilterStrategyList([]);
		try {
			const res = await getStrategiesConfig({
				Online: 1,
				Env: 'all',
			});
			if (res.Error) {
				setError(true);
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: res.Error.Message });
				}
				return;
			}
			const _strategyList: Array<Strategy> = res.StrategyConfigList.map((strategy) => ({
				strategyId: strategy.StrategyId,
				name: strategy.Name,
				groupId: strategy.GroupId,
				product: strategy.Product,
				desc: strategy.Desc,
				online: strategy.Online,
				env: strategy.Env,
				isSupportCustom: strategy.IsSupportCustom
			}));

			sortStragegyList(_strategyList);
			setStrategyList(_strategyList);
			setFilterStrategyList(_strategyList);
		} catch (err) {
			const message = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setError(true);
		}
		setLoading(false);
	};

	//拉取指定策略ID的评估项列表数据
	const getTemplateStrategyIds = async (currentTemplate) => {
		try {
			const res = await describeReportTemplate({
				AppId: 1253985742,  // 公共参数
				TemplateId: parseInt(currentTemplate),
				Type: "system",
			})
			if (res.Error) {
				let msg = res.Error.Message || "未知错误"
				message.error({ content: msg });
				return
			} else {
				setTemplateStrategyIds(res.StrategyIDs || [])
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	};

	//排序策略列表，排序方式：类别(依次为安全，可靠，服务限制，成本，性能) -> 产品名称(字母顺序，忽略大小写) -> 策略ID（由小到大）
	const sortStragegyList = (strategyList: Array<Strategy>): void => {
		/* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */
		strategyList.sort((prev, curr) => {
			if (prev.groupId - curr.groupId !== 0) {
				return prev.groupId - curr.groupId;
			} else if (prev.product !== curr.product) {
				return prev.product.toLowerCase() < curr.product.toLowerCase() ? -1 : 1;
			} else {
				return prev.strategyId - curr.strategyId;
			}
		});
	};

	//删除策略
	const handleDelete = async (strategyId: number) => {
		try {
			const res = await deleteStrategiesConfig({
				StrategyID: strategyId,
			});
			if (res.Error) {
				const message: string = res.Error.Message;
				tips.error({ content: message });
				return;
			}
			if (res.result) {
				tips.success({ content: '删除成功' });
				getStrategies();
			} else {
				tips.success({ content: '删除失败' });
			}
		} catch (err) {
			const message = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setError(true);
		}
	};

	const askQuestion = async (id) => {
		const yes = await Modal.confirm({
			message: '确认删除此策略,操作将被记录？',
			okText: '删除',
			cancelText: '取消',
		});
		if (yes) {
			handleDelete(id);
		}
	};

	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
			<Content>
				<Content.Header title="策略列表"></Content.Header>
				<Content.Body>
					<Table.ActionPanel>
						<Justify
							/*
							left={
								<>
									<Button
										type="primary"
										onClick={() => {
											history.push('/advisor/strategies-manage/new');
										}}
									>
										新增
									</Button>
								</>
							} */
							right={
								<>
									<SelectMultiple
										style={{ width: '300px', marginRight: 10 }}
										searchable
										clearable={true}
										appearance="button"
										options={productsOptions}
										value={choosedProducts}
										placeholder="按照云产品进行过滤"
										onChange={(v) => {
											setChoosedProducts(v);
											setPageIndex(1);
										}}
									/>
									{/* <i className="tea-divider tea-divider--space-2n" /> */}
									<SearchBox
										onSearch={(value) => {
											setSearchInput(value);
											setPageIndex(1);
										}}
										onClear={() => {
											setSearchInput('');
											setPageIndex(1);
										}}
										placeholder={'按照策略名称或策略 ID 进行过滤'}
										style={{ width: '300px' }}
										maxLength={60}
									/>
									<Button
										icon="refresh"
										onClick={() => {
											getStrategies();
											setCurrentTemplate("")
										}}
									/>
								</>
							}
						/>
					</Table.ActionPanel>
					<Card>
						<Table
							records={filteredRecords}
							columns={[
								{
									key: 'strategyId',
									header: '策略ID',
									width: 100,
								},
								{
									key: 'group',
									header: '分组',
									width: 100,
									render: (record) => <p>{groupDict[record.groupId]}</p>,
								},
								{
									key: 'product',
									header: '云产品',
									width: 200,
									render: (record) => <p>{productDict[record.product]}</p>,
								},
								{
									key: 'name',
									header: '策略名称',
									width: 360
								},
								{
									key: 'threshold',
									header: '支持自定义阈值',
									width: 130,
									render: (record) => <p>{record.isSupportCustom === true ? '是' : '否'}</p>,
								},
								{
									key: 'desc',
									header: '策略描述',
								},

								{
									key: 'statistics',
									header: '运营统计',
									width: 100,
									render: (record: any) =>
										<Button type="link" onClick={() => {
											history.push(`/advisor/strategies-manage/operational-detail/${record.strategyId}`);
										}}>
											详情
										</Button>
								},/*
								{
									key: 'online',
									header: '上线状态',
									width: 100,
									render: (record) => <p>{record.online ? '已上线' : '未上线'}</p>,
								},
								{
									key: 'operation',
									header: '操作',
									render: (record) => (
										<>
											<Link to={`/advisor/strategies-manage/edit/${record.strategyId}`}>
												编辑
											</Link>
											<Button
												style={{ marginLeft: 10 }}
												type="link"
												onClick={() => {
													askQuestion(record.strategyId);
												}}
											>
												删除
											</Button>
										</>
									),
								},*/
							]}
							addons={[
								autotip({
									isLoading: loading,
									isError: error,
								}),
								pageable({
									pageIndex,
									onPagingChange: (pagingQuery) => {
										setPageIndex(pagingQuery.pageIndex);
									},
								}),
								// scrollable({ maxHeight: 480 }),
								filterable({
									type: 'multiple',
									column: 'group',
									value: choosedGroups,
									onChange: (value) => {
										// setGroups(value.filter((x) => x !== 'all'));
										setChoosedGroups(value);
										console.log(choosedGroups);
										setPageIndex(1);
									},
									all: {
										value: ALL_VALUE,
										text: '全部',
									},
									options: groupsOptions,
								}),
								
								filterable({
									type: 'single',
									column: 'threshold',
									value: choosedThreshold,
									onChange: (value) => {
										setChoosedThreshold(value);
										setPageIndex(1);
									},
									all: {
										value: ALL_VALUE,
										text: '全部',
									},
									options: [{ value: 'true', text: '是' }, { value: 'false', text: '否' }],
								}),
								filterable({
									type: 'multiple',
									column: 'env',
									value: choosedEnv,
									onChange: (value) => {
										setChoosedEnv(value);
										setPageIndex(1);
									},
									all: {
										value: ALL_VALUE,
										text: '全部',
									},
									options: envOptions,
								}),
								// filterable({
								// 	type: 'multiple',
								// 	column: 'online',
								// 	value: online,
								// 	onChange: (value) => {
								// 		debugger;
								// 		setOnline(value.filter((x) => x !== 'all'));
								// 		setPageIndex(1);
								// 	},
								// 	all: {
								// 		value: 'all',
								// 		text: 'All',
								// 	},
								// 	options:
								// 		ONLINE_OPTS.map((item) => ({
								// 			value: item,
								// 			text: item,
								// 		})) || [],
								// }),
								// scrollable({
								// 	maxHeight: 491,
								// }),
							]}
						/>
					</Card>
				</Content.Body>
			</Content>}</div>}
		</Body>
	);
}
