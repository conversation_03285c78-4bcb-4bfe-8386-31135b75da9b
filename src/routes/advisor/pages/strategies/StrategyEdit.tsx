import React, { useContext, useEffect, useState } from "react";
import {
	<PERSON>readcrumb,
	Card,
	Layout,
	Button,
	Form,
	Input,
	Select,
	Switch,
	message as tips,
} from "@tea/component";
import { Link } from "react-router-dom";
import { PRODUCT_MAP, GROUP_MAP, LEVEL_MAP } from "@src/utils/map";
import {
	getStrategiesConfig,
	createStrategiesConfig,
	modifyStrategiesConfig,
} from "@src/api/advisor/strategies";
import { useForm, useField } from "react-final-form-hooks";
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';
const { Body, Content } = Layout;

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}

// 分组信息的下拉选项
const groupOptions = Array.from(GROUP_MAP.entries()).map((item) => ({
	value: item[0].toString(),
	text: item[1],
}));
// 产品信息的下拉选项
const productOptions = Array.from(PRODUCT_MAP.entries()).map((item) => ({
	value: item[0],
	text: item[1],
}));
// 风险等级下拉选项
const levelOptions = Array.from(LEVEL_MAP.entries()).map((item) => ({
	value: item[0].toString(),
	text: item[1],
}));
// 初始表单
const initFormData = {
	strategyId: "",
	group: "",
	product: "",
	name: "",
	desc: "",
	repair: "",
	notice: "",
	ignore: "",
	online: false,
};

export function StrategyEdit({ match, location, history }) {
	const [conditions, setConditions] = useState([
		{
			id: +new Date(),
			desc: "",
			level: 0,
			isNew: true,
			operation: "add",
		},
	]);
	//页面权限状态
	const history1 = useHistory();
	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history1.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	useEffect(() => {
		initData();
	}, []);

	const initData = async () => {
		// 没有sid时是新增
		if (!match.params.sid) {
			// 设置form值
			for (let key in initFormData) {
				form.change(key, initFormData[key]);
			}
			return;
		}
		try {
			const result = await getStrategiesConfig({
				StrategyID: parseInt(match.params.sid),
			});
			if (result.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (result.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: result.Error.Message });
				}
				return;
			}
			const strategy =
				Array.isArray(result.StrategyConfigList) &&
				result.StrategyConfigList[0];
			if (strategy) {
				const formData = {
					strategyId: strategy.StrategyId,
					group: strategy.GroupId.toString(),
					product: strategy.Product,
					name: strategy.Name,
					desc: strategy.Desc,
					repair: strategy.Repair,
					notice: strategy.Notice,
					ignore: strategy.Ignore,
					online: strategy.Online,
				};
				// 设置form值
				for (let key in formData) {
					form.change(key, formData[key]);
				}
				// 设置condition
				strategy.Conditions.length > 0 &&
					setConditions(
						strategy.Conditions.map((condition) => ({
							id: condition.ConditionId,
							desc: condition.Desc,
							level: condition.Level,
							isNew: false,
							operation: "update",
						}))
					);
			}
		} catch (err) {
			let message: string = err.msg || err.toString() || "unknown error";
			tips.error({ content: message });
		}
	};
	const onSubmit = async (values) => {
		try {
			const data = {
				GroupId: parseInt(values.group),
				Product: values.product,
				Name: values.name,
				Desc: values.desc,
				Online: values.online,
				Repair: values.repair,
				Notice: values.notice,
				Ignore: values.ignore,
				Env: values.env, // 接口新增 env 字段
				Conditions: conditions.map((condition) => {
					return condition.isNew
						? {
							Level: condition.level,
							Desc: condition.desc,
							IsNew: true,
							Operation: condition.operation,
						}
						: {
							ConditionId: condition.id,
							Level: condition.level,
							Desc: condition.desc,
							IsNew: false,
							Operation: condition.operation,
						};
				}),
			};
			// 没有match.params.sid时是新增
			if (match.params.sid) {
				const res = await modifyStrategiesConfig({
					Config: {
						...data,
						StrategyId: parseInt(match.params.sid),
					},
				});
				if (res.Error) {
					const message = res.Error.Message;
					tips.error({ content: message });
					return;
				}
				if (res.result) {
					tips.success({ content: `修改成功` });
					history.push({ pathname: "/advisor/strategies-manage" });
				} else {
					tips.error({ content: "修改失败，请重试" });
				}
			} else {
				const res = await createStrategiesConfig({
					StrategyConfigList: [data],
				});
				if (res.Error) {
					const message = res.Error.Message;
					tips.error({ content: message });
					return;
				}
				if (res.result) {
					tips.success({ content: `新增成功` });
					history.push({ pathname: "/advisor/strategies-manage" });
				} else {
					tips.error({ content: "新增失败，请重试" });
				}
			}
		} catch (err) {
			let message: string = err.msg || err.toString() || "unknown error";
			tips.error({ content: message });
		}
	};

	const { form, handleSubmit, validating, submitting } = useForm({
		onSubmit,
		/**
		 * 默认为 shallowEqual
		 * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
		 * useEffect(() => form.initialize({ }), []);
		 */
		initialValuesEqual: () => true,
		validate: ({
			group,
			product,
			name,
			desc,
			repair,
			notice,
			ignore,
			online,
		}) => ({
			group:
				typeof group === "string" && !group.trim()
					? "请选择分组"
					: undefined,
			product:
				typeof product === "string" && !product.trim()
					? "请选择所属云产品"
					: undefined,
			name:
				typeof name === "string" && !name.trim()
					? "请输入策略名称"
					: undefined,
			desc:
				typeof desc === "string" && !desc.trim()
					? "请输入策略描述"
					: undefined,
			repair:
				typeof repair === "string" && !repair.trim()
					? "请输入策略建议"
					: undefined,
			notice:
				typeof notice === "string" && !notice.trim()
					? "请输入提示信息"
					: undefined,
			ignore:
				typeof ignore === "string" && !ignore.trim()
					? "请输入忽略信息"
					: undefined,
		}),
	});

	const group = useField("group", form);
	const product = useField("product", form);
	const name = useField("name", form);
	const desc = useField("desc", form);
	const repair = useField("repair", form);
	const notice = useField("notice", form);
	const ignore = useField("ignore", form);
	const online = useField("online", form);

	// 增加风险项
	const handleAddCondition = () => {
		setConditions((conditions) => {
			return conditions.concat({
				id: +new Date(),
				level: 0,
				desc: "",
				isNew: true,
				operation: "add",
			});
		});
	};

	// 删除风险项
	const handleDeleteCondition = (id) => {
		setConditions((conditions) => {
			let index = conditions.findIndex(
				(condition) => condition.id === id
			);
			conditions[index] = {
				...conditions[index],
				operation: "delete",
			};
			const filterConditions = conditions.filter((condition) => {
				return condition.operation === "delete" ? false : true;
			});
			filterConditions.length === 0 &&
				conditions.push({
					id: +new Date(),
					desc: "",
					level: 0,
					isNew: true,
					operation: "add",
				});
			return conditions.slice();
		});
	};

	// 修改风险项 type 0:level, 1:desc
	const handleChangeCondition = (id, newValue, type) => {
		setConditions((conditions) => {
			let condition = conditions.find((condition) => condition.id === id);
			switch (type) {
				case 0:
					condition.level = parseInt(newValue);
					break;
				case 1:
					condition.desc = newValue;
					break;
				default:
					throw new Error("参数错误");
			}
			return conditions.slice();
		});
	};

	return (
		<Body>{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
			<Content className="intlc-stack-content">
				<Content.Header
					title={
						<Breadcrumb>
							<Breadcrumb.Item>
								<Link
									to={{
										pathname: "/advisor/strategies-manage",
									}}
								>
									策略管理
								</Link>
							</Breadcrumb.Item>
							{match.params.sid ? (
								<>
									<Breadcrumb.Item>{`策略${match.params.sid}`}</Breadcrumb.Item>
									<Breadcrumb.Item>编辑</Breadcrumb.Item>
								</>
							) : (
								<Breadcrumb.Item>新增</Breadcrumb.Item>
							)}
						</Breadcrumb>
					}
				/>
				<Content.Body>
					<form onSubmit={handleSubmit}>
						<Button
							type="primary"
							style={{ marginBottom: 10 }}
							loading={submitting}
							disabled={validating}
						>
							保存
						</Button>
						<Card>
							<Card.Body>
								<Form>
									{match.params.sid && (
										<Form.Item label="策略ID">
											<Input
												size="m"
												value={match.params.sid}
												disabled
											/>
										</Form.Item>
									)}
									<Form.Item
										status={getStatus(
											group.meta,
											validating
										)}
										label="分组"
									>
										<Select
											type="simulate"
											appearance="button"
											options={groupOptions}
											{...group.input}
										/>
									</Form.Item>
									<Form.Item
										status={getStatus(
											product.meta,
											validating
										)}
										label="所属云产品"
									>
										<Select
											type="simulate"
											appearance="button"
											options={productOptions}
											{...product.input}
										/>
									</Form.Item>
									<Form.Item
										status={getStatus(
											name.meta,
											validating
										)}
										label="策略名称"
									>
										<Input
											size="full"
											placeholder="输入策略名称"
											{...name.input}
										/>
									</Form.Item>
									<Form.Item
										status={getStatus(
											desc.meta,
											validating
										)}
										label="策略描述"
									>
										<Input
											size="full"
											placeholder="输入策略描述"
											{...desc.input}
										/>
									</Form.Item>
									<Form.Item
										status={getStatus(
											repair.meta,
											validating
										)}
										label="策略建议"
									>
										<Input
											size="full"
											placeholder="输入策略建议"
											{...repair.input}
										/>
									</Form.Item>
									<Form.Item
										status={getStatus(
											notice.meta,
											validating
										)}
										label="提示信息"
									>
										<Input
											size="full"
											placeholder="输入提示信息"
											{...notice.input}
										/>
									</Form.Item>
									<Form.Item
										status={getStatus(
											ignore.meta,
											validating
										)}
										label="忽略信息"
									>
										<Input
											size="full"
											placeholder="输入忽略信息"
											{...ignore.input}
										/>
									</Form.Item>
									{conditions
										.filter((condition) => {
											return condition.operation ===
												"delete"
												? false
												: true;
										})
										.map((condition, index) => (
											<Form.Item
												label={`风险项${index + 1}`}
												key={condition.id}
												className="condition-form-item"
											>
												<div className="condition">
													<Select
														type="simulate"
														appearance="button"
														options={levelOptions}
														value={condition.level.toString()}
														onChange={(level) =>
															handleChangeCondition(
																condition.id,
																level,
																0
															)
														}
													/>
													<Input
														size="full"
														placeholder="输入风险项描述"
														value={condition.desc}
														onChange={(desc) =>
															handleChangeCondition(
																condition.id,
																desc,
																1
															)
														}
													/>
													<Button
														htmlType="button"
														onClick={
															handleAddCondition
														}
													>
														新增
													</Button>
													<Button
														htmlType="button"
														onClick={() =>
															handleDeleteCondition(
																condition.id
															)
														}
													>
														删除
													</Button>
												</div>
											</Form.Item>
										))}
									<Form.Item
										status={getStatus(
											online.meta,
											validating
										)}
										label="上线开关"
									>
										<Switch {...online.input} />
									</Form.Item>
								</Form>
							</Card.Body>
						</Card>
					</form>
				</Content.Body>
			</Content>}</div>}
		</Body>
	);
}
