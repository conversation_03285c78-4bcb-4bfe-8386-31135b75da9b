import React, { useContext, useEffect, useState } from "react";
import { Layout, Card, message } from "@tea/component";
const { Content } = Layout;
import './style.less'
import { BaseInfo } from './components/BaseInfo'
import { RiskTrends } from './components/RiskTrends'
import { CustomerList } from './components/CustomerList'
import { RiskGovernance } from "./components/RiskGovernance";
import { DescribeStrategyOperationInfo, DescribeRiskManageInfo } from '@src/api/advisor/strategies';
export function OperationalDetail({ history, match }) {
  // 策略ID
  const StrategyId = Number(match.params.strategyId) || ''
  // 风险项信息和风险项客户分布
  const [strategyOperationInfo, setStrategyOperationInfo] = useState({})
  // 风险治理运营情况
  const [riskManageInfo, setRiskManageInfo] = useState({})
  // 查询风险项信息和风险项客户分布
  const getDescribeStrategyOperationInfo = async () => {
    try {
      const res = await DescribeStrategyOperationInfo({ StrategyId })
      if (res.Error) {
        let msg = res.Error.Message
        message.error({ content: msg });
        return
      } else {
        setStrategyOperationInfo(res)
      }
    } catch (err) {
      const msg = err.msg || err.toString() || "未知错误"
      message.error({ content: msg });
    }
  }

  // 查询风险项风险治理信息
  const getDescribeRiskManageInfo = async () => {
    try {
      const res = await DescribeRiskManageInfo({ StrategyId })
      if (res.Error) {
        let msg = res.Error.Message
        message.error({ content: msg });
        return
      } else {
        setRiskManageInfo(res)
      }
    } catch (err) {
      const msg = err.msg || err.toString() || "未知错误"
      message.error({ content: msg });
    }
  }

  useEffect(() => {
    getDescribeStrategyOperationInfo()
    getDescribeRiskManageInfo()
  }, [])

  return (
    <Content className="detailWrap">
      <Content.Header title='运营统计详情' className="headerWrap" showBackButton onBackButtonClick={() => { history.push('/advisor/strategies-manage/') }} />
      <Content.Body className="bodyWrap" full>
        <Card>
          <Card.Body>
            <BaseInfo data={strategyOperationInfo}></BaseInfo>
          </Card.Body>
        </Card>
        <Card>
          <Card.Body title="风险项变化趋势">
            <RiskTrends StrategyId={StrategyId}></RiskTrends>
          </Card.Body>
        </Card>
        <Card>
          <Card.Body title="存在风险的客户列表">
            <CustomerList StrategyId={StrategyId}></CustomerList>
          </Card.Body>
        </Card>
        <Card>
          <Card.Body title="风险治理运营情况">
            <RiskGovernance data={riskManageInfo} StrategyId={StrategyId}></RiskGovernance>
          </Card.Body>
        </Card>
      </Content.Body>
    </Content>
  )
}
