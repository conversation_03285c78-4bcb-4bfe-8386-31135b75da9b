import React, { useEffect, useState } from "react";
import {Table, message, StatusTip } from "@tea/component";
const { scrollable, pageable, autotip } = Table.addons;
import { DescribeCustomerAndRiskList } from '@src/api/advisor/strategies';
export function CommonTable({ searchParams, columnData }) {
  const [total, setTotal] = useState(0);
  const [pageIndex, setPageIndex] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [loading, setLoading] = useState(true);
  const [records, setRecords] = useState([]);

  // 获取表格数据
  async function getData({ pageIndex = 1, pageSize = 10 } = {}) {
    try {
      setLoading(true)
      // 分页查询参数
      const pageParams = {
        Limit: pageSize,
        Offset: (pageIndex - 1) * pageSize,
      }
      // 记录当前页
      setPageIndex(pageIndex)
      setPageSize(pageSize)
      const res = await DescribeCustomerAndRiskList({ ...searchParams, ...pageParams })
      if (res.Error) {
        setRecords([]);
        setLoading(false)
        let msg = res.Error.Message
        message.error({ content: msg });
        return
      } else {
        setRecords(res.CustomerAndRiskList || [])
        setTotal(res.TotalCount)
        setLoading(false)
      }
    } catch (err) {
      setRecords([]);
      setLoading(false)
      const msg = err.msg || err.toString() || "未知错误"
      message.error({ content: msg });
    }
  }

  useEffect(() => {
    if (searchParams.StrategyId) {
      getData({ pageIndex: 1, pageSize })
    }
  }, [searchParams])

  return (
    <Table
      verticalTop
      records={records}
      columns={columnData || []}
      addons={[
        pageable({
          recordCount: total,
          pageIndex: pageIndex,
          onPagingChange: query => getData(query),
        }),
        autotip({
          isLoading: loading,
        }),
        scrollable({ maxHeight: 490 }),
      ]}
      topTip={
        records?.length == 0 && <StatusTip status={'empty'} />
      }
    />
  )
}
