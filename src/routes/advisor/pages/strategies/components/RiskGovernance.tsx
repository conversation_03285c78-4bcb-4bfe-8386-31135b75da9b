import React, { useEffect, useState } from "react";
import { Row, Col, Table, MetricsBoard, Text, StatusTip, DatePicker, Button, message, Status } from "@tea/component";
const { RangePicker } = DatePicker;
const { scrollable } = Table.addons;
import moment from "moment";
import { CommonTable } from "./CommonTable";
import { LineChart } from "./LineChart";
import { DescribeRiskManageTrendInfo } from '@src/api/advisor/strategies';
interface Props {
  data: any
  StrategyId
}
export function RiskGovernance({ data = {}, StrategyId }: Props) {
  const { StrategyRcmdInfo = {}, StrategyRiskStateInfoSet = [], CustomerRejectSet = [], CustomerHoldSet = [] } = data
  // 解决次数
  const resloveData = StrategyRiskStateInfoSet.filter(i => i.State === 2)[0] || {}
  // 拒绝次数
  const rejectData = StrategyRiskStateInfoSet.filter(i => i.State === -3)[0] || {}
  // 挂起次数
  const holdData = StrategyRiskStateInfoSet.filter(i => i.State === -2)[0] || {}
  const [rangeTime, setRangeTime] = useState<any>([moment().subtract(1, 'months'), moment()])
  const [trendData, setTrendData] = useState([])
  // 表格展示列
  const columnData = [
    {
      key: "CustomerName",
      header: "客户名称",
    },
    {
      key: "AppId",
      header: "AppId",
    },
    {
      key: "OperateReason",
      header: "原因",
    },
    {
      key: "OperateTime",
      header: "操作时间",
    }
  ]
  // 查询风险项信息和风险项客户分布
  const getDescribeRiskManageTrendInfo = async () => {
    try {
      const params = {
        StrategyId,
        StartTime: rangeTime[0].format('YYYY-MM-DD'),
        EndTime: rangeTime[1].format('YYYY-MM-DD')
      }
      const res = await DescribeRiskManageTrendInfo(params)
      if (res.Error) {
        let msg = res.Error.Message
        message.error({ content: msg });
        return
      } else {
        let result = [];
        let haveData = res.RiskManageTrendInfos ? res.RiskManageTrendInfos.every(i => i.DataSet && i.DataSet.length) : false;
        if (haveData) {
          (res.RiskManageTrendInfos || []).map(i => {
            let temp = (i.DataSet || []).map(j => {
              return { ...j, Name: i.Name }
            })
            result = [...result, ...temp]
          })
        } else {
          result = null
        }
        setTrendData(result)
      }
    } catch (err) {
      setTrendData(null)
      const msg = err.msg || err.toString() || "未知错误"
      message.error({ content: msg });
    }
  }
  function disabledDate(date, start) {
    // 选择范围在今天之前，且选择跨度不大于一个月
    const isAfterToday = date.isAfter(moment(), "day");
    return !isAfterToday;
  }
  useEffect(() => {
    getDescribeRiskManageTrendInfo()
  }, [StrategyId])

  return (
    <div className="riskGovernance">
      <div>
        <div>
          <div className="intlc-assessment-tabitem__title">风险治理概览</div>
          <Row showSplitLine style={{ marginTop: 20 }}>
            <Col>
              <MetricsBoard
                title="推荐次数"
                value={StrategyRcmdInfo.TotalCount || 0}
                infos={[
                  <Text theme="text">系统推荐{StrategyRcmdInfo.SysRcmdCount || 0}次</Text>,
                  <Text theme="text">客户加入{StrategyRcmdInfo.CusAddCount || 0}次</Text>,
                  <Text theme="text">售后推荐{StrategyRcmdInfo.TamRcmdCount || 0}次</Text>
                ]}
              />
            </Col>
            <Col>
              <MetricsBoard
                title="解决次数"
                value={resloveData.OperationNumber}
                infos={[<Text theme="text">占比{resloveData.Ratio}</Text>]}
              />
            </Col>
            <Col>
              <MetricsBoard
                title="拒绝次数"
                value={rejectData.OperationNumber}
                infos={[<Text theme="text">占比{rejectData.Ratio}</Text>]}
              />
            </Col>
            <Col>
              <MetricsBoard
                title="挂起次数"
                value={holdData.OperationNumber}
                infos={[<Text theme="text">占比{holdData.Ratio}</Text>]}
              />
            </Col>
          </Row>
        </div>
        <div>
          <div className="intlc-assessment-tabitem__title">风险治理趋势</div>
          <div style={{ marginTop: 20 }}>
            <RangePicker
              disabledDate={disabledDate}
              style={{ marginRight: 10 }}
              value={rangeTime}
              onChange={value => setRangeTime(value)}
            />
            <Button type="primary" onClick={() => { getDescribeRiskManageTrendInfo() }}>
              搜索
            </Button>
          </div>
          {
            trendData ?
              <LineChart single={false} data={trendData}></LineChart>
              :
              <Status style={{ margin: '0 auto' }} icon={'blank'} size={'s'} title={"暂无数据"} />
          }


        </div>
      </div>
      <div>
        <div>
          <div className="intlc-assessment-tabitem__title">客户拒绝记录</div>
          <Table
            verticalTop
            records={CustomerRejectSet || []}
            columns={columnData || []}
            addons={[
              scrollable({ maxHeight: 490 }),
            ]}
            topTip={
              (!CustomerRejectSet || CustomerRejectSet?.length == 0) && <StatusTip status={'empty'} />
            }
          ></Table>
          {/* <CommonTable columnData={columnData} searchParams={searchParams}></CommonTable> */}
        </div>
        <div>
          <div className="intlc-assessment-tabitem__title">客户挂起记录</div>
          <Table
            verticalTop
            records={CustomerHoldSet || []}
            columns={columnData || []}
            addons={[
              scrollable({ maxHeight: 490 }),
            ]}
            topTip={
              (!CustomerHoldSet || CustomerHoldSet?.length == 0) && <StatusTip status={'empty'} />
            }
          ></Table>
          {/* <CommonTable></CommonTable> */}
        </div>
      </div>
    </div>
  )
}
