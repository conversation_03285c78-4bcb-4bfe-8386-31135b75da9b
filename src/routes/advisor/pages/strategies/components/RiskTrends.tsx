import React, { useEffect, useState } from "react";
import { Select, DatePicker, Button, message, Status } from "@tea/component";
import moment from "moment";
const { RangePicker } = DatePicker;
import { LineChart } from "./LineChart";
import { DescribeStrategyTrendInfo } from '@src/api/advisor/strategies';
const options = [
  {
    value: "ka",
    text: "KA客户",
  },
  {
    value: "wa",
    text: "腰部客户",
  },
  {
    value: "ma",
    text: "中长尾客户",
  },
];

export function RiskTrends({ StrategyId }) {
  const [customerGrade, setCustomerGrade] = useState('');
  const [rangeTime, setRangeTime] = useState<any>([moment().subtract(1, 'months'), moment()])
  const [trendData, setTrendData] = useState([])
  // 查询风险项信息和风险项客户分布
  const getDescribeStrategyTrendInfo = async () => {
    try {
      const params = {
        StrategyId,
        CustomerGrade: customerGrade,
        StartTime: rangeTime[0].format('YYYY-MM-DD'),
        EndTime: rangeTime[1].format('YYYY-MM-DD')
      }
      const res = await DescribeStrategyTrendInfo(params)
      if (res.Error) {
        let msg = res.Error.Message
        message.error({ content: msg });
        return
      } else {
        setTrendData(res.StrategyInfoSet || [])
      }
    } catch (err) {
      const msg = err.msg || err.toString() || "未知错误"
      message.error({ content: msg });
    }
  }
  function disabledDate(date, start) {
    // 选择范围在今天之前，且选择跨度不大于一个月
    const isAfterToday = date.isAfter(moment(), "day");
    return !isAfterToday;
  }
  useEffect(() => {
    getDescribeStrategyTrendInfo()
  }, [StrategyId])
  return (
    <div>
      <div className="searchWrap">
        <Select
          clearable
          appearance="button"
          options={options}
          value={customerGrade}
          placeholder="请选择客户等级"
          style={{ width: 150 }}
          onChange={value => setCustomerGrade(value)}
        />
        <RangePicker
          style={{ margin: '0 10px' }}
          value={rangeTime}
          disabledDate={disabledDate}
          onChange={value => setRangeTime(value)}
        />
        <Button type="primary" onClick={() => { getDescribeStrategyTrendInfo() }}>
          搜索
        </Button>
      </div>
      <div className="trendsWrap">
        {
          trendData && trendData.length ?
            trendData.map((i, index) => {
              const data = (i.DataSet || []).map(m => {
                return { ...m, Name: i.TypeFlag === 1 ? '客户数' : '资源数' }
              })
              return <LineChart key={index} title={i.Name} data={data} color={i.TypeFlag === 1 ? '#3168f0' : '#eb6455'}></LineChart>
            })
            :
            <Status
              style={{ margin: '0 auto' }}
              icon={'blank'}
              size={'s'}
              title={"暂无数据"}
            />
        }
      </div>
    </div>
  )
}
