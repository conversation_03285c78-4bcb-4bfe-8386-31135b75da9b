import React, { useEffect, useState } from "react";
import { TagSearchBox } from "@tea/component";
import moment from "moment";
import { CommonTable } from "./CommonTable";
export function CustomerList({ StrategyId }) {
  const CustomerGradesOptions = [
    {
      key: "ka",
      name: "KA客户",
    },
    {
      key: "wa",
      name: "腰部客户",
    },
    {
      key: "ma",
      name: "中长尾客户",
    },
  ];
  // 搜索框值
  const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);
  // 构造后的查询参数
  const [searchParams, setSearchParams] = useState({})
  const tagSearchBoxProps: any = {
    minWidth: 500,
    style: {
      width: 500
    },
    attributes: [
      {
        type: "input",
        key: "AppId",
        name: "APPID",
      },
      {
        type: "input",
        key: "CustomerName",
        name: "客户名称",
      },
      {
        type: ['multiple', { searchable: false }],
        key: 'CustomerGrades',
        name: '客户等级',
        values: CustomerGradesOptions,
      },
      {
        type: "input",
        key: "Tam",
        name: "售后负责人",
      },
    ],
    value: tagSelectBoxValue,
    hideHelp: true,
    tips: '支持APPID、客户名称、客户等级、售后负责人过滤，多个关键词用竖线"|"分隔',
    onChange: (tags) => { setTagSelectBoxValue(tags) }
  };
  // 表格展示列
  const columnData = [
    {
      key: "Date",
      header: "日期",
    },
    {
      key: "AppId",
      header: "AppId",
    },
    {
      key: "CustomerName",
      header: "客户名称",
    },
    {
      key: "CustomerGrade",
      header: "客户等级",
    },
    {
      key: "Tam",
      header: "售后负责人",
    },
    {
      key: "RiskManageStatus",
      header: "风险治理状态",
    },
    {
      key: "HighRiskCount",
      header: "高风险实例数",
      width: 100
    },
    {
      key: "MediumRiskCount",
      header: "中风险实例数",
      width: 100
    },
    {
      key: "IgnoredCount",
      header: "已忽略实例数",
      width: 100
    },
  ]
  useEffect(() => {
    let result = {};
    (tagSelectBoxValue || []).forEach(i => {
      if (i.attr.key === 'CustomerGrades') {
        result[i.attr.key] = i.values.map(j => j.key)
      } else {
        result[i.attr.key] = i.attr.key === 'AppId' ? Number(i.values[0].name) : i.values[0].name
      }
    })
    setSearchParams({ ...result, StrategyId })
  }, [tagSelectBoxValue])

  return (
    <div>
      <TagSearchBox {...tagSearchBoxProps} />
      <CommonTable columnData={columnData} searchParams={searchParams}></CommonTable>
    </div>
  )
}
