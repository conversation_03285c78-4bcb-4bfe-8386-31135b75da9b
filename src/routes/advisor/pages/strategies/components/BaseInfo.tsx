import React, { useEffect, useState } from "react";
import { Text, Row, Col } from "@tea/component";
import { BasicPie } from "@tencent/tea-chart/lib/basicpie";
import _ from 'lodash'

// 警告条件主题颜色映射
const conditionThemeConfig = {
  '-1': 'weak',
  0: 'success',
  1: 'warning',
  2: 'warning',
  3: 'danger',
};
// 把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
const simpleMarkdownToHTML = (input: string = ''): string => {
  const reg = new RegExp(
    '(\\[[\u4e00-\u9fa5_a-zA-Z0-9,、/-\\s]+\\])(\\((\\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\\))',
    'g'
  );
  let result = input.replace(reg, (match, text, link) => `<a href="${link.slice(1, -1)}" target="_blank">${text.slice(1, -1)}</a>`);

  // 无序列表markdown语法转换
  if (result.includes('- ')) {
    result = result
      .split('- ')
      .filter(r => r.length)
      .map((r, index) => (!index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`))
      .join('');
  }
  return result;
};

// 风险项客户分布配置
const pieOptions: any = {
  legend: {
    align: 'right'
  },
  dataLabels: {
    enable: true,
    formatter: (value, index, data) => {
      return `${data.serieName}: ${data.value}`;
    },
  }
}
export function BaseInfo({ data }) {
  // 策略信息
  const [strategyInfo, setStrategyInfo] = useState<any>({})
  // 客户分布
  const [pieData, setPieData] = useState(
    [
      { key: 'Ka', type: "KA客户", value: 0 },
      { key: 'Wa', type: "腰部客户", value: 0 },
      { key: 'Ma', type: "中长尾客户", value: 0 },
    ],
  )
  useEffect(() => {
    setStrategyInfo(data.StrategyInfo || {})
    setPieData((oldVal) => {
      let result = _.cloneDeep(oldVal)
      result.forEach(i => {
        i.value = (data.StrategyCustomerDistribution || {})[i.key] || 0
      })
      return result
    })
  }, [data])
  return (
    <div>
      <Row gap={100}>
        <Col span={14}>
          <div className="intlc-assessment-tabitem__title">
            <Text style={{ marginRight: 20 }}>
              {strategyInfo.StrategyName}
            </Text>
            <Text style={{ fontSize: 12, color: '#006cff', marginRight: 12 }}>
              上线日期:{strategyInfo.CreateTime}
            </Text>
            <Text style={{ fontSize: 12, color: '#006cff', marginRight: 12 }}>
              更新日期:{strategyInfo.UpdateTime}
            </Text>
            <Text style={{ fontSize: 12, marginRight: 12 }}>
              分类:{strategyInfo.GroupName}
            </Text>
          </div>
          <div className="intlc-assessment-tabitem__introduction">
            <div className="intlc-assessment-tabitem__instructions">{strategyInfo.Desc}</div>
          </div>

          <div >
            {/* 警告条件 */}
            <div className="intlc-assessment-tabcoll__title">
              警告条件
            </div>
            {(strategyInfo.Conditions || []).sort((c1, c2) => c2.Level - c1.Level).map(condition => (
              <div key={condition.ConditionId} className="intlc-assessment-tabcoll__conditions">
                <Text
                  bgTheme={conditionThemeConfig[condition.Level.toString()]}
                  verticalAlign="top"
                  className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--instructions"
                ></Text>
                <div className="intlc-assessment-tabitem__instructions intlc-assessment-tabitem__instructions--inline">
                  {condition.Desc}
                </div>
              </div>
            ))}

            {/* 优化建议 */}
            <div className="intlc-assessment-tabcoll__title">
              优化建议
            </div>
            <div
              className="intlc-assessment-tabcoll__advice"
              dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(strategyInfo.Repair) }}
            ></div>

          </div>

        </Col>
        <Col span={10}>
          <div className="intlc-assessment-tabitem__title" style={{ marginBottom: 10 }}>风险项客户分布</div>
          <BasicPie
            height={250}
            dataSource={pieData}
            position="value"
            color="type"
            legend={pieOptions.legend}
            dataLabels={pieOptions.dataLabels}
          />
        </Col>
      </Row>
    </div>
  )
}
