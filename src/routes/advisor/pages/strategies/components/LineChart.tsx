import React, { useRef, useEffect, useState } from "react";
import { Select, DatePicker, But<PERSON>, Text, Row, Col } from "@tea/component";
import moment from "moment";
import { BasicLine } from "@tencent/tea-chart/lib/basicline";

export function LineChart({ title = '', data = [], color = '#3168f0', single = true }) {
  const lineOptions = {
    height: 250,
    legend: {
      enable: !single
    },
    color: 'Name',
    theme: single ? { color: [color] } : {}
  }
  return (
    <div className="lineChartWrap">
      <div className="intlc-assessment-tabitem__title">{title || ''}</div>
      <BasicLine
        height={lineOptions.height}
        position="Date*Count"
        dataSource={data || []}
        legend={lineOptions.legend}
        color={lineOptions.color}
        theme={lineOptions.theme}
      />
    </div>
  )
}
