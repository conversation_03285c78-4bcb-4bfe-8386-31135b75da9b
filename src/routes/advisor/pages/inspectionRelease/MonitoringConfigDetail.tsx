import React, { useState, useMemo, useRef, useEffect } from 'react';
import {
	Layout,
	Card,
	H3,
	Form,
	Input,
	Select,
	Button,
	message,
	Switch
} from '@tea/component';
import { useField, useForm } from "react-final-form-hooks";
import { cloneDeep } from "lodash";
import { getStorage, setStorage } from "@src/utils/storage";
import { ModifyMonitorConfigInfo } from "@src/api/advisor/inspectionRelease";
import { useHistory } from '@tea/app';

const productInfo = JSON.parse(getStorage('productInfo')) || {list: []};

const formInfoList: any = [
	{
		name: 'TaskType',
		label: '巡检类型',
		placeholder: '请输入巡检类型',
		required: false,
		errorMessage: '请输入巡检类型',
		type: 'input'
	},
	{
		name: 'Product',
		label: '产品',
		placeholder: '请选择产品',
		required: true,
		errorMessage: '请选择产品',
		type: 'select',
		searchable: true,
		options: [
			...productInfo?.list
		]
	},
	{
		name: 'InsType',
		label: '实例类型',
		placeholder: '请输入实例类型',
		required: true,
		errorMessage: '请输入实例类型',
		type: 'input'
	},
	{
		name: 'MetricName',
		label: '指标名称',
		placeholder: '请输入指标名称',
		required: true,
		errorMessage: '请输入指标名称',
		type: 'input'
	},
	{
		name: 'Namespace',
		label: '监控namespace',
		placeholder: '请输入监控namespace',
		required: true,
		errorMessage: '请输入监控namespace',
		type: 'input'
	},
	{
		name: 'Period',
		label: '获取时间间隔（s）',
		placeholder: '请输入获取时间间隔（s）',
		required: true,
		errorMessage: '请输入获取时间间隔（s）',
		type: 'input'
	},
	{
		name: 'Percents',
		label: '分位值',
		placeholder: '请输入分位值',
		required: true,
		errorMessage: '请输入分位值',
		type: 'input'
	},
	{
		name: 'Days',
		label: '获取天数',
		placeholder: '请输入获取天数',
		required: true,
		errorMessage: '请输入获取天数',
		type: 'input'
	},
	{
		name: 'DimensionName',
		label: '维度名称',
		placeholder: '请输入维度名称',
		required: true,
		errorMessage: '请输入维度名称',
		type: 'input'
	},
	{
		name: 'Online',
		label: '是否启用',
		placeholder: '请勾选是否启用',
		required: true,
		errorMessage: '请勾选是否启用',
		type: 'switch'
	},
	{
		name: 'UseInsIdAsDimension',
		label: '是否使用实例ID作为监控维度',
		placeholder: '请勾选使用实例ID作为监控维度',
		required: true,
		errorMessage: '请勾选使用实例ID作为监控维度',
		type: 'switch'
	},
	{
		name: 'ValueType',
		label: '取值类型',
		placeholder: '请填写取值类型',
		required: true,
		errorMessage: '请填写取值类型',
		type: 'input'
	},
	{
		name: 'DayType',
		label: '日期类型',
		placeholder: '请填写日期类型',
		required: true,
		errorMessage: '请填写日期类型',
		type: 'input'
	},
  {
    name: 'Env',
    label: '环境',
    placeholder: '请选择环境',
    required: true,
    errorMessage: '请选择环境',
    type: 'select',
    options: [
      {
        text: 'public',
        value: 'public'
      },
      {
        text: 'private',
        value: 'private'
      }
    ]
  },
  {
    name: 'ViewName',
    label: '视图名称',
    placeholder: '请填写视图名称',
    required: false,
    errorMessage: '请填写视图名称',
    type: 'input'
  },
  {
    name: 'Statistics',
    label: '统计方式',
    placeholder: '请填写统计方式',
    required: false,
    errorMessage: '请填写统计方式',
    type: 'input'
  },
  {
    name: 'AliasMetricName',
    label: '指标别名',
    placeholder: '请填写指标别名',
    required: false,
    errorMessage: '请填写指标别名',
    type: 'input'
  },
];
const originFormData = {};
function getStatus(meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}
export function MonitoringConfigDetail() {
	const path = location.pathname.split('/')[2];
  const currentEnv = path === 'inspection-release-t' ? 1 : path === 'inspection-release-domestic' ? 2 : path === 'inspection-release-pre' ? 5 : 3;
	const history = useHistory();
	const inspectStrategyInfos = JSON.parse(getStorage('inspectInfos'));
	const {Body, Content} = Layout;
	const [formRenderList, setFormRenderList] = useState(cloneDeep(formInfoList));
	// 提交表单
	const onSubmit = async (value) => {
		const params: any = {
			...value,
			Days: Number(value.Days),
			Period: Number(value.Period),
			Online: value.Online ? 1 : 0,
			UseInsIdAsDimension: value.UseInsIdAsDimension ? 1 : 0,
		};
		try {
			const res = await ModifyMonitorConfigInfo({
				MonitorConfigInfo: params,
				ModifyType: Object.keys(inspectStrategyInfos).length > 0 ? 2 : 1,
				Env: currentEnv,
			});
			message.success({
				content: '操作成功'
			});
			setStorage('inspectTabKey', 'monitor');
			history.push(`/advisor/${path}`);
		} catch (err) { };
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {
			...originFormData,
		},
		validate: (formInfo) => {
			const validateFormInfo = {};
			formInfoList.forEach((item) => {
				if ((item.type != 'switch' && item.required)) {
					validateFormInfo[item.name] = formInfo[item.name] !== '' && formInfo[item.name] !== undefined ? undefined : item.errorMessage;
				}
			});
			return validateFormInfo;
		},
	});
	useMemo(()=>{
		if (inspectStrategyInfos && Object.keys(inspectStrategyInfos).length > 0) {
			form.initialize({
				...inspectStrategyInfos
			});
		};
	}, []);
	const formFieldInfo: any = {};
	formRenderList.forEach((el)=>{
		formFieldInfo[el.name] = useField(el.name, form);
	});
	return <Body className={'detail-form-wrap'}>
		<Content.Header showBackButton onBackButtonClick={() => {
			setStorage('inspectTabKey', 'monitor');
			history.push(`/advisor/${path}`);
		}}/>
		<Content.Body>
			<form onSubmit={handleSubmit}>
				<Form
					layout={'inline'}
				>
					<Card>
						<H3>
							监控配置
						</H3>
						<Card.Body>
					{
						formRenderList.map((item, i)=>{
							return item.type == 'input'
								?
								<Form.Item
									key={i}
									required={item.required}
									label={item.label}
									status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
									message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
								>
									<Input {...formFieldInfo[item['name']].input} autoComplete="off" placeholder={item.placeholder} />
								</Form.Item>
								:
								item.type == 'textarea'
									?
									<div key={i}>
										<Form.Item
											required={item.required}
											label={item.label}
											status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
											message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
										>
											<Input.TextArea
												{...formFieldInfo[item['name']].input}
												autoComplete="off"
												placeholder={item.placeholder}
												style={{
													width: '820px',
													height: '30px'
												}}
											/>
										</Form.Item>
									</div>
									:
									item.type == 'select'
										?
										<Form.Item
											key={i}
											required={item.required}
											label={item.label}
											status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
											message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
										>
											<Select
												searchable={item.searchable || false}
												overlayClassName={item.name == 'Template' ? 'tem-sel' : ''}
												matchButtonWidth
												{...formFieldInfo[item['name']].input}
												options={item.options}
												appearance="button"
												size='m'
											/>
										</Form.Item>
										:
										item.type == 'switch'
											?
											<Form.Item
												key={i}
												className={'form-item-switch'}
												required={item.required}
												label={item.label}
												status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
												message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
											>
												<Switch {...formFieldInfo[item['name']].input} />
											</Form.Item>
											:
											<></>;
						})
					}
						</Card.Body>
						<div className='btn-wrap'>
							{
								path === 'inspection-release-t' && <Button htmlType="submit" type={'primary'}>保存</Button>
							}
							<Button htmlType={'button'} onClick={() => {
								setStorage('inspectTabKey', 'monitor');
								history.push(`/advisor/${path}`);
							}}>返回</Button>
						</div>
					</Card>
				</Form>
			</form>
		</Content.Body>
	</Body>;
}