import React, { useState, useEffect } from 'react';
import {
	Layout,
	Tabs,
	TabPanel
} from '@tea/component';
import {StrategyConfigList} from "@src/routes/advisor/pages/inspectionRelease/StrategyConfigList";
import {MonitoringConfiguration} from "@src/routes/advisor/pages/inspectionRelease/MonitoringConfiguration";
import {ProductList} from "@src/routes/advisor/pages/inspectionRelease/ProductList";
import './index.less';
import { useHistory } from '@tea/app';


export function InspectionRelease() {
	const path = location.pathname.split('/')[2];
	const {Body, Content} = Layout;
	const history = useHistory();
	const tabs = [
		{ id: "strategy", label: "策略配置" },
		{ id: "monitor", label: "监控项配置" },
		{ id: "product", label: "产品列表" }
	];
	const [activeId, setActiveId] = useState(tabs[0]['id']);
	useEffect(()=>{
		return ()=>{};
	}, [history.location.pathname]);
	return <Body>
		<Content.Header title={`巡检发布-${path === 'inspection-release-t' ? '测试' : path === 'inspection-release-domestic' ? '国内' : path === 'inspection-release-pre' ? '预发布' : '国际'}`}></Content.Header>
		<Content.Body>
			<Tabs
				ceiling
				activeId={activeId}
				tabs={tabs}
				onActive={
				    (tab: any) => {
                        setActiveId(tab.id);
                    }
				}
			>
				<TabPanel id="strategy">
					<StrategyConfigList />
				</TabPanel>
				<TabPanel id="monitor">
					<MonitoringConfiguration />
				</TabPanel>
				<TabPanel id="product">
					<ProductList />
				</TabPanel>
			</Tabs>
		</Content.Body>
	</Body>;
}