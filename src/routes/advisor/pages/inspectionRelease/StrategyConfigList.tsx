import React, { useState, useMemo, useEffect } from 'react';
import { Card, Button, Table, Icon, StatusTip, Switch, message, Modal, Justify, Input } from '@tea/component';
import { useActivate } from 'react-activation';
import {ComprehensiveSearch} from "@src/components/ComprehensiveSearch";
import { getStorage, setStorage } from "@src/utils/storage";
import { cloneDeep } from "lodash";
import { DescribeStrategies, ModifyStrategyInfo, UpdateConfigToProduction, getDescribeProductList } from "@src/api/advisor/inspectionRelease";
import { useHistory } from '@tea/app';
import { getCurrentEnv, getPath } from "@src/routes/advisor/pages/inspectionRelease/methods";

export function StrategyConfigList() {
	const [path, setPath] = useState(getPath());
	const [currentEnv, setCurrentEnv] = useState(getCurrentEnv());
	const [selectedKeys, setSelectedKeys] = useState([]);
  const [offset, setOffset] = useState(0);
  const [limit, setLimit] = useState(10);
  const [filters, setFilters] = useState([]);
	const [loading, setLoading] = useState(false);
	const [productList, setProductList] = useState([]);
	const [records, setRecords] = useState<any>({
		AdvisorStrategyInfos: []
	});
	const [strategyInfos, setStrategyInfos] = useState([]);
	const history = useHistory();
	const {pageable, selectable} = Table.addons;
	const [publishInfo, setPublishInfo] = useState({
    sourceEnv: 1,
		env: 1,
		id: [],
		text: ''
	});
	const [visible, setVisible] = useState(false);
  const [reason, setReason] = useState('');
  const [reasonModalInfo, setReasonModalInfo] = useState<any>({
    visible: false
  });
  const [clicked, setClicked] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);
	const modifyStrategyInfo = async (isDelete, key, value, id, item)=>{
    if (currentEnv !== 1 && currentEnv !== 5) {
      if (!reason) {
        return;
      }
      setSaveLoading(true);
    }
		try {
			const res = await ModifyStrategyInfo({
				Online: item.Online,
				GuardOnline: item.GuardOnline,
				GuardVolumeOnline: item.GuardVolumeOnline,
				[key]: value,
				StrategyId: id,
				DeleteStatus: isDelete ? 1 : 0,
				Env: currentEnv,
        Reason: reason,
        Type: key,
        Updater: getStorage('engName'),
        ShowError: true,
        OnlyData: true
			});
			message.success({
				content: '操作成功'
			});
      setSaveLoading(false);
      setReasonModalInfo({
        ...reasonModalInfo,
        visible: false
      });
			getTabData();
		} catch (err) {
      setSaveLoading(false);
    };
	};
	const updateConfigToProduction = async (Env, Id, SourceEnv)=>{
		try {
			const res = await UpdateConfigToProduction({
        SourceEnv,
				Env,
				ConfigType: 1,
				Id
			});
			message.success({
				content: '操作成功'
			});
			setSelectedKeys([]);
			setVisible(false);
      getTabData();
		} catch (err) {};
	};
	const columns = [
		{
			key: "StrategyId",
			header: "策略ID"
		},
		{
			key: "StrategyName",
			header: "策略名称",
      render: item => {
        return item.StrategyName ? <div>{item.StrategyName}</div> : '-';
      }
		},
		{
			key: "Env",
			header: "环境"
		},
		{
			key: "Online",
			header: "日常巡检",
			render: item => {
				return <Switch
					value={item.Online === 1}
					onChange={
						(val)=>{
              if (currentEnv === 1 || currentEnv === 5) {
                modifyStrategyInfo('', 'Online', val ? 1 : 0, item.StrategyId, item);
              } else {
                setReasonModalInfo({
                  visible: true,
                  type: 'Online',
                  val: val ? 1 : 0,
                  id: item.StrategyId,
                  item
                });
              }
						}
					}
				/>;
			}
		},
		{
			key: "GuardOnline",
			header: "护航实例巡检",
			render: item => {
				return <Switch
					value={item.GuardOnline === 1}
					onChange={
						(val)=>{
              if (currentEnv === 1 || currentEnv === 5) {
                modifyStrategyInfo('', 'GuardOnline', val ? 1 : 0, item.StrategyId, item);
              } else {
                setReasonModalInfo({
                  visible: true,
                  type: 'GuardOnline',
                  val: val ? 1 : 0,
                  id: item.StrategyId,
                  item
                });
              }
						}
					}
				/>;
			}
		},
		{
			key: "GuardVolumeOnline",
			header: "护航容量巡检",
			render: item => {
				return <Switch
					value={item.GuardVolumeOnline === 1}
					onChange={
						(val)=>{
              if (currentEnv === 1 || currentEnv === 5) {
                modifyStrategyInfo('', 'GuardVolumeOnline', val ? 1 : 0, item.StrategyId, item);
              } else {
                setReasonModalInfo({
                  visible: true,
                  type: 'GuardVolumeOnline',
                  val: val ? 1 : 0,
                  id: item.StrategyId,
                  item
                });
              }
						}
					}
				/>;
			}
		},
		{
			key: "abc",
			header: "同步",
			render: item => {
				return <Button
					type={'link'}
					onClick={
						()=>{
              if (currentEnv === 1) {
                setPublishInfo({
                  sourceEnv: 1,
                  env: 5,
                  id: [item.StrategyId],
                  text: '预发环境'
                });
              } else {
                setPublishInfo({
                  sourceEnv: 5,
                  env: 4,
                  id: [item.StrategyId],
                  text: '国内国际生产环境'
                });
              }
							setVisible(true);
						}
					}
				>
					发布
				</Button>
				// return  <Dropdown
				// 	trigger="hover"
				// 	button="更多"
				// 	appearance="button"
				// >
				// 	<List type="option">
				// 		<List.Item
				// 			onClick={
				// 				()=>{
				// 					setPublishInfo({
				// 						env: 4,
                //                         id: [item.StrategyId.toString()],
                //                         text: '国内、国际生产环境'
				// 					});
				// 					setVisible(true);
				// 				}
				// 			}
				// 		>
				// 			发布到国内、国际生产环境
				// 		</List.Item>
				// 		 <List.Item
				// 			onClick={
				// 				()=>{
				// 					setPublishInfo({
				// 						env: 3,
				// 						id: item.StrategyId,
				// 						text: '国际生产环境'
				// 					});
				// 					setVisible(true);
				// 				}
				// 			}
				// 		 >
				// 			发布到国际生产环境
				// 		 </List.Item>
				// 	</List>
				// </Dropdown>;
			}
		},
		{
			key: "ab",
			header: "操作",
			render: item => {
				return <div className={'inspect-handle-btn-wrap'}>
					{
						path === 'inspection-release-t'
						?
							<>
								<Button
									type={'link'}
									onClick={
										()=>{
											setStorage('inspectInfos', strategyInfos.find((el)=>{
												return el.AdvisorStrategyInfo.StrategyId === item.StrategyId;
											}));
											history.push(`/advisor/${path}/detail`);
										}
									}
								>
									编辑
								</Button>
								<Button
									type={'link'}
									onClick={
										()=>{
											modifyStrategyInfo(true, 'Online', item.Online, item.StrategyId, item);
										}
									}
								>
									删除
								</Button>
							</>
							:
							<Button
								type={'link'}
								onClick={
									()=>{
										setStorage('inspectInfos', strategyInfos.find((el)=>{
											return el.AdvisorStrategyInfo.StrategyId === item.StrategyId;
										}));
										history.push(`/advisor/${path}/detail`);
									}
								}
							>
								查看详情
							</Button>
					}
				</div>;
			}
		},
		];
	if (currentEnv !== 1 && currentEnv !== 5 && columns[5]['key'] == 'GuardVolumeOnline') {
		columns.splice(6, 1);
	}
	const getTabData = async () => {
		setLoading(true);
		try {
			const res = await DescribeStrategies({
        Limit: limit,
        Offset: offset,
        Filters: filters,
        OnlyData: true,
        ShowError: true,
        AppId: 1253985742,
        Name: getStorage('engName'),
        Env: currentEnv
      });
			const advisorStrategyInfos = res.StrategyInfos.map((item)=>{
				return {
					...item.AdvisorStrategyInfo
				};
			});
			setStrategyInfos(cloneDeep(res.StrategyInfos));
			setRecords({
				TotalCount: res.TotalCount,
				AdvisorStrategyInfos: cloneDeep(advisorStrategyInfos)
			});
			setStorage('inpectReleaseGroup', res.Groups);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	const getAllProduct = async () => {
		try {
			const res = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742
			});
			const list = [];
			for (const key in res.ProductDict) {
				list.push({
					text: res.ProductDict[key],
					value: key
				});
			}
			res.list = list;
			setProductList(list);
			setStorage('productInfo', JSON.stringify(cloneDeep(res)));
		} catch (err) {};
	};
	const batchPublishBtnClick = () => {
    if (currentEnv === 1) {
      setPublishInfo({
        sourceEnv: 1,
        env: 5,
        id: selectedKeys.map(item => Number(item)),
        text: '预发环境'
      });
    } else {
      setPublishInfo({
        sourceEnv: 5,
        env: 4,
        id: selectedKeys.map(item => Number(item)),
        text: '国内国际生产环境'
      });
    }
		setVisible(true);
	};
  useEffect(()=>{
    getAllProduct();
  }, []);
  useEffect(()=>{
    setTimeout(()=>{
      getTabData();
    }, 0);
  }, [offset, limit, currentEnv, filters]);
	useEffect(()=>{
    setPath(getPath());
    setCurrentEnv(getCurrentEnv());
	}, [history.location.pathname]);
  useActivate(() => {
    getTabData();
  });
  useMemo(()=>{
    if (reasonModalInfo.visible === false) {
      setClicked(false);
      setReason('');
    }
  }, [reasonModalInfo]);
	return <div className={'inspect-strategy-wrap'}>
		<Card>
			<Card.Body>
				{
					<ComprehensiveSearch
						originFilterData={
							[
								{
									label: '策略ID',
									name: 'strategy_id',
									type: 'input',
									value: ''
								},
								{
									label: '策略名称',
									name: 'name',
									type: 'input',
									value: ''
								},
								{
									label: '产品',
									name: 'products',
									type: 'mutipleSelect',
									value: '',
									options: [
										...productList
									]
								},
							]
						}
						onReset={()=>{
              setFilters([]);
              setOffset(0);
							setSelectedKeys([]);
						}}
						onSearch={(filters)=>{
              setFilters(filters);
              setOffset(0);
						}}
					/>
				}
			</Card.Body>
		</Card>
		<Card>
			<Card.Body>
				{(currentEnv === 1 || currentEnv === 5) && (
					<Justify left={ <Button type="primary" disabled = {selectedKeys.length > 0 ? false : true} onClick={batchPublishBtnClick}>批量发布</Button> } />
				)}
				<Table
					records = {
						records.AdvisorStrategyInfos ? records.AdvisorStrategyInfos : []
					}
					recordKey="StrategyId"
					columns={columns}
					topTip={
						(loading || records.AdvisorStrategyInfos.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
					}
					addons={
						[
							pageable({
								recordCount: records.TotalCount ? records.TotalCount : 0,
								onPagingChange: ({pageIndex, pageSize}) => {
                  setOffset((pageIndex - 1) * pageSize);
                  setLimit(pageSize);
								},
								pageSizeOptions: [10, 20, 30, 50, 100, 200],
								pageIndex: (offset / limit) + 1,
								pageSize: limit
							}),
              (currentEnv === 1 || currentEnv === 5) && selectable({
								value: selectedKeys,
								onChange: (keys, context) => {
									setSelectedKeys(keys);
								},
								rowSelect: false,
								render: (element, { disabled }) => {
									return disabled ? <Icon type="loading" /> : element;
								},
							}),
							// scrollable({
							// 	minWidth: 1800
							// }),
							// selectable({
							// 	value: selectedKeys,
							// 	onChange: (keys, context) => {
							// 		setSelectedKeys(keys);
							// 	}
							// }),
						]
					}
				/>
			</Card.Body>
		</Card>
		<Modal
			visible={visible}
			caption={'发布二次确认'}
			onClose={() => setVisible(false)}
		>
			<Modal.Body>
				{
					`请确认是否发布${publishInfo.text}`
				}
			</Modal.Body>
			<Modal.Footer>
				<Button
					type="primary"
					onClick={() => {
						updateConfigToProduction(publishInfo.env, publishInfo.id, publishInfo.sourceEnv);
					}}
				>
					确定
				</Button>
				<Button
					type="weak"
					onClick={() => setVisible(false)}
				>
					取消
				</Button>
			</Modal.Footer>
		</Modal>
    <Modal
      visible={reasonModalInfo.visible}
      caption={'原因'}
      onClose={() => {
        setReasonModalInfo({
          ...reasonModalInfo,
          visible: false
        });
      }}
    >
      <Modal.Body>
        <div className={`form-reason-item ${(clicked && !reason) ? 'form-err-reason-item' : ''}`}>
          <Input.TextArea
            placeholder={'请填写原因'}
            onChange={
              (val)=>{
                setReason(val);
              }
            }
          />
          <div className={'error-txt'}>请填写原因</div>
        </div>
      </Modal.Body>
      <Modal.Footer>
        <Button
          loading={saveLoading}
          type="primary"
          onClick={() => {
            setClicked(true);
            modifyStrategyInfo('', reasonModalInfo.type, reasonModalInfo.val, reasonModalInfo.id, reasonModalInfo.item);
          }}
        >
          确定
        </Button>
        <Button
          type="weak"
          onClick={() => {
            setReasonModalInfo({
              ...reasonModalInfo,
              visible: false
            });
          }}
        >
          取消
        </Button>
      </Modal.Footer>
    </Modal>
	</div>;
}