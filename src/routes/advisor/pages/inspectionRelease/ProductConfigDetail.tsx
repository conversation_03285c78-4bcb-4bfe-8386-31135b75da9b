import React, { useState, useMemo, useRef, useEffect } from 'react';
import {
	Layout,
	Card,
	H3,
	Form,
	Input,
	Select,
	Button,
	message,
	Switch
} from '@tea/component';
import { useField, useForm } from "react-final-form-hooks";
import { cloneDeep } from "lodash";
import { getStorage, setStorage } from "@src/utils/storage";
import { ModifyProductConfigInfo } from "@src/api/advisor/inspectionRelease";
import { useHistory } from '@tea/app';


const formInfoList: any = [
	{
		name: 'SubProduct',
		label: '产品',
		placeholder: '请输入产品',
		required: true,
		errorMessage: '请输入产品',
		type: 'input'
	},
	{
		name: 'FatherProduct',
		label: '所属产品',
		placeholder: '请输入所属产品',
		required: true,
		errorMessage: '请输入所属产品',
		type: 'input'
	},
	{
		name: 'SubProductZh',
		label: '子产品名称-中文',
		placeholder: '请输入子产品名称-中文',
		required: true,
		errorMessage: '请输入子产品名称-中文',
		type: 'input'
	},
	{
		name: 'SubProductEn',
		label: '子产品名称-英文',
		placeholder: '请输入子产品名称-英文',
		required: true,
		errorMessage: '请输入子产品名称-英文',
		type: 'input'
	},
	{
		name: 'MonitorDataV3',
		label: 'monitor-datav3特性是否打开',
		placeholder: '请勾选是否启用',
		required: true,
		errorMessage: '请勾选是否启用',
		type: 'switch'
	},
	{
		name: 'ResultReflectField',
		label: '文本结果映射',
		placeholder: '请填写文本结果映射',
		required: false,
		errorMessage: '请填写文本结果映射',
		type: 'textarea'
	},
];
const originFormData = {};
function getStatus(meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}
export function ProductConfigDetail() {
	const path = location.pathname.split('/')[2];
  const currentEnv = path === 'inspection-release-t' ? 1 : path === 'inspection-release-domestic' ? 2 : path === 'inspection-release-pre' ? 5 : 3;
	const history = useHistory();
	const inspectStrategyInfos = JSON.parse(getStorage('inspectInfos'));
	const {Body, Content} = Layout;
	const [formRenderList, setFormRenderList] = useState(cloneDeep(formInfoList));
	// 提交表单
	const onSubmit = async (value) => {
		const params: any = {
			...value,
			MonitorDataV3: value.MonitorDataV3 ? 1 : 0,
		};
		try {
			const res = await ModifyProductConfigInfo({
				ProductConfigInfo: params,
				ModifyType: Object.keys(inspectStrategyInfos).length > 0 ? 2 : 1,
				Env: currentEnv,
			});
			message.success({
				content: '操作成功'
			});
			setStorage('inspectTabKey', 'product');
			history.push(`/advisor/${path}`);
		} catch (err) { };
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {
			...originFormData,
		},
		validate: (formInfo) => {
			const validateFormInfo = {};
			formInfoList.forEach((item) => {
				if (item.required && item.type != 'switch') {
					validateFormInfo[item.name] = formInfo[item.name] !== '' && formInfo[item.name] !== undefined  ? undefined : item.errorMessage;
				}
			});
			return validateFormInfo;
		},
	});
	useMemo(()=>{
		if (inspectStrategyInfos && Object.keys(inspectStrategyInfos).length > 0) {
			form.initialize({
				...inspectStrategyInfos
			});
		};
	}, []);
	const formFieldInfo: any = {};
	formRenderList.forEach((el)=>{
		formFieldInfo[el.name] = useField(el.name, form);
	});
	return <Body className={'detail-form-wrap'}>
		<Content.Header showBackButton onBackButtonClick={() => {
			setStorage('inspectTabKey', 'product');
			history.push(`/advisor/${path}`);
		}}/>
		<Content.Body>
			<form onSubmit={handleSubmit}>
				<Form
					layout={'inline'}
				>
					<Card>
						<H3>
							产品配置
						</H3>
						<Card.Body>
							{
								formRenderList.map((item, i)=>{
									return item.type == 'input'
										?
										<Form.Item
											key={i}
											required={item.required}
											label={item.label}
											status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
											message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
										>
											<Input {...formFieldInfo[item['name']].input} autoComplete="off" placeholder={item.placeholder} />
										</Form.Item>
										:
										item.type == 'textarea'
											?
											<div key={i}>
												<Form.Item
													required={item.required}
													label={item.label}
													status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
													message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
												>
													<Input.TextArea
														{...formFieldInfo[item['name']].input}
														autoComplete="off"
														placeholder={item.placeholder}
														style={{
															width: '820px',
															height: '30px'
														}}
													/>
												</Form.Item>
											</div>
											:
											item.type == 'select'
												?
												<Form.Item
													key={i}
													required={item.required}
													label={item.label}
													status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
													message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
												>
													<Select
														searchable={item.searchable || false}
														overlayClassName={item.name == 'Template' ? 'tem-sel' : ''}
														matchButtonWidth
														{...formFieldInfo[item['name']].input}
														options={item.options}
														appearance="button"
														size='m'
													/>
												</Form.Item>
												:
												item.type == 'switch'
													?
													<Form.Item
														key={i}
														className={'form-item-switch'}
														required={item.required}
														label={item.label}
														status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
														message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
													>
														<Switch {...formFieldInfo[item['name']].input} />
													</Form.Item>
													:
													<></>;
								})
							}
						</Card.Body>
						<div className='btn-wrap'>
							{
								path === 'inspection-release-t' && <Button htmlType="submit" type={'primary'}>保存</Button>
							}
							<Button htmlType={'button'} onClick={() => {
								setStorage('inspectTabKey', 'product');
								history.push(`/advisor/${path}`);
							}}>返回</Button>
						</div>
					</Card>
				</Form>
			</form>
		</Content.Body>
	</Body>;
}