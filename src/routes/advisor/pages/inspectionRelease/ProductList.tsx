import React, { useState, useEffect} from 'react';
import { Card, StatusTip, Table, Icon, message, Switch, Button, Modal, Justify } from '@tea/component';
import { useHistory } from "@tea/app";
import { DescribeProductConfigInfo, ModifyProductConfigInfo, UpdateConfigToProduction, getDescribeProductList } from "@src/api/advisor/inspectionRelease";
import { getStorage, setStorage } from "@src/utils/storage";
import { ComprehensiveSearch } from "@src/components/ComprehensiveSearch";
import { cloneDeep } from "lodash";
import { getCurrentEnv, getPath } from "@src/routes/advisor/pages/inspectionRelease/methods";
import { useActivate } from "react-activation";

export function ProductList() {
  const [path, setPath] = useState(getPath());
  const [currentEnv, setCurrentEnv] = useState(getCurrentEnv());
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [offset, setOffset] = useState(0);
  const [limit, setLimit] = useState(10);
  const [filters, setFilters] = useState([]);
	const [loading, setLoading] = useState(false);
	const [productList, setProductList] = useState([]);
	const [records, setRecords] = useState<any>({
		ProductConfigInfos: []
	});
	const history = useHistory();
	const {pageable, selectable} = Table.addons;
	const [publishInfo, setPublishInfo] = useState({
    sourceEnv: 1,
		env: 1,
		id: [],
		text: ''
	});
	const [visible, setVisible] = useState(false);
	const modifyProductConfigInfo = async (isDelete, key, value, item)=>{
		try {
			const res = await ModifyProductConfigInfo({
				[key]: value,
				SubProduct: item.SubProduct,
				DeleteStatus: isDelete ? 1 : 0,
				Env: currentEnv,
				ModifyType: 2
			});
			message.success({
				content: '操作成功'
			});
			getTabData();
		} catch (err) {};
	};
	const updateConfigToProduction = async (Env, Id, SourceEnv)=>{
		try {
			const res = await UpdateConfigToProduction({
        SourceEnv,
				Env,
				ConfigType: 3,
				Id
			});
			message.success({
				content: '操作成功'
			});
			setSelectedKeys([]);
			setVisible(false);
      getTabData();
		} catch (err) {};
	};
	const columns = [
		{
			key: "SubProduct",
			header: "子产品",
      render(item) {
        return <div>
          {item.SubProduct || '-'}
        </div>;
      }
		},
		{
			key: "FatherProduct",
			header: "所属产品",
      render(item) {
        return <div>
          {item.FatherProduct || '-'}
        </div>;
      }
		},
		{
			key: "SubProductZh",
			header: "子产品名称-中文",
      render(item) {
        return <div>
          {item.SubProductZh || '-'}
        </div>;
      }
		},
		{
			key: "SubProductEn",
			header: "子产品名称-英文",
      render(item) {
        return <div>
          {item.SubProductEn || '-'}
        </div>;
      }
		},
		{
			key: "MonitorDataV3",
			header: "monitor-datav3特性是否打开",
			render: item => {
				return <Switch
					value={item.MonitorDataV3 === 1}
					onChange={
						(val)=>{
							modifyProductConfigInfo('', 'MonitorDataV3', val ? 1 : 0, item);
						}
					}
				/>;
			}
		},
		{
			key: "GuardVolumeOnline",
			header: "同步",
			render: item => {
				return <Button
					type={'link'}
					onClick={
						()=>{
              if (currentEnv === 1) {
                setPublishInfo({
                  sourceEnv: 1,
                  env: 5,
                  id: [item.Id],
                  text: '预发环境'
                });
              } else {
                setPublishInfo({
                  sourceEnv: 5,
                  env: 4,
                  id: [item.Id],
                  text: '国内国际生产环境'
                });
              }
							setVisible(true);
						}
					}
				>
					发布
				</Button>;
			}
		},
		{
			key: "ab",
			header: "操作",
			render: item => {
				return <div className={'inspect-handle-btn-wrap'}>
					{
						path === 'inspection-release-t'
						?
							<>
								<Button
									type={'link'}
									onClick={
										()=>{
											setStorage('inspectInfos', item);
											history.push(`/advisor/${path}/product-detail`);
										}
									}
								>
									编辑
								</Button>
								<Button
									type={'link'}
									onClick={
										()=>{
											modifyProductConfigInfo(true, 'MonitorDataV3', item.MonitorDataV3, item);
										}
									}
								>
									删除
								</Button>
							</>
							:
							<>
								<Button
									type={'link'}
									onClick={
										()=>{
											setStorage('inspectInfos', item);
											history.push(`/advisor/${path}/product-detail`);
										}
									}
								>
									查看详情
								</Button>
								<Button
									type={'link'}
									onClick={
										()=>{
											modifyProductConfigInfo(true, 'MonitorDataV3', item.MonitorDataV3, item);
										}
									}
								>
									删除
								</Button>
							</>
					}
				</div>;
			}
		},
	];
	if (currentEnv !== 1 && currentEnv !== 5 && columns[4]['key'] == 'MonitorDataV3') {
		columns.splice(5, 1);
	}
	const getTabData = async () => {
		setLoading(true);
		try {
			const res = await DescribeProductConfigInfo({
        Limit: limit,
        Offset: offset,
        Filters: filters,
        OnlyData: true,
        ShowError: true,
        AppId: 1253985742,
        Name: getStorage('engName'),
        Env: currentEnv
      });
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	const getAllProduct = async () => {
		try {
			const res = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742
			});
			const list = [];
			for (const key in res.ProductDict) {
				list.push({
					text: res.ProductDict[key],
					value: key
				});
			}
			res.list = list;
			setProductList(list);
			setStorage('productInfo', JSON.stringify(cloneDeep(res)));
		} catch (err) {};
	};
	const batchPublishBtnClick = () => {
    if (currentEnv === 1) {
      setPublishInfo({
        sourceEnv: 1,
        env: 5,
        id: selectedKeys.map(item => Number(item)),
        text: '预发环境'
      });
    } else {
      setPublishInfo({
        sourceEnv: 5,
        env: 4,
        id: selectedKeys.map(item => Number(item)),
        text: '国内国际生产环境'
      });
    }
		setVisible(true);
	};
  useEffect(()=>{
    getAllProduct();
  }, []);
  useEffect(()=>{
    setTimeout(()=>{
      getTabData();
    }, 0);
  }, [offset, limit, currentEnv, filters]);
  useEffect(()=>{
    setPath(getPath());
    setCurrentEnv(getCurrentEnv());
  }, [history.location.pathname]);
  useActivate(() => {
    getTabData();
  });
	return <div className={'inspect-product-wrap'}>
		<Card>
			<Card.Body>
				{
					<ComprehensiveSearch
						originFilterData={
							[
								{
									label: '产品',
									name: 'products',
									type: 'mutipleSelect',
									value: '',
									options: [
										...productList
									]
								},
							]
						}
						onReset={()=>{
              setFilters([]);
              setOffset(0);
              setSelectedKeys([]);
						}}
						onSearch={(filters)=>{
              setFilters(filters);
              setOffset(0);
						}}
						suffix={
							path === 'inspection-release-t'
							&&
							<Button
								style={
									{
										marginLeft: '10px'
									}
								}
								onClick={
									()=>{
										setStorage('inspectInfos', {});
										history.push(`/advisor/${path}/product-detail`);
									}
								}
							>
								新增产品
							</Button>
						}
					/>
				}
			</Card.Body>
		</Card>
		<Card>
			<Card.Body>
				{(currentEnv === 1 || currentEnv === 5) && (
					<Justify left={ <Button type="primary" disabled = {selectedKeys.length > 0 ? false : true} onClick={batchPublishBtnClick}>批量发布</Button> } />
				)}
				<Table
					records={
						records?.ProductConfigInfos ? records.ProductConfigInfos : []
					}
					recordKey="Id"
					columns={columns}
					topTip={
						(loading || records?.ProductConfigInfos?.length == 0) &&
						<StatusTip status={loading ? "loading" : "empty"}/>
					}
					addons={
						[
							pageable({
								recordCount: records?.TotalCount ? records.TotalCount : 0,
								onPagingChange: ({pageIndex, pageSize}) => {
                  setOffset((pageIndex - 1) * pageSize);
                  setLimit(pageSize);
								},
								pageSizeOptions: [10, 20, 30, 50, 100, 200],
                pageIndex: (offset / limit) + 1,
                pageSize: limit
							}),
              (currentEnv === 1 || currentEnv === 5) && selectable({
								value: selectedKeys,
								onChange: (keys, context) => {
									setSelectedKeys(keys);
								},
								rowSelect: false,
								render: (element, {disabled}) => {
									return disabled ? <Icon type="loading"/> : element;
								},
							}),
						]
					}
				/>
			</Card.Body>
		</Card>
		<Modal
			visible={visible}
			caption={'发布二次确认'}
			onClose={() => setVisible(false)}
		>
			<Modal.Body>
				{
					`请确认是否发布${publishInfo.text}`
				}
			</Modal.Body>
			<Modal.Footer>
				<Button
					type="primary"
					onClick={() => {
						updateConfigToProduction(publishInfo.env, publishInfo.id, publishInfo.sourceEnv);
					}}
				>
					确定
				</Button>
				<Button
					type="weak"
					onClick={() => setVisible(false)}
				>
					取消
				</Button>
			</Modal.Footer>
		</Modal>
	</div>;
}