import React, { useState, useMemo, useEffect } from 'react';
import { Layout, Card, H3, Form, Input, Select, Button, message, Switch } from '@tea/component';
import { useField, useForm } from "react-final-form-hooks";
import { cloneDeep } from "lodash";
import { getStorage, setStorage } from "@src/utils/storage";
import { ModifyStrategyInfo } from "@src/api/advisor/inspectionRelease";
import { useHistory } from '@tea/app';


const conditionFieldInfo = [
	// {
	// 	name: 'StrategyId',
	// 	label: '巡检ID',
	// 	placeholder: '请填写巡检ID',
	// 	required: true,
	// 	errorMessage: '请填写巡检ID',
	// 	type: 'input'
	// },
	{
		name: 'ConditionId',
		label: '条件ID',
		placeholder: '请填写条件ID',
		required: false,
		errorMessage: '请填写条件ID',
		type: 'input',
		disabled: true
	},
	{
		name: 'Level',
		label: '级别',
		placeholder: '请选择',
		required: true,
		errorMessage: '请选择',
		type: 'select',
		options: [
			{
				text: '中风险',
				value: 2
			},
			{
				text: '高风险',
				value: 3
			}
		],
    disabled: true
	},
	{
		name: 'Desc',
		label: '描述',
		placeholder: '请填写描述',
		required: true,
		errorMessage: '请填写描述',
		type: 'textarea',
    disabled: true
	},
	{
		name: 'DescEn',
		label: '描述-英文',
		placeholder: '请填写描述-英文',
		required: false,
		errorMessage: '请填写描述-英文',
		type: 'textarea',
    disabled: true
	},
	{
		name: 'Policy',
		label: '策略',
		placeholder: '请填写策略',
		required: true,
		errorMessage: '请填写策略',
		type: 'textarea'
	},
	{
		name: 'PolicyEn',
		label: '策略-英文',
		placeholder: '请填写策略-英文',
		required: false,
		errorMessage: '请填写策略-英文',
		type: 'textarea'
	},
	{
		name: 'PolicyV2',
		label: '策略V2',
		placeholder: '请填写策略V2',
		required: true,
		errorMessage: '请填写策略V2',
		type: 'textarea'
	},
	{
		name: 'Score',
		label: '分数',
		placeholder: '请填写分数',
		required: true,
		errorMessage: '请填写分数',
		type: 'input',
    disabled: true
	},
	{
		name: 'Priority',
		label: '优先级',
		placeholder: '请填写优先级',
		required: true,
		errorMessage: '请填写优先级',
		type: 'input',
    disabled: true
	},
];

const formInfoList: any = [
	[
		{
			name: 'Online',
			label: '日常巡检',
			placeholder: '请勾选日常巡检',
			required: true,
			errorMessage: '请勾选日常巡检',
			type: 'switch'
		},
		{
			name: 'GuardVolumeOnline',
			label: '护航容量巡检',
			placeholder: '请勾选日常容量巡检',
			required: true,
			errorMessage: '请勾选日常容量巡检',
			type: 'switch'
		},
		{
			name: 'GuardOnline',
			label: '护航实例巡检',
			placeholder: '请勾选日常实例巡检',
			errorMessage: '请勾选日常实例巡检',
			required: true,
			type: 'switch'
		},
		{
			name: 'StrategyId',
			label: '巡检ID',
			placeholder: '请输入巡检ID',
			required: false,
			errorMessage: '请输入巡检ID',
			type: 'input',
			disabled: true
		},
		{
			name: 'StrategyName',
			label: '巡检名称',
			placeholder: '请输入巡检名称',
			required: true,
			errorMessage: '请输入巡检名称',
			type: 'input',
      disabled: true
		},
		{
			name: 'StrategyNameEn',
			label: '巡检名称-英文',
			placeholder: '请输入巡检名称-英文',
			required: false,
			errorMessage: '请输入巡检名称-英文',
			type: 'input',
      disabled: true
		},
		{
			name: 'Product',
			label: '产品',
			placeholder: '请选择产品',
			required: true,
			errorMessage: '请选择产品',
			type: 'select',
			searchable: true,
			options: [],
      disabled: true
		},
		{
			name: 'Group',
			label: '类别',
			placeholder: '请选择类别',
			required: true,
			errorMessage: '请选择类别',
			type: 'select',
			options: [],
      disabled: true
		},
		{
			name: 'InstanceType',
			label: '实例类型',
			placeholder: '请输入实例类型',
			required: true,
			errorMessage: '请输入实例类型',
			type: 'input'
		},
		{
			name: 'StrategyShortName',
			label: '策略缩写名称',
			placeholder: '请填写策略缩写名称',
			required: true,
			errorMessage: '请填写策略缩写名称',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'StrategyShortNameEn',
			label: '策略缩写名称-英文',
			placeholder: '请填写策略缩写名称-英文',
			required: false,
			errorMessage: '请填写策略缩写名称-英文',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'ShortDesc',
			label: '策略短描述',
			placeholder: '请填写策略短描述',
			required: true,
			errorMessage: '请填写策略短描述',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'ShortDescEn',
			label: '策略短描述-英文',
			placeholder: '请填写策略短描述-英文',
			required: false,
			errorMessage: '请填写策略短描述-英文',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'Desc',
			label: '描述',
			placeholder: '请填写描述',
			required: true,
			errorMessage: '请填写描述',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'DescEn',
			label: '描述-英文',
			placeholder: '请填写描述-英文',
			required: false,
			errorMessage: '请填写描述-英文',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'Repair',
			label: '修复建议',
			placeholder: '请填写修复建议',
			required: true,
			errorMessage: '请填写修复建议',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'RepairEn',
			label: '修复建议-英文',
			placeholder: '请填写修复建议-英文',
			required: false,
			errorMessage: '请填写修复建议-英文',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'Notice',
			label: '风险数量提示',
			placeholder: '请填写风险数量提示',
			required: true,
			errorMessage: '请填写风险数量提示',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'NoticeEn',
			label: '风险数量提示-英文',
			placeholder: '请填写风险数量提示-英文',
			required: false,
			errorMessage: '请填写风险数量提示-英文',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'Ignore',
			label: '忽略提示',
			placeholder: '请填写忽略提示',
			required: true,
			errorMessage: '请填写忽略提示',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'IgnoreEn',
			label: '忽略提示-英文',
			placeholder: '请填写忽略提示-英文',
			required: false,
			errorMessage: '请填写忽略提示-英文',
			type: 'textarea',
      disabled: true
		},
		{
			name: 'IsSupportUserPolicy',
			label: '是否支持用户自定义策略',
			placeholder: '请勾选是否支持用户自定义策略',
			required: true,
			errorMessage: '请勾选是否支持用户自定义策略',
			type: 'switch'
		},
		{
			name: 'IsPublic',
			label: '是否公共巡检项',
			placeholder: '请勾选日常巡检',
			required: true,
			errorMessage: '请勾选日常巡检',
			type: 'switch'
		},
		{
			name: 'Score',
			label: '得分',
			placeholder: '请输入得分',
			required: true,
			errorMessage: '请输入得分',
			type: 'input'
		},
	],
	[
		conditionFieldInfo
	],
	[
		// {
		// 	name: 'StrategyIdFrontend',
		// 	label: '巡检ID',
		// 	placeholder: '请填写巡检ID',
		// 	required: true,
		// 	errorMessage: '请填写巡检ID',
		// 	type: 'input'
		// },
		{
			name: 'Url',
			label: 'url',
			placeholder: '请填写url',
			required: false,
			errorMessage: '请填写url',
			type: 'input'
		},
		{
			name: 'UrlEn',
			label: 'url-en',
			placeholder: '请填写url-en',
			required: false,
			errorMessage: '请填写url-en',
			type: 'input'
		},
		{
			name: 'LinkedId',
			label: 'link_id',
			placeholder: '请填写link_id',
			required: false,
			errorMessage: '请填写link_id',
			type: 'input'
		},
		{
			name: 'PriId',
			label: 'pri_id',
			placeholder: '请填写pri_id',
			required: false,
			errorMessage: '请填写pri_id',
			type: 'input'
		},
		// {
		// 	name: 'StrategyNameEn',
		// 	label: '更新时间',
		// 	placeholder: '请填写更新时间',
		// 	required: true,
		// 	errorMessage: '请填写更新时间',
		// 	type: 'input'
		// },
		{
			name: 'Fields',
			label: '列表配置fields\r\n(json格式展示)',
			placeholder: '请填写列表配置',
			required: false,
			errorMessage: '请填写列表配置',
			type: 'textarea'
		},
	]
];
const originFormData = {};
function getStatus(meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}
let releaseInitLoad = true;
export function StrategyConfigDetail() {
	const productInfo = JSON.parse(getStorage('productInfo')) || {list: []};
	const inpectReleaseGroup = JSON.parse(getStorage('inpectReleaseGroup'));
	if (releaseInitLoad) {
		formInfoList[0][6].options = [...productInfo?.list];
		formInfoList[0][7].options = inpectReleaseGroup?.map((item)=>{
			return {
				text: item,
				value: item
			};
		});
		releaseInitLoad = false;
	}
	const path = location.pathname.split('/')[2];
  const currentEnv = path === 'inspection-release-t' ? 1 : path === 'inspection-release-domestic' ? 2 : path === 'inspection-release-pre' ? 5 : 3;
	const history = useHistory();
	const inspectStrategyInfos = JSON.parse(getStorage('inspectInfos'));
	const {Body, Content} = Layout;
	const [formRenderList, setFormRenderList] = useState(cloneDeep(formInfoList));
	// 提交表单
	const onSubmit = async (value) => {
		const params: any = {
			AdvisorStrategyInfo: {},
			AdvisorConditionInfo: [],
			AdvisorFrontendInfo: {}
		};
		for (const key in value) {
			if (key.indexOf('-frontend') != -1) {
				const k = key.split('-frontend')[0];
				params.AdvisorFrontendInfo[k] = (k == 'StrategyId' ? Number(value[key]) : value[key]);
			} else if (key.indexOf('-') != -1) {
				const arr = key.split('-');
				if (params.AdvisorConditionInfo[arr[1]] === undefined) {
					params.AdvisorConditionInfo[arr[1]] = {
						[arr[0]]: (arr[0] == 'StrategyId' ? Number(value[key]) : value[key])
					};
				} else {
					params.AdvisorConditionInfo[arr[1]][arr[0]] = ((arr[0] == 'StrategyId' || arr[0] == 'ConditionId' || arr[0] == 'Level' || arr[0] == 'Score' || arr[0] == 'Priority' || arr[0] == 'Days') ? Number(value[key]) : value[key]);
				}
			} else {
				if (key === 'Online' || key === 'GuardOnline' || key === 'GuardVolumeOnline' || key === 'IsSupportUserPolicy' || key === 'IsPublic') {
					params.AdvisorStrategyInfo[key] = value[key] ? 1 : 0;
				} else {
					params.AdvisorStrategyInfo[key] = (key == 'StrategyId' || key == 'Score' ? Number(value[key]) : value[key]);
				}
			}
		}
		try {
			const res = await ModifyStrategyInfo({
				StrategyInfo: params,
				StrategyId: 0,
				ModifyType: Object.keys(inspectStrategyInfos).length > 0 ? 2 : 1,
				Env: currentEnv,
			});
			message.success({
				content: '操作成功'
			});
			setStorage('inspectTabKey', 'strategy');
			history.push(`/advisor/${path}`);
		} catch (err) { };
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {
			...originFormData,
		},
		validate: (formInfo) => {
			const validateFormInfo = {};
			formRenderList.forEach((el, i) => {
				if (i == 1) {
					el.forEach((val, j)=>{
						val.forEach((obj)=>{
							const name = `${obj.name}-${j}`;
							if ((obj.type != 'switch' && obj.required)) {
								validateFormInfo[name] = formInfo[name] !== '' && formInfo[name] !== undefined ? undefined : obj.errorMessage;
							}
						});
					});
				} else if (i == 2) {
					el.forEach((item)=>{
						if ((item.type != 'switch' && item.required)) {
							validateFormInfo[`${item.name}-frontend`] = formInfo[`${item.name === 'StrategyIdFrontend' ? 'StrategyId' : item.name}-frontend`] !== '' && formInfo[`${item.name === 'StrategyIdFrontend' ? 'StrategyId' : item.name}-frontend`] !== undefined ? undefined : item.errorMessage;
						}
					});
				} else {
					el.forEach((item)=>{
						if ((item.type != 'switch' && item.required)) {
							validateFormInfo[item.name] = formInfo[item.name] !== '' && formInfo[item.name] !== undefined ? undefined : item.errorMessage;
						}
					});
				}
			});
			return validateFormInfo;
		},
	});
	useMemo(()=>{
		if (inspectStrategyInfos && Object.keys(inspectStrategyInfos).length > 0) {
      if (inspectStrategyInfos?.AdvisorConditionInfo) {
        formRenderList[1] = inspectStrategyInfos?.AdvisorConditionInfo?.map(()=>{
          return conditionFieldInfo;
        });
        setFormRenderList(cloneDeep(formRenderList));
      }
			const obj = {};
			for (const key in inspectStrategyInfos) {
				if (key === 'AdvisorConditionInfo') {
					const list = inspectStrategyInfos[key];
					list?.forEach((item, i)=>{
						for (const k in item) {
							obj[`${k}-${i}`] = item[k];
						}
					});
				} else {
					for (const k in inspectStrategyInfos[key]) {
						if (key === 'AdvisorFrontendInfo') {
							obj[`${k}-frontend`] = inspectStrategyInfos[key][k];
						} else {
							if (k === 'Online' || k === 'GuardOnline' || k === 'GuardVolumeOnline' || k === 'IsSupportUserPolicy' || k === 'IsPublic') {
								obj[k] = (inspectStrategyInfos[key][k] === 1);
							} else {
								obj[k] = inspectStrategyInfos[key][k];
							}
						};
					}
				}
			}
			form.initialize({
				...obj
			});
		};
	}, []);
	const formFieldInfo: any = {};
	formRenderList.forEach((el, i)=>{
		if (i === 1) {
			for (let j = 0;j < 10; ++j) {
				el[0].forEach((obj)=>{
					const name = `${obj.name}-${j}`;
					formFieldInfo[name] = useField(name, form);
				});
			}
		} else if (i === 2) {
			el.forEach((item) => {
				const name = `${item.name}-frontend`;
				formFieldInfo[item.name] = useField(name === 'StrategyIdFrontend-frontend' ? 'StrategyId-frontend' : name, form);
			});
		} else {
			el.forEach((item) => {
				formFieldInfo[item.name] = useField(item.name, form);
			});
		}
	});
	useEffect(()=>{
		return ()=>{
			releaseInitLoad = true;
		};
	}, []);
	return <Body className={'detail-form-wrap'}>
		<Content.Header showBackButton onBackButtonClick={() => {
			setStorage('inspectTabKey', 'strategy');
			history.push(`/advisor/${path}`);
		}}/>
		<Content.Body>
			<form onSubmit={handleSubmit}>
				<Form
					layout={'inline'}
				>
					{
						formRenderList.map((el, i)=>{
							return <div key={i}>
								<Card>
									<H3>
										{
											i == 0
											?
												'策略配置'
												:
												i == 1
												?
												<>
													条件配置
													<Button
														style={
															{
																marginLeft: '10px'
															}
														}
                            disabled={formRenderList[1]?.length > 9}
														onClick={
															(e)=>{
																formRenderList[1].push(cloneDeep(conditionFieldInfo));
																setFormRenderList(cloneDeep(formRenderList));
																e.stopPropagation();
																e.preventDefault();
															}
														}
													>
														新增
													</Button>
												</>
													:
													'前端列表配置'
										}
									</H3>
									{
										i === 1
										?
											<Card.Body>
												{
													el.map((obj, j)=>{
														return <div key={j} className={j > 0 && obj.length > 0 ? 'form-line' : ''}>
															{
																obj.map((item, i)=>{
																	const name = `${item.name}-${j}`;
																	return item.type == 'input'
																		?
																		<Form.Item
																			key={i}
																			required={item.required}
																			label={item.label}
																			status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																			message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																		>
																			<Input {...formFieldInfo[name].input} autoComplete="off" placeholder={item.placeholder} disabled={item.disabled || false}/>
																		</Form.Item>
																		:
																		item.type == 'textarea'
																			?
																			<div key={i}>
																				<Form.Item
																					required={item.required}
																					label={item.label}
																					status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																					message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																				>
																					<Input.TextArea
																						{...formFieldInfo[name].input}
																						autoComplete="off"
																						placeholder={item.placeholder}
																						style={{
																							width: '820px',
																							height: '60px'
																						}}
                                            disabled={item.disabled || false}
																					/>
																				</Form.Item>
																			</div>
																			:
																			item.type == 'select'
																				?
																				<Form.Item
																					key={i}
																					required={item.required}
																					label={item.label}
																					status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																					message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																				>
																					<Select
																						searchable={item.searchable || false}
																						matchButtonWidth
																						{...formFieldInfo[name].input}
																						options={item.options}
																						appearance="button"
																						size='m'
                                            disabled={item.disabled || false}
																					/>
																				</Form.Item>
																				:
																				item.type == 'switch'
																					?
																					<Form.Item
																						key={i}
																						className={'form-item-switch'}
																						required={item.required}
																						label={item.label}
																						status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																						message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																					>
																						<Switch {...formFieldInfo[name].input} disabled={item.disabled || false}/>
																					</Form.Item>
																					:
																					<></>;
																})
															}
															{
																j > 0 && obj.length > 0 && <div>
																	<Button
																		style={
																			{
																				marginBottom: '20px'
																			}
																		}
																		onClick={
																			(e)=>{
																				formRenderList[1].forEach((el, k)=>{
																					conditionFieldInfo.forEach((val)=>{
																						const name = `${val.name}-${k}`;
																						if (j <= k) {
																							form.change(name, values[`${val.name}-${k + 1}`]);
																						}
																					});
																				});
																				formRenderList[1].splice(j, 1);
																				setFormRenderList(cloneDeep(formRenderList));
																				e.stopPropagation();
																				e.preventDefault();
																			}
																		}
																	>删除</Button>
																</div>
															}
														</div>;
													})
												}
											</Card.Body>
											:
											<Card.Body>
												{
													el.map((item, i)=>{
														return item.type == 'input'
															?
															<Form.Item
																key={i}
																required={item.required}
																label={item.label}
																status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
															>
																<Input {...formFieldInfo[item['name']].input} autoComplete="off" placeholder={item.placeholder} disabled={item.disabled || false} />
															</Form.Item>
															:
															item.type == 'textarea'
																?
																<div key={i}>
																	<Form.Item
																		required={item.required}
																		label={item.label}
																		status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																		message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																	>
																		<Input.TextArea
																			{...formFieldInfo[item['name']].input}
																			autoComplete="off"
																			placeholder={item.placeholder}
																			style={{
																				width: '820px',
																				height: '60px'
																			}}
                                      disabled={item.disabled || false}
																		/>
																	</Form.Item>
																</div>
																:
																item.type == 'select'
																	?
																	<Form.Item
																		key={i}
																		required={item.required}
																		label={item.label}
																		status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																		message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																	>
																		<Select
																			searchable={item.searchable || false}
																			overlayClassName={item.name == 'Template' ? 'tem-sel' : ''}
																			matchButtonWidth
																			{...formFieldInfo[item['name']].input}
																			options={item.options}
																			appearance="button"
																			size='m'
                                      disabled={item.disabled || false}
																		/>
																	</Form.Item>
																	:
																	item.type == 'switch'
																		?
																		<Form.Item
																			key={i}
																			className={'form-item-switch'}
																			required={item.required}
																			label={item.label}
																			status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																			message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																		>
																			<Switch {...formFieldInfo[item['name']].input} disabled={item.disabled || false}/>
																		</Form.Item>
																		:
																		<></>;
													})
												}
											</Card.Body>
									}
									{
										i === 2 && <div className='btn-wrap'>
											{
												path === 'inspection-release-t' && <Button htmlType="submit" type={'primary'}>保存</Button>
											}
											<Button htmlType={'button'} onClick={() => {
												setStorage('inspectTabKey', 'strategy');
												history.push(`/advisor/${path}`);
											}}>返回</Button>
										</div>
									}
								</Card>
							</div>;
						})
					}
				</Form>
			</form>
		</Content.Body>
	</Body>;
}