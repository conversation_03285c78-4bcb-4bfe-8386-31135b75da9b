import React, { useState, useEffect, useContext, useMemo } from 'react';
import { useHistory } from '@tea/app';
import { app } from '@tencent/tea-app';
import moment from 'moment';
import { DatePicker, Form, Radio } from '@tencent/tea-component';
import { Layout } from '@tea/component/layout';
import { Card } from '@tencent/tea-component/lib/card';
import { Text } from '@tencent/tea-component/lib/text';
import { Button } from '@tencent/tea-component/lib/button';
import { Table } from '@tencent/tea-component/lib/table';
import { Modal } from '@tencent/tea-component/lib/modal';

import { Segment } from '@tencent/tea-component/lib/segment';
import { getStorage, setStorage, removeStorage } from '@src/utils/storage';
import { getSummaryMsg, getArchReports } from '@src/api/advisor/summary';
import { reportVisitPage } from '@src/utils/report';
import {
	getReportFileAsync,
	getReportResultAsync,
	getProductsGroups,
	getCustomerName,
	describeReportTemplateList,
} from '@src/api/advisor/estimate';
import { AdvisorGroupReport } from '@src/routes/advisor/estimate/AdvisorGroupReport';
import { DownLoadReportTplTag, DownLoadReportTplTagSon } from '@src/types/advisor/estimate1';
import { toUTCtime } from '@src/utils/toUTCtime';
import { Bubble } from '@tea/component/bubble';
import { Icon } from '@tea/component/icon';
import { BasicPie } from '@tencent/tea-chart/lib/basicpie';
import { BasicLine } from '@tencent/tea-chart/lib/basicline';
import { Tabs, TabPanel } from '@tencent/tea-component/lib/tabs';
import { AdvisorLable } from '@src/routes/architecture/components/EditorTopMenu/OverviewReport/components/AdvisorLable';
import { message } from '@tea/component/message';
import { autotip } from '@tencent/tea-component/lib/table/addons';
import _ from 'lodash';
import { insertCSS } from '@src/utils/insertCSS';

import { iconSvg } from '@src/configs/iconSvg';
import './archReport.less';
import { AdvisorStrategyReport } from '@src/routes/architecture/components/EditorTopMenu/OverviewReport/components/AdvisorStrategyReport';
insertCSS(
	'AdvisorLable',
	`
.app-advisor-layout__content-body-inner {
	max-width: 100% !important;
	}

`
);
const { Body, Content } = Layout;
const { scrollable, columnsResizable } = Table.addons;
// 当前下载报告状态队列池
let currentReportStatusQueue = [];

export function Summary({ match }) {
	// 报告类别，默认为外部报告
	const [env, setEnv] = useState('public');
	// 报告时间，默认为空，即为最后一次报告生成时间
	// const [reportDate, setReportDate] = useState(moment().endOf("d").format("YYYY-MM-DD"));
	const [reportDate, setReportDate] = useState(moment().format('YYYY-MM-DD'));
	// 内部报告提示
	const [visableWarning, setVisableWarning] = useState(false);
	// 维度列表
	const [Groups, setGroups] = useState<Array<{ Id: number; GroupName: string }>>([]);
	// 产品列表--下拉框
	const [TemplateOptions, setTemplateOptions] = useState([]);
	// 产品列表
	const [Products, setProducts] = useState([]);
	// 产品列表--下拉框
	const [ProductsOptions, setProductsOptions] = useState([]);
	// 产品映射表
	const [ProductsMap, setProductsMap] = useState(new Map());
	// 业务连续评估总览策略集合
	const [overviewStrategies, setOverviewStrategies] = useState([]);
	// 安全策略集合
	const [securityStrategies, setSecurityStrategies] = useState([]);
	// 可靠策略集合
	const [architectStrategies, setArchitectStrategies] = useState([]);
	// 性能策略集合
	const [performanceStrategies, setPerformanceStrategies] = useState([]);
	// 成本策略集合
	const [costStrategies, setCostStrategies] = useState([]);
	// 服务限制策略集合
	const [resourceStrategies, setResourceStrategies] = useState([]);
	// TIC角色授权状态
	const [isTICAuthorized, setIsTICAuthorized] = useState(false);
	// CAM角色授权状态
	const [isCAMAuthorized, setIsCAMAuthorized] = useState(false);
	// 当前任务ID
	const [currentTaskId, setCurrentTaskId] = useState('');
	// 当前报告下载ID
	const [currentRequestId, setCurrentRequestId] = useState('');
	// 下载地址
	const [cosUrl, setCosUrl] = useState({ CosUrl: '', CosUrlPdf: '' });
	// 当前报告下载状态
	const [downStatus, setDownStatus] = useState('');
	// 最近一次巡检时间
	const [lastTaskTime, setTlastTaskTime] = useState('--');
	// 客户名称
	const [customerName, setCustomerName] = useState('--');
	// 策略组任务结果概览
	const [groupSummary, setGroupSummary] = useState({});
	// 当前评估数据
	const [currentProductSummaries, setCurrentProductSummaries] = useState([]);
	// 云产品评估数据
	const [productSummaries, setProductSummaries] = useState([]);
	// 云产品评估数据(all)ProductSummaries
	const [productSummariesAll, setProductSummariesAll] = useState([]);
	// 风险分布数据
	const [riskPie, setRiskPie] = useState([]);
	// 高危评估数据
	const [strategyTopSummaries, setStrategyTopSummaries] = useState([]);
	// 风险趋势数据
	const [riskDays, setRiskDays] = useState([]);
	// 风险趋势数据
	const [flag, setFlag] = useState(0);
	// 查询任务结果定时器
	let taskTimer;
	// 下载报告按钮，错误提示文本
	const [downloadFileReportError, setdownloadFileReportError] = useState(null);
	// 下载报告状态队列池
	const [reportStatusQueue, setReportStatusQueue] = useState([]);

	// 数据加载状态
	const [isLoading, setIsLoading] = useState(true);
	// 数据加载时显示的内容
	const [loadingMsg, setLoadingMsg] = useState('');

	// 云架构报告数据
	const [architectureList, setArchitectureList] = useState([]);
	// 下载报告状态队列池
	const [reportStatusQueue1, setReportStatusQueue1] = useState([]);

	const defaultColumns = [
		{
			key: 'StrategyName',
			header: '评估项名称',
		},
		{
			key: 'HighRiskCount',
			width: 100,
			header: () => (
				<p>
					<span style={{ color: '#e54545' }}>{'高风险'}</span>
				</p>
			),
		},
		{
			key: 'MediumRiskCount',
			width: 100,
			header: () => (
				<p>
					<span style={{ color: '#ff9d00' }}>{'中风险'}</span>
				</p>
			),
		},
		{
			key: 'Count',
			width: 100,
			header: '资源数',
		},
	];
	const [columns, setColumns] = useState(defaultColumns);
	// 策略组名称映射
	const tabNameMap = new Map([
		// 资源
		[-1, 'overview'],
		[1, 'security'],
		[2, 'architecture'],
		[3, 'resource'],
		[4, 'cost'],
		[5, 'performance'],
		// 架构
		[6, 'property'], // 性能
		[7, 'disaster'], // 容灾容错
		[8, 'loadBalance'], // 负载均衡
		[9, 'securityArchitect'], // 架构安全
	]);
	const history = useHistory();

	// 日期初始化
	const initDate = async () => {
		let tmp = '';

		const today = new Date(); // 转换成Data();
		const y = today.getFullYear();
		const m = today.getMonth() + 1;
		const month = m < 10 ? `0${m}` : m;
		const d = today.getDate();
		const day = d < 10 ? `0${d}` : d;
		tmp = `${y}-${month}-${day}`;
		setReportDate(tmp);
	};

	useEffect(() => {
		reportVisitPage({
			isaReportMeunName: '云巡检报告',
		});
	}, []);
	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getProductsGroups({
				AppId: Number(match.params.appid),
				Env: env,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });
				return;
			}
			setProducts(res.Products);
			setGroups(res.Groups);
			const tmpProductsOptions = [];
			const tmpProductsMap = new Map();
			for (const i in res.ProductDict) {
				tmpProductsOptions.push({ value: i, text: res.ProductDict[i] });
				tmpProductsMap.set(i, res.ProductDict[i]);
			}
			setProductsOptions(tmpProductsOptions); // 产品下拉框
			// ProductsMap.putMap(tmpProductsMap)
			setProductsMap(tmpProductsMap); // 产品映射表
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });
		}
	};

	// 获取所有用户模板信息
	const getTemplatesInfo = async (queryAppId) => {
		try {
			const res = await describeReportTemplateList({
				AppId: queryAppId,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });
				return;
			}
			const tmpTemplateOptions = [{ value: '0', text: '不使用模板' }];
			if (res.TemplateList) {
				res.TemplateList.map((item) => {
					tmpTemplateOptions.push({ value: item.Id, text: item.Name });
				});
			}
			setTemplateOptions(tmpTemplateOptions); // 模板下拉框*/
			// console.log(tmpTemplateOptions)
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });
		}
	};

	// 全部维度id列表
	const AllGroups = useMemo(() => {
		const tmp = [];
		Groups.forEach((i) => {
			tmp.push(i.Id.toString());
		});
		return tmp;
	}, [Groups]);

	// 云架构报告数据请求
	const archFn = async () => {
		try {
			const listData = await getArchReports({
				AppId: Number(match.params.appid),
				Date: reportDate,
			});
			if (listData.Error) {
				const msg = listData.Error.Message || '';
				message.error({ content: msg });
				setIsLoading(true);
				setLoadingMsg(msg);
				return;
			}
			if (listData.Has) {
				// 数据存在，并加载完成
				setIsLoading(false);
				if (listData.CustomerCloudMapOverviewSet.length) {
					setArchitectureList(listData.CustomerCloudMapOverviewSet);
				} else {
					setArchitectureList([]);
				}
			} else {
				if (listData.IsAuthorized === 4) {
					setIsLoading(true);
					message.error({ content: '该日期报告不存在！' });
					setLoadingMsg('提示：用户已开通云顾问授权，但该日期报告不存在，请确认最近一次巡检时间并重新选择日期。');
				} else if (listData.IsAuthorized === 3) {
					setIsLoading(true);
					message.error({ content: '客户未开通云顾问“报告解读”，可以引导客户在云顾问控制台【服务授权】页面开通！' });
					setLoadingMsg('提示：客户未开通云顾问“报告解读”，可以引导客户在云顾问控制台【服务授权】页面开通');
				} else if (listData.IsAuthorized === 2) {
					setIsLoading(true);
					message.error({ content: '用户未开通云顾问授权！' });
					setLoadingMsg('提示：用户已开通云顾问授权，但该日期报告不存在，请确认最近一次巡检时间并重新选择日期。');
				} else {
					setIsLoading(true);
					message.error({ content: '云顾问权限判断异常！' });
					setLoadingMsg('提示：云顾问权限判断异常。');
				}
			}
		} catch (err) {
			console.log(err);
		}
	};

	useEffect(() => {
		if (env === 'public') {
			getProductsGroupsInfo();
			getTemplatesInfo(Number(match.params.appid));
			setDownStatus('');
			setCosUrl({ CosUrl: '', CosUrlPdf: '' });
		} else {
			archFn();
		}
		setIsLoading(true);
		setLoadingMsg('数据加载中...');
	}, [env, reportDate]);

	useEffect(() => {
		if (ProductsMap.size) {
			getSummaryMsgData();
		} else {
			setIsLoading(true);
			setLoadingMsg('暂无报告数据。');
		}
	}, [ProductsMap]);

	// 获取当前的最新任务ID
	const getSummaryMsgData = async () => {
		try {
			const customerNameData = await getCustomerName({
				AppId: Number(match.params.appid),
			});
			if (customerNameData.Error) {
				const msg = customerNameData.Error.Message || '';
				message.error({ content: msg });
				setIsLoading(true);
				setLoadingMsg(msg);
				return;
			}
			setCustomerName(customerNameData.CustomerName);
			const summaryData = await getSummaryMsg({
				AppId: Number(match.params.appid),
				Env: env,
				Date: reportDate,
			});
			if (summaryData.Error) {
				const msg = summaryData.Error.Message || '';
				message.error({ content: msg });
				setIsLoading(true);
				setLoadingMsg(msg);
				return;
			}

			if (summaryData.Has) {
				// 数据存在，并加载完成
				setIsLoading(false);

				setCurrentTaskId(summaryData.TaskId);
				// handleReportAsync(summaryData.TaskId);
				if (summaryData.GroupSummaries.length > 0) {
					const currentGroupSummary = {};
					summaryData.GroupSummaries.forEach((summary) => {
						currentGroupSummary[tabNameMap.get(summary.GroupId)] = {
							high: summary.HighRiskCount,
							medium: summary.MediumRiskCount,
							low: summary.LowRiskCount,
							none: summary.NoRiskCount,
						};
					});
					setGroupSummary(currentGroupSummary);
				} else {
					setGroupSummary({});
				}

				if (summaryData.ProductSummaries.length > 0) {
					const ProductSummariesAll = _.orderBy(
						summaryData.ProductSummaries.map(item => _.merge(item, {
							productName: ProductsMap.get(item.Product),
							type: ProductsMap.get(item.Product),
							value: item.RiskCount,
						})),
						['value'],
						['desc']
					);

					const ProductSummaries = ProductSummariesAll.slice(0, 5);
					const ProductSummariesOther = ProductSummariesAll.slice(5);
					const otherValue = _.sumBy(ProductSummariesOther, 'value');
					const riskDistribute = _.cloneDeep(ProductSummaries);
					riskDistribute.push({ type: '其他', value: otherValue });

					_.remove(riskDistribute, item => item.value === 0);
					setCurrentProductSummaries(ProductSummaries);
					setProductSummariesAll(ProductSummariesAll);
					setProductSummaries(ProductSummaries);
					setRiskPie(riskDistribute);
				} else {
					setCurrentProductSummaries([]);
					setProductSummariesAll([]);
					setProductSummaries([]);
					setRiskPie([]);
				}

				setStrategyTopSummaries(summaryData.StrategyTopSummaries);

				if (summaryData.RiskDaysList) {
					let tmpList = [];
					for (const i in summaryData.RiskDaysList) {
						let name = '';
						if (i === '1') {
							name = '安全';
						} else if (i === '2') {
							name = '可靠';
						} else if (i === '3') {
							name = '服务限制';
						} else if (i === '4') {
							name = '成本';
						} else if (i === '5') {
							name = '性能';
						} else if (i === '-1') {
							name = '总览';
						} else {
							name = '-2';
						}
						if (summaryData.RiskDaysList[i].length > 0) {
							tmpList = tmpList.concat(summaryData.RiskDaysList[i].map(item => ({ time: item.Date, value: item.Count, key: name })));
						}
					}
					setRiskDays(tmpList);
				} else {
					setRiskDays([]);
				}

				if (summaryData.FinishTime.length > 0) {
					const finishTimeStamp = new Date(summaryData.FinishTime).getTime() / 1000;
					setTlastTaskTime(toUTCtime(finishTimeStamp));
				} else {
					setTlastTaskTime('-');
				}
			} else {
				if (summaryData.IsAuthorized === 4) {
					setIsLoading(true);
					message.error({ content: '该日期报告不存在！' });
					setLoadingMsg('提示：用户已开通云顾问授权，但该日期报告不存在，请确认最近一次巡检时间并重新选择日期。');
				} else if (summaryData.IsAuthorized === 3) {
					setIsLoading(true);
					message.error({ content: ' 客户未开通云顾问“报告解读”，可以引导客户在云顾问控制台【服务授权】页面开通！' });
					setLoadingMsg('提示： 客户未开通云顾问“报告解读”，可以引导客户在云顾问控制台【服务授权】页面开通');
				} else if (summaryData.IsAuthorized === 2) {
					setIsLoading(true);
					message.error({ content: '用户未开通云顾问授权！' });
					setLoadingMsg('提示：客户未开通云顾问，可以引导客户在云顾问控制台【服务授权】页面开通。');
				} else {
					setIsLoading(true);
					message.error({ content: '云顾问权限判断异常！' });
					setLoadingMsg('提示：云顾问权限判断异常。');
				}
			}
		} catch (err) {
			setIsLoading(true);
			const msg = err.msg || err.toString() || '未知错误';
			// //app.tips.error({ message });
			message.error({ content: msg });
			setLoadingMsg(msg);
		}
	};

	// 异步请求生成下载报告
	const handleReportAsync = async (
		taskId,
		env,
		type,
		currentLanguage,
		currentProducts,
		currentGroups,
		currentStrategyIds,
		currentTags
	) => {
		try {
			// 参数处理
			const tmp = [];
			currentGroups.forEach((i) => {
				tmp.push(parseInt(i));
			});
			currentGroups = tmp;

			// 全选，则传入空列表，后台表示全部维度
			if (currentGroups.length === Groups.length) {
				currentGroups = [];
			}
			// 全选，则传入空列表，后台标识全部产品
			if (currentProducts.length === Products.length) {
				currentProducts = [];
			}

			const reportAsync = await getReportFileAsync({
				AppId: Number(match.params.appid),
				Id: -1,
				Type: 'Group',
				TaskId: taskId,
				Env: env,
				Products: currentProducts,
				GroupIDs: currentGroups, // GroupIDs: [1,2,3],
				StrategyIDs: currentStrategyIds,
				Tags: currentTags
					.filter((i) => {
						if (i.TagKey != '') {
							return i;
						}
					})
					.map(i => ({ TagKey: i.TagKey, TagValues: i.TagValues })),
				Language: currentLanguage,
			});

			if (reportAsync.Error) {
				const msg = reportAsync.Error.Message || '';
				message.error({ content: msg });
				setdownloadFileReportError(msg); // 触发异步报告生成任务或下载报告失败，均提示错误
				return;
			}
			setCurrentRequestId(reportAsync.ResultId);
			taskTimer = setInterval(() => {
				setFlag(flag + 1);
				getResultAsync(reportAsync.ResultId);
			}, 2000);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			setdownloadFileReportError(msg); // 触发异步报告生成任务或下载报告失败，均提示错误
		}
	};

	// 请求Excel异步下载
	const handleReportAsync1 = async (taskId, MapUUId) => {
		try {
			const customerNameData = await getCustomerName({
				AppId: Number(match.params.appid),
			});
			if (customerNameData.Error) {
				const msg = customerNameData.Error.Message || '';
				message.error({ content: msg });
				setIsLoading(true);
				setLoadingMsg(msg);
				return;
			}

			const params = {
				Id: -1,
				Type: 'Group',
				TaskType: 'mapTaskType',
				TaskId: taskId,
				AppId: Number(match.params.appid),
				Uin: `${customerNameData.Uin}`,
				CloudMapUuid: MapUUId,
			};

			const reportAsync = await getReportFileAsync(params);

			if (reportAsync.Error) {
				const msg = reportAsync.Error.Message || '';
				message.error({ content: msg });
				return;
			}

			if (currentReportStatusQueue.find(report => taskId === report.TaskId)) {
				const index = currentReportStatusQueue.findIndex(report => taskId === report.TaskId);
				currentReportStatusQueue[index] = {
					TaskId: taskId,
					ReportId: reportAsync.ResultId || '',
					CosUrl: '',
					TaskStatus: '',
				};
			} else {
				currentReportStatusQueue = [
					...currentReportStatusQueue,
					{
						TaskId: taskId,
						ReportId: reportAsync.ResultId || '',
						CosUrl: '',
						TaskStatus: '',
					},
				];
			}
			let reportTimer;
			getResultAsync1(reportAsync.ResultId, currentReportStatusQueue, reportTimer);

			reportTimer = setInterval(() => {
				getResultAsync1(reportAsync.ResultId, currentReportStatusQueue, reportTimer);
			}, 2000);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	// 获取Excel异步下载结果
	const getResultAsync1 = async (id, currentReportStatusQueue, reportTimer) => {
		try {
			const reportResult = await getReportResultAsync({
				AppId: Number(match.params.appid),
				ResultId: id,
			});
			if (reportResult.Error) {
				const msg = reportResult.Error.Message || '';
				message.error({ content: msg });
				return;
			}

			const currentReport = currentReportStatusQueue.find(report => report.ReportId === id) || {};

			currentReport.CosUrl = reportResult.CosUrl || '';
			currentReport.CosUrlPdf = reportResult.CosUrlPdf || '';
			currentReport.TaskStatus = reportResult.TaskStatus || '';
			if (
				reportTimer
				&& currentReportStatusQueue.every(report => report.TaskStatus === 'success' || report.TaskStatus === 'failed')
			) {
				clearInterval(reportTimer);
				setReportStatusQueue1([...currentReportStatusQueue]);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	// 下载报告
	const getResultAsync = async (requestId) => {
		try {
			const reportResult = await getReportResultAsync({
				AppId: Number(match.params.appid),
				ResultId: requestId,
			});
			if (reportResult.Error) {
				setDownStatus('failed');
				const msg = reportResult.Error.Message || '';
				message.error({ content: msg });
				setdownloadFileReportError(msg); // 触发异步报告生成任务或下载报告失败，均提示错误
				return;
			}
			if (reportResult.TaskStatus === 'success') {
				setCosUrl({ CosUrl: reportResult.CosUrl, CosUrlPdf: reportResult.CosUrlPdf });
				setDownStatus('success');

				clearInterval(taskTimer);
			} else if (reportResult.TaskStatus === 'failed') {
				setDownStatus('failed');
				message.error({ content: '生成报告失败' });
				clearInterval(taskTimer);
			} else {
				if (flag > 10) {
					clearInterval(taskTimer);
				}
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			setdownloadFileReportError(msg); // 触发异步报告生成任务或下载报告失败，均提示错误
		}
	};

	const assessmentLabelsList = useMemo(
		() => [
			// {
			// 	id: 'overview',
			// 	label: <AdvisorLable id="overview" groupSummary={groupSummary} />,
			// },
			{
				id: 'security',
				label: <AdvisorLable id="security" groupSummary={groupSummary} />,
			},
			{
				id: 'architecture',
				label: <AdvisorLable id="architecture" groupSummary={groupSummary} />,
			},
			{
				id: 'performance',
				label: <AdvisorLable id="performance" groupSummary={groupSummary} />,
			},
			{
				id: 'cost',
				label: <AdvisorLable id="cost" groupSummary={groupSummary} />,
			},
			{
				id: 'resource',
				label: <AdvisorLable id="resource" groupSummary={groupSummary} />,
			},
		],
		[groupSummary]
	);
	function genTooltip(series) {
		const [{ title, value }] = series;
		const timeValue = '时间';
		const riskNumberDescribe = '风险数量';

		return `<span>${timeValue}: ${title}</span></br><span>${riskNumberDescribe}: ${value}</span>`;
	}
	function changeProductSummariesAll() {
		setCurrentProductSummaries(productSummariesAll);
	}
	function changeProductSummaries() {
		setCurrentProductSummaries(productSummaries);
	}
	// 策略配置映射
	const strategiesConfig = {
		overview: overviewStrategies,
		security: securityStrategies,
		architecture: architectStrategies,
		performance: performanceStrategies,
		cost: costStrategies,
		resource: resourceStrategies,
	};

	// 外部服务报告列表
	const InspectionContent = (
		<div>
			<br />
			<Card>
				<Card.Body
					title={
						<>
							<span style={{ fontSize: 20 }}>概览</span>
							<Text style={{ display: 'inline-flex', alignItems: 'center' }}>
								{reportStatusQueue && (
									<AdvisorGroupReport
										id=""
										AppId={Number(match.params.appid)}
										type="Group"
										env={env}
										currentTaskId={currentTaskId}
										TemplateOptions={TemplateOptions}
										Products={Products}
										ProductsOptions={ProductsOptions}
										Groups={Groups}
										AllGroups={AllGroups}
										reportStatusQueue={reportStatusQueue}
										onChangeReportAsync={handleReportAsync}
										getTemplatesList={getTemplatesInfo}
										downStatus={downStatus}
										cosUrl={cosUrl}
									></AdvisorGroupReport>
								)}
							</Text>
						</>
					}
				>
					<div className={'intlc-survey-overview'}>
						<div className={'intlc-survey-date'}>
							<span>{'AppID'}：</span>
							<span>{match.params.appid}</span>
							<span>&nbsp;&nbsp;</span>
							<span>{'客户名称'}：</span>
							<span>{customerName}</span>
							<span>&nbsp;&nbsp;</span>
							<br />
							<span>{'最近一次评估'}：</span>
							<span>{lastTaskTime}</span>
						</div>

						<div className="intlc-assessment-layout">
							<section>
								<Tabs className="intlc-assessment-tabs" tabs={assessmentLabelsList}></Tabs>
							</section>
						</div>
					</div>
				</Card.Body>
			</Card>
			<Card>
				<Card.Body title={'云产品评估'}>
					<Table
						records={currentProductSummaries}
						columns={[
							{
								key: 'productName',
								header: '产品名',
							},
							{
								key: 'HighRiskCount',
								header: () => (
									<p>
										<span style={{ color: '#e54545' }}>{'高风险'}</span>
									</p>
								),
							},
							{
								key: 'MediumRiskCount',
								header: () => (
									<p>
										<span style={{ color: '#ff9d00' }}>{'中风险'}</span>
									</p>
								),
							},
							{
								key: 'Count',
								header: '资源数',
							},
							{
								key: 'RiskRate',
								header: () => (
									<>
										{'风险率'}
										<Bubble content={'风险率=当前风险数/(资源数*已开启资源型巡检项)'}>
											<Icon type="help" />
										</Bubble>
									</>
								),
								render: x => `${(x.RiskRate * 100).toString().match(/^\d+(?:\.\d{0,2})?/)}%`,
							},
							{
								key: 'StrategyCount',
								header: '已开启评估策略数',
							},
						]}
						addons={[
							autotip({
								emptyText: '没有数据',
							}),
						]}
					></Table>
					{currentProductSummaries.length !== 0 && currentProductSummaries.length === 5 && (
						<div className={'intlc-survey-more'}>
							<Button
								type="primary"
								className={'intlc-survey-more-btn'}
								onClick={changeProductSummariesAll}
							>
								{'查看更多'}
							</Button>
						</div>
					)}
					{currentProductSummaries.length !== 0 && currentProductSummaries.length !== 5 && (
						<div className={'intlc-survey-more'}>
							<Button type="primary" className={'intlc-survey-more-btn'} onClick={changeProductSummaries}>
								{'收起'}
							</Button>
						</div>
					)}
				</Card.Body>
			</Card>

			<div className="intlc-survey-summary">
				<Card>
					<Card.Body title={'风险评估Top5'}>
						<Table
							records={strategyTopSummaries}
							columns={columns}
							addons={[
								autotip({
									emptyText: '没有数据',
								}),
							]}
						></Table>
					</Card.Body>
				</Card>

				<Card className="app-international-code-card">
					<Card.Body title={'风险分布'}>
						<BasicPie
							circle
							height={300}
							dataSource={riskPie}
							position="value"
							color="type"
							dataLabels={{
								enable: true,
								formatter: (value, index, data) => `${data.serieName}: ${data.percent}%`,
							}}
							legend={{
								align: 'right',
							}}
						/>
					</Card.Body>
				</Card>
			</div>

			<Card>
				<Card.Body
					title={'风险趋势'}
					// subtitle={
					// 	<Bubble content={t('风险数量 = 资源数 * 已开启的评估项')}>
					// 		<Icon type="help" />
					// 	</Bubble>
					// }
				>
					<BasicLine
						smooth
						height={250}
						position="time*value"
						dataSource={riskDays}
						color="key"
						tooltip={{
							enableSort: true,
							header: { typeText: '类型', valueText: '风险数量' },
						}}
					/>
				</Card.Body>
			</Card>
		</div>
	);

	const tabDom = GroupRiskCountSet => GroupRiskCountSet.map((j, index) => {
		const risk = {
			[tabNameMap.get(j.GroupId)]: {
				high: j.HighRiskCount,
				medium: j.MediumRiskCount,
				none: j.NoRiskCount,
			},
		};
		return {
			key: index,
			id: j.GroupId,
			label: <AdvisorLable id={tabNameMap.get(j.GroupId)} groupSummary={risk} />,
		};
	});

	// 云架构报告列表
	const archContent = (
		<div className="archWrap">
			<Card>
				<Card.Body>
					<div className="archContent">
						{architectureList.map((i, index) => (
							<div className="itemWrap" key={index}>
								<div className="infoWrap">
									<div className="titleWrap">
										<div className="title">
											架构名称：
											<Text overflow tooltip verticalAlign="top" className="titleVal">
												{i.MapName}
											</Text>
										</div>
									</div>
									<div>
										<div className="user">
											创建用户：
											<Text overflow tooltip verticalAlign="top" className="userName">
												{i.Creator}
											</Text>
										</div>
										<div className="time">
											<span>创建时间：</span> {i.CreateTime}
										</div>
									</div>
									<div>
										<div className="user">
											更新用户：
											<Text overflow tooltip verticalAlign="top" className="userName">
												{i.UpdateUser}
											</Text>
										</div>
										<div className="time">
											<span>最新更新时间：</span> {i.UpdateTime}
										</div>
									</div>
								</div>
								<div className="imgWrap">
									{/* <Tabs className="intlc-assessment-tabs" tabs={tabDom(i.GroupRiskCountSet)}></Tabs> */}
									<div
										className="svgFile"
										dangerouslySetInnerHTML={{
											__html: i.SvgFile ? i.SvgFile : iconSvg['svg-blank'],
										}}
									/>
								</div>
								<div className="btnWrap">
									<AdvisorStrategyReport
										taskId={i.TaskId}
										MapUUId={i.MapUUId}
										reportStatusQueue={reportStatusQueue1}
										onChangeReportAsync={handleReportAsync1}
									></AdvisorStrategyReport>
								</div>
							</div>
						))}
					</div>
				</Card.Body>
			</Card>
		</div>
	);
	return (
		<Content className="intlc-survey-content intlc-stack-fullscreen intlc-stack-has-min-width">
			<Content.Header
				showBackButton
				onBackButtonClick={() => {
					history.push('/advisor/assess');
				}}
			/>
			<Content.Body className={env === 'public' ? '' : 'summaryContent'}>
				<Card>
					<Card.Body
						title={
							<>
								<div>
									<span style={{ fontSize: 20 }}>查询报告条件</span>
									<hr />
									<Form style={{ marginTop: 20 }} layout={'default'}>
										<Form.Item label="报告类型：">
											<section>
												<Radio.Group
													value={env}
													onChange={(value) => {
														setEnv(value);
													}}
												>
													<Radio name="public">云巡检报告</Radio>
													{/* <Radio name="public">云巡检报告（原外部服务报告）</Radio> */}
													{/* <Radio name="private">内部服务报告<b style={{ color: 'red' }}>（内部服务报告不能直接给到客户！）</b></Radio> */}
													{/* <Radio name="cloud">云架构报告</Radio> */}
												</Radio.Group>
											</section>
											<Modal
												visible={visableWarning}
												onClose={() => setVisableWarning(false)}
												caption="警告"
											>
												<Modal.Body>
													<div style={{ fontSize: 16, color: 'red' }}>
														<b>内部服务报告不能直接给到客户!</b>
													</div>
												</Modal.Body>
												<Modal.Footer>
													<Button
														type="primary"
														onClick={(e) => {
															setVisableWarning(false);
														}}
													>
														确定
													</Button>
												</Modal.Footer>
											</Modal>
										</Form.Item>
										<Form.Item
											label={
												<>
													报告日期
													<Bubble
														content={
															'时间选择范围为30天内；若日期为空，则默认拉取最近一次的扫描报告。'
														}
													>
														<Icon type="help" />
													</Bubble>
													：
												</>
											}
										>
											<DatePicker
												defaultValue={moment(reportDate)}
												range={[moment(new Date(new Date().getTime() + new Date().getTimezoneOffset() * 60 * 1000 + 8 * 60 * 60 * 1000)).subtract(30, 'd')
													.startOf('d'), moment(new Date(new Date().getTime() + new Date().getTimezoneOffset() * 60 * 1000 + 8 * 60 * 60 * 1000)).endOf('d')]}
												onChange={(value) => {
													if (value) {
														setReportDate(value.format('YYYY-MM-DD'));
													} else {
														setReportDate('');
													}
												}}
												// onOpenChange={open => console.log(open ? "open" : "close")}
												clearable
											/>
										</Form.Item>
									</Form>
									{/* <div className="reportTip"> */}
									{/* 	原有内部服务报告会逐步下线，如果有需要请联系fisherhhyu（余浩瀚）获取 */}
									{/* </div> */}
								</div>
							</>
						}
					></Card.Body>
				</Card>

				{isLoading ? (
					<div>
						<br />
						<Card>
							<Card.Body title={loadingMsg}></Card.Body>
						</Card>
					</div>
				) : env === 'public' ? (
					InspectionContent
				) : (
					archContent
				)}
			</Content.Body>
		</Content>
	);
}
