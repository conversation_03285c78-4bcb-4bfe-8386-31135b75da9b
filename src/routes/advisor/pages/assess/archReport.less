.reportTip {
	font-size: 12px;
	color: '#888';
	position: relative;
	top: 20px;
}
.summaryContent {
	flex: 1;
	overflow: hidden;
	.tea-layout__content-body-inner {
		height: 100%;
		display: flex;
		flex-direction: column;
		&>div {
			width: 100%;
		}
		.archWrap {
			margin-top: 20px;
			flex: 1;
			overflow: hidden;
			.tea-card,
			.tea-card__body,
			.tea-card__content {
				height: 100%;
			}
			.archContent {
				overflow: auto;
				height: 100%;
				display: flex;
				flex-wrap: wrap;
				&::-webkit-scrollbar {
					width: 2px;
					height: 6px;
				}
				&::-webkit-scrollbar-thumb {
					background: #888;
					border-radius: 20px;
				}
				&::-webkit-scrollbar-track {
					border-radius: 20px;
				}
				.itemWrap {
					width: calc((100% - 70px) / 4);
					height: 372px;
					box-sizing: border-box;
					margin: 8px;
					border: 1px solid #e8eaee;
					cursor: pointer;
					min-width: 392px;
					padding: 20px 20px 20px 16px;
					.imgWrap {
						width: 100%;
						height: 200px;
						margin: 15px 0;
						border:1px solid #F3F4F7;
						box-sizing: border-box;
						overflow: hidden;
						.svgFile {
							width: 100%;
							height: 100%;
							svg {
								width: 100%;
								height: 100%;
								overflow: visible;
							}
						}
					}
					.infoWrap {
						&>div {
							font-family: PingFang SC;
							font-size: 12px;
							font-weight: 400;
							line-height: 20px;
							letter-spacing: 0px;
							text-align: left;
							display: flex;
							color: #00000099;
							.user {
								display: flex;
								align-items: center;
								margin-right: 16px;
								width: 35%;
								.userName {
									width: calc(100% - 60px)
								}
							}
							.time {
								span {
									display: inline-block;
									width: 86px;
								}
							}
						}
						.titleWrap {
							margin: 5px 0 10px 0;
							display: flex;
							justify-content: space-between;
							.title {
								font-family: PingFang SC;
								font-size: 16px;
								font-weight: 500;
								line-height: 22px;
								letter-spacing: 0px;
								white-space: nowrap;
								width: calc(100% - 130px);
								color: #000000e5;
								.titleVal {
									width: 74%;
								}
							}
							.downloadBtn {
								font-size: 16px;
								font-weight: 500;
								color: #006eff;
							}
						}
					}
				}
			}
		}
	}
}