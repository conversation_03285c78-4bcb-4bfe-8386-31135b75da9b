import React, { useState, useEffect, useContext, useMemo } from 'react';
import { app } from '@tencent/tea-app';
// import { t, span, lng } from '@tencent/tea-app/i18n';
import { useHistory } from '@tea/app';
import { Link } from 'react-router-dom';
import {
	getConfig,
	createScanTask,
	getLastTask,
	getTaskResultSummary,
	getTaskProgressSummary,
	getRegionCodes,
} from '@src/api/advisor/estimate';
import { Modal } from '@tencent/tea-component';
import { toUTCtime } from '@src/utils/toUTCtime';
import { getStorage, setStorage, removeStorage } from '@src/utils/storage';
import { debounce } from 'lodash';
import { Layout } from '@tea/component/layout';
import { Alert } from '@tea/component/alert';
import { Button } from '@tea/component/button';
import { message } from '@tea/component/message';
import { Justify } from '@tencent/tea-component/lib/justify';
import { ExternalLink } from '@tencent/tea-component/lib/link';
import { SearchBox } from '@tencent/tea-component/lib/searchbox';
import { Card } from '@tencent/tea-component/lib/card';
import { Tabs, TabPanel } from '@tencent/tea-component/lib/tabs';
import { Text } from '@tencent/tea-component/lib/text';
import { Form } from '@tencent/tea-component/lib/form';
import { Icon } from '@tencent/tea-component/lib/icon';
import { Guide } from '@tencent/tea-component/lib/guide';
import { AdvisorPanel } from '../../estimate/AdvisorPanel';
import { ConfigPanel } from '../../estimate//ConfigPanel';
import { EmptyPanel } from '../../estimate//EmptyPanel';
import { AdvisorLable } from '../../estimate//AdvisorLable';
import moment from 'moment';
import classNames from 'classnames';
import { NotPermission } from '@src/routes/NotPermission';


const { Body, Content } = Layout;

export function AssessResult({ match }) {
	const [startState, setStartState] = useState(false);
	// 业务连续评估总览策略集合
	const [overviewStrategies, setOverviewStrategies] = useState([]);
	// 安全策略集合
	const [securityStrategies, setSecurityStrategies] = useState([]);
	// 可靠策略集合
	const [architectStrategies, setArchitectStrategies] = useState([]);
	// 性能策略集合
	const [performanceStrategies, setPerformanceStrategies] = useState([]);
	// 成本策略集合
	const [costStrategies, setCostStrategies] = useState([]);
	// 服务限制策略集合
	const [resourceStrategies, setResourceStrategies] = useState([]);
	// 当前任务ID
	const [currentTaskId, setCurrentTaskId] = useState('');
	// 当前评估时间
	const [currentTaskTime, setCurrentTaskTime] = useState(0);
	// 策略搜索输入值
	const [strategyInputValue, setStrategyInputValue] = useState('');
	// 策略组任务结果概览
	const [groupSummary, setGroupSummary] = useState({});
	// 策略任务结果概览
	const [strategySummary, setStrategySummary] = useState({});
	// TIC角色授权状态
	const [isTICAuthorized, setIsTICAuthorized] = useState(false);
	// CAM角色授权状态
	const [isCAMAuthorized, setIsCAMAuthorized] = useState(false);
	// 下载报告状态队列池中是否有正在执行的任务
	const [isReportRunning, setIsReportRunning] = useState(false);
	// 策略任务评估进度百分比
	const [taskProgress, setTaskProgress] = useState(0);
	// 策略任务预估剩余时间
	const [taskEstimatedTime, setTaskEstimatedTime] = useState(-1);
	// 是否首屏获取上一次策略任务中
	const [lastTaskStatus, setLastTaskStatus] = useState(false);
	// 当前策略评估新手引导步骤
	const [strategyCurrent, setStrategyCurrent] = useState(-1);
	// 获取获取当前的最新任务ID loading  默认开启loading，获取成功后取消loading
	const [currentTaskloading, setCurrentTaskloading] = useState(true);
	// 地区对应编码
	const [regionCodes, setRegionCodes] = useState([]);
	const history = useHistory();
	//页面权限状态
	const [permission, setPermission] = useState(0);  //0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	//从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		let menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			//判断是否存在
			let tmp = menuItems.filter(i => { if (history.location.pathname.includes(i.route)) { return i } })
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	}
	//持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);
	// 查询任务结果定时器
	let taskTimer;

	// 策略组名称映射
	const tabNameMap = new Map([
		[-1, 'overview'],
		[1, 'security'],
		[2, 'architecture'],
		[3, 'resource'],
		[4, 'cost'],
		[5, 'performance'],
	]);

	const assessmentLabelsList = useMemo(
		() => [
			{
				id: 'overview',
				label: <AdvisorLable id="overview" groupSummary={groupSummary} />,
			},
			{
				id: 'security',
				label: <AdvisorLable id="security" groupSummary={groupSummary} />,
			},
			{
				id: 'architecture',
				label: <AdvisorLable id="architecture" groupSummary={groupSummary} />,
			},
			{
				id: 'performance',
				label: <AdvisorLable id="performance" groupSummary={groupSummary} />,
			},
			{
				id: 'cost',
				label: <AdvisorLable id="cost" groupSummary={groupSummary} />,
			},
			{
				id: 'resource',
				label: <AdvisorLable id="resource" groupSummary={groupSummary} />,
			},
		],
		[groupSummary]
	);

	const assessmentTabsList = [
		{
			id: 'overview',
		},
		{
			id: 'security',
		},
		{
			id: 'architecture',
		},
		{
			id: 'performance',
		},
		{
			id: 'cost',
		},
		{
			id: 'resource',
		},
	];

	// 策略配置映射
	const strategiesConfig = {
		overview: overviewStrategies,
		security: securityStrategies,
		architecture: architectStrategies,
		performance: performanceStrategies,
		cost: costStrategies,
		resource: resourceStrategies,
	};

	// 查询TIC角色授权状态
	// const getTICRoleAuthorizationStatus = async () => {
	// 	try {
	// 		const roleStatus = await getRoleStatus();
	// 		if (roleStatus.Error) {
	// 			const message = roleStatus.Error.Message || '';
	// 			////app.tips.error(message);
	// 			return;
	// 		}

	// 		if (roleStatus && roleStatus.Status) {
	// 			setIsTICAuthorized(true);
	// 		} else {
	// 			setIsTICAuthorized(false);
	// 		}

	// 		if (roleStatus && roleStatus.RoleExist) {
	// 			setIsCAMAuthorized(true);
	// 		} else {
	// 			setIsCAMAuthorized(false);
	// 		}

	// 		// 未授权情况下强制跳转授权页面
	// 		if (!roleStatus.Status || !roleStatus.RoleExist) {
	// 			history.push('/advisor/auth');
	// 		}
	// 	} catch (err) {
	// 		const message = err.msg || err.toString() || t('未知错误');
	// 		////app.tips.error(t('{{message}}', { message }));
	// 	}
	// };

	// 获取平台的策略配置信息
	const getConfigList = async () => {
		try {
			const config = await getConfig({
				AppId: Number(match.params.appid),
			});
			if (config.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (config.Error.Code === 401) {
					setPermission(2);
				} else {
					message.error({ content: config.Error.Message });
				}
				return
			}

			let ovStrategies = [];

			config.Groups.forEach((group) => {
				const currentStrategies = group.Strategies || [];
				switch (group.GroupId) {
					case 1:
						setSecurityStrategies(currentStrategies);
						break;
					case 2:
						setArchitectStrategies(currentStrategies);
						break;
					case 3:
						setResourceStrategies(currentStrategies);
						break;
					case 4:
						setCostStrategies(currentStrategies);
						break;
					case 5:
						setPerformanceStrategies(currentStrategies);
						break;
					default:
						break;
				}
				ovStrategies = [...ovStrategies, ...currentStrategies];
			});
			setOverviewStrategies(ovStrategies);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			////app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	// 获取当前的最新任务ID
	const getCurrentTask = async () => {
		setLastTaskStatus(true);

		try {
			const lastTask = await getLastTask({
				AppId: Number(match.params.appid),
				Env: '',
			});
			if (lastTask.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (lastTask.Error.Code === 401) {
					setPermission(2);
				} else {
					message.error({ content: lastTask.Error.Message });
				}
				return
			}
			if (!lastTask.Has) {
				// const createTask = await createScanTask({
				// 	AppId: Number(match.params.appid),
				// });
				// if (createTask.Error) {
				// 	const msg = createTask.Error.Message || '';
				// 	////app.tips.error(message);
				// 	message.error({ content: msg });
				// 	return;
				// }
				setStorage('ADVISOR_LAST_TASK_TIME', '');
				setCurrentTaskId('');
				//无最新任务，取消loading
				setCurrentTaskloading(false);
			} else {
				setCurrentTaskId(lastTask.TaskId);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			////app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	// 开始评估创建任务ID
	const createCurrentTask = async () => {
		setCurrentTaskTime(0);
		setCurrentTaskId('');
		setGroupSummary({});
		setTaskProgress(0);
		setTaskEstimatedTime(-1);

		try {
			const createTask = await createScanTask({
				AppId: Number(match.params.appid),
				Env: '',
			});
			if (createTask.Error) {
				const msg = createTask.Error.Message || '';
				////app.tips.error(message);
				message.error({ content: msg });
				return;
			}
			setCurrentTaskId(createTask.TaskId);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			////app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	// 查询当前任务执行结果和进度概览
	const getCurrentTaskSummary = async () => {
		if (!currentTaskId) {
			return;
		}

		try {
			// 任务执行结果概览
			const taskResultSummary = await getTaskResultSummary({
				TaskId: currentTaskId,
				AppId: Number(match.params.appid),
			});

			// 首屏第一次发出Summary请求后，加载中状态变为评估中状态
			if (lastTaskStatus) {
				setLastTaskStatus(false);
			}

			if (taskResultSummary.Error) {
				const msg = taskResultSummary.Error.Message || '';
				////app.tips.error(message);
				message.error({ content: msg });
				return;
			}

			// 任务执行进度概览
			const taskProgressSummary = await getTaskProgressSummary({
				TaskId: currentTaskId,
				AppId: Number(match.params.appid),
			});
			if (taskProgressSummary.Error) {
				const msg = taskProgressSummary.Error.Message || '';
				////app.tips.error(message);
				message.error({ content: msg });
				return;
			}

			if (taskResultSummary.GroupSummaries && taskResultSummary.GroupSummaries.length > 0) {
				const currentGroupSummary = {};
				taskResultSummary.GroupSummaries.forEach((summary) => {
					currentGroupSummary[tabNameMap.get(summary.GroupId)] = {
						high: summary.HighRiskCount,
						medium: summary.MediumRiskCount,
						low: summary.LowRiskCount,
						none: summary.NoRiskCount,
					};
				});
				setGroupSummary(currentGroupSummary);
			}

			if (taskResultSummary.StrategySummaries && taskResultSummary.StrategySummaries.length > 0) {
				const currentStrategySummary = {};
				taskResultSummary.StrategySummaries.forEach((summary) => {
					currentStrategySummary[summary.StrategyId] = {
						high: summary.HighRiskCount,
						medium: summary.MediumRiskCount,
						low: summary.LowRiskCount,
						none: summary.NoRiskCount,
						ignore: summary.IgnoredInstanceCount,
						status: summary.Code,
					};
				});
				setStrategySummary(currentStrategySummary);
			}

			if (taskResultSummary.FinishTime.length > 0) {
				const finishTimeStamp = new Date(taskResultSummary.FinishTime).getTime() / 1000;
				setCurrentTaskTime(finishTimeStamp);
				//获取最新任务时间成功，取消loading
				setCurrentTaskloading(false);
				setStorage('ADVISOR_LAST_TASK_TIME', finishTimeStamp);
			}

			// 开启新手引导
			if (taskResultSummary.FinishTime.length > 0) {
				if (!!+getStorage('ADVISOR_ASSESS_GUIDE_STATUS')) {
					setStrategyCurrent(-1);
				} else {
					setStrategyCurrent(0);
					setStorage('ADVISOR_ASSESS_GUIDE_STATUS', '1');
				}
			}

			if (taskProgressSummary.Finished) {
				// 进度依赖Summary执行完成，完成前都为99%
				setTaskProgress(99);
				setTaskEstimatedTime(0);
			} else {
				const currentTaskProgress = taskProgressSummary.Overview.TotalCount
					? Math.floor(
						(taskProgressSummary.Overview.ScannedCount / taskProgressSummary.Overview.TotalCount) * 100
					)
					: 0;
				const currentTaskEstimatedTime = taskProgressSummary.EstimatedTime;

				setTaskProgress(currentTaskProgress);
				setTaskEstimatedTime(currentTaskEstimatedTime);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			////app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	// 当前策略是否已全部关闭
	// xxx
	// 获取地区对应编码
	const getCodes = async () => {
		try {
			// if (regionCodes.length === 0) {
			const codes = await getRegionCodes({
				AppId: Number(match.params.appid),
			});
			if (codes.Error) {
				//判断页面权限,401表示无权限，其他状态码表示接口报错
				if (codes.Error.Code === 401) {
					setPermission(2);
				} else {
					message.error({ content: codes.Error.Message });
				}
				return
			}

			setRegionCodes(codes.Codes);
			// }
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		getCodes();
	}, []);

	useEffect(() => {
		// getTICRoleAuthorizationStatus();
		getConfigList();
	}, []);

	useEffect(() => {
		getCurrentTask();
	}, []);

	// 轮询获取当前任务执行结果
	useEffect(() => {
		// 立即执行一次
		if (!currentTaskTime) {
			getCurrentTaskSummary();
		}
		taskTimer = setInterval(() => {
			getCurrentTaskSummary();
		}, 5000);

		if (taskTimer && currentTaskTime) {
			clearInterval(taskTimer);
		}

		return () => {
			clearInterval(taskTimer);
		};
	}, [currentTaskId, currentTaskTime]);

	// 任务执行完成状态
	const taskCompleted = useMemo(() => currentTaskTime > 0, [currentTaskTime]);

	// 策略任务预估剩余时间格式化
	const taskEstimatedDate = useMemo(() => {
		return taskEstimatedTime === -1 ? '-' : taskEstimatedTime / 60 >= 1 ? Math.round(taskEstimatedTime / 60) : 1;
	}, [taskEstimatedTime]);
	const checkAgain = async () => {
		const yes = await Modal.confirm({
			message: '确认发起评估任务么？',
			icon: 'warning',
			description:
				'警告：本数据数据不能直接给用户，仅提供内部使用，如需要用户数据时，请指引用户在腾讯云租户端页面发起扫描获取',
			okText: '发起',
			cancelText: '取消',
			onOk: () => {
				createCurrentTask();
			},
		});
	};

	return (
		<Body>
			{permission === 0 ? <div></div> : <div>{permission === 2 ? <NotPermission /> :
				<Content className="intlc-stack-content intlc-stack-fullscreen intlc-stack-has-min-width">
					<Content.Header
						title={'评估结果'}
					// operation={
					// 	// 中国站已上线帮助文档，国际站待上线

					// 	<ExternalLink href="https://cloud.tencent.com/document/product/1264/46690">
					// 		<span>帮助文档</span>
					// 	</ExternalLink>
					// }
					/>
					<Content.Body>
						<Justify
							left={
								<>
									<Text
										theme="strong"
										verticalAlign="top"
										className="intlc-assessment-label"
										style={{ marginRight: 20 }}
									>{`APPID: ${match.params.appid}`}</Text>
									<Text theme="label" verticalAlign="top" className="intlc-assessment-label">
										<span>最近评估时间</span>：
										{/* 正在查询时，一律显示 - */}
										{currentTaskloading ? <>-</> : <></>}
										{/* 查询结束，任务id存在，任务时间为空，说明任务仍在执行，显示 - */}
										{!currentTaskloading && currentTaskId && !currentTaskTime ? <>-</> : <></>}
										{/* 查询结束，任务id存在，任务时间非空，说明任务已完成，显示完成时间 */}
										{!currentTaskloading && currentTaskId && currentTaskTime ? toUTCtime(currentTaskTime) : <></>}
										{/* 查询结束，任务id不存在，显示无记录*/}
										{!currentTaskloading && !currentTaskId ? '无记录，请检查APPID是否正确' : <></>}
									</Text>
								</>
							}
							right={
								<>
									<section>
										<SearchBox
											onSearch={(value) => setStrategyInputValue(value)}
											disabled={!taskCompleted}
											placeholder={'输入评估项名称搜索'}
											style={{ width: '280px' }}
										/>
									</section>
								</>
							}
						/>
						<div className="intlc-assessment-layout">
							<Card>
								<Card.Body style={{ padding: 0 }}>
									<section>
										<Tabs
											className="intlc-assessment-tabs"
											tabs={assessmentLabelsList}
											tabBarRender={(children, tab) => <a>{children}</a>}
										>
											{assessmentTabsList.map((tab) => (
												// 未激活时强制渲染
												<TabPanel key={tab.id} id={tab.id} forceRender>
													{strategiesConfig[tab.id] && strategySummary ? (
														<AdvisorPanel
															regionCodes={regionCodes}
															AppId={Number(match.params.appid)}
															id={tab.id}
															env={''}
															currentTaskId={currentTaskId}
															Groups={{}}
															Products={{}}
															ProductsOptions={{}}
															strategyInputValue={strategyInputValue}
															taskCompleted={currentTaskTime === 0 && lastTaskStatus ? true : taskCompleted}
															detectionStrategies={strategiesConfig[tab.id]}
															strategySummary={strategySummary}
															onChangeReportRunning={setIsReportRunning}
														></AdvisorPanel>
													) : strategiesConfig[tab.id] ? (
														<ConfigPanel
															id={tab.id}
															detectionStrategies={strategiesConfig[tab.id]}
														></ConfigPanel>
													) : (
														<EmptyPanel id={tab.id}></EmptyPanel>
													)}
												</TabPanel>
											))}
										</Tabs>
									</section>
								</Card.Body>
							</Card>
						</div>
					</Content.Body>
				</Content>}</div>}
		</Body>
	);
}
