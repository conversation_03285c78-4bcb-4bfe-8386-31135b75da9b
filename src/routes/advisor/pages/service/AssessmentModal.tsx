import React from 'react';
import { Modal, Radio, Form, Button, Input, message, Text } from '@tencent/tea-component';
import { handleDescribeIsaHighAvailabilityServiceAssess } from "@src/api/advisor/service";
import { getStorage } from "@src/utils/storage";
import { useForm, useField } from 'react-final-form-hooks';


function getStatus (meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}
export function AssessmentModal({visible, handleVisible, info}) {
	const onSubmit = async (value)=>{
		try {
			await handleDescribeIsaHighAvailabilityServiceAssess({
				...value,
				OrderId: info.OrderId,
				UpdatePerson: getStorage('engName'),
				EvaluateResult: parseInt(value.EvaluateResult),
				ShowError: true,
				OnlyData: true
			});
			message.success({
				content: '操作成功'
			});
			handleVisible(true);
		} catch (err) {}
	};
	const { form, handleSubmit, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {
			EvaluateResult: '',
			EvaluateReason: ''
		},
		validate: (formInfo) => {
			return {
				EvaluateResult: !formInfo.EvaluateResult ? '请选择评估结果' : undefined,
				EvaluateReason: !formInfo.EvaluateReason ? '请填写评估理由' : undefined
			};
		},
	});
	const evaluateResultField = useField('EvaluateResult', form);
	const evaluateReasonField = useField('EvaluateReason', form);
	return (
		<Modal className={'service-modal'} visible={visible} caption="评估是否接受服务申请" onClose={
			()=>{
				handleVisible();
			}
		}>
			<form  onSubmit={handleSubmit}>
				<Modal.Body>
						<Form>
						<Form.Item label={'服务类型：'}>
							<Input
								className={'service-form-input'}
								value={info.ServerType}
								readonly={true}
							/>
						</Form.Item>
						<Form.Item label={'服务名称：'}>
							<Input
								className={'service-form-input'}
								value={info.OrderName}
								readonly={true}
							/>
						</Form.Item>
						<Form.Item label={'服务单号：'}>
							<Input
								className={'service-form-input'}
								value={info.OrderId}
								readonly={true}
							/>
						</Form.Item>
						<Form.Item label={'客户名称：'}>
							<Input
								className={'service-form-input'}
								value={info.CustomerName}
								readonly={true}
							/>
						</Form.Item>
						<Form.Item
							label={'评估结果：'}
							required={true}
							status={getStatus(evaluateResultField.meta, validating)}
							message={(getStatus(evaluateResultField.meta, validating) === "error" && evaluateResultField.meta.error)}
						>
							<Radio.Group {...evaluateResultField.input}>
								<Radio name="1">同意</Radio>
								<Radio name="2">拒绝</Radio>
							</Radio.Group>
						</Form.Item>
						<Form.Item
							label={'评估理由：'}
							required={true}
							status={getStatus(evaluateReasonField.meta, validating)}
							message={(getStatus(evaluateReasonField.meta, validating) === "error" && evaluateReasonField.meta.error)}
						>
							<Input.TextArea size={'l'}  {...evaluateReasonField.input}></Input.TextArea>
						</Form.Item>
					</Form>
				</Modal.Body>
				<Modal.Footer>
					<Button
						htmlType={'submit'}
						type="primary"
					>
						确定
					</Button>
					<Button type="weak" onClick={()=>{
						handleVisible();
					}}>
						取消
					</Button>
				</Modal.Footer>
			</form>
		</Modal>
	);
}
