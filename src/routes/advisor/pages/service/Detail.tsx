import React, { useState, useMemo } from 'react';
import { Table, Button, Row, Card, Layout, H4, Col, Select, Text, Input, List, Tag } from '@tencent/tea-component';
import { useHistory, useAegisLog } from '@tea/app';
import { getDescribeIsaHighAvailabilityServiceDetail, getDescribeCosDownloadUrl } from '@src/api/advisor/service';
import { DescribeIsaHighAvailabilityServiceDetailResData } from "@src/types/advisor/service";
import { getStorage } from "@src/utils/storage";

export function Detail(match) {
	const { Body, Content } = Layout;
	const history = useHistory();
	const aegis = useAegisLog();
	const [records, setRecords] = useState<DescribeIsaHighAvailabilityServiceDetailResData>({
		OrderName: '',
		OrderId: 0,
		CustomerInfo: '',
		OrderNeed: '',
		ExpectServiceTime: 0,
		InvolveProductList: [],
		Contact<PERSON>erson: '',
		ContactPhone: '',
		ContactEmail: '',
		OtherInformation: '',
		EvaluateResult: '',
		EvaluateReason: '',
		Attachment: []
	});

	const getTabData = async () => {
		// setLoading(true);
		try {
			const res = await getDescribeIsaHighAvailabilityServiceDetail({
				OrderId: parseInt(match.match.params.id),
				ShowError: true,
				OnlyData: true
			});
			setRecords(res);
			// setLoading(false);
		} catch (err) {
			// setLoading(false);
		}
	};
	useMemo(() => {
		getTabData();
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: '专家服务/详情页面',
			ext3: localStorage.getItem('engName')
		})
	}, []);
	const download = async (item) => {
		const res = await getDescribeCosDownloadUrl({
			OrderId: parseInt(match.match.params.id),
			ShowError: true,
			OnlyData: true,
			UpdatePerson: getStorage('engName'),
			AttachmentInfoList: [{
				AttachmentInstruction: '',
				AttachmentName: item.AttachmentName
			}]
		})
		const a = document.createElement('a');
		a.href = res.AttachmentInfoList[0].CosUrl;
		a.download = item.AttachmentName;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
	};
	return (<Body className={'service-detail'}>
		<Content.Header
			showBackButton
			title={`${records.OrderId}（${records.OrderName}）`}
			onBackButtonClick={() => {
				history.push('/advisor/service');
			}}
		></Content.Header>
		<Content.Body>
			<Card>
				<Card.Body>
					<Row>
						<Col span={4}>
							<Text theme="label">服务单名称：</Text>
						</Col>
						<Col>
							<Text>{records.OrderName}</Text>
						</Col>
					</Row>
					<Row>
						<Col span={4}>
							<Text theme="label">服务单号：</Text>
						</Col>
						<Col>
							<Text>{records.OrderId}</Text>
						</Col>
					</Row>
					<Row>
						<Col span={4}>
							<Text theme="label">客户APPID/UIN/客户名称：</Text>
						</Col>
						<Col>
							<Text>{records.CustomerInfo}</Text>
						</Col>
					</Row>
					<H4>容灾服务诉求</H4>
					<Row>
						<Col span={4}>
							<Text theme="label">容灾需求和目标：</Text>
						</Col>
						<Col>
							<Text>{records.OrderNeed}</Text>
						</Col>
					</Row>
					<Row verticalAlign={'middle'}>
						<Col span={4}>
							<Text theme="label">预估服务周期（天）：</Text>
						</Col>
						<Col>
							<Text>{records.ExpectServiceTime}</Text>
						</Col>
					</Row>
					<Row>
						<Col span={4}>
							<Text theme="label">涉及云产品：</Text>
						</Col>
						<Col>
							{
								records?.InvolveProductList?.map((el, i) => {
									return <Tag key={i}>{el}</Tag>;
								})
							}
						</Col>
					</Row>
					<H4>客户信息</H4>
					<Row>
						<Col span={4}>
							<Text theme="label">联系人：</Text>
						</Col>
						<Col>
							<Text>{records.ContactPerson}</Text>
						</Col>
					</Row>
					<Row>
						<Col span={4}>
							<Text theme="label">联系人电话：</Text>
						</Col>
						<Col>
							<Text>{records.ContactPhone}</Text>
						</Col>
					</Row>
					<Row>
						<Col span={4}>
							<Text theme="label">联系人邮箱：</Text>
						</Col>
						<Col>
							<Text>{records.ContactEmail}</Text>
						</Col>
					</Row>
					<H4>其他</H4>
					<Text theme={'label'}>{records.OtherInformation}</Text>
					<H4>评估是否接受申请</H4>
					<Row>
						<Col span={4}>
							<Text theme="label">评估结果：</Text>
						</Col>
						<Col>
							<Text>{records.EvaluateResult}</Text>
						</Col>
					</Row>
					<Row>
						<Col span={4}>
							<Text theme="label">评估理由：</Text>
						</Col>
						<Col>
							<Text>{records.EvaluateReason}</Text>
						</Col>
					</Row>
					<H4>附件</H4>
					{
						(records.Attachment?.length) > 0
							?
							records.Attachment?.map((item, i) => {
								return <Button
									style={{
										marginRight: '20px'
									}}
									onClick={() => {
										download(item);
									}}
									type={'link'}
									key={i}
								>
									{
										item.AttachmentName
									}
								</Button>
							})
							:
							<Text theme={'label'}>暂无</Text>
					}
				</Card.Body>
			</Card>
		</Content.Body>
	</Body>);
}