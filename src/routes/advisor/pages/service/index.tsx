import React, { useEffect, useMemo, useState } from 'react';
import { Layout, Card } from '@tencent/tea-component';
import { cloneDeep } from "lodash";
import './index.less';
import { getStorage } from "@src/utils/storage";
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory, useAegisLog } from '@tea/app';
import { getDescribeIsaHighAvailabilityServiceOrderList, handleDescribeIsaHighAvailabilityServiceDone } from "@src/api/advisor/service";
import { Button, Col, DatePicker, Input, Row, Select, StatusTip, Table, Text, message } from "@tea/component";
import { DescribeIsaHighAvailabilityServiceOrderListResParams, DescribeIsaHighAvailabilityServiceOrderListParams } from "@src/types/advisor/service";
import { Link } from "react-router-dom";
import moment from "moment";
import { reportVisitPage } from '@src/utils/report';
import { AssessmentModal } from "@src/routes/advisor/pages/service/AssessmentModal";
import { UpdateFileModal } from "@src/routes/advisor/pages/service/UpdateFileModal";

const initFilterData = [
	{
		label: 'APPID',
		value: 'AppId',
		type: 'input',
		span: [4, 20],
		virtualVal: ''
	},
	{
		label: '客户名称',
		value: 'CustomerName',
		type: 'input',
		span: [4, 20],
		virtualVal: ''
	}
];
const initParams = {
	Limit: 10,
	Offset: 0,
	OnlyData: true,
	ShowError: true,
	AppId: 0,
	CustomerName: ''
};
let timer;
export function Service() {
	const { Body, Content } = Layout;
	const history = useHistory();
	const aegis = useAegisLog();
	const ctxUser = localStorage.getItem('engName');

	const [permission, setPermission] = useState(0);
	const CheckPermission = () => {
		const menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			// 判断是否存在
			const tmp = menuItems.filter(i => {
				if (history.location.pathname.includes(i.route)) {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
		// 临时测试
		setPermission(1);
	};
	// 持续从localStorage获取菜单列表
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	const [records, setRecords] = useState<DescribeIsaHighAvailabilityServiceOrderListResParams>({
		ServiceDetailList: []
	});
	const [filterParams, setFilterParams] = useState<DescribeIsaHighAvailabilityServiceOrderListParams>({
		...cloneDeep(initParams)
	});
	const [filterData, setFilterData] = useState<any>([...cloneDeep(initFilterData)]);
	const [loading, setLoading] = useState(false);
	const { RangePicker } = DatePicker;
	const [assessmentModalVisible, setAssessmentModalVisible] = useState(false);
	const [updateFileModalVisible, setUpdateFileModalVisible] = useState(false);
	const [currentInfo, setCurrentInfo] = useState({});
	const [columns, setColumns] = useState<any>([
		{
			key: "ServerType",
			header: "服务类型"
		},
		{
			key: "OrderName",
			header: "服务名称",
			render: (item) => (
				<Link to={`/advisor/service/detail/${item.OrderId}`}>{item.OrderName}</Link>
			),
		},
		{
			key: "OrderId",
			header: "服务单号"
		},
		{
			key: "AppId",
			header: "APPID"
		},
		{
			key: "Uin",
			header: "UIN"
		},
		{
			key: "CustomerName",
			header: "客户名称"
		},
		{
			key: "CreateTime",
			header: "申请时间"
		},
		{
			key: "ExpectServiceTime",
			header: "预估服务周期(天)"
		},
		{
			key: "State",
			header: "申请状态"
		},
		{
			key: "UpdatePerson",
			header: "最后处理人"
		},
		{
			key: "UpdateTime",
			header: "最后操作时间"
		},
		{
			key: "AffectedNum1",
			header: "操作",
			fixed: 'right',
			render: (item) => {
				return (
					<>
						<Button
							type={'link'}
							onClick={() => {
								setAssessmentModalVisible(true);
								setCurrentInfo(item);
								aegis.reportEvent({
									name: 'Click',
									ext1: 'service-evaluation-btn',
									ext2: ctxUser,
									ext3: '专家服务'
								});
							}}
							disabled={item.State !== '已申请'}
						>评估</Button>
						<Button
							type={'link'}
							onClick={async () => {
								try {
									await handleDescribeIsaHighAvailabilityServiceDone({
										OrderId: item.OrderId,
										UpdatePerson: getStorage('engName'),
										ShowError: true,
										OnlyData: true
									});
									message.success({
										content: '操作成功'
									});
									getTabData(filterParams);
								} catch (err) { }
							}}
							disabled={item.State !== '已生效'}
						>完成</Button>
						<Button
							type={'link'}
							onClick={() => {
								setUpdateFileModalVisible(true);
								setCurrentInfo(item);
								aegis.reportEvent({
									name: 'Click',
									ext1: 'service-update-attach-btn',
									ext2: ctxUser,
									ext3: '专家服务'
								});
							}}
						>更新附件</Button>
					</>
				);
			},
		},
	]);
	const refreshAllParams = (name, val) => {
		setFilterParams({
			...filterParams,
			[name]: val
		});
	};
	// 分页
	const { pageable, scrollable } = Table.addons;
	const resetFilterData = () => {
		const list = cloneDeep(initFilterData);
		setFilterData([
			...list
		]);
	};
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeIsaHighAvailabilityServiceOrderList({
				...filterParams,
				AppId: filterParams.AppId == '' ? 0 : isNaN(parseInt(filterParams.AppId)) ? -1 : parseInt(filterParams.AppId)
			});
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	useMemo(() => {
		getTabData(filterParams);
		reportVisitPage({
			isaReportMeunName: '专家服务',
		});
	}, []);

	useEffect(() => {
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: '专家服务',
			ext3: ctxUser
		})
	}, [])
	return (
		<Body>{
			permission === 0
				?
				<div></div>
				:
				permission === 2
					?
					<NotPermission />
					:
					<>
						<Content.Header
							className={'service-header'}
							title={'专家服务-高可用服务'}
							subtitle={
								<div className="service-desc">
									<Text>
										【<Text theme={'primary'}>!!!</Text> <Text theme={'danger'}>下线通知</Text>】该功能将于<Text theme={'danger'}>10月31日</Text>【<Text theme={'danger'}>下线</Text>】，如有需求，请及时联系 lanlanzhang。
									</Text>
								</div>
							}
						></Content.Header>
						<Content.Body>
							<Card>
								<Card.Body className='fault-filter-wrap'>
									<Row>
										{
											filterData.map((item, i) => {
												return item.type != 'mutipleSelect' && (<Col span={12} key={i}>
													<Row verticalAlign={"middle"}>
														<Col span={item?.span[0]}>
															<Text theme="label" verticalAlign="middle">{item.label}</Text>
														</Col>
														<Col span={item?.span[1]}>
															{
																item.type == 'input' ?
																	<Input
																		size={'l'}
																		onChange={(val) => {
																			item.virtualVal = val;
																			setFilterData([
																				...filterData
																			]);
																			refreshAllParams(item.value, val);
																		}
																		}
																		value={item.virtualVal}
																		placeholder={item.placeholder}
																	/>
																	:
																	item.type == 'mutipleTime' ?
																		<RangePicker
																			showTime
																			value={item.virtualVal}
																			range={[
																				moment()
																					.subtract(365, "d")
																					.startOf("d"),
																				moment().endOf("d"),
																			]}
																			onChange={(val) => {
																				item.virtualVal = [...val];
																				setFilterData([
																					...filterData
																				]);
																				refreshAllParams(item.value, [
																					val[0].format("YYYY-MM-DD HH:mm:ss"),
																					val[1].format("YYYY-MM-DD HH:mm:ss")
																				]);
																			}
																			}
																		/>
																		:
																		item.type == 'select'
																			?
																			<Select
																				options={item.options}
																				appearance="button"
																				size='m'
																				value={item.virtualVal}
																				onChange={(val) => {
																					item.virtualVal = val;
																					setFilterData([
																						...filterData
																					]);
																					refreshAllParams(item.value, val);
																				}
																				}
																			></Select>
																			:
																			<></>
															}
														</Col>
													</Row>
												</Col>);
											})
										}
									</Row>
									<Row style={{
										justifyContent: 'center',
										margin: '20px 10px 10px 10px'
									}}>
										<Button type='primary' onClick={() => {
											setFilterParams({
												...filterParams,
												Offset: 0
											});
											getTabData({
												...filterParams,
												Offset: 0
											});
											aegis.reportEvent({
												name: 'Click',
												ext1: 'service-view-btn',
												ext2: ctxUser,
												ext3: '专家服务'
											});
										}}>查询</Button>
										<Button
											style={{
												marginLeft: '10px'
											}} onClick={() => {
												resetFilterData();
												setFilterParams({
													...cloneDeep(initParams)
												});
												getTabData({
													...cloneDeep(initParams)
												});
											}}>重置</Button>
									</Row>
								</Card.Body>
							</Card>
							<Card>
								<Card.Body>
									<Table
										records={
											records.ServiceDetailList ? records.ServiceDetailList : []
										}
										columns={columns}
										topTip={
											(loading || records.ServiceDetailList.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
										}
										addons={
											[
												pageable({
													recordCount: records.TotalCount ? records.TotalCount : 0,
													onPagingChange: ({ pageIndex, pageSize }) => {
														setFilterParams({
															...filterParams,
															Limit: pageSize,
															Offset: (pageIndex - 1) * pageSize
														});
														getTabData({
															...filterParams,
															Limit: pageSize,
															Offset: (pageIndex - 1) * pageSize
														});
													},
													pageSizeOptions: [10, 20, 30, 50, 100, 200],
													pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
													pageSize: filterParams.Limit
												}),
												scrollable({
													minWidth: 1800
												})
											]
										}
									/>
								</Card.Body>
							</Card>
						</Content.Body>
						{
							assessmentModalVisible && <AssessmentModal
								visible={assessmentModalVisible}
								handleVisible={
									(isRefresh) => {
										setAssessmentModalVisible(false);
										if (isRefresh) {
											getTabData(filterParams);
										}
									}
								}
								info={currentInfo}
							></AssessmentModal>
						}
						{
							updateFileModalVisible && <UpdateFileModal
								visible={updateFileModalVisible}
								handleVisible={
									(isRefresh) => {
										setUpdateFileModalVisible(false);
										if (isRefresh) {
											getTabData(filterParams);
										}
									}
								}
								itemInfo={currentInfo}
							></UpdateFileModal>
						}
					</>
		}
		</Body>
	);
}
