import React, { useState } from 'react';
import { Modal, Form, Button, Input, message, StatusTip, Table } from '@tencent/tea-component';
import { handleDescribeDeleteAttachment, getDescribeCosUploadUrl } from "@src/api/advisor/service";
import { getStorage } from "@src/utils/storage";
import { cloneDeep } from "lodash";

export function UpdateFileModal({visible, handleVisible, itemInfo}) {
	const [info, setInfo] = useState(cloneDeep(itemInfo));
	const [loading, setLoading] = useState(false);
	const [fileChange, setFileChange] = useState(false);
	const deleteFile = async (item)=>{
		try {
			await handleDescribeDeleteAttachment({
				OrderId: info.OrderId,
				AttachmentInfoList: [{
					AttachmentName: item.AttachmentName,
					AttachmentInstruction: item.AttachmentInstruction
				}],
				UpdatePerson: getStorage('engName'),
				ShowError: true,
				OnlyData: true
			});
			message.success({
				content: '删除成功'
			});
			info.AttachmentInfoList.forEach((el, i)=>{
				if (item.AttachmentName == el.AttachmentName) {
					info.AttachmentInfoList.splice(i, 1);
				}
			});
			setFileChange(true);
			setInfo(cloneDeep(info));
		} catch (err) {}
	};
	const ajaxSendFile = (url, file)=>{
		return new Promise((resolve, reject)=>{
			const xhr = new XMLHttpRequest();
			xhr.open("PUT", url);
			xhr.onload = function () {
				if (this.status == 200) {
					message.success({
						content: '上传成功'
					});
				}
				resolve('成功')
			};
			xhr.onerror = function () {
				message.error({
					content: '网络错误'
				});
				reject('失败');
				setLoading(false);
			};
			xhr.send(file);
		});
	};
	const upload = async (e) => {
		setLoading(true);
		const files = cloneDeep(Array.from(e.target.files));
		if (files.length == 0) {
			setLoading(false);
			return;
		}
		(document as  any).querySelector('.service-file').value = '';
		const attachmentInfoList = [];
		files.forEach((item: any)=>{
			attachmentInfoList.push({
				AttachmentInstruction: '',
				AttachmentName: item.name
			});
		});
		try {
			const res = await getDescribeCosUploadUrl({
				OrderId: info.OrderId,
				UpdatePerson: getStorage('engName'),
				AttachmentInfoList: attachmentInfoList,
				ShowError: true,
				OnlyData: true
			});
			const promiseList = [];
			files.forEach((item: any)=>{
				// const formData = new FormData();
				// formData.append('file', item);
				res.AttachmentInfoList.forEach((el)=>{
					if (item.name == el.AttachmentName) {
						promiseList.push(ajaxSendFile(el.CosUrl, item));
					}
				})
			});
			Promise.all(promiseList).then(()=>{
				setLoading(false);
				info.AttachmentInfoList = info.AttachmentInfoList || [];
				attachmentInfoList.forEach((el)=>{
					let same = false;
					info.AttachmentInfoList?.forEach((item)=>{
						if (el.AttachmentName == item.AttachmentName) {
							same = true;
						}
					});
					if (!same) {
						info.AttachmentInfoList.push(el);
					}
				});
				setInfo(cloneDeep(info));
				setFileChange(true);
			}).catch(()=>{
				setLoading(false);
			});
		} catch (err) {
			setLoading(false);
		}
	};
	return (
		<Modal
			className={'service-modal'}
			visible={visible}
			caption="附件管理"
			onClose={
			()=>{
				handleVisible(fileChange);
			}
		}>
			<Modal.Body>
				<Form>
					<Form.Item label={'服务类型：'}>
						<Input
							className={'service-form-input'}
							value={info.ServerType}
							readonly={true}
						/>
					</Form.Item>
					<Form.Item label={'服务名称：'}>
						<Input
							className={'service-form-input'}
							value={info.OrderName}
							readonly={true}
						/>
					</Form.Item>
					<Form.Item label={'服务单号：'}>
						<Input
							className={'service-form-input'}
							value={info.OrderId}
							readonly={true}
						/>
					</Form.Item>
					<Form.Item label={'客户名称：'}>
						<Input
							className={'service-form-input'}
							value={info.CustomerName}
							readonly={true}
						/>
					</Form.Item>
				</Form>
				<Table
					records={info.AttachmentInfoList}
					topTip={
						(!info.AttachmentInfoList || info.AttachmentInfoList?.length == 0) && <StatusTip status={"empty"} />
					}
					columns={[
						{
							key: "AttachmentName",
							header: "附件名",
						},
						{
							key: "Other",
							header: "操作",
							render: (item)=>{
								return <Button type={'link'} onClick={
									()=>{
										deleteFile(item);
									}
								}>删除</Button>;
							}
						},
					]}
				></Table>
			</Modal.Body>
			<Modal.Footer>
				<span>
					<input
						className={'service-file'}
						multiple={true}
						type="file"
						autoComplete="off"
						style={{display: 'none'}}
						onChange={(e)=>{
							upload(e);
						}}
					/>
					<Button
						type="primary"
						onClick={(e)=>{
							(e.target as any).parentNode.querySelector('input').click();
						}}
						loading={loading}
					>
					上传附件
					</Button>
				</span>
				<Button type="weak" onClick={()=>{
					handleVisible(fileChange);
				}}>
					取消
				</Button>
			</Modal.Footer>
		</Modal>
	);
}
