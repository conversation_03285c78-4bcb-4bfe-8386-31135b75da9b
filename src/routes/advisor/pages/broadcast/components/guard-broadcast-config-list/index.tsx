import React, { useState, useEffect, useMemo } from 'react';
import {
	Table,
	message,
	Button,
	Row,
	Card,
	Col,
	Select,
	Text,
	StatusTip,
	Tag,
	Icon,
	Justify,
	SelectMultiple,
	Layout,
} from '@tencent/tea-component';
import { Popconfirm, Button as TdButton } from 'tdesign-react';
import { useHistory } from '@tea/app';
import { map, find, isEmpty } from 'lodash';
import { useToggle } from '../../hooks/common';

import {
	getMetricConfig,
	updateChatBiMetricStatus,
	getAllMetrics,
	getMonitorProduct,
} from '@src/api/advisor/broadcast';

import { ProductOption } from '@src/types/cloudEscort/broadcast/broadStrategy/page';
import { GuardtFormInfoListConfig, GuardStatusOption, All } from '../../conf/config';

const { pageable } = Table.addons;
const { Body, Content } = Layout;
interface MetricListFilter {
	product: string[],
	status?: string,
	metric: string,
}

enum statusType {
	online = 1,
	offline = 0,
	all = 2,
}

const defaultFilterOption = {
	product: [],
	metric: All,
	status: `${statusType.all}`,
};

export default function GuardBroadcastConfigList() {
	const history = useHistory();
	const operator = localStorage.getItem('engName');
	// 过滤项
	const [filter, setFilter] = useState<MetricListFilter>(defaultFilterOption);
	// 产品下拉选项
	const [productOption, setProductOption] = useState<Array<ProductOption>>([]);

	// 指标下拉选项
	const [metricsOption, setMetricsOption] = useState([]);

	// 播报策略列表
	const [metricConfigList, setMetricConfigList] = useState([]);

	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	// 加载状态
	const [isLoading, startLoad, endLoad] = useToggle(false);

	// 获取云产品列表
	const getMonitorProductList = async () => {
		try {
			const res: any = await getMonitorProduct();
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setProductOption(map(res?.ProductInfo, item => ({ text: item.ProductCN,	value: item.ProductId	})));
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取监控指标
	const getAllMetricsList = async () => {
		try {
			const res: any = await getAllMetrics();
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const MetricsList = map(res?.MetricNameInfos, item => ({
				text: item.MetricCNName,
				value: item.MetricCNName,
			}));
			setMetricsOption([{ text: '全部', value: 'all' }].concat(MetricsList));
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getMetricConfigList = async (filterParams) => {
		startLoad();
		try {
			const { product, metric, status } = filterParams;
			const params = {
				AppId: 1253985742,
				...(isEmpty(product) || product === All ? {} : { ProductId: product }),
				...(!metric || metric === All ? {} : { Metric: [metric] }),
				Status: +status,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
			};
			const res: any = await getMetricConfig(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setMetricConfigList(res?.MetricConfigList);
				setTotalPage(res?.TotalCount);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	const selectInfoListOption = useMemo(() => map(GuardtFormInfoListConfig, (item) => {
		if (item.value === 'product') {
			return { ...item, optionsConfig: productOption };
		} if (item.value === 'metric') {
			return { ...item, optionsConfig: metricsOption };
		}
		return item;
	}), [productOption, metricsOption]);

	useEffect(() => {
		getMonitorProductList();
		getAllMetricsList();
	}, []);

	useEffect(() => {
		setPageNumber(1);
	}, [filter]);

	useEffect(() => {
		getMetricConfigList(filter);
	}, [pageSize, pageNumber]);

	const handleMetricConfigStatusChange = async (id: number, status: number) => {
		try {
			const res: any = await updateChatBiMetricStatus({
				ChatBiMetricId: id,
				Status: status,
				Operator: operator,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setPageNumber(1);
				getMetricConfigList(filter);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	const columns = [
		{
			key: 'Id',
			align: 'left',
			header: '监控配置ID',
		},
		{
			key: 'Namespace',
			align: 'left',
			header: '命名空间',
			width: '10%',
		},
		{
			key: 'ProductCN',
			align: 'left',
			header: '云产品',
			width: '10%',
			render: item => (<Text tooltip>{item.ProductCN}</Text>),
		},
		{
			key: 'MetricCNName',
			align: 'left',
			header: '指标名称',
			width: '10%',
			render: item => (<Text tooltip>{item.MetricCNName}</Text>),
		},
		{
			key: 'MetricName',
			align: 'left',
			header: '指标英文名',
			width: '10%',
			render: item => (<Text tooltip>{item.MetricName}</Text>),
		},
		{
			key: 'Desc',
			align: 'left',
			header: '说明',
			width: '10%',
			render: item => (<Text tooltip>{item.Desc}</Text>),
		},
		{
			key: 'Unit',
			align: 'left',
			header: '单位',
		},
		{
			key: 'Creator',
			header: '创建人|更新人',
			align: 'left',
			render: (item) => {
				const { Creator, Updater } = item;
				return (<>
					{Creator && <><Tag theme="primary">{Creator}</Tag><br /></>}
					{Updater && <Tag theme="primary">{Updater}</Tag>}
				</>);
			},
		},
		{
			key: 'CreateTime',
			header: '创建时间|更新时间',
			align: 'left',
			width: '12%',
			render: (item) => {
				const { CreateTime, UpdateTime } = item;
				return (<>
					{CreateTime && <><Tag >{CreateTime}</Tag><br /></>}
					{UpdateTime && <Tag >{UpdateTime}</Tag>}
				</>);
			},
		},
		{
			key: 'TableFieldCode',
			align: 'left',
			header: '创建状态',
		},
		{
			key: 'Status',
			align: 'left',
			header: '状态',
			render: (item) => {
				const status = find(GuardStatusOption, status => status.value === `${item.Status}`) || {};
				return (<div style={{ display: 'flex', alignItems: 'center' }}>
					{+status.value === statusType.online ? <Icon type="success" /> : <Icon type="not" />}
					<span style={{ marginLeft: '3px' }}>{status.text}</span>
				</div>);
			},
		},
		{
			key: '',
			align: 'left',
			header: '操作',
			width: 200,
			render: item => (
				<div className='braodcast-config-btn'>
					<Button type="link" disabled={!item?.IsAdmin && item?.Creator !== operator } onClick={() => {
						history.push({ pathname: `/advisor/monitor-config-editor/${item.Id}` });
					}}>
        		编辑
					</Button>
					<Popconfirm
						content='确定启用吗？'
						cancelBtn={null}
						confirmBtn={
							<TdButton size='small' onClick={() => handleMetricConfigStatusChange(item.Id, statusType.online)}>
								确定
							</TdButton>
						}
					>
						<Button
							tooltip={!item?.IsAdmin ? '播报策略启停请联系管理员fisherhhyu;pumbaahu;hertzzhang' : ''}
							type="link"
							style={{ marginLeft: 8 }}
							disabled={item.Status === +statusType.online || !item?.IsAdmin || item?.TableFieldCode === '创建中'}
						>
							启用
						</Button>
					</Popconfirm>
					<Popconfirm
						content='确定停用吗？'
						cancelBtn={null}
						confirmBtn={
							<TdButton size='small' onClick={() => handleMetricConfigStatusChange(item.Id, statusType.offline)}>
								确定
							</TdButton>
						}
					>
						<Button
							tooltip={!item?.IsAdmin ? '播报策略启停请联系管理员fisherhhyu;pumbaahu;hertzzhang' : ''}
							type="link"
							style={{ marginLeft: 8 }}
							disabled={item.Status === +statusType.offline || !item?.IsAdmin || item?.TableFieldCode === '创建中'}
						>
							停用
						</Button>
					</Popconfirm>
				</div>
			),
		},
	];

	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	const tableOptionProps = {
		style: { marginTop: 15 },
		recordKey: 'Id',
		verticalTopL: true,
		columns,
		records: metricConfigList,
		topTip: isLoading ? <StatusTip status="loading"></StatusTip> : metricConfigList.length === 0 && <StatusTip status="empty" />,
		addons: [
			pageable({
				pageIndex: pageNumber,
				recordCount: totalPage,
				onPagingChange: handlePageChange,
			}),
		],
	};

	const handleCreateBtnClick = () => {
		history.push({
			pathname: '/advisor/monitor-config-editor/0',
		});
	};

	return (
		<Body>
			<Content>
				<Content.Header title="监控配置"></Content.Header>
				<Content.Body>
					<Card>
						{/* <Alert style={{ marginTop: 20 }}>
							<h4>特别说明:</h4>
							<List type="bullet">
								<List.Item>请参考
									<ExternalLink href="https://iwiki.woa.com/pages/viewpage.action?pageId=4006736976">
										《护航播报接入说明》
									</ExternalLink>
								</List.Item>
							</List>
						</Alert> */}
						<Card.Body>
							<Row gap={15}>
								<>
									{
										selectInfoListOption.map(item => (
											<Col key={item.label}>
												<Row verticalAlign={'middle'}>
													<Col span={6} >
														<Text theme="label" verticalAlign="middle">{item.label}</Text>
													</Col>
													<Col span={18} >
														{
															item?.type === 'multiple'
																? <SelectMultiple
																	appearance="button"
																	matchButtonWidth
																	searchable
																	size="full"
																	options={item.optionsConfig}
																	value={filter[item.value]}
																	onChange={value => setFilter({
																		...filter,
																		[item.value]: value,
																	})}
																	placeholder="请选择"
																/>
																: <Select
																	appearance="button"
																	matchButtonWidth
																	searchable
																	size="full"
																	options={item.optionsConfig}
																	value={filter[item.value]}
																	onChange={value => setFilter({
																		...filter,
																		[item.value]: value,
																	})}
																	placeholder="请选择"
																/>
														}
													</Col>
												</Row>
											</Col>
										))
									}
								</>
							</Row>
							<div style={{ marginTop: 10, textAlign: 'center' }}>
								<Button
									type="primary"
									onClick={() => {
										getMetricConfigList(filter);
									}}
								>
              		查询
								</Button>
								<Button style={{ marginLeft: 15 }} onClick={() => {
									setFilter(defaultFilterOption);
									getMetricConfigList(defaultFilterOption);
								}}>
									重置
								</Button>
							</div>
						</Card.Body>
					</Card>
					<Card>
						<Card.Body>
							<Justify
								left={
									<Button
										type="primary"
										onClick={handleCreateBtnClick}
									>
										+单指标监控
									</Button>} />
							{/* @ts-ignore */}
							<Table {...tableOptionProps} />
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}
