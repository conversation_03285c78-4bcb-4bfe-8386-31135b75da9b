import React, { useState, useEffect } from 'react';
import {
	message,
	Button,
	Input,
	TextArea,
	Form,
	Modal,
} from '@tencent/tea-component';
import { Skeleton } from 'tdesign-react';
import { useForm, Controller } from 'react-hook-form';
import { map, find, isEmpty, clone, filter, uniqWith, isEqual } from 'lodash';

import {
	getDimensionInfoListByStrategyId,
	updateDimensionInfoListByStrategyId,
} from '@src/api/advisor/broadcast';
import './index.less';
import uuid from 'react-uuid';

interface IProps {
	strategyId: number;
	close: () => void;
}

export default function BroadcastParamsPanel({ strategyId, close }: IProps) {
	const {
		control,
		setValue,
		handleSubmit,
		formState: { errors },
	} = useForm({ mode: 'all' });
	const operator = localStorage.getItem('engName');
	const [dimensionInfoList, setDimensionInfoList] = useState([]);
	const [loading, setLoading] = useState(false);

	const [infoLoading, setInfoLoading] = useState({});

	const getStatus = (fieldState) => {
		if (fieldState?.error?.message) {
			return 'error';
		}
		if (!fieldState.isDirty) {
			return undefined;
		}
		return fieldState.invalid ? 'error' : 'success';
	};

	const getDimensionInfoList = async () => {
		setInfoLoading(true);
		try {
			const res: any = await getDimensionInfoListByStrategyId({
				StrategyId: strategyId,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setDimensionInfoList(res?.DimensionInfoList);
			map(res?.DimensionInfoList, (item) => {
				setValue(`${item?.DimensionId}@NameSpace`, item?.NameSpace);
				setValue(`${item?.DimensionId}@DimensionName`, item?.DimensionName);
				setValue(`${item?.DimensionId}@SourceDataGetCondition`, item?.SourceDataGetCondition);
			});
			setInfoLoading(false);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const onSubmitHandle = async (values) => {
		setLoading(true);
		const dimensionInfoListParams = [];
		Object.keys(values).forEach((key) => {
			const [dimensionId] = key.split('@');
			const dimensionInfo = find(dimensionInfoList, item => `${item.DimensionId}` === dimensionId) || {};
			if (!isEmpty(dimensionInfo)) {
				dimensionInfoListParams.push({
					DimensionId: dimensionId,
					DimensionName: values[`${dimensionId}@DimensionName`],
					NameSpace: values[`${dimensionId}@NameSpace`],
					SourceDataGetCondition: values[`${dimensionId}@SourceDataGetCondition`],
				});
			}
		});
		const list = uniqWith(dimensionInfoListParams, isEqual);

		try {
			const res: any = await updateDimensionInfoListByStrategyId({
				StrategyId: strategyId,
				Operator: operator,
				DimensionInfoList: map(list, item => ({
					...item,
					DimensionId: /^\d+$/.test(item?.DimensionId) ? +item?.DimensionId : -1,
				})),
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			if (res?.Msg) {
				message.warning({ content: res?.Msg });
			} else {
				message.success({ content: '操作成功，等待审核通过后生效' });
				close();
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		} finally {
			setLoading(false);
		}
	};

	const handleDimensionAdd = () => {
		const dimensionInfoListNew = clone(dimensionInfoList);
		dimensionInfoListNew.push({
			DimensionId: uuid(),
			DimensionName: '',
			NameSpace: '',
			SourceDataGetCondition: '',
		});
		setDimensionInfoList(dimensionInfoListNew);
	};

	const handleDimensionRemove = (dimensionId) => {
		const dimensionInfoListNew = clone(dimensionInfoList);
		const list = filter(dimensionInfoListNew, item => item.DimensionId !== dimensionId);
		setDimensionInfoList(list);
	};

	useEffect(() => {
		getDimensionInfoList();
	}, []);

	return (
		<>
			<Modal.Body>
				<Form layout="inline-vertical">
					{
						infoLoading
							? <Skeleton theme="article">.</Skeleton>
							: <>
								{
									dimensionInfoList?.map((item, index) => <div className='panel-form-item' key={item?.DimensionId}>
										<Controller
											name={`${item?.DimensionId}@NameSpace`}
											control={control}
											rules={{
												validate: (value) => {
													if (!value) {
														return '请输入命名空间';
													}
													return undefined;
												},
											}}
											render={({ field, fieldState }) => (
												<Form.Item
													label={index === 0 && '命名空间'}
													status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
													message={errors[`${item?.DimensionId}@NameSpace`]?.message}
												>
													<Input
														{...field}
														size="s"
														placeholder="请输入命名空间"
													/>
												</Form.Item>
											)}
										/>
										<Controller
											name={`${item?.DimensionId}@DimensionName`}
											control={control}
											rules={{
												validate: (value) => {
													if (!value) {
														return '请输入维度';
													}
													return undefined;
												},
											}}
											render={({ field, fieldState }) => (
												<Form.Item
													label={index === 0 && '维度'}
													status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
													message={errors[`${item?.DimensionId}@DimensionName`]?.message}
												>
													<Input
														{...field}
														size="s"
														placeholder="请输入维度"
													/>
												</Form.Item>
											)}
										/>
										<Controller
											name={`${item?.DimensionId}@SourceDataGetCondition`}
											control={control}
											rules={{
												validate: (value) => {
													if (!value) {
														return '请输入适配字段';
													}
													try {
														JSON.parse(value);
														return undefined;
													}  catch (e) {
														return '请输入合法的JSON格式';
													}
												},
											}}
											render={({ field, fieldState }) => (
												<Form.Item
													label={index === 0 && '适配字段JSON'}
													status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
													message={errors[`${item?.DimensionId}@SourceDataGetCondition`]?.message}
													className='panel-form-item-textarea'
												>
													<TextArea
														style={{ width: 260, height: 200 }}
														{...field}
														size="l"
														placeholder="请输入适配字段"
													/>
												</Form.Item>
											)}
										/>
										<Button
											type="link"
											style={{ marginTop: 13 }}
											disabled={dimensionInfoList?.length === 1}
											onClick={() => handleDimensionRemove(item?.DimensionId)}
										>
											删除
										</Button>
									</div>)
								}
							</>
					}
				</Form>
				<Button type="link" onClick={handleDimensionAdd}>
					新增
				</Button>
			</Modal.Body>
			<Modal.Footer>
				<Button
					type="primary"
					loading={loading}
					onClick={handleSubmit(onSubmitHandle) as any}
				>
					提交变更
				</Button>
				<Button type="weak" onClick={close}>
					取消
				</Button>
			</Modal.Footer>
		</>
	);
}
