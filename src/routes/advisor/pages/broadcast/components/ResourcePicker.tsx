import React, { useState, useEffect, FC, useMemo } from 'react';
import { map, filter, isEmpty, forEach } from 'lodash';
import { getGuardProductInstance } from '@src/api/advisor/broadcast';
import { useToggle } from '../hooks/common';
import { Tag, message, Transfer, Table, TagSearchBox } from '@tencent/tea-component';
import { AttributeValue } from '@tencent/tea-component/src/tagsearchbox/AttributeSelect';
interface IResourceSupplePanelProps {
	guardId: number;
	resourceDetail: any
	productLimitMap: any;
	onInsListLoad: Function
	editable: boolean
	onSourceListChange: Function
}
const { selectable, removeable, scrollable, autotip } = Table.addons;

const columns = [
	{
		key: 'AppId',
		header: 'APPID',
	},
	{
		key: 'InstanceId',
		header: '实例ID/实例名',
		width: '50%',
		render: (item) => {
			const { InstanceId, InstanceName } = item;
			return (<>
				{InstanceId && <><Tag theme="primary">{InstanceId}</Tag><br /></>}
				{InstanceName && <Tag>{InstanceName}</Tag>}
			</>);
		},
	},
	{
		key: 'Region',
		header: '地域',
	},
];


const attributes: Array<AttributeValue> = [
	{
		type: 'input',
		key: 'insID',
		name: '实例ID',
	},
	{
		type: 'input',
		key: 'insName',
		name: '实例名',
	},
];

function SourceTable({ dataSource, targetKeys, onChange, loading, editable }) {
	return (
		<Table
			records={dataSource}
			recordKey="InstanceId"
			rowDisabled={record => !editable}
			columns={columns}
			addons={[
				scrollable({
					maxHeight: 310,
				}),
				selectable({
					value: targetKeys,
					onChange,
					rowSelect: true,
				}),
				autotip({
					isLoading: loading,
				}),
			]}
		/>
	);
}

function TargetTable({ dataSource, onRemove, loading, editable }) {
	return (
		<Table
			records={dataSource}
			recordKey="InstanceId"
			rowDisabled={record => !editable}
			columns={columns}
			addons={[
				removeable({ onRemove }),
				autotip({
					isLoading: loading,
				}),
			]}
		/>
	);
}

const ResourcePicker: FC<IResourceSupplePanelProps> = ({
	guardId,
	editable,
	resourceDetail,
	productLimitMap,
	onInsListLoad,
	onSourceListChange,
}: IResourceSupplePanelProps) => {
	const { Product, ResourceIds } = resourceDetail;
	const [isLoading, startLoad, endLoad] = useToggle(false);
	const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);
	const [targetKeys, setTargetKeys] = useState(map(ResourceIds, ({ InstanceId }) => InstanceId));
	const [instanceList, setInstanceList] = useState([]);

	// 获取护航信息
	const getGuardSheet = async () => {
		startLoad();
		try {
			const filters = [
				{ Name: 'guard_id', Values: [`${guardId}`] },
				{ Name: 'product', Values: [Product] },
			];
			const res = await getGuardProductInstance({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				endLoad();
				return;
			}
			setInstanceList(res.Instance);
			onInsListLoad(res.Instance, Product);
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const handleSearchTagChange = (tags) => {
		setTagSelectBoxValue(tags);
	};

	const tagSearchBoxProps = {
		minWidth: '100%',
		attributes,
		value: tagSelectBoxValue,
		hideHelp: true,
		tips: '支持批量搜索，多个关键词用竖线"|"分割',
		onChange: handleSearchTagChange,
	};

	// 搜索
	const filterList = useMemo(() => {
		if (isEmpty(tagSelectBoxValue)) return instanceList;
		const keyWords = [];
		forEach(tagSelectBoxValue, (tag) => {
			forEach(tag.values, (tagValues) => {
				keyWords.push({ type: tag.attr?.key, name: tagValues.name });
			});
		});
		return filter(instanceList, (ins) => {
			for (const keyWord of keyWords) {
				if (ins.InstanceId.includes(keyWord.name) && keyWord.type === 'insID') return true;
				if (ins.InstanceName.includes(keyWord.name) && keyWord.type === 'insName') return true;
			}
			return false;
		});
	}, [tagSelectBoxValue, instanceList]);

	useEffect(() => {
		getGuardSheet();
	}, [Product]);

	useEffect(() => {
		const targetKeys = map(ResourceIds, ({ InstanceId }) => InstanceId);
		setTargetKeys(targetKeys);
	}, [ResourceIds]);

	useEffect(() => {
		onSourceListChange(filter(instanceList, i => targetKeys.includes(`${i.InstanceId}`)), Product);
	}, [targetKeys, instanceList]);

	const handleSourceChange = (keys) => {
		if (keys.length === filterList?.length && targetKeys?.length === productLimitMap[Product]) {
			setTargetKeys([]);
			return;
		}
		if (keys.length === filterList?.length && keys.length > productLimitMap[Product]) {
			const list = map(filterList, i => i.InstanceId);
			const keyList = list?.slice(0, productLimitMap[Product]);
			setTargetKeys(keyList);
			message.warning({ content: '资源已选择到上限' });
			return;
		}
		if (keys.length < filterList?.length && keys.length > productLimitMap[Product]) {
			message.warning({ content: '资源已选择到上限' });
			return;
		}
		setTargetKeys(keys);
	};

	return (
		<Transfer
			leftCell={
				<Transfer.Cell
					scrollable={false}
					title={
						<div style={{ marginTop: 6 }}>
							<span style={{ fontSize: '14px', fontWeight: 'bold' }}>
								<span style={{ color: 'red', marginRight: 5 }}>*</span>
                  请选择重点实例，播报上限：{productLimitMap[Product]}
							</span>
						</div>
					}
					header={
						<TagSearchBox {...tagSearchBoxProps}/>
					}
				>
					<SourceTable
						dataSource={filterList}
						targetKeys={targetKeys}
						onChange={keys => handleSourceChange(keys)}
						loading={isLoading}
						editable={editable}
					/>
				</Transfer.Cell>
			}
			rightCell={
				<Transfer.Cell title={`已选择 (${targetKeys.length})`}>
					<TargetTable
						dataSource={filter(instanceList, i => targetKeys.includes(`${i.InstanceId}`))}
						onRemove={key => setTargetKeys(targetKeys.filter(i => i !== key))}
						loading={isLoading}
						editable={editable}
					/>
				</Transfer.Cell>
			}
		/>
	);
};
export default ResourcePicker;
