import React, { useState, useEffect, useMemo, useImperativeHandle } from 'react';
import { useHistory, useAegisLog } from '@tea/app';
import { Col, Row, Text, DatePicker, TimePicker, Form, message, Select, Tag, List, Switch, Button, Modal } from '@tencent/tea-component';
import './style.less';
import moment from 'moment';
import { broadcastBaseLabel } from '../../conf/config';
import { PushGuardChat } from '@src/api/advisor/guard';
import { ModifyBroadcastBaseInfo } from '@src/api/advisor/broadcast';

import { pick, isEqual } from 'lodash';
import { getProcessEnv } from '../../../../../../../app/utils';

interface IBroadcastBasePanelProps {
	guardInfo: any;
	broadcastInfo: any
	editable: boolean
	onInfoChange: Function
	reload: Function
}

const compareSelectOption = [
	{ text: '5分钟', value: '5' },
	{ text: '10分钟', value: '10' },
	{ text: '30分钟', value: '30' },
	{ text: '60分钟', value: '60' },
	{ text: '2小时', value: '120' },
	{ text: '3小时', value: '180' },
];

const FORMAT_DATE = 'YYYY-MM-DD HH:mm:ss';

const TIME_FORMAT_DATE = 'HH:mm';

const { RangePicker } = DatePicker;
const { RangePicker: TimeRangePicker } = TimePicker;

const BroadcastBasePanel = React.forwardRef(({ guardInfo, broadcastInfo, editable, onInfoChange, reload }: IBroadcastBasePanelProps, ref) => {
	// 当前环境
	const env = getProcessEnv();
	const history = useHistory();
	const aegis = useAegisLog();
	const operator = localStorage.getItem('engName');
	const {
		GuardId,
		GuardName,
		MainAppId,
		CustomerName,
		RelatedAppId,
		RelatedCustomerNames,
		StartTime: GuardStartTime,
		EndTime: GuardEndTime,
	} = guardInfo;
	const {
		BroadcastId,
		BroadcastName,
		Scene,
		GroupIds,
		StartTime,
		EndTime,
		StartClock,
		EndClock,
		Period,
		OnlyWorkdays,
		GtsChatid,
		AntoolTaskId,
		ProcessInstanceId,
	} = broadcastInfo;

	const [wcGroupIds, setWcGroupIds] = useState(GroupIds);

	const [rangeDateTime, setRangeDateTime] = useState([moment(StartTime).format(FORMAT_DATE), moment(EndTime).format(FORMAT_DATE)]);

	const [timeRange, setTimeRange] = useState([StartClock, EndClock]);

	const [tipPeriod, setTipPeriod] = useState(`${Period / 60}` || '30');

	const [isOnlyWorkdays, setIsOnlyWorkdays] = useState(OnlyWorkdays);

	const initFromInfo = pick(broadcastInfo, ['GroupIds', 'StartTime', 'EndTime', 'Period', 'OnlyWorkdays', 'StartClock', 'EndClock']);
	// 外部群弹框提示
	const [showTip, setShowTip] = useState(false);
	const URL = `https://${(getProcessEnv() === 'production' || getProcessEnv() === 'production-abroad') ? '' : 'test-'}antool.woa.com/fe-base/antool-page/visit/info?taskId=${AntoolTaskId}&processInstanceId=${ProcessInstanceId}`;

	// const handleGroupIdsInputChange = (value, index) => {
	// 	const temp = clone(wcGroupIds);
	// 	temp[index] = value;
	// 	setWcGroupIds(temp)
	// }

	// const handleGroupIdsInputDelete = index => {
	// 	const temp = clone(wcGroupIds);
	// 	temp.splice(index, 1);
	// 	setWcGroupIds(temp)
	// }

	const ruleDetail = useMemo(() => ({
		GroupIds: wcGroupIds,
		StartTime: moment(rangeDateTime[0]).format('YYYY-MM-DD 00:00:00'),
		EndTime: moment(rangeDateTime[1]).format('YYYY-MM-DD 23:59:59'),
		StartClock: timeRange[0],
		EndClock: timeRange[1],
		Period: +tipPeriod * 60,
		OnlyWorkdays: isOnlyWorkdays,
	}), [wcGroupIds, rangeDateTime, timeRange, tipPeriod, isOnlyWorkdays]);

	useEffect(() => {
		onInfoChange(isEqual(initFromInfo, ruleDetail));
	}, [initFromInfo, ruleDetail]);

	const handleBaseInfoSave = async () => {
		try {
			const res = await ModifyBroadcastBaseInfo({
				...ruleDetail,
				AppId: 1253985742,
				BroadcastId,
				Updater: operator,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				reload();
				return false;
			}
			message.success({ content: '已自动保存' });
			reload();
			return true;
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			return false;
		}
	};

	// 将保存方法暴露给父组件
	useImperativeHandle(ref, () => ({
		save: handleBaseInfoSave,
	}));

	const handleJoinChat = async () => {
		try {
			const res = await PushGuardChat({
				GuardId,
				AppId: MainAppId,
				User: [operator],
				Type: 'broadcast',
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			aegis.reportEvent({
				name: 'Click',
				ext1: 'broadcast-to-group-btn',
				ext2: localStorage.getItem('engName'),
				ext3: '播报订阅/编辑策略',
			});
			message.success({ content: res.Message });
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	return (
		<div className='intlc-broadcast-base-info__inner' >
			{!editable && <div className='content-mask'></div>}
			<div className='intlc-broadcast__inner'>
				<div className='intlc-broadcast__header'>
					<h3>基础信息</h3>
				</div>
				<div className='intlc-broadcast__body'>
					<Row>
						<Col span={8}>
							<List>
								{broadcastBaseLabel.map((item, index) => (
									<List.Item key={index}>
										<Text theme="label">{item}</Text>
									</List.Item>))}
							</List>
						</Col>
						<Col span={16}>
							<List>
								<List.Item>{`${BroadcastId} | ${BroadcastName}`}</List.Item>
								<List.Item>{Scene}</List.Item>
								<List.Item>
									<Button
										type="link"
										onClick={() => history.push({ pathname: `/advisor/guard/summary/${GuardId}` })}>
										{`${GuardId} | ${GuardName}`}
									</Button>
								</List.Item>
								<List.Item>
									<Tag theme="primary" style={{ margin: 0, marginBottom: 5 }}>{MainAppId} | {CustomerName}</Tag><br />
									{
										RelatedAppId?.map((item, index) => <div key={index}>
											<Tag theme="primary" style={{ margin: 0, marginBottom: 5 }}>{item} | {RelatedCustomerNames[index]}</Tag><br />
										</div>)
									}
								</List.Item>
							</List>
						</Col>
					</Row>
				</div>
			</div>
			<div className='intlc-broadcast__inner'>
				<div className='intlc-broadcast__header'>
					<h3>推送规则</h3>
				</div>
				<div className='intlc-broadcast__body'>
					<Form>
						<Form.Item label="护航播报群（内部）" tips="仅用于护航播报，默认只有护航提单人和护航负责人在群内。">
							<Button type="text" className='intlc-broadcast-chat'>{GroupIds[0]}</Button>
							<Button type="link" onClick={handleJoinChat}>
								一键入群
							</Button>
						</Form.Item>
						<Form.Item label="护航播报群（外部）" tips="通过 Antool 建立，只支持总览类播报策略。">
							<Button type="link" style={{ color: '#000000E6' }} className='intlc-broadcast-chat'>
								{ GtsChatid || <span style={{ color: '#0006' }}>{ AntoolTaskId ? '未建立' : '暂无' }</span>}
							</Button>
							{
								AntoolTaskId
								&& <Button type="link" onClick={() => {
									setShowTip(true);
								}}>
									{ GtsChatid ? '入群指引' : '建群指引'}
								</Button>
							}
						</Form.Item>
						<Form.Item required label="开始/结束时间">
							<RangePicker
								defaultValue={[moment(StartTime), moment(EndTime)]}
								range={[moment(GuardStartTime), moment(GuardEndTime)]}
								onChange={value => setRangeDateTime([
									value[0].format(FORMAT_DATE),
									value[1].format(FORMAT_DATE),
								])
								}
							/>
						</Form.Item>
						<Form.Item required label="播报时段">
							<TimeRangePicker
								defaultValue={StartClock ? [moment(StartClock, TIME_FORMAT_DATE), moment(EndClock, TIME_FORMAT_DATE)] : undefined}
								format={TIME_FORMAT_DATE}
								onChange={value => setTimeRange([
									value[0].format(TIME_FORMAT_DATE),
									value[1].format(TIME_FORMAT_DATE),
								])
								}
							/>
						</Form.Item>
						<Form.Item required label="提醒频率">
							<Select
								value={tipPeriod}
								matchButtonWidth
								options={compareSelectOption}
								appearance="button"
								size="m"
								onChange={value => setTipPeriod(value)}
							/>
						</Form.Item>
						<Form.Item label="仅工作日提醒">
							<Switch value={isOnlyWorkdays} onChange={value => setIsOnlyWorkdays(value)}></Switch>
						</Form.Item>
					</Form>
					{/* { */}
					{/* 	editable */}
					{/* 	&& <Form.Action className='intlc-broadcast__operate'> */}
					{/* 		<Button type="primary" onClick={handleBaseInfoSave}> */}
					{/* 			保存 */}
					{/* 		</Button> */}
					{/* 	</Form.Action> */}
					{/* } */}
				</div>
			</div>
			<Modal visible={showTip} caption={`当前子任务单ID：${AntoolTaskId}`} onClose={() => {
				setShowTip(false);
			}} disableCloseIcon>
				<Modal.Body style={{ lineHeight: '24px' }}>
					<div>1. 【确认子任务单】<a href={URL} target="_blank">{URL}</a></div>
					{ GtsChatid
						? <div>2. 【入群】在子任务单页面右下角，点击“一键拉群”图标，进入已有外部群；</div>
						:						<>
							<div>2. 【建群】在子任务单页面右下角，点击“一键拉群”图标，建立外部群；</div>
							<div>3. 【配置播报】建立外部群成功后，刷新本页面，继续配置播报订阅；</div>
						</>
					}
					<div style={{ marginTop: 20 }}>注意，如 Antool 页面存在权限问题，请按需申请操作；如过程有疑惑，请联系 lucasxye。</div>
				</Modal.Body>
				<Modal.Footer>
					<Button type="primary" onClick={() => {
						setShowTip(false);
					}}>
            确定
					</Button>
				</Modal.Footer>
			</Modal>
		</div>
	);
});
export default BroadcastBasePanel;
