import React from 'react';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';

class Code extends React.Component {
    render() {
        return <SyntaxHighlighter
            showLineNumbers
            // @ts-ignore
            language={this.props.lang}
            wrapLines
            style={vscDarkPlus}
        >
            {/* @ts-ignore */}
            {this.props.children.replace(/^\s+|\s+$/g, '')}
        </SyntaxHighlighter>
    }
}


export function CodeHighLighter({ codeText, language }) {
    return (
        // @ts-ignore
        <Code lang={language}>{codeText}</Code>
    )
}