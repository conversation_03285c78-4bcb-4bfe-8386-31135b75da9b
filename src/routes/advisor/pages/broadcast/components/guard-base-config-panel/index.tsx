import React, { useEffect, useMemo, useState } from 'react';
import { Button, Form, Input, TextArea, TagSelect, message, Select } from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import {
	getMonitorProduct,
	createMetricConfig,
	modifyMetricConfig,
	getMonitorMetrics,
	getMetricConfig,
} from '@src/api/advisor/broadcast';
import { Skeleton } from 'tdesign-react';
import { map, reduce, find, pick, isEmpty } from 'lodash';
import { useForm, Controller } from 'react-hook-form';
import { useCheckIfAuthed, NoPermission, PermissionLoading } from '../../hooks/checkIfAuthed';
import './index.less';


interface IProps {
	strategyId: number;
}

export function GuardBaseConfigPanel({ strategyId }: IProps) {
	let isAuth; let authLoading;
	const {
		control,
		watch,
		setValue,
		handleSubmit,
		formState: { errors },
	} = useForm({ mode: 'all' });
	const operator = localStorage.getItem('engName');
	const history = useHistory();
	if (strategyId > 0) {
		const { isAuth: isAuthOther, loading } = useCheckIfAuthed({
			pageStatus: 'monitor-config-editor',
			key: 'MonitorGuardStrategyId',
			value: `${strategyId}`,
		});
		isAuth = isAuthOther;
		authLoading = loading;
	}
	const [loading, setLoading] = useState(false);
	const [monitorLoading, setMonitorLoading] = useState(false);
	const [products, setProducts] = useState([]);
	const [namespaceMap, setNamespaceMap] = useState({});
	const [metricCNOption, setMetricCNOption] = useState([]);
	const [metricMap, setMetricMap] = useState({});
	const [metricConfig, setMetricConfig] = useState<any>({});

	const WatchProductId = watch('ProductId');
	const WatchNamespace = watch('Namespace');
	const WatchMetricCNName = watch('MetricCNName');

	const getStatus = (fieldState) => {
		if (fieldState?.error?.message) {
			return 'error';
		}
		if (!fieldState.isDirty) {
			return undefined;
		}
		return fieldState.invalid ? 'error' : 'success';
	};

	// 获取播报策略信息
	const getConfiguredMonitorMetricStrategy = async (strategyIdParams) => {
		if (!strategyIdParams) return;
		setMonitorLoading(true);
		try {
			const params = {
				Id: +strategyIdParams,
				AppId: 1253985742,
				Status: 2,
			};
			const res: any = await getMetricConfig(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				const metricStrategyInfo = res?.MetricConfigList[0];
				setMetricConfig(metricStrategyInfo);

				Object.keys(metricStrategyInfo).map((item) => {
					if (item === 'Dimensions') {
						if (metricStrategyInfo[item][0]) {
							setValue(item, metricStrategyInfo[item]);
						} else {
							setValue(item, []);
						}
					} else {
						setValue(item, metricStrategyInfo[item]);
					}
				});
			}
			setMonitorLoading(false);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			setMonitorLoading(false);
		}
	};

	// 获取云产品列表
	const getMonitorProductList = async () => {
		try {
			const res: any = await getMonitorProduct();
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setProducts(map(res?.ProductInfo, item => ({ text: item.ProductCN,	value: item.ProductId	})));
			const namespaceMaps = reduce(res?.ProductInfo, (acc, cur) => {
				acc[cur.ProductId] = cur.Namespaces;
				return acc;
			}, {});
			setNamespaceMap(namespaceMaps);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取节点监控指标
	const getNodeMonitorMetriData = async () => {
		if (!WatchNamespace) return;
		try {
			const res: any = await getMonitorMetrics({
				Namespace: WatchNamespace,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setMetricCNOption(map(res?.MonitorMetricSet, item => ({
				text: item.MetricCNName,
				value: item.MetricCNName,
			})));
			const metricMaps = reduce(res?.MonitorMetricSet, (acc, cur) => {
				acc[cur.MetricCNName] = {
					metricName: cur.MetricName,
					unit: cur.Unit,
					dimensions: cur.Dimensions,
				};
				return acc;
			}, {});
			setMetricMap(metricMaps);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const dimensionsOptions = useMemo(() => map(metricMap[WatchMetricCNName]?.dimensions, item => ({
		text: item,
		value: item,
	})), [WatchMetricCNName, metricMap]);

	const onSubmitHandle = async (values) => {
		setLoading(true);
		const params: any = {};
		if (!!strategyId) {
			params.MetricConfig = {
				...metricConfig,
				Updater: operator,
				Desc: values?.Desc,
				Dimensions: values?.Dimensions,
				Unit: values?.Unit,
				InsField: values?.InsField,
			};
			params.Operator = operator;
		} else {
			const monitorProduct = find(products, item => item.value === values?.ProductId) || {};
			params.MonitorProductInfo = { ProductId: monitorProduct?.value, ProductCN: monitorProduct?.text };
			params.MonitorMetric = pick(values, ['Namespace', 'MetricName', 'MetricCNName', 'Unit', 'Dimensions']);
			params.Desc = values?.Desc;
			params.InsField = values?.InsField;
		}
		const apiName = !!strategyId
			? modifyMetricConfig
			: createMetricConfig;
		try {
			const res: any = await apiName(params);
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			message.success({ content: '操作成功' });
			history.push('/advisor/monitor-config');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		getMonitorProductList();
	}, []);

	useEffect(() => {
		getNodeMonitorMetriData();
	}, [WatchNamespace]);

	useEffect(() => {
		getConfiguredMonitorMetricStrategy(strategyId);
	}, [strategyId]);

	if (strategyId > 0 && authLoading) {
		return <PermissionLoading />;
	}
	if (strategyId > 0 && isAuth === false) {
		return <NoPermission />;
	}
	return <div className='intlc-broadcast__inner'>
		<div className='intlc-broadcast__header'>
			<h3>基础信息</h3>
		</div>
		{
			monitorLoading
				? <Skeleton theme="article" >.</Skeleton>
				: <>
					<div className='intlc-broadcast__body'>
						<Form
							layout="fixed"
							fixedLabelWidth={140}
						>
							<Controller
								name="ProductId"
								control={control}
								rules={{
									validate: value => (!value ? '请选择云产品' : undefined),
								}}
								render={({ field, fieldState }) => (
									<Form.Item
										required
										label="云产品(云架构支持产品)"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.ProductId?.message}
									>
										{
											!!strategyId
												? <Input
													{...field}
													style={{ width: '100%' }}
													disabled
												/>
												: <Select
													style={{ width: '100%' }}
													{...field}
													disabled={!!strategyId}
													onChange={(value) => {
														field.onChange(value);
														setValue('Namespace', '');
														setValue('MetricCNName', '');
													}}
													searchable
													matchButtonWidth
													appearance="button"
													placeholder="请选择云产品"
													options={products}
												/>
										}
									</Form.Item>
								)}
							/>
							<Controller
								name="Namespace"
								control={control}
								rules={{
									validate: value => (!value ? '请选择命名空间' : undefined),
								}}
								render={({ field, fieldState }) => (
									<Form.Item
										required
										label="命名空间"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.Namespace?.message}
									>
										{
											!!strategyId
												? <Input
													{...field}
													style={{ width: '100%' }}
													disabled
												/>
												: <Select
													style={{ width: '100%' }}
													{...field}
													disabled={!WatchProductId || !!strategyId}
													onChange={(value) => {
														field.onChange(value);
														setValue('MetricCNName', '');
													}}
													searchable
													matchButtonWidth
													appearance="button"
													placeholder="请选择命名空间"
													options={namespaceMap[WatchProductId]?.map(item => ({
														text: item,
														value: item,
													}))}
												/>
										}
									</Form.Item>
								)}
							/>
							<Controller
								name="MetricCNName"
								control={control}
								rules={{
									validate: (value) => {
										if (!value) {
											return '请选择指标';
										}
										return undefined;
									},
								}}
								render={({ field, fieldState }) => (
									<Form.Item
										required
										label="指标中文名"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.MetricCNName?.message}
									>
										{
											!!strategyId
												? <Input
													{...field}
													style={{ width: '100%' }}
													disabled
												/>
												: <Select
													style={{ width: '100%', maxWidth: 370 }}
													{...field}
													onChange={(value) => {
														field.onChange(value);
														setValue('MetricName', metricMap[value].metricName ?? '');
														setValue('Unit', metricMap[value]?.unit ?? '');
														setValue('Dimensions', metricMap[value]?.dimensions ?? '');
													}}
													searchable
													matchButtonWidth
													disabled={!WatchNamespace || !!strategyId}
													appearance="button"
													placeholder="请选择指标"
													options={metricCNOption}
												/>
										}
									</Form.Item>
								)}
							/>
							<Controller
								name="MetricName"
								control={control}
								rules={{
									validate: (value) => {
										if (!value) {
											return '请输入指标英文名';
										}
										return undefined;
									},
								}}
								render={({ field, fieldState }) => (
									<Form.Item
										required
										label="指标英文名"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.MetricName?.message}
									>
										<Input
											{...field}
											style={{ width: '100%' }}
											disabled
											placeholder="请输入指标"
											autoComplete="off"
										/>
									</Form.Item>
								)}
							/>
							<Controller
								name="Unit"
								control={control}
								render={({ field, fieldState }) => (
									<Form.Item
										label="单位"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.Unit?.message}
									>
										<Input
											{...field}
											style={{ width: '100%' }}
											disabled={!strategyId || (!!strategyId && !metricConfig?.IsAdmin)}
											placeholder="请输入"
											autoComplete="off"
										/>
									</Form.Item>
								)}
							/>
							<Controller
								name="Dimensions"
								control={control}
								rules={{
									validate: (value) => {
										if (isEmpty(value)) {
											return '请输入维度';
										}
										return undefined;
									},
								}}
								render={({ field, fieldState }) => (
									<Form.Item
										required
										label="维度"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.Dimensions?.message}
									>
										<TagSelect
											{...field}
											disabled={!strategyId || (!!strategyId && !metricConfig?.IsAdmin)}
											options={dimensionsOptions}
										/>
									</Form.Item>
								)}
							/>
							<Controller
								name="InsField"
								control={control}
								render={({ field, fieldState }) => (
									<Form.Item
										label="实例维度"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.InsField?.message}
									>
										<Input
											style={{ width: '100%' }}
											{...field}
										/>
									</Form.Item>
								)}
							/>
							<Controller
								name="Desc"
								control={control}
								rules={{
									validate: value => (!value ? '请输入指标说明' : undefined),
								}}
								render={({ field, fieldState }) => (
									<Form.Item
										required
										label="指标说明"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.Desc?.message}
									>
										<TextArea
											style={{ width: '100%' }}
											{...field}
											placeholder="请输入指标说明"
											maxLength={255}
										/>
									</Form.Item>
								)}
							/>
						</Form>
					</div>
					<hr />
					<div className='broadcast-config-opt'>
						<Button
							loading={loading}
							type="primary"
							onClick={handleSubmit(onSubmitHandle) as any}
						>
							保存
						</Button>
					</div>
				</>
		}
	</div>;
}
