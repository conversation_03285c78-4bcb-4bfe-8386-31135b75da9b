import React, { useMemo } from 'react';
import { Card, Layout } from '@tencent/tea-component';
import { BaseInfoConfigPanel } from '../base-info-config-panel';
import { useHistory } from '@tea/app';

const { Body, Content } = Layout;

export function BroadcastConfigEditor(match) {
	const history = useHistory();
	const isExistStrategy = useMemo(() => !!+match.match.params.strategyid, [match]);

	return (
		<Body>
			<Content>
				<Content.Header
					title={isExistStrategy ? '编辑播报策略' : '新增播报策略'}
					onBackButtonClick={() => history.push('/advisor/config-management?tab=2')}
					showBackButton={true}
				/>
				<Content.Body>
					<Card>
						<Card.Body>
							<section>
								<div>
									<BaseInfoConfigPanel strategyId={+match.match.params.strategyid}/>
								</div>
							</section>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body >
	);
}
