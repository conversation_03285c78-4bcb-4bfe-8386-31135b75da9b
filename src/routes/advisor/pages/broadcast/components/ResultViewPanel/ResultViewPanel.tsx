

import React, { useMemo, useState } from 'react';
import './style.less';
import { useHistory } from '@tea/app';
import { Button, Icon, Bubble, Tabs, TabPanel, Form, Switch, Modal } from '@tencent/tea-component';
import { CodeHighLighter } from '../CodeHighLighter';
import { useToggle } from '../../hooks/common';
import { STATUS } from '../../conf/config';
import { omit, isEmpty } from 'lodash'

interface IExtensibleInputProps {
    testStatus: number
    statusEditable: boolean
    resultParam: any
    status: boolean
    onStrategyCreate: Function
}

const tabConfig = [
    { id: 'result', label: '出参' },
    { id: 'content', label: '播报内容' },
]

const ResultViewPanel = ({ testStatus, resultParam, status, statusEditable, onStrategyCreate }: IExtensibleInputProps) => {
    const history = useHistory();
    const [online, setOnline] = useState(status);
    const [quitDialogVisiable, openQuitDialog, closeQuitDialog] = useToggle(false);
    const code = useMemo(() => {
        return isEmpty(resultParam) ? '' : JSON.stringify(omit(resultParam, ['Content']), null, 4);
    }, [resultParam]);

    const contentCode = useMemo(() => {
        return resultParam?.Content ? JSON.stringify(resultParam.Content, null, 4) : ''
    }, [resultParam]);

    return (<div>
        <div className='intlc-broadcast-result__inner'>
            <div className='intlc-broadcast__header'>
                <h3>测试结果</h3>
            </div>
            <div className='intlc-broadcast__body'>
                <div className='intlc-broadcast__result'>
                    {
                        testStatus === STATUS.SUCCESS
                            ? <>
                                <Icon size="s" type="success" />
                                <span className='intlc-broadcast-result__text'>测试通过</span>
                            </>
                            : testStatus === STATUS.FAIL
                                ? <>
                                    <Icon type="error" size="s" />
                                    <span className='intlc-broadcast-result__text'>测试失败</span>
                                    <Bubble content="返回上一步重新填写测试参数">
                                        <Icon type="help" style={{ marginLeft: 6 }} />
                                    </Bubble>
                                </>
                                : <>
                                    <Icon type="not" size="s" />
                                    <span className='intlc-broadcast-result__text'>未开始测试</span>
                                </>

                    }
                </div>
                <div className='intlc-broadcast__body'>
                    <Tabs ceiling animated={false} tabs={tabConfig} tabBarStyle={{ position: 'relative', top: '20px', padding: 0 }}>
                        <TabPanel id="result">
                            <div className='intlc-broadcast-editor'>
                                <CodeHighLighter codeText={code} language="json" />
                            </div>
                        </TabPanel>
                        <TabPanel id="content">
                            <div className='intlc-broadcast-editor'>
                                <CodeHighLighter codeText={contentCode} language="json" />
                            </div>
                        </TabPanel>
                    </Tabs>
                </div>

            </div>
        </div>
        <div className='intlc-broadcast-result__inner'>
            <div className='intlc-broadcast__operate'>
                <Form>
                    <Form.Item
                        required
                        label="是否启用"
                        tips="测试通过后，才可以启用"
                    >
                        <Switch
                            disabled={!statusEditable}
                            value={online}
                            onChange={value => setOnline(value)}
                        ></Switch>
                    </Form.Item>
                </Form>

                <Button htmlType="button" type="primary" onClick={() => onStrategyCreate(online)}>
                    提交
                </Button>
                <Button htmlType="button" style={{ marginLeft: 8 }} onClick={() => openQuitDialog()}>
                    取消
                </Button>
            </div>
        </div>
        <Modal visible={quitDialogVisiable} caption="退出录入" onClose={closeQuitDialog}>
            <Modal.Body>当前策略信息还没有被保存，确认退出吗？</Modal.Body>
            <Modal.Footer>
                <Button type="primary" onClick={() => history.goBack()}>确定</Button>
                <Button type="weak" onClick={closeQuitDialog}>取消</Button>
            </Modal.Footer>
        </Modal>
    </div >
    );
};
export default ResultViewPanel;
