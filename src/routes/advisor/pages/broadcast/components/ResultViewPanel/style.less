.intlc-broadcast-result__inner{
  text-align: left;
  white-space: normal;
  vertical-align: middle;
  min-width: 420px;
  width: 900px;
  margin: 30px auto;
  background-color: #fff;
  box-shadow: 2px 2px 4px 0 rgb(54 58 80 / 32%);
  border-radius: 0;
  padding: 25px;
  box-sizing: border-box;
  .intlc-broadcast__header{
    font-size: 14px;
    min-height: 22px;
    color: #000;
    position: relative;
    margin-bottom: 10px;
  }

  .intlc-broadcast__body{
    color: rgba(0, 0, 0, 0.9);
    word-break: break-word;
    word-wrap: break-word;

    .intlc-broadcast__result {
      display: flex;
      align-items: center;
      margin-bottom: 10px;

      .intlc-broadcast-result__text {
        margin-left: 7px;
        font-size: 16px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }

  .intlc-broadcast__operate {
    display: flex;
    justify-content: flex-start;
    margin: 20px auto; 
  }

  .intlc-broadcast-editor {
    margin-top: -15px;
  }
}