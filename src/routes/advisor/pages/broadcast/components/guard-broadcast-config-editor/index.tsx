import React, { useMemo } from 'react';
import { Card, Layout, Alert, List } from '@tencent/tea-component';
import { GuardBaseConfigPanel } from '../guard-base-config-panel';
import { useHistory } from '@tea/app';

const { Body, Content } = Layout;

export function GuardBroadcastConfigEditor(match) {
	const history = useHistory();
	const isExistStrategy = useMemo(() => !!+match.match.params.strategyid, [match]);

	return (
		<Body>
			<Content>
				<Content.Header
					title={isExistStrategy ? '编辑节点监控指标' : '新增节点监控指标'}
					onBackButtonClick={() => history.push('/advisor/monitor-config')}
					showBackButton={true}
				/>
				<Content.Body>
					<Alert>
						<h4>特别说明:</h4>
						<List type="bullet">
							<List.Item>
								数据来源：各云产品的基础监控指标，通过云监控接口获取。
							</List.Item>
						</List>
					</Alert>
					<Card>
						<Card.Body>
							<section>
								<div>
									<GuardBaseConfigPanel strategyId={+match.match.params.strategyid}/>
								</div>
							</section>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body >
	);
}
