
import React, { useEffect, useState } from 'react';
import { Button, Form, Input, TextArea, InputAdornment, message, Select, TagSelect } from '@tencent/tea-component';
import { subTypeOption, typeOption } from '../../conf/broadcast-config';
import { useHistory } from '@tea/app';
import {
	getSupportBroadCastMetric,
	GetConfiguredMonitorMetricStrategy,
	CreateMonitorMetricStrategy,
	UpdateMonitorMetricStrategy,
	GetMonitorProductList,
} from '@src/api/advisor/broadcast';
import { useCheckIfAuthed, NoPermission, PermissionLoading } from '../../hooks/checkIfAuthed';
import StuffSearchTdesign from '../stuff-search-tdesign';
import { find, map, omit, pick, reduce, isEmpty } from 'lodash';
import { useForm, Controller } from 'react-hook-form';
import './index.less';

const compareSelectOption = ['>', '=', '<'];
interface IProps {
	strategyId: number;
}

export function BaseInfoConfigPanel({ strategyId }: IProps) {
	let isAuth; let authLoading;
	const {
		control,
		watch,
		setValue,
		handleSubmit,
		formState: { errors },
	} = useForm({ mode: 'all' });
	const operator = localStorage.getItem('engName');
	if (strategyId > 0) {
		const { isAuth: isAuthOther, loading } = useCheckIfAuthed({
			pageStatus: 'broadcast-config-editor',
			key: 'MonitorBroadcastStrategyId',
			value: `${strategyId}`,
		});
		isAuth = isAuthOther;
		authLoading = loading;
	}
	const history = useHistory();
	const [loading, setLoading] = useState(false);
	const [products, setProducts] = useState([]);
	const [nameSpaceMaps, setNameSpaceMaps] = useState({});
	const [metricListMap, setMetricListMap] = useState({});

	const [thresholdFactor, setThresholdFactor] = useState('=');
	const [thresholdUnit, setThresholdUnit] = useState('');
	const [editProducts, setEditProducts] = useState([]);

	const WatchProductId = watch('ProductId');
	const WatchNamespace = watch('Namespace');
	const WatchMetric = watch('Metric');

	const getStatus = (fieldState) => {
		if (fieldState?.error?.message) {
			return 'error';
		}
		if (!fieldState.isDirty) {
			return undefined;
		}
		return fieldState.invalid ? 'error' : 'success';
	};

	// 获取播报策略信息
	const getConfiguredMonitorMetricStrategy = async (strategyIdParams) => {
		if (!strategyIdParams || isEmpty(editProducts)) return;
		try {
			const res: any = await GetConfiguredMonitorMetricStrategy({
				StrategyId: strategyIdParams,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const metricStrategy = res?.Info;
			Object.keys(metricStrategy).map((item) => {
				if (item === 'Threshold') {
					setThresholdFactor(metricStrategy[item][0]?.Factor);
					setThresholdUnit(metricStrategy[item][0]?.Unit);
					setValue('ThresholdValue', metricStrategy[item][0]?.Values);
				} else if (item === 'Expert') {
					if (metricStrategy[item][0]) {
						setValue('Expert', metricStrategy[item]);
					} else {
						setValue('Expert', []);
					}
				} else if (item === 'Dimensions') {
					if (metricStrategy[item][0]) {
						setValue('Dimensions', metricStrategy[item]);
					} else {
						setValue('Dimensions', []);
					}
				} else if (item === 'ProductId' && !!strategyId) {
					const productItem = find(editProducts, i => (i?.ProductId === metricStrategy[item])) || {};
					setValue(item, productItem?.ProductName);
				} else {
					setValue(item, metricStrategy[item]);
				}
			});
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取新建指标的配置信息
	const getBroadCastMetric = async () => {
		if (!!strategyId) return;
		try {
			const res: any = await getSupportBroadCastMetric();
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setProducts(map(res?.ProductList, item => ({ text: item.ProductName,	value: item.Product	})));
			const nameSpaceMaps = reduce(res?.ProductNameSpaceList, (acc, cur) => {
				acc[cur.Product] = cur.NameSpaceList;
				return acc;
			}, {});
			setNameSpaceMaps(nameSpaceMaps);
			const metricMaps = reduce(res?.NameSpaceMetricList, (acc, cur) => {
				acc[cur.NameSpace] = cur.MetricList;
				return acc;
			}, {});
			setMetricListMap(metricMaps);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取云产品列表
	const getMonitorProductList = async () => {
		if (!strategyId) return;
		try {
			const res: any = await GetMonitorProductList();
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setEditProducts(res?.List);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const compareSelect = (
		<Select
			matchButtonWidth
			options={compareSelectOption.map(value => ({ value }))}
			value={thresholdFactor}
			onChange={value => setThresholdFactor(value)}
		/>
	);

	const unitInput = (
		<Input
			size="xs"
			placeholder="单位"
			disabled
			value={thresholdUnit}
			onChange={value => setThresholdUnit(value)}
		/>
	);

	const onSubmitHandle = async (values) => {
		setLoading(true);
		let params = omit(values, ['ThresholdValue']);
		params.Threshold = [{
			Factor: thresholdFactor,
			Unit: thresholdUnit,
			Values: +values.ThresholdValue,
		}];
		const metricObj = find(metricListMap[WatchNamespace], item => (item.Metric === values?.Metric)) || {};
		params.Metric = metricObj?.Metric;
		params.ChatBIMetricId = metricObj?.ChatBIMetricId;
		params.MetricName = metricObj?.MetricName;

		params.Operator = operator;
		if (!!strategyId) {
			params = pick(params, ['Threshold', 'Expert', 'Operator', 'Desc']);
			params.StrategyId = strategyId;
		}
		const apiName = !!strategyId
			? UpdateMonitorMetricStrategy
			: CreateMonitorMetricStrategy;
		try {
			const res: any = await apiName(params);
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			message.success({ content: '操作成功' });
			history.push('/advisor/config-management?tab=2');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		getBroadCastMetric();
		getMonitorProductList();
		setValue('SubType', 'usage');
	}, []);

	useEffect(() => {
		const metricItem = find(
			metricListMap[WatchNamespace],
			item => item.Metric === WatchMetric
		) || {};
		setThresholdUnit(metricItem?.Unit);
	}, [WatchMetric]);

	useEffect(() => {
		getConfiguredMonitorMetricStrategy(strategyId);
	}, [strategyId, editProducts]);
	if (authLoading) {
		return <PermissionLoading />;
	}
	if (isAuth === false) {
		return <NoPermission />;
	}
	return <div className='intlc-broadcast__inner'>
		<div className='intlc-broadcast__header'>
			<h3>基础信息</h3>
		</div>
		<div className='intlc-broadcast__body'>
			<Form
				layout="fixed"
				fixedLabelWidth={100}
			>
				<Controller
					name="SubType"
					control={control}
					rules={{
						validate: value => (!value ? '请选择类型' : undefined),
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							label="类型"
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.SubType?.message}
						>
							<Select
								style={{ width: '100%' }}
								{...field}
								appearance="button"
								matchButtonWidth
								disabled
								placeholder="请选择类型"
								options={subTypeOption.map(item => ({ value: item.value, text: item.text }))}
							/>
						</Form.Item>
					)}
				/>
				<Controller
					name="Type"
					control={control}
					rules={{
						validate: value => (!value ? '请选择维度' : undefined),
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							label="维度"
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.Type?.message}
						>
							<Select
								style={{ width: '100%' }}
								{...field}
								matchButtonWidth
								appearance="button"
								disabled={!!strategyId}
								placeholder="请选择维度"
								options={typeOption.map(item => ({
									value: item.value,
									text: item.text,
									disabled: item.disabled,
								 }))}
							/>
						</Form.Item>
					)}
				/>
				<Controller
					name="ProductId"
					control={control}
					rules={{
						validate: value => (!value ? '请选择云产品' : undefined),
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							label="云产品"
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.ProductId?.message}
						>
							{
								!!strategyId
									? <Input
										{...field}
										style={{ width: '100%' }}
										disabled
									/>
									: <Select
										style={{ width: '100%' }}
										{...field}
										disabled={!!strategyId}
										onChange={(value) => {
											field.onChange(value);
											setValue('Namespace', '');
											setValue('Metric', '');
											setValue('ThresholdValue', '');
											setValue('Dimensions', []);
											setThresholdUnit('');
										}}
										searchable
										matchButtonWidth
										appearance="button"
										placeholder="请选择云产品"
										options={products}
									/>
							}
						</Form.Item>
					)}
				/>
				<Controller
					name="Namespace"
					control={control}
					rules={{
						validate: value => (!value ? '请选择命名空间' : undefined),
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							label="命名空间"
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.Namespace?.message}
						>
							{
								!!strategyId
									? <Input
										{...field}
										style={{ width: '100%' }}
										disabled
									/>
									: <Select
										style={{ width: '100%' }}
										{...field}
										disabled={!WatchProductId || !!strategyId}
										onChange={(value) => {
											field.onChange(value);
											setValue('Metric', '');
											setValue('Dimensions', []);
											setValue('ThresholdValue', '');
											setThresholdUnit('');
										}}
										searchable
										matchButtonWidth
										appearance="button"
										placeholder="请选择命名空间"
										options={nameSpaceMaps[WatchProductId]?.map(item => ({
											text: item,
											value: item,
										}))}
									/>
							}
						</Form.Item>
					)}
				/>
				{
					!strategyId
					&& <Controller
						name="Metric"
						control={control}
						rules={{
							validate: (value) => {
								if (!value) {
									return '请选择节点监控指标';
								}
								return undefined;
							},
						}}
						render={({ field, fieldState }) => (
							<Form.Item
								required
								label="节点监控指标"
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.Metric?.message}
							>
								<Select
									style={{ width: '100%', maxWidth: 370 }}
									{...field}
									onChange={(value) => {
										field.onChange(value);
										const metricItem = find(
											metricListMap[WatchNamespace],
											item => item.Metric === value
										) || {};
										setValue('Dimensions', metricItem?.Dimensions);
										setValue('ThresholdValue', '');
										setThresholdUnit(metricItem?.Unit);
									}}
									searchable
									matchButtonWidth
									disabled={!WatchNamespace || !!strategyId}
									appearance="button"
									placeholder="请选择节点监控指标"
									options={metricListMap[WatchNamespace]?.map(item => ({
										text: item.MetricName,
										value: item.Metric,
									}))}
								/>
							</Form.Item>
						)}
					/>
				}
				{
					!!strategyId
					&& <Controller
						name="MetricName"
						control={control}
						rules={{
							validate: (value) => {
								if (!value) {
									return '请选择节点监控指标';
								}
								return undefined;
							},
						}}
						render={({ field, fieldState }) => (
							<Form.Item
								required
								label="节点监控指标"
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.MetricName?.message}
							>
								<Input {...field} style={{ width: '100%' }} disabled/>
							</Form.Item>
						)}
					/>
				}
				<Controller
					name="Dimensions"
					control={control}
					rules={{
						validate: (value) => {
							if (!value) {
								return '请输入维度';
							}
							return undefined;
						},
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							label="节点监控维度"
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.Dimensions?.message}
						>
							<TagSelect
								{...field}
								disabled
								options={map(metricListMap[WatchNamespace]?.dimensions, item => ({
									text: item,
									value: item,
								 }))}
							/>
						</Form.Item>
					)}
				/>
				<Controller
					name="ThresholdValue"
					control={control}
					rules={{
						validate: async (value) => {
							if (!value) {
								return '请输入阈值';
							}
							if (value < 0) {
								return '请输入大于等于零的阈值';
							}
							return undefined;
						},
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							label="默认阈值"
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.ThresholdValue?.message}
						>
							<InputAdornment before={compareSelect} after={unitInput}>
								<Input
									{...field}
									size="s"
									disabled={!WatchMetric}
									type="number"
									placeholder="请输入阈值"
									autoComplete="off"
								/>
							</InputAdornment>
						</Form.Item>
					)}
				/>
				<Controller
					name="Expert"
					control={control}
					render={({ field, fieldState }) => (
						<Form.Item
							label="专项接口人"
							className='broadcastt-statistics-select'
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.Expert?.message}
						>
							<StuffSearchTdesign value={field.value} onChange={field.onChange} />
						</Form.Item>
					)}
				/>
				<Controller
					name="Desc"
					control={control}
					rules={{
						validate: value => (!value ? '请输入播报项说明' : undefined),
					}}
					render={({ field, fieldState }) => (
						<Form.Item
							required
							label="播报项说明"
							status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
							message={errors.Desc?.message}
						>
							<TextArea
								style={{ width: '100%' }}
								{...field}
								placeholder="请输入播报项说明"
								maxLength={255}
							/>
						</Form.Item>
					)}
				/>
			</Form>
		</div>
		<hr />
		<div className='broadcast-config-opt'>
			<Button
				loading={loading}
				type="primary"
				onClick={handleSubmit(onSubmitHandle) as any}
			>
				保存
			</Button>
		</div>
	</div>;
}
