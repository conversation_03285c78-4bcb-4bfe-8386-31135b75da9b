import React, { useEffect, useMemo, useState } from 'react'
import { useForm, useField } from 'react-final-form-hooks';
import { Button, Form, Input, TextArea, InputAdornment, message, Select } from "@tencent/tea-component";
import { getProductsGroups } from '@src/api/advisor/estimate';
import { subTypeOption, typeOption } from '../../conf/config';
import { pick, isEmpty, map, get, reduce } from 'lodash';
import './style.less';

function getStatus(meta, validating) {
    if (meta.active && validating) {
        return "validating";
    }
    if (!meta.touched) {
        return null;
    }
    return meta.error ? "error" : "success";
}

const strategyInfo = ['Product', 'CNName', 'ENName', 'Desc', 'SubType', 'Type', 'Url', 'Action', 'Threshold'];

const compareSelectOption = ['>', '=', '<']

interface Threshold {
    Values: number
    Factor: string
    Unit: string
}
interface StrategyInfo {
    Product: string,
    CNName: string
    ENName: string
    Desc?: string
    SubType: string
    Type: string
    Url: string
    Action: string
    Threshold?: Threshold
}
interface IBaseInfoPanelProps {
    baseStrategyInfo: StrategyInfo
    partEditable: boolean
    onSubmit: any
    onFormInfoChange: Function
}
export function BaseInfoPanel({ baseStrategyInfo, partEditable, onFormInfoChange, onSubmit }: IBaseInfoPanelProps) {
    const [productOption, setProductOption] = useState([]);
    const [thresholdFactor, setThresholdFactor] = useState(get(baseStrategyInfo, 'Threshold[0].Factor'));
    const [thresholdUnit, setThresholdUnit] = useState(get(baseStrategyInfo, 'Threshold[0].Unit'))

    const initialVal = useMemo(() => {
        const initialInfo = reduce(strategyInfo, (ret, item) => {
            ret[item] = '';
            return ret
        }, {});
        return !isEmpty(baseStrategyInfo)
            ? { ...pick(baseStrategyInfo, strategyInfo), Threshold: get(baseStrategyInfo, 'Threshold[0].Values') }
            : initialInfo
    }, [baseStrategyInfo]);

    const compareSelect = (
        <Select
            matchButtonWidth
            options={compareSelectOption.map(value => ({ value }))}
            defaultValue={thresholdFactor}
            onChange={value => setThresholdFactor(value)}
        />
    );

    const unitInput = (
        <Input size="xs" placeholder="单位" defaultValue={thresholdUnit} onChange={value => setThresholdUnit(value)} />
    )
    //获取产品和维度信息
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({ AppId: 1253985742 })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let productNameList = map(res.Products, item => ({
                    text: res.ProductDict[item],
                    value: item
                }));
                setProductOption(productNameList)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    // values：form表单当前值
    // pristine： form表单是否改变
    const { form, handleSubmit, values, pristine, validating, submitting } = useForm({
        onSubmit: value => onSubmit(value, { thresholdFactor, thresholdUnit }),
        initialValuesEqual: () => true,
        initialValues: initialVal,
        // @ts-ignore
        validate: ({ Product, CNName, ENName, SubType, Type, Url, Action }) => ({
            Product: !Product ? "请选择云产品" : undefined,
            CNName: !CNName ? "播报项不能为空" : undefined,
            ENName: !ENName ? '播报项英文不能为空' : undefined,
            SubType: !SubType ? '请选择类型' : undefined,
            Type: !Type ? '请选择维度' : undefined,
            Url: !Url ? '服务器地址不能为空' : undefined,
            Action: !Action ? '接口名不能为空' : undefined
        }),
    });
    const product = useField("Product", form);
    const cnName = useField("CNName", form);
    const enName = useField("ENName", form);
    const desc = useField("Desc", form);
    const subType = useField('SubType', form);
    const type = useField('Type', form);
    const url = useField('Url', form);
    const action = useField('Action', form);
    const threshold = useField('Threshold', form);

    useEffect(() => {
        onFormInfoChange(pristine)
    }, [pristine]);

    useEffect(() => {
        getProductsGroupsInfo();
    }, []);

    return <div className='intlc-broadcast__inner'>
        <div className='intlc-broadcast__header'>
            <h3>基础信息</h3>
        </div>
        <div className='intlc-broadcast__body'>
            <form onSubmit={handleSubmit}>
                <Form>
                    <Form.Item
                        required
                        label="云产品"
                        status={getStatus(product.meta, validating)}
                        message={getStatus(product.meta, validating) === "error" && product.meta.error}
                    >
                        <Select
                            {...product.input}
                            searchable
                            disabled={partEditable}
                            matchButtonWidth
                            placeholder="请选择云产品"
                            options={productOption}
                            appearance="button"
                            size="m"
                        />
                    </Form.Item>
                    <Form.Item
                        required
                        label="播报项"
                        status={getStatus(cnName.meta, validating)}
                        message={getStatus(cnName.meta, validating) === "error" && cnName.meta.error}
                    >
                        <Input {...cnName.input} autoComplete="off" placeholder="请输入播报项" disabled={partEditable} />
                    </Form.Item>
                    <Form.Item
                        required
                        label="Policy Name"
                        status={getStatus(enName.meta, validating)}
                        message={getStatus(enName.meta, validating) === "error" && enName.meta.error}
                    >
                        <Input {...enName.input} autoComplete="off" placeholder="请输入播报项英文" disabled={partEditable} />
                    </Form.Item>
                    <Form.Item
                        label="播报项说明"
                        status={getStatus(desc.meta, validating)}
                        message={getStatus(desc.meta, validating) === "error" && desc.meta.error}
                    >
                        <TextArea {...desc.input} autoComplete="off" placeholder="比如一些计算规则或者时间精度等说明" />
                    </Form.Item>
                    <Form.Item
                        required
                        label="类型"
                        status={getStatus(subType.meta, validating)}
                        message={getStatus(subType.meta, validating) === "error" && subType.meta.error}
                    >
                        <Select
                            {...subType.input}
                            disabled={partEditable}
                            matchButtonWidth
                            placeholder="请选择类型"
                            options={subTypeOption.filter(item => item.value !== 'all')}
                            appearance="button"
                            size="m"
                        />
                    </Form.Item>
                    <Form.Item
                        required
                        label="维度"
                        status={getStatus(type.meta, validating)}
                        message={getStatus(type.meta, validating) === "error" && type.meta.error}
                    >
                        <Select
                            {...type.input}
                            disabled={partEditable}
                            matchButtonWidth
                            placeholder="请选择维度"
                            options={typeOption.filter(item => item.value !== 'all')}
                            appearance="button"
                            size="m"
                        />
                    </Form.Item>
                    <Form.Item
                        required
                        label="服务器地址"
                        status={getStatus(url.meta, validating)}
                        message={getStatus(url.meta, validating) === "error" && url.meta.error}
                    >
                        <Input {...url.input} autoComplete="off" placeholder="http://xxxx:8080/xxx" />
                    </Form.Item>
                    <Form.Item
                        required
                        label="接口名"
                        status={getStatus(action.meta, validating)}
                        message={getStatus(action.meta, validating) === "error" && action.meta.error}
                    >
                        <Input {...action.input} autoComplete="off" placeholder="Describexxxx" />
                    </Form.Item>
                    <Form.Item
                        label="默认阈值"
                        status={getStatus(threshold.meta, validating)}
                        message={getStatus(threshold.meta, validating) === "error" && threshold.meta.error}
                    >
                        <InputAdornment before={compareSelect} after={unitInput}>
                            <Input {...threshold.input} autoComplete="off" size="s" placeholder="请输入阈值" />
                        </InputAdornment>
                    </Form.Item>
                </Form>
                <Form.Action className='intlc-broadcast__operate'>
                    <Button
                        type="primary"
                        htmlType="submit"
                        loading={submitting}
                        disabled={validating}
                    >
                        保存
                    </Button>
                </Form.Action>
            </form>
        </div>
    </div>
} 