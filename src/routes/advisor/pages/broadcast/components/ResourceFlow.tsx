import React, { useState, useEffect, FC, useMemo } from 'react';
import { map, uniq, filter, find } from "lodash";
import { DescribeGuardSheet } from '@src/api/advisor/guard';
import { ExtensibleInput } from '../components'
import { message, Button, Table, Select } from '@tencent/tea-component';

interface IResourceSupplePanelProps {
    guardId: number
    editable: boolean
    resourceDetail: any
    onSourceListChange: Function
}

const ResourceFlow: FC<IResourceSupplePanelProps> = ({ guardId, editable, resourceDetail, onSourceListChange }: IResourceSupplePanelProps) => {
    const { Product, ResourceIds } = resourceDetail
    const [instanceList, setInstanceList] = useState([]);

    const [appIdOption, setAppIdOption] = useState([])

    useEffect(() => {
        let instances = map(ResourceIds, ({ InstanceId }, index) => {
            let resourceInfo = InstanceId.split('/');
            if (resourceInfo[0]) {
                return {
                    Id: index,
                    AppId: resourceInfo[0],
                    InstanceIds: resourceInfo[1]?.split(';')
                }
            }
            else {
                return {}
            }
        })
        setInstanceList(instances)
    }, [ResourceIds]);

    useEffect(() => {
        let targetResource = map(instanceList, ({ AppId, InstanceIds }) => ({
            AppId: +AppId,
            Product,
            InstanceId: InstanceIds.join(';'),
        }))
        onSourceListChange(targetResource, Product)
    }, [instanceList])

    useEffect(() => {
        getGuardSheet()
    }, []);

    const filterSelectOption = useMemo(() => {
        return map(appIdOption, i => {
            let temp = find(instanceList, item => item.AppId === i.value);
            return temp ? { ...i, disabled: true } : i;
        })
    }, [appIdOption, instanceList])

    // 获取护航信息
    const getGuardSheet = async () => {
        try {
            let filters = [{ Name: 'guard_id', Values: [guardId + ''] }];
            const res = await DescribeGuardSheet({
                Filters: filters.filter(i => { if (i.Values.length) { return i } }),
                Offset: 0,
                Limit: 1,
                AppId: 1253985742, //接口必须传appid
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let item = res.Guard[0];
                if (item) {
                    const appIdList = item.InstanceTemplateCount ? Object.keys(item.InstanceTemplateCount) : []
                    setAppIdOption(map(appIdList, item => ({ text: item, value: item })));
                }
                else {
                    setAppIdOption([]);
                }
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    const columns = [
        {
            key: "AppId",
            header: "APPID",
            render: item => {
                const handleSelectChange = value => {
                    let cur = map(instanceList, i => {
                        if (i.Id === item.Id) {
                            i.AppId = value
                        }
                        return i
                    })
                    setInstanceList(cur);
                }
                return <>
                    <Select
                        value={item.AppId}
                        matchButtonWidth
                        options={filterSelectOption}
                        appearance="button"
                        size="m"
                        onChange={value => handleSelectChange(value)}
                    />
                </>
            }
        },
        {
            key: "Id",
            header: "流ID",
            render: item => {
                const handleInputChange = ({ type, index = 0, value = '' }) => {
                    let cur = map(instanceList, i => {
                        if (i.Id === item.Id) {
                            if (type === 'ADD') {
                                i.InstanceIds.push('')
                            }
                            else if (type === 'DELETE') {
                                i.InstanceIds.splice(index, 1)
                            }
                            else if (type === 'CHANG') {
                                i.InstanceIds[index] = value
                            }
                        }
                        return i
                    })
                    setInstanceList(cur);
                }
                return <>
                    <ExtensibleInput
                        initialList={item.InstanceIds}
                        onInputAdd={() => handleInputChange({ type: 'ADD' })}
                        onInputDelete={index => handleInputChange({ type: 'DELETE', index })}
                        onInputChange={(value, index) => handleInputChange({ type: 'CHANG', index, value })}
                    />
                </>
            }
        },
        {
            key: "",
            header: "操作",
            width: '10%',
            render: item => <Button
                type="link"
                onClick={() => setInstanceList(filter(instanceList, i => i.Id !== item.Id))}
            >删除</Button>
        }
    ];

    const tableOptionProps = {
        recordKey: 'Id',
        verticalTopL: true,
        compact: true,
        bordered: true,
        columns,
        records: instanceList,
    }

    const handleTableRowAdd = () => {
        setInstanceList(instanceList.concat({ Id: instanceList.length, AppId: 0, InstanceIds: [''] }))
    }

    return (
        <div>
            <p style={{ fontSize: '14px', fontWeight: 'bold', margin: 10, marginLeft: 0 }}>
                请补充信息
            </p>
            <div style={{ position: 'relative', width: '80%' }}>
                {!editable && <div className='content-mask'></div>}
                {/* @ts-ignore */}
                <Table {...tableOptionProps} />
                {
                    instanceList?.length < appIdOption?.length
                    && <Button type="link" htmlType="button" onClick={handleTableRowAdd}>添加</Button>
                }
            </div>
        </div>
    );
};
export default ResourceFlow;
