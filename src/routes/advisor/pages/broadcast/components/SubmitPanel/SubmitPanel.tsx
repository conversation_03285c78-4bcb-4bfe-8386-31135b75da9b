import React, { useState, FC } from 'react';
import './style.less';
import { Form, Switch } from "@tencent/tea-component";
import { SOURCE_STATUS } from '../../conf/config'

interface ISubmitPanelProps {
	statusDisable: boolean
	strategyStatus: number
	onSwitchChange: Function
}
const SubmitPanel: FC<ISubmitPanelProps> = ({ statusDisable, strategyStatus, onSwitchChange }: ISubmitPanelProps) => {

	const renderSwitchTip = (
		<>
			<p>无法启用原因：</p>
			<p>1. 常规播报和组合播报都未打开播报</p>
			<p>2. 打开常规播报，策略列表中没有选择播报策略</p>
			<p>3. 选择播报资源中，除了直播流ID外，有产品没有选择播报资源</p>
		</>
	)
	return (
		<div className='intlc-broadcast-submit__inner'>
			<div className='intlc-broadcast__header'>
				<h3>播报状态</h3>
			</div>
			<div className='intlc-broadcast__body'>
				<Form>
					<Form.Item label="是否启用" required tips={renderSwitchTip}>
						<Switch
							disabled={!statusDisable}
							defaultValue={strategyStatus === SOURCE_STATUS.SUCCESS}
							onChange={value => onSwitchChange(value)}
						/>
					</Form.Item>
				</Form>
			</div>
		</div>
	);
};
export default SubmitPanel;
