

import React, { useState, useEffect } from 'react';
import './style.less';
import { Button, Input } from '@tencent/tea-component';
interface IExtensibleInputProps {
    initialList: Array<string>
    disableIndex?: number
    limit?: number
    onInputAdd: Function
    onInputChange: Function
    onInputDelete: Function
}

const ExtensibleInput = ({ initialList, disableIndex = -1, limit = 50, onInputAdd, onInputChange, onInputDelete }: IExtensibleInputProps) => {
    return (
        <>
            <div className='intlc-broadcast__unit'>
                <ul className='intlc-broadcast__list'>
                    {
                        initialList?.map((item, index) => (
                            <div key={index} className='intlc-broadcast__item'>
                                <div className='intlc-broadcast-number'>{index + 1}</div>
                                <div>
                                    <Input autoComplete="off" disabled={disableIndex === index} value={item} onChange={value => onInputChange(value, index)} />
                                    {
                                        initialList?.length > 1
                                        && disableIndex !== index
                                        && <Button icon="dismiss" htmlType="button" style={{ marginLeft: 8 }} title="删除" onClick={() => onInputDelete(index)} />
                                    }
                                </div>
                            </div>)
                        )
                    }
                </ul>
                {
                    initialList?.length < limit
                    && <Button type="link" htmlType="button" onClick={() => onInputAdd()}>添加</Button>
                }
            </div>
        </>
    );
};
export default ExtensibleInput;
