import React, { useState } from 'react';
import { Select } from 'tdesign-react';
import { DescribeUserList } from '@src/api/advisor/broadcast';
import useOnceFunc from '@src/hooks/useOnceFunc';
import { debounce } from 'lodash';

interface StuffSearchType {
	value?: string[];
	onChange?: (value: string[]) => void;
}
// 员工搜索组件
export default function StuffSearchTdesign(props: StuffSearchType) {
	const { value, onChange } = props;
	const [options, setOptions] = useState([]);

	// 远程搜索
	const search = debounce((name: string) => {
		const search = name || 'a';
		DescribeUserList({ Name: search, Limit: 20 }).then((d: any) => {
			if (d?.UserList) {
				const newOptions = d?.UserList?.map(item => ({
					value: item.EnName,
					label: item.FullName,
				}));
				setOptions(newOptions);
			} else {
				setOptions([]);
			}
		});
	}, 500);

	// 默认加载一次，不至于没有选项看着像bug
	useOnceFunc(() => search(''));

	return (
		<Select
			filterable
			options={options}
			value={value}
			multiple
			placeholder="输入关键词搜索"
			onSearch={search}
			onChange={(value) => {
				onChange?.(value as any);
			}}
		/>
	);
}
