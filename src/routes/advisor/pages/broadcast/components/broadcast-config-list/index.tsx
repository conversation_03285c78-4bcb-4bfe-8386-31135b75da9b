import React, { useState, useEffect, useMemo } from 'react';
import {
	Table,
	message,
	Button,
	Row,
	Card,
	Col,
	Select,
	Text,
	StatusTip,
	Input,
	Tag,
	Icon,
	Alert,
	List,
	Justify,
	Bubble,
	Modal,
} from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { map, find, isEmpty } from 'lodash';
import { useToggle } from '../../hooks/common';
import BroadcastParamsPanel from '../broadcast-params-panel';

import {
	GetMonitorMetricStrategyList,
	GetMonitorProductList,
	UpdateMonitorMetricStrategyStatus,
} from '@src/api/advisor/broadcast';

import { ProductOption } from '@src/types/cloudEscort/broadcast/broadStrategy/page';
import { selectInfoListConfig, subTypeOption, statusOption, typeOption } from '../../conf/config';

const { pageable } = Table.addons;

interface BoardcastListFilter {
	product: string,
	subType: string,
	type: string,
	online: string,
	cnName: string,
}

const defaultFilterOption = {
	product: 'all',
	subType: 'all',
	type: 'all',
	online: 'all',
	cnName: '',
};

enum statusType {
	online = '1',
	offline = '0',
}

export default function BroadcastConfigEditor() {
	const history = useHistory();
	const operator = localStorage.getItem('engName');
	// 过滤项
	const [filter, setFilter] = useState<BoardcastListFilter>(defaultFilterOption);

	// 产品下拉选项
	const [productOption, setProductOption] = useState<Array<ProductOption>>([]);
	// 播报策略列表
	const [broadStrategyList, setBroadStrategyList] = useState([]);

	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	const [modalVisible, setModalVisible] = useState(false);
	const [currentBroadStrategy, setCurrentBroadStrategy] = useState<any>({});

	// 加载状态
	const [isLoading, startLoad, endLoad] = useToggle(false);

	// 获取云产品列表
	const getMonitorProductList = async () => {
		try {
			const res: any = await GetMonitorProductList();
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const MonitorProductList = map(res?.List, item => ({	text: item.ProductName,	value: item.ProductId	}));
			setProductOption([{ text: '全部', value: 'all' }].concat(MonitorProductList));
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getBroadStrategyList = async (filterParams) => {
		startLoad();
		try {
			const { product, subType, type, online, cnName } = filterParams;
			const filters = [
				{ Key: 'product', Value: product === 'all' ? [] : [product] },
				{ Key: 'cnname', Value: [cnName] },
				{ Key: 'type', Value: type === 'all' ? [] : [type] },
				{ Key: 'sub_type', Value: subType === 'all' ? [] : [subType] },
				{ Key: 'online', Value: online === 'all' ? [] : [online] },
			];
			const params = {
				AppId: 1253985742,
				Filters: filters,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
				Operator: operator,
			};
			const res = await GetMonitorMetricStrategyList(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setBroadStrategyList(res.List);
				setTotalPage(res.Total);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};
	const selectInfoListOption = useMemo(() => map(selectInfoListConfig, item => (item.value === 'product' ? { ...item, optionsConfig: productOption } : item)), [productOption]);

	useEffect(() => {
		getMonitorProductList();
	}, []);

	useEffect(() => {
		setPageNumber(1);
	}, [filter]);

	useEffect(() => {
		getBroadStrategyList(filter);
	}, [pageSize, pageNumber]);

	const handleBroadcastStatusChange = async (StrategyId: string, EnabledStatus: number) => {
		try {
			const res: any = await UpdateMonitorMetricStrategyStatus({
				StrategyId,
				EnabledStatus,
				Operator: operator,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setPageNumber(1);
				getBroadStrategyList(filter);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	const columns = [
		{
			key: 'StrategyId',
			align: 'left',
			header: '播报项ID',
		},
		{
			key: 'ProductName',
			align: 'left',
			header: '云产品',
			render: (item) => {
				const nameTxt = find(productOption, product => product.value === item.ProductId);
				return (<>{isEmpty(nameTxt) ? '-' : nameTxt.text}</>);
			},
		},
		{
			key: 'StrategyName',
			align: 'left',
			header: '播报项',
			width: '13%',
			render: item => (<Text tooltip>{item.StrategyName}</Text>),
		},
		{
			key: 'SubType',
			align: 'left',
			header: '类型',
			render: (item) => {
				const subTypeTxt = find(subTypeOption, subType => subType.value === item.SubType) || {};
				return (<>{isEmpty(subTypeTxt) ? '-' : subTypeTxt.text}</>);
			},
		},
		{
			key: 'Type',
			align: 'left',
			header: '维度',
			render: (item) => {
				const typeTxt = find(typeOption, type => type.value === item.Type) || {};
				return (<>{isEmpty(typeTxt) ? '-' : typeTxt.text}</>);
			},
		},
		{
			key: 'MetricName',
			align: 'left',
			header: '监控指标',
			width: '13%',
			render: item => (<Text tooltip>{item.MetricName}</Text>),
		},
		{
			key: 'Expert',
			align: 'left',
			header: '专项接口人',
			render: (item) => {
				const expert = item?.Expert?.split(';');
				return (<>
					{
						expert?.map(item => (
							<Tag theme="default">{item}</Tag>
						))
					}
				</>);
			},
		},
		{
			key: 'CreatedBy',
			header: '创建人|更新人',
			align: 'left',
			render: (item) => {
				const { CreatedBy, UpdatedBy } = item;
				return (<>
					{CreatedBy && <><Tag theme="primary">{CreatedBy}</Tag><br /></>}
					{UpdatedBy && <Tag theme="primary">{UpdatedBy}</Tag>}
				</>);
			},
		},
		{
			key: 'CreatedAt',
			header: '创建时间|更新时间',
			align: 'left',
			width: '12%',
			render: (item) => {
				const { CreatedAt, UpdatedAt } = item;
				return (<>
					{CreatedAt && <><Tag >{CreatedAt}</Tag><br /></>}
					{UpdatedAt && <Tag >{UpdatedAt}</Tag>}
				</>);
			},
		},
		{
			key: 'EnableStatus',
			align: 'left',
			header: '状态',
			render: (item) => {
				const status = find(statusOption, status => status.value === `${item.EnableStatus}`) || {};
				return (<div style={{ display: 'flex', alignItems: 'center' }}>
					{status.value === statusType.online ? <Icon type="success" /> : <Icon type="not" />}
					<span style={{ marginLeft: '3px' }}>{status.text}</span>
				</div>);
			},
		},
		{
			key: '',
			align: 'left',
			header: '操作',
			width: 200,
			render: item => (
				<div className='braodcast-config-btn'>
					<Button type="link" disabled={!item?.IsEnableAuth && item?.CreatedBy !== operator} onClick={() => {
						history.push({ pathname: `/advisor/broadcast-config-editor/${item.StrategyId}` });
					}}>
        		编辑
					</Button>
					<Button
						type="link"
						onClick={() => {
							setModalVisible(true);
							setCurrentBroadStrategy(item);
						}}
						disabled={!item?.IsEnableAuth}
					>
        		适配
					</Button>
					<Bubble
						arrowPointAtCenter
						placement="top"
						trigger="hover"
						content={!item?.IsEnableAuth ? '播报策略启停请联系管理员fisherhhyu;pumbaahu;hertzzhang' : ''}
					>
						<Button
							type="link"
							style={{ marginLeft: 8 }}
							onClick={() => handleBroadcastStatusChange(item.StrategyId, +statusType.online)}
							disabled={item.EnableStatus === +statusType.online || !item?.IsEnableAuth}
						>
        			启用
						</Button>
					</Bubble>
					<Bubble
						arrowPointAtCenter
						placement="top"
						trigger="hover"
						content={!item?.IsEnableAuth ? '播报策略启停请联系管理员fisherhhyu;pumbaahu;hertzzhang' : ''}
					>
						<Button
							type="link"
							style={{ marginLeft: 8 }}
							onClick={() => handleBroadcastStatusChange(item.StrategyId, +statusType.offline)}
							disabled={item.EnableStatus === +statusType.offline || !item?.IsEnableAuth}
						>
							停用
						</Button>
					</Bubble>
				</div>
			),
		},
	];

	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	const tableOptionProps = {
		style: { marginTop: 15 },
		recordKey: 'StrategyId',
		verticalTopL: true,
		columns,
		records: broadStrategyList,
		topTip: isLoading ? <StatusTip status="loading"></StatusTip> : broadStrategyList.length === 0 && <StatusTip status="empty" />,
		addons: [
			pageable({
				pageIndex: pageNumber,
				recordCount: totalPage,
				onPagingChange: handlePageChange,
			}),
		],
	};

	const handleCreateBtnClick = () => {
		history.push({
			pathname: '/advisor/broadcast-config-editor/0',
		});
	};

	return (
		<>
			<Alert style={{ marginTop: 20 }}>
				<h4>特别说明:</h4>
				<List type="bullet">
					<List.Item>
						当前支持基于护航节点监控已支持的指标进行播报策略配置
					</List.Item>
				</List>
			</Alert>
			<Card>
				<Card.Body>
					<Row gap={15}>
						<>
							{
								selectInfoListOption.map(item => (
									<Col key={item.label}>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">{item.label}</Text>
											</Col>
											<Col span={18} >
												<Select
													appearance="button"
													matchButtonWidth
													searchable={item.value === 'product'}
													size="full"
													options={item.optionsConfig}
													value={filter[item.value]}
													onChange={value => setFilter({ ...filter, [item.value]: value })}
													placeholder="请选择"
												/>
											</Col>
										</Row>
									</Col>
								))
							}
						</>
					</Row>
					<Row verticalAlign={'middle'} gap={15}>
						<Col span={6}>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">播报项</Text>
								</Col>
								<Col span={18}>
									<Input size='full' value={filter.cnName} onChange={value => setFilter({ ...filter, cnName: value })} />
								</Col>
							</Row>
						</Col>
					</Row>
					<div style={{ marginTop: 10, textAlign: 'center' }}>
						<Button
							type="primary"
							onClick={() => {
								getBroadStrategyList(filter);
							}}
						>
              查询
						</Button>
						<Button style={{ marginLeft: 15 }} onClick={() => {
							setFilter(defaultFilterOption);
							getBroadStrategyList(defaultFilterOption);
						}}>
							重置
						</Button>
					</div>
				</Card.Body>
			</Card>
			<Card>
				<Card.Body>
					<Justify left={<Button type="primary" onClick={handleCreateBtnClick}>新建</Button>} />
					{/* @ts-ignore */}
					<Table {...tableOptionProps} />
				</Card.Body>
			</Card>
			<Modal
				visible={modalVisible}
				size='l'
				caption="适配字段（JSON）"
				onClose={() => {
					setModalVisible(false);
					setCurrentBroadStrategy({});
				}}
			>
				<BroadcastParamsPanel
					strategyId={currentBroadStrategy?.StrategyId}
					close={() => {
						setModalVisible(false);
						setCurrentBroadStrategy({});
					}}
				/>
			</Modal>
		</>
	);
}
