import React, { useEffect, useMemo, useState } from 'react'
import { useForm, useField } from 'react-final-form-hooks';
import { Button, RadioGroup, Radio, Form, Input, message, Select } from "@tencent/tea-component";
import { DescribeProductRegionAndZone } from '@src/api/advisor/guard';
import { ExtensibleInput } from '../../components'
import { CodeHighLighter } from '../CodeHighLighter';

import { pick, isEmpty, map, clone, reduce, isNumber, get } from 'lodash';
import './style.less';

function getStatus(meta, validating) {
    if (meta.active && validating) {
        return "validating";
    }
    if (!meta.touched) {
        return null;
    }
    return meta.error ? "error" : "success";
}

const interfaceBaseInfo = ['AppId', 'Uin', 'SubAccountUin', 'Region', 'Period', 'AccountArea', 'Limit', 'Offset'];

interface Filter {
    Values: Array<string>
    Name: string
}
interface InterfaceInfo {
    AppId: string,
    Uin: string
    SubAccountUin?: string
    Region?: string
    ResourceIds: Array<string>
    Period?: string
    Filters?: Array<Filter>
    AccountArea?: number
    Limit?: number
    Offset?: number
}

interface IBaseInfoPanelProps {
    interfaceInfo: InterfaceInfo
    reqTestParams: any
    onSubmit: any,
    onTestTaskStart: Function
}

const defaultResourceIdsList = [''];
const defaultFilters = [
    {
        Name: '',
        Values: ['']
    },
]

export function InterfaceTestPanel({ interfaceInfo, reqTestParams, onSubmit, onTestTaskStart }: IBaseInfoPanelProps) {
    const [regionOption, setRegionOption] = useState([]);
    const [resourceIdsList, setResourceIdsList] = useState(get(interfaceInfo, 'ResourceIds', defaultResourceIdsList));
    const [filters, setFilters] = useState(get(interfaceInfo, 'Filters', defaultFilters))

    const initialVal = useMemo(() => {
        const initialInfo = reduce(interfaceBaseInfo, (ret, item) => {
            ret[item] = '';
            return ret
        }, {});
        return !isEmpty(interfaceInfo)
            ? { ...pick(interfaceInfo, interfaceBaseInfo), AccountArea: interfaceInfo.AccountArea + '' }
            : initialInfo
    }, [interfaceInfo]);

    //获取产品和维度信息
    const getProductRegionAndZone = async () => {
        try {
            const res = await DescribeProductRegionAndZone({ AppId: ********** })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let regionsList = map(res.Regions, ({ Region, RegionName }) => ({
                    text: RegionName,
                    value: Region
                }));
                setRegionOption(regionsList)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    const { form, handleSubmit, validating, submitting } = useForm({
        onSubmit: values => onSubmit({ values, resourceIdsList, filters }),
        initialValuesEqual: () => true,
        initialValues: initialVal,
        // @ts-ignore
        validate: ({ AppId }) => ({
            AppId: !AppId ? "请选择账户AppID" : !isNumber(+AppId) ? '请输入数字' : undefined,
        }),
    });
    const appId = useField("AppId", form);
    const uin = useField("Uin", form);
    const subAccountUin = useField("SubAccountUin", form);
    const region = useField("Region", form);
    const period = useField('Period', form);
    // const accountArea = useField('AccountArea', form);
    // const limit = useField('Limit', form);
    // const offset = useField('Offset', form);

    useEffect(() => {
        getProductRegionAndZone()
    }, []);

    const handleResourceIdChange = (value, index) => {
        const temp = clone(resourceIdsList);
        temp[index] = value;
        setResourceIdsList(temp)
    }

    const handleDeleteClick = index => {
        const temp = clone(resourceIdsList);
        temp.splice(index, 1);
        setResourceIdsList(temp)
    }

    const handleFilterChange = ({ value = '', index = 0, filterIndex = 0, type }) => {
        const temp = clone(filters);
        switch (type) {
            case 'DELETE': temp.splice(filterIndex, 1); break;
            case 'NAME_CHANGE': temp[filterIndex] = { ...temp[filterIndex], Name: value }; break;
            case 'VALUES_ADD': temp[filterIndex] = { ...temp[filterIndex], Values: temp[filterIndex].Values.concat(['']) }; break;
            case 'VALUE_CHANGE': temp[filterIndex].Values[index] = value; break;
            case 'VALUE_DELETE': temp[filterIndex].Values.splice(index, 1); break;
            default:
                break;
        }
        setFilters(temp);
    }

    const code = useMemo(() => {
        return reqTestParams.AppId ? JSON.stringify(reqTestParams, null, 4) : '';
    }, [reqTestParams])

    return <div className='intlc-broadcast-box'>
        <div className='intlc-broadcast__interface'>
            <div className='intlc-broadcast__header'>
                <h3>输入测试参数</h3>
            </div>
            <div className='intlc-broadcast__body'>
                <form onSubmit={handleSubmit}>
                    <Form>
                        <Form.Item
                            required
                            label="APPID"
                            status={getStatus(appId.meta, validating)}
                            message={getStatus(appId.meta, validating) === "error" && appId.meta.error}
                        >
                            <Input {...appId.input} autoComplete="off" placeholder="账户AppId" />
                        </Form.Item>
                        <Form.Item
                            label="Uin"
                            status={getStatus(uin.meta, validating)}
                            message={getStatus(uin.meta, validating) === "error" && uin.meta.error}
                        >
                            <Input {...uin.input} autoComplete="off" placeholder="主账户Uin" />
                        </Form.Item>
                        <Form.Item
                            label="SubAccountUin"
                            status={getStatus(subAccountUin.meta, validating)}
                            message={getStatus(subAccountUin.meta, validating) === "error" && subAccountUin.meta.error}
                        >
                            <Input {...subAccountUin.input} autoComplete="off" placeholder="子账户Uin" />
                        </Form.Item>
                        <Form.Item
                            label="Region"
                            status={getStatus(region.meta, validating)}
                            message={getStatus(region.meta, validating) === "error" && region.meta.error}
                        >
                            <Select
                                {...region.input}
                                searchable
                                matchButtonWidth
                                placeholder="请选择地域"
                                options={regionOption}
                                appearance="button"
                                size="m"
                            />
                        </Form.Item>
                        <Form.Item required label="ResourceIds.N">
                            <ExtensibleInput
                                initialList={resourceIdsList}
                                onInputAdd={() => setResourceIdsList(resourceIdsList.concat(['']))}
                                onInputDelete={handleDeleteClick}
                                onInputChange={handleResourceIdChange}
                            />
                        </Form.Item>
                        <Form.Item
                            label="Period"
                            status={getStatus(period.meta, validating)}
                            message={getStatus(period.meta, validating) === "error" && period.meta.error}
                        >
                            <Input {...period.input} autoComplete="off" placeholder="获取播报信息（调用接口）频率，单位：秒 " />
                        </Form.Item>
                        <Form.Item label="Filters.N">
                            <div className='intlc-broadcast-filter'>
                                <ul className='intlc-broadcast__list'>
                                    {
                                        filters.map((item, filterIndex) => (
                                            <div key={filterIndex} className='intlc-broadcast__item'>
                                                <div className='intlc-broadcast-number'>{filterIndex + 1}</div>
                                                <div className='intlc-broadcast-group'>
                                                    <Form.Item label="Name">
                                                        <Input
                                                            value={item.Name}
                                                            onChange={value => handleFilterChange({ value, filterIndex, type: 'NAME_CHANGE' })}
                                                        />
                                                    </Form.Item>
                                                    <Form.Item label="Values.N">
                                                        <ExtensibleInput
                                                            initialList={item.Values}
                                                            onInputDelete={index => handleFilterChange({ index, filterIndex, type: 'VALUE_DELETE' })}
                                                            onInputAdd={() => handleFilterChange({ filterIndex, type: 'VALUES_ADD' })}
                                                            onInputChange={(value, index) => handleFilterChange({ value, index, filterIndex, type: 'VALUE_CHANGE' })}
                                                        />
                                                    </Form.Item>
                                                </div>
                                                {
                                                    filters.length > 1
                                                    && <Button
                                                        icon="dismiss"
                                                        htmlType="button"
                                                        style={{ marginLeft: 8 }}
                                                        onClick={() => handleFilterChange({ filterIndex, type: 'DELETE' })}
                                                    />
                                                }
                                            </div>
                                        ))
                                    }
                                </ul>
                                <Button type="link" htmlType="button" onClick={() => setFilters(filters.concat([{ Name: '', Values: [''] }]))}>添加</Button>
                            </div>
                        </Form.Item>
                        {/* <Form.Item
                            label="AccountArea"
                            status={getStatus(accountArea.meta, validating)}
                            message={getStatus(accountArea.meta, validating) === "error" && accountArea.meta.error}
                        >
                            <RadioGroup {...accountArea.input}>
                                <Radio name="0">非国际站</Radio>
                                <Radio name='1'>国际站</Radio>
                            </RadioGroup>
                        </Form.Item>
                        <Form.Item
                            label="Offset"
                            status={getStatus(offset.meta, validating)}
                            message={getStatus(offset.meta, validating) === "error" && offset.meta.error}
                        >
                            <Input {...offset.input} autoComplete="off" placeholder="偏移量，默认0" />
                        </Form.Item>
                        <Form.Item
                            label="Limit"
                            status={getStatus(limit.meta, validating)}
                            message={getStatus(limit.meta, validating) === "error" && limit.meta.error}
                        >
                            <Input {...limit.input} autoComplete="off" placeholder="条数" />
                        </Form.Item> */}
                    </Form>
                    <Form.Action className='intlc-broadcast__operate'>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={submitting}
                            disabled={validating}
                        >
                            保存
                        </Button>
                        <Button
                            type="primary"
                            htmlType="button"
                            onClick={() => onTestTaskStart()}
                        >
                            发起测试
                        </Button>
                    </Form.Action>
                </form>
            </div>
        </div>

        <div className='intlc-broadcast__interface'>
            <div className='intlc-broadcast__header'>
                <h3>入参</h3>
            </div>
            <div className='intlc-broadcast__body'>
                <CodeHighLighter codeText={code} language="json" />
            </div>
        </div>
    </div>

} 