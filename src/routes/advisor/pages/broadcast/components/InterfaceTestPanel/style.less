.intlc-broadcast-box {
    display: flex;
    justify-content: center;

    .intlc-broadcast__interface {
        text-align: left;
        white-space: normal;
        vertical-align: middle;
        min-width: 420px;
        width: 630px;
        margin: 30px;
        background-color: #fff;
        box-shadow: 2px 4px 4px 0 rgb(54 58 80 / 32%);
        border-radius: 0;
        padding: 25px;
        box-sizing: border-box;
    
        .intlc-broadcast__header {
            font-size: 14px;
            min-height: 22px;
            color: #000;
            position: relative;
            margin-bottom: 10px;
        }
    
        .intlc-broadcast__body{
            color: rgba(0, 0, 0, 0.9);
            word-break: break-word;
            word-wrap: break-word;

            .intlc-broadcast-filter {
                position: relative;
                padding-left: 13px;
            
                .intlc-broadcast__item {
                    display: flex;
                    margin-bottom: 10px;
                
                    .intlc-broadcast-number {
                      width: 20px;
                      height: 30px;
                      line-height: 30px;
                      vertical-align: top;
                      font-size: 12px;
                    }
            
                    .intlc-broadcast-group {
                        background-color: #f2f2f2;
                        padding: 10px;
                    }
                }
                
                &::before {
                    content: "";
                    display: block;
                    width: 3px;
                    height: 100%;
                    background-color: #ddd;
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: 0;
                }
            }
    
            .intlc-broadcast__operate {
              display: flex;
              justify-content: center;
            }
          }
    }
}

