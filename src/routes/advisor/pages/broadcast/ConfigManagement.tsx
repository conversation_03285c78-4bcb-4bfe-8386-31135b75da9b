import React, { useMemo, useState, useEffect } from 'react';
import { Layout, Button, Segment, Bubble } from '@tencent/tea-component';
import './style.less';
import { BroadStrategy } from './BroadStrategy';
import BroadcastConfigList from './components/broadcast-config-list';
// import GuardBroadcastConfigList from './components/guard-broadcast-config-list';
import { SpecialStaffing } from './SpecialStaffing';
import { useCheckIfAuthed } from './hooks/checkIfAuthed';
const { Body, Content } = Layout;

enum TabType {
	BroadStrategy = '1',
	BroadcastConfigList = '2',
	GuardBroadcastConfigList = '3',
	SpecialStaffing = '4',
}

export function ConfigManagement() {
	const { search } = location;
	const isAbroadSite = localStorage.getItem('site') === 'sinapore';
	const hasTabType = search.indexOf('tab') !== -1;
	const searchVal = hasTabType ? search.split('=')[1] : '2';
	const [value, setValue] = useState(+searchVal);
	const [tabOption, setTabOption] = useState([
		// { text: '播报策略', value: TabType.BroadStrategy },
		{ text: '监控指标播报策略', value: TabType.BroadcastConfigList },
		// { text: '护航监控配置', value: TabType.GuardBroadcastConfigList },
		{ text: '默认专项人员', value: TabType.SpecialStaffing },
	]);
	const { isAuth } = useCheckIfAuthed({
		pageStatus: 'broad-strategy-editor',
		key: '',
		value: '',
	});
	const tabOptionSite = useMemo(() => {
		const ret = tabOption;
		if (isAbroadSite) {
			return ret.filter(item => (
				item.value === TabType.BroadStrategy
				|| item.value === TabType.SpecialStaffing
			));
		}
		return ret;
	}, [tabOption]);

	useEffect(() => {
		if (isAuth) {
			setTabOption((last) => {
				last.splice(1, 0, { text: '播报策略', value: TabType.BroadStrategy });
				return [...last];
			});
		}
	}, [isAuth]);
	return (
		<Body>
			<Content>
				<Content.Header title="护航配置管理"></Content.Header>
				<Content.Body>
					<div className='configTabWrap'>
						<Segment
							value={value.toString()}
							onChange={value => setValue(parseInt(value, 10))}
							options={tabOptionSite}
						/>
						<Bubble content="查看护航巡检项、应急预案、播报项等配置" placement='top-end'>
							<Button
								type="link"
								onClick={() => {
									window.open('https://xiaoma.woa.com/#/review/8119/379276');
								}}>
                更多配置
							</Button>
						</Bubble>
					</div>
					{value === 1 && <BroadStrategy />}
					{value === 2 && <BroadcastConfigList />}
					{/* {value === 3 && <GuardBroadcastConfigList />} */}
					{value === 4 && <SpecialStaffing />}
				</Content.Body>
			</Content>
		</Body>
	);
}
