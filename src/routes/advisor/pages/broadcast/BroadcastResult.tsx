import React, { useState, useEffect, useMemo } from 'react';
import { Table, message, Button, Row, Card, Layout, Col, Select, SelectMultiple, Text, StatusTip, Input, Modal, Icon } from '@tencent/tea-component';
import { CodeHighLighter } from './components/CodeHighLighter';
import { useCheckIfAuthed, NoPermission, PermissionLoading } from './hooks/checkIfAuthed';
import { map, find, isEmpty } from 'lodash';
import { DescribeGuardSheet } from '@src/api/advisor/guard';
import { DescribeBroadcastResults } from '@src/api/advisor/broadcast';
import {
	resultListSelectConfig,
	subTypeOption,
	typeOption,
	actionOption,
	sourceOption,
	ACTION_STATUS,
	SOURCE_STATUS,
} from './conf/config';
import { useToggle } from './hooks/common';
import { getDescribeProductList } from '@src/api/advisor/faultNotification';
import './style.less';

const { Body, Content } = Layout;
const { pageable } = Table.addons;
interface BoardcastResultFilter {
	product: string[],
	cnName: string,
	subType: string,
	type: string,
	actionStatus: string
	sourceStatus: string
}

const defaultFilterOption = {
	product: [],
	cnName: '',
	subType: 'all',
	type: 'all',
	actionStatus: 'all',
	sourceStatus: 'all',
};
export function BroadcastResult(match) {
	const [filter, setFilter] = useState<BoardcastResultFilter>(defaultFilterOption);
	const [productOption, setProductOption] = useState([]);

	const [codeDialogVisible, openCodeDialog, closeCodeDialog] = useToggle(false);

	const [resultDialogVisible, openResultDialog, closeResultDialog] = useToggle(false);

	const [guardName, setGuardName] = useState('');

	const [broadStrategyResults, setBroadStrategyResults] = useState([]);

	const [codeJson, setCodeJson] = useState('');
	const [resultContent, setResultContent] = useState('');

	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	const [isLoading, startLoad, endLoad] = useToggle(false);

	// 重置状态和监听重置状态进行查询
	const [reStart, setRestart] = useState(true);

	const { isAuth, loading } = useCheckIfAuthed({
		pageStatus: 'broadcast-result',
		key: 'GuardId',
		value: match.match.params.guardid,
	});

	// 监听是否被重置，并在重置后删除事件监听器
	useEffect(() => {
		getBroadcastResults();
	}, [reStart]);

	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getDescribeProductList({
				AppId: 1253985742,
				OnlyData: true,
				ShowError: true,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const productNameList = [];
			// eslint-disable-next-line no-restricted-syntax
			for (const key in res.ProductDict) {
				productNameList.push({
					text: res.ProductDict[key],
					value: key,
				});
			}

			// setProductOption([{ text: '全部', value: 'all' }].concat(productNameList));
			setProductOption(productNameList);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 查询指定id的guard
	const getGuardSheet = async () => {
		try {
			const filters = [
				{ Name: 'guard_id', Values: [match.match.params.guardid] },
			];
			const res = await DescribeGuardSheet({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset: 0,
				Limit: 10,
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const item = res.Guard[0] || {};
			setGuardName(item?.GuardName);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getBroadcastResults = async () => {
		startLoad();
		try {
			const { product, subType, type, cnName, actionStatus, sourceStatus } = filter;
			const filters = [
				{ Name: 'broadcast_id', Values: [match.match.params.broadcastid] },
				{ Name: 'product', Values: product },
				{ Name: 'cnname', Values: [cnName] },
				{ Name: 'type', Values: type === 'all' ? [] : [type] },
				{ Name: 'sub_type', Values: subType === 'all' ? [] : [subType] },
				{ Name: 'action_status', Values: actionStatus === 'all' ? [] : [actionStatus] },
				{ Name: 'is_normal', Values: sourceStatus === 'all' ? [] : [sourceStatus] },
			];

			const params = {
				AppId: 1253985742,
				Filters: filters,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
			};
			const res = await DescribeBroadcastResults(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				const broadcastResults = map(res.BroadcastResults, (item, index) => ({ ...item, Id: index }));
				setBroadStrategyResults(broadcastResults);
				setTotalPage(res.TotalCount);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};
	const selectInfoListOption = useMemo(() => map(resultListSelectConfig, item => (item.value === 'product' ? { ...item, optionsConfig: productOption } : item)), [productOption]);

	useEffect(() => {
		getProductsGroupsInfo();
		getGuardSheet();
	}, []);

	useEffect(() => {
		getBroadcastResults();
	}, [pageSize, pageNumber]);
	// 搜索条件改变时重置页数
	useEffect(() => {
		setPageNumber(1);
	}, [filter]);

	const handleViewBtnClick = (item) => {
		const temp = JSON.parse(item.Detail);
		const code = item?.Detail ? JSON.stringify(temp, null, 4) : '';
		setCodeJson(code);
		openCodeDialog();
	};

	const handleViewResult = (item) => {
		setResultContent(item.Result);
		openResultDialog();
	};
	const columns = [
		{
			key: 'AppId',
			align: 'left',
			header: 'APPID',
		},
		{
			key: 'Product',
			align: 'left',
			header: '云产品',
			render: (item) => {
				const nameTxt = find(productOption, product => product.value === item.Product);
				return (<>{isEmpty(nameTxt) ? '-' : nameTxt.text}</>);
			},
		},
		{
			key: 'CNName',
			align: 'left',
			header: '播报项',
		},
		{
			key: 'SubType',
			align: 'left',
			header: '类型',
			render: (item) => {
				const subTypeTxt = find(subTypeOption, subType => subType.value === item.SubType) || {};
				return (<>{isEmpty(subTypeTxt) ? '-' : subTypeTxt.text}</>);
			},
		},
		{
			key: 'Type',
			align: 'left',
			header: '维度',
			render: (item) => {
				const typeTxt = find(typeOption, type => type.value === item.Type) || {};
				return (<>{isEmpty(typeTxt) ? '-' : typeTxt.text}</>);
			},
		},
		{
			key: 'ActionStatus',
			align: 'left',
			header: '接口执行情况',
			render: (item) => {
				const status = find(actionOption, status => status.value === `${item.ActionStatus}`) || {};
				const theme = +status.value === ACTION_STATUS.SUCCESS ? 'success' : +status.value === ACTION_STATUS.ERROR ? 'danger' : 'warning';
				return (<div style={{ display: 'flex', alignItems: 'center' }}>
					<Icon type={theme === 'danger' ? 'error' : theme} />
					<span style={{ marginLeft: '3px' }}>
						<Text theme={theme}>{status.text}</Text>
					</span>
				</div>);
			},
		},
		{
			key: 'IsNormal',
			header: '资源状态',
			align: 'left',
			render: (item) => {
				const status = find(sourceOption, status => status.value === `${item.IsNormal}`) || {};
				const theme = +status.value === SOURCE_STATUS.SUCCESS ? 'success' : +status.value === SOURCE_STATUS.ERROR ? 'danger' : 'warning';
				return (<div style={{ display: 'flex', alignItems: 'center' }}>
					<Icon type={theme === 'danger' ? 'error' : theme} />
					<span style={{ marginLeft: '3px' }}>
						<Text theme={theme}>{status.text}</Text>
					</span>
				</div>);
			},
		},
		{
			key: 'Result',
			header: '结论',
			align: 'left',
			render: item => <Button type="link" onClick={() => handleViewResult(item)}>查看结论</Button>,
		},
		{
			key: 'SendTime',
			align: 'left',
			width: '15%',
			header: '播报时间',
		},
		{
			key: 'Detail',
			header: '具体值',
			align: 'left',
			render: item => <Button type="link" onClick={() => handleViewBtnClick(item)}>查看</Button>,
		},
	];
	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};
	const tableOptionProps = {
		style: { marginTop: 15 },
		recordKey: 'Id',
		verticalTopL: true,
		columns,
		records: broadStrategyResults,
		topTip: isLoading ? <StatusTip status="loading"></StatusTip> : broadStrategyResults.length === 0 && <StatusTip status="empty" />,
		addons: [
			pageable({
				pageIndex: pageNumber,
				recordCount: totalPage,
				onPagingChange: handlePageChange,
			}),
		],
	};

	const cascadeSourceOption = useMemo(() => {
		if (filter.actionStatus !== 'all') {
			if (filter.actionStatus === `${ACTION_STATUS.SUCCESS}`) {
				setFilter({ ...filter, sourceStatus: `${SOURCE_STATUS.SUCCESS}` });
				return sourceOption.filter(item => item.value !== 'all');
			}

			setFilter({ ...filter, sourceStatus: `${SOURCE_STATUS.EMPTY}` });
			return sourceOption.filter(item => item.value === `${SOURCE_STATUS.EMPTY}`);
		}
		return sourceOption;
	}, [filter.actionStatus, sourceOption]);

	if (loading) {
		return <PermissionLoading />;
	}
	if (isAuth === false) {
		return <NoPermission />;
	}
	return (
		<Body>
			<Content>
				<Content.Header title={`播报结果：${match.match.params.guardid} ${guardName ? ` | ${guardName}` : ''}`}></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body>
							<div>
								<Row gap={20}>
									<>
										{selectInfoListOption.map(item => (
											<Col key={item.label}>
												<Row verticalAlign={'middle'}>
													<Col span={6}>
														<Text theme="label" verticalAlign="middle">{item.label}</Text>
													</Col>
													<Col span={18}>
														{item.value === 'product' ? <SelectMultiple
															listHeight={400}
															appearance="button"
															searchable
															size="full"
															boxClassName='productListBox'
															options={item.optionsConfig}
															value={filter[item.value]}
															allOption={{ value: 'all', text: '全部' }}
															onChange={(value) => {
																setFilter({ ...filter, product: value });
															}}
															placeholder="请选择"
														/> : <Select
															appearance="button"
															matchButtonWidth
															size="full"
															options={item.optionsConfig}
															value={filter[item.value]}
															onChange={value => setFilter({ ...filter, [item.value]: value })}
															placeholder="请选择"
														/>}
													</Col>
												</Row>
											</Col>
										))}
										< Col>
											<Row verticalAlign={'middle'}>
												<Col span={6}>
													<Text theme="label" verticalAlign="middle">播报项</Text>
												</Col>
												<Col span={18}>
													<Input value={filter.cnName} size='full'
														   onChange={value => setFilter({ ...filter, cnName: value })}/>
												</Col>
											</Row>
										</Col>
									</>
								</Row>
								<Row gap={20} verticalAlign={'middle'}>
									<Col>
										<Row verticalAlign={'middle'}>
											<Col span={6}>
												<Text theme="label" verticalAlign="middle">接口执行情况</Text>
											</Col>
											<Col span={18}>
												<Select
													appearance="button"
													matchButtonWidth
													size="full"
													options={actionOption}
													value={filter.actionStatus}
													onChange={value => setFilter({ ...filter, actionStatus: value })}
												/>
											</Col>
										</Row>
									</Col>
									<Col>
										<Row verticalAlign={'middle'}>
											<Col span={6}>
												<Text theme="label" verticalAlign="middle">资源状态</Text>
											</Col>
											<Col span={18}>
												<Select
													appearance="button"
													matchButtonWidth
													size="full"
													options={cascadeSourceOption}
													value={filter.sourceStatus}
													onChange={value => setFilter({ ...filter, sourceStatus: value })}
												/>
											</Col>
										</Row>
									</Col>
									<Col></Col>
									<Col></Col>
								</Row>
							</div>
							<div style={{ margin: 10, textAlign: 'center' }}>
								<Button type="primary" onClick={getBroadcastResults}>查询</Button>
								<Button style={{ marginLeft: 15 }}
									onClick={() => {
										setFilter(defaultFilterOption);
										setRestart(a => !a);
									}}>重置</Button>
							</div>
						</Card.Body>
					</Card>
					<Card>
						<Card.Body>
							{/* @ts-ignore */}
							<Table {...tableOptionProps} />
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
			<Modal
				maskClosable
				size="xl"
				visible={codeDialogVisible}
				caption="API返回详情"
				onClose={() => closeCodeDialog()}
			>
				<Modal.Body>
					<CodeHighLighter codeText={codeJson} language="json" />
				</Modal.Body>
			</Modal>
			<Modal
				maskClosable
				size="xl"
				visible={resultDialogVisible}
				caption="结论"
				onClose={() => closeResultDialog()}
			>
				<Modal.Body>
					<CodeHighLighter codeText={resultContent} language="json" />
				</Modal.Body>
			</Modal>
		</Body >
	);
}
