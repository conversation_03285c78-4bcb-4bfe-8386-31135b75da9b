import React, { useState, useEffect, useMemo, useRef } from 'react';
import { message, But<PERSON>, Card, Layout, Stepper, StatusTip, Icon } from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import './components/BroadcastBasePanel/style.less';
import { DescribeGuardSheet } from '@src/api/advisor/guard';
import { map, isEmpty, merge, find } from 'lodash';
import { useToggle } from './hooks/common';
import { BroadcastBasePanel, StrategyListPanel, ResourceSupplePanel, SubmitPanel } from './components';

import { getBroadcastSheet, DescribeCombinedBroadcastConfigs, ModifyBroadcastOnline } from '@src/api/advisor/broadcast';
import { useCheckIfAuthed, NoPermission, PermissionLoading } from './hooks/checkIfAuthed';

import { GuardParams } from '@src/types/advisor/guard';
import { broadcastStatusOption, SOURCE_STATUS, STATUS } from './conf/config';
import { IconTsa } from '@src/components/IconTsa';
import './style.less';

const { Body, Content } = Layout;

const steps = [
	{ id: 'baseInfo', label: '基础信息' },
	{ id: 'strategyList', label: '选择播报策略' },
	{ id: 'resourceSupple', label: '选择播报资源' },
	{ id: 'submit', label: '启用' },
];

const defaultInfoChangeStatus = {
	ruleInfoStatus: false,
	broadStrategyStatus: false,
	pendingStatus: false,
};

export function BroadcastEditor(match) {
	const history = useHistory();
	const { isAuth, loading } = useCheckIfAuthed({
		pageStatus: 'broadcast-editor',
		key: 'BroadcastId',
		value: match.match.params.broadcastid,
	});
	// 护航单信息
	const [currentGuardInfo, setCurrentGuardInfo] = useState<GuardParams>({});
	// 护航单ID
	const [guardId, setGuardId] = useState<number>(0);

	// 播报订阅信息
	const [broadcastInfo, setBroadcastInfo] = useState<any>({});
	// 组合播报信息
	const [combinedBroadcastConfig, setCombinedBroadcastConfig] = useState<any>({});

	// 步骤信息改变状态
	const [changeStatus, setChangeStatus] = useState(defaultInfoChangeStatus);

	// 启用状态
	const [switchValue, setSwitchValue] = useState(false);
	const [visible, openDialog, closeDialog] = useToggle(false);

	const [stepDirection, setStepDirection] = useState('NEXT');

	// 控制步骤
	const [current, setCurrent] = useState('baseInfo');
	const currentIndex = current ? steps.findIndex(x => x.id === current) : -1;
	const next = current && steps[currentIndex + 1];
	const prev = current ? steps[currentIndex - 1] : steps[steps.length - 1];

	const isBroadcastEditabale = !!+match.match.params.type;
	const operator = localStorage.getItem('engName');

	// 调用子组件方法
	const saveRef = useRef(null);
	// 点击下一步时进入 loading 等待保存完成
	const [loadingNext, setLoadingNext] = useState(false);
	const [loadingLast, setLoadingLast] = useState(false);
	const [disabledNext, setDisabledNext] = useState(false);
	const [disabledLast, setDisabledLast] = useState(false);

	// 获取护航信息
	const getGuardSheet = async () => {
		try {
			const filters = [
				{ Name: 'guard_id', Values: [`${guardId}`] },
			];
			const res = await DescribeGuardSheet({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset: 0,
				Limit: 10,
				AppId: 1253985742, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const item = res.Guard[0] || {};
			setCurrentGuardInfo(item);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	// 获取播报订阅信息
	const getBroadcastSheetInfo = async () => {
		try {
			const res = await getBroadcastSheet({ AppId: 1253985742, BroadcastId: +match.match.params.broadcastid });
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			setGuardId(res.BroadcastSheet.GuardId);
			setBroadcastInfo(res.BroadcastSheet);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 获取组合播报内容
	const getCombinedData = async () => {
		try {
			const res: any = await DescribeCombinedBroadcastConfigs({
				AppId: 1253985742,
				BroadcastId: +match.match.params.broadcastid,
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setCombinedBroadcastConfig(res || {});
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		getBroadcastSheetInfo();
	}, []);

	useEffect(() => {
		if (!guardId) return;
		getGuardSheet();
	}, [guardId]);

	const isShowBaseInfoPanel = useMemo(() => !isEmpty(broadcastInfo), [broadcastInfo]);

	// 启用开关是否可用
	const statusDisable = useMemo(() => {
		const { Products, BroadcastConfig, Enable } = broadcastInfo;
		if (broadcastInfo?.Online === SOURCE_STATUS.SUCCESS) {
			return true;
		}
		// 如果其中有一个打开，就得判断产品是否选择实例
		if (Enable || combinedBroadcastConfig.Enable) {
			let result = true;
			// 如果打开常规播报，就要校验有没有选择策略
			if (Enable) {
				result = result && !isEmpty(BroadcastConfig);
			}
			if (!isEmpty(Products)) {
				const pickFinished = Products
					.filter(item => item.Product !== 'stream')
					.every(item => item.ResourceIds.length > 0);
				result = result && pickFinished;
			}
			return result;
		}
		// 如果两个都未打开，不允许启用
		return false;
	}, [broadcastInfo, combinedBroadcastConfig]);

	// 待补充信息预处理
	const resourcePending = useMemo(() => map(broadcastInfo?.Products, ({ Product, ResourceIds, Name, IsPending }) => ({
		Product,
		Name,
		IsPending,
		ResourceIds: map(ResourceIds, item => ({ InstanceId: item })),
	})), [broadcastInfo.Products]);

	// 点击下一步时自动保存
	const handleStepBtnClick = async (type) => {
		if (current === 'submit') {
			setCurrent(type === 'NEXT' ? next.id : prev.id);
			return;
		}
		setStepDirection(type);
		if (type === 'NEXT') {
			setLoadingNext(true);
			setDisabledLast(true);
		} else {
			setLoadingLast(true);
			setDisabledNext(true);
		}
		// 子组件保存会返回结果 true / false
		const res = await saveRef.current.save();
		if (type === 'NEXT') {
			setLoadingNext(false);
			setDisabledLast(false);
		} else {
			setLoadingLast(false);
			setDisabledNext(false);
		}
		if (res) {
			setCurrent(type === 'NEXT' ? next.id : prev.id);
		}
	};

	// 记录信息是否改变
	const handleDetailChange = (status, type) => {
		const temp = merge(changeStatus, { [type]: status });
		setChangeStatus(temp);
	};

	// 初始化启用状态
	useEffect(() => {
		setSwitchValue(broadcastInfo.Online === STATUS.SUCCESS);
	}, [broadcastInfo.Online]);

	// 保存状态
	const handleStatusInfoSave = async () => {
		try {
			const res = await ModifyBroadcastOnline({
				AppId: 1253985742,
				BroadcastId: broadcastInfo.BroadcastId,
				Online: switchValue ? 1 : 0,
				Updater: operator,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				history.push('/advisor/broadcast');
				return;
			}
			message.success({ content: '提交保存成功' });
			history.push('/advisor/broadcast');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const TitleContent = () => {
		const status = find(broadcastStatusOption, status => status.value === `${broadcastInfo.Online}`) || {};
		return (
			<div style={{ display: 'flex', alignItems: 'center' }}>
				<Button
					type='text'
					className='nav-item-home-btn'
					onClick={() => history.push('/advisor/broadcast')}
				>
					<div className="nav-item-svg__box nav-home-btn">
						<IconTsa type='icon-home' className='home-icon' />
						<span className='nav-item-ht'>回到列表</span>
					</div>
				</Button>
				<span style={{ marginRight: '60px' }}>编辑播报订阅: {match.match.params.broadcastid} {currentGuardInfo?.GuardName ? ` | ${currentGuardInfo.GuardName}` : ''}</span>
				{+status.value === STATUS.SUCCESS ? <Icon type="success" /> : +status.value === STATUS.FAIL ? <Icon type="not" /> : <Icon type="warning" />}
				<span style={{ marginLeft: '3px', fontSize: '12px', fontWeight: 'normal' }}>{status.text}</span>
			</div>
		);
	};

	if (loading) {
		return <PermissionLoading />;
	}
	if (isAuth === false) {
		return <NoPermission />;
	}
	return (
		<Body>
			<Content>
				<Content.Header
					title={<TitleContent/>}>
				</Content.Header>
				<Content.Body>
					<Card>
						<Card.Body>
							<Stepper steps={steps} current={current} />
							<section>
								<div style={{ position: 'relative' }}>
									{/* {!isBroadcastEditabale && <div className='content-mask'></div>} */}
									{(() => {
										switch (steps[currentIndex]?.id) {
											case 'baseInfo':
												return <>
													{
														isShowBaseInfoPanel
															? <BroadcastBasePanel
																ref={saveRef}
																guardInfo={currentGuardInfo}
																broadcastInfo={broadcastInfo}
																editable={isBroadcastEditabale}
																onInfoChange={status => handleDetailChange(!status, 'ruleInfoStatus')}
																reload={getBroadcastSheetInfo}
															/>
															: <StatusTip status="loading" />
													}
												</>;
											case 'strategyList':
												return <StrategyListPanel
													ref={saveRef}
													products={currentGuardInfo.ProductDesc}
													broadcastId={broadcastInfo.BroadcastId}
													editable={isBroadcastEditabale}
													selectedStrategy={broadcastInfo.BroadcastConfig}
													onInfoChange={status => handleDetailChange(!status, 'broadStrategyStatus')}
													reload={getBroadcastSheetInfo}
													reloadC={getCombinedData}
													GtsChatid={broadcastInfo.GtsChatid}
													Enable={broadcastInfo.Enable}
												/>;
											case 'resourceSupple':
												return <ResourceSupplePanel
													ref={saveRef}
													guardId={guardId}
													broadcastId={broadcastInfo.BroadcastId}
													resourcePending={resourcePending}
													editable={isBroadcastEditabale}
													onInfoChange={status => handleDetailChange(!status, 'pendingStatus')}
													reload={getBroadcastSheetInfo}
													online={broadcastInfo.Online}
												/>;
											case 'submit':
												return <SubmitPanel
													statusDisable={statusDisable}
													strategyStatus={broadcastInfo.Online}
													onSwitchChange={value => setSwitchValue(value)}
												/>;
											default: return <></>;
										}
									})()}
								</div>
								<div style={{ display: 'flex', justifyContent: 'center' }}>
									<Button disabled={!prev || disabledLast} loading={loadingLast} onClick={() => handleStepBtnClick('PREV')}>上一步</Button>
									{
										next
                                        && <Button
                                        	type="primary"
                                        	disabled={!next || !current || disabledNext}
                                        	onClick={() => handleStepBtnClick('NEXT')}
                                        	style={{ marginLeft: 10 }}
                                        	loading={loadingNext}
                                        >
                                            下一步
                                        </Button>
									}
									{
										isBroadcastEditabale && current === 'submit'
                                        && <>
                                        	<Button
                                        		type="primary"
                                        		onClick={handleStatusInfoSave}
                                        		style={{ marginLeft: 6 }}
                                        	>
                                                提交
                                        	</Button>
                                        </>
									}
								</div>
							</section>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
			{/* <Modal visible={visible} caption="保存提醒" onClose={closeDialog}> */}
			{/*     <Modal.Body>当前更改的信息还没有保存，确认跳转到其他步骤吗</Modal.Body> */}
			{/*     <Modal.Footer> */}
			{/*         <Button type="primary" onClick={() => { setCurrent(stepDirection === 'NEXT' ? next.id : prev.id); closeDialog() }}>确定</Button> */}
			{/*         <Button type="weak" onClick={closeDialog}>取消</Button> */}
			{/*     </Modal.Footer> */}
			{/* </Modal> */}
		</Body >
	);
}
