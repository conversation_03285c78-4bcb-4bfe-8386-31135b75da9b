import React, { useState, useEffect, useMemo } from 'react';
import {
	Table,
	message,
	Button,
	Row,
	Card,
	Col,
	Select,
	Text,
	StatusTip,
	Input,
	Tag,
	Icon,
	Alert,
	List,
	ExternalLink,
	Justify,
} from '@tencent/tea-component';
import { useHistory, useAegisLog } from '@tea/app';
import { reportVisitPage } from '@src/utils/report';
import { map, find, isEmpty } from 'lodash';
import { useToggle } from './hooks/common';

import { DescribeBroadcastStrategys } from '@src/api/advisor/broadcast';

import { ProductOption } from '@src/types/cloudEscort/broadcast/broadStrategy/page';
import { Strategy } from '@src/types/cloudEscort/broadcast/broadStrategy/api';
import { selectInfoListConfig, subTypeOption, statusOption, typeOption } from './conf/config';
import { getDescribeProductList } from '@src/api/advisor/faultNotification';

const { pageable } = Table.addons;

interface BoardcastListFilter {
	product: string,
	subType: string,
	type: string,
	online: string,
	cnName: string,
}

const defaultFilterOption = {
	product: 'all',
	subType: 'all',
	type: 'all',
	online: 'all',
	cnName: '',
};

enum broadcastTabType {
	tamBroadcast = 1,
	configBroadcast = 2,
}

export function BroadStrategy() {
	const history = useHistory();
	const aegis = useAegisLog();

	const ctxUser = localStorage.getItem('engName');
	// 过滤项
	const [filter, setFilter] = useState<BoardcastListFilter>(defaultFilterOption);
	// 产品下拉选项
	const [productOption, setProductOption] = useState<Array<ProductOption>>([]);
	// 播报策略列表
	const [broadStrategyList, setBroadStrategyList] = useState<Array<Strategy>>([]);

	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	// 加载状态
	const [isLoading, startLoad, endLoad] = useToggle(false);

	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getDescribeProductList({
				AppId: 1253985742,
				OnlyData: true,
				ShowError: true,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const productNameList = [];
			for (const key in res.ProductDict) {
				productNameList.push({
					text: res.ProductDict[key],
					value: key,
				});
			}
			setProductOption([{ text: '全部', value: 'all' }].concat(productNameList));
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	const getBroadStrategyList = async () => {
		startLoad();
		try {
			const { product, subType, type, online, cnName } = filter;
			const filters = [
				{ Name: 'product', Values: product === 'all' ? [] : [product] },
				{ Name: 'cnname', Values: [cnName] },
				{ Name: 'type', Values: type === 'all' ? [] : [type] },
				{ Name: 'sub_type', Values: subType === 'all' ? [] : [subType] },
				{ Name: 'online', Values: online === 'all' ? [] : [online] },
			];
			const params = {
				AppId: 1253985742,
				Filters: filters,
				StrategySource: broadcastTabType.tamBroadcast,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
			};
			const res = await DescribeBroadcastStrategys(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setBroadStrategyList(res.BroadcastStrategy);
				setTotalPage(res.TotalCount);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};
	const selectInfoListOption = useMemo(() => map(selectInfoListConfig, item => (item.value === 'product' ? { ...item, optionsConfig: productOption } : item)), [productOption]);

	useEffect(() => {
		getProductsGroupsInfo();
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: '护航配置管理/播报策略',
			ext3: ctxUser,
		});
		reportVisitPage({
			isaReportMeunName: '护航配置管理/播报策略',
		});
	}, []);

	useEffect(() => {
		setPageNumber(1);
	}, [filter]);

	useEffect(() => {
		getBroadStrategyList();
	}, [pageSize, pageNumber]);


	const columns = [
		{
			key: 'StrategyId',
			align: 'left',
			header: '播报项ID',
		},
		{
			key: 'Product',
			align: 'left',
			header: '云产品',
			render: (item) => {
				const nameTxt = find(productOption, product => product.value === item.Product);
				return (<>{isEmpty(nameTxt) ? '-' : nameTxt.text}</>);
			},
		},
		{
			key: 'CNName',
			align: 'left',
			header: '播报项',
		},
		{
			key: 'ENName',
			align: 'left',
			header: 'PolicyName',
		},
		{
			key: 'SubType',
			align: 'left',
			header: '类型',
			render: (item) => {
				const subTypeTxt = find(subTypeOption, subType => subType.value === item.SubType) || {};
				return (<>{isEmpty(subTypeTxt) ? '-' : subTypeTxt.text}</>);
			},
		},
		{
			key: 'Type',
			align: 'left',
			header: '维度',
			render: (item) => {
				const typeTxt = find(typeOption, type => type.value === item.Type) || {};
				return (<>{isEmpty(typeTxt) ? '-' : typeTxt.text}</>);
			},
		},
		{
			key: 'Expert',
			align: 'left',
			header: '专项接口人',
		},
		{
			key: 'Creater',
			header: '创建人|更新人',
			align: 'left',
			render: (item) => {
				const { Creater, Updater } = item;
				return (<>
					{Creater && <><Tag theme="primary">{Creater}</Tag><br /></>}
					{Updater && <Tag theme="primary">{Updater}</Tag>}
				</>);
			},
		},
		{
			key: 'CreateTime',
			header: '创建时间|更新时间',
			align: 'left',
			width: '16%',
			render: (item) => {
				const { CreateTime, UpdateTime } = item;
				return (<>
					{CreateTime && <><Tag >{CreateTime}</Tag><br /></>}
					{UpdateTime && <Tag >{UpdateTime}</Tag>}
				</>);
			},
		},
		{
			key: 'Online',
			align: 'left',
			header: '状态',
			render: (item) => {
				const status = find(statusOption, status => status.value === `${item.Online}`) || {};
				return (<div style={{ display: 'flex', alignItems: 'center' }}>
					{status.value === '1' ? <Icon type="success" /> : <Icon type="not" />}
					<span style={{ marginLeft: '3px' }}>{status.text}</span>
				</div>);
			},
		},
		{
			key: '',
			align: 'left',
			header: '操作',
			render: item => (<Button type="link" onClick={() => {
				aegis.reportEvent({
					name: 'Click',
					ext1: 'strategy-edit-btn',
					ext2: ctxUser,
					ext3: '护航配置管理/播报策略',
				});
				history.push({ pathname: `/advisor/broad-strategy-editor/${item.StrategyId}` });
			}}>编辑</Button>),
		},

	];

	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	const tableOptionProps = {
		style: { marginTop: 15 },
		recordKey: 'StrategyId',
		verticalTopL: true,
		columns,
		records: broadStrategyList,
		topTip: isLoading ? <StatusTip status="loading"></StatusTip> : broadStrategyList.length === 0 && <StatusTip status="empty" />,
		addons: [
			pageable({
				pageIndex: pageNumber,
				recordCount: totalPage,
				onPagingChange: handlePageChange,
			}),
		],
	};

	const handleCreateBtnClick = () => {
		aegis.reportEvent({
			name: 'Click',
			ext1: 'strategy-create-btn',
			ext2: ctxUser,
			ext3: '护航配置管理/播报策略',
		});
		history.push({
			pathname: '/advisor/broad-strategy-editor/0',
		});
	};

	return (
		<>
			<Alert style={{ marginTop: 20 }}>
				<h4>特别说明:</h4>
				<List type="bullet">
					<List.Item>请参考
						<ExternalLink href="https://iwiki.woa.com/pages/viewpage.action?pageId=4006736976">
                            《护航播报接入说明》
						</ExternalLink>
					</List.Item>
					<List.Item>
                        策略创建成功后，云产品、播报项、类型和维度将不允许修改
					</List.Item>
				</List>
			</Alert>
			<Card>
				<Card.Body>
					<Row gap={15}>
						<>
							{
								selectInfoListOption.map(item => (
									<Col key={item.label}>
										<Row verticalAlign={'middle'}>
											<Col span={6} >
												<Text theme="label" verticalAlign="middle">{item.label}</Text>
											</Col>
											<Col span={18} >
												<Select
													appearance="button"
													matchButtonWidth
													searchable={item.value === 'product'}
													size="full"
													options={item.optionsConfig}
													value={filter[item.value]}
													onChange={value => setFilter({ ...filter, [item.value]: value })}
													placeholder="请选择"
												/>
											</Col>
										</Row>
									</Col>
								))
							}
						</>
					</Row>
					<Row verticalAlign={'middle'} gap={15}>
						<Col span={6}>
							<Row verticalAlign={'middle'}>
								<Col span={6}>
									<Text theme="label" verticalAlign="middle">播报项</Text>
								</Col>
								<Col span={18}>
									<Input size='full' value={filter.cnName} onChange={value => setFilter({ ...filter, cnName: value })} />
								</Col>
							</Row>
						</Col>
					</Row>
					<div style={{ marginTop: 10, textAlign: 'center' }}>
						<Button type="primary" onClick={() => {
							aegis.reportEvent({
								name: 'Click',
								ext1: 'strategy-view-btn',
								ext2: ctxUser,
								ext3: '护航配置管理/播报策略',
							});
							getBroadStrategyList();
						}}>查询</Button>
						<Button style={{ marginLeft: 15 }} onClick={() => setFilter(defaultFilterOption)}>重置</Button>
					</div>
				</Card.Body>
			</Card>
			<Card>
				<Card.Body>
					<Justify left={<Button type="primary" onClick={handleCreateBtnClick}>新建</Button>} />
					{/* @ts-ignore */}
					<Table {...tableOptionProps} />
				</Card.Body>
			</Card>
		</>
	);
}
