
export const All = 'all';

export const statusOption = [
	{
		text: '全部',
		value: All,
	},
	{
		text: '已停用',
		value: '0',
	},
	{
		text: '生效中',
		value: '1',
	},
];

export const subTypeOption = [
	{
		text: '全部',
		value: 'all',
	},
	{
		text: '事件告警类型',
		value: 'event',
	},
	{
		text: '水位告警类型',
		value: 'usage',
	},
];

export const typeOption = [
	{
		text: '全部',
		value: 'all',
	},
	{
		text: '资源',
		value: 'resource',
	},
	{
		text: '护航资源总览',
		value: 'overview',
	},
	{
		text: '客户资源总览',
		value: 'appid',
	},
];

export const selectInfoListConfig = [
	{
		label: '云产品',
		value: 'product',
		optionsConfig: [
			{
				text: '全部',
				value: All,
			},
		],
	},
	{
		label: '类型',
		value: 'subType',
		optionsConfig: subTypeOption,
	},
	{
		label: '维度',
		value: 'type',
		optionsConfig: typeOption,
	},
	{
		label: '状态',
		value: 'online',
		optionsConfig: statusOption,
	},
];

export const GuardStatusOption = [
	{
		text: '全部',
		value: '2',
	},
	{
		text: '已停用',
		value: '0',
	},
	{
		text: '生效中',
		value: '1',
	},
];

export const GuardtFormInfoListConfig = [
	{
		label: '云产品',
		value: 'product',
		type: 'multiple',
		optionsConfig: [
			{
				text: '全部',
				value: All,
			},
		],
	},
	{
		label: '状态',
		value: 'status',
		optionsConfig: GuardStatusOption,
	},
	{
		label: '监控指标',
		value: 'metric',
		optionsConfig: [
			{
				text: '全部',
				value: All,
			},
		],
	},
];

export const broadcastListConfig = [
	{
		label: '播报ID',
		value: 'broadcastId',
	},
	{
		label: '护航ID',
		value: 'guardId',
	},
	{
		label: '护航名称',
		value: 'guardName',
	},
	{
		label: '客户ID',
		value: 'appId',
	},
];

export const STATUS = {
	SUCCESS: 1,
	FAIL: 0,
	PEEDDING: -1,
	TOADD: 2,
};

export const broadcastStatusOption = [
	{ text: '全部', value: 'all' },
	{ text: '已启用', value: `${STATUS.SUCCESS}` },
	{ text: '已停用', value: `${STATUS.FAIL}` },
	{ text: '待补充', value: `${STATUS.TOADD}` },
];

export const resultListSelectConfig = [
	{
		label: '云产品',
		value: 'product',
		optionsConfig: [
			{
				text: '全部',
				value: 'all',
			},
		],
	},
	{
		label: '类型',
		value: 'subType',
		optionsConfig: subTypeOption,
	},
	{
		label: '维度',
		value: 'type',
		optionsConfig: typeOption,
	},
];

export const ACTION_STATUS = {
	ERROR: 0,
	SUCCESS: 1,
	STOP: 2,
};

export const actionOption = [
	{ text: '全部', value: 'all' },
	{ text: '执行正常', value: `${ACTION_STATUS.SUCCESS}` },
	{ text: '执行异常', value: `${ACTION_STATUS.ERROR}` },
	{ text: '播报项已停用', value: `${ACTION_STATUS.STOP}` },
];

export const SOURCE_STATUS = {
	ERROR: 0,
	SUCCESS: 1,
	EMPTY: 2,
};

export const sourceOption = [
	{ text: '全部', value: 'all' },
	{ text: '正常', value: `${SOURCE_STATUS.SUCCESS}` },
	{ text: '异常', value: `${SOURCE_STATUS.ERROR}` },
	{ text: '空', value: `${SOURCE_STATUS.EMPTY}` },
];

export const broadcastBaseLabel = ['播报ID | 名称', '场景', '关联护航单', 'APPID | 客户名称'];
