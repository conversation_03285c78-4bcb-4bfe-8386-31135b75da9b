import React, { useState, useEffect, useCallback } from 'react';
import { Status } from '@tencent/tea-component';
import { CheckIfAuthed } from '@src/api/advisor/guard';
import { getUserInfo } from '@src/api/common';

/**
 * 自定义Hook，用于检查用户是否具有特定页面操作权限。
 *
 * @param key - 权限验证所需的键值，用于过滤条件
 * @param value - 权限验证所需的值，与key配合使用
 * @param operator - 可选的操作员标识，若未提供则从localStorage获取
 * @param pageStatus - 当前页面状态，用于权限判断
 * @returns 包含IsAuth（布尔值表示是否通过权限验证）和loading（表示验证是否正在进行）的对象
 */
const useCheckIfAuthed = ({
	key,
	value,
	operator,
	pageStatus,
}: {
	key?: string,
	value?: string,
	operator?: string,
	pageStatus: string,
}) => {
	const [loading, setLoading] = useState(undefined);
	const [isAuth, setIsAuth] = useState(undefined);

	/**
	 * 获取当前操作员标识，优先使用传入的operator参数
	 * 若不存在则从localStorage中获取engName作为备选
	 */
	const curOperator = operator || localStorage.getItem('engName');

	/**
	 * 异步检查用户权限的核心方法
	 * @param operator - 操作员标识（可选参数，默认使用curOperator）
	 * 该方法会调用CheckIfAuthed服务进行权限验证，并更新状态
	 */
	const checkAuth = useCallback(
		(operator = curOperator) => {
			CheckIfAuthed({
				Operator: operator,
				PageStatus: pageStatus,
				FilterName: {
					...(key ? { Key: key } : {}),
					...(value ? { Value: value } : {}),
				},
			}).then((res) => {
				if (!res.Error) {
					setIsAuth(res?.IsAuth);
				}
			})
				.finally(() => {
					setLoading(false);
				});
		},
		[],
	);

	/**
	 * 初始化副作用处理：
	 * 1. 设置加载状态为true
	 * 2. 当没有操作员标识时，先获取用户信息再验证权限
	 * 3. 当存在操作员标识时，直接执行权限验证
	 */
	useEffect(() => {
		setLoading(true);
		if (!curOperator) {
			getUserInfo().then((res) => {
				if (res.data.EngName) {
					checkAuth(res.data.EngName);
				}
			});
		} else {
			checkAuth();
		}
	}, []);

	return { isAuth, loading: isAuth === undefined || loading };
};
const PermissionLoading = () => <Status
	icon={'loading'}
	size={'l'}
	title={'加载中'}
/>;
const NoPermission = () => <Status
	icon={'no-permission'}
	size={'l'}
	title={'暂无权限'}
/>;
export {
	useCheckIfAuthed,
	PermissionLoading,
	NoPermission,
};
