import React, { useState, useEffect } from 'react';
import { useToggle } from './hooks/common';
import { Row, Card, Col, Text, SelectMultiple, Input, message, Button, Table, Bubble, Icon, StatusTip, Modal, Form, InputNumber } from '@tencent/tea-component';
const { pageable } = Table.addons;
import RTXPicker from '@tencent/qmfe-yoa-react-ui/es/RTXPicker/index';
import { getProductsGroups } from '@src/api/advisor/estimate';
import { DescribeGuardExpertConfig, UpdateGuardExpertConfig } from '@src/api/advisor/specialStaffing';
import { cloneDeep } from "lodash";
import { useAegisLog } from '@tea/app';
import { reportVisitPage } from '@src/utils/report';

export function SpecialStaffing() {
    const aegis = useAegisLog();
    // 当前登录人
    const engName = localStorage.getItem('engName');

    const [pageSize, setPageSize] = useState<number>(10);
    const [totalPage, setTotalPage] = useState<number>(0);
    const [pageNumber, setPageNumber] = useState<number>(1);

    const [visible, setVisible] = useState(false);

    //产品映射选项
    const [productDict, setProductDict] = useState<Array<any>>([])
    //产品下拉框选项
    const [productsOptions, setProductsOptions] = useState<Array<any>>([])
    // 云产品选择
    const [productList, setProductList] = useState<Array<string>>([])
    // 护航人员
    const [responser, setResponser] = useState<string>('')
    // 表格数据
    const [tableData, setTableData] = useState<Array<any>>([])
    // 当前行
    const [currentData, setCurrentData] = useState<any>({})
    // 加载状态
    const [isLoading] = useToggle(false);

    //获取云产品清单 
    const getProductsGroupsInfo = async () => {
        try {
            const res = await getProductsGroups({
                AppId: 1253985742, //接口必须传appid  为获取全量产品列表，因此传内部中心账号
                Env: 'all',
                TaskType: 'guardTaskType',
            })
            if (res.Error) {
                let msg = res.Error.Message
                message.error({ content: msg });
                return
            } else {
                let tmpProductsOptions = []
                for (var i in res.ProductDict) {
                    tmpProductsOptions.push({ value: i, text: res.ProductDict[i] })
                }
                setProductDict(res.ProductDict || [])
                setProductsOptions(tmpProductsOptions)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            message.error({ content: msg });
        }
    }

    // 获取表格数据
    const getTableData = async () => {
        try {
            const filters = [
                { Name: "product", Values: productList },
                { Name: "responsible_person", Values: responser ? [responser] : [] },
            ]
            let params = {
                AppId: 1253985742,
                Filters: filters,
                Offset: (pageNumber - 1) * pageSize,
                Limit: pageSize,
            }
            const res = await DescribeGuardExpertConfig(params)
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                setTableData(res.GuardExpertConfigSlice);
                setTotalPage(res.Count);
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    }

    // 修改默认专项人员
    const savePerson = async () => {
        const { ExpertInterfacePerson, ExpertInterfaceLeader, GuardInterfacePerson, Supporter } = currentData
        // 人员不能为空，除了默认关注人
        if (!(ExpertInterfacePerson && ExpertInterfaceLeader && GuardInterfacePerson && Supporter)) {
            return
        }
        try {
            let params = {
                AppId: 1253985742,
                GuardExpertConfig: { ...currentData },
                User: engName,
            }
            const res = await UpdateGuardExpertConfig(params)
            if (res.Error) {
                message.error({ content: res.Error.Message });
            } else {
                getTableData()
                message.success({ content: '修改成功' });
                setVisible(false);
            }
        } catch (err) {
            const msg = err.msg || err.toString() || '未知错误';
            message.error({ content: msg });
        }
    }

    // 表格配置
    const tableOptionProps = {
        style: { marginTop: 15 },
        recordKey: 'Product',
        columns: [
            {
                key: "Product",
                header: "产品",
                render: item => <div>{productDict[item.Product] || item.Product}</div>
            },
            {
                key: "ExpertInterfacePerson",
                header: "专项接口人",
                render: item => item.ExpertInterfacePerson ? <div>{item.ExpertInterfacePerson}</div> : <Text theme="weak">未填写</Text>
            },
            {
                key: "AndonDutyID",
                header: "队列ID",
            },
            {
                key: "ExpertInterfaceLeader",
                header: "专项Leader",
                render: item => item.ExpertInterfaceLeader ? <div>{item.ExpertInterfaceLeader}</div> : <Text theme="weak">未填写</Text>
            },
            {
                key: "GuardInterfacePerson",
                header: () => (
                    <>
                        <span>（审批）专项分配负责人</span>
                        <Bubble
                            arrowPointAtCenter
                            placement="top"
                            content="默认分配"
                        >
                            <Icon type="info" />
                        </Bubble>
                    </>
                ),
                render: item => item.GuardInterfacePerson ? <div>{item.GuardInterfacePerson}</div> : <Text theme="weak">未填写</Text>
            },
            {
                key: "Supporter",
                header: () => (
                    <>
                        <span>（审批）巡检结果负责人</span>
                        <Bubble
                            arrowPointAtCenter
                            placement="top"
                            content="默认分配，或在审批过程中自定义填写"
                        >
                            <Icon type="info" />
                        </Bubble>
                    </>
                ),
                render: item => item.Supporter ? <div>{item.Supporter}</div> : <Text theme="weak">未填写</Text>
            },
            {
                key: "Follower",
                header: "默认关注人",
                render: item => item.Follower ? <div>{item.Follower}</div> : <Text theme="weak">未填写</Text>
            },
            {
                key: "operation",
                header: () => (
                    <>
                        <span>操作</span>
                        <Bubble
                            arrowPointAtCenter
                            placement="top"
                            content="修改前，请确保原来审批人员的 MyOA 单已审批"
                        >
                            <Icon type="info" />
                        </Bubble>
                    </>
                ),
                render: item =>
                    <Bubble
                        arrowPointAtCenter
                        placement="top"
                        content={`专项接口人 ${item.ExpertInterfacePerson} 可以修改本条记录`}
                    >
                        <Button disabled={!item.ExpertInterfacePerson.includes(engName)} type="link" onClick={() => {
                            // 是否有人有未审批的单子
                            if (item.NotApprovalProgressPeople) {
                                message.error({ content: `${item.NotApprovalProgressPeople} 有待审批的 MyOA 单或护航审批单，请在审批完成后操作` })
                            } else {
                                setCurrentData(cloneDeep(item));
                                setVisible(true)
                            }

                        }}>修改</Button>
                    </Bubble>

            },
        ],
        records: tableData,
        topTip: isLoading ? <StatusTip status="loading"></StatusTip> : tableData.length === 0 && <StatusTip status="empty" />,
        addons: [
            pageable({
                pageIndex: pageNumber,
                recordCount: totalPage,
                onPagingChange: ({ pageIndex, pageSize }) => {
                    setPageSize(pageSize);
                    setPageNumber(pageIndex);
                }
            }),
        ]
    }

    //页面加载初始化 获取完全产品清单
    useEffect(() => {
        getProductsGroupsInfo();
        aegis.reportEvent({
            name: 'manual-PV',
            ext1: location.pathname,
            ext2: '护航配置管理/默认专项人员',
            ext3: engName
        })
        reportVisitPage({
            isaReportMeunName: '护航配置管理/默认专项人员',
        });
    }, [])

    // 监听当前页和每页数据变化，获取表格数据
    useEffect(() => {
        getTableData();
    }, [pageSize, pageNumber])

    return (
        <>
            <Card>
                <Card.Body>
                    <Row gap={15}>
                        <Col span={6}>
                            <Row verticalAlign={'middle'}>
                                <Col span={6}>
                                    <Text theme="label" verticalAlign="middle">
                                        护航云产品
                                    </Text>
                                </Col>
                                <Col span={18}>
                                    <SelectMultiple
                                        listHeight={400}
                                        boxClassName='productListBox'
                                        appearance="button"
                                        options={productsOptions}
                                        value={productList}
                                        allOption={{ value: 'ALL', text: 'ALL' }}
                                        onChange={(v) => {
                                            setProductList(v);
                                        }}
                                        size="full"
                                        searchable
                                    />
                                </Col>
                            </Row>
                        </Col>
                        <Col span={6}>
                            <Row verticalAlign={'middle'}>
                                <Col span={6}>
                                    <Text theme="label" verticalAlign="middle">
                                        护航人员
                                    </Text>
                                </Col>
                                <Col span={18}>
                                    <Input
                                        placeholder='请输入护航人员'
                                        value={responser}
                                        onChange={(v) => {
                                            setResponser(v);
                                        }}
                                        size="full"
                                    />
                                </Col>
                            </Row>
                        </Col>

                    </Row>
                    <div style={{ marginTop: 10, textAlign: 'center' }}>
                        <Button
                            className='guard-search-button'
                            style={{ margin: 10 }}
                            type="primary"
                            onClick={() => { getTableData() }}>查询
                        </Button>
                        <Button style={{ margin: 10 }} onClick={() => { setProductList([]); setResponser(''); }}>
                            重置
                        </Button>
                    </div>
                </Card.Body>
            </Card>
            <Card>
                <Card.Body>
                    {/* @ts-ignore */}
                    <Table {...tableOptionProps} />
                </Card.Body>
            </Card>
            <Modal size='l' visible={visible} caption={productDict[currentData.Product]} onClose={() => { setVisible(false) }}>
                <Modal.Body>
                    <Row>
                        <Col span={6}>
                            <Text theme="label">专项接口人</Text>
                        </Col>
                        <Col span={18}>
                            <Form.Control showStatusIcon={false} status={currentData.ExpertInterfacePerson ? 'success' : 'error'} message={currentData.ExpertInterfacePerson ? '' : '不能为空'}>
                                <RTXPicker
                                    style={{ "width": "100%" }}
                                    initRtx={currentData.ExpertInterfacePerson ? currentData.ExpertInterfacePerson.split(';') : []}
                                    valueType="array"
                                    onChange={(tags: any) => {
                                        setCurrentData(val => {
                                            val.ExpertInterfacePerson = tags ? tags.join(';') : '';
                                            return cloneDeep(val);
                                        })
                                    }}
                                />
                            </Form.Control>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={6}>
                            <Text style={{ position:'relative', top: 6 }} theme="label">队列ID</Text>
                        </Col>
                        <Col span={18}>
                            <InputNumber
                                max={100000}
                                allowEmpty
                                size='l'
                                hideButton 
								value={currentData.AndonDutyID ? Number(currentData.AndonDutyID) : null}
                                onChange={(id: any) => {
                                    setCurrentData(val => {
                                        val.AndonDutyID = id ? String(id) : '';
                                        return cloneDeep(val);
                                    })
                                }}
							/>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={6}>
                            <Text theme="label">专项Leader</Text>
                        </Col>
                        <Col span={18}>
                            <Form.Control showStatusIcon={false} status={currentData.ExpertInterfaceLeader ? 'success' : 'error'} message={currentData.ExpertInterfaceLeader ? '' : '不能为空'}>
                                <RTXPicker
                                    style={{ "width": "100%" }}
                                    initRtx={currentData.ExpertInterfaceLeader ? currentData.ExpertInterfaceLeader.split(';') : []}
                                    valueType="array"
                                    onChange={(tags: any) => {
                                        setCurrentData(val => {
                                            val.ExpertInterfaceLeader = tags ? tags.join(';') : '';
                                            return cloneDeep(val);
                                        })
                                    }}
                                />
                            </Form.Control>
                        </Col>
                    </Row>
                    <Row verticalAlign={'middle'}>
                        <Col span={6}>
                            <Text theme="label" verticalAlign="middle">
                                （审批）专项分配负责人
                            </Text>
                        </Col>
                        <Col span={18}>
                            <Form.Control showStatusIcon={false} status={currentData.GuardInterfacePerson ? 'success' : 'error'} message={currentData.GuardInterfacePerson ? '' : '不能为空'}>
                                <RTXPicker
                                    style={{ "width": "100%" }}
                                    initRtx={currentData.GuardInterfacePerson ? currentData.GuardInterfacePerson.split(';') : []}
                                    valueType="array"
                                    onChange={(tags: any) => {
                                        setCurrentData(val => {
                                            val.GuardInterfacePerson = tags ? tags.join(';') : '';
                                            return cloneDeep(val);
                                        })
                                    }}
                                />
                            </Form.Control>
                        </Col>
                    </Row>
                    <Row verticalAlign={'middle'}>
                        <Col span={6}>
                            <Text theme="label" verticalAlign="middle">
                                （审批）巡检结果负责人
                            </Text>
                        </Col>
                        <Col span={18}>
                            <Form.Control showStatusIcon={false} status={currentData.Supporter ? 'success' : 'error'} message={currentData.Supporter ? '' : '不能为空'}>
                                <RTXPicker
                                    style={{ "width": "100%" }}
                                    initRtx={currentData.Supporter ? currentData.Supporter.split(';') : []}
                                    valueType="array"
                                    onChange={(tags: any) => {
                                        setCurrentData(val => {
                                            val.Supporter = tags ? tags.join(';') : '';
                                            return cloneDeep(val);
                                        })
                                    }}
                                />
                            </Form.Control>
                        </Col>
                    </Row>
                    <Row verticalAlign={'middle'}>
                        <Col span={6}>
                            <Text theme="label" verticalAlign="middle">
                                默认关注人
                            </Text>
                        </Col>
                        <Col span={18}>
                            <RTXPicker
                                style={{ "width": "100%" }}
                                initRtx={currentData.Follower ? currentData.Follower.split(';') : []}
                                valueType="array"
                                onChange={(tags: any) => {
                                    setCurrentData(val => {
                                        val.Follower = tags ? tags.join(';') : '';
                                        return val;
                                    })
                                }}
                            />
                        </Col>
                    </Row>
                </Modal.Body>
                <Modal.Footer>
                    <Button type="primary" onClick={() => { savePerson() }}>
                        确定
                    </Button>
                    <Button type="weak" onClick={() => { setVisible(false) }}>
                        取消
                    </Button>
                </Modal.Footer>
            </Modal>
        </>
    );
}
