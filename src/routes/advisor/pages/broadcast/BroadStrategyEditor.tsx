import React, { useState, useEffect, useMemo } from 'react';
import { Alert, List, message, Button, ExternalLink, Card, Layout, Stepper, StatusTip, Modal } from '@tencent/tea-component';
import { BaseInfoPanel, InterfaceTestPanel, ResultViewPanel } from './components';
import { useHistory } from '@tea/app';
import { useCheckIfAuthed, NoPermission, PermissionLoading } from './hooks/checkIfAuthed';
import { omit, isEqual, isEmpty, pick, get } from 'lodash';
import uuid from 'react-uuid';
import { useToggle } from './hooks/common';

import {
	startInterfaceTest,
	CreateBroadcastStrategy,
	DescribeBroadcastStrategys,
	ModifyBroadcastStrategy,
} from '@src/api/advisor/broadcast';

import { InterfaceTestRes } from '@src/types/cloudEscort/broadcast/broadStrategy/api';
import { STATUS } from './conf/config';

const { Body, Content } = Layout;

// 接口参数测试文档
const externalUrl = 'https://iwiki.woa.com/pages/viewpage.action?pageId=**********';

const reqParamsConfig = ['AppId', 'Uin', 'SubAccountUin', 'Region', 'ResourceIds', 'Filters', 'AccountArea', 'Period', 'Limit', 'Offset', 'Threshold'];

const steps = [
	{ id: 'baseInfo', label: '基础信息' },
	{ id: 'interfaceTest', label: '接口信息测试' },
	{ id: 'testResult', label: '测试结果' },
];

export function BroadStrategyEditor(match) {
	let isAuth; let authLoading;
	const history = useHistory();
	const operator = localStorage.getItem('engName');

	// 策略信息
	const [strategyInfo, setStrategyInfo] = useState<any>({});
	// 策略启用状态
	const [strategyStatus, setStrategyStatus] = useState<number>(0);

	// 初始策略信息
	const [strategyInfoInit, setStrategyInfoInit] = useState<any>({});

	// 接口测试请求数据
	const [interfaceInfo, setInterfaceInfo] = useState<any>({});
	// 测试结果
	const [result, setResult] = useState<InterfaceTestRes>({});
	// 当前测试状态
	const [testStatus, setTestStatus] = useState<number>(STATUS.PEEDDING);

	// 记录关键字段是否变化
	const [hasStrategyChange, setHasStrategyChange] = useState(false);
	// 记录基础信息是否变化
	const [hasBaseInfoChange, setHasBaseInfoChange] = useState(false);

	const [current, setCurrent] = useState('baseInfo');

	// 保存提示框
	const [visible, openDialog, closeDialog] = useToggle(false);

	const id = match?.match?.params?.strategyid ?? 0;
	if (id > 0) {
		const { isAuth: isAuthOther, loading } = useCheckIfAuthed({
			pageStatus: 'broad-strategy-editor',
			key: '',
			value: '',
		});
		isAuth = isAuthOther;
		authLoading = loading;
	}

	const currentIndex = current ? steps.findIndex(x => x.id === current) : -1;
	const next = current && steps[currentIndex + 1];
	const prev = current ? steps[currentIndex - 1] : steps[steps.length - 1];

	// 获取策略信息
	const getBroadStrategyInfo = async () => {
		if (!isExistStrategy) return;
		try {
			const filters = [
				{ Name: 'strategy_ids', Values: [id] },
			];
			const res = await DescribeBroadcastStrategys({
				Filters: filters.filter((i) => {
					if (i.Values.length) {
						return i;
					}
				}),
				Offset: 0,
				Limit: 10,
				AppId: **********, // 接口必须传appid
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			const item = res.BroadcastStrategy[0];
			const { Url, Action } = item?.Server;
			const strategy = {
				...pick(item, ['Product', 'CNName', 'ENName', 'Type', 'SubType', 'Desc', 'Url', 'Action', 'Threshold']),
				Url,
				Action,
			};
			setStrategyInfo(strategy);
			setStrategyInfoInit({ ...strategy, Online: item.Online });
			setStrategyStatus(item.Online);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 保存策略基础信息
	const handleStrategyInfoSave = (value, { thresholdFactor, thresholdUnit }) => {
		const strategy = {
			...value,
			Threshold: thresholdFactor || thresholdFactor || value.Threshold
				? [{ Values: +value.Threshold, Factor: thresholdFactor, Unit: thresholdUnit, Policy: value.ENName }]
				: [],
		};
		if (isExistStrategy) {
			const isInfoNoChange = isEqual(omit(strategy, ['Desc']), omit(strategyInfoInit, ['Desc', 'Online']));
			setHasStrategyChange(!isInfoNoChange);
			// 当策略信息除了Desc数据发生变化，状态为未生效，并只有测试通过后才能改变
			if (!isInfoNoChange) {
				setStrategyStatus(STATUS.FAIL);
				setTestStatus(STATUS.PEEDDING);
			}
		}
		setStrategyInfo(strategy);
		setHasBaseInfoChange(false);
		message.success({ content: '基础信息保存成功' });
	};

	// 保存接口测试数据
	const handleInterfaceInfoSave = ({ values, resourceIdsList, filters }) => {
		setInterfaceInfo({
			...values,
			ResourceIds: resourceIdsList,
			Filters: filters,
		});
		message.success({ content: '接口信息保存成功' });
	};

	const reqTestParams = useMemo(() => {
		const temp = pick({ ...strategyInfo, ...interfaceInfo }, reqParamsConfig);
		if (get(temp, 'Threshold[0]')) {
			temp.Threshold[0].Policy = strategyInfo.ENName;
		}
		return omit(temp, ['AccountArea', 'Limit', 'Offset']);
	}, [strategyInfo, interfaceInfo]);

	// 接口测试
	const startTestTask = async () => {
		const { Product, CNName, Url } = strategyInfo;

		try {
			const res = await startInterfaceTest({
				AppId: **********,
				RequestId: uuid(),
				ProductName: Product,
				CNName,
				Url,
				TestJson: reqTestParams,
				User: operator,
			});
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				setTestStatus(STATUS.FAIL);
				setResult(res);
				setCurrent(next ? next.id : null);
				return;
			}
			if (res?.Pass === STATUS.SUCCESS) {
				setTestStatus(STATUS.SUCCESS);
				setResult(res);
				message.success({ content: '接口测试成功' });
			} else {
				setTestStatus(STATUS.FAIL);
				setResult(res);
				message.error({ content: '接口测试不通过，请提供有效数据' });
			}
			setCurrent(next ? next.id : null);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	// 策略数据新建保存
	const handleStrategyCreate = async (online) => {
		if (isEmpty(strategyInfo)) {
			message.error({ content: '没有填写基础信息' });
			return;
		}
		try {
			const baseParams = {
				AppId: **********,
				Online: online ? 1 : 0,
				Product: strategyInfo.Product,
				Server: pick(strategyInfo, ['Url', 'Action']),
				Threshold: strategyInfo.Threshold,
				BroadcastStrategy: pick(strategyInfo, ['CNName', 'ENName', 'Type', 'SubType', 'Desc']),
			};
			const reqParams = isExistStrategy
				? { ...baseParams, StrategyId: +id, Updater: operator }
				: { ...baseParams, Creater: operator };
			// @ts-ignore
			const res = isExistStrategy ? await ModifyBroadcastStrategy(reqParams) : await CreateBroadcastStrategy(reqParams);

			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			message.success({ content: '保存策略成功' });
			history.push('/advisor/config-management?tab=1');
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		getBroadStrategyInfo();
	}, []);

	const isExistStrategy = useMemo(() => !!+id, [match]);

	const isShowBaseInfoPanel = useMemo(() => !(isEmpty(strategyInfo) && isExistStrategy), [strategyInfo]);

	const statusDisable = useMemo(() => {
		if (isExistStrategy) {
			if (hasStrategyChange) {
				return testStatus === STATUS.SUCCESS;
			}

			return testStatus === STATUS.SUCCESS || strategyStatus === STATUS.SUCCESS;
		}

		return testStatus === STATUS.SUCCESS;
	}, [isExistStrategy, hasStrategyChange, testStatus, strategyStatus]);

	const baseInfoEditable = useMemo(() => isExistStrategy && strategyInfoInit.Online === STATUS.SUCCESS, [isExistStrategy, strategyInfoInit]);

	const handleNextBtnClick = () => {
		if (current === 'baseInfo' && hasBaseInfoChange) {
			openDialog();
		} else {
			setCurrent(next ? next.id : null);
		}
	};

	if (authLoading) {
		return <PermissionLoading />;
	}
	if (isAuth === false) {
		return <NoPermission />;
	}
	return (
		<Body>
			<Content>
				<Content.Header
					title={isExistStrategy ? '编辑播报策略' : '新增播报策略'}
					onBackButtonClick={() => history.push('/advisor/config-management?tab=1')}
					showBackButton={true}
				/>
				<Content.Body>
					<Alert>
						<h4>特别说明：</h4>
						<List type="bullet">
							<List.Item>
                请参考
								<ExternalLink href={externalUrl} style={{ marginRight: 10 }}>《播报接入说明》</ExternalLink>
							</List.Item>
							<List.Item>策略创建成功后，云产品、播报项、类型和维度将不允许修改</List.Item>
						</List>
					</Alert>
					<Card>
						<Card.Body>
							<Stepper steps={steps} current={current} />
							<section>
								<div>
									{(() => {
										switch (steps[currentIndex]?.id) {
											case 'baseInfo':
												return <>
													{
														isShowBaseInfoPanel
															? <BaseInfoPanel
																partEditable={baseInfoEditable}
																baseStrategyInfo={strategyInfo}
																onFormInfoChange={status => setHasBaseInfoChange(!status)}
																onSubmit={handleStrategyInfoSave}
															/>
															: <StatusTip status="loading" />
													}
												</>;
											case 'interfaceTest':
												return <InterfaceTestPanel
													interfaceInfo={interfaceInfo}
													reqTestParams={reqTestParams}
													onSubmit={handleInterfaceInfoSave}
													onTestTaskStart={startTestTask}
												/>;
											case 'testResult':
												return <ResultViewPanel
													statusEditable={statusDisable}
													testStatus={testStatus}
													resultParam={result}
													status={strategyStatus === STATUS.SUCCESS}
													onStrategyCreate={handleStrategyCreate}
												/>;
											default: return <>已完成策略录入</>;
										}
									})()}
								</div>
								<div style={{ display: 'flex', justifyContent: 'center' }}>
									<Button disabled={!prev} onClick={() => setCurrent(prev.id)}>上一步</Button>
									{
										next
										&& <Button
											type="primary"
											disabled={!next || !current}
											onClick={handleNextBtnClick}
											style={{ marginLeft: 10 }}
										>
												下一步
										</Button>
									}
								</div>
							</section>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
			<Modal visible={visible} caption="保存提醒" onClose={closeDialog}>
				<Modal.Body>当前基础信息还没有保存，确认进入下一步吗</Modal.Body>
				<Modal.Footer>
					<Button type="primary" onClick={() => {
						setCurrent(next ? next.id : null); closeDialog();
					}}>确定</Button>
					<Button type="weak" onClick={closeDialog}>取消</Button>
				</Modal.Footer>
			</Modal>
		</Body >
	);
}
