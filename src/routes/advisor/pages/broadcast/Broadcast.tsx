import React, { useState, useEffect, useMemo } from 'react';
import { Table, message, Button, Row, Card, Layout, Col, Select, Text, StatusTip, Input, Tag, Icon } from '@tencent/tea-component';
import { useHistory, useAegisLog } from '@tea/app';
import { find } from 'lodash';
import { useToggle } from './hooks/common';
import { getUrlParamFromLocation, getSearchParam } from '@src/utils/common';
import { DescribeBroadcastLists } from '@src/api/advisor/broadcast';

import { BroadcastList } from '@src/types/cloudEscort/broadcast/broadcastSub/api';
import { broadcastListConfig, broadcastStatusOption, STATUS } from './conf/config';
import GuideLinks from '../guard/components/GuideLinks';
import { reportVisitPage } from '@src/utils/report';
import { Bubble } from '@tea/component/bubble';
import { PushGuardChat } from '@src/api/advisor/guard';

const { Body, Content } = Layout;
const { pageable } = Table.addons;
interface BoardcastListFilter {
	broadcastId: string,
	guardId: string,
	guardName: string,
	appId: string,
	customerName: string,
	online: string
}

const defaultFilterOption = {
	broadcastId: '',
	guardId: '',
	guardName: '',
	appId: '',
	customerName: '',
	online: 'all',
};

export function Broadcast() {
	const history = useHistory();
	const aegis = useAegisLog();
	// 是否是运营端插件
	const isISA = getSearchParam('source', location) === 'ISA';

	const ctxUser = localStorage.getItem('engName');
	// url参数指定护航ID
	// @ts-ignore
	const { guardid } = getUrlParamFromLocation(['guardid'], location);

	const fixDefaultFilterOption = useMemo(() => ({ ...defaultFilterOption, guardId: guardid }), [guardid]);

	// 筛选项
	const [filter, setFilter] = useState<BoardcastListFilter>(fixDefaultFilterOption);

	const [broadcastList, setBroadcastList] = useState<Array<BroadcastList>>([]);

	// 分页
	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);

	// 加载状态
	const [isLoading, startLoad, endLoad] = useToggle(false);

	// 重置查询条件
	function resetParams() {
		setFilter({ ...defaultFilterOption });
	}

	// 重置状态和监听重置状态进行查询
	const [reStart, setRestart] = useState(true);
	// 监听是否被重置，并在重置后删除事件监听器
	useEffect(() => {
		getBroadcastList();
	}, [reStart]);

	// 一键入群
	const handleJoinChat = async (GuardId) => {
		try {
			const res = await PushGuardChat({
				GuardId,
				AppId: 1253985742,
				User: [ctxUser],
				Type: 'broadcast',
			});
			if (res.Error) {
				message.error({ content: res.Error.Message });
				return;
			}
			aegis.reportEvent({
				name: 'Click',
				ext1: 'broadcast-to-group-btn',
				ext2: localStorage.getItem('engName'),
				ext3: '播报订阅/编辑策略',
			});
			message.success({ content: res.Message });
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};

	useEffect(() => {
		aegis.reportEvent({
			name: 'manual-PV',
			ext1: location.pathname,
			ext2: '播报订阅',
			ext3: ctxUser,
		});
		reportVisitPage({
			isaReportMeunName: '播报订阅',
		});
	}, []);
	const getBroadcastList = async () => {
		startLoad();
		try {
			const { broadcastId, guardId, guardName, appId, customerName, online } = filter;
			const filters = [
				{ Name: 'broadcast_id', Values: [broadcastId] },
				{ Name: 'guard_id', Values: [guardId] },
				{ Name: 'guard_name', Values: [guardName] },
				{ Name: 'appid', Values: [appId] },
				{ Name: 'customer_name', Values: [customerName] },
				{ Name: 'online', Values: online === 'all' ? [''] : [online] },
			];

			const params = {
				AppId: 1253985742,
				Filters: filters,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
			};
			const res = await DescribeBroadcastLists(params);
			if (res.Error) {
				message.error({ content: res.Error.Message });
			} else {
				setBroadcastList(res.BroadcastLists);
				setTotalPage(res.TotalCount);
			}
			endLoad();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			endLoad();
		}
	};

	useEffect(() => {
		setPageNumber(1);
	}, [filter]);

	useEffect(() => {
		getBroadcastList();
	}, [pageSize, pageNumber]);

	const columns = [
		{
			key: 'BroadcastId',
			align: 'left',
			header: '播报ID | 名称',
			width: '15%',
			render: item => <Button type="link" style={{ whiteSpace: 'pre-wrap', textAlign: 'left' }} disabled={!item?.BroadcastUrl && item?.Responser !== ctxUser} onClick={() => {
				aegis.reportEvent({
					name: 'Click',
					ext1: 'broadcast-view-result-btn',
					ext2: ctxUser,
					ext3: '播报订阅',
				});
				if (item?.BroadcastUrl) {
					window.open(item?.BroadcastUrl);
				} else {
					history.push({ pathname: `/advisor/broadcast-editor/${item.BroadcastId}/1` });
				}
			}}
			>
				{`${item.BroadcastId} | ${item.BroadcastName}`}
			</Button>,
		},
		{
			key: 'GuardId',
			align: 'left',
			header: '关联护航单',
			width: '15%',
			render: item => <a
				onClick={() => {
					if (item?.GuardUrl) {
						window.open(item?.GuardUrl);
					} else {
						history.push({ pathname: `/advisor/guard/summary/${item.GuardId}` });
					}
				}
				}
			>
				{`${item.GuardId} | ${item.GuardName}`}
			</a>,
		},
		{
			key: 'GuardName',
			align: 'left',
			header: '客户APPID | 名称',
			width: '20%',
			render: item => (
				item.CustomerInfo.map(({ AppId, Name }, index) => (<div key={index}>
					<Tag >{AppId} | {Name}</Tag><br />
				</div>))
			),
		},
		{
			key: 'Online',
			align: 'left',
			header: '状态',
			render: (item) => {
				const status = find(broadcastStatusOption, status => status.value === `${item.Online}`) || {};
				return (<div style={{ display: 'flex', alignItems: 'center' }}>
					{+status.value === STATUS.SUCCESS ? <Icon type="success" /> : +status.value === STATUS.FAIL ? <Icon type="not" /> : <Icon type="warning" />}
					<span style={{ marginLeft: '3px' }}>{status.text}</span>
				</div>);
			},
		},
		{
			key: 'GroupIds',
			align: 'left',
			header: () => (
				<>
                    播报群
					<Bubble content="仅用于护航播报，默认只有护航提单人和护航负责人在群内。您可点击一键进群。">
						<Icon type="info" />
					</Bubble>
				</>
			),
			render: item => <Button type="link" onClick={() => handleJoinChat(item.GuardId)}>
                    一键进群
			</Button>,
		},
		{
			key: 'Result',
			align: 'left',
			header: '操作',
			render: item => <Button type="link" disabled={ctxUser !== item.Responser} onClick={() => {
				aegis.reportEvent({
					name: 'Click',
					ext1: 'broadcast-view-result-btn',
					ext2: ctxUser,
					ext3: '播报订阅',
				});
				window.open(`/advisor/broadcast-result/${item.BroadcastId}/${item.GuardId}`);
			}}>
        查看播报记录
			</Button>,
		},
		{
			key: 'Creater',
			header: '创建人|更新人',
			align: 'left',
			render: (item) => {
				const { Creater, Updater } = item;
				return (<>
					{Creater && <><Tag>{Creater}</Tag><br /></>}
					{Updater && <Tag>{Updater}</Tag>}
				</>);
			},
		},
		{
			key: 'CreateTime',
			header: '创建时间|更新时间',
			align: 'left',
			width: '15%',
			render: (item) => {
				const { CreateTime, UpdateTime } = item;
				return (<>
					{CreateTime && <><Tag >{CreateTime}</Tag><br /></>}
					{UpdateTime && <Tag >{UpdateTime}</Tag>}
				</>);
			},
		},
	];

	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	const tableOptionProps = {
		style: { marginTop: 15 },
		recordKey: 'BroadcastId',
		verticalTopL: true,
		columns,
		records: broadcastList,
		topTip: isLoading ? <StatusTip status="loading"></StatusTip> : broadcastList.length === 0 && <StatusTip status="empty" emptyText="暂无数据（当前护航暂无播报订阅记录）" />,
		addons: [
			pageable({
				pageIndex: pageNumber,
				recordCount: totalPage,
				onPagingChange: handlePageChange,
			}),
			// autotip({
			//     emptyText: '暂无数据（当前护航暂无播报订阅记录）'
			// }),
		],
	};

	return (
		<Body>
			<Content>
				{!isISA && <Content.Header title="播报订阅" operation={<GuideLinks />}></Content.Header>}
				<Content.Body>
					<Card>
						<Card.Body>
							<Row gap={20}>
								<>{
									broadcastListConfig.map(item => (
										<Col key={item.label}>
											<Row verticalAlign={'middle'}>
												<Col span={6} >
													<Text theme="label" verticalAlign="middle">{item.label}</Text>
												</Col>
												<Col span={18} >
													<Input size='full' value={filter[item.value]} onChange={value => setFilter({ ...filter, [item.value]: value })} />
												</Col>
											</Row>
										</Col>
									))
								} </>
							</Row>
							<Row verticalAlign={'middle'} gap={20}>
								<Col span={6}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">客户名称</Text>
										</Col>
										<Col span={18}>
											<Input size='full' value={filter.customerName} onChange={value => setFilter({ ...filter, customerName: value })} />
										</Col>
									</Row>
								</Col>
								<Col span={6}>
									<Row verticalAlign={'middle'}>
										<Col span={6}>
											<Text theme="label" verticalAlign="middle">状态</Text>
										</Col>
										<Col span={18}>
											<Select
												className='broadcastOnlineSelect'
												appearance="button"
												matchButtonWidth
												size="m"
												options={broadcastStatusOption}
												value={filter.online}
												onChange={value => setFilter({ ...filter, online: value })}
											/>
										</Col>
									</Row>
								</Col>
							</Row>
							<div style={{ margin: 10, textAlign: 'center' }}>
								<Button type="primary" onClick={() => {
									aegis.reportEvent({
										name: 'Click',
										ext1: 'broadcast-view-btn',
										ext2: ctxUser,
										ext3: '播报订阅',
									});
									getBroadcastList();
								}}>查询</Button>
                                &nbsp;&nbsp;
								<Button onClick={() => {
									resetParams();
									setRestart(a => !a);
								}}>重置</Button>
							</div>
						</Card.Body>
					</Card>
					<Card>
						<Card.Body>
							{/* @ts-ignore */}
							<Table {...tableOptionProps} />
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}
