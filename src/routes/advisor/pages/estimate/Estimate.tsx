import React, { useState } from 'react';
import { Card, Layout, Form, Button, Input } from '@tencent/tea-component';
const { Body, Content } = Layout;
import { useForm, useField } from 'react-final-form-hooks';

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return 'validating';
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? 'error' : 'success';
}

export function Estimate({ history, location }) {
	const onSubmit = (values) => {
		history.push({
			pathname: `/advisor/estimate/result/${values.appid}`,
		});
	};
	const { form, handleSubmit, validating, submitting } = useForm({
		onSubmit,
		/**
		 * 默认为 shallowEqual
		 * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
		 * useEffect(() => form.initialize({ }), []);
		 */
		initialValuesEqual: () => true,
		initialValues: {
			uin: (location.state && location.state.uin) || '',
			appid: (location.state && location.state.appid) || '',
		},
		validate: ({ uin, appid }) => ({
			//uin: !uin.trim() ? "请输入UIN" : undefined,
			appid: !appid.trim() ? '请输入AppId' : undefined,
		}),
	});

	//const uin = useField("uin", form);
	const appid = useField('appid', form);
	return (
		<Body>
			<Content>
				<Content.Header title="评估结果"></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body>
							<div className="empty-card-body">
								<form onSubmit={handleSubmit} className="assess-search-form">
									<Form layout="inline">
										{/* <Form.Item
                                            status={getStatus(uin.meta, validating)}
                                            className="item"
                                            label="UIN"
                                        >
                                            <Input
                                                size="m"
                                                placeholder="输入UIN"
                                                {...uin.input}
                                            />
                                        </Form.Item> */}
										<Form.Item
											status={getStatus(appid.meta, validating)}
											className="item"
											label="AppId"
										>
											<Input size="m" placeholder="输入Appid" {...appid.input} />
										</Form.Item>
									</Form>
									<Form.Action className="action">
										<Button
											type="primary"
											htmlType="submit"
											loading={submitting}
											disabled={validating}
										>
											查询
										</Button>
									</Form.Action>
								</form>
							</div>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}
