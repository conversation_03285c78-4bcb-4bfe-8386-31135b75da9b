import React, { useState, useEffect } from 'react';
import { Layout, message as tips, DatePicker } from '@tencent/tea-component';
import { LineChart } from '@src/components/LineChart';

import moment from 'moment';
import { getAdvisorMonitor } from '@src/api/advisor/monitor';
import { expandNumberList } from '@src/utils/arrayUtils';
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';
import { reportVisitPage } from '@src/utils/report';
const { RangePicker } = DatePicker;
const { Body, Content } = Layout;

export function Monitor() {
	// 开始时间
	const [start, setStart] = useState(moment().subtract(1, 'month'));
	// 结束时间
	const [end, setEnd] = useState(moment().subtract(1, 'days'));

	// 用户数量列表
	const [userNumberList, setUserNumberList] = useState([]);
	// 资源数量列表
	const [resourceTotalList, setResourceTotalList] = useState([]);
	// 策略数量列表
	const [strategyTotalList, setStrategyTotalList] = useState([]);

	const [loading, setLoading] = useState(false);
	const [error, setError] = useState(false);
	// 页面权限状态
	const history = useHistory();
	const [permission, setPermission] = useState(0); // 0表示未知，还没有拉取到数据，默认显示空；1表示有权限，显示正常页面；2表示无权限，显示无权限页面
	// 从localStorage获取菜单列表，并根据当前路径判断是否有权限
	const CheckPermission = () => {
		const menuItems = JSON.parse(localStorage.getItem('menuItems'));
		if (menuItems) {
			// 判断是否存在
			const tmp = menuItems.filter((i) => {
				if (history.location.pathname.includes(i.route)) {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
	};
	// 持续从localStorage获取菜单列表
	let timer;
	useEffect(() => {
		timer = setInterval(() => {
			CheckPermission();
		}, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	useEffect(() => {
		fetch();
		reportVisitPage({
			isaReportMeunName: '大盘监控',
		});
	}, []);

	const fetch = async (_start = start, _end = end) => {
		setLoading(true);
		setError(false);
		try {
			const res = await getAdvisorMonitor({
				StartTime: _start.format('YYYY-MM-DD'),
				EndTime: _end.format('YYYY-MM-DD'),
			});
			if (res.Error) {
				setError(true);
				// 判断页面权限,401表示无权限，其他状态码表示接口报错
				if (res.Error.Code === 401) {
					setPermission(2);
				} else {
					tips.error({ content: res.Error.Message });
				}
			} else {
				setUserNumberList(expandNumberList(res.ListUsers));
				setResourceTotalList(expandNumberList(res.ResourceTotalList.map(item => ({
					AssessedResourceTotal:
								item.HealthCount + item.HighRiskCount + item.MediumRiskCount + item.IgnoreCount,
					...item,
				}))));
				// setStrategyTotalList(
				// 	expandNumberList(
				// 		res.StrategyTotalList.map((item) => ({
				// 			AssessedStrategyTotal:
				// 				item.HealthCount + item.HighRiskCount + item.MediumRiskCount + item.IgnoreCount,
				// 			...item,
				// 		}))
				// 	)
				// );
			}
		} catch (err) {
			const message: string = err.msg || err.toString() || 'unknown error';
			tips.error({ content: message });
			setError(true);
		}
		setLoading(false);
	};

	return (
		<Body>
			{permission === 0 ? (
				<div></div>
			) : (
				<div>
					{permission === 2 ? (
						<NotPermission />
					) : (
						<Content>
							<Content.Header
								title="大盘监控"
								operation={
									<RangePicker
										style={{ marginRight: 30 }}
										onChange={(value) => {
											setStart(value[0]);
											setEnd(value[1]);
											fetch(value[0], value[1]);
										}}
										value={[start, end]}
									/>
								}
							></Content.Header>
							<Content.Body>
								<LineChart
									mark={'advisor1'}
									data={userNumberList}
									title="累计授权用户数"
									fetch={() => {
										fetch();
									}}
									status={
										(loading && 'loading')
										|| (error && 'error')
										|| (userNumberList.length === 0 ? 'empty' : null)
									}
								/>
								<LineChart
									mark={'advisor2'}
									data={resourceTotalList}
									title="评估资源数"
									fetch={() => {
										fetch();
									}}
									status={
										(loading && 'loading')
										|| (error && 'error')
										|| (resourceTotalList.length === 0 ? 'empty' : null)
									}
								/>
								{/* <LineChart
						mark={'advisor3'}
						data={strategyTotalList}
						title="评估策略数"
						fetch={() => {
							fetch();
						}}
						status={
							(loading && 'loading') ||
							(error && 'error') ||
							(strategyTotalList.length === 0 ? 'empty' : null)
						}
					/> */}
							</Content.Body>
						</Content>
					)}
				</div>
			)}
		</Body>
	);
}
