import React, { useState, useEffect } from 'react';
import {
	Table,
	Button,
	Card,
	Layout,
	message as tips,
	StatusTip,
	Bubble,
	Icon,
	Text,
	Justify,
	TagSearchBox,
	Tag,
	message,
} from '@tencent/tea-component';
import { reportVisitPage } from '@src/utils/report';
import { forEach, map, isEmpty, replace } from "lodash";
import { stringMap2Options, ApproveInfo, ApprovalStatusDict } from '@src/types/privilege/privilege';
import { getUserInfo } from '@src/api/common';
import { getApproveInfo, getUserRoles } from '@src/api/privilege/privilege';
import { AttributeValue } from '@tencent/tea-component/src/tagsearchbox/AttributeSelect';
import { useHistory } from '@tea/app';

const { Body, Content } = Layout;
const { pageable, sortable } = Table.addons;

//所有APPID账号
const allAcount = 88888;

export function PrivilegeApplyAndRole() {
    // 分页
    const [pageSize, setPageSize] = useState<number>(10);
    const [totalPage, setTotalPage] = useState<number>(0);
    const [pageNumber, setPageNumber] = useState<number>(1);

    const [loading, setLoading] = useState<boolean>(false)

    const history = useHistory();

    const [tagSelectBoxValue, setTagSelectBoxValue] = useState([]);

    const [listFilter, setListFilter] = useState([]);
    //当前登录rtx
    const [rtx, setRtx] = useState<string>('');

    //个人申请和权限列表
    const [applyRoleSet, setApplyRoleSet] = useState<Array<ApproveInfo>>([]);

    //当前排序列
    const [sorts, setSorts] = useState([]);

    //业务角色映射
    const [userRoleDict, setUserRoleDict] = useState([]);
    //系统角色映射
    const [systemRoleDict, setSystemRoleDict] = useState<Map<string, string>>(new Map());

    //获取当前登录账号rtx
    const getCurrentOperator = async () => {
        try {
            const res = await getUserInfo()
            setRtx(res.data.EngName || '')
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //我的权限单表头
    const myPrivilegeColumns = [
        {
            key: 'ApplyId',
            header: 'ID',
            width: '5%',
            render: item => {
                return <Button type="link" onClick={() => history.push(`/advisor/privilege/apply/${item.ApplyId}`)}>{item.ApplyId}</Button>
            }
        },
        {
            key: 'Appid',
            header: 'Appid',
            width: '12%',
            render: item => {
                if (item.CustomerInfo.AppId === 0) {
                    return <Text>--</Text>
                }
                if (item.CustomerInfo.AppId === allAcount) {
                    return <Text>所有用户</Text>
                } else {
                    return <>
                        <Text>{item.CustomerInfo.AppId}</Text>
                        <br />
                        <Text>{item.CustomerInfo.CustomerName}</Text>
                    </>
                }
            }
        },
        {
            key: 'UserRoleZh',
            header: '岗位角色',
        },
        {
            key: 'SystemRoles',
            header: '权限',
            width: '16%',
            render: item => (<>
                {
                    item.SystemRoles?.map((item, index) => (<div key={index}>
                        {
                            systemRoleDict.get(item)
                            && <><Tag theme="primary" >{systemRoleDict.get(item)}</Tag><br /></>
                        }
                    </div>))
                }
            </>)
        },
        {
            key: 'Status',
            header: <>
                状态
                <Bubble
                    content={<>请审批人到<a href="https://my.woa.com/" target="_blank" rel="noopener noreferrer">https://my.woa.com/</a>实施审批动作</>}>
                    <Icon type="info" />
                </Bubble>
            </>,
            render: item => {
                const theme = item.Status === 'finish' ? 'success' : item.Status === 'approving' ? 'warning' : 'danger';
                return (<div style={{ display: 'flex', alignItems: 'center' }}>
                    <Icon type={theme === 'danger' ? 'error' : theme} />
                    <span style={{ marginLeft: '3px' }}>
                        <Text theme={theme}>{ApprovalStatusDict.get(item.Status)}</Text>
                    </span>
                </div>)
            }
        },
        {
            key: 'CurrentApprover',
            header: '审批人',
            render: item => {
                return <Text>{item.CurrentApprover.trim() || '--'}</Text>
            }
        },
        {
            key: 'ApproveMessage',
            header: '审批信息',
            render: item => {
                return <Text>{item.ApproveMessage || "--"}</Text>
            }
        },
        {
            key: 'ExpireTime',
            header: '有效期至',
        },
        {
            key: 'CreateTime',
            header: '申请时间',
        },
        {
            key: 'operate',
            header: '操作',
            render: item => {
                return <Button
                    type="link"
                    onClick={() => history.push(`/advisor/privilege/apply-renew/${item.ApplyId}`)}
                    disabled={item.Status !== 'finish' || item.CustomerInfo.AppId === allAcount}
                >续期</Button>
            }
        },
    ]

    //查询我的权限列表
    const getApplyAndRoles = async () => {
        if (!rtx) return;
        setLoading(true);
        try {
            const res = await getApproveInfo({
                Name: rtx,
                Filters: listFilter,
                Offset: (pageNumber - 1) * pageSize,
                Limit: pageSize,
            })
            setLoading(false)
            if (res.Error) {
                tips.error({ content: res.Error.Message });
                return
            } else {
                let ApproveInfoSet = res.ApproveInfoSet || []
                setApplyRoleSet(ApproveInfoSet)
                setTotalPage(res.TotalCount)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    useEffect(() => {
        getApplyAndRoles()
    }, [listFilter, pageNumber, pageSize, rtx]);

    //查询权限列表
    const getRoleRecords = async () => {
        setLoading(true)
        try {
            const res = await getUserRoles({})
            setLoading(false)
            if (res.Error) {
                tips.error({ content: res.Error.Message });
                return
            } else {
                //获取业务角色映射关系
                let tmpUserRoleList = map(res.UserRoleSet, ({ Name, CnName }) => ({ key: Name, name: CnName }));
                setUserRoleDict(tmpUserRoleList)

                //获取系统角色映射关系
                let tmpSystemRoleDict = new Map()
                res.SystemRoleSet.map(i => {
                    map(i.SystemRoles, item => {
                        tmpSystemRoleDict.set(item.Name, item.CnName)
                    })
                })
                setSystemRoleDict(tmpSystemRoleDict)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //页面加载初始化
    useEffect(() => {
        getCurrentOperator();
        getRoleRecords();
        reportVisitPage({
			isaReportMeunName: '我的权限',
		});
    }, [])

    const handleSearchTagChange = (tags) => {
        const filter = [];
		let isCorrectAppId = true;
        forEach(tags, (tag) => {
            const name = tag.attr ? tag.attr.key : 'name';
            const valueList = map(tag.values, item => (
                tag.attr?.key === "userRoles" || tag.attr?.key === "status"
                    ? item.key
                    : item.name
            ));
            if (tag.attr?.key === 'appId') {
				const tempAppIdList = valueList[0]?.split(",");
				// 设置正则过滤非数字内容
				for (let i = 0;i < tempAppIdList.length;i++) {
					const newNum = replace(tempAppIdList[i], /[^0-9]/g, '');
					if (newNum !== tempAppIdList[i]) {
						isCorrectAppId = false;
						break;
					}
				}
				if (!isCorrectAppId) {
					message.warning({ content: 'AppId只能为纯数字' });
				}
                filter.push({ Name: name, Values: tempAppIdList });
            }
            else {
                filter.push({
                    Name: name,
                    Values: tag.attr?.key === 'name' ? isEmpty(valueList) ? [rtx] : valueList : valueList
                });
            }
        });
		setTagSelectBoxValue(tags);
		if (isCorrectAppId) {
			setListFilter(filter);
      setPageNumber(1);
		}
    };

    // 标签搜索框
    const attributes: Array<AttributeValue> = [
        {
            type: ['multiple', { searchable: true }],
            key: 'userRoles',
            name: '岗位角色',
            values: userRoleDict,
        },
        {
            type: ['multiple', { searchable: true }],
            key: 'status',
            name: '状态',
            values: map(stringMap2Options(ApprovalStatusDict), ({ text, value }) => ({ key: value, name: text })),
        },
        {
            type: "input",
            key: "name",
            name: "用户名",
        },
        {
            type: "input",
            key: "appId",
            name: "数据范围",
        },
    ];
    const tagSearchBoxProps = {
        minWidth: '55%',
        attributes,
        value: tagSelectBoxValue,
        hideHelp: true,
        tips: '支持岗位角色、状态、用户名和AppId数据范围(逗号分隔如appid1,appid2)过滤，多个关键词用竖线"|"分隔',
        onChange: handleSearchTagChange,
    };

    const handlePageChange = ({ pageIndex, pageSize }) => {
        setPageSize(pageSize);
        setPageNumber(pageIndex);
    };
    return (
        <Body>
            <Content>
                <Content.Header title="我的权限"></Content.Header>
                <Content.Body>
                    <Card>
                        <Card.Body>
                            <Justify
                                style={{ marginBottom: 30 }}
                                left={
                                    <div style={{ display: 'flex' }}>
                                        <TagSearchBox {...tagSearchBoxProps} />
                                        <Button
                                            type="primary"
                                            style={{ marginLeft: 12 }}
                                            onClick={() => history.push('/advisor/privilege/apply-editor')}
                                        >申请</Button>
                                    </div>
                                }
                                right={<></>}
                            />
                            <Table
                                columns={myPrivilegeColumns}
                                records={[...applyRoleSet].sort(sortable.comparer(sorts))}
                                topTip={
                                    (loading || applyRoleSet.length === 0) &&
                                    <StatusTip status={loading ? "loading" : "empty"} />
                                }
                                addons={[
                                    pageable({
                                        pageIndex: pageNumber,
                                        recordCount: totalPage,
                                        onPagingChange: handlePageChange,
                                    }),
                                    sortable({
                                        columns: [{ key: "CreateTime", prefer: "desc" }],
                                        value: sorts,
                                        onChange: value => setSorts(value),
                                    }),
                                ]}
                                recordKey="ApplyId"
                            />
                        </Card.Body>
                    </Card>
                </Content.Body>
            </Content>
        </Body>
    );
}
