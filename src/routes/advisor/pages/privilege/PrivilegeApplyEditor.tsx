import React, { useState, useEffect, useMemo } from 'react';
import { Table, Button, Card, Layout, message as tips, Text, Input, Form, Radio, StatusTip, Modal, Select, Bubble, Stepper } from '@tencent/tea-component';

const { Body, Content } = Layout;
import { cloneDeep, map, isEmpty, toPairs, reduce, find, some } from "lodash";

import { ApprovalDaysDict, CustomerInfo, numberMap2Options } from '@src/types/privilege/privilege';
import { getAppIDByUin, getUserInfo } from '@src/api/common';
import { createUserRules, getUserRoles, getSystemRoles } from '@src/api/privilege/privilege';
import PrivilegeRoleEditor from './PrivilegeRoleEditor';
import { useHistory } from '@tea/app';
import './style.less';
import { Col, Icon, message, Row } from "@tea/component";

const { selectable } = Table.addons;

export function PrivilegeApplyEditor() {
	const history = useHistory();
	const [loading, setLoading] = useState<boolean>(false);

	//当前登录rtx
	const [rtx, setRtx] = useState<string>('');

	//角色数据范围编辑框
	const [visibleRoleEditor, setVisibleRoleEditor] = useState<boolean>(false);
	//已选实例数量弹窗
	const [visibleSelectedAppids, setVisibleSelectedAppids] = useState<boolean>(false);

	//选择申请天数
	const [applyDays, setApplyDays] = useState<number>(7);
	//申请原因
	const [reason, setReason] = useState<string>('');
	//申请原因必填提示
	const [visibleReason, setVisibleReason] = useState<boolean>(false);

	// 权限列表选中的业务权限
	const [selectedRoles, setSelectedRoles] = useState<Array<string>>([])

	// 选中的角色
	const [userRole, setUserRole] = useState('TAM');

	// 岗位角色列表
	const [userRoleList, setUserRoleList] = useState([]);

	// 权限列表
	const [systemRoleList, setSystemRoleList] = useState([]);

	// 权限和appidlist的数据
	const [roleAndAppidData, setRoleAndAppidData] = useState({});

	// 当前权限
	const [currentPermission, setCurrentPermission] = useState('');

	// 保存appid搜索值
	const [allSearchValue, setAllSearchValue] = useState('');


	const [notSetAppids, setNotSetAppids] = useState(false)
	//获取当前登录账号rtx
	const getCurrentOperator = async () => {
		try {
			const res = await getUserInfo()
			setRtx(res.data.EngName || '')
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}
	//查询角色列表
	const getRoleRecords = async () => {
		try {
			const res = await getUserRoles({ Offset: 0, Limit: 30 });
			if (res.Error) {
				tips.error({ content: res.Error.Message });
				return
			} else {
				setUserRoleList(res.UserRoleSet);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	//查询当前角色的权限列表
	const getSystemRolesList = async () => {
		setLoading(true)
		try {
			const res = await getSystemRoles({ UserRole: userRole })
			if (res.Error) {
				tips.error({ content: res.Error.Message });
				return
			} else {
				let rolesList = [];
				map(res.SystemRoleInfoSet, item => {
					rolesList = rolesList.concat(item.SystemRoles)
				});

				setSystemRoleList(rolesList);
			}
			setLoading(false)
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	function StepperProcess() {
		const steps = [
			{
				id: "choose",
				label: "1、选择岗位角色”",
			},
			{
				id: "load_permission",
				label: "2、选择“权限”",
				detail: (
					<>
						按需勾选权限
					</>
				),
			},
			{
				id: "load_appid",
				label: "3、添加“数据范围”",
				detail: (
					<>
						“数据范围（AppID）” &gt; 点击“添加” &gt; 在弹框中搜索并选择AppID（如显示“--”则无需操作）
					</>
				),
			},
			{
				id: "load_affected",
				label: "4、填写时长、原因",
			},
			{
				id: "approve",
				label: "5、提交申请",
				detail: (
					<>
						可在我的权限首页查看当前审批人，审批人在
						<a href="https://my.woa.com/" target="_blank" rel="noopener noreferrer">MyOA</a>
						审批
					</>
				),
			},
		];

		return <Stepper type="default" steps={steps} />;
	}

	//角色单表头
	const roleColumns = [
		{
			key: "select",
			header: "选择",
			width: '10%',
		},
		{
			key: 'CnName',
			header: '权限',
		},
		{
			key: 'RoleRange',
			header: <Bubble error placement="top" visible={notSetAppids} content="请点击“添加”选择数据范围" >
				<span style={{ color: 'red', marginRight: 4 }}>*</span>数据范围（AppID）
				<Text style={
					{
						cursor: 'pointer',
						color: '#000'
					}
				} onClick={(e) => {
					setVisibleUinTransfer(true);
					e.preventDefault();
				}
				}>UIN转换</Text>
			</Bubble>,
			render: item => {
				if (item.ApproveType !== 1) return "--";
				let appidsLength = (roleAndAppidData[item.Name] || []).length;
				const checkRoles = selectedRoles?.includes(item.Name);
				return <Button
					type="link"
					disabled={!checkRoles}
					onClick={() => {
						setCurrentPermission(item.Name);
						setVisibleRoleEditor(true)
					}}
				>
					{appidsLength > 0 ? '编辑' : checkRoles && notSetAppids ? <span style={{ color: 'red' }}>添加</span> : '添加'}
				</Button>
			}
		},
		{
			key: 'Selected',
			header: '已选数据',
			render: item => {
				if (item.ApproveType !== 1) return "--";
				let appidsLength = (roleAndAppidData[item.Name] || []).length
				return <Button
					type="link"
					disabled={!selectedRoles?.includes(item.Name)}
					onClick={() => {
						setCurrentPermission(item.Name);
						setVisibleSelectedAppids(true)
					}}
				>{appidsLength}</Button>
			}
		},
	]

	//已选APPID表头
	const selectedAppidsColumns = [
		{
			key: 'AppID',
			header: 'APPID',
			render: item => {
				return <Text>{item.AppID}</Text>
			}
		},
		{
			key: 'CustomerName',
			header: '客户名称',
			render: item => {
				return <Text>{item.CustomerName}</Text>
			}
		},
	]

	useEffect(() => {
		getSystemRolesList();
		setRoleAndAppidData({});
		setSelectedRoles([]);
	}, [userRole]);

	useEffect(() => {
		let temp = cloneDeep(roleAndAppidData);
		map(selectedRoles, item => {
			if (!temp[item]) {
				temp[item] = []
			}
		})
		setRoleAndAppidData(temp);
	}, [selectedRoles]);

	//页面加载初始化
	useEffect(() => {
		getCurrentOperator();
		getRoleRecords();
	}, []);

	//创建申请单
	const createApply = async () => {
		if (!reason.trim()) {
			setVisibleReason(true);
			return
		}
		try {
			const systemRoleAndAppIdData = reduce(toPairs(roleAndAppidData), (ret, [key, value]) => {
				if (selectedRoles.includes(key)) {
					ret.push({ SystemRoleSet: key, AppIdSet: map(value, item => item.AppID) })
				}
				return ret;
			}, [])
			const params = {
				Name: rtx,
				DurationTime: applyDays,
				ApplyReason: reason,
				Data: [
					{
						UserRole: userRole,
						SystemRoleAndAppIdData: systemRoleAndAppIdData
					}
				]
			}
			const res = await createUserRules(params)
			if (res.Error) {
				tips.error({ content: res.Error.Message });
				return
			} else {
				tips.success({ content: "权限提交成功！" })
				history.push('/advisor/privilege/my')
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	useEffect(() => {
		if (isEmpty(selectedRoles)) {
			setNotSetAppids(false);
			return;
		}
		const existNotSetAppids = some(toPairs(roleAndAppidData), ([key, value]) => {
			if (selectedRoles.includes(key)) {
				const currentRole = find(systemRoleList, item => item.Name === key) || {};
				return !isEmpty(currentRole) && isEmpty(value) && currentRole.ApproveType === 1;
			}
		})
		setNotSetAppids(existNotSetAppids)
	}, [roleAndAppidData, selectedRoles, systemRoleList])

	//检查申请参数
	const checkParamsOrCreateApply = () => {
		let existNotSetAppids = false;
		map(toPairs(roleAndAppidData), ([key, value]) => {
			if (selectedRoles.includes(key)) {
				const currentRole = find(systemRoleList, item => item.Name === key) || {};
				if (!isEmpty(currentRole) && isEmpty(value) && currentRole.ApproveType === 1) {
					existNotSetAppids = true
				}
			}
		})
		if (existNotSetAppids) {
			setNotSetAppids(true);
		} else {
			createApply()
		}
	}

	//更新选择的APPID
	const updateSelectedData = (searchValue, records: Array<CustomerInfo>) => {
		setAllSearchValue(searchValue);

		let temp = cloneDeep(roleAndAppidData);
		temp[currentPermission] = records;
		setRoleAndAppidData(temp);
	}

	const userRoleOption = useMemo(() => {
		return map(userRoleList, ({ Name, CnName }) => ({ text: CnName, value: Name }))
	}, [userRoleList]);

	const tableListProps = {
		recordKey: 'Name',
		columns: roleColumns,
		records: systemRoleList,
		topTip: loading ? <StatusTip status="loading" /> : isEmpty(systemRoleList) && <StatusTip status="empty" />,
		addons: [
			selectable({
				targetColumnKey: "select",
				all: false,
				value: selectedRoles,
				onChange: (value, context) => {
					setSelectedRoles(value);
				},
			})
		]
	}
	const [uin, setUin] = useState<string>("");                                   // 转换源UIN
	const [appIdTransfered, setAppIdTransfered] = useState<number>(0);            // 转换目标APPID
	const [visibleUinTransfer, setVisibleUinTransfer] = useState(false);          // UIN转换
	// UIN转换
	const transferUin = async (uin: string) => {
		if (uin.trim() == "") {
			message.error({ content: "uin为空，请输入合法uin值" });
			return;
		}

		try {
			const res = await getAppIDByUin({
				SrcUin: uin,
			});
			if (res.Error) {
				const msg = res.Error.Message
				message.error({ content: msg });
				return;
			} else {
				setAppIdTransfered(res.AppId)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误";
			message.error({ content: msg });
		}
	};
	return (
		<Body className='apply-editor'>
			<Content>
				<Content.Header
					showBackButton
					onBackButtonClick={() => history.push('/advisor/privilege/my')}
					title="权限申请"
				></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body title="申请流程">
							{StepperProcess()}
						</Card.Body>
					</Card>
					<Card>
						<Card.Body>
							<div className='apply-editor-role'>
								<h3 className='apply-editor-role-title'>岗位角色</h3>
								<Radio.Group
									value={userRole}
									onChange={value => setUserRole(value)}
								>
									{
										userRoleOption?.map((item, index) => (
											<Radio key={index} name={item.value}>{item.text}</Radio>
										))
									}
								</Radio.Group>
							</div>
						</Card.Body>
						<Card.Body title="权限选择">
							{/* @ts-ignore */}
							<Table {...tableListProps} />
						</Card.Body>
						<Card.Body >
							<Form>
								<Form.Item label="申请时长" required>
									<Select
										options={numberMap2Options(ApprovalDaysDict)}
										value={applyDays.toString()}
										onChange={(v) => { setApplyDays(Number(v)) }}
										appearance="button"
										searchable
										size="m"
									/>
								</Form.Item>
								<Form.Item label="申请原因" required>
									<Bubble error placement="top" visible={visibleReason} content="申请原因不能为空" >
										<Input value={reason} onChange={v => { setReason(v); setVisibleReason(!v.trim()) }} size="l" />
									</Bubble>
								</Form.Item>
							</Form>
							<div className='apply-editor-box'>
								<Button
									type="primary"
									disabled={notSetAppids || !reason}
									className='apply-editor-box-btn'
									onClick={checkParamsOrCreateApply}
								>
									提交申请
								</Button>
							</div>
						</Card.Body>
					</Card>
				</Content.Body>
				{
					<Modal visible={visibleRoleEditor} size='xl' onClose={() => setVisibleRoleEditor(false)}>
						<Modal.Body>
							<PrivilegeRoleEditor
								rtx={rtx}
								currentRole={userRole}
								allSearchValue={allSearchValue}
								selectedAppids={map(roleAndAppidData[currentPermission], item => item.AppID + '') || []}
								selectedRecords={roleAndAppidData[currentPermission] || []}
								onPickDataChange={(searchValue, recordsPassed) => { updateSelectedData(searchValue, recordsPassed) }}
							/>
						</Modal.Body>
						<Modal.Footer>
							<Button type="primary" onClick={() => setVisibleRoleEditor(false)}>
								确定
							</Button>
						</Modal.Footer>
					</Modal>
				}
				{
					<Modal visible={visibleSelectedAppids} size='xl' onClose={() => setVisibleSelectedAppids(false)}>
						<Table
							recordKey="AppID"
							columns={selectedAppidsColumns}
							records={roleAndAppidData[currentPermission]}
							topTip={
								loading ? <StatusTip status="loading" /> : isEmpty(roleAndAppidData[currentPermission]) && <StatusTip status="empty" />
							}
						/>
					</Modal>
				}
				{ // UIN转APPID弹窗
					<Modal size={"m"} visible={visibleUinTransfer} onClose={() => { setVisibleUinTransfer(false); close(); }} caption="将 UIN 转换为 APPID">
						<Modal.Body>
							<Card key={"uinTransfer"} style={{ margin: 2 }}>
								<Card.Body>
									<Row gap={30} verticalAlign={"middle"}>
										<Col span={4}>客户UIN</Col>
										<Col span={6}>
											<Input
												value={uin}
												onChange={(value, context) => {
													setUin(value);
												}}
												placeholder="请输入UIN，以转换输出APPID"
											/>
										</Col>
									</Row >
									<Row>
										<Col span={4}>
											<Button type="link" onClick={() => { transferUin(uin); }} >
												转换<Icon type="arrowright" />
											</Button>
										</Col>
										<Col span={6}>
											<Text align="left">{appIdTransfered ? appIdTransfered : ""}</Text>
										</Col>
									</Row >
								</Card.Body>
							</Card>
						</Modal.Body>
					</Modal>
				}
			</Content>
		</Body >
	);
}
