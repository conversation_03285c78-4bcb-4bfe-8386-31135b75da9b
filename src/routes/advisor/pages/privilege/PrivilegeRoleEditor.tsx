import React, { useState, useEffect, forwardRef } from 'react';
import { Text, Transfer, Table, SearchBox, message as tips, StatusTip, } from '@tencent/tea-component';

import _ from "lodash";
import { AppidStatusInRoleDict, CustomerInfo } from '@src/types/privilege/privilege';
import { getCustomerInfoByAppID } from '@src/api/privilege/privilege';
const { selectable, removeable, scrollable } = Table.addons;

interface Props {
	onPickDataChange: Function,
	currentRole?: string,
	selectedAppids?: Array<string>,
	selectedRecords?: Array<CustomerInfo>,
	rtx?: string,
	allSearchValue: string
}

const sourceColumns = [
	{
		key: "appid",
		header: "appid/客户名称",
		render: item => (
			<>
				<p>
					<Text theme="primary">{item.AppID}</Text>
				</p>
				<p>{item.CustomerName}</p>
			</>
		),
	},
	{
		key: "status",
		header: "状态",
		width: 100,
		render: item => {
			switch (item.Status) {
				case 1:
					return <span style={{ color: "green" }}>已申请</span>;
				case 0:
					return <span style={{ color: "blue" }}>未申请</span>;
			}
			return AppidStatusInRoleDict.get(item.Status);
		},
	},
];

const targetColumns = [
	{
		key: "appid",
		header: "appid",
		render: item => (<Text theme="primary">{item.AppID}</Text>),
	},
	{
		key: "customerName",
		header: "客户名称",
		render: item => (<p>{item.CustomerName}</p>),
	},
];

function SourceTable({ dataSource, targetKeys, onChange, loading }) {
	return (
		<Table
			records={dataSource}
			recordKey="AppID" //与加载记录(records)的字段名相同，而非与表头(columns)的字段名相同
			rowDisabled={record => { return record.Status === 1 }} // 已申请账号不支持选择
			rowClassName={record => record.Status}
			columns={sourceColumns}
			topTip={loading && <StatusTip status="loading" />}
			addons={
				[
					scrollable({
						maxHeight: 310,
						onScrollBottom: () => console.log("到达底部"),
					}),
					selectable({
						value: targetKeys,
						onChange,
						rowSelect: true,
					}),
				]}
		/>
	);
}

function TargetTable({ dataSource, onRemove }) {
	return (
		<Table
			records={dataSource}
			recordKey="AppID" //与加载记录(records)的字段名相同，而非与表头(columns)的字段名相同
			columns={targetColumns}
			addons={[removeable({ onRemove })]}
		/>
	);
}

function PrivilegeRoleEditor({ currentRole, allSearchValue, selectedAppids, selectedRecords, rtx, onPickDataChange }: Props) {
	const [searchValue, setSearchValue] = useState('');
	const [sourceRecords, setSourceRecords] = useState<Array<CustomerInfo>>([]);

	const [targetRecords, setTargetRecords] = useState<Array<CustomerInfo>>(selectedRecords || []);
	const [targetKeys, setTargetKeys] = useState(selectedAppids || []);

	const [loading, setLoading] = useState(false);

	const getCustomerInfo = async (appids, offset, limit) => {
		setLoading(true)
		try {
			const res = await getCustomerInfoByAppID({
				Name: rtx,
				AppIDSet: appids,
				UserRole: currentRole,
				Offset: offset,
				Limit: limit,
			})
			setLoading(false)
			if (res.Error) {
				tips.error({ content: res.Error.Message });
				return
			} else {
				setSourceRecords(res.CustomerInfoSet)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	//更新选择的APPID，监听其值更新相关记录
	const updateSelectedData = (selected: Array<string>) => {
		let tmp = _.cloneDeep(targetKeys)

		selected.map(i => {
			if (tmp.indexOf(i) === -1) {
				tmp.push(i)
			}
		})
		setTargetKeys(tmp)
	}

	//监听输入的值，更新左边的表
	useEffect(() => {
		let tmpAppids = searchValue.trim().split(/\n|\r|,|;|:|\||\s/).filter(j => { if (j != "") { return j } });
		let targetAppids = []
		tmpAppids.map(item => {
			let t = /^\d{10}$/.exec(item)
			if (t && !targetAppids.includes(t[0])) {
				targetAppids.push(t[0])
			}
		})
		if (targetAppids.length > 0) {
			getCustomerInfo(targetAppids.map(i => Number(i)), 0, targetAppids.length)
		}
	}, [searchValue])

	//更新选择的值，回传给父组件
	useEffect(() => {
		onPickDataChange(searchValue, targetRecords)
	}, [targetKeys, targetRecords]);

	useEffect(() => {
		setSearchValue(allSearchValue);
	}, []);

	//监听选择的值，更新右边的表
	useEffect(() => {
		//原有数据增删、加上已选数据
		let tmpRecords = []
		let tmpAppids = []

		let onRecords = targetRecords.filter(i => { return targetKeys.includes(i.AppID.toString()) })
		onRecords.map(i => {
			tmpRecords.push(i)
			tmpAppids.push(i.AppID)
		})

		let selectRecords = sourceRecords.filter(i => { return targetKeys.includes(i.AppID.toString()) && tmpAppids.indexOf(i.AppID) == -1; }) // && alreadyKeys.indexOf(i.AppID) == -1; 
		selectRecords.map(i => {
			tmpRecords.push(i)
		})

		setTargetRecords(tmpRecords)
	}, [targetKeys])

	return (
		<Transfer
			header={<></>}
			leftCell={
				<Transfer.Cell
					scrollable={false}
					title="选择APPID"
					tip="建议每次搜索不超过20个账号"
					header={
						<SearchBox
							placeholder='输入APPID，以空格或换行分隔'
							value={searchValue}
							onChange={(value) => {
								setSearchValue(value);
							}}
						/>
					}
				>
					<SourceTable
						dataSource={sourceRecords}
						targetKeys={targetKeys}
						loading={loading}
						onChange={(keys) => {
							updateSelectedData(keys)
						}}
					/>
				</Transfer.Cell>
			}
			rightCell={
				<Transfer.Cell title={`已选择 (${targetKeys.length})`}>
					<TargetTable
						dataSource={targetRecords}
						onRemove={key => setTargetKeys(targetKeys.filter(i => i !== key))}
					/>
				</Transfer.Cell>
			}
		/>
	);
}

export default PrivilegeRoleEditor