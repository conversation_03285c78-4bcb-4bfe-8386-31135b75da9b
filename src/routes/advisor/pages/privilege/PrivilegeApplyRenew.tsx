import React, { useState, useEffect, useMemo } from 'react';
import { Table, Button, Card, Layout, message as tips, Text, Tag, StatusTip, Status, Form, Select, Bubble, Input } from '@tencent/tea-component';
import { numberMap2Options } from '@src/types/privilege/privilege';
const { Body, Content } = Layout;
import { map, isEmpty } from "lodash";
import { Filter } from '@src/types/advisor/guard';
import { ApprovalDaysDict, UserRoleRelatedInfo } from '@src/types/privilege/privilege';
import { getUserInfo } from '@src/api/common';
import { getApproveUserRules, getUserRoles } from '@src/api/privilege/privilege';
import { useHistory } from '@tea/app';
import { columnsResizable } from '@tencent/tea-component/lib/table/addons';
import { createRenewUserRules } from '@src/api/privilege/privilege';

export function PrivilegeApplyRenew(match) {

    const applyId = match.match.params.applyid || '';

    const [loading, setLoading] = useState<boolean>(true)

    //当前登录rtx
    const [rtx, setRtx] = useState<string>('')

    //个人申请列表
    const [applyRecord, setApplyRecord] = useState<any>({})
    //个人申请角色信息
    const [applyRoleData, setApplyRoleData] = useState<Array<UserRoleRelatedInfo>>([])

    //系统角色映射
    const [systemRoleDict, setSystemRoleDict] = useState<Map<string, string>>(new Map())

    const history = useHistory();

    //选择申请天数
    const [applyDays, setApplyDays] = useState<number>(7);
    //申请原因
    const [reason, setReason] = useState<string>('');
    //申请原因必填提示
    const [visibleReason, setVisibleReason] = useState<boolean>(false);

    //获取当前登录账号rtx
    const getCurrentOperator = async () => {
        try {
            const res = await getUserInfo()
            setRtx(res.data.EngName || '')
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //申请单表头
    const applyRoleColumns = [
        {
            key: 'UserRole',
            header: '岗位角色',
        },
        {
            key: 'SystemRoles',
            header: '权限',
            render: item => (<>
                {
                    item.SystemRoleSet?.map((item, index) => (<div key={index}>
                        {
                            systemRoleDict.get(item)
                            && <><Tag theme="primary" >{systemRoleDict.get(item)}</Tag><br /></>
                        }
                    </div>))
                }
            </>)
        },
        {
            key: 'CustomerInfoSet',
            header: '数据范围',
            render: item => {
                const ret = item.CustomerInfoSet?.map(r => {
                    if (r.AppId === 0) return '--\n';
                    return r.AppId + " | " + r.CustomerName + "\n"
                })
                return ret.length == 0 ? "--" : <Text>{ret}</Text>
            }
        },
    ]

    //查询我的申请列表
    const getApprovals = async () => {
        if (!rtx) return;
        setLoading(true)
        try {
            let filters: Array<Filter> = [
                { Name: 'applyId', Values: [match.match.params.applyid] },
            ]
            const res = await getApproveUserRules({
                Name: rtx,
                Filters: filters,
            })
            setLoading(false)
            if (res.Error) {
                tips.error({ content: res.Error.Message });
                return
            } else {
                if (res.ApproveUserRulesSet == undefined || res.ApproveUserRulesSet.length < 1) {
                    tips.error({ content: "查询申请记录为空" });
                    return
                }
                setApplyRecord(res.ApproveUserRulesSet[0]);

                setApplyRoleData(res.ApproveUserRulesSet[0].UserRoleRelatedInfoSet);
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //查询权限列表
    const getRoleRecords = async () => {
        try {
            const res = await getUserRoles({})

            if (res.Error) {
                tips.error({ content: res.Error.Message });
                return
            } else {
                //获取系统角色映射关系
                let tmpSystemRoleDict = new Map()
                res.SystemRoleSet.map(i => {
                    map(i.SystemRoles, item => {
                        tmpSystemRoleDict.set(item.Name, item.CnName)
                    })
                })
                setSystemRoleDict(tmpSystemRoleDict)
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }

    //页面加载初始化
    useEffect(() => {
        getCurrentOperator();
        getRoleRecords();
    }, [])

    //页面加载默认记录
    useEffect(() => {
        getApprovals();
    }, [rtx])

    const handleApplyRenew = async () => {
        if (!reason.trim()) {
            setVisibleReason(true)
            return
        }
        try {
            const params = {
                Name: rtx,
                DurationTime: applyDays,
                ApplyReason: reason,
                ApplyId: +applyId
            }
            const res = await createRenewUserRules(params)
            if (res.Error) {
                tips.error({ content: res.Error.Message });
                return
            } else {
                tips.success({ content: "权限续期提交成功！" })
                history.push('/advisor/privilege/my')
            }
        } catch (err) {
            const msg = err.msg || err.toString() || "未知错误"
            tips.error({ content: msg });
        }
    }
    return (
        <Body>
            <Content>
                <Content.Header
                    showBackButton
                    onBackButtonClick={() => history.push('/advisor/privilege/my')}
                    title={`【权限续期】${applyId} | ${applyRecord?.Title}`}
                ></Content.Header>
                {
                    loading
                        ? <Status icon="loading" size="m" title={<><Button icon='loading' />数据正在加载中...</>} />
                        : <>
                            <Content.Body>
                                <Card>
                                    <Card.Body>
                                        <Table
                                            columns={applyRoleColumns}
                                            records={applyRoleData}
                                            bordered={true}
                                            topTip={loading ? <StatusTip status="loading" /> : isEmpty(applyRoleData) && <StatusTip status="empty" />}
                                            recordKey="UserRole"
                                            style={{ marginBottom: 20 }}
                                            addons={[
                                                columnsResizable({
                                                    onResizeEnd: () => { },
                                                    minWidth: 48,
                                                    maxWidth: 48,
                                                }),
                                            ]}
                                        />
                                        <Form>
                                            <Form.Item label="申请时长" required>
                                                <Select
                                                    options={numberMap2Options(ApprovalDaysDict)}
                                                    value={applyDays.toString()}
                                                    onChange={(v) => { setApplyDays(Number(v)) }}
                                                    appearance="button"
                                                    searchable
                                                    size="m"
                                                />
                                            </Form.Item>
                                            <Form.Item label="申请原因" required>
                                                <Bubble error placement="top" visible={visibleReason} content="申请原因不能为空" >
                                                    <Input value={reason} onChange={v => { setReason(v); setVisibleReason(!v.trim()) }} size="l" />
                                                </Bubble>
                                            </Form.Item>
                                        </Form>
                                        <div className='apply-editor-box'>
                                            <Button type="primary" className='apply-editor-box-btn' onClick={handleApplyRenew}>
                                                提交申请
                                            </Button>
                                        </div>
                                    </Card.Body>
                                </Card>
                            </Content.Body>
                        </>
                }
            </Content>
        </Body >
    );
}