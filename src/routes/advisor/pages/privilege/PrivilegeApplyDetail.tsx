import React, { useState, useEffect, useMemo } from 'react';
import { Table, Button, Card, Layout, message as tips, Text, Tag, StatusTip, Status, Icon, List, Timeline } from '@tencent/tea-component';
import { Row, Col } from "@tencent/tea-component";
const { Body, Content } = Layout;
import { map, isEmpty } from "lodash";
import { Filter } from '@src/types/advisor/guard';
import { ApprovalDaysDict, ApprovalStatusDict, UserRoleRelatedInfo } from '@src/types/privilege/privilege';
import { getUserInfo } from '@src/api/common';
import { getApproveUserRules, getUserRoles } from '@src/api/privilege/privilege';
import { useHistory } from '@tea/app';
import { columnsResizable } from '@tencent/tea-component/lib/table/addons';

const applyBaseLabel = ['申请人', '申请时长', '申请原因', '申请日期', '当前审批人', '审批状态'];

export function PrivilegeApplyDetail(match) {

	const applyId = match.match.params.applyid || '';

	const [loading, setLoading] = useState<boolean>(true)

	//当前登录rtx
	const [rtx, setRtx] = useState<string>('')

	//个人申请列表
	const [applyRecord, setApplyRecord] = useState<any>({})
	//个人申请角色信息
	const [applyRoleData, setApplyRoleData] = useState<Array<UserRoleRelatedInfo>>([])

	//系统角色映射
	const [systemRoleDict, setSystemRoleDict] = useState<Map<string, string>>(new Map())

	const history = useHistory();

	//获取当前登录账号rtx
	const getCurrentOperator = async () => {
		try {
			const res = await getUserInfo()
			setRtx(res.data.EngName || '')
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	//申请单表头
	const applyRoleColumns = [
		{
			key: 'UserRole',
			header: '岗位角色',
		},
		{
			key: 'SystemRoles',
			header: '权限',
			render: item => (<>
				{
					item.SystemRoleSet?.map((item, index) => (<div key={index}>
						{
							systemRoleDict.get(item)
							&& <><Tag theme="primary" >{systemRoleDict.get(item)}</Tag><br /></>
						}
					</div>))
				}
			</>)
		},
		{
			key: 'CustomerInfoSet',
			header: '数据范围',
			render: item => {
				const ret = item.CustomerInfoSet?.map(r => {
					if (r.AppId === 0) return '--\n';
					return r.AppId + " | " + r.CustomerName + "\n"
				})
				return ret.length == 0 ? "--" : <Text>{ret}</Text>
			}
		},
	]

	//查询我的申请列表
	const getApprovals = async () => {
		if (!rtx) return;
		setLoading(true)
		try {
			let filters: Array<Filter> = [
				{ Name: 'applyId', Values: [match.match.params.applyid] },
			]
			const res = await getApproveUserRules({
				Name: rtx,
				Filters: filters,
			})
			setLoading(false)
			if (res.Error) {
				tips.error({ content: res.Error.Message });
				return
			} else {
				if (res.ApproveUserRulesSet == undefined || res.ApproveUserRulesSet.length < 1) {
					tips.error({ content: "查询申请记录为空" });
					return
				}
				setApplyRecord(res.ApproveUserRulesSet[0]);

				setApplyRoleData(res.ApproveUserRulesSet[0].UserRoleRelatedInfoSet);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	//查询权限列表
	const getRoleRecords = async () => {
		try {
			const res = await getUserRoles({})

			if (res.Error) {
				tips.error({ content: res.Error.Message });
				return
			} else {
				//获取系统角色映射关系
				let tmpSystemRoleDict = new Map()
				res.SystemRoleSet.map(i => {
					map(i.SystemRoles, item => {
						tmpSystemRoleDict.set(item.Name, item.CnName)
					})
				})
				setSystemRoleDict(tmpSystemRoleDict)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			tips.error({ content: msg });
		}
	}

	//页面加载初始化
	useEffect(() => {
		getCurrentOperator();
		getRoleRecords();
	}, [])

	//页面加载默认记录
	useEffect(() => {
		getApprovals();
	}, [rtx])

	const theme = useMemo(() => {
		return applyRecord.Status === 'finish' ? 'success' : applyRecord.Status === 'approving' ? 'warning' : 'danger';
	}, [applyRecord.Status])

	const StatusComp = () => {
		return (<div style={{ display: 'flex', alignItems: 'center' }}>
			<Icon type={theme === 'danger' ? 'error' : theme} />
			<span style={{ marginLeft: '3px' }}>
				<Text theme={theme}>{ApprovalStatusDict.get(applyRecord.Status)}</Text>
			</span>
		</div>)
	}
	return (
		<Body>
			<Content>
				<Content.Header
					showBackButton
					onBackButtonClick={() => history.push('/advisor/privilege/my')}
					title={`【权限申请详情】${applyId} | ${applyRecord?.Title}`}
				></Content.Header>
				{
					loading
						? <Status icon="loading" size="m" title={<><Button icon='loading' />数据正在加载中...</>} />
						: <Content.Body>
							<Card>
								<Card.Body>
									<Table
										columns={applyRoleColumns}
										records={applyRoleData}
										bordered={true}
										topTip={loading ? <StatusTip status="loading" /> : isEmpty(applyRoleData) && <StatusTip status="empty" />}
										recordKey="UserRole"
										addons={[
											columnsResizable({
												onResizeEnd: () => { },
												minWidth: 48,
												maxWidth: 48,
											}),
										]}
									/>
									<div style={{ display: 'flex' }}>
										<div className='intlc-broadcast__inner'>
											<div className='intlc-broadcast__header'>
												<h3>申请信息</h3>
											</div>
											<div className='intlc-broadcast__body'>
												<Row>
													<Col span={8}>
														<List>
															{applyBaseLabel.map((item, index) => (
																<List.Item key={index}>
																	<Text theme="label">{item}</Text>
																</List.Item>))}
														</List>
													</Col>
													<Col span={16}>
														<List>
															<List.Item>{applyRecord.Name}</List.Item>
															<List.Item>{ApprovalDaysDict.get(applyRecord.DurationTime) || '--'}</List.Item>
															<List.Item>{applyRecord.ApplyReason || '--'}</List.Item>
															<List.Item>{applyRecord.CreateTime || '--'}</List.Item>
															<List.Item>{applyRecord.CurrentApprover || '--'}</List.Item>
															<List.Item>{StatusComp()}</List.Item>
														</List>
													</Col>
												</Row>
											</div>
										</div>
										<div className='intlc-broadcast__inner'>
											<div className='intlc-broadcast__header'>
												<h3>申请流程</h3>
											</div>
											<div className='intlc-broadcast__body'>
												<Timeline mode="vertical" dashedLine>
													<Timeline.Item theme="success" label={applyRecord.CreateTime} title="发起人">
														{applyRecord.Name}
													</Timeline.Item>
													<Timeline.Item
														label={theme === 'warning'
															? '审批中'
															: theme === 'success'
																? `${applyRecord.UpdateTime} 已完成`
																: '申请失败'}
														title="审批人"
														icon={theme === 'warning'
															? <Icon type="loading" />
															: theme === 'success'
																? <Icon type="success" />
																: <Icon type="error" />}
														theme="default"
													>
														{applyRecord.CurrentApprover}
													</Timeline.Item>
												</Timeline>
											</div>
										</div>
									</div>
								</Card.Body>
							</Card>
						</Content.Body>
				}
			</Content>
		</Body >
	);
}