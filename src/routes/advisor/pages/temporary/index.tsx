import React from 'react';
import { Link } from "react-router-dom";
import './index.less';
export function Transfer({match}) {
	const type = match.params.type;
	return (
		<div className={'temporary-wrap'}>
			<div className="con">
				{
					type == 'my-customer' || type == 'overviewindex'
					?
						<>
							迁移至：<Link to={'/advisor/data-screening/recommend'}>运营端数据总览</Link>
						</>
						:
						type == 'strategies-manage'
					?
							<>
								迁移至：配置管理&nbsp;{">"}&nbsp;<Link to={'/advisor/strategies-manage'}>云巡检策略管理</Link>
							</>
							:
							<></>
				}
			</div>
		</div>
	);
}
