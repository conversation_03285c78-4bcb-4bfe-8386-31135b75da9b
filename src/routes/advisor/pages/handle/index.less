.advisor-handle-wrap {
  .pie-area {
    position: relative;
    .line {
      height: 91%;
      border-right: thin solid #E2E2E2;
      position: absolute;
      right: -90px;
      top: 7%;
    }
    .pie-shape {
      width: 100%;
      position: relative;
      .pie-percent {
        position: absolute;
        left: -4%;
        top: 50%;
        margin-top: -18px;
        width: 100%;
        text-align: center;
        .num {
          font-size: 24px;
        }
        .percent {
          font-size: 14px;
        }
      }
    }
  }
  .pie-desc-area {
    margin-left: 150px;
    display: flex;
    align-items: center;
    .tea-grid__box {
      flex: 1 0 auto;
      height: auto;
    }
  }
  .tea-segment {
    padding: 16px 0 22px 0;
  }
  .tea-tabs.tea-tabs--ceiling {
    margin-top: 10px;
    position: relative;
  } 
  .tea-tabs.tea-tabs--ceiling > .tea-tabs__tabbar {
    //left: 20px;
    padding-left: 0;
  }
  .pie-col {
    width: 230px;
    flex-grow: inherit;
    margin-left: 25px;
  }
  .trend-wrap {
    display: inline-flex;
    align-items: center;
    white-space: nowrap;
    margin-top: 5px;
  }
  .trend-wrap-en {
    white-space: normal;
  }
  .pie-loading-wrap {
    height: 160px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .bubble-desc-wrap {
    display: flex;
    align-items: center;
  }
	.strategy-name {
		display: flex;
		align-items: flex-start;
		.tea-tag {
			margin: 0 0 0 5px;
			cursor: pointer;
		}
	}
	.follow-tag {
		border-radius: 10px;
	}
	.follow-tag span {
		white-space: nowrap;
	}
	.risk-tag {
		margin-top: 0;
		span {
			display: flex;
			align-items: center;
		}
	}
}
.handle-modal-desc {
  padding-bottom: 30px;
}
@media screen and (max-width: 1550px) {
  .advisor-handle-wrap .pie-col {
    width: 170px;
  }
  .advisor-handle-wrap .pie-desc-area {
    margin-left: 110px;
  }
  .advisor-handle-wrap .pie-area .line {
    right: -70px;
  }
}
@media screen and (max-width: 1300px) {
  .advisor-handle-wrap .pie-col {
    width: 170px;
  }
  .advisor-handle-wrap .pie-desc-area {
    margin-left: 90px;
  }
  .advisor-handle-wrap .pie-area .line {
    right: -70px;
  }
}
@media screen and (max-width: 1200px) {
  .advisor-handle-wrap .pie-col {
    width: 170px;
  }
  .advisor-handle-wrap .pie-desc-area {
    margin-left: 100px;
  }
  .advisor-handle-wrap .pie-area .line {
    right: -70px;
  }
}

.hanle-modal-wrap {
	.handle-form {
		width: 100%;
		.tea-form__label {
			display: none;
		}
		.tea-form__controls {
			width: 100%;
			padding-right: 0;
		}
	}
	.title-required {
		&:after {
			display: inline-block;
			content: "*";
			color: #e54545;
			line-height: 1;
			position: absolute;
			margin: 5px 0 0 3px;
		}
	}
	.tea-form-check {
		margin-right: 0;
	}
	.tea-form-check__label {
		margin-left: 5px;
		color: #006eff;
	}
	.tea-form-check__label:before {
		display: inline-block;
		content: "*";
		color: #e54545;
		line-height: 1;
		position: absolute;
		left: 15px;
		margin: 5px 0 0 3px;
	}
	.tea-dialog__inner {
		width: 620px;
	}
	.intlc-assessment-tabcoll__conditions {
		display: flex;
		.intlc-assessment-tabs-tag__block {
			height: 0;
			padding: 4px;
		}
	}
	.tea-form__controls .tea-icon-valid {
		margin-left: 5px;
	}
}