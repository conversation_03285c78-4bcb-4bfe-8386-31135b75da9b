import React, { useState, useMemo, useRef } from 'react';
import {
	Layout,
	Card,
	H3,
	Row,
	Col,
	Segment,
	MetricsBoard,
	Text,
	Tabs,
	TabPanel,
	StatusTip,
	Bubble
} from '@tea/component';
import { BasicPie } from '@tencent/tea-chart/lib/basicpie';
import RiskTab from './RiskTab';
import RecommendRiskTab from './RecommendRiskTab';
import { HandleModal } from "@src/routes/advisor/pages/handle/HandleModal";
import { DescribeRiskOverview } from "@src/api/advisor/handle";
import {
	DescribeRiskOverviewRes
} from "@src/types/advisor/handle";
import { reportVisitPage } from '@src/utils/report';
import { getDescribeProductList } from "@src/api/advisor/faultNotification";
import './index.less';
import { getStorage } from "@src/utils/storage";

const { Body, Content } = Layout;

const options = [
	{
		text: '近 30 天',
		value: '30',
	},
	{
		text: '近 60 天',
		value: '60',
	},
	{
		text: '近 90 天',
		value: '90',
	},
	{
		text: '近半年',
		value: '180',
	},
	{
		text: '近一年',
		value: '365',
	}
];


export function Handle(match) {
	const customerName = getStorage('handle_customer_name');
	const id = match.match.params.id;
	const uin = match.match.params.uin;
	const [isInitLoading, setIsInitLoading] = useState(true);
	const [viewLoading, setViewLoading] = useState(true);
	const [activeId, setActiveId] = useState('1');
	const [segmentValue, setSegmentValue] = useState('30');
	const tabs = [
		{ id: "1", label: '当前风险' },
		{ id: "3", label: '可推荐风险' },
		{ id: "2", label: '历史风险记录' }
	];
	const [products, setProducts] = useState([]);
	const [overviewInfo, setOverviewInfo] = useState<DescribeRiskOverviewRes>({
		RecommendTime: '',
		Total: 0,
		TotalSolved: 0,
		Progress: 0,
		Todo: 0,
		TodoRatio: 0,
		Doing: 0,
		DoingRatio: 0,
		History: {
			Done: 0,
			Pending: 0,
			Reject: 0,
			TimeCost: 0,
		},
		IsRiskRecommendSupport: false
	});

	// 当前进展和历史概览
	const getDescribeRiskOverview = async (Duration) => {
		setViewLoading(true);
		try {
			const result = await DescribeRiskOverview({
				AppId: Number(id),
				Duration,
				OnlyData: true,
				ShowError: true
			});
			setOverviewInfo(result);
			setIsInitLoading(false);
			setViewLoading(false);
		} catch (err) {
			setViewLoading(false);
		}
	};
	// 获取产品和维度信息
	const getProductsGroupsInfo = async () => {
		try {
			const res = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742
			});
			const tmp = [];
			for (const key in res.ProductDict) {
				tmp.push({ key, name: res.ProductDict[key] });
			}
			setProducts(tmp);
		} catch (err) {}
	};
	useMemo(()=>{
		getDescribeRiskOverview(parseInt(segmentValue));
	}, [segmentValue]);
	useMemo(()=>{
		getProductsGroupsInfo();
		reportVisitPage({
			isaReportMeunName: '风险治理',
		});
	}, []);
	const [visible, setVisible]  = useState(false);
	const [configInfo, setConfigInfo] = useState<any>({
		type: 1
	});
	const ref1 = useRef<any>();
	const ref3 = useRef<any>();
	return <Body className={'advisor-handle-wrap'}>
		<Content>
			<Content.Header
				title={'风险治理'}
				subtitle={
					!isInitLoading
					&&
					<Text theme={'primary'}>
						APPID：
						{
							id
						}
						&nbsp;
						&nbsp;
						客户名称：
						{
							customerName
						}
						&nbsp;
						&nbsp;
							最近推荐时间：{
							overviewInfo.RecommendTime || '--'
						}（每周一更新）
					</Text>
				}
			/>
			<Content.Body>
				<Row>
					<Col span={12}>
						<Card>
							<Card.Body>
								<H3>
									{
										'当前进展'
									}
								</H3>
								<Row>
									<Col className={'pie-col'}>
										<div className={'pie-area'} style={
											{
												position: 'relative'
											}
										}>
											{
												isInitLoading && viewLoading
													?
													<div className={'pie-loading-wrap'}>
														<StatusTip status={'loading'} />
													</div>
													:
													<div className={'pie-shape'}>
														<BasicPie
															circle
															height={160}
															dataSource={[
																{ type: '已解决', value: overviewInfo.TotalSolved },
																{ type: '未解决', value: overviewInfo.Total == 0 ? 1 : overviewInfo.Total - overviewInfo.TotalSolved },
															]}
															position="value"
															color="type"
															legend={
																{
																	enable: false,
																	align: 'right'
																}
															}
														/>
														<div className={'pie-percent'}>
															<Text className={'num'}>{isInitLoading ? '-' : overviewInfo.Progress}</Text>
															<Text className={'percent'}>%</Text>
														</div>
													</div>
											}
											<div style={
												{
													fontSize: '14px'
												}
											}>
												当前累计有&nbsp;<Text theme={'success'}>{
												isInitLoading ? '--' : overviewInfo.Total
											}</Text>&nbsp;项风险加入治理，已解决&nbsp;<Text theme={'success'}>{
												isInitLoading ? '--' : overviewInfo.TotalSolved
											}</Text>&nbsp;项，完成进度:&nbsp;<Text theme={'primary'}>{
												isInitLoading ? '--' : overviewInfo.Progress
											}%</Text>
											</div>
											<div className={'line'}></div>
										</div>
									</Col>
									<Col className={'pie-desc-area'}>
										<Row>
											<Col span={12}>
												<MetricsBoard
													title={'待处理'}
													value={<Text theme={"danger"}>{isInitLoading ? '-' : overviewInfo.Todo}</Text>}
													unit={'个风险项'}
													infos={
														[
															<div className={`trend-wrap`}>
																比较上周
																{
																	overviewInfo.TodoRatio > 0
																		?
																		<svg xmlns="http://www.w3.org/2000/svg"
																			 className="icon icon-tabler icon-tabler-arrow-narrow-up"
																			 width="16" height="16" viewBox="0 0 24 24"
																			 strokeWidth="2" stroke="#e54545"
																			 fill="none" strokeLinecap="round"
																			 strokeLinejoin="round">
																			<path stroke="none" d="M0 0h24v24H0z"
																				  fill="none" />
																			<line x1="12" y1="5" x2="12" y2="19" />
																			<line x1="16" y1="9" x2="12" y2="5" />
																			<line x1="8" y1="9" x2="12" y2="5" />
																		</svg>
																		:
																		<svg xmlns="http://www.w3.org/2000/svg"
																			 className="icon icon-tabler icon-tabler-arrow-narrow-down"
																			 width="16" height="16" viewBox="0 0 24 24"
																			 strokeWidth="2" stroke="#0abf5b"
																			 fill="none" strokeLinecap="round"
																			 strokeLinejoin="round">
																			<path stroke="none" d="M0 0h24v24H0z"
																				  fill="none" />
																			<line x1="12" y1="5" x2="12" y2="19" />
																			<line x1="16" y1="15" x2="12" y2="19" />
																			<line x1="8" y1="15" x2="12" y2="19" />
																		</svg>
																}
																{
																	isInitLoading ? '--' : (Math.abs(overviewInfo.TodoRatio || 0))
																}
															</div>
														]
													}
												/>
											</Col>
											<Col>
												<MetricsBoard
													title={'处理中'}
													value={<Text theme={'success'}>{isInitLoading ? '-' : overviewInfo.Doing}</Text>}
													unit={'个风险项'}
													infos={
														[
															<div className={`trend-wrap`}>
																比较上周
																{
																	overviewInfo.DoingRatio > 0
																		?
																		<svg xmlns="http://www.w3.org/2000/svg"
																			 className="icon icon-tabler icon-tabler-arrow-narrow-up"
																			 width="16" height="16" viewBox="0 0 24 24"
																			 strokeWidth="2" stroke="#e54545"
																			 fill="none" strokeLinecap="round"
																			 strokeLinejoin="round">
																			<path stroke="none" d="M0 0h24v24H0z"
																				  fill="none" />
																			<line x1="12" y1="5" x2="12" y2="19" />
																			<line x1="16" y1="9" x2="12" y2="5" />
																			<line x1="8" y1="9" x2="12" y2="5" />
																		</svg>
																		:
																		<svg xmlns="http://www.w3.org/2000/svg"
																			 className="icon icon-tabler icon-tabler-arrow-narrow-down"
																			 width="16" height="16" viewBox="0 0 24 24"
																			 strokeWidth="2" stroke="#0abf5b"
																			 fill="none" strokeLinecap="round"
																			 strokeLinejoin="round">
																			<path stroke="none" d="M0 0h24v24H0z"
																				  fill="none" />
																			<line x1="12" y1="5" x2="12" y2="19" />
																			<line x1="16" y1="15" x2="12" y2="19" />
																			<line x1="8" y1="15" x2="12" y2="19" />
																		</svg>
																}
																{
																	isInitLoading ? '--' : (Math.abs(overviewInfo.DoingRatio) || 0)
																}
															</div>
														]
													}
												/>
											</Col>
										</Row>
									</Col>
								</Row>
							</Card.Body>
						</Card>
					</Col>
					<Col span={12}>
						<Card style={
							{
								height: '100%'
							}
						}>
							<Card.Body>
								<H3>
									{
										'历史概览'
									}
								</H3>
								<Segment
									value={segmentValue}
									options={options}
									onChange={(value) => {
										setSegmentValue(value);
										// setPickerValue(rangeMap[value]);
									}
									}
								/>
								<Row showSplitLine>
									<Col span={6}>
										<MetricsBoard
											title={
												<div className={'bubble-desc-wrap'}>
													{
														'已解决'
													}
													{
														segmentValue == '365'
														&&
														<Bubble
															arrowPointAtCenter
															placement="top"
															content={'包含1年内开通风险治理前已解决的风险'}
														>
															<svg
																style={
																	{
																		marginLeft: '3px',
																		cursor: 'pointer'
																	}
																}
																xmlns="http://www.w3.org/2000/svg"
																width="16"
																height="16"
																viewBox="0 0 24 24"
																fill="none"
															>
																<g id="ç³»ç»å¸®å©-System-help">
																	<path id="stroke3 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3ZM1 12C1 5.92487 5.92487 1 12 1C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12ZM12 8.55556C11.325 8.55556 10.7778 9.10276 10.7778 9.77778V10.7778H8.77778V9.77778C8.77778 7.99819 10.2204 6.55556 12 6.55556C13.7796 6.55556 15.2222 7.99819 15.2222 9.77778C15.2222 10.65 14.8744 11.4429 14.3121 12.0221L13 13.398V14.1111H11V12.5972L12.8755 10.6306C13.091 10.4093 13.2222 10.1099 13.2222 9.77778C13.2222 9.10276 12.675 8.55556 12 8.55556ZM11 15.4444H13.0043V17.4488H11V15.4444Z" fill="black" fill-opacity="0.4"/>
																</g>
															</svg>
														</Bubble>
													}
												</div>
											}
											value={isInitLoading || viewLoading ? '-' : overviewInfo?.History?.Done}
											unit={'个风险项'}
										/>
									</Col>
									<Col span={6}>
										<MetricsBoard
											title={'已挂起'}
											value={isInitLoading || viewLoading ? '-' : overviewInfo?.History?.Pending}
											unit={'个风险项'}
										/>
									</Col>
									<Col span={6}>
										<MetricsBoard
											title={'已拒绝'}
											value={isInitLoading || viewLoading ? '-' : overviewInfo?.History?.Reject}
											unit={'个风险项'}
										/>
									</Col>
									<Col span={6}>
										<MetricsBoard
											title={'平均处理时长'}
											value={isInitLoading || viewLoading ? '-' : overviewInfo?.History?.TimeCost}
											unit={'天'}
										/>
									</Col>
								</Row>
							</Card.Body>
						</Card>
					</Col>
				</Row>
				<Card style={
					{
						marginTop: '20px'
					}
				}>
					<Card.Body>
						<H3>
							{
								'风险治理列表'
							}
						</H3>
						<Tabs
							ceiling
							animated={false}
							tabs={tabs}
							destroyInactiveTabPanel={false}
							activeId={activeId}
							onActive={(tab)=>{
								setActiveId(tab.id);
							}}
						>
							{
								tabs.map((item)=>{
									return <TabPanel id={item.id} key={item.id}>
										{
											item.id == '3'
											?
												<RecommendRiskTab
													type={1}
													products={products}
													hadle = {
														(type, item)=>{
															setVisible(true);
															setConfigInfo({
																type,
																id: item.Id,
																objectType: item.ObjectType,
																recommendId: item.RecommendId || ''
															});
														}
													}
													ref={ref3}
													id={id}
												/>
												:
												<RiskTab
													ref={item.id == '1' ? ref1 : null}
													type={parseInt(item.id)}
													products={products}
													hadle = {
														(type, item)=>{
															setVisible(true);
															setConfigInfo({
																type,
																id: item.Id,
																objectType: item.ObjectType,
																recommendId: item.RecommendId || ''
															});
														}
													}
													id={id}
												/>
										}
									</TabPanel>;
								})
							}
						</Tabs>
					</Card.Body>
				</Card>
			</Content.Body>
		</Content>
		{
			<HandleModal
				visible={visible}
				handleVisible={
					(type)=>{
						if (type) {
							if (type == 1 || type == 2) {
								ref1.current.getDescribeRiskLists();
							} else if (type == 3) {
								ref3.current.getDescribeToRecommendRiskLists();
							}
						}
						setVisible(false);
					}
				}
				configInfo={configInfo}
				id={id}
				uin={uin}
			/>
		}
	</Body>;
};
