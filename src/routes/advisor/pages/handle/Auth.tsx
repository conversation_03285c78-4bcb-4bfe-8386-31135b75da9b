import React, { useState } from 'react';
import { Card, Layout, Form, Button, Input } from '@tencent/tea-component';
import { useForm, useField } from 'react-final-form-hooks';
import { DescribeRiskAuthorization, getSummaryMsg, getCustomerName } from "@src/api/advisor/handle";
import { message } from "@tea/component/message";
import { getStorage, setStorage } from "@src/utils/storage";
const { Body, Content } = Layout;

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return 'validating';
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? 'error' : 'success';
}

export function Auth({ history, location }) {
	const onSubmit = async (values) => {
		try {
			const customerNameData = await getCustomerName({
				AppId: Number(values.appid),
				ShowError: true,
				OnlyData: true
			});
			setStorage('handle_customer_name', customerNameData.CustomerName);
			if (!customerNameData.IsValid) {
				message.error({
					content: '用户不存在'
				});
				return;
			}
			// const summaryData = await getSummaryMsg({
			// 	AppId: Number(values.appid),
			// 	ShowError: true,
			// 	OnlyData: true
			// });
			// if (summaryData.IsAuthorized == 2) {
			// 	message.error({
			// 		content: '用户未开通云顾问授权！'
			// 	});
			// 	return;
			// }
			// if (summaryData.IsAuthorized == 3) {
			// 	message.error({
			// 		content: '客户未开通云顾问“报告解读”，可以引导客户在云顾问控制台【服务授权】页面开通！'
			// 	});
			// 	return;
			// }
			await DescribeRiskAuthorization({
				AppId: Number(values.appid),
				Name: getStorage('engName'),
				ShowError: true,
				OnlyData: true
			});
			history.push({
				pathname: `/advisor/handle/${values.appid}/${customerNameData.Uin}`,
			});
		} catch (err) {}
	};
	const { form, handleSubmit, validating, submitting } = useForm({
		onSubmit,
		/**
		 * 默认为 shallowEqual
		 * 如果初始值有多层，会导致重渲染，也可以使用 `useEffect` 设置初始值：
		 * useEffect(() => form.initialize({ }), []);
		 */
		initialValuesEqual: () => true,
		initialValues: {
			uin: (location.state && location.state.uin) || '',
			appid: (location.state && location.state.appid) || '',
		},
		validate: ({ uin, appid }) => ({
			appid: !appid.trim() ? '请输入AppId' : undefined,
		}),
	});
	const appid = useField('appid', form);
	return (
		<Body>
			<Content>
				<Content.Header title="评估结果"></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body>
							<div className="empty-card-body">
								<form onSubmit={handleSubmit} className="assess-search-form">
									<Form layout="inline">
										{/* <Form.Item
                                            status={getStatus(uin.meta, validating)}
                                            className="item"
                                            label="UIN"
                                        >
                                            <Input
                                                size="m"
                                                placeholder="输入UIN"
                                                {...uin.input}
                                            />
                                        </Form.Item> */}
										<Form.Item
											status={getStatus(appid.meta, validating)}
											className="item"
											label="AppId"
										>
											<Input size="m" placeholder="请输入Appid" {...appid.input} />
										</Form.Item>
									</Form>
									<Form.Action className="action">
										<Button
											type="primary"
											htmlType="submit"
											loading={submitting}
											disabled={validating}
										>
											查询
										</Button>
									</Form.Action>
								</form>
							</div>
						</Card.Body>
					</Card>
				</Content.Body>
			</Content>
		</Body>
	);
}
