import React, { useMemo, useState } from 'react';
import {
	Modal,
	Form,
	Button,
	Input,
	StatusTip,
	Text,
	Checkbox,
	message
} from '@tencent/tea-component';
import { getStorage } from "@src/utils/storage";
import { DescribeTaskSummaryV2, CreateRiskRecommend, DescribeRiskManageStrategyDetail } from "@src/api/advisor/handle";
import {
	getDescribeEventDetail
} from '@src/api/advisor/commonEvent';
import { useField, useForm } from "react-final-form-hooks";

// 把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
const simpleMarkdownToHTML = (input: string): string => {
	/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
	const reg = new RegExp(
		'(\\[[\u4e00-\u9fa5_a-zA-Z0-9,、/-\\s]+\\])(\\((\\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\\))',
		'g'
	);
	/* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */
	let result = input.replace(reg, (match, text, link) => `<a href="${link.slice(1, -1)}" target="_blank">${text.slice(1, -1)}</a>`);

	// 无序列表markdown语法转换
	if (result.includes('- ')) {
		result = result
			.split('- ')
			.filter(r => r.length)
			.map((r, index) => (!index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`))
			.join('');
	}
	return result;
};
function getStatus(meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}
export function HandleModal({visible, handleVisible, configInfo, id, uin}) {
	const [recommendAdvice, setRecommendAdvice] = useState('');
	const titleMap = {
		1: '添加处理建议',
		2: '提醒客户处理风险',
		3: '给客户推荐风险'
	};
	const tDsecMap = {
		1: '您可以在现有风险优化建议的基础上，补充更详细的优化建议。在您选择"提交"之后，客户会在该风险项的【风险治理详情】页面中，看到您提供的【来自售后团队的建议】，请确认您提供的内容是正确并有效的。',
		2: '您选择提醒客户优先处理该风险，在您点击"确认"提交之后，我们会在腾讯云控制台云顾问的【风险治理】提示客户优先处理该风险项',
		3: '您选择将该风险项推荐给客户。在您"确定"之后，客户可以在腾讯云控制台云顾问的【风险治理】看到您推荐的风险项并进行相应的处理。新推荐的风险项在客户【风险治理】页面的默认状态为"待处理"。您也可以在现有优化建议的基础上补充更详细的优化建议。'
	};
	// 警告条件主题颜色映射
	const conditionThemeConfig = {
		'-1': 'weak',
		0: 'success',
		1: 'warning',
		2: 'warning',
		3: 'danger',
	};
	const [loading, setLoading] = useState(false);
	const [btnLoading, setBtnLoading] = useState(false);
	const [strategy, setStrategy] = useState<any>({});
	const [lastSuccessTaskId, setLastSuccessTaskId] = useState('');
	const onSubmit = async (value)=>{
		setBtnLoading(true);
		try {
			await CreateRiskRecommend({
				AppId: Number(id),
				Name: getStorage('engName'),
				StrategyId: configInfo.id,
				ObjectType: configInfo.objectType,
				RecommendId: configInfo.recommendId,
				LastSuccessTaskId: lastSuccessTaskId,
				Type: configInfo.type,
				Advice: value.advice || '',
				OnlyData: true,
				ShowError: true
			});
			message.success({
				content: '操作成功'
			});
			handleVisible(configInfo.type);
			setBtnLoading(false);
		} catch (err) {
			setBtnLoading(false);
		}
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		validate: (formInfo) => {
			const validateFormInfo = {};
			if (configInfo?.type == 1) {
				validateFormInfo['advice'] = formInfo['advice'] ? undefined : '请填写处理建议';
				validateFormInfo['agree'] = formInfo['agree'] ? undefined : '请勾选规则';
			} else if (configInfo?.type == 2) {
				validateFormInfo['agree'] = formInfo['agree'] ? undefined : '请勾选规则';
			}
			return validateFormInfo;
		},
	});
	const adviceField = useField('advice', form);
	const agreeField = useField('agree', form);
	useMemo(async ()=>{
		if (visible) {
			if (configInfo.objectType == 'event') {
				setLoading(true);
				try {
					const res = await getDescribeEventDetail({
						AppId: Number(id),
						Name: getStorage('engName'),
						Id: configInfo.id,
						Limit: 10000,
						Offset: 0,
						Filters: [],
						ShowError: true,
						OnlyData: true
					});
					setStrategy({
						StrategyName: res.Title,
						Desc: res.PublicDesc,
						Repair: res.PublicAdvice
					});
					setLastSuccessTaskId(res.LastSuccessTaskId);
					if (configInfo?.type == 1) {
						const resDetail = await DescribeRiskManageStrategyDetail({
							AppId: Number(id),
							TaskId: res.LastSuccessTaskId,
							StrategyId: configInfo.id,
							ObjectType: configInfo.objectType,
							RecommendId: configInfo.recommendId,
							ShowError: true,
							OnlyData: true
						});
						setRecommendAdvice(resDetail.Strategy.Advice);
					}
					setLoading(false);
				} catch (err) {
					setLoading(false);
				}
			} else {
				setLoading(true);
				try {
					const res = await DescribeTaskSummaryV2({
						AppId: Number(id),
						Uin: uin,
						Filters: [
							{
								Name: 'strategyId',
								Values: [String(configInfo.id)]
							}
						],
						ShowError: true,
						OnlyData: true
					});
					setStrategy(res.StrategySummaries == null ? {} : res.StrategySummaries[0]);
					setLastSuccessTaskId(res.LastSuccessTaskId);
					if (configInfo?.type == 1) {
						const resDetail = await DescribeRiskManageStrategyDetail({
							AppId: Number(id),
							TaskId: res.LastSuccessTaskId,
							StrategyId: configInfo.id,
							ObjectType: configInfo.objectType,
							RecommendId: configInfo.recommendId,
							ShowError: true,
							OnlyData: true
						});
						setRecommendAdvice(resDetail.Strategy.Advice);
					}
					setLoading(false);
				} catch (err) {
					setLoading(false);
				}
			}
		}
	}, [visible]);
	useMemo(()=>{
		if (configInfo?.type == 1) {
			form.initialize({
				advice: recommendAdvice
			});
		}
	}, [recommendAdvice]);
	useMemo(()=>{
		form.reset();
	}, [configInfo]);
	return (
		<Modal className={'hanle-modal-wrap'} visible={visible} caption={titleMap[configInfo?.type]} onClose={
			()=>{
				handleVisible();
			}
		}>
			<form
				onSubmit={handleSubmit}
			>
			<Modal.Body>
				{
					loading
						?
						<div style={{
							minHeight: '200px',
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center'
						}}>
							<StatusTip.LoadingTip></StatusTip.LoadingTip>
						</div>
						:
						<>
							<div>
								<Text className={'intlc-assessment-tabcoll__advice'}>
									{
										tDsecMap[configInfo?.type]
									}
								</Text>
							</div>
							<div className="intlc-assessment-tabcoll__title">
								{
									strategy.StrategyName
								}
							</div>
							<div className="intlc-assessment-tabcoll__advice">
								{
									strategy.Desc
								}
							</div>
							{
								strategy?.Conditions?.length > 0 && <>
									<div className="intlc-assessment-tabcoll__title">警告条件</div>
									{strategy?.Conditions?.sort((c1, c2) => c2.Level - c1.Level)?.map(condition => (
										<div key={condition.ConditionId} className="intlc-assessment-tabcoll__conditions">
											<Text
												bgTheme={conditionThemeConfig[condition.Level.toString()]}
												verticalAlign="top"
												className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--instructions"
											></Text>
											<div className="intlc-assessment-tabitem__instructions intlc-assessment-tabitem__instructions--inline">
												{condition.Desc}
											</div>
										</div>
									))}
								</>
							}
							<div className="intlc-assessment-tabcoll__title">优化建议</div>
							<div
								className="intlc-assessment-tabcoll__advice"
								dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(strategy.Repair || '--') }}
							>
							</div>
							{
								configInfo?.type == 1
								?
									<>
										<div className="intlc-assessment-tabcoll__title title-required">添加处理建议</div>
										<Form className={'handle-form'}>
											<Form.Item
												required={true}
												status={getStatus(adviceField.meta, validating)}
												message={getStatus(adviceField.meta, validating) === "error" && adviceField.meta.error}
											>
												<Input.TextArea
													{
														...adviceField.input
													}
													size={'full'}
													placeholder={'请补充处理建议。处理建议应能使客户更好的理解该风险项可能造成的潜在隐患，以及指导客户优化/修复该风险。'}
												/>
											</Form.Item>
											<Form.Item
												required={true}
												status={getStatus(agreeField.meta, validating)}
												message={getStatus(agreeField.meta, validating) === "error" && agreeField.meta.error}
											>
												<Checkbox
													{
														...agreeField.input
													}
												>
													我确认本次推荐的内容符合客户的业务需求，并已知晓本次操作可以对客户带来的影响。
												</Checkbox>
											</Form.Item>
										</Form>
									</>
									:
									<></>
							}
							{
								configInfo?.type == 2
									?
									<>
										<Form
											className={'handle-form'}
											style={
												{
													marginTop: '16px'
												}
											}
										>
											<Form.Item
												required={true}
												status={getStatus(agreeField.meta, validating)}
												message={getStatus(agreeField.meta, validating) === "error" && agreeField.meta.error}
											>
												<Checkbox
													{
														...agreeField.input
													}
												>
													我确认本次推荐的内容符合客户的业务需求，并已知晓本次操作可以对客户带来的影响。
												</Checkbox>
											</Form.Item>
										</Form>
									</>
									:
									<></>
							}
							{
								configInfo?.type == 3
									?
									<>
										<div className="intlc-assessment-tabcoll__title">添加处理建议</div>
										<Form className={'handle-form'}>
											<Form.Item>
												<Input.TextArea
													size={'full'}
													placeholder={'请补充处理建议。处理建议应能使客户更好的理解该风险项可能造成的潜在隐患，以及指导客户优化/修复该风险。'}
													{
														...adviceField.input
													}
												/>
											</Form.Item>
										</Form>
									</>
									:
									<></>
							}
						</>
				}
			</Modal.Body>
			<Modal.Footer>
				<Button
					htmlType="submit"
					type="primary"
					loading={btnLoading}
				>
					提交
				</Button>
				<Button
					type="weak"
					onClick={(e)=>{
					handleVisible();
					e.stopPropagation();
					e.preventDefault();
				}}>
					取消
				</Button>
			</Modal.Footer>
			</form>
		</Modal>
	);
}
