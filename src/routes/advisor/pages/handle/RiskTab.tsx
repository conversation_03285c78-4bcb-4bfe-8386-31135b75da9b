import React, { useMemo, useState, forwardRef, useImperativeHandle } from 'react';
import {
	TagSearchBox,
	Table,
	Text,
	StatusTip,
	Button,
	Bubble,
	Tag
} from '@tea/component';
import { cloneDeep } from 'lodash';
import { DescribeRiskLists } from '@src/api/advisor/handle';
import { app } from '@tea/app';
import { stateMap } from "@src/routes/advisor/pages/handle/constants";

const initParams = {
  Limit: 20,
  Offset: 0,
  Filters: [],
};

const RiskTab = forwardRef((props: any, ref) => {
	const { type, products, hadle, id } = props;
  const [loading, setLoading] = useState(true);
  const [tagVal, setTagVal] = useState([]);
  const [attributes, setAttributes] = useState<any>([
    {
      type: 'input',
      key: 'strategyName',
      name: '评估项名称',
    },
    {
      type: ['multiple', { searchable: true }],
      key: 'product',
      name: '云产品',
      values: [],
    },
    {
      type: 'single',
      key: 'state',
      name: '状态',
      values: [],
    },
  ]);
  const [columns, setColumns] = useState<any>([
    {
      key: 'Name',
      header: '风险项名称',
		render(item) {
		  return <div className={'strategy-name'}>
			  {
				  item.Name
			  }
			  {
				  item.Warn > 0 && <Bubble placement={'right'} content={'来自技术服务团队的提醒'}>
					  <Tag className={'follow-tag'} theme={'error'} dark>
						  请关注
					  </Tag>
				  </Bubble>
			  }
		  </div>;
		}
	},
    {
      key: 'State',
      header: '当前状态',
      render(item) {
        return <Text theme={(item.State == -1 || item.State == -3) ? 'danger' : (item.State == 1 || item.State == 2) ? 'success' : item.State == -2 ? 'warning' : 'weak'}>
          {
            item.StateMap?.[item.State]
          }
        </Text>;
      },
    },
    {
      key: 'Duration',
      header: '持续时长（天）',
      render: item => (item.Duration != -1 ? item.Duration : '--'),
    },
    {
      key: 'RiskCount',
      header: '涉及资源数',
      render: item => (item.RiskCount != -1 ? item.RiskCount : '--'),
    },
    {
      key: 'Handler',
      header: '风险处理人',
      render: item => (item.Handler ? item.Handler : '--'),
    },
  ]);
  useMemo(()=>{
    if (type == 2) {
      columns.splice(5, 0, {
        key: 'UpdateTime',
        header: '更新时间',
        render: item => (item.UpdateTime ? item.UpdateTime : '--'),
      });
      setColumns(columns);
    }
	if (type == 1) {
		columns.splice(5, 0, {
			key: 'UpdateTime',
			header: '操作',
			render: item => {
				return <>
					<Button
						type={'link'}
						onClick={
							()=>{
								hadle(2, item);
							}
						}
					>
						提醒客户处理
					</Button>
					<Button
						type={'link'}
						onClick={
							()=>{
								hadle(1, item);
							}
						}
					>
						添加处理建议
					</Button>
				</>;
			},
		});
		setColumns(columns);
	}
  }, [type]);
  const sortColums = columns.filter((item)=>{
    return item.key != 'operation';
  }).map((item)=>{
    return item.key;
  });
  useMemo(()=>{
    if (products.length) {
      attributes[1]['values'] = products;
      setAttributes(attributes);
    }
  }, [products]);
  const [filterParams, setFilterParams] = useState<any>({
    ...cloneDeep(initParams),
    Type: type
  });
  const [recordsInfo, setRecordsInfo] = useState({
    RiskLists: [],
    TotalCount: 0,
    StateMap: null
  });
  useMemo(()=>{
    if (!attributes[2]['values']['length']) {
      attributes[2]['values'] = Object.entries(stateMap).filter((item: any)=>{
        if (type == '1') {
          return item[0] == -1 || item[0] == 1;
        } else {
          return item[0] == -2 || item[0] == -3 || item[0] == 2;
        }
      }).map((item)=>{
        return {
          key: item[0],
          name: item[1]
        };
      });
      setAttributes(attributes);
    }
  }, [recordsInfo]);
  // 分页
  const { pageable, sortable } = Table.addons;
  const [sorts, setSorts] = useState([]);
  // 获取记录
  const getDescribeRiskLists = async (filterParams) => {
    setLoading(true);
    try {
      const result = await DescribeRiskLists({
        ...filterParams,
		  AppId: Number(id),
		  ShowError: true,
		  OnlyData: true
      });
      result.RiskLists?.forEach((item)=>{
        item.StateMap = {
          ...stateMap
        };
        item.LastSuccessTaskId = result.LastSuccessTaskId;
      });
      setRecordsInfo(result);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    }
  };
	useImperativeHandle(ref, ()=> {
		return {
			getDescribeRiskLists: ()=>{
				getDescribeRiskLists(filterParams);
			}
		};
	}, [filterParams]);
  useMemo(() => {
    getDescribeRiskLists(filterParams);
  }, []);

  return (<>
    <div style={
      {
        maxWidth: '55%'
      }
    }>
      <TagSearchBox
        attributes={attributes}
        minWidth={420}
        value={tagVal}
        onChange={
          (val)=>{
            let strategyName = null;
            val.forEach((item)=>{
              if (!item.attr) {
                strategyName = item.values;
              }
            });
            let hasRecord = false;
            val.forEach((item)=>{
              if (item.attr?.['key'] == 'strategyName' && strategyName) {
                item.values = cloneDeep(strategyName);
                hasRecord = true;
              } else if (strategyName && !hasRecord && !item.attr) {
                item.attr = {
                  type: 'input',
                  key: 'strategyName',
                  name: '评估项名称',
                };
              }
            });
            const valList = val.filter((item)=>{
              return item.attr;
            });
            const filterList = valList.map((item)=>{
              if (item.attr['key'] == 'product' || item.attr['key'] == 'state') {
                return {
                  Name: item.attr['key'],
                  Values: item.values.map((el)=>el.key)
                };
              } else {
                return {
                  Name: item.attr['key'],
                  Values: item.values.map((el)=>el.name)
                };
              }
            });
            setTagVal(cloneDeep(valList));
            setFilterParams({
              ...filterParams,
              Offset: 0,
              Filters: [
                ...filterList
              ]
            });
            getDescribeRiskLists({
              ...filterParams,
              Offset: 0,
              Filters: [
                ...filterList
              ]
            });
          }
        }
      />
    </div>
    <Table
      records = {
        [...recordsInfo?.RiskLists].sort(sortable.comparer(sorts))
      }
      columns={columns}
      topTip={
        (loading || recordsInfo?.RiskLists.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
      }
      addons={
        [
          sortable({
            columns: sortColums,
            value: sorts,
            onChange: value => setSorts(value),
          }),
          pageable({
            recordCount: recordsInfo?.TotalCount,
            onPagingChange: ({ pageIndex, pageSize }) => {
              setFilterParams({
                ...filterParams,
                Limit: pageSize,
                Offset: (pageIndex - 1) * pageSize,
              });
              getDescribeRiskLists({
                ...filterParams,
                Limit: pageSize,
                Offset: (pageIndex - 1) * pageSize,
              });
            },
            pageSizeOptions: [10, 20, 30, 50, 100, 200],
            pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
            pageSize: filterParams.Limit,
          }),
        ]
      }
    />
  </>);
});
export default RiskTab;
