import React, { useMemo, useState, forwardRef, useImperativeHandle } from 'react';
import {
	TagSearchBox,
	Table,
	Text,
	StatusTip,
	Button,
	Tag
} from '@tea/component';
import { cloneDeep } from 'lodash';
import { DescribeToRecommendRiskLists } from '@src/api/advisor/handle';
import { stateMap } from "@src/routes/advisor/pages/handle/constants";

const initParams = {
	Limit: 20,
	Offset: 0,
	Filters: [],
};

const RecommendRiskTab = forwardRef((props: any, ref)=>{
	const { type, products, hadle, id } = props;
	const [loading, setLoading] = useState(true);
	const [tagVal, setTagVal] = useState([]);
	const [attributes, setAttributes] = useState<any>([
		{
			type: 'input',
			key: 'strategyName',
			name: '评估项名称',
		},
		{
			type: ['multiple', { searchable: true }],
			key: 'product',
			name: '云产品',
			values: [],
		},
		{
			type: 'single',
			key: 'group',
			name: '类别',
			values: [],
		},
	]);
	const [columns, setColumns] = useState<any>([
		{
			key: 'Name',
			header: '风险项名称'
		},
		{
			key: 'Group',
			header: '类别'
		},
		{
			key: 'HighRiskCount',
			header: '高风险',
			render: item => {
				return <div  style={
					{
						display: 'flex',
						alignItems: 'center'
					}
				}>
					<Text theme={'danger'}>
						{
							item.HighRiskCount
						}
					</Text>
					&nbsp;
					&nbsp;
					{
						<Tag theme={item.HighRiskRatio > 0 ? 'error' : 'success'} className={'risk-tag'}>
							同比上周
							{
								item.HighRiskRatio > 0
									?
									<svg xmlns="http://www.w3.org/2000/svg"
										 className="icon icon-tabler icon-tabler-arrow-narrow-up"
										 width="16" height="16" viewBox="0 0 24 24"
										 strokeWidth="2" stroke="#e54545"
										 fill="none" strokeLinecap="round"
										 strokeLinejoin="round">
										<path stroke="none" d="M0 0h24v24H0z"
											  fill="none" />
										<line x1="12" y1="5" x2="12" y2="19" />
										<line x1="16" y1="9" x2="12" y2="5" />
										<line x1="8" y1="9" x2="12" y2="5" />
									</svg>
									:
									<svg xmlns="http://www.w3.org/2000/svg"
										 className="icon icon-tabler icon-tabler-arrow-narrow-down"
										 width="16" height="16" viewBox="0 0 24 24"
										 strokeWidth="2" stroke="#0abf5b"
										 fill="none" strokeLinecap="round"
										 strokeLinejoin="round">
										<path stroke="none" d="M0 0h24v24H0z"
											  fill="none" />
										<line x1="12" y1="5" x2="12" y2="19" />
										<line x1="16" y1="15" x2="12" y2="19" />
										<line x1="8" y1="15" x2="12" y2="19" />
									</svg>
							}
							{
								Math.abs(item.HighRiskRatio)
							}
						</Tag>
					}
				</div>;
			}
		},
		{
			key: 'MediumRiskCount',
			header: '中风险',
			render: item => {
				return <div style={
					{
						display: 'flex',
						alignItems: 'center'
					}
				}>
					<Text theme={'warning'}>
						{
							item.MediumRiskCount
						}
					</Text>
					&nbsp;
					&nbsp;
					{
						<Tag theme={item.MediumRiskRatio > 0 ? 'error' : 'success'} className={'risk-tag'}>
							同比上周
							{
								item.MediumRiskRatio > 0
									?
									<svg xmlns="http://www.w3.org/2000/svg"
										 className="icon icon-tabler icon-tabler-arrow-narrow-up"
										 width="16" height="16" viewBox="0 0 24 24"
										 strokeWidth="2" stroke="#e54545"
										 fill="none" strokeLinecap="round"
										 strokeLinejoin="round">
										<path stroke="none" d="M0 0h24v24H0z"
											  fill="none" />
										<line x1="12" y1="5" x2="12" y2="19" />
										<line x1="16" y1="9" x2="12" y2="5" />
										<line x1="8" y1="9" x2="12" y2="5" />
									</svg>
									:
									<svg xmlns="http://www.w3.org/2000/svg"
										 className="icon icon-tabler icon-tabler-arrow-narrow-down"
										 width="16" height="16" viewBox="0 0 24 24"
										 strokeWidth="2" stroke="#0abf5b"
										 fill="none" strokeLinecap="round"
										 strokeLinejoin="round">
										<path stroke="none" d="M0 0h24v24H0z"
											  fill="none" />
										<line x1="12" y1="5" x2="12" y2="19" />
										<line x1="16" y1="15" x2="12" y2="19" />
										<line x1="8" y1="15" x2="12" y2="19" />
									</svg>
							}
							{
								Math.abs(item.MediumRiskRatio)
							}
						</Tag>
					}
				</div>;
			},
		},
		{
			key: 'operation',
			header: '操作',
			render: item => {
				return <Button
					type={'link'}
					onClick={
						()=>{
							hadle(3, item);
						}
					}
				>
					推荐给用户
				</Button>;
			}
		},
	]);
	const sortColums = columns.filter((item)=>{
		return item.key != 'operation';
	}).map((item)=>{
		return item.key;
	});
	useMemo(()=>{
		if (products.length) {
			attributes[1]['values'] = products;
			setAttributes(attributes);
		}
	}, [products]);
	const [filterParams, setFilterParams] = useState<any>({
		...cloneDeep(initParams)
	});
	const [recordsInfo, setRecordsInfo] = useState({
		RiskLists: [],
		TotalCount: 0,
		StateMap: null
	});
	useMemo(()=>{
		if (!attributes[2]['values']['length']) {
			attributes[2]['values'] = Object.entries({
				1: '安全',
				2: '可靠',
				3: '服务限制',
				4: '成本',
				5: '性能'
			}).map((item)=>{
				return {
					key: item[0],
					name: item[1]
				};
			});
			setAttributes(attributes);
		}
	}, [recordsInfo]);
	// 分页
	const { pageable, sortable } = Table.addons;
	const [sorts, setSorts] = useState([]);
	// 获取记录
	const getDescribeToRecommendRiskLists = async (filterParams) => {
		setLoading(true);
		try {
			const result = await DescribeToRecommendRiskLists({
				...filterParams,
				AppId: Number(id),
				ShowError: true,
				OnlyData: true
			});
			result.RiskLists?.forEach((item)=>{
				item.StateMap = {
					...stateMap
				};
				item.LastSuccessTaskId = result.LastSuccessTaskId;
			});
			setRecordsInfo(result);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		}
	};
	useImperativeHandle(ref, ()=> {
		return {
			getDescribeToRecommendRiskLists: ()=>{
				getDescribeToRecommendRiskLists(filterParams);
			}
		};
	}, [filterParams]);
	useMemo(() => {
		getDescribeToRecommendRiskLists(filterParams);
	}, []);

	return (<>
		<div style={
			{
				maxWidth: '55%'
			}
		}>
			<TagSearchBox
				attributes={attributes}
				minWidth={420}
				value={tagVal}
				onChange={
					(val)=>{
						let strategyName = null;
						val.forEach((item)=>{
							if (!item.attr) {
								strategyName = item.values;
							}
						});
						let hasRecord = false;
						val.forEach((item)=>{
							if (item.attr?.['key'] == 'strategyName' && strategyName) {
								item.values = cloneDeep(strategyName);
								hasRecord = true;
							} else if (strategyName && !hasRecord && !item.attr) {
								item.attr = {
									type: 'input',
									key: 'strategyName',
									name: '评估项名称',
								};
							}
						});
						const valList = val.filter((item)=>{
							return item.attr;
						});
						const filterList = valList.map((item)=>{
							if (item.attr['key'] == 'product' || item.attr['key'] == 'group') {
								return {
									Name: item.attr['key'],
									Values: item.values.map((el)=>el.key)
								};
							} else {
								return {
									Name: item.attr['key'],
									Values: item.values.map((el)=>el.name)
								};
							}
						});
						setTagVal(cloneDeep(valList));
						setFilterParams({
							...filterParams,
							Offset: 0,
							Filters: [
								...filterList
							]
						});
						getDescribeToRecommendRiskLists({
							...filterParams,
							Offset: 0,
							Filters: [
								...filterList
							]
						});
					}
				}
			/>
		</div>
		<Table
			records = {
				[...recordsInfo?.RiskLists].sort(sortable.comparer(sorts))
			}
			columns={columns}
			topTip={
				(loading || recordsInfo?.RiskLists.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
			}
			addons={
				[
					sortable({
						columns: sortColums,
						value: sorts,
						onChange: value => setSorts(value),
					}),
					pageable({
						recordCount: recordsInfo?.TotalCount,
						onPagingChange: ({ pageIndex, pageSize }) => {
							setFilterParams({
								...filterParams,
								Limit: pageSize,
								Offset: (pageIndex - 1) * pageSize,
							});
							getDescribeToRecommendRiskLists({
								...filterParams,
								Limit: pageSize,
								Offset: (pageIndex - 1) * pageSize,
							});
						},
						pageSizeOptions: [10, 20, 30, 50, 100, 200],
						pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
						pageSize: filterParams.Limit,
					}),
				]
			}
		/>
	</>);
});

export default RecommendRiskTab;
