import React, { useState, useMemo } from 'react';
import { <PERSON> } from "react-router-dom";
import { Table, Button, Row, Card, Layout, Col, Select, SelectMultiple, Text, Input, DatePicker, StatusTip, Tag } from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { getDescribeFaultByOwner } from '@src/api/advisor/faultNotification';
import { ConfigGroup, FilterParams } from "@src/types/advisor/faultNotification";
import moment from 'moment';
import { cloneDeep } from "lodash";
import './index.less';
import { setStorage, getStorage, removeStorage } from "@src/utils/storage";
import { TagSelect, Tooltip } from "@tea/component";
import { getProcessEnv } from '../../../../../app/utils';

// 是否是国际站
const isAbroad = getProcessEnv() === 'production-abroad';

let initFilterData = [
	{
		label: '客户APPID',
		value: 'appid',
		type: 'input',
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: '客户名称',
		value: 'customer_name',
		type: 'input',
		span: [5, 19],
		virtualVal: ''
	},
	{
		label: '售后负责人',
		value: 'owner',
		type: 'input',
		span: [6, 18],
		virtualVal: getStorage('engName'),
		placeholder: '多人请用;分隔'
	},
	{
		label: '故障名称',
		value: 'name',
		type: 'input',
		span: [5, 19],
		virtualVal: ''
	},
	{
		label: '是否通知',
		value: 'is_informed',
		type: 'select',
		options: [
			{
				text: '全部',
				value: ''
			},
			{
				text: '未通知',
				value: '1'
			},
			{
				text: '已通知',
				value: '2'
			}
		],
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: '当前状态',
		value: 'status',
		type: 'select',
		options: [
			{
				text: '全部',
				value: ''
			},
			{
				text: '故障中',
				value: '1'
			},
			{
				text: '故障已恢复',
				value: '2'
			}
		],
		span: [5, 19],
		virtualVal: ''
	},
	{
		label: '故障时间',
		value: 'start_time,end_time',
		type: 'mutipleTime',
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: 'UIN',
		value: 'uin',
		type: 'input',
		span: [5, 19],
		virtualVal: ''
	},
	{
		label: '涉及云产品',
		value: 'product',
		type: 'mutipleSelect',
		options: [
			{
				text: '全部',
				value: ''
			}
		],
		span: [6, 18],
		virtualVal: []
	},
];

// 国际站适配
if (isAbroad) {
	initFilterData = initFilterData.filter(i => i.value !== 'is_informed');
}

const initParams = {
	Limit: 10,
	Offset: 0,
	Name: getStorage('engName'),
	Filters: [
		{
			Name: 'owner',
			Values: [getStorage('engName')]
		}
	],
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742
};

export function MyNotifications({ productList }) {
	const {Body, Content} = Layout;
	const [filterData, setFilterData] = useState<any>([...cloneDeep(initFilterData)]);
	const [loading, setLoading] = useState(false);
	const history = useHistory();
	const [columns, setColumns] = useState<any>([
		{
			key: "AppId",
			header: "APPID"
		},
		{
			key: "Uin",
			header: "UIN"
		},
		{
			key: "CustomerName",
			header: "客户名称"
		},
		{
			key: "Id",
			header: "故障 ID",
			render: item=>(<Link to={`/advisor/fault-notification/detail/${item.Id}`}>{item.Id}</Link>)
		},
		{
			key: "Title",
			header: "故障标题"
		},
		{
			key: "Product",
			header: "涉及云产品",
			className: 'resource-list',
			render: (item) => (
				<div className={'resource-list'}>
					{
						item?.Product.map((el, i)=>(<div key={i} className={'fault-tag-wrap'}>
							<Tooltip title={el}>
								<Tag theme={'primary'} className={'def-tag'}>{el}</Tag>
							</Tooltip>
						</div>))
					}
				</div>
			),
		},
		{
			key: "StartTime",
			header: "故障开始时间"
		},
		{
			key: "EndTime",
			header: "故障恢复时间",
			render: item => (item.EndTime ? item.EndTime : '-')
		},
		{
			key: "Duration",
			header: "持续时长",
			render: item => (
				<Text>{item.Duration}</Text>
			),
		},
		{
			key: "IsInformed",
			header: "是否通知",
			render: item => {
				const arr = item.IsInformed.split('|');
				return (
					isAbroad ? '-' : <Tag theme={arr[0] == 1 ? 'warning' : 'success'}>{arr[1]}</Tag>
				);
			},
		},
		{
			key: "Status",
			header: "故障当前状态",
			render: item => {
				const arr = item.Status.split('|');
				return (
					<Tag theme={arr[0] == 1 ? 'error' : 'success'}>{arr[1]}</Tag>
				);
			},
		},
		{
			key: "AffectedNum1",
			header: "操作",
			render: (item) => (
				item.IsActive
					?
					(isAbroad ? '-' : <Text
						theme={'primary'}
						className={'notice-btn'}
						onClick={
							()=>{
								setStorage('oneFaultDesc', JSON.stringify({
									...item,
									Resources: item.Product,
									onlySee: item.IsActive ? false : true,
									Name: item.Title,
									Owner: getStorage('engName'),
									from: 'self'
								}));
								history.push('/advisor/fault-notification/examine');
							}
						}
					>{'通知客户'}</Text>)
					:
					(<>
						<Text
							theme={'primary'}
							className={'notice-btn'}
							onClick={
								()=>{
									setStorage('oneFaultDesc', JSON.stringify({
										...item,
										Resources: item.Product,
										onlySee: item.IsActive ? false : true,
										Name: item.Title,
										Owner: getStorage('engName'),
										from: 'self'
									}));
									history.push('/advisor/fault-notification/examine');
								}
							}
						>{'查看'}</Text>
						{isAbroad ? '' : <Text
							theme={'weak'}
						>{'通知客户'}</Text>}
					</>)
			),
		},
	]);
	const [records, setRecords] = useState<ConfigGroup>({
		FaultLists: []
	});
	const { RangePicker } = DatePicker;
	const [filterParams, setFilterParams] = useState<FilterParams>({
		...cloneDeep(initParams)
	});
	const resetFilterData = (productList)=>{
		const list = cloneDeep(initFilterData);
		for (let i = 0;i < list.length; ++i) {
			if (list[i]['value'] == 'product') {
				list[i]['options'] = [...cloneDeep(productList)];
				break;
			}
		}
		setFilterData([
			...list
		]);
	};
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeFaultByOwner(filterParams);
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				if (item.Name == nameList[0]) {
					item.Values = [val[0]];
					notHasName = false;
				}
				if (item.Name == nameList[1]) {
					item.Values = [val[1]];
					notHasName = false;
				}
			} else {
				if (item.Name == name) {
					item.Values = Array.isArray(val) ? [...val] : [val];
					notHasName = false;
				}
			}
		});
		if (notHasName) {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				filterParams.Filters.push({
					Name: nameList[0],
					Values: [val[0]]
				});
				filterParams.Filters.push({
					Name: nameList[1],
					Values: [val[1]]
				});
			} else {
				filterParams.Filters.push({
					Name: name,
					Values: Array.isArray(val) ? [...val] : [val]
				});
			}
		}
		setFilterParams({
			...filterParams
		});
	};
	useMemo(() => {
		getTabData(filterParams);
		resetFilterData(productList);
		return ()=>{
			getStorage('oneFaultDesc') && removeStorage('oneFaultDesc');
		};
	}, [productList]);
	// 分页
	const {pageable, columnsResizable} = Table.addons;

	return (
		<Body>
			<Content.Body>
				<Card className='fault-filter-wrap'>
					<Card.Body>
						<Row>
							{
								filterData.map((item, i) => {
									return item.type != 'mutipleSelect' && (<Col span={i == 2 || i == 6 ? 75 : 55} key={i}>
										<Row verticalAlign={"middle"}>
											<Col span={item?.span[0]}>
												<Text theme="label" verticalAlign="middle">{item.label}</Text>
											</Col>
											<Col span={item?.span[1]}>
												{
													item.type == 'input' ?
														<Input onChange={(val) => {
															item.virtualVal = val;
															setFilterData([
																...filterData
															]);
															refreshAllParams(item.value, val);
														}
														}
															   value={item.virtualVal}
														/>
														:
															item.type == 'mutipleTime' ?
																<RangePicker
																	showTime
																	value={item.virtualVal}
																	range={[
																		moment()
																			.subtract(365, "d")
																			.startOf("d"),
																		moment().endOf("d"),
																	]}
																	onChange={(val) => {
																		item.virtualVal = [...val];
																		setFilterData([
																			...filterData
																		]);
																		refreshAllParams(item.value, [
																			val[0].format("YYYY-MM-DD"),
																			val[1].format("YYYY-MM-DD")
																		]);
																	}
																	}
																/>
																:
																item.type == 'select' ?
																	<Select
																		options={item.options}
																		appearance="button"
																		size='m'
																		value={item.virtualVal}
																		onChange={(val) => {
																			item.virtualVal = val;
																			setFilterData([
																				...filterData
																			]);
																			refreshAllParams(item.value, val);
																		}
																		}
																	></Select>
																	:
																	<></>
												}
											</Col>
										</Row>
									</Col>);
								})
							}
						</Row>
						<Row verticalAlign={"middle"} style={{
							marginTop: '10px'
						}}>
							<Col span={3}>
								<Text theme="label" verticalAlign="middle">{filterData[filterData.length - 1].label}</Text>
							</Col>
							<Col>
								<TagSelect
									options={filterData[filterData.length - 1].options}
									value={filterData[filterData.length - 1].virtualVal}
									onChange={(val) => {
										filterData[filterData.length - 1].virtualVal = [...val];
										setFilterData([
											...filterData
										]);
										refreshAllParams(filterData[filterData.length - 1].value, val);
									}
									}
									style={{
										maxWidth: '1000px'
									}}
								></TagSelect>
							</Col>
						</Row>
						<Row style={{
							justifyContent: 'center',
							margin: '20px 10px 10px 10px'
						}}>
							<Button type='primary' onClick={() => {
								setFilterParams({
									...filterParams,
									Offset: 0
								});
								getTabData({
									...filterParams,
									Offset: 0
								});
							}}>查询</Button>
							<Button style={{
								marginLeft: '10px'
							}} onClick={()=>{
								resetFilterData(productList);
								setFilterParams({
									...cloneDeep(initParams)
								});
								getTabData({
									...cloneDeep(initParams)
								});
							}}>重置</Button>
						</Row>
					</Card.Body>
				</Card>
				<Card>
					<Card.Body>
						<Table
							records = {
								records.FaultLists ? records.FaultLists : []
							}
							columns={columns}
							topTip={
								(loading || records.FaultLists.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
							}
							addons={
								[
									columnsResizable({
										onResizeEnd: columns => {
											setColumns(columns);
										},
										minWidth: 100,
										maxWidth: 1000,
										columns: ['Product']
									}),
									pageable({
										recordCount: records.TotalCount ? records.TotalCount : 0,
										onPagingChange: ({pageIndex, pageSize}) => {
											setFilterParams({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize
											});
											getTabData({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize
											});
										},
										pageSizeOptions: [10, 20, 30, 50, 100, 200],
										pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
										pageSize: filterParams.Limit
									})
								]
							}
						/>
					</Card.Body>
				</Card>
			</Content.Body>
		</Body>
	)
}
