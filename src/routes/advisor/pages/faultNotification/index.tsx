/* eslint-disable no-nested-ternary */
import React, { useEffect, useMemo, useState } from 'react';
import { Tabs, TabPanel, Layout } from '@tencent/tea-component';
import { MyNotifications } from '@src/routes/advisor/pages/faultNotification/MyNotifications';
import { FaultList } from '@src/routes/advisor/pages/faultNotification/FaultList';
import { cloneDeep } from 'lodash';
import './index.less';
import { setStorage, getStorage, removeStorage } from '@src/utils/storage';
import { NotPermission } from '@src/routes/NotPermission';
// import { useHistory } from '@tea/app';
import { reportVisitPage } from '@src/utils/report';
import { getDescribeProductList } from '@src/api/advisor/faultNotification';

let timer;
export function FaultNotification() {
	let oneFaultDesc: any = {};
	if (getStorage('oneFaultDesc')) {
		oneFaultDesc = JSON.parse(getStorage('oneFaultDesc'));
		removeStorage('oneFaultDesc');
	}
	const { Body, Content } = Layout;
	const [productList, setProductList] = useState([]);
	const tabs = [
		{ id: 'index', label: '故障列表' },
		{ id: 'self', label: '我的通知' },
	];
	const getAllProduct = async () => {
		try {
			const res = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742,
			});
			const list = [];
			for (const key in res.ProductDict) {
				list.push({
					text: res.ProductDict[key],
					value: key,
				});
			}
			res.list = list;
			setProductList(list);
			setStorage('productInfo', JSON.stringify(cloneDeep(res)));
		} catch (err) {};
	};
	useMemo(() => {
		getAllProduct();
		reportVisitPage({
			isaReportMeunName: '平台故障通知',
		});
	}, []);
	const [activeId, setActiveId] = useState(oneFaultDesc.from == 'self' ? 'self' : 'index');
	const changeActive = (tab) => {
		setActiveId(tab.id);
	};
	// const history = useHistory();
	const [permission, setPermission] = useState(0);
	const CheckPermission = () => {
		const menuItems = JSON.parse(localStorage.getItem('menuItems'));
		if (menuItems) {
			// 判断是否存在
			const tmp = menuItems.filter((i) => {
				if (i.key == 'fault') {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
		// 临时测试
		// setPermission(1);
	};
	// 持续从localStorage获取菜单列表
	useEffect(() => {
		timer = setInterval(() => {
			CheckPermission();
		}, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);

	return (
		<Body>{
			permission === 0
				?	<div></div>
				:	permission === 2
					?	<NotPermission />
					:	<>
						<Content.Header title={'平台故障通知'}></Content.Header>
						<Content.Body>
							<Tabs
								ceiling
								animated={false}
								tabs={tabs}
								destroyInactiveTabPanel={false}
								activeId={activeId}
								onActive={changeActive}
							>
								<TabPanel id="index">
									<FaultList
										productList={productList}
									></FaultList>
								</TabPanel>
								<TabPanel id="self">
									<MyNotifications
										productList={productList}
									></MyNotifications>
								</TabPanel>
							</Tabs>
						</Content.Body>
					</>
		}
		</Body>
	);
}
