/* eslint-disable no-nested-ternary */
import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import {
	Table,
	Button,
	Row,
	Card,
	Layout,
	Col,
	Select,
	Text,
	Input,
	DatePicker,
	StatusTip,
	Tag,
	TagSelect,
} from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { getDescribeFaultLists } from '@src/api/advisor/faultNotification';
import { ConfigGroup, FilterParams } from '@src/types/advisor/faultNotification';
import { REPORT_DOMAIN, PLATFORM_REPORT_URL } from '../touch/constants';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { getProcessEnv } from '../../../../../app/utils';
import './index.less';
import { getStorage } from '@src/utils/storage';
import { Tooltip } from '@tea/component';
const initFilterData = [
	{
		label: '故障ID',
		value: 'id',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '故障名称',
		value: 'name',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '当前状态',
		value: 'status',
		type: 'select',
		options: [
			{
				text: '全部',
				value: '',
			},
			{
				text: '故障中',
				value: '1',
			},
			{
				text: '故障已恢复',
				value: '2',
			},
		],
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '售后负责人',
		value: 'owner',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
		placeholder: '多人请用;分隔',
	},
	{
		label: '客户名称',
		value: 'customer_name',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '客户APPID',
		value: 'appid',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '故障时间',
		value: 'start_time,end_time',
		type: 'mutipleTime',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '涉及云产品',
		value: 'product',
		type: 'mutipleSelect',
		options: [
			{
				text: '全部',
				value: '',
			},
		],
		span: [6, 18],
		virtualVal: [],
	},
];
const initParams = {
	Limit: 10,
	Offset: 0,
	Filters: [],
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742,
	Name: getStorage('engName'),
};

export function FaultList({ productList }) {
	const history = useHistory();
	const { search } = location;
	const hasAppid = search.indexOf('appid') != -1;
	const searchVal = hasAppid ? search.split('=')[1] : '';
	const initOriginFilterData = cloneDeep(initFilterData);
	const [searchAppId, setSearchAppId] = useState('');
	if (hasAppid) {
		history.replace(location.pathname);
	}
	// 是否是国际站
	const isAbroad = getProcessEnv() === 'production-abroad';
	if (hasAppid) {
		if (!searchAppId) {
			setSearchAppId(searchVal);
		}
	}
	initOriginFilterData[5].virtualVal = searchAppId;
	const { Body, Content } = Layout;
	const [filterData, setFilterData] = useState<any>([...cloneDeep(initOriginFilterData)]);
	const [loading, setLoading] = useState(false);

	const [columns, setColumns] = useState<any>([
		{
			key: 'Id',
			header: '故障 ID',
			width: 100,
			render: item => (<Link to={`/advisor/fault-notification/detail/${item.Id}`}>{item.Id}</Link>),
		},
		{
			key: 'Title',
			header: '故障标题',
			render(item) {
				return <Tooltip title={item.Title}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Title
						}
					</span>
				</Tooltip>;
			},
		},
		{
			key: 'CreateType',
			header: '录入方式',
			render: (item) => {
				const CreateTypeArr = item.CreateType.split('|');
				return (
					<Text>
						{CreateTypeArr[1]}
						<br/>
						{CreateTypeArr[2]}
					</Text>
				);
			},
		},
		{
			key: 'Product',
			header: '涉及云产品',
			className: 'resource-list',
			render: item => (
				<div className={'resource-list'}>
					{
						item?.Product.map((el, i) => (<div key={i} className={'fault-tag-wrap'}>
							<Tooltip title={el}>
								<Tag theme={'primary'} className={'def-tag'}>{el}</Tag>
							</Tooltip>
						</div>))
					}
				</div>
			),
		},
		{
			key: 'StartTime',
			header: '故障开始时间',
		},
		{
			key: 'EndTime',
			header: '故障恢复时间',
			render: item => (item.EndTime ? item.EndTime : '-'),
		},
		{
			key: 'Duration',
			header: '持续时长',
			render: item => (
				<Text>{item.Duration}</Text>
			),
		},
		{
			key: 'AffectedNum',
			header: '影响客户数',
		},
		{
			key: 'Status',
			header: '故障当前状态',
			render: (item) => {
				const statusArr = item.Status.split('|');
				return (
					statusArr[0]
					 ? <Tag theme={statusArr[0] == 1 ? 'warning' : 'success'}>
							{statusArr[1]}
							<br/>
							{statusArr[2]}
						</Tag>
						: '-'
				);
			},
		},
		{
			key: 'Creater',
			header: '录单人',
		},
		{
			key: 'CreateTime',
			header: '录入时间',
		},
		{
			key: 'AffectedNum1',
			header: '操作',
			fixed: 'right',
			render: item => (
				<>
					{
						isAbroad
							? <Link to={`/advisor/fault-notification/abroad-detail/${item.Id}`}>查看详情</Link>
							: <Link to={`/advisor/fault-notification/detail/${item.Id}`}>查看详情</Link>
					}
					{
						item.IsActive && <Link style={{ marginLeft: '10px' }} to={`/advisor/fault-notification/new/${item.Id}`}>更新状态</Link>
					}
				</>
			),
		},
	]);
	const [records, setRecords] = useState<ConfigGroup>({
		FaultLists: [],
	});
	const { RangePicker } = DatePicker;
	const [filterParams, setFilterParams] = useState<FilterParams>({
		...cloneDeep(initParams),
		Filters: [
			{
				Name: 'appid',
				Values: [searchVal],
			},
		],
	});
	const resetFilterData = (init?: boolean) => {
		let list;
		if (init) {
			list = cloneDeep(initOriginFilterData);
		} else {
			list = cloneDeep(initFilterData);
		}
		for (let i = 0;i < list.length; ++i) {
			if (list[i].value == 'product') {
				list[i].options = [...cloneDeep(productList)];
				break;
			}
		}
		setFilterData([
			...list,
		]);
	};
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeFaultLists(filterParams);
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (name.indexOf(',') !== -1) {
				const nameList = name.split(',');
				if (item.Name === nameList[0]) {
					item.Values = [val[0]];
					notHasName = false;
				}
				if (item.Name === nameList[1]) {
					item.Values = [val[1]];
					notHasName = false;
				}
			} else {
				if (item.Name === name) {
					item.Values = Array.isArray(val) ? [...val] : [val];
					notHasName = false;
				}
			}
		});
		if (notHasName) {
			if (name.indexOf(',') !== -1) {
				const nameList = name.split(',');
				filterParams.Filters.push({
					Name: nameList[0],
					Values: [val[0]],
				});
				filterParams.Filters.push({
					Name: nameList[1],
					Values: [val[1]],
				});
			} else {
				filterParams.Filters.push({
					Name: name,
					Values: Array.isArray(val) ? [...val] : [val],
				});
			}
		}
		setFilterParams({
			...filterParams,
		});
	};
	useMemo(() => {
		if (productList.length > 0) {
			resetFilterData(true);
		}
	}, [productList]);
	useMemo(() => {
		getTabData(filterParams);
	}, []);
	// 分页
	const { pageable, scrollable, columnsResizable } = Table.addons;
	if (location.search.indexOf('appid') != -1) {
		const history = useHistory();
		history.replace(location.pathname);
	}

	return (
		<Body>
			<Content.Body>
				<Card>
					<Card.Body className='fault-filter-wrap'>
						<Row>
							{
								filterData.map((item, i) => item.type != 'mutipleSelect' && (<Col span={i == 2 || i == 6 ? 75 : 55} key={i}>
									<Row verticalAlign={'middle'}>
										<Col span={item?.span[0]}>
											<Text theme="label" verticalAlign="middle">{item.label}</Text>
										</Col>
										<Col span={item?.span[1]}>
											{
												item.type == 'input'
													? <Input onChange={(val) => {
														item.virtualVal = val;
														setFilterData([
															...filterData,
														]);
														refreshAllParams(item.value, val);
													}
													}
													value={item.virtualVal}
													placeholder={item.placeholder}
													/>
													:	item.type === 'mutipleTime'
														? <RangePicker
															showTime
															value={item.virtualVal}
															range={[
																moment()
																	.subtract(365, 'd')
																	.startOf('d'),
																moment().endOf('d'),
															]}
															onChange={(val) => {
																item.virtualVal = [...val];
																setFilterData([
																	...filterData,
																]);
																refreshAllParams(item.value, [
																	val[0].format('YYYY-MM-DD HH:mm:ss'),
																	val[1].format('YYYY-MM-DD HH:mm:ss'),
																]);
															}
															}
														/>
														:	item.type === 'select'
															?	<Select
																options={item.options}
																appearance="button"
																size='m'
																value={item.virtualVal}
																onChange={(val) => {
																	item.virtualVal = val;
																	setFilterData([
																		...filterData,
																	]);
																	refreshAllParams(item.value, val);
																}
																}
															></Select>
															:	<></>
											}
										</Col>
									</Row>
								</Col>))
							}
						</Row>
						<Row verticalAlign={'middle'} style={{
							marginTop: '10px',
						}}>
							<Col span={3}>
								<Text theme="label" verticalAlign="middle">{filterData[filterData.length - 1].label}</Text>
							</Col>
							<Col>
								<TagSelect
									options={filterData[filterData.length - 1].options}
									value={filterData[filterData.length - 1].virtualVal}
									onChange={(val) => {
										filterData[filterData.length - 1].virtualVal = [...val];
										setFilterData([
											...filterData,
										]);
										refreshAllParams(filterData[filterData.length - 1].value, val);
									}
									}
									style={{
										maxWidth: '1000px',
									}}
								></TagSelect>
							</Col>
						</Row>
						<Row style={{
							justifyContent: 'center',
							margin: '20px 10px 10px 10px',
						}}>
							<Button type='primary' onClick={() => {
								setFilterParams({
									...filterParams,
									Offset: 0,
								});
								getTabData({
									...filterParams,
									Offset: 0,
								});
							}}>
								查询
							</Button>
							<Button
								style={{ marginLeft: '10px' }}
								onClick={() => {
									resetFilterData();
									setFilterParams({
										...cloneDeep(initParams),
									});
									getTabData({
										...cloneDeep(initParams),
									});
								}}>
								重置
							</Button>
							<Button
								disabled={!records.IsCreateActive}
								tooltip={!records.IsCreateActive && '请您点击【权限管理】->【我的权限】->【申请】->【专项权限】->【故障通知管理读写权限】申请权限，审批通过后可创建'}
								style={{ marginLeft: '10px' }}
								onClick={() => {
									history.push('/advisor/fault-notification/new/0');
								}}>
									新建故障通知
							</Button>
							<Button
								style={{ marginLeft: '10px' }}
								onClick={() => {
									window.open(`${REPORT_DOMAIN}${PLATFORM_REPORT_URL}`);
								}}
							>
								查看运营报表
							</Button>
						</Row>
					</Card.Body>
				</Card>
				<Card>
					<Card.Body>
						<Table
							records = {
								records.FaultLists ? records.FaultLists : []
							}
							columns={columns}
							topTip={
								(loading || records.FaultLists.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
							}
							addons={
								[
									columnsResizable({
										onResizeEnd: (columns) => {
											setColumns(columns);
										},
										minWidth: 100,
										maxWidth: 1000,
										columns: ['Product'],
									}),
									pageable({
										recordCount: records.TotalCount ? records.TotalCount : 0,
										onPagingChange: ({ pageIndex, pageSize }) => {
											setFilterParams({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize,
											});
											getTabData({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize,
											});
										},
										pageSizeOptions: [10, 20, 30, 50, 100, 200],
										pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
										pageSize: filterParams.Limit,
									}),
									scrollable({
										minWidth: 1800,
									}),
								]
							}
						/>
					</Card.Body>
				</Card>
			</Content.Body>
		</Body>
	);
}
