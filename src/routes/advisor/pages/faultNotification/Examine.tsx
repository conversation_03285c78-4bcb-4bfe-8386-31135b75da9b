import React, { useState, useMemo } from 'react';
import { Button, Row, Card, Layout, H4, Col, Text, Checkbox, Justify, message as Message, Tag, StatusTip } from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { getDescribeFaultInform, pushFaultInform } from '@src/api/advisor/faultNotification';
import { getStorage } from "@src/utils/storage";


export function Examine() {
	const {Body, Content} = Layout;
	const history = useHistory();
	const [message, setMessage] = useState('');
	const [isConfirm, setIsConfirm] = useState(false);
	const oneFaultDesc = JSON.parse(getStorage('oneFaultDesc')) || {};
	const [loading, setLoading] = useState(false);
	useMemo(async () => {
		setLoading(true);
		try {
			const res = await getDescribeFaultInform({
				Id: oneFaultDesc.Id,
				Name: getStorage('engName'),
				AppId: oneFaultDesc.AppId,
				OnlyData: true
			});
			setMessage(res.Message);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		}
	}, []);
	// 推送消息
	const sendInfo = async () => {
		if (!isConfirm) {
			Message.warning({
				content: '请先确认推送信息'
			});
			return;
		}
		try {
			const res = await pushFaultInform({
				Id: oneFaultDesc.Id,
				Name: getStorage('engName'),
				AppId: oneFaultDesc.AppId,
				OnlyData: true,
				ShowError: true
			});
			Message.success({
				content: res.Message
			});
			history.push('/advisor/fault-notification');
		} catch (err) {}
	};
	return (<Body>
		<Content.Header
			showBackButton
			title={`推送故障通知-${oneFaultDesc.CustomerName}-（APPID:${oneFaultDesc.AppId}）`}
			onBackButtonClick={() => {
				history.goBack();
			}}
		></Content.Header>
		<Content.Body>
			<Card>
				<Card.Body>
					<div>
						<Row>
							<Col span={12}>
								<Row className={'desc-item'}>
									<Col span={4}>
										<Text theme="label">客户名称：</Text>
									</Col>
									<Col>
										<Text>{
											oneFaultDesc.CustomerName
										}</Text>
									</Col>
								</Row>
							</Col>
							<Col span={12}>
								<Row className={'desc-item'}>
									<Col span={4}>
										<Text theme="label">APPID：</Text>
									</Col>
									<Col>
										<Text>{
											oneFaultDesc.AppId
										}</Text>
									</Col>
								</Row>
							</Col>
						</Row>
						<Row>
							<Col span={12}>
								<Row className={'desc-item'}>
									<Col span={4}>
										<Text theme="label">UIN：</Text>
									</Col>
									<Col>
										<Text>{
											oneFaultDesc.Uin
										}</Text>
									</Col>
								</Row>
							</Col>
							<Col span={12}>
								<Row className={'desc-item'}>
									<Col span={4}>
										<Text theme="label">售后负责人：</Text>
									</Col>
									<Col>
										<Text>{
											oneFaultDesc.Owner
										}</Text>
									</Col>
								</Row>
							</Col>
						</Row>
						<Row>
							<Col span={12}>
								<Row className={'desc-item'} verticalAlign={'middle'}>
									<Col span={4}>
										<Text theme="label">涉及云产品：</Text>
									</Col>
									<Col style={{
										marginTop: '-3px'
									}}>
										{
											oneFaultDesc?.Product?.map((item, i)=>(<Tag theme={'primary'} key={i}>{item}</Tag>))
										}
									</Col>
								</Row>
							</Col>
							<Col span={12}>
								<Row className={'desc-item'}>
									<Col span={4}>
										<Text theme="label">故障名称：</Text>
									</Col>
									<Col>
										<Text>{
											oneFaultDesc.Name
										}</Text>
									</Col>
								</Row>
							</Col>
						</Row>
						<Row>
							<Col span={12}>
								<Row className={'desc-item'}>
									<Col span={4}>
										<Text theme="label">故障开始时间：</Text>
									</Col>
									<Col>
										<Text>{
											oneFaultDesc.StartTime
										}</Text>
									</Col>
								</Row>
							</Col>
							<Col span={12}>
								<Row className={'desc-item'}>
									<Col span={4}>
										<Text theme="label">故障恢复时间：</Text>
									</Col>
									<Col>
										<Text>{
											oneFaultDesc.EndTime
										}</Text>
									</Col>
								</Row>
							</Col>
						</Row>
						<Row className={'desc-item'}>
							<Col span={4}>
								<Text theme="label">故障描述：</Text>
							</Col>
							<Col>
								<Text>{
									oneFaultDesc.Desc
								}</Text>
							</Col>
						</Row>
						<Row className={'desc-item'}>
							<Col span={4}>
								<Text theme="label">故障原因：</Text>
							</Col>
							<Col>
								<Text>{
									oneFaultDesc.Reason
								}</Text>
							</Col>
						</Row>
						<H4 className={'info-t'}>客户群消息推送信息预览</H4>
						<Card>
							<Card.Body>
								{
									loading
									?
										<div
											style={{
												minHeight: "200px",
												display: 'flex',
												alignItems: 'center',
												justifyContent: 'center'
											}}
											className={'tips-wrap'}
										>
											<StatusTip  status={loading ? "loading" : "empty"}></StatusTip>
										</div>
										:
									message?.split('\n').map((item, i)=>{
									return (<div key={i} style={{
										marginBottom: '10px'
									}}>
										{item}
									</div>);
								})
								}
							</Card.Body>
						</Card>
						{
							oneFaultDesc.onlySee
								?
								<></>
								:
								<>
									<div className={'conform-wrap'}>
										<Checkbox
											value={isConfirm}
											onChange={(val)=>{
												setIsConfirm(val);
											}}
										>
											<Text theme={'primary'} verticalAlign={'top'}>我已认真审核以上消息，确认可以推送到客户的售后群。</Text>
										</Checkbox>
									</div>
									<Justify
										right={
											<>
												<Button type={'primary'} onClick={() => {
													sendInfo();
												}}>推送消息</Button>
												<Button onClick={()=>{
													history.goBack();
												}}>返回</Button>
											</>
										}
									></Justify>
								</>
						}
					</div>
				</Card.Body>
			</Card>
		</Content.Body>
	</Body>);
}
