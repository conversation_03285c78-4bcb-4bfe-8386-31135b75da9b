.desc-wrap{
	padding-top: 20px;
	.tea-col{
		&:nth-child(1){
			width: 110px;
		}
	}
}
.desc-item{
	.tea-col{
		&:nth-child(1){
			width: 110px;
		}
	}
}
.info-t{
	padding: 20px 0;
}
.conform-wrap{
	padding: 20px 0;
}
.notice-btn{
	cursor: pointer;
	margin-right: 10px;
	&:hover{
		text-decoration: underline;
	}
}
.fault-new-wrap{
	.tea-form__item{
		margin-right: 40px;
		.tea-form__label{
			padding-right: 10px;
			min-width: 120px;
		}
		.tea-form__controls>.tea-btn{
			margin-left: 10px;
		}
	}
	.tea-form{
		position: relative;
		.form-modal{
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			z-index: 2;
		}
		.tea-row{
			margin: 0;
		}
	}
	.desc-list-t{
		margin: 4px 0 20px 0;
	}
	.desc-view-t{
		margin: 20px 0;
	}
	.view-item{
		margin-top: 10px;
		span:nth-child(1){
			margin-right: 10px;
		}
	}
	.btn-wrap{
		display: flex;
		justify-content: center;
		button:nth-child(1){
			margin-right: 20px;
		}
	}
	.file-form-item{
		.tea-form__controls{
			padding-top: 0;
		}
		.tea-icon-valid{
			margin-top: 7px!important;
		}
	}
}
.resource-list{
	max-height: 75px;
	overflow-y: auto;
}
.fault-tag-wrap{
	span{
		white-space: nowrap!important;
	}
}



@media screen and (min-width: 900px){
	.fault-filter-wrap{
		.tea-grid__item-75{
			width: 50%;
		}
		.tea-grid__item-55{
			width: 50%;
		}
		.tea-grid__item-6{
			width: 20.83333%;
		}
		.tea-grid__item-2{
			width: 10.5%;
		}
		.tea-grid__item-3{
			width: 10.5%;
		}
		.tea-grid__item-22{
			width: 89.3%;
		}
	}
}
@media screen and (min-width: 1468px){
	.fault-filter-wrap{
		.tea-grid__item-75{
			width: 33.33333%;
		}
		.tea-grid__item-55{
			width: 33.33333%;
		}
		.tea-grid__item-2{
			width: 7%;
		}
		.tea-grid__item-3{
			width: 7%;
		}
		.tea-grid__item-22{
			width: 84.6%;
		}
	}
}
@media screen and (min-width: 1600px){
	.fault-filter-wrap{
		.tea-grid__item-75{
			width: 31.25%;
		}
		.tea-grid__item-55{
			width: 22.916%;
		}
		.tea-grid__item-6{
			width: 25%;
		}
		.tea-grid__item-2{
			width: 6.5%;
		}
		.tea-grid__item-3{
			width: 5.7%;
		}
		.tea-grid__item-22{
			width: 91.3%;
		}
	}
}
.tem-sel {
	.tea-dropdown-box {
		min-width: 260px!important;
	}
}
