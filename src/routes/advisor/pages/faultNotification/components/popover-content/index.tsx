import React, { useState, useEffect } from 'react';
import { Skeleton } from 'tdesign-react';
import { getFaultInformPopWin } from '@src/api/advisor/faultNotification';

import { getStorage } from '@src/utils/storage';
import './index.less';

export function PopoverContent({ record }: any) {
	const [isloading, setIsloading] = useState(false);
	const [informMessage, setInformMessage] = useState('');

	// 预览推送信息
	const perviewInformDetail = async () => {
		setIsloading(true);
		try {
			const res = await getFaultInformPopWin({
				Id: record.Id,
				Name: getStorage('engName'),
				AppId: record.AppId,
				OnlyData: true,
				Language: record?.InformLang ? record?.InformLang : 'zh',
			});
			setInformMessage(res.Message);
			setIsloading(false);
		} catch (err) {
			setIsloading(false);
		}
	};

	useEffect(() => {
		perviewInformDetail();
	}, []);

	return (<>
		<div className='popover-body'>
			{
				isloading
					? <Skeleton theme="paragraph">.</Skeleton>
					: informMessage?.split('\n').map((item, i) => (<div key={i} style={{
						marginBottom: '10px',
					}}>
						{item}
					</div>
					))
			}
		</div>
	</>);
}
