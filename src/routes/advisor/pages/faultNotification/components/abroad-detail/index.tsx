import React, { useState, useMemo } from 'react';
import {
	Table,
	Button,
	Row,
	Card,
	Layout,
	H4,
	Col,
	Select,
	Text,
	Input,
	Tag,
	Tooltip,
	Bubble,
	Modal,
} from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { getDescribeFaultDetail } from '@src/api/advisor/faultNotification';
import { FaultDetailInfo, DetailFilterParams } from '@src/types/advisor/faultNotification';
import { getStorage } from '@src/utils/storage';
import { InformModal } from '../inform-modal';
import { cloneDeep } from 'lodash';
import { StatusTip } from '@tea/component';
import { PopoverContent } from '../popover-content';
import { getProcessEnv } from '../../../../../../../app/utils';
import moment from 'moment';
import './index.less';
const { pageable } = Table.addons;

// 是否是国际站
const isAbroad = getProcessEnv() === 'production-abroad';

const informStatusList = ['未通知 Not Informed', '已通知 Informed'];
let initFilterData = [
	{
		label: 'APPID',
		value: 'appid',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: <>客户名称<br/>Customer</>,
		value: 'customer_name',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: <>售后负责人<br/>Owner</>,
		value: 'owner',
		type: 'input',
		span: [6, 18],
		virtualVal: getStorage('engName'),
		placeholder: '多人请用;分隔',
	},
	{
		label: <>通知状态<br/>Status</>,
		value: 'is_informed',
		type: 'select',
		options: [
			{
				text: '全部 All',
				value: '',
			},
			{
				text: informStatusList[0],
				value: '1',
			},
			{
				text: informStatusList[1],
				value: '2',
			},
		],
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: 'UIN',
		value: 'uin',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
];

// 国际站适配
if (isAbroad) {
	initFilterData = initFilterData.filter(i => i.value !== 'is_informed');
}

const initParams = {
	Id: 1001,
	Limit: 20,
	Offset: 0,
	Filters: [{
		Name: 'owner',
		Values: [getStorage('engName')],
	}],
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742,
	Name: getStorage('engName'),
};


export function AbroadDetail(match) {
	const { Body, Content } = Layout;
	const history = useHistory();
	const [loading, setLoading] = useState(false);
	const [records, setRecords] = useState<FaultDetailInfo>({
		Name: '',
		Product: [],
		StartTime: '',
		EndTime: '',
		Desc: '',
		Reason: '',
		TotalCount: 1,
		RequestId: '',
		AffectedLists: [],
		Advice: '',
		Tool: '',
		Status: '',
		ContentEn: {
			NameEn: '',
			DescEn: '',
			ReasonEn: '',
			AdviceEn: '',
			ToolEn: '',
		},
	});
	const [filterParams, setFilterParams] = useState<DetailFilterParams>({
		...cloneDeep(initParams),
		Id: parseInt(match.match.params.id),
	});
	const [filterData, setFilterData] = useState(cloneDeep(initFilterData));

	const [currentItemRecord, setCurrentItemRecord] = useState<any>();
	const [isShowModal, setIsShowModal] = useState(false);

	const columns = [
		{
			key: 'AppId',
			header: 'APPID',
		},
		{
			key: 'Uin',
			header: 'UIN',
		},
		{
			key: 'CustomerName',
			header: '客户名称 Customer',
			render(item) {
				return <Tooltip title={item.CustomerName}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.CustomerName
						}
					</span>
				</Tooltip>;
			},
		},
		{
			key: 'Owner',
			header: '售后负责人 Owner',
		},
		{
			key: 'Resources',
			header: '涉及资源列表 Instance',
			render: item => (
				<div className={'resource-list'}>
					{
						item.Resources?.map((val, i) => <div key={i} className={'fault-tag-wrap'}><Tag theme={'primary'}>{val}</Tag></div>)
					}
				</div>
			),
		},
		{
			key: 'IsInformed',
			header: '通知状态 Status',
			render: (item) => {
				const arr = item.IsInformed.split('|');
				return (
					(arr[0]
						? <Tag theme={arr[0] == 1 ? 'error' : 'success'}>
							{ arr[1] }
							<br/>
							{arr[2]}
						</Tag>
						: '-')
				);
			},
		},
		{
			key: 'InformTime',
			header: '通知时间 Inform Time',
			render: item => (item.InformTime ? item.InformTime : '-'),
		},
		{
			key: 'Informer',
			header: '审批人 Informer',
			render: item => (item.Informer ? item.Informer : '-'),
		},
		{
			key: 'AffectedNum1',
			header: '操作 Operation',
			render: item => (
				(
					item.IsActive
						?	<Text
							theme='primary'
							className='notice-btn'
							onClick={
								() => {
									setCurrentItemRecord({
										...records,
										...item,
										Id: parseInt(match.match.params.id),
									});
									setIsShowModal(true);
								}
							}
						>
							<>通知客户<br/>Inform</>
						</Text>
						: item.InformTime
							? <Bubble
								placement="left"
								trigger="hover"
								placementOffset='205'
								overlayClassName="popover-content-box"
								content={<PopoverContent record={{
									...records,
									...item,
									Id: parseInt(match.match.params.id),
								}}/>
								}
							>
								<Text theme='weak'>
									<>通知客户<br/>Inform</>
								</Text>
							</Bubble>
							: <Bubble content={getContent(item)} error={true}>
								<Text theme='weak'>
									<>通知客户<br/>Inform</>
								</Text>
							</Bubble>)
			),
		},
	];

	// 构造提示文案
	function getContent(data: any = {}) {
		const { EndTime, Status } = records || {};
		const { Owner, InformTime } = data || {};
		// 当前用户
		const rtx = getStorage('engName');
		// 是否有权限
		const hasPermission = Owner?.includes(rtx);
		// 是否 没有通知 && 已经恢复 && 大于1小时
		const isRestoredOneHour = !InformTime && Status?.split('|')?.[0] === '2' && moment().isAfter(moment(EndTime).add(1, 'hour'));
		if (!hasPermission) {
			return '没有权限，若有需求请联系售后owner';
		}

		if (isRestoredOneHour) {
			return '故障恢复超过1小时不能再操作，请通过线下合适方式与客户沟通';
		}
		return '';
	}

	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (item.Name == name) {
				item.Values = Array.isArray(val) ? [...val] : [val];
				notHasName = false;
			}
		});
		if (notHasName) {
			filterParams.Filters.push({
				Name: name,
				Values: Array.isArray(val) ? [...val] : [val],
			});
		}
		setFilterParams({
			...filterParams,
		});
	};

	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeFaultDetail(filterParams);
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		}
	};
	useMemo(() => {
		getTabData(filterParams);
	}, []);

	return (<Body>
		<Content.Header
			showBackButton
			title='Incident Details'
			onBackButtonClick={() => {
				history.push('/advisor/fault-notification');
			}}
		></Content.Header>
		<Content.Body>
			<Card>
				<Card.Body>
					<H4 style={{ marginBottom: 15 }}>
						<div style={{ marginBottom: 4 }}>详情</div>
						<div>Details</div>
					</H4>
					<div className='desc-wrap-box'>
						<Row>
							<Col span={4}>
								<Text theme="label">故障名称：</Text>
							</Col>
							<Col>
								<Text>{records?.Name}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">Title：</Text>
							</Col>
							<Col>
								<Text>{records?.ContentEn?.NameEn}</Text>
							</Col>
						</Row>
						<Row verticalAlign={'middle'}>
							<Col span={4}>
								<Text theme="label">涉及云产品：</Text>
							</Col>
							<Col style={{
								marginTop: '-3px',
							}}>
								{
									records.Product.map((item, i) => (<Tag theme={'primary'} key={i}>{item}</Tag>))
								}
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">故障开始时间 Start Time：</Text>
							</Col>
							<Col>
								<Text>{records.StartTime}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">故障恢复时间 End Time：</Text>
							</Col>
							<Col>
								<Text>{records.EndTime || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">故障描述：</Text>
							</Col>
							<Col>
								<Text>{records?.Desc}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">Description：</Text>
							</Col>
							<Col>
								<Text>{records?.ContentEn?.DescEn}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">故障原因：</Text>
							</Col>
							<Col>
								<Text>{records?.Reason}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">Reason：</Text>
							</Col>
							<Col>
								<Text>{records?.ContentEn?.ReasonEn}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">故障恢复建议：</Text>
							</Col>
							<Col>
								<Text>{records.Advice || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">Recover Advise：</Text>
							</Col>
							<Col>
								<Text>{records?.ContentEn?.AdviceEn || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">故障定位工具：</Text>
							</Col>
							<Col>
								<Text>{records.Tool || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">Relevant Tools：</Text>
							</Col>
							<Col>
								<Text>{records?.ContentEn?.ToolEn || '-'}</Text>
							</Col>
						</Row>
					</div>
					<H4 style={{ paddingTop: '20px' }}>
            影响列表
						<div style={{ marginTop: 4 }}>Customer list</div>
					</H4>
					<Row style={{
						marginTop: '20px',
					}}>
						{
							filterData.map((item, i) => (<Col span={6} key={i}>
								<Row verticalAlign={'middle'}>
									<Col span={item?.span[0]}>
										<Text theme="label" verticalAlign="middle">{item.label}</Text>
									</Col>
									<Col span={item?.span[1]}>
										{
											item.type == 'input'
												? <Input
													value={item.virtualVal}
													onChange={(val) => {
														item.virtualVal = val;
														setFilterData([
															...filterData,
														]);
														refreshAllParams(item.value, val);
													}
													}
													placeholder={item.placeholder}
												/>
												:	item.type == 'select'
													? <Select
														options={item.options}
														appearance="button"
														size='m'
														value={item.virtualVal}
														onChange={(val) => {
															item.virtualVal = val;
															setFilterData([
																...filterData,
															]);
															refreshAllParams(item.value, val);
														}
														}
													></Select>
													:	<></>
										}
									</Col>
								</Row>
							</Col>))
						}
					</Row>
					<Row style={{
						justifyContent: 'center',
						margin: '20px 10px 10px 10px',
					}}>
						<Button type='primary' onClick={() => {
							setFilterParams({
								...filterParams,
								Offset: 0,
							});
							getTabData({
								...filterParams,
								Offset: 0,
							});
						}}>
              Search
						</Button>
						<Button
							style={{
								marginLeft: '10px',
							}}
							onClick={() => {
								setFilterData(cloneDeep(initFilterData));
								setFilterParams({
									...cloneDeep(initParams),
									Id: parseInt(match.match.params.id),
								});
								getTabData({
									...cloneDeep(initParams),
									Id: parseInt(match.match.params.id),
								});
							}}>
                Reset
						</Button>
					</Row>
					<Table
						records = {
							records?.AffectedLists ? records?.AffectedLists : []
						}
						columns={columns}
						topTip={
							(loading || records?.AffectedLists?.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
						}
						addons={
							[
								pageable({
									recordCount: records?.TotalCount ? records?.TotalCount : 0,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setFilterParams({
											...filterParams,
											Limit: pageSize,
											Offset: (pageIndex - 1) * pageSize,
										});
										getTabData({
											...filterParams,
											Limit: pageSize,
											Offset: (pageIndex - 1) * pageSize,
										});
									},
									pageSizeOptions: [10, 20, 30, 50, 100, 200],
									pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
									pageSize: filterParams.Limit,
								}),
							]
						}
					/>
				</Card.Body>
			</Card>
			<Modal size={698} visible={isShowModal} onClose={() => setIsShowModal(false)}>
				<InformModal
					record={currentItemRecord}
					onModalClose={() => setIsShowModal(false)}
					onSuccess={() => {
						setIsShowModal(false);
						getTabData(filterParams);
					}}
				/>
			</Modal>
		</Content.Body>
	</Body>);
}
