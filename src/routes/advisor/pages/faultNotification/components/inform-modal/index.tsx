
import React, { useState, useEffect, useRef } from 'react';
import {
	Button,
	Checkbox,
	Select,
	Text,
	Input,
	Icon,
	DatePicker,
	TextArea,
	Modal,
	Form,
	Bubble,
	message,
} from '@tencent/tea-component';
import { Skeleton } from 'tdesign-react';
import { useForm, Controller } from 'react-hook-form';
import {
	pushFaultInform,
	getFaultInformPopWin,
	getTranslateDetail,
} from '@src/api/advisor/faultNotification';

import { getStorage } from '@src/utils/storage';
import { isEmpty, pick } from 'lodash';
import moment from 'moment';
import './index.less';

const enum langStatus {
	'CN' = 'zh',
	'EN' = 'en',
}

const langToLabelMap = {
	[langStatus.CN]: {
		Title: '通知客户',
		Name: '故障名称：',
		StartTime: '故障开始时间：',
		EndTime: '故障结束时间：',
		Desc: '故障描述：',
		Reason: '故障原因：',
		Advice: '故障恢复建议：',
		Tool: '故障定位工具：',
	},
	[langStatus.EN]: {
		Title: 'Inform Customer',
		Name: 'Title:',
		StartTime: 'Start Time:',
		EndTime: 'End Time:',
		Desc: 'Description:',
		Reason: 'Reason:',
		Advice: 'Recover Advise:',
		Tool: 'Relevant Tools:',
	},
};

const langOptions = [
	{ text: 'English', value: langStatus.EN },
	{ text: '中文', value: langStatus.CN },
];
export function InformModal({ record, onModalClose, onSuccess }: any) {
	const [lang, setLang] = useState<any>(langStatus.CN);
	const [isloading, setIsloading] = useState(false);
	const [sendLoading, setSendLoading] = useState(false);
	const [popoverVisible, setPopoverVisible] = useState(false);
	const [informMessage, setInformMessage] = useState('');
	const [isConfirm, setIsConfirm] = useState(false);
	const [translateLoading, setTranslateLoading] = useState(false);
	const initialValuesRef = useRef(pick(record, ['Name', 'Desc', 'Reason', 'Advice', 'Tool']));
	const {
		control,
		getValues,
		setValue,
		handleSubmit,
		formState: { errors },
	} = useForm({ mode: 'all' });

	const isCnLang = lang === langStatus.CN;
	const getStatus = (fieldState) => {
		if (fieldState?.error?.message) {
			return 'error';
		}
		if (!fieldState.isDirty) {
			return undefined;
		}
		return fieldState.invalid ? 'error' : 'success';
	};

	const onSubmitHandle = async (values) => {
		setPopoverVisible(true);
		perviewInformDetail(values);
	};

	// 预览推送信息
	const perviewInformDetail = async (values = {}) => {
		setIsloading(true);
		try {
			const res = await getFaultInformPopWin({
				Id: record.Id,
				Name: getStorage('engName'),
				AppId: record.AppId,
				OnlyData: true,
				Language: lang,
				Content: pick(values, ['Name', 'Desc', 'Reason', 'Advice', 'Tool']),
			});
			setInformMessage(res.Message);
			setIsloading(false);
		} catch (err) {
			setIsloading(false);
		}
	};

	// 发送推送故障通知
	const handleInformSubmit = () => {
		setSendLoading(true);
		pushFaultInform({
			Id: record.Id,
			Name: getStorage('engName'),
			AppId: record.AppId,
			Language: lang,
			OnlyData: true,
			ShowError: true,
		}).then((res) => {
			setSendLoading(false);
			if (res) {
				if (res.Error) {
					const msg = res.Error.Message;
					message.error({ content: msg });
					return;
				}
				message.success({ content: '推送成功' });
				setPopoverVisible(false);
				onSuccess();
			}
		});
	};

	const handleFormSetValue = (lang = langStatus.CN) => {
		Object.keys(langToLabelMap[lang]).forEach((key) => {
			if (!isEmpty(record)) {
				if (key === 'StartTime' || key === 'EndTime') {
					if (record[key]) {
						setValue(key, moment(record[key]));
					}
				} else {
					if (lang === langStatus.CN) {
						setValue(key, record[key]);
					} else {
						setValue(key, record?.ContentEn?.[`${key}En`]);
					}
				}
			}
		});
	};

	// 语言切换and翻译
	const handleLangChange = async (value) => {
		setLang(value);
		if (value === langStatus.EN) {
			const values = getValues();
			const currentValues = pick(values, ['Name', 'Desc', 'Reason', 'Advice', 'Tool']);
			const hasChanges = Object.keys(currentValues)
				.some(key => currentValues[key] !== initialValuesRef.current[key]);
			if (hasChanges) {
				setTranslateLoading(true);
				try {
					const res = await getTranslateDetail({
						Id: record.Id,
						AppId: record.AppId,
						OnlyData: true,
						SourceContent: currentValues,
					});
					const resRecord = res?.TargetContent;
					Object.keys(langToLabelMap[lang]).forEach((key) => {
						if (!isEmpty(record)) {
							if (key === 'StartTime' || key === 'EndTime') {
								if (record[key]) {
									setValue(key, moment(record[key]));
								}
							} else {
								setValue(key, resRecord[key]);
							}
						}
					});
					setTranslateLoading(false);
				} catch (err) {
					setTranslateLoading(false);
				}
			} else {
				handleFormSetValue(langStatus.EN);
			}
		} else {
			handleFormSetValue();
		}
		setPopoverVisible(false);
	};

	useEffect(() => {
		handleFormSetValue();
	}, [record]);

	return (<>
		<Modal.Body>
			<div className="fault-notification-inform-title">
				<h3>{langToLabelMap[lang]?.Title}</h3>
				<div>
					<Select
						style={{ width: 150, marginRight: 25 }}
						appearance="button"
						options={langOptions}
						value={lang}
						onChange={value => handleLangChange(value)}
					/>
				</div>
			</div>
			<div className="fault-notification-inform-form">
				<Form
					layout="fixed"
					fixedLabelWidth={100}
				>
					<Controller
						name="Name"
						control={control}
						rules={{
							validate: value => (!value ? (isCnLang ? '请输入故障名称' : 'Please enter the fault name') : undefined),
						}}
						render={({ field, fieldState }) => (
							<Form.Item
								required
								label={langToLabelMap[lang]?.Name}
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.Name?.message}
							>
								<Input
									style={{ width: '100%' }}
									{...field}
									size="s"
									disabled={translateLoading}
									autoComplete="off"
								/>
							</Form.Item>
						)}
					/>
					<Controller
						name="StartTime"
						control={control}
						render={({ field, fieldState }) => (
							<Form.Item
								required
								label={langToLabelMap[lang]?.StartTime}
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.StartTime?.message}
							>
								<DatePicker showTime {...field} disabled placeholder={isCnLang ? '选择时间' : 'Choose a time'}/>
							</Form.Item>
						)}
					/>
					<Controller
						name="EndTime"
						control={control}
						render={({ field, fieldState }) => (
							<Form.Item
								label={langToLabelMap[lang]?.EndTime}
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.EndTime?.message}
							>
								<DatePicker showTime {...field} disabled placeholder={isCnLang ? '选择时间' : 'Choose a time'} />
							</Form.Item>
						)}
					/>
					<Controller
						name="Desc"
						control={control}
						rules={{
							validate: value => (!value ? (isCnLang ? '请输入故障描述' : 'Please enter a description of the fault') : undefined),
						}}
						render={({ field, fieldState }) => (
							<Form.Item
								required
								label={langToLabelMap[lang]?.Desc}
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.Desc?.message}
							>
								<TextArea
									style={{ width: '100%' }}
									{...field}
									disabled={translateLoading}
								/>
							</Form.Item>
						)}
					/>
					<Controller
						name="Reason"
						control={control}
						rules={{
							validate: value => (!value ? (isCnLang ? '请输入故障原因' : 'Please enter the reason for the failure')  : undefined),
						}}
						render={({ field, fieldState }) => (
							<Form.Item
								required
								label={langToLabelMap[lang]?.Reason}
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.Reason?.message}
							>
								<TextArea
									style={{ width: '100%' }}
									{...field}
									disabled={translateLoading}
								/>
							</Form.Item>
						)}
					/>
					<Controller
						name="Advice"
						control={control}
						render={({ field, fieldState }) => (
							<Form.Item
								label={langToLabelMap[lang]?.Advice}
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.Advice?.message}
							>
								<TextArea
									style={{ width: '100%' }}
									{...field}
									disabled={translateLoading}
								/>
							</Form.Item>
						)}
					/>
					<Controller
						name="Tool"
						control={control}
						render={({ field, fieldState }) => (
							<Form.Item
								label={langToLabelMap[lang]?.Tool}
								status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
								message={errors.Tool?.message}
							>
								<TextArea
									style={{ width: '100%' }}
									{...field}
									disabled={translateLoading}
								/>
							</Form.Item>
						)}
					/>
				</Form>
			</div>
		</Modal.Body>
		<Modal.Footer>
			<Bubble
				placement="top"
				visible={popoverVisible}
				trigger="contextMenu"
				overlayClassName="popover-box"
				content={
					<div className='popover-content'>
						<div className='popover-title'>
							<h3>
								{
									isCnLang ? '请确认通知内容！' : 'All Translation Confirmed? Notification will be sent!'
								}
							</h3>
							<Icon
								type="close"
								style={{ cursor: 'pointer' }}
								onClick={() => {
									setIsConfirm(false);
									setPopoverVisible(false);
								}}
							/>
						</div>
						<div className='popover-body'>
							{
								isloading
									? <Skeleton theme="paragraph">.</Skeleton>
									: <>
										<div>
											{
												informMessage?.split('\n').map((item, i) => (<div key={i} style={{
													marginBottom: '10px',
												}}>
													{item}
												</div>
												))
											}
										</div>
										<div className={'conform-wrap'}>
											<Checkbox
												value={isConfirm}
												onChange={(val) => {
													setIsConfirm(val);
												}}
											>
												<Text theme='primary' verticalAlign='top'>
													{
														isCnLang
															? '我已认真审核以上消息，确认可以推送到客户的售后群。'
															: 'I’ve reviewed the message and translation above and confirmed this can be sent to customer.'
													}
												</Text>
											</Checkbox>
										</div>
									</>
							}
						</div>
						<div className='popover-footer'>
							<Button
								loading={isloading || sendLoading}
								type="primary"
								style={{ marginRight: 10 }}
								disabled={!isConfirm}
								onClick={handleInformSubmit}
							>
								{
									isCnLang ? '发送' : 'Send'
								}
							</Button>
							<Button type="weak" onClick={() => {
								setIsConfirm(false);
								setPopoverVisible(false);
							}}>
								{
									isCnLang ? '取消' : 'Cancel'
								}
							</Button>
						</div>
					</div>
				}
			>
				<Button
					type="primary"
					disabled={translateLoading}
					onClick={handleSubmit(onSubmitHandle) as any}
				>
					{
						isCnLang ? '预览' : 'Preview'
					}
				</Button>
			</Bubble>
			<Button type="weak" onClick={() => onModalClose()}>
				{
					isCnLang ? '取消' : 'Cancel'
				}
			</Button>
		</Modal.Footer>
	</>);
}
