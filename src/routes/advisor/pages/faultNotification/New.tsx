import React, { useState, useMemo, useEffect } from 'react';
import { Table, Button, Card, Layout, Select, Text, Input, Tag, DatePicker, SelectMultiple, Upload, H3, Alert, message, ExternalLink, StatusTip, Form, Bubble } from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { useForm, useField } from 'react-final-form-hooks';
import {
	getDescribeFaultDetail,
	createFaultNotice,
	getDescribeFaultTemplate,
	getDescribeProductList,
	DescribeFaultProductTemplates
} from '@src/api/advisor/faultNotification';
import { DetailFilterParams } from "@src/types/advisor/faultNotification";
import { getStorage } from "@src/utils/storage";
import { cloneDeep, get, debounce } from "lodash";
import moment from "moment";

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}

const formInfoList: any = [
	{
		name: 'Title',
		label: '故障名称',
		placeholder: '请输入故障名称',
		required: true,
		errorMessage: '请输入故障名称',
		type: 'input'
	},
	{
		name: 'Product',
		label: '涉及云产品',
		placeholder: '请选择云产品',
		required: true,
		errorMessage: '请选择云产品',
		type: 'mutipleSelect',
		options: []
	},
	{
		name: 'Template',
		label: '故障通知模板',
		placeholder: '请选择',
		errorMessage: '请选择故障通知模板',
		type: 'select',
		options: []
	},
	{
		name: 'Status',
		label: '故障状态',
		placeholder: '请选择故障状态',
		required: true,
		errorMessage: '请选择故障状态',
		type: 'select',
		options: [
			{
				text: '故障中',
				value: 1
			},
			{
				text: '已恢复',
				value: 2
			}
		]
	},
	{
		name: 'StartTime',
		label: '故障开始时间',
		placeholder: '选择时间或输入',
		required: true,
		errorMessage: '请选择或输入故障开始时间',
		type: 'time'
	},
	{
		name: 'EndTime',
		label: '故障恢复时间',
		placeholder: '选择时间或输入',
		required: true,
		errorMessage: '请选择或输入故障恢复时间',
		type: 'time'
	},
	// {
	// 	name: 'Person',
	// 	label: '关注人',
	// 	placeholder: '请填写关注人',
	// 	required: true,
	// 	errorMessage: '请填写关注人',
	// 	type: 'input'
	// },
	{
		name: 'Desc',
		label: '故障描述(对外)',
		placeholder: '同步给客户的故障描述，请谨慎填写，注意对外话术(故障描述和故障原因合计不超过160字）',
		required: true,
		errorMessage: '请填写故障描述',
		type: 'textarea'
	},
	{
		name: 'Reason',
		label: '故障原因(对外)',
		placeholder: '同步给客户的故障原因，请谨慎填写，注意对外话术(故障描述和故障原因合计不超过160字）',
		required: true,
		errorMessage: '请填写故障原因',
		type: 'textarea'
	},
	{
		name: 'Advice',
		label: '故障恢复建议(对内)',
		placeholder: '用于指导售后负责人如何协助客户定位及处理故障，不会直接对客户展示，请尽量详细描述(最多400字）',
		required: false,
		errorMessage: '请填写故障恢复建议',
		type: 'textarea',
		length: 400
	},
	{
		name: 'Tool',
		label: '故障定位工具(对内)',
		placeholder: '如果有内部系统或工具可以帮助售后同学查询故障详情，请在此描述，不会直接对客户展示(最多400字）',
		required: false,
		errorMessage: '请填写故障定位工具',
		type: 'textarea',
		length: 400
	},
	{
		name: 'File',
		label: '上传影响列表',
		placeholder: '请上传影响列表',
		required: true,
		errorMessage: '请上传影响列表',
		type: 'file'
	}
]
const originFormData = {};
formInfoList.forEach((item) => {
	originFormData[item.name] = '';
});

const initParams = {
	AppId: 1253985742,
	Name: getStorage('engName'),
	Limit: 10,
	Offset: 0,
	ShowError: true,
	OnlyData: true
};


export function New(match) {
	const baseURL = getStorage('site') === 'sinapore' ? `${window.location.origin}/2` : `${window.location.origin}/1`;
	const { Body, Content } = Layout;
	const {
		scrollable,
		pageable
	} = Table.addons;
	const history = useHistory();
	const [uploading, setUploading] = useState(false);
	const [records, setRecords] = useState<any>({
		AffectedLists: []
	});
	const formId = match.match.params.id;
	const isUpdate = match.match.params.id != 0;
	const [loading, setLoading] = useState(false);
	const formFieldInfo = {};
	const [formRenderList, setFormRenderList] = useState(cloneDeep(formInfoList));
	const [productInfo, setProductInfo] = useState<any>({});
	const [templateInfo, setTemplateInfo] = useState({});
	const [filterParams, setFilterParams] = useState<DetailFilterParams>({
		...cloneDeep(initParams),
		Id: parseInt(formId)
	});
	const [currentStatus, setCurrentStatus] = useState(0);
	const [changeType, setChangeType] = useState('default');
	// 是否有超长的资源
	const [isLong, setIsLong] = useState(false)
	// 根据资源最大长度降序排序
	function sortByLength(list) {
		let cloneData = [];
		cloneData = [...list].sort((a, b) => {
			// 计算每个客户的最大资源长度
			// eslint-disable-next-line max-len
			const getMaxLength = (resources) =>  resources.length > 0 ? Math.max(...resources.map(r => r.length)) : 0;
			const maxA = getMaxLength(a.Resources);
			const maxB = getMaxLength(b.Resources);
			return maxB - maxA; // 降序排列
		  });
		return cloneData;
	}
	const getTabData = async (filterParams, isInit) => {
		setLoading(true);
		try {
			const res = await getDescribeFaultDetail(filterParams);
			res.StartTime = moment(res.StartTime);
			res.EndTime = res.EndTime ? moment(res.EndTime) : '';
			if (isInit) {
				setRecords(res);
			} else {
				setRecords({
					TotalCount: res.TotalCount,
					AffectedLists: [
						...cloneDeep(res.AffectedLists),
					]
				});
			}
			setLoading(false);
		} catch (err) {
			setLoading(false);
		}
	};
	const [templates, setTemplates] = useState([]);
	// 重置表单渲染项
	useMemo(() => {
		const list = cloneDeep(formInfoList);
		for (let i = 0; i < list.length; ++i) {
			if (list[i]['name'] == 'Product') {
				list[i]['options'] = [...cloneDeep(productInfo.list || [])];
			}
			if (list[i]['name'] == 'Template' && templates) {
				list[i]['options'] = templates.map((item) => {
					return {
						text: item.Name,
						value: JSON.stringify(item)
					};
				});
			}
		}
		setFormRenderList([
			...list
		]);
	}, [productInfo, templates]);

	useEffect(() => {
		setIsLong(false);
		const result = records?.AffectedLists?.
			some(item => item.Resources.some(id => id.length > 255));
		setIsLong(result);
	}, [records]);

	const [version, setVersion] = useState('');
	// 提交表单
	const onSubmit = async (value) => {
		const params = {
			...value
		};
		try {
			params['StartTime'] = params['StartTime']?.format('YYYY-MM-DD HH:mm:ss');
			params['EndTime'] = params['EndTime'] ? params['EndTime']?.format('YYYY-MM-DD HH:mm:ss') : '';
			params['Id'] = parseInt(formId);
			params['AppId'] = 1253985742;
			params['Name'] = getStorage('engName');
			params['OnlyData'] = true;
			params['ShowError'] = true;
			params['Product'] = [params['Product']];
			params['VersionId'] = version;
			const res = await createFaultNotice(params);
			message.success({
				content: res.Message
			});
			history.push('/advisor/fault-notification');
		} catch (err) { };
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {
			...originFormData,
		},
		validate: (formInfo) => {
			const validateFormInfo = {};
			formInfoList.forEach((item) => {
				if (item.name == 'EndTime' && formInfo.Status == 1) {
					validateFormInfo[item.name] = undefined;
				} else if (item.name == 'Advice' || item.name == 'Tool' || item.name == 'Template') {
					validateFormInfo[item.name] = undefined;
				} else if (item.name == 'Desc' || item.name == 'Reason') {
					if (formInfo['Desc'] || formInfo['Reason']) {
						const pureWords: string = (formInfo['Desc'] + formInfo['Reason']).replace(/[\r\n\s]/g, "");
						if ((pureWords.length) > 160) {
							validateFormInfo['Desc'] = '输入字符超长，故障描述和故障建议合计不超过160字';
							validateFormInfo['Reason'] = '输入字符超长，故障描述和故障建议合计不超过160字';
						} else {
							validateFormInfo[item.name] = formInfo[item.name] ? undefined : item.errorMessage;
						}
					} else {
						validateFormInfo[item.name] = formInfo[item.name] ? undefined : item.errorMessage;
					}
				} else {
					validateFormInfo[item.name] = formInfo[item.name] ? undefined : item.errorMessage;
				}
			});
			return validateFormInfo;
		},
	});
	formInfoList.forEach((item) => {
		formFieldInfo[item.name] = useField(item.name, form);
	});
	// 匹配选择的产品
	const getCurrntProductName = (name, type?: string) => {
		if (!name) {
			return;
		}
		let currentName;
		for (const key in productInfo?.ProductDict) {
			if (type == 'name') {
				if (productInfo.ProductDict[key].indexOf(name) != -1) {
					currentName = key;
				}
			} else {
				if (key == name) {
					currentName = productInfo.ProductDict[key];
				}
			}
		}
		return currentName;
	};
	useMemo(() => {
		if (isUpdate && records.Status) {
			// const selProductList = [];
			// for (const key in productInfo?.ProductDict) {
			// 	if (records?.Product?.indexOf(productInfo?.ProductDict[key]) != -1) {
			// 		selProductList.push(key);
			// 	}
			// }
			let initProductName = getCurrntProductName(records?.Product?.[0], 'name');
			if (!initProductName) {
				initProductName = records?.Product?.[0];
				setChangeType('input');
			}
			form.initialize({
				...records,
				Status: parseInt(records?.Status?.split('|')[0]),
				File: 'had',
				Title: records?.Name,
				Product: initProductName
			});
			setCurrentStatus(parseInt(records?.Status?.split('|')[0]));
		}
	}, [records, productInfo]);
	const handleSuccess = (result, { file }) => {
		if (!isUpdate) {
			setRecords({
				AffectedLists: sortByLength(result.Response.Affected),
				TotalCount: result.Response.TotalCount
			});
		} else {
			setRecords({
				TotalCount: result.Response.TotalCount,
				AffectedLists: [
					// ...cloneDeep(records.AffectedLists),
					...sortByLength(result.Response.Affected)
				]
			});
		}
		setFilterParams({
			...filterParams,
			Offset: 0
		});
		setVersion(result.Response.VersionId);
		setUploading(false);
	};
	const handleError = (error, { xhr }) => {
		const responseText = xhr.responseText.indexOf('Response') != -1 ? get(JSON.parse(xhr.responseText), 'Response.Error.Message') : xhr.responseText;
		message.error({
			content: responseText
		});
		setUploading(false);
	};
	// 获取产品信息
	useMemo(async () => {
		if (!getStorage('productInfo')) {
			try {
				const res = await getDescribeProductList({
					OnlyData: true,
					ShowError: true,
					AppId: 1253985742
				});
				const list = [];
				for (const key in res.ProductDict) {
					list.push({
						text: res.ProductDict[key],
						value: key
					});
				}
				res.list = list;
				setProductInfo(cloneDeep(res));
			} catch (err) { };
		} else {
			setProductInfo(JSON.parse(getStorage('productInfo')));
		}
	}, []);
	// 获取预览模版
	useMemo(async () => {
		const res = await getDescribeFaultTemplate({
			AppId: 1253985742,
			OnlyData: true,
			ShowErroe: true
		});
		const obj = {};
		res?.TemplateLists.forEach((item) => {
			obj[item.Status] = item
		});
		setTemplateInfo(obj)
	}, []);
	// 更新表单信息
	useMemo(async () => {
		if (isUpdate) {
			getTabData(filterParams, true);
		}
	}, [])
	// 下载文件上传模版
	// const downloadTem = ()=>{
	// 	window.open('https://cos.ap-guangzhou.myqcloud.com/iwiki-prod-1258638997/attachment/ff57e4204baea13535c19b7dc9e69d48fb6f133421a88a0c4ae45f62bbf47129/121807af-6eec-11ed-b4c9-2ad6b66f91bb?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKID0kFrDOSW8ZfWfYIp8d16C5j7FTaRmNWP%2F20221128%2Fap-guangzhou%2Fs3%2Faws4_request&X-Amz-Date=20221128T072347Z&X-Amz-Expires=300&X-Amz-SignedHeaders=host&iwiki-user=coolran&response-content-disposition=attachment%3Bfilename%3D%22Template.xlsx%22&X-Amz-Signature=ead98820f5f344dcf7ca9b90a1ce52a42b9ebcbea6443d5b909353ae3b69e666');
	// };
	// 格式化时间
	const changeTime = (time, type) => {
		const date = new Date(time);
		const setTime = (type == 'forward' ? (date.getTime() + 1 * 1000) : (date.getTime() - 1 * 1000));
		const newDate = new Date(setTime);
		return `${newDate.getFullYear()}-${newDate.getMonth() + 1 >= 10 ? newDate.getMonth() + 1 : `0${newDate.getMonth() + 1}`}-${newDate.getDate() >= 10 ? newDate.getDate() : `0${newDate.getDate()}`} ${newDate.getHours() >= 10 ? newDate.getHours() : `0${newDate.getHours()}`}:${newDate.getMinutes() >= 10 ? newDate.getMinutes() : `0${newDate.getMinutes()}`}:${newDate.getSeconds() >= 10 ? newDate.getSeconds() : `0${newDate.getSeconds()}`}`;
	};

	const getDescribeFaultProductTemplates = async (productName) => {
		form.change('Template', '');
		try {
			const res = await DescribeFaultProductTemplates({
				AppId: 1253985742,
				Product: productName,
				OnlyData: true,
				ShowError: true
			});
			setTemplates(res.Templates);
		} catch (e) { }
	}
	const getTemDebounce = useMemo(() => {
		return debounce(getDescribeFaultProductTemplates, 300);
	}, []);
	return (<Body className={'fault-new-wrap'}>
		<Content.Header
			showBackButton
			title={isUpdate ? '更改故障通知' : '新建故障通知'}
			onBackButtonClick={() => {
				history.push('/advisor/fault-notification');
			}}
		></Content.Header>
		<Content.Body>
			<Card>
				<form onSubmit={handleSubmit}>
					<Card.Body>
						<Form
							layout={'inline'}
						>
							{
								// records?.Status?.split('|')[0] == 2 && <div className="form-modal"></div>
							}
							{
								formRenderList.map((item, i) => (
									item.type == 'input'
										?
										<Form.Item
											key={i}
											required={item.required}
											label={item.label}
											status={!records.Status && item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
											message={!records.Status && item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
										>
											<Input disabled={isUpdate ? true : false} {...formFieldInfo[item['name']].input} autoComplete="off" placeholder={item.placeholder} />
										</Form.Item>
										:
										item.type == 'textarea'
											?
											<div key={i}>
												<Form.Item
													required={item.required}
													label={item.label}
													status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
													message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
												>
													<Input.TextArea
														{...formFieldInfo[item['name']].input}
														autoComplete="off"
														placeholder={item.placeholder}
														style={{
															width: '820px',
															height: '80px'
														}}
													/>
												</Form.Item>
											</div>
											:
											item.type == 'time'
												?
												item['name']=='StartTime'
												?
												<Bubble content='格式：yyyy-mm-dd hh:MM:ss'>
														<Form.Item
															key={i}
															required={item.required}
															label={item.label}
															status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
															message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
														>
															<DatePicker
																disabled={isUpdate ? true : false}
																{...formFieldInfo[item['name']].input}
																placeholder={item.placeholder}
																showTime
																range={[
																	moment(values['EndTime'] || moment())
																		.subtract(365, "d")
																		.startOf("d"),
																	values['EndTime']
																		?
																		moment(changeTime(values['EndTime'].format("YYYY-MM-DD HH:mm:ss"), 'backword'))
																		:
																		moment().endOf('s')
																]}
															/>
														</Form.Item>
													</Bubble>
													:
													<Bubble content='格式：yyyy-mm-dd hh:MM:ss'>
														<Form.Item
															key={i}
															required={values.Status == 2 ? true : false}
															label={item.label}
															status={item.required && values.Status == 2 ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
															message={item.required && values.Status == 2 ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
														>
															<DatePicker
																disabled={values.Status == 2 && currentStatus != 2 ? false : true}
																{...formFieldInfo[item['name']].input}
																showTime
																placeholder={item.placeholder}
																range={[
																	values['StartTime']
																		?
																		moment(changeTime(moment(values['StartTime'].format("YYYY-MM-DD HH:mm:ss")), 'forward'))
																		:
																		moment(moment())
																			.subtract(365, "d")
																			.startOf("d"),
																	moment(moment())
																		.endOf("s")]}
															/>
														</Form.Item>
													</Bubble>
												:
												item.type == 'mutipleSelect'
													?
													<Form.Item
														key={i}
														required={item.required}
														label={item.label}
														status={!records.Status && item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
														message={!records.Status && item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
													>
														<Select
															matchButtonWidth
															disabled={isUpdate ? true : false}
															value={formFieldInfo[item['name']].input.value}
															onChange={(val) => {
																setChangeType('default');
																form.change('Product', val);
																getDescribeFaultProductTemplates(val);
															}}
															options={item.options}
															appearance="button"
															// appearance={'pure'}
															// 	button={<Input
															// 		disabled={isUpdate ? true : false}
															// 		value={changeType == 'input' ? values.Product : getCurrntProductName(values.Product)}
															// 		onChange={(val)=>{
															// 			setChangeType('input');
															// 			form.change('Product', val);
															// 			getTemDebounce(val);
															// 		}}
															// 		onClick={(e)=>{
															// 			let num = 0;
															// 			const timer = setInterval((el)=>{
															// 				num++;
															// 				el.focus();
															// 				if (num == 20) {
															// 					clearInterval(timer);
															// 				} else if (document.querySelector('.tea-input--search') && num < 10) {
															// 					num = 10;
															// 				}
															// 			}, 10, e.currentTarget);
															// 		}}
															// 	/>
															// }
															size='m'
															searchable
														/>
													</Form.Item>
													:
													item.type == 'select'
														?
														<Form.Item
															key={i}
															required={item.required}
															label={item.label}
															status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
															message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
														>
															<Select
																overlayClassName={item.name == 'Template' ? 'tem-sel' : ''}
																matchButtonWidth
																disabled={(currentStatus == 2 || (templates?.length == 0 && item.name == 'Template')) ? true : false}
																{...formFieldInfo[item['name']].input}
																options={item.options}
																appearance="button"
																size='m'
																onChange={(val) => {
																	if (item.name == 'Template') {
																		const result = JSON.parse(val);
																		form.change(item.name, val);
																		for (const key in result) {
																			form.change(key, result[key]);
																		}
																	} else {
																		form.change(item.name, val);
																	}
																}}
															/>
														</Form.Item>
														:
														item.type == 'file'
															?
															<div key={i}>
																<Form.Item
																	required={item.required}
																	label={item.label}
																	status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																	message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																	className='file-form-item'
																>
																	<Upload
																		// @ts-ignore
																		value={formFieldInfo[item['name']].input.value}
																		onChange={formFieldInfo[item['name']].input.onChange}
																		action={`${baseURL}/fault`}
																		onStart={
																			() => {
																				setUploading(true);
																			}
																		}
																		onSuccess={handleSuccess}
																		onError={handleError}
																		headers={{
																			Action: 'DescribeFaultResources',
																			FaultId: parseInt(formId)
																		}}
																	>
																		<Button disabled={false} htmlType={'button'} loading={uploading}>点击上传</Button>
																	</Upload>
																	{/* <Button */}
																	{/* 	htmlType={'button'} */}
																	{/* 	type={'primary'} */}
																	{/* 	onClick={downloadTem} */}
																	{/* > */}
																	{/* 	下载表格模版 */}
																	{/* 	<svg style={{verticalAlign: 'sub', marginLeft: '3px'}} width="16px" height="16px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> */}
																	{/* 		<path d="M13 3L13 12.5858L16.5 9.08579L17.9142 10.5L12 16.4142L6.08579 10.5L7.5 9.08579L11 12.5858L11 3L13 3ZM4.5 14V19H19.5V14H21.5V21H2.5V14H4.5Z" fill="#fff"></path> */}
																	{/* 	</svg> */}
																	{/* </Button> */}
																	<ExternalLink style={{
																		verticalAlign: 'middle',
																		marginLeft: '10px'
																	}} href={'https://iwiki.woa.com/pages/viewpage.action?pageId=4007253707'}>
																		下载表格模版
																	</ExternalLink>
																</Form.Item>
															</div>
															:
															<></>
								))
							}
						</Form>
						<H3 className={'desc-list-t'}>
							共有<Text theme={'primary'}>{records.TotalCount ? records.TotalCount : 0}</Text>家大客户受到影响
						</H3>
						<Table
							records={
								records.AffectedLists ? records.AffectedLists : []
							}
							columns={[
								{
									key: "AppId",
									header: "APPID"
								},
								{
									key: "CustomerName",
									header: "客户名称"
								},
								{
									key: "Owner",
									header: "售后负责人"
								},
								{
									key: "Resources",
									header: <>
										涉及资源列表
										{isLong && <Text style={{fontSize: 11, marginLeft: 16}} theme="danger">单条记录异常，请保持影响列表每行1个资源实例！</Text>}
									</>,
									render: (item: any) => (
										<div className={'resource-list'}>
											{/* 长度排序，超长的标红 */}
											{item?.Resources?.
												sort((a, b) => b.length - a.length)?.
												map((el, i) => (<Tag theme={el?.length > 255 ? 'error' : 'primary'} key={i}>{el}</Tag>))}
										</div>
									)
								},
							]}
							topTip={
								(loading || records.AffectedLists.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
							}
							addons={[
								scrollable({
									maxHeight: 300
								}),
								pageable({
									recordCount: records.TotalCount ? records.TotalCount : 0,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setFilterParams({
											...filterParams,
											Limit: pageSize,
											Offset: (pageIndex - 1) * pageSize
										});
										if (!values.File || values.File == 'had') {
											getTabData({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize
											}, false);
										}
									},
									pageSizeOptions: [10, 20, 30, 50, 100, 200],
									pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
									pageSize: filterParams.Limit
								}),
							]}
						/>
						<H3 className={'desc-view-t'}>
							客户群消息推送信息预览
						</H3>
						<Card>
							<Card.Body>
								{
									templateInfo[1]
										?
										<>
											<Text>{templateInfo[values.Status ? values.Status : 1]?.Start
												.replace('${AppId}', records.AffectedLists[0]?.AppId ? records.AffectedLists[0].AppId : 'XXXID')
												.replace('${CustomerName}', records.AffectedLists[0]?.CustomerName ? records.AffectedLists[0].CustomerName : 'XXX名称')
												.replace('${Product}', values.Product ? ` ${getCurrntProductName(values.Product) || values.Product}` : 'XXX')
											}</Text>
											<div className='view-item'>
												<Text>{templateInfo[values.Status ? values.Status : 1]?.StartTime.replace('${StartTime}', values.StartTime ? values.StartTime.format("YYYY-MM-DD HH:mm:ss") : '--')}</Text>
											</div>
											{
												values.Status == 2 && <div className='view-item'>
													<Text>{templateInfo[values.Status ? values.Status : 1]?.EndTime.replace('${EndTime}', values.EndTime ? values.EndTime.format("YYYY-MM-DD HH:mm:ss") : '--')}</Text>
												</div>
											}
											<div className='view-item'>
												<Text>{templateInfo[values.Status ? values.Status : 1]?.Desc}</Text>
												<Text>{values.Desc}</Text>
											</div>
											<div className='view-item'>
												<Text>{templateInfo[values.Status ? values.Status : 1]?.Reason}</Text>
												<Text>{values.Reason}</Text>
											</div>
											<div className='view-item'>
												<Text>{templateInfo[values.Status ? values.Status : 1]?.Resources}</Text>
												<Text style={{
													position: 'relative',
													top: '-3px'
												}}>
													{
														records.AffectedLists[0]?.Resources?.map((item, i) => {
															if (i < 6) {
																return <Tag theme={'primary'} key={i}>{item}</Tag>;
															} else {
																return <></>;
															}
														})
													}
													{
														records.AffectedLists[0]?.Resources?.length > 6 && (<span>...</span>)
													}
												</Text>
											</div>
											<div className='view-item'>
												<Text>{templateInfo[values.Status ? values.Status : 1]?.End}</Text>
											</div>
										</>
										:
										<div style={{
											textAlign: 'center'
										}}>
											<StatusTip.LoadingTip></StatusTip.LoadingTip>
										</div>
								}
							</Card.Body>
						</Card>
						<Alert type="error" style={{ marginTop: 8 }}>
							特别注意：请仔细核对故障信息，提交后将通知客户的售后负责人。若售后负责人审核通过，以上内容会直接推送至客户售后群
						</Alert>
						<div className='btn-wrap'>
							<Button htmlType="submit" type={'primary'}>提交</Button>
							<Button htmlType={'button'} onClick={() => {
								history.push('/advisor/fault-notification');
							}}>返回</Button>
						</div>
					</Card.Body>
				</form>
			</Card>
		</Content.Body>
	</Body>);
}