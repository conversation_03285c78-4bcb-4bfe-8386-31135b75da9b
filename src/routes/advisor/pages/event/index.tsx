import React, { useEffect, useMemo, useState } from 'react';
import { Layout } from '@tencent/tea-component';
import { NotPermission } from '@src/routes/NotPermission';
import { useHistory } from '@tea/app';
import { cloneDeep } from "lodash";
import './index.less';
import { getStorage } from "@src/utils/storage";
import { Link } from "react-router-dom";
import {
	Button,
	Card,
	Col,
	DatePicker,
	Input,
	Row,
	Select,
	StatusTip,
	Table,
	TagSelect,
	Text,
	SelectMultiple,
	Bubble,
	Timeline,
	Icon,
	message,
	Modal,
	Tooltip
} from "@tea/component";
import { ConfigGroup, DescribeEventListsParams } from "@src/types/advisor/commonEvent";
import { getDescribeProductList } from "@src/api/advisor/faultNotification";
import { getDescribeEventLists, updateEvent } from "@src/api/advisor/commonEvent";
import { reportVisitPage } from '@src/utils/report';
import moment from "moment";
import { statusMap } from './constants';

let timer;

const statusOptions = [];

for (const key in statusMap) {
	statusOptions.push({
		text: statusMap[key],
		value: key
	});
}

const initFilterData = [
	{
		label: '事件ID',
		value: 'id',
		type: 'input',
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: '事件名称',
		value: 'title',
		type: 'input',
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: '状态',
		value: 'status',
		type: 'mutipleSelect',
		options: [
			...statusOptions
		],
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: '创建人',
		value: 'creater',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '售后负责人',
		value: 'owner',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
		placeholder: '多人请用;分隔'
	},
	{
		label: '客户APPID',
		value: 'appid',
		type: 'input',
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: '创建时间',
		value: 'start_time,end_time',
		type: 'mutipleTime',
		span: [6, 18],
		virtualVal: ''
	},
	{
		label: '涉及云产品',
		value: 'product',
		type: 'tagSelect',
		options: [
			{
				text: '全部',
				value: ''
			}
		],
		span: [6, 18],
		virtualVal: []
	},
];

const initParams = {
	Limit: 10,
	Offset: 0,
	Filters: [],
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742,
	Name: getStorage('engName')
};

export function CommonEvent() {
	const history = useHistory();
	const search = location.search;
	const hasAppid = search.indexOf('appid') != -1;
	const searchVal = hasAppid ? search.split('=')[1] : '';
	const initOriginFilterData = cloneDeep(initFilterData);
	const [searchAppId, setSearchAppId] = useState('');
	if (hasAppid) {
		history.replace(location.pathname);
	}
	if (hasAppid) {
		if (!searchAppId) {
			setSearchAppId(searchVal);
		}
	}
	initOriginFilterData[5]['virtualVal'] = searchAppId;
	const {Body, Content} = Layout;
	const [permission, setPermission] = useState(0);
	const CheckPermission = () => {
		const menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			// 判断是否存在
			const tmp = menuItems.filter(i => {
				if (history.location.pathname.includes(i.route)) {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
		// 临时测试
		setPermission(1);
	};
	// 持续从localStorage获取菜单列表
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);
	const tabs = [
		{ id: "index", label: "告警查询" },
		{ id: "manage", label: "触达管理" }
	];
	const [filterData, setFilterData] = useState<any>([...cloneDeep(initOriginFilterData)]);
	const [loading, setLoading] = useState(false);
	// 持续查询开关
	const [continueSwitch, setContinueSwitch] = useState<boolean>(true);
	const [visible, setVisible] = useState(false);
	const [currentItem, setCurrentItem] = useState<any>({});

	const [columns, setColumns] = useState<any>([
		{
			key: "Id",
			header: "事件ID",
			width: 100,
			render: item=>(<Link to={`/advisor/common-event/${item.EventType === 1 ? 'self-developed-detail' : 'detail'}/${item.Id}`}>{item.Id}</Link>)
		},
		{
			key: "Title",
			header: "隐患事件标题",
			render(item) {
				return <Tooltip title={item.Title}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.Title
						}
					</span>
				</Tooltip>;
			}
		},
		{
			key: "Product",
			header: "状态",
			className: 'resource-list',
			render: (item) => (
				<>
					<Bubble
						placement="right"
						trigger="hover"
						placementOffset="10 + 20%"
						style={{ maxWidth: 600 }}
						content={
							<Timeline mode="vertical" dashedLine style={{ height: "auto" }}>
								{
									statusOptions.map((val, i) => {
										const mateItem = item.Approval.find((el) => {
											return el.Type == val.value;
										});
										return  <Timeline.Item
											key={i}
											label={
												<>
													<Text>{val.text}</Text>
													{
														mateItem && <Bubble content={
															<Text>
																{
																	mateItem.Handler
																}
																{
																	mateItem.IsApproved ? '-已审批' : '-审批中'
																}
															</Text>
														} placement={"right-end"}>
															<Icon style={{ marginLeft: 4 }} type="info" />
														</Bubble>
													}
												</>
											}
											style={{ height: 35 }}
											theme={item.Status > val.value ? "success" : "default"}
											icon={item.Status == val.value && item.Status != 50
												? <Icon type="loading" />
												: (item.Status > val.value || item.Status == 50) ? <Icon type="success" /> : <Icon type="cur" />}
										/>;
									})
								}
							</Timeline>
						}
					>
						<Button type={'link'}>
							{
								statusMap[item.Status]
							}
						</Button>
					</Bubble>
				</>
			),
		},
		{
			key: "Creater",
			header: "创建人"
		},
		{
			key: "CreateTime",
			header: "创建时间",
			render: item => (item.CreateTime ? item.CreateTime : '-')
		},
		// {
		// 	key: "Duration",
		// 	header: "通知方式",
		// 	render: item => (
		// 		<Text>{item.Duration}</Text>
		// 	),
		// },
		{
			key: "AffectedNum1",
			header: "操作",
			// fixed: 'right',
			render: (item) => {
				return (
					<>
						<Link to={`/advisor/common-event/${item.EventType === 1 ? 'self-developed-detail' : 'detail'}/${item.Id}`}>查看详情</Link>
						{
							item.IsEditActive
							?
								<Link style={{
									margin: '0 10px'
								}} to={`/advisor/common-event/new/${item.Id}?EventType=${item.EventType || 0}`}>编辑</Link>
								:
								<Button style={{
									margin: '0 10px'
								}} type={'link'} disabled={true}>编辑</Button>
						}
						<Button disabled={!item.IsDeleteActive} type={'link'} onClick={()=>{
							setVisible(true);
							setCurrentItem(item);
						}}>删除</Button>
					</>
				);
			},
		},
	]);
	const [records, setRecords] = useState<ConfigGroup>({
		EventLists: []
	});
	const { RangePicker } = DatePicker;
	const [filterParams, setFilterParams] = useState<DescribeEventListsParams>({
		...cloneDeep(initParams),
		Filters: [
			{
				Name: 'appid',
				Values: [searchVal]
			}
		]
	});
	const resetFilterData = (init?: boolean)=>{
		let list;
		if (init) {
			list = cloneDeep(initOriginFilterData);
		} else {
			list = cloneDeep(initFilterData);
		}
		for (let i = 0;i < list.length; ++i) {
			if (list[i]['value'] == 'product') {
				list[i]['options'] = [...cloneDeep(productList)];
				break;
			}
		}
		setFilterData([
			...list
		]);
	};
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeEventLists(filterParams);
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				if (item.Name == nameList[0]) {
					item.Values = [val[0]];
					notHasName = false;
				}
				if (item.Name == nameList[1]) {
					item.Values = [val[1]];
					notHasName = false;
				}
			} else {
				if (item.Name == name) {
					item.Values = Array.isArray(val) ? [...val] : [val];
					notHasName = false;
				}
			}
		});
		if (notHasName) {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				filterParams.Filters.push({
					Name: nameList[0],
					Values: [val[0]]
				});
				filterParams.Filters.push({
					Name: nameList[1],
					Values: [val[1]]
				});
			} else {
				filterParams.Filters.push({
					Name: name,
					Values: Array.isArray(val) ? [...val] : [val]
				});
			}
		}
		setFilterParams({
			...filterParams
		});
	};
	const [productList, setProductList] = useState([]);
	const getAllProduct = async () => {
		try {
			const res = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742
			});
			const list = [];
			for (const key in res.ProductDict) {
				list.push({
					text: res.ProductDict[key],
					value: key
				});
			}
			res.list = list;
			setProductList(list);
		} catch (err) {};
	};
	useMemo(()=>{
		getAllProduct();
	}, []);
	useMemo(()=>{
		if (productList.length > 0) {
			resetFilterData(true);
		}
	}, [productList]);
	useEffect(() => {
		// 开启持续查询
		getTabData(filterParams);
		reportVisitPage({
			isaReportMeunName: '产品隐患服务',
		});
	}, []);
	// 分页
	const {pageable, scrollable, columnsResizable} = Table.addons;
	
	const delItem = async (id)=>{
		try {
			const res = await updateEvent({
				Id: id,
				Status: -50,
				Name: getStorage('engName'),
				AppId: 1253985742,
				ShowError: true,
				OnlyData: true
			});
			message.success({
				content: '删除成功'
			});
			getTabData(filterParams);
		} catch (err) {};
	}
	return (
		<Body className={'touch-wrap'}>{
			permission === 0
				?
				<div></div>
				:
				permission === 2
					?
					<NotPermission />
					:
					<>
						<Body>
							<Content.Header
								title={'主动服务通知'}
							></Content.Header>
							<Content.Body>
								<Card>
									<Card.Body className='fault-filter-wrap'>
										<Row>
											{
												filterData.map((item, i) => {
													return item.type != 'tagSelect' && (<Col span={i == 2 || i == 6 ? 75 : 55} key={i}>
														<Row verticalAlign={"middle"}>
															<Col span={item?.span[0]}>
																<Text theme="label" verticalAlign="middle">{item.label}</Text>
															</Col>
															<Col span={item?.span[1]}>
																{
																	item.type == 'input' ?
																		<Input onChange={(val) => {
																			item.virtualVal = val;
																			setFilterData([
																				...filterData
																			]);
																			refreshAllParams(item.value, val);
																		}
																		}
																			   value={item.virtualVal}
																			   placeholder={item.placeholder}
																		/>
																		:
																		item.type == 'mutipleTime' ?
																			<RangePicker
																				showTime
																				value={item.virtualVal}
																				range={[
																					moment()
																						.subtract(365, "d")
																						.startOf("d"),
																					moment().endOf("d"),
																				]}
																				onChange={(val) => {
																					item.virtualVal = [...val];
																					setFilterData([
																						...filterData
																					]);
																					refreshAllParams(item.value, [
																						val[0].format("YYYY-MM-DD HH:mm:ss"),
																						val[1].format("YYYY-MM-DD HH:mm:ss")
																					]);
																				}
																				}
																			/>
																			:
																			item.type == 'select'
																				?
																				<Select
																					options={item.options}
																					appearance="button"
																					size='m'
																					value={item.virtualVal}
																					onChange={(val) => {
																						item.virtualVal = val;
																						setFilterData([
																							...filterData
																						]);
																						refreshAllParams(item.value, val);
																					}
																					}
																				></Select>
																				:
																				item.type == 'mutipleSelect'
																					?
																					<SelectMultiple
																						options={item.options}
																						appearance="button"
																						size='m'
																						value={item.virtualVal}
																						allOption={{
																							value: "all",
																							text: "全部",
																						}}
																						onChange={(val) => {
																							item.virtualVal = val;
																							setFilterData([
																								...filterData
																							]);
																							refreshAllParams(item.value, val);
																						}
																						}
																					></SelectMultiple>
																					:
																				<></>
																}
															</Col>
														</Row>
													</Col>);
												})
											}
										</Row>
										<Row verticalAlign={"middle"} style={{
											marginTop: '10px'
										}}>
											<Col span={3}>
												<Text theme="label" verticalAlign="middle">{filterData[filterData.length - 1].label}</Text>
											</Col>
											<Col>
												<TagSelect
													options={filterData[filterData.length - 1].options}
													value={filterData[filterData.length - 1].virtualVal}
													onChange={(val) => {
														filterData[filterData.length - 1].virtualVal = [...val];
														setFilterData([
															...filterData
														]);
														refreshAllParams(filterData[filterData.length - 1].value, val);
													}
													}
													style={{
														maxWidth: '1000px'
													}}
												></TagSelect>
											</Col>
										</Row>
										<Row style={{
											justifyContent: 'center',
											margin: '20px 10px 10px 10px'
										}}>
											<Button type='primary' onClick={() => {
												setFilterParams({
													...filterParams,
													Offset: 0
												});
												getTabData({
													...filterParams,
													Offset: 0
												});
											}}>查询</Button>
											<Button style={{
												marginLeft: '10px'
											}} onClick={()=>{
												resetFilterData();
												setFilterParams({
													...cloneDeep(initParams)
												});
												getTabData({
													...cloneDeep(initParams)
												});
											}}>重置</Button>
											<Button
												disabled={!records.IsCreateActive}
												tooltip={!records.IsCreateActive && records.Tip}
												style={{
													marginLeft: '10px'
												}} onClick={()=>{
												history.push('/advisor/common-event/new/0');
											}}>新建隐患事件</Button>
											<a href="https://beacon.woa.com/datatalk/tarc/dashboard/257933?isFullscreen=true&isShared=true&share_token=2543050ad230c751a109eacbafa4e4ae_PERMANENT_tarc_v_hlchuang_257933_LOGIN&menuIds=menu_g7i25hdg" target={'_blank'}>
												<Button
													style={{
														marginLeft: '10px'
													}}>查看运营报表</Button>
											</a>
										</Row>
									</Card.Body>
								</Card>
								<Card>
									<Card.Body>
										<Table
											records = {
												records.EventLists ? records.EventLists : []
											}
											columns={columns}
											topTip={
												(loading || records.EventLists.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
											}
											addons={
												[
													columnsResizable({
														onResizeEnd: columns => {
															setColumns(columns);
														},
														minWidth: 100,
														maxWidth: 1000,
														columns: ['Product']
													}),
													pageable({
														recordCount: records.TotalCount ? records.TotalCount : 0,
														onPagingChange: ({pageIndex, pageSize}) => {
															setFilterParams({
																...filterParams,
																Limit: pageSize,
																Offset: (pageIndex - 1) * pageSize
															});
															getTabData({
																...filterParams,
																Limit: pageSize,
																Offset: (pageIndex - 1) * pageSize
															});
														},
														pageSizeOptions: [10, 20, 30, 50, 100, 200],
														pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
														pageSize: filterParams.Limit
													}),
													// scrollable({
													// 	minWidth: 1800
													// })
												]
											}
										/>
										<Modal visible={visible} caption="注意" onClose={()=>{
											setVisible(false);
										}}>
											<Modal.Body>
												<Text style={{ whiteSpace: "pre-line" }}>
													{["请确认要删除 ", <a>{`${currentItem.Id}|${currentItem.Title}`}</a>, " ? \n\n - 删除后不可恢复，本事件终止。"]}
												</Text>
											</Modal.Body>
											<Modal.Footer>
												<Button type="primary" onClick={()=>{
													setVisible(false);
													delItem(currentItem.Id);
												}}>
													确定
												</Button>
												<Button type="weak" onClick={()=>{
													setVisible(false);
												}}>
													取消
												</Button>
											</Modal.Footer>
										</Modal>
									</Card.Body>
								</Card>
							</Content.Body>
						</Body>
					</>
		}
		</Body>
	);
}
