import React, { useState, useMemo } from 'react';
import {
	Table,
	Row,
	Card,
	Layout,
	H4,
	Col,
	Select,
	Text,
	Input,
	Tag,
	Form,
	Radio,
	Bubble,
	Icon,
	Alert,
	DatePicker,
} from '@tencent/tea-component';
import { useForm, useField } from 'react-final-form-hooks';
import { useHistory } from '@tea/app';
import {
	createEventApproval,
	getDescribeEventDetail,
	modifyEventComment,
	CreateEventDownload,
	DescribeEventDownload,
} from '@src/api/advisor/commonEvent';
import { DescribeEventDetailParams } from '@src/types/advisor/commonEvent';
import { getStorage } from '@src/utils/storage';
import { cloneDeep } from 'lodash';
import moment from 'moment';
import { Button, message, StatusTip, Tooltip } from '@tea/component';

const stateMap: any = {
	'22-n': '处理中',
	'5-n': '待确认接单',
	'3-n': '已结单',
	'-1-n': '未建单',
};

const stateOptions = [];
for (const key in stateMap) {
	stateOptions.push({
		text: stateMap[key],
		value: key.split('-n')[0],
	});
}

const initFilterData = [
	{
		label: 'APPID',
		value: 'appid',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: '客户名称',
		value: 'customer_name',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '售后负责人',
		value: 'owner',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
		placeholder: '多人请用;分隔',
	},
	{
		label: '工单状态',
		value: 'ticket_status',
		type: 'select',
		span: [6, 18],
		virtualVal: '',
		placeholder: '请选择工单状态',
		options: stateOptions,
	},
	{
		label: 'UIN',
		value: 'uin',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
];

const initParams = {
	Id: 1001,
	Limit: 20,
	Offset: 0,
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742,
	Name: getStorage('engName'),
	Filters: [],
};

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return 'validating';
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? 'error' : 'success';
}

enum PushType {
	PushNow = '1',
	PushLaterTime = '2',
}

const DATA_FORMATE = 'YYYY-MM-DD HH:mm:ss';
export function Detail(match) {
	const formId = match.match.params.id;
	const { Body, Content } = Layout;
	const history = useHistory();
	const [loading, setLoading] = useState(false);
	const [records, setRecords] = useState<any>({
		Name: '',
		Product: [],
		StartTime: '',
		EndTime: '',
		Desc: '',
		Reason: '',
		TotalCount: 1,
		RequestId: '',
		CustomerLists: [],
		Advice: '',
		Tool: '',
	});
	const [originRecords, setOriginRecords] = useState<any>({});
	const [filterParams, setFilterParams] = useState<DescribeEventDetailParams>({
		...cloneDeep(initParams),
		Id: parseInt(match.match.params.id),
	});
	const [filterData, setFilterData] = useState(cloneDeep(initFilterData));
	// 是否复杂诉求
	const [isComplex, setIsComplex] = useState('2');
	const [isApproved, setIsApproved] = useState('1');
	const [isPushNow, setIsPushNow] = useState(PushType.PushNow);
	const [pushTime, setPushTime] = useState(moment());

	const onSubmitComment = async (value) => {
		const params = {
			...value,
		};
		try {
			params.Id = parseInt(formId);
			// params['AppId'] = appId;
			params.Name = getStorage('engName');
			params.OnlyData = true;
			params.ShowError = true;
			// params['Product'] = [params['Product']];
			const res = await modifyEventComment(params);
			message.success({
				content: res.Message,
			});
			getTabData(filterParams);
		} catch (err) {
			setRecords(cloneDeep(originRecords));
		};
	};
	const columns = [
		{
			key: 'AppId',
			header: 'APPID',
		},
		{
			key: 'Uin',
			header: 'UIN',
		},
		{
			key: 'CustomerName',
			header: '客户名称',
			render(item) {
				return <Tooltip title={item.CustomerName}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.CustomerName
						}
					</span>
				</Tooltip>;
			},
		},
		{
			key: 'Owner',
			header: '售后负责人',
		},
		{
			key: 'Resources',
			header: '涉及资源列表',
			render: item => (
				<div style={
					{
						display: 'flex',
					}
				}>
					<Text
						copyable
						style={
							{
								fontSize: 0,
								marginRight: '5px',
								display: 'flex',
								alignItems: 'center',
							}
						}
					>{item.Resources.some(v => v instanceof Object) ? item.Resources.map(v => v?.ResourceId).join('\r\n') : item.Resources.join('\r\n')}</Text>
					<div className={'resource-list'}>
						{
							item.Resources?.map((val, i) => <div key={i} className={'fault-tag-wrap'}>
								<Tooltip title={val instanceof Object ? val?.ResourceId : val}>
									<Tag theme={'primary'} className={'def-tag'}>{val instanceof Object ? val?.ResourceId : val}</Tag>
								</Tooltip>
							</div>)
						}
					</div>
				</div>
			),
		},
		{
			key: 'Status',
			header: '工单状态',
			render: item => (item.TicketStatus ? item.TicketUrl ? (<a href={item.TicketUrl} target={'_blank'}>{item.TicketStatus}</a>) : item.TicketStatus : '-'),
		},
		{
			key: 'Comment',
			header: <>
				备注
				<Bubble
					content={
						'点击输入框外侧或按Enter键保存'
					}
				>
					<Icon type="info" />
				</Bubble>
			</>,
			render: (item, rowKey, recordIndex) => <Input
				placeholder={'请输入备注'}
				value={item.Comment}
				onKeyDown={(e) => {
					if (e.key == 'Enter') {
						(e.target as any).blur();
					}
				}}
				onBlur={
					(e) => {
						if (item.Comment === originRecords?.CustomerLists[recordIndex]?.Comment) {
							return;
						}
						onSubmitComment({
							Comment: item.Comment,
							AppId: item.AppId,
						});
						(e.target as any).blur();
					}
				}
				onChange={
					(val) => {
						item.Comment = val;
						setRecords(cloneDeep(records));
					}
				}
			/>,
		},
	];

	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (item.Name == name) {
				item.Values = Array.isArray(val) ? [...val] : [val];
				notHasName = false;
			}
		});
		if (notHasName) {
			filterParams.Filters.push({
				Name: name,
				Values: Array.isArray(val) ? [...val] : [val],
			});
		}
		setFilterParams({
			...filterParams,
		});
	};

	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeEventDetail(filterParams);
			setRecords(res);
			setOriginRecords(cloneDeep(res));
			setLoading(false);
		} catch (err) {
			setLoading(false);
		}
	};
	const [clickExport, setClickExport] = useState(false);
	const createEventDownload = async () => {
		setClickExport(true);
		try {
			const res = await CreateEventDownload({
				Id: parseInt(formId),
				OnlyData: true,
				ShowError: true,
			});
			getDescribeEventDownload(res.TaskId);
		} catch (err) {
			setClickExport(false);
		}
	};
	const getDescribeEventDownload = async (taskId) => {
		try {
			const res = await DescribeEventDownload({
				Id: parseInt(formId),
				TaskId: taskId,
				OnlyData: true,
				ShowError: true,
			});
			if (res.TaskStatus === 'success') {
				const a = document.createElement('a');
				a.href = res.CosUrl;
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
				setClickExport(false);
			} else {
				setTimeout((taskId) => {
					getDescribeEventDownload(taskId);
				}, 5000, taskId);
			}
		} catch (err) {
			setClickExport(false);
		}
	};
	useMemo(() => {
		getTabData(filterParams);
	}, []);
	// 分页
	const { pageable, scrollable } = Table.addons;

	// 提交表单
	const onSubmit = async (value) => {
		const params = {
			...value,
		};
		try {
			params.NoticeTime = params.NoticeTime?.format('YYYY-MM-DD HH:mm:ss');
			params.Id = parseInt(formId, 10);
			params.AppId = 1253985742;
			params.Name = getStorage('engName');
			if (originRecords.Status === 20) {
				if (isPushNow === PushType.PushLaterTime && pushTime.isBefore(moment())) {
					message.warning({ content: '推送时间不能早于当前时间' });
					return;
				}
				params.IsComplex = isComplex == '1';
				params.PushTime = isPushNow === PushType.PushNow ? '' : pushTime.format('YYYY-MM-DD HH:mm:ss');
			}
			params.IsApproved = isApproved == '1';
			params.OnlyData = true;
			params.ShowError = true;
			// params['Product'] = [params['Product']];
			const res = await createEventApproval(params);
			message.success({
				content: res.Message,
			});
			history.push('/advisor/common-event');
		} catch (err) { };
	};
	const { form, handleSubmit, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		validate: (formInfo) => {
			const validateFormInfo: any = {};
			if (isApproved == '2') {
				validateFormInfo.Reason = !formInfo.Reason ? '请输入驳回原因' : undefined;
			}
			return validateFormInfo;
		},
	});
	const reasonField = useField('Reason', form);
	return (<Body>
		<Content.Header
			showBackButton
			title={'隐患事件详情'}
			onBackButtonClick={() => {
				history.push('/advisor/common-event');
			}}
		></Content.Header>
		<Content.Body>
			<Card>
				<Card.Body>
					{
						originRecords.Status === 10
						&& <Alert>
						【专项审批原则】（技术向审批）
							<br />&nbsp;&nbsp;a.风险是否属实——直接风险产品方确认，产品风险真实存在
							<br />&nbsp;&nbsp;b.风险收敛整改方案完整——风险整改技术方案合理，具有可执行性
						</Alert>
					}
					{
						originRecords.Status === 20
						&& <Alert>【TAM 审批原则】（客户向审批）
							<br />&nbsp;&nbsp;a.产品侧整改技术方案是否清晰完整
							<br />&nbsp;&nbsp;b.明确该产品风险治理所带来的客户业务收益，能否提升客户业务稳定性/健壮性
						</Alert>
					}
					<H4>事件描述</H4>
					<div className={'desc-wrap'}>
						<Row>
							<Col span={4}>
								<Text theme="label">事件名称：</Text>
							</Col>
							<Col>
								<Text>{records.Title}</Text>
							</Col>
						</Row>
						<Row verticalAlign={'middle'}>
							<Col span={4}>
								<Text theme="label">涉及云产品：</Text>
							</Col>
							<Col style={{
								marginTop: '-3px',
							}}>
								{
									<Tag theme={'primary'}>{records.Product}</Tag>
								}
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">通知方式：</Text>
							</Col>
							<Col>
								<Text>内部企微群、应用号、{records.NotificationType === 0 ? '工单（给1线）、云顾问控制台和官网首页小卡片' : (records.NotificationType === 1 ? '工单（给1线）' : '云顾问控制台和官网首页小卡片')}</Text>
							</Col>
						</Row>
						{records.RiskLevel !== -1 && <Row>
							<Col span={4}>
								<Text theme="label">风险级别：</Text>
							</Col>
							<Col>
								<Text>{records.RiskLevel === 2 ? '中风险' : '高风险'}</Text>
							</Col>
						</Row>}
						<Row>
							<Col span={4}>
								<Text theme="label">对内描述信息：</Text>
							</Col>
							<Col>
								<Text>{records.PrivateContent || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">对外问题描述：</Text>
							</Col>
							<Col>
								<Text>{records.PublicDesc || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">对外处理建议：</Text>
							</Col>
							<Col>
								<Text>{records.PublicAdvice || '-'}</Text>
							</Col>
						</Row>
					</div>
					<H4 style={{
						paddingTop: '20px',
						paddingBottom: '20px',
					}}>影响列表</H4>
					<Row>
						{
							filterData.map((item, i) => (<Col span={6} key={i}>
								<Row verticalAlign={'middle'}>
									<Col span={item?.span[0]}>
										<Text theme="label" verticalAlign="middle">{item.label}</Text>
									</Col>
									<Col span={item?.span[1]}>
										{
											item.type == 'input'
												? <Input
													value={item.virtualVal}
													onChange={(val) => {
														item.virtualVal = val;
														setFilterData([
															...filterData,
														]);
														refreshAllParams(item.value, val);
													}
													}
													placeholder={item.placeholder}
												/>
												:	item.type == 'select'
													? <Select
														options={item.options}
														appearance="button"
														size='m'
														value={item.virtualVal}
														onChange={(val) => {
															item.virtualVal = val;
															setFilterData([
																...filterData,
															]);
															refreshAllParams(item.value, val);
														}
														}
													/>
													:	<></>
										}
									</Col>
								</Row>
							</Col>))
						}
					</Row>
					<Row style={{
						justifyContent: 'center',
						margin: '20px 10px 10px 10px',
					}}>
						<Button type='primary' onClick={() => {
							setFilterParams({
								...filterParams,
								Offset: 0,
							});
							getTabData({
								...filterParams,
								Offset: 0,
							});
						}}>查询</Button>
						<Button style={{
							marginLeft: '10px',
						}} onClick={() => {
							setFilterData(cloneDeep(initFilterData));
							setFilterParams({
								...cloneDeep(initParams),
								Id: parseInt(match.match.params.id),
							});
							getTabData({
								...cloneDeep(initParams),
								Id: parseInt(match.match.params.id),
							});
						}}>重置</Button>
						<Button
							disabled={clickExport}
							style={{
								marginLeft: '10px',
							}} onClick={() => {
								createEventDownload();
							}}>
							{
								clickExport ? '导出中' : '导出'
							}
						</Button>
						<Button
							style={{
								marginLeft: '10px',
							}} onClick={() => {
								window.open(`https://beacon.woa.com/datatalk/tarc/dashboard/257933?isFullscreen=true&event_id_value=${formId}`);
							}}>
							查看该隐患运营数据
						</Button>
					</Row>
					<Table
						records={
							records.CustomerLists ? records.CustomerLists : []
						}
						columns={columns}
						topTip={
							(loading || records.CustomerLists.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
						}
						addons={
							[
								pageable({
									recordCount: records.TotalCount ? records.TotalCount : 0,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setFilterParams({
											...filterParams,
											Limit: pageSize,
											Offset: (pageIndex - 1) * pageSize,
										});
										getTabData({
											...filterParams,
											Limit: pageSize,
											Offset: (pageIndex - 1) * pageSize,
										});
									},
									pageSizeOptions: [10, 20, 30, 50, 100, 200],
									pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
									pageSize: filterParams.Limit,
								}),
								scrollable({
									maxHeight: records.IsApprovalActive ? 400 : 'inherit',
								}),
							]
						}
					/>
					{
						records.IsApprovalActive
						&& <form
							onSubmit={handleSubmit}
						>
							<H4 style={{
								paddingBottom: '20px',
							}}>审批</H4>
							<Form
								className={'event-form'}
							>
								{originRecords.Status === 20 && <Form.Item
									required={true}
									label={<span>是否复杂诉求:</span>}
									tips={<>
										一般服务诉求：与客户依照服务诉求持续进行沟通，确认问题闭环后，按流程进行结单。（结单工单中的选填字段可忽略不填）<br />
										复杂服务诉求：与客户完成初轮沟通后并填写完全量字段，明确客户诉求后及时与客户Owner同步，后续将由客户Owner立项跟进处置。
									</>}
								>
									<Radio.Group value={isComplex} onChange={value => setIsComplex(value)}>
										<Radio name="1">是</Radio>
										<Radio name="2">否</Radio>
									</Radio.Group>
								</Form.Item>}
								<Form.Item
									required={true}
									label={<span>是否同意:</span>}
								>
									<Radio.Group value={isApproved} onChange={value => setIsApproved(value)}>
										<Radio name="1">同意</Radio>
										<Radio name="2">驳回</Radio>
									</Radio.Group>
								</Form.Item>
								{
									isApproved === '2' && <Form.Item
										required={true}
										label={'驳回原因:'}
										status={getStatus(reasonField.meta, validating)}
										message={(getStatus(reasonField.meta, validating) === 'error' && reasonField.meta.error)}
									>
										<Input.TextArea
											{...reasonField.input}
											size={'l'}
											placeholder={'请输入驳回原因'}
										/>
									</Form.Item>
								}
								{
									originRecords.Status === 20
									&& <Form.Item
										required={true}
										label={<span>推送时间:</span>}
										className={isPushNow === PushType.PushLaterTime ? 'push-time' : ''}
									>
										<Radio.Group value={isPushNow} onChange={(value: any) => setIsPushNow(value)}>
											<Radio name="1">立即推送</Radio>
											<Radio name="2" className={isPushNow === PushType.PushLaterTime ? 'push-later-time' : ''} >
												<span>定时推送</span>
												{
													isPushNow === PushType.PushLaterTime
												&& <DatePicker
													className='push-later-time-picker'
													format={DATA_FORMATE}
													range={[moment(), moment().add(3, 'year')]}
													showTime={{ format: 'HH:mm:ss' }}
													value={pushTime}
													onChange={value => setPushTime(value)}
												/>
												}
											</Radio>
										</Radio.Group>
									</Form.Item>
								}
							</Form>
							<div className='btn-wrap' style={{
								marginTop: '30px',
								display: 'flex',
								paddingLeft: '110px',
							}}>
								<Button htmlType={'submit'} type={'primary'} onClick={() => {

								}}>提交</Button>
							</div>
						</form>
					}
				</Card.Body>
			</Card>
		</Content.Body>
	</Body>);
}
