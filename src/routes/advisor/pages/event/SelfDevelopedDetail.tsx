/* eslint-disable max-len */
import React, { useState, useMemo } from 'react';
import { Table, Row, Card, Layout, H4, Col, Text, Input, Tag, Form, Radio, Bubble, Icon, Alert, Button, message, StatusTip, Tooltip } from '@tencent/tea-component';
import { useForm, useField } from 'react-final-form-hooks';
import { useHistory } from '@tea/app';
import { createEventApproval, DescribeInternalEventDetail, modifyEventComment, CreateEventDownload, DescribeEventDownload, ModifyInternalEventChat } from '@src/api/advisor/commonEvent';
import { DescribeEventDetailParams } from '@src/types/advisor/commonEvent';
import { getStorage } from '@src/utils/storage';
import { cloneDeep } from 'lodash';
const { pageable, scrollable } = Table.addons;


const initFilterData = [
	{
		label: 'APPID',
		value: 'appid',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: 'UIN',
		value: 'uin',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: '资源负责人',
		value: 'resource_owner',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '负责人leader',
		value: 'resource_owner_leader',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
];

const initParams = {
	Id: 1001,
	Limit: 20,
	Offset: 0,
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742,
	Name: getStorage('engName'),
	Filters: [],
};

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return 'validating';
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? 'error' : 'success';
}

export function SelfDevelopedDetail(match) {
	const formId = match.match.params.id;
	const { Body, Content } = Layout;
	const history = useHistory();
	const [loading, setLoading] = useState(false);
	const [records, setRecords] = useState<any>({
		Name: '',
		Product: [],
		StartTime: '',
		EndTime: '',
		Desc: '',
		Reason: '',
		TotalCount: 1,
		RequestId: '',
		Affected: [],
		Advice: '',
		Tool: '',
	});
	const [originRecords, setOriginRecords] = useState<any>({});
	const [filterParams, setFilterParams] = useState<DescribeEventDetailParams>({
		...cloneDeep(initParams),
		Id: Number(match.match.params.id),
	});
	const [filterData, setFilterData] = useState(cloneDeep(initFilterData));
	// 是否复杂诉求
	const [isComplex, setIsComplex] = useState('2');
	const [isApproved, setIsApproved] = useState('1');
	const [isSuccess, setIsSuccess] = useState(false);

	const onSubmitComment = async (value) => {
		const params = {
			...value,
		};
		try {
			params.Id = parseInt(formId);
			params.Name = getStorage('engName');
			params.OnlyData = true;
			params.ShowError = true;
			const res = await modifyEventComment(params);
			message.success({
				content: res.Message,
			});
			getTabData(filterParams);
		} catch (err) {
			setRecords(cloneDeep(originRecords));
		};
	};

	const columns = [
		{
			key: 'AppId',
			header: 'APPID',
		},
		{
			key: 'Uin',
			header: 'UIN',
		},
		{
			key: 'ResourceOwner',
			header: '资源负责人',
		},
		{
			key: 'ResourceOwnerLeader',
			header: '资源负责人leader',
		},
		{
			key: 'Resources',
			header: '涉及资源列表',
			render: item => (
				<div style={
					{
						display: 'flex',
					}
				}>
					<Text
						copyable
						style={
							{
								fontSize: 0,
								marginRight: '5px',
								display: 'flex',
								alignItems: 'center',
							}
						}
					>{item.Resources.some(v => v instanceof Object) ? item.Resources.map(v => v?.ResourceId).join('\r\n') : item.Resources.join('\r\n')}</Text>
					<div className={'resource-list'}>
						{
							item.Resources?.map((val, i) => <div key={i} className={'fault-tag-wrap'}>
								<Tooltip title={val instanceof Object ? val?.ResourceId : val}>
									<Tag theme={'primary'} className={'def-tag'}>{val instanceof Object ? val?.ResourceId : val}</Tag>
								</Tooltip>
							</div>)
						}
					</div>
				</div>
			),
		},
		// {
		// 	key: 'Comment',
		// 	header: <>
		// 		备注
		// 		<Bubble
		// 			content={
		// 				'点击输入框外侧或按Enter键保存'
		// 			}
		// 		>
		// 			<Icon type="info" />
		// 		</Bubble>
		// 	</>,
		// 	render: (item, rowKey, recordIndex) => <Input
		// 		placeholder={'请输入备注'}
		// 		value={item.Comment}
		// 		onKeyDown={(e) => {
		// 			if (e.key == 'Enter') {
		// 				(e.target as any).blur();
		// 			}
		// 		}}
		// 		onBlur={
		// 			(e) => {
		// 				if (item.Comment === originRecords?.CustomerLists[recordIndex]?.Comment) {
		// 					return;
		// 				}
		// 				onSubmitComment({
		// 					Comment: item.Comment,
		// 					AppId: item.AppId,
		// 				});
		// 				(e.target as any).blur();
		// 			}
		// 		}
		// 		onChange={
		// 			(val) => {
		// 				item.Comment = val;
		// 				setRecords(cloneDeep(records));
		// 			}
		// 		}
		// 	/>,
		// },
	];

	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (item.Name === name) {
				item.Values = Array.isArray(val) ? [...val] : [val];
				notHasName = false;
			}
		});
		if (notHasName) {
			filterParams.Filters.push({
				Name: name,
				Values: Array.isArray(val) ? [...val] : [val],
			});
		}
		setFilterParams({
			...filterParams,
		});
	};

	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await DescribeInternalEventDetail(filterParams);
			setRecords(res);
			setOriginRecords(cloneDeep(res));
			setLoading(false);
		} catch (err) {
			setLoading(false);
		}
	};

	const [clickExport, setClickExport] = useState(false);

	const createEventDownload = async () => {
		setClickExport(true);
		try {
			const res = await CreateEventDownload({
				Id: Number(formId),
				OnlyData: true,
				ShowError: true,
			});
			getDescribeEventDownload(res.TaskId);
		} catch (err) {
			setClickExport(false);
		}
	};

	const getDescribeEventDownload = async (taskId) => {
		try {
			const res = await DescribeEventDownload({
				Id: Number(formId),
				TaskId: taskId,
				OnlyData: true,
				ShowError: true,
			});
			if (res.TaskStatus === 'success') {
				const a = document.createElement('a');
				a.href = res.CosUrl;
				document.body.appendChild(a);
				a.click();
				document.body.removeChild(a);
				setClickExport(false);
			} else {
				setTimeout((taskId) => {
					getDescribeEventDownload(taskId);
				}, 5000, taskId);
			}
		} catch (err) {
			setClickExport(false);
		}
	};

	useMemo(() => {
		getTabData(filterParams);
	}, []);

	// 提交表单
	const onSubmit = async (value) => {
		const params = {
			...value,
		};
		try {
			params.NoticeTime = params.NoticeTime?.format('YYYY-MM-DD HH:mm:ss');
			params.Id = Number(formId);
			params.AppId = 1253985742;
			params.Name = getStorage('engName');
			if (originRecords.Status === 20) {
				params.IsComplex = isComplex == '1';
			}
			params.IsApproved = isApproved == '1';
			params.OnlyData = true;
			params.ShowError = true;
			const res = await createEventApproval(params);
			message.success({
				content: res.Message,
			});
			history.push('/advisor/common-event');
		} catch (err) { };
	};
	const { form, handleSubmit, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		validate: (formInfo) => {
			const validateFormInfo: any = {};
			if (isApproved == '2') {
				validateFormInfo.Reason = !formInfo.Reason ? '请输入驳回原因' : undefined;
			}
			return validateFormInfo;
		},
	});
	const reasonField = useField('Reason', form);

	// 自动拉群
	const createEventChat = async () => {
		try {
			const res = await ModifyInternalEventChat({
				Id: Number(formId),
				Name: getStorage('engName'),
				OnlyData: true,
				ShowError: true,
			});
			setIsSuccess(true);
			message.success({ content: res.Message });
		} catch (err) {}
	};


	return (<Body>
		<Content.Header showBackButton title={'隐患事件详情'} onBackButtonClick={() => {
			history.push('/advisor/common-event');
		}}
		></Content.Header>
		<Content.Body>
			<Card>
				<Card.Body>
					{originRecords.Status === 10 && <Alert>【专项审批原则】（技术向审批）<br />&nbsp;&nbsp;a.风险是否属实——直接风险产品方确认，产品风险真实存在<br />&nbsp;&nbsp;b.风险收敛整改方案完整——风险整改技术方案合理，具有可执行性</Alert>}
					{originRecords.Status === 20 && <Alert>【TAM 审批原则】（客户向审批）<br />&nbsp;&nbsp;a.产品侧整改技术方案是否清晰完整<br />&nbsp;&nbsp;b.明确该产品风险治理所带来的客户业务收益，能否提升客户业务稳定性/健壮性</Alert>}
					<H4>事件描述</H4>
					<div className={'desc-wrap'}>
						<Row>
							<Col span={4}>
								<Text theme="label">事件名称：</Text>
							</Col>
							<Col>
								<Text>{records.Title}</Text>
							</Col>
						</Row>
						<Row verticalAlign={'middle'}>
							<Col span={4}>
								<Text theme="label">涉及云产品：</Text>
							</Col>
							<Col style={{ marginTop: '-3px' }}>
								<Tag theme={'primary'}>{records.Product}</Tag>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">通知方式：</Text>
							</Col>
							<Col>
								<Text>内部企微群</Text>
							</Col>
						</Row>
						{records.RiskLevel !== -1 && <Row>
							<Col span={4}>
								<Text theme="label">风险级别：</Text>
							</Col>
							<Col>
								<Text>{records.RiskLevel === 2 ? '中风险' : '高风险'}</Text>
							</Col>
						</Row>}
						<Row>
							<Col span={4}>
								<Text theme="label">通知发起人：</Text>
							</Col>
							<Col>
								<Text>{records.Updater}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">对内描述信息：</Text>
							</Col>
							<Col>
								<Text>{records.PrivateContent || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">对外问题描述：</Text>
							</Col>
							<Col>
								<Text>{records.PublicDesc || '-'}</Text>
							</Col>
						</Row>
						<Row>
							<Col span={4}>
								<Text theme="label">对外处理建议：</Text>
							</Col>
							<Col>
								<Text>{records.PublicAdvice || '-'}</Text>
							</Col>
						</Row>
					</div>
					<H4 style={{ paddingTop: '20px', paddingBottom: '20px' }}>影响列表</H4>
					<Row>
						{
							filterData.map((item, i) => (<Col span={6} key={i}>
								<Row verticalAlign={'middle'}>
									<Col span={item?.span[0]}>
										<Text theme="label" verticalAlign="middle">{item.label}</Text>
									</Col>
									<Col span={item?.span[1]}>
										<Input
											value={item.virtualVal}
											onChange={(val) => {
												item.virtualVal = val;
												setFilterData([
													...filterData,
												]);
												refreshAllParams(item.value, val);
											}
											}
											placeholder={item.placeholder}
										/>

									</Col>
								</Row>
							</Col>))
						}
					</Row>
					<Row style={{ justifyContent: 'center', margin: '20px 10px 10px 10px' }}>
						<Button type='primary' onClick={() => {
							setFilterParams({
								...filterParams,
								Offset: 0,
							});
							getTabData({
								...filterParams,
								Offset: 0,
							});
						}}>查询</Button>
						<Button style={{ marginLeft: '10px' }} onClick={() => {
							setFilterData(cloneDeep(initFilterData));
							setFilterParams({
								...cloneDeep(initParams),
								Id: Number(match.match.params.id),
							});
							getTabData({
								...cloneDeep(initParams),
								Id: Number(match.match.params.id),
							});
						}}>重置</Button>
						<Button disabled={clickExport} style={{ marginLeft: '10px' }} onClick={() => {
							createEventDownload();
						}}>
							{clickExport ? '导出中' : '导出'}
						</Button>
						<Button style={{ marginLeft: '10px' }} onClick={() => {
							window.open(`https://beacon.woa.com/datatalk/tarc/dashboard/257933?isFullscreen=true&event_id_value=${formId}`);
						}}>
							查看该隐患运营数据
						</Button>
						<Bubble content={`如果内部群里人员过多可能导致一定舆情风险，请谨慎发起拉群。涉及负责人超过${records.AddChatMaxMember || 0}人，请分批录单拉群`} error>
							<Button type='primary' style={{ marginLeft: '10px' }} disabled={!records.IsAddChatActive || isSuccess} onClick={createEventChat}>自动拉群</Button>
						</Bubble>
					</Row>
					<Table
						records={records.Affected ? records.Affected : []}
						columns={columns}
						topTip={(loading || records.Affected.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />}
						addons={
							[
								pageable({
									recordCount: records.TotalCount ? records.TotalCount : 0,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setFilterParams({
											...filterParams,
											Limit: pageSize,
											Offset: (pageIndex - 1) * pageSize,
										});
										getTabData({
											...filterParams,
											Limit: pageSize,
											Offset: (pageIndex - 1) * pageSize,
										});
									},
									pageSizeOptions: [10, 20, 30, 50, 100, 200],
									pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
									pageSize: filterParams.Limit,
								}),
								scrollable({
									maxHeight: records.IsApprovalActive ? 400 : 'inherit',
								}),
							]
						}
					/>
					{
						records.IsApprovalActive && <form
							onSubmit={handleSubmit}
						>
							<H4 style={{
								paddingBottom: '20px',
							}}>审批</H4>
							<Form
								className={'event-form'}
							>
								{originRecords.Status === 20 && <Form.Item
									required={true}
									label={<span>是否复杂诉求:</span>}
									tips={<>
										一般服务诉求：与客户依照服务诉求持续进行沟通，确认问题闭环后，按流程进行结单。（结单工单中的选填字段可忽略不填）<br />
										复杂服务诉求：与客户完成初轮沟通后并填写完全量字段，明确客户诉求后及时与客户Owner同步，后续将由客户Owner立项跟进处置。
									</>}
								>
									<Radio.Group value={isComplex} onChange={value => setIsComplex(value)}>
										<Radio name="1">是</Radio>
										<Radio name="2">否</Radio>
									</Radio.Group>
								</Form.Item>}
								<Form.Item
									required={true}
									label={<span>是否同意:</span>}
								>
									<Radio.Group value={isApproved} onChange={value => setIsApproved(value)}>
										<Radio name="1">同意</Radio>
										<Radio name="2">驳回</Radio>
									</Radio.Group>
								</Form.Item>
								{
									isApproved == '2' && <Form.Item
										required={true}
										label={'驳回原因:'}
										status={getStatus(reasonField.meta, validating)}
										message={(getStatus(reasonField.meta, validating) === 'error' && reasonField.meta.error)}
									>
										<Input.TextArea
											{...reasonField.input}
											size={'l'}
											placeholder={'请输入驳回原因'}
										/>
									</Form.Item>
								}
							</Form>
							<div className='btn-wrap' style={{
								marginTop: '30px',
								display: 'flex',
								paddingLeft: '110px',
							}}>
								<Button htmlType={'submit'} type={'primary'} onClick={() => {

								}}>提交</Button>
							</div>
						</form>
					}
				</Card.Body>
			</Card>
		</Content.Body>
	</Body>);
}
