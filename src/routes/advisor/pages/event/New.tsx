import React, { useState, useMemo, useEffect } from 'react';
import { Table, Button, Card, Layout, Select, Text, Input, Tag, DatePicker, Checkbox, Upload, H3, Alert, message, ExternalLink, StatusTip, Form } from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { useForm, useField } from 'react-final-form-hooks';
import {
	getDescribeProductList
} from '@src/api/advisor/faultNotification';
import {
	getDescribeEventDetail,
	updateEvent,
	DescribeInternalEventDetail,
	ModifyInternalEvent,
} from '@src/api/advisor/commonEvent';
import { getSearchParam } from '@src/utils/common';
import { DetailFilterParams } from "@src/types/advisor/faultNotification";
import { getStorage } from "@src/utils/storage";
import { cloneDeep, get } from "lodash";
import moment from "moment";

function getStatus(meta, validating) {
	if (meta.active && validating) {
		return "validating";
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? "error" : "success";
}

const formInfoList: any = [
	{
		name: 'Title',
		label: '事件名称',
		placeholder: '请输入事件名称(最多40字)',
		required: true,
		errorMessage: '请输入事件名称',
		type: 'input'
	},
	{
		name: 'Product',
		label: '涉及云产品',
		placeholder: '请选择云产品',
		required: true,
		errorMessage: '请选择云产品',
		type: 'mutipleSelect',
		options: []
	},
	{
		name: 'NotificationType',
		label: '通知方式',
		placeholder: '请选择通知方式',
		required: true,
		errorMessage: '请选择通知方式',
		type: 'text',
		options: []
	},
	{
		name: 'RiskLevel',
		label: '风险级别',
		placeholder: '请选择风险级别',
		required: true,
		errorMessage: '请选择风险级别',
		type: 'select',
		options: [
			{ text: '高风险', value: 3 },
			{ text: '中风险', value: 2 },
		]
	},
	{
		name: 'PrivateContent',
		label: '对内问题描述',
		placeholder: '请填写对内问题描述',
		required: true,
		errorMessage: '请填写对内问题描述',
		type: 'textarea',
		length: 500
	},
	{
		name: 'PublicDesc',
		label: '对外问题描述',
		placeholder: '描述问题背景、产生的原因，可能造成的影响\n这里的内容会由一线同步给客户，请注意对外话术',
		required: true,
		errorMessage: '请填写对外问题描述',
		type: 'textarea',
		length: 500
	},
	{
		name: 'PublicAdvice',
		label: '对外处理建议',
		placeholder: '需要详细描述问题的处理步骤，给出具体的指引\n这里的内容会由一线同步给客户，请注意对外话术',
		required: true,
		errorMessage: '请填写对外处理建议',
		type: 'textarea',
		length: 100000
	},
	{
		name: 'File',
		label: '上传影响列表',
		placeholder: '请上传影响列表',
		required: true,
		errorMessage: '请上传影响列表',
		type: 'file'
	}
]
const originFormData = {};
formInfoList.forEach((item) => {
	originFormData[item.name] = '';
});

const initParams = {
	AppId: 1253985742,
	Name: getStorage('engName'),
	Limit: 10000,
	Offset: 0,
	ShowError: true,
	OnlyData: true
};


export function New(match) {
	const baseURL = getStorage('site') === 'sinapore' ? `${window.location.origin}/2` : `${window.location.origin}/1`;
	const { Body, Content } = Layout;
	const {
		scrollable
	} = Table.addons;
	const history = useHistory();
	const [uploading, setUploading] = useState(false);
	const [records, setRecords] = useState<any>({
		CustomerLists: []
	});
	const formId = match.match.params.id;
	const isUpdate = match.match.params.id != 0;
	// 是否是自研单
	const isSelf =  getSearchParam('EventType', location) === '1';
	// 是否有超长的资源
	const [isLong, setIsLong] = useState(false);
	// 大客户单列
	const columns = [
		{
			key: 'AppId',
			header: 'APPID',
		},
		{
			key: 'CustomerName',
			header: '客户名称',
		},
		{
			key: 'Owner',
			header: '售后负责人',
		},
		{
			key: "Resources",
			header: <>
				涉及资源列表
				{isLong && <Text style={{fontSize: 11, marginLeft: 16}} theme="danger">单条记录异常，请保持影响列表每行1个资源实例！</Text>}
			</>,
			render: (item: any) => (
				<div className={'resource-list'}>
					{/* 长度排序，超长的标红 */}
					{item?.Resources?.
						sort((a, b) => b.length - a.length)?.
						map((el, i) => (<Tag theme={el?.ResourceId?.length > 255 ? 'error' : 'primary'} key={i}>{el instanceof Object ? el?.ResourceId : el}
						</Tag>))}
				</div>
			)
		},
	];
	// 自研单列
	const selfColumns = [
		{
			key: 'AppId',
			header: 'APPID',
		},
		{
			key: 'Uin',
			header: 'UIN',
		},
		{
			key: 'ResourceOwner',
			header: '资源负责人',
		},
		{
			key: 'ResourceOwnerLeader',
			header: '资源负责人leader',
		},
		{
			key: "Resources",
			header: <>
				涉及资源列表
				{isLong && <Text style={{fontSize: 11, marginLeft: 16}} theme="danger">单条记录异常，请保持影响列表每行1个资源实例！</Text>}
			</>,
			render: (item: any) => (
				<div className={'resource-list'}>
					{/* 长度排序，超长的标红 */}
					{item?.Resources?.
						sort((a, b) => b.length - a.length)?.
						map((el, i) => (<Tag theme={el?.ResourceId?.length > 255 ? 'error' : 'primary'} key={i}>{el instanceof Object ? el?.ResourceId : el}
						</Tag>))}
				</div>
			)
		},
	];
	const [loading, setLoading] = useState(false);
	const formFieldInfo = {};
	const [formRenderList, setFormRenderList] = useState(cloneDeep(formInfoList));
	const [productInfo, setProductInfo] = useState<any>({});
	const [addedAffectedInfo, setAddedAffectedInfo] = useState([]);
	const [filterParams, setFilterParams] = useState<DetailFilterParams>({
		...cloneDeep(initParams),
		Id: parseInt(formId)
	});
	const [changeType, setChangeType] = useState('default');
	const [saveType, setSaveType] = useState(1);
	// 通知方式
	const [notificationType, setNotificationType] = useState(['1', '2'])
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			// 判读是查询大客户还是自研单详情
			const DataFunc = isSelf ? DescribeInternalEventDetail : getDescribeEventDetail;
			const res = await DataFunc(filterParams);
			res.NoticeTime = moment(res.NoticeTime);
			if (isSelf) {
				// 如果是自研单，构造成原来的结构
				res.CustomerLists = res.Affected || [];
			}
			setRecords(res);
			setAddedAffectedInfo(cloneDeep(res.CustomerLists));
			setLoading(false);
		} catch (err) {
			setLoading(false);
		}
	};
	// 重置表单渲染项
	useMemo(() => {
		const list = cloneDeep(formInfoList);
		for (let i = 0; i < list.length; ++i) {
			if (list[i]['name'] == 'Product') {
				list[i]['options'] = [...cloneDeep(productInfo.list || [])];
				break;
			}
		}
		setFormRenderList([
			...list
		]);
	}, [productInfo]);

	useEffect(() => {
		setIsLong(false);
		const result = records?.CustomerLists?.
			flatMap(m => m.Resources.map(n => n.ResourceId))?.
			some(id => id.length > 255);
		setIsLong(result);
	}, [records]);

	// 提交表单
	const onSubmit = async (value) => {
		const params = {
			...value
		};
		try {
			params['NoticeTime'] = params['NoticeTime']?.format('YYYY-MM-DD HH:mm:ss');
			params['Id'] = parseInt(formId);
			params['AppId'] = 1253985742;
			params['Name'] = getStorage('engName');
			params['Affected'] = addedAffectedInfo;
			params['Status'] = saveType;
			params['OnlyData'] = true;
			params['ShowError'] = true;
			params['NotificationType'] = notificationType.length === 2 ? 0 : Number(notificationType[0])
			params['RiskLevel'] = notificationType.includes('2') ? params['RiskLevel'] : -1;
			// 判读是查询大客户还是自研单详情
			const saveFunc = isSelf ? ModifyInternalEvent : updateEvent;
			const res = await saveFunc(params);
			message.success({
				content: res.Message
			});
			history.push('/advisor/common-event');
		} catch (err) { };
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {
			...originFormData,
		},
		validate: (formInfo) => {
			const validateFormInfo = {};
			formInfoList.forEach((item) => {
				if (item.name == 'NotificationType') {
					validateFormInfo[item.name] = undefined;
				} else {
					validateFormInfo[item.name] = !formInfo[item.name] ? item.errorMessage : undefined;
				}
			});
			// 如果没有选择控制台，就不校验风险等级
			if (!notificationType.includes('2')) {
				validateFormInfo['RiskLevel'] = undefined
			}
			return validateFormInfo;
		},
	});
	formInfoList.forEach((item) => {
		formFieldInfo[item.name] = useField(item.name, form);
	});
	// 匹配选择的产品
	const getCurrntProductName = (name, type?: string) => {
		if (!name) {
			return;
		}
		let currentName;
		for (const key in productInfo?.ProductDict) {
			if (type == 'name') {
				if (productInfo.ProductDict[key].indexOf(name) != -1) {
					currentName = key;
				}
			} else {
				if (key == name) {
					currentName = productInfo.ProductDict[key];
				}
			}
		}
		return currentName;
	};
	useMemo(() => {
		if (isUpdate && records.Status) {
			// const selProductList = [];
			// for (const key in productInfo?.ProductDict) {
			// 	if (records?.Product?.indexOf(productInfo?.ProductDict[key]) != -1) {
			// 		selProductList.push(key);
			// 	}
			// }
			let initProductName = getCurrntProductName(records?.Product, 'name');

			if (!initProductName) {
				initProductName = records?.Product;
				setChangeType('input');
			}
			form.initialize({
				...records,
				File: 'had',
				Product: initProductName
			});
			// 通知方式初始化
			setNotificationType(records.NotificationType === 0 ? ['1', '2'] : [records.NotificationType.toString()])
		}
	}, [records, productInfo]);
	const handleSuccess = (result, { file }) => {
		setRecords({
			CustomerLists: result.Response.Affected,
			TotalCount: result.Response.TotalCount,
			Product: records?.Product,
		});
		result?.Response?.Notice && message.warning({ content: result.Response.Notice });
		setAddedAffectedInfo(result.Response.Affected);
		setUploading(false);
	};
	const handleError = (error, { xhr }) => {
		const responseText = xhr.responseText.indexOf('Response') != -1 ? get(JSON.parse(xhr.responseText), 'Response.Error.Message') : xhr.responseText;
		message.error({
			content: responseText
		});
		setUploading(false);
	};
	// 获取产品信息
	useMemo(async () => {
			try {
				const res = await getDescribeProductList({
					OnlyData: true,
					ShowError: true,
					AppId: 1253985742
				});
				const list = [];
				for (const key in res.ProductDict) {
					list.push({
						text: res.ProductDict[key],
						value: key
					});
				}
				res.list = list;
				setProductInfo(cloneDeep(res));
			} catch (err) { };
	}, []);
	// 更新表单信息
	useMemo(async () => {
		if (isUpdate) {
			getTabData(filterParams);
		}
	}, []);
	// 格式化时间
	const changeTime = (time, type) => {
		const date = new Date(time);
		const setTime = (type == 'forward' ? (date.getTime() + 1 * 1000) : (date.getTime() - 1 * 1000));
		const newDate = new Date(setTime);
		return `${newDate.getFullYear()}-${newDate.getMonth() + 1 >= 10 ? newDate.getMonth() + 1 : `0${newDate.getMonth() + 1}`}-${newDate.getDate() >= 10 ? newDate.getDate() : `0${newDate.getDate()}`} ${newDate.getHours() >= 10 ? newDate.getHours() : `0${newDate.getHours()}`}:${newDate.getMinutes() >= 10 ? newDate.getMinutes() : `0${newDate.getMinutes()}`}:${newDate.getSeconds() >= 10 ? newDate.getSeconds() : `0${newDate.getSeconds()}`}`;
	};
	return (<Body className={'fault-new-wrap'}>
		<Content.Header
			showBackButton
			title={isUpdate ? '更改产品隐患事件' : '新建产品隐患事件'}
			onBackButtonClick={() => {
				history.push('/advisor/common-event');
			}}
		></Content.Header>
		<Content.Body>
			<Card>
				<form onSubmit={handleSubmit}>
					<Card.Body>
						<Form
							layout={'inline'}
						>
							{
								// records?.Status?.split('|')[0] == 2 && <div className="form-modal"></div>
							}
							{
								formRenderList.map((item, i) => (
									item.type == 'input'
										?
										<Form.Item
											key={i}
											required={item.required}
											label={item.label}
											status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
											message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
											suffix={<Text style={{ fontWeight: 'bold' }} theme={'danger'}>（事件名称将在腾讯云控制台首页云顾问小卡片展示，请谨慎填写！）</Text>}
										>
											<Input {...formFieldInfo[item['name']].input} autoComplete="off" placeholder={item.placeholder} maxLength={40} />
										</Form.Item>
										:
										item.type == 'text'
											?
											<div key={i}>
												<Form.Item
													required={item.required}
													label={item.label}
													status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
													message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
												>
													{isSelf ? <Form.Text style={{ display: 'inline' }}>内部企微群</Form.Text> : <>
														<Form.Text style={{ display: 'inline' }}>内部企微群</Form.Text>
														<Form.Text style={{ display: 'inline', margin: '0 20px' }}>企业号</Form.Text>
														<Checkbox.Group value={notificationType} onChange={value => value.length ? setNotificationType(value) : ''}>
															<Checkbox name="1">安灯事件单 （给1线，工作时间审批后自动创建）</Checkbox>
															<Checkbox name="2">云顾问控制台【风险治理】页面和官网首页小卡片</Checkbox>
														</Checkbox.Group>
													</>}
												</Form.Item>
											</div>
											:
											item.type == 'textarea'
												?
												<div key={i}>
													<Form.Item
														required={item.required}
														label={item.label}
														status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
														message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
													>
														<Input.TextArea
															{...formFieldInfo[item['name']].input}
															autoComplete="off"
															placeholder={item.placeholder}
															style={{
																width: '820px',
																height: '60px'
															}}
															maxLength={item.length ? item.length : 80} />
													</Form.Item>
												</div>
												:
												item.type == 'time'
													?
													<Form.Item
														key={i}
														required={item.required}
														label={item.label}
														status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
														message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
													>
														<DatePicker
															{...formFieldInfo[item['name']].input}
															showTime
															range={[
																moment(moment())
																	.subtract(365, "d")
																	.startOf("d"),
																moment(moment())
																	.endOf("s")]}
														/>
													</Form.Item>
													:
													item.type == 'mutipleSelect'
														?
														<Form.Item
															key={i}
															required={item.required}
															label={item.label}
															status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
															message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
														>
															<Select
																value={formFieldInfo[item['name']].input.value}
																onChange={(val) => {
																	setChangeType('default');
																	form.change('Product', val);
																}}
																options={item.options}
																appearance={'pure'}
																button={<Input
																	value={changeType == 'input' ? values.Product : getCurrntProductName(values.Product)}
																	onChange={(val) => {
																		setChangeType('input');
																		form.change('Product', val);
																	}}
																	onClick={(e) => {
																		let num = 0;
																		const timer = setInterval((el) => {
																			num++;
																			el.focus();
																			if (num == 20) {
																				clearInterval(timer);
																			} else if (document.querySelector('.tea-input--search') && num < 10) {
																				num = 10;
																			}
																		}, 10, e.currentTarget);
																	}}
																/>
																}
																size='m'
																searchable
															/>
														</Form.Item>
														:
														item.type == 'select'
															?
															(item.name === 'RiskLevel' && notificationType.includes('2')) ? <Form.Item
																key={i}
																required={item.required}
																label={item.label}
																status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
															>
																<Select
																	{...formFieldInfo[item['name']].input}
																	options={item.options}
																	appearance="button"
																	size='m'
																/>
															</Form.Item> : ''
															:
															item.type == 'file'
																?
																<div key={i}>
																	<Form.Item
																		required={item.required}
																		label={item.label}
																		status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																		message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																		className='file-form-item'
																	>
																		<Upload
																			// @ts-ignore
																			value={formFieldInfo[item['name']].input.value}
																			onChange={formFieldInfo[item['name']].input.onChange}
																			action={`${baseURL}/event`}
																			onStart={
																				() => {
																					setUploading(true);
																				}
																			}
																			onSuccess={handleSuccess}
																			onError={handleError}
																			headers={isSelf ? { Action: 'DescribeInternalResources' } : { Action: 'DescribeResources', FaultId: Number(formId) }}
																			data={isSelf ? { Product: getCurrntProductName(records?.Product, 'name') } : { IsUpdate: isUpdate }}
																		>
																			<Button disabled={false} htmlType={'button'} loading={uploading}>点击上传</Button>
																		</Upload>
																		{/* <Button */}
																		{/* 	htmlType={'button'} */}
																		{/* 	type={'primary'} */}
																		{/* 	onClick={downloadTem} */}
																		{/* > */}
																		{/* 	下载表格模版 */}
																		{/* 	<svg style={{verticalAlign: 'sub', marginLeft: '3px'}} width="16px" height="16px" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"> */}
																		{/* 		<path d="M13 3L13 12.5858L16.5 9.08579L17.9142 10.5L12 16.4142L6.08579 10.5L7.5 9.08579L11 12.5858L11 3L13 3ZM4.5 14V19H19.5V14H21.5V21H2.5V14H4.5Z" fill="#fff"></path> */}
																		{/* 	</svg> */}
																		{/* </Button> */}
																		<ExternalLink style={{
																			verticalAlign: 'middle',
																			marginLeft: '10px'
																		}} href={'https://iwiki.woa.com/pages/viewpage.action?pageId=4007253707'}>
																			下载表格模版
																		</ExternalLink>
																	</Form.Item>
																</div>
																:
																<></>
								))
							}
						</Form>
						{ !isSelf && <H3 className={'desc-list-t'}>
							共有<Text theme={'primary'}>{records.TotalCount ? records.TotalCount : 0}</Text>家大客户受到影响
						</H3>}
						<Table
							records={
								records.CustomerLists ? records.CustomerLists : []
							}
							columns={isSelf ? selfColumns : columns}
							topTip={
								(loading || records.CustomerLists.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
							}
							addons={[
								scrollable({
									maxHeight: 300
								})
							]}
						/>
						<H3 className={'desc-view-t'}>
							客户群消息推送信息预览
						</H3>
						<Card>
							<Card.Body>
								<>
									<div className='view-item'>
										<Text>问题描述：</Text>
										<Text>{values.PublicDesc}</Text>
									</div>
									<div className='view-item'>
										<Text>处理建议：</Text>
										<Text>{values.PublicAdvice}</Text>
									</div>
									<div className='view-item'>
										<Text>受影响的资源：</Text>
										<Text style={{ position: 'relative', top: '-3px' }}>
											{
												records?.CustomerLists?.
													flatMap(m => m.Resources.map(n => n.ResourceId))?.
													map((id, i) => {
														if (i < 6) {
															return <Tag theme={'primary'} key={id}>{id}</Tag>;
														}
														return '';
													})
											}
											{
												records?.CustomerLists?.
													flatMap(m => m.Resources.map(n => n.ResourceId))?.
													length > 6 && (<span>...</span>)
											}
										</Text>
									</div>
								</>
							</Card.Body>
						</Card>
						<div className='btn-wrap' style={{
							marginTop: '30px'
						}}>
							<Button htmlType="submit" onClick={() => {
								setSaveType(1);
							}}>保存草稿</Button>
							<Button htmlType={'submit'} type={'primary'} onClick={() => {
								setSaveType(2);
							}}>提交</Button>
						</div>
					</Card.Body>
				</form>
			</Card>
		</Content.Body>
	</Body>);
}