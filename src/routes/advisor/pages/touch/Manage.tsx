import React, { useState, useMemo } from 'react';
import { Table, Button, Row, Card, Layout, Col, Select, SelectMultiple, Text, Input, DatePicker, StatusTip, Tag, TagSelect } from '@tencent/tea-component';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { getStorage } from '@src/utils/storage';
import {
	getDescribeAlarmCustomers,
} from '@src/api/advisor/touch';
import { ManageModal } from '@src/routes/advisor/pages/touch/ManageModal';

const initFilterData = [
	{
		label: 'APPID',
		value: 'appid',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: 'UIN',
		value: 'uin',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: '客户名称',
		value: 'customer_name',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: '售后负责人',
		value: 'owner',
		type: 'input',
		placeholder: '多人以;分隔',
		span: [6, 18],
		virtualVal: '',
	},
];

const initParams = {
	Limit: 10,
	Offset: 0,
	Filters: [],
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742,
	Name: getStorage('engName'),
};

export function Manage() {
	const { Body, Content } = Layout;
	const [filterData, setFilterData] = useState<any>([...cloneDeep(initFilterData)]);
	const [loading, setLoading] = useState(false);

	const [columns, setColumns] = useState<any>([
		{
			key: 'AppId',
			header: 'APPID',
		},
		{
			key: 'Uin',
			header: 'UIN',
		},
		{
			key: 'CustomerName',
			header: '客户名称',
		},
		{
			key: 'Owner',
			header: '售后负责人',
			render: item => (item.Owner ? item.Owner : '-'),
		},
		{
			key: 'Operator',
			header: '操作人',
			render: item => (item.Operator ? item.Operator : '-'),
		},
		{
			key: 'AffectedNum1',
			header: '操作',
			render: item => (
				<Text
					theme={'primary'}
					onClick={() => {
						setDetailVisible(true);
						setDetailId(item.AppId);
						setTitle(`推送管理-${item.CustomerName}(APPID:${item.AppId})`);
					}}
					className={'link-touch-btn'}
				>
						推送管理
				</Text>
			),
		},
	]);
	const [records, setRecords] = useState<any>({
		CustomerLists: [],
	});
	const { RangePicker } = DatePicker;
	const [filterParams, setFilterParams] = useState({
		...cloneDeep(initParams),
	});
	const [detailVisible, setDetailVisible] = useState(false);
	const [detailId, setDetailId] = useState(0);
	const [title, setTitle] = useState('');
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeAlarmCustomers(filterParams);
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				if (item.Name == nameList[0]) {
					item.Values = [val[0]];
					notHasName = false;
				}
				if (item.Name == nameList[1]) {
					item.Values = [val[1]];
					notHasName = false;
				}
			} else {
				if (item.Name == name) {
					item.Values = Array.isArray(val) ? [...val] : name == 'owner' ? val.split(';') :  [val];
					notHasName = false;
				}
			}
		});
		if (notHasName) {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				filterParams.Filters.push({
					Name: nameList[0],
					Values: [val[0]],
				});
				filterParams.Filters.push({
					Name: nameList[1],
					Values: [val[1]],
				});
			} else {
				filterParams.Filters.push({
					Name: name,
					Values: Array.isArray(val) ? [...val] : name == 'owner' ? val.split(';') :  [val],
				});
			}
		}
		setFilterParams({
			...filterParams,
		});
	};
	useMemo(() => {
		getTabData(filterParams);
	}, []);
	// 分页
	const { pageable } = Table.addons;
	const resetFilterData = () => {
		const list = cloneDeep(initFilterData);
		setFilterData([
			...list,
		]);
	};
	return (
		<Body>
			<Content.Body>
				<Card>
					<Card.Body className='touch-filter-wrap'>
						<Row>
							{
								filterData.map((item, i) => item.type != 'tagSelect' && (<Col span={i % 4 == 0 ? 75 : 55} key={i}>
									<Row verticalAlign={'middle'}>
										<Col span={item?.span[0]}>
											<Text theme="label" verticalAlign="middle">{item.label}</Text>
										</Col>
										<Col span={item?.span[1]}>
											{
												item.type == 'input'
													? <Input onChange={(val) => {
														item.virtualVal = val;
														setFilterData([
															...filterData,
														]);
														refreshAllParams(item.value, val);
													}
													}
														value={item.virtualVal}
														placeholder={item.placeholder}
													/>
													:	item.type == 'mutipleTime'
														? <RangePicker
															showTime
															value={item.virtualVal}
															range={[
																moment()
																	.subtract(365, 'd')
																	.startOf('d'),
																moment().endOf('d'),
															]}
															onChange={(val) => {
																item.virtualVal = [...val];
																setFilterData([
																	...filterData,
																]);
																refreshAllParams(item.value, [
																	val[0].format('YYYY-MM-DD HH:mm:ss'),
																	val[1].format('YYYY-MM-DD HH:mm:ss'),
																]);
															}
															}
														/>
														:	item.type == 'select'
															?	<Select
																options={item.options}
																appearance="button"
																size='m'
																value={item.virtualVal}
																onChange={(val) => {
																	item.virtualVal = val;
																	setFilterData([
																		...filterData,
																	]);
																	refreshAllParams(item.value, val);
																}
																}
															></Select>
															:	item.type == 'mutipleSelect'
																?	<SelectMultiple
																	options={item.options}
																	appearance="button"
																	size='m'
																	value={item.virtualVal}
																	onChange={(val) => {
																		item.virtualVal = val;
																		setFilterData([
																			...filterData,
																		]);
																		refreshAllParams(item.value, val);
																	}
																	}
																></SelectMultiple>
																:	<></>
											}
										</Col>
									</Row>
								</Col>))
							}
						</Row>
						<Row style={{
							justifyContent: 'center',
							margin: '20px 10px 10px 10px',
						}}>
							<Button type='primary' onClick={() => {
								setFilterParams({
									...filterParams,
									Offset: 0,
								});
								getTabData({
									...filterParams,
									Offset: 0,
								});
							}}>查询</Button>
							<Button style={{
								marginLeft: '10px',
							}} onClick={() => {
								resetFilterData();
								setFilterParams({
									...cloneDeep(initParams),
								});
								getTabData({
									...cloneDeep(initParams),
								});
							}}>重置</Button>
						</Row>
					</Card.Body>
				</Card>
				<Card>
					<Card.Body>
						<Table
							records = {
								records.CustomerLists ? records.CustomerLists : []
							}
							columns={columns}
							topTip={
								(loading || records.CustomerLists.length == 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
							}
							addons={
								[
									pageable({
										recordCount: records.TotalCount ? records.TotalCount : 0,
										onPagingChange: ({ pageIndex, pageSize }) => {
											setFilterParams({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize,
											});
											getTabData({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize,
											});
										},
										pageSizeOptions: [10, 20, 30, 50, 100, 200],
										pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
										pageSize: filterParams.Limit,
									}),
								]
							}
						/>
						<ManageModal title={title} visible={detailVisible} id={detailId} handleVisible={(refresh) => {
							setDetailVisible(false);
							if (refresh) {
								getTabData(filterParams);
							}
						}}></ManageModal>
					</Card.Body>
				</Card>
			</Content.Body>
		</Body>
	);
}
