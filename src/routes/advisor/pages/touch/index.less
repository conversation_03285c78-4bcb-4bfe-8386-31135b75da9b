.touch-wrap{
	.link-touch-btn{
		cursor: pointer;
		&:hover{
			text-decoration: underline;
		}
	}
	.resource-list{
		max-height: 75px;
		overflow-y: auto;
	}
}
.def-tag span{
	max-width: 80px!important;
	white-space: nowrap!important;
}
@media screen and (min-width: 900px){
	.touch-filter-wrap{
		.tea-grid__item-75{
			width: 50%;
		}
		.tea-grid__item-55{
			width: 50%;
		}
		.tea-grid__item-6{
			width: 20.83333%;
		}
		.tea-grid__item-2{
			width: 10.5%;
		}
		.tea-grid__item-3{
			width: 10.5%;
		}
		.tea-grid__item-22{
			width: 89.3%;
		}
	}
}
@media screen and (min-width: 1468px){
	.touch-filter-wrap{
		.tea-grid__item-75{
			width: 33.33333%;
		}
		.tea-grid__item-55{
			width: 33.33333%;
		}
		.tea-grid__item-2{
			width: 7%;
		}
		.tea-grid__item-3{
			width: 7%;
		}
		.tea-grid__item-22{
			width: 84.6%;
		}
	}
}
@media screen and (min-width: 1600px){
	.touch-filter-wrap{
		.tea-grid__item-75{
			width: 31.25%;
		}
		.tea-grid__item-55{
			width: 22.916%;
		}
		.tea-grid__item-6{
			width: 25%;
		}
		.tea-grid__item-2{
			width: 7.8%;
		}
		.tea-grid__item-3{
			width: 5.7%;
		}
		.tea-grid__item-22{
			width: 91.3%;
		}
	}
}
.manage-modal{
	.tea-dialog__inner{
		min-width: 800px;
		.tea-text-overflow{
			white-space: normal;
		}
	}
}