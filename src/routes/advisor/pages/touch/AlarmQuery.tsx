/* eslint-disable no-nested-ternary */
import React, { useState, useMemo } from 'react';
import {
	Table,
	Button,
	Row,
	Card,
	Layout,
	Col,
	Select,
	SelectMultiple,
	Text,
	Input,
	DatePicker,
	StatusTip,
	Tag,
	TagSelect,
	Bubble
} from '@tencent/tea-component';
import moment from 'moment';
import { cloneDeep } from 'lodash';
import { getStorage } from '@src/utils/storage';
import { REPORT_DOMAIN, HEALTH_REPORT_URL } from './constants';
import {
	getDescribeAlarmPolicy,
	getDescribeAlarmLists,
} from '@src/api/advisor/touch';
import { getDescribeProductList } from '@src/api/advisor/faultNotification';
import { AlarmDetailModal } from '@src/routes/advisor/pages/touch/AlarmDetailModal';
import { Tooltip } from '@tea/component';
import { useHistory } from '@tea/app';

const initFilterData = [
	{
		label: 'APPID',
		value: 'appid',
		type: 'input',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: 'UIN',
		value: 'uin',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: '客户名称',
		value: 'customer_name',
		type: 'input',
		span: [5, 19],
		virtualVal: '',
	},
	{
		label: '告警策略',
		value: 'policy_id',
		type: 'select',
		span: [5, 19],
		options: [{
			text: '全部类型',
			value: '',
		}],
		virtualVal: '',
		searchable: true,
	},
	{
		label: '售后负责人',
		value: 'owner',
		type: 'input',
		placeholder: '多人以;分隔',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '通知方式',
		value: 'method',
		type: 'mutipleSelect',
		span: [5, 19],
		options: [
			{
				text: '企微应用号',
				value: 'wx',
			},
			{
				text: '内部企微群',
				value: 'chat',
			},
			{
				text: '安灯事件单',
				value: 'andon',
			},
			{
				text: '电话通知',
				value: 'call',
			},
			{
				text: '客户售后群',
				value: 'out',
			},
		],
		virtualVal: '',
	},
	{
		label: '反馈状态',
		value: 'feedback',
		type: 'select',
		span: [5, 19],
		options: [
			{
				text: '全部类型',
				value: '',
			},
			{
				text: '准确',
				value: '1',
			},
			{
				text: '误告',
				value: '-1',
			},
		],
		virtualVal: '',
	},
	{
		label: '屏蔽状态',
		value: 'block',
		type: 'select',
		span: [5, 19],
		options: [
			{
				text: '全部类型',
				value: '',
			},
			{
				text: '屏蔽实例所有告警',
				value: '1',
			},
			{
				text: '屏蔽实例该策略告警',
				value: '2',
			},
		],
		virtualVal: '',
	},
	{
		label: '告警时间',
		value: 'start_time,end_time',
		type: 'mutipleTime',
		span: [6, 18],
		virtualVal: '',
	},
	{
		label: '产品',
		value: 'product',
		type: 'tagSelect',
		options: [
			{
				text: '全部',
				value: '',
			},
		],
		span: [6, 18],
		virtualVal: [],
	},
];

const initParams = {
	Limit: 10,
	Offset: 0,
	Filters: [],
	OnlyData: true,
	ShowError: true,
	AppId: 1253985742,
	Name: getStorage('engName'),
};

export function AlarmQuery() {
	const history = useHistory();
	const { search } = location;
	const hasAppid = search.indexOf('appid') != -1;
	const searchVal = hasAppid ? search.split('=')[1] : '';
	const initOriginFilterData = cloneDeep(initFilterData);
	const [searchAppId, setSearchAppId] = useState('');
	if (hasAppid) {
		history.replace(location.pathname);
	}
	if (hasAppid) {
		if (!searchAppId) {
			setSearchAppId(searchVal);
		}
	}
	initOriginFilterData[0].virtualVal = searchAppId;
	const { Body, Content } = Layout;
	const [filterData, setFilterData] = useState<any>([...cloneDeep(initOriginFilterData)]);
	const [loading, setLoading] = useState(false);

	const [columns, setColumns] = useState<any>([
		{
			key: 'AppId',
			header: 'APPID',
			width: 100,
		},
		{
			key: 'Uin',
			header: 'UIN',
		},
		{
			key: 'CustomerName',
			header: '客户名称',
			render(item) {
				return <Tooltip title={item.CustomerName}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.CustomerName
						}
					</span>
				</Tooltip>;
			},
		},
		{
			key: 'Product',
			header: '产品',
			className: 'resource-list',
			render: item => (
				<Tooltip title={item.Product}>
					<Tag theme={'primary'} className={'def-tag'}>{item.Product}</Tag>
				</Tooltip>
			),
		},
		{
			key: 'PolicyName',
			header: '告警策略',
			render(item) {
				return <Tooltip title={item.PolicyName}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.PolicyName
						}
					</span>
				</Tooltip>;
			},
		},
		{
			key: 'PolicyOwner',
			header: '产研告警推送人',
			width: 110,
			render: item => {
				return (item.PolicyOwner
					?
					<Bubble content={`如对策略有疑问，请联系${item.PolicyOwner}`} dark>
						{
							item.PolicyOwner
						}
					</Bubble>
					: '-');
			},
		},
		{
			key: 'PolicyOwnerCenter',
			header: '策略归属中心',
			render: item => (item.PolicyOwnerCenter ?? '-'),
		},
		{
			key: 'AlarmObject',
			header: '告警对象',
			render(item) {
				return <Tooltip title={item.AlarmObject}>
					<span
						className={'tea-text-overflow'}
					>
						{
							item.AlarmObject
						}
					</span>
				</Tooltip>;
			},
		},
		{
			key: 'AlarmTime',
			header: '首次异常时间',
			render: item => (item.AlarmTime ? item.AlarmTime : '-'),
			width: 170,
		},
		{
			key: 'LatestAlarmTime',
			header: '最新异常时间',
			render: item => (item.LatestAlarmTime ? item.LatestAlarmTime : '-'),
			width: 170,
		},
		{
			key: 'Method',
			header: '触达方式',
			render: item => (
				<div className={'resource-list'}>
					{
						item?.Method.map((el, i) => (<div key={i} className={'fault-tag-wrap'}><Tag theme={'primary'}>{el?.split('|')[1]}</Tag></div>))
					}
				</div>
			),
		},
		{
			key: 'Owner',
			header: '售后负责人',
			render: item => (item.Owner ? item.Owner : '-'),
		},
		{
			key: 'TicketStatus',
			header: '工单状态',
			render: item => (item.TicketStatus ? item.TicketUrl ? <a className={'link-touch-btn'} target={'_blank'} href={`${item.TicketUrl}`}>{item.TicketStatus}</a> : item.TicketStatus : '-'),
		},
		{
			key: 'Feedback',
			header: '是否准确',
			render: (item) => {
				const feedbackList = item.Feedback.split('|');
				return (
					<Tag theme={feedbackList[0] == 1 ? 'success' : feedbackList[1] == -1 ? 'error' : 'default'}>
						{
							feedbackList[1]
						}
					</Tag>
				);
			},
		},
		{
			key: 'Block',
			header: '屏蔽状态',
			render: item => (item.Block ? item.Block?.split('|')[1] : '-'),
		},
		{
			key: 'Operator',
			header: '操作人',
			render: item => (item.Operator ? item.Operator : '-'),
		},
		{
			key: 'AffectedNum1',
			header: '操作',
			fixed: 'right',
			render: item => (
				<Text
					theme={'primary'}
					onClick={() => {
						setDetailVisible(true);
						setDetailId(item.Id);
						setAppId(item.AppId);
					}}
					className={'link-touch-btn'}
				>
						查看详情
				</Text>
			),
		},
	]);
	const [records, setRecords] = useState<any>({
		AlarmLists: [],
	});
	const { RangePicker } = DatePicker;
	const [filterParams, setFilterParams] = useState({
		...cloneDeep(initParams),
		Filters: [
			{
				Name: 'appid',
				Values: [searchVal],
			},
		],
	});
	const [detailVisible, setDetailVisible] = useState(false);
	const [detailId, setDetailId] = useState(0);
	const [appId, setAppId] = useState(0);
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await getDescribeAlarmLists(filterParams);
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filterParams.Filters.forEach((item) => {
			if (name.indexOf(',') !== -1) {
				const nameList = name.split(',');
				if (item.Name === nameList[0]) {
					item.Values = [val[0]];
					notHasName = false;
				}
				if (item.Name === nameList[1]) {
					item.Values = [val[1]];
					notHasName = false;
				}
			} else {
				if (item.Name === name) {
					item.Values = Array.isArray(val) ? [...val] : name == 'owner' ? val.split(';') :  [val];
					notHasName = false;
				}
			}
		});
		if (notHasName) {
			if (name.indexOf(',') !== -1) {
				const nameList = name.split(',');
				filterParams.Filters.push({
					Name: nameList[0],
					Values: [val[0]],
				});
				filterParams.Filters.push({
					Name: nameList[1],
					Values: [val[1]],
				});
			} else {
				filterParams.Filters.push({
					Name: name,
					Values: Array.isArray(val) ? [...val] : name == 'owner' ? val.split(';') :  [val],
				});
			}
		}
		setFilterParams({
			...filterParams,
		});
	};
	const [filterGetData, setFilterGetData] = useState({
		productList: null,
		strategyList: null,
	});
	useMemo(async () => {
		const res = await getDescribeAlarmPolicy({
			AppId: 1253985742,
			OnlyData: true,
			ShowError: true,
		});
		const list = [];
		for (const key in res.PolicyDict) {
			list.push({
				text: res.PolicyDict[key],
				value: key,
			});
		}
		const result = await getDescribeProductList({
			OnlyData: true,
			ShowError: true,
			AppId: 1253985742,
		});
		const arr = [];
		for (const key in result.ProductDict) {
			arr.push({
				text: result.ProductDict[key],
				value: key,
			});
		}
		result.list = arr;
		setFilterGetData({
			productList: arr,
			strategyList: list,
		});
	}, []);
	useMemo(() => {
		getTabData(filterParams);
	}, []);
	// 分页
	const { pageable, scrollable, columnsResizable } = Table.addons;
	const resetFilterData = (filterGetData, init?: boolean) => {
		let list;
		if (init) {
			list = cloneDeep(initOriginFilterData);
		} else {
			list = cloneDeep(initFilterData);
		}
		for (let i = 0;i < list.length; ++i) {
			if (list[i].value == 'product') {
				list[i].options = [...cloneDeep(filterGetData.productList)];
			}
			if (list[i].value == 'policy_id') {
				list[i].options = [
					{
						text: '全部类型',
						value: '',
					},
					...cloneDeep(filterGetData.strategyList),
				];
			}
		};
		setFilterData([
			...list,
		]);
	};
	useMemo(() => {
		if (filterGetData.productList && filterGetData.strategyList) {
			resetFilterData(filterGetData, true);
		}
	}, [filterGetData]);
	return (
		<Body>
			<Content.Body>
				<Card>
					<Card.Body className='touch-filter-wrap'>
						<Row>
							{
								filterData.map((item, i) => item.type != 'tagSelect' && (<Col span={i % 4 == 0 ? 75 : 55} key={i}>
									<Row verticalAlign={'middle'}>
										<Col span={item?.span[0]}>
											<Text theme="label" verticalAlign="middle">{item.label}</Text>
										</Col>
										<Col span={item?.span[1]}>
											{
												item.type === 'input'
													? <Input
														onChange={(val) => {
															item.virtualVal = val;
															setFilterData([
																...filterData,
															]);
															refreshAllParams(item.value, val);
														}
														}
														value={item.virtualVal}
														placeholder={item.placeholder}
													/>
													:	item.type === 'mutipleTime'
														? <RangePicker
															showTime
															value={item.virtualVal}
															range={[
																moment()
																	.subtract(365, 'd')
																	.startOf('d'),
																moment().endOf('d'),
															]}
															onChange={(val) => {
																item.virtualVal = [...val];
																setFilterData([
																	...filterData,
																]);
																refreshAllParams(item.value, [
																	val[0].format('YYYY-MM-DD HH:mm:ss'),
																	val[1].format('YYYY-MM-DD HH:mm:ss'),
																]);
															}
															}
														/>
														:	item.type === 'select'
															?	<Select
																listWidth={200}
																searchable={item.searchable}
																options={item.options}
																appearance="button"
																size='m'
																value={item.virtualVal}
																onChange={(val) => {
																	item.virtualVal = val;
																	setFilterData([
																		...filterData,
																	]);
																	refreshAllParams(item.value, val);
																}
																}
															></Select>
															:	item.type === 'mutipleSelect'
																?	<SelectMultiple
																	listWidth={200}
																	options={item.options}
																	appearance="button"
																	size='m'
																	value={item.virtualVal}
																	onChange={(val) => {
																		item.virtualVal = val;
																		setFilterData([
																			...filterData,
																		]);
																		refreshAllParams(item.value, val);
																	}
																	}
																></SelectMultiple>
																:	<></>
											}
										</Col>
									</Row>
								</Col>))
							}
						</Row>
						<Row verticalAlign={'middle'} style={{
							marginTop: '10px',
						}}>
							<Col span={2}>
								<Text theme="label" verticalAlign="middle">{filterData[filterData.length - 1].label}</Text>
							</Col>
							<Col>
								<TagSelect
									options={filterData[filterData.length - 1].options}
									value={filterData[filterData.length - 1].virtualVal}
									onChange={(val) => {
										filterData[filterData.length - 1].virtualVal = [...val];
										setFilterData([
											...filterData,
										]);
										refreshAllParams(filterData[filterData.length - 1].value, val);
									}
									}
									style={{
										maxWidth: '1000px',
									}}
								></TagSelect>
							</Col>
						</Row>
						<Row style={{
							justifyContent: 'center',
							margin: '20px 10px 10px 10px',
						}}>
							<Button type='primary' onClick={() => {
								setFilterParams({
									...filterParams,
									Offset: 0,
								});
								getTabData({
									...filterParams,
									Offset: 0,
								});
							}}>查询</Button>
							<Button style={{
								marginLeft: '10px',
							}} onClick={() => {
								resetFilterData(filterGetData);
								setFilterParams({
									...cloneDeep(initParams),
								});
								getTabData({
									...cloneDeep(initParams),
								});
							}}>重置</Button>
							<Button
								style={{ marginLeft: '10px' }}
								onClick={() => {
									window.open(`${REPORT_DOMAIN}${HEALTH_REPORT_URL}`);
								}}
							>
								查看运营报告
							</Button>
						</Row>
					</Card.Body>
				</Card>
				<Card>
					<Card.Body>
						<Table
							records = {
								records.AlarmLists ? records.AlarmLists : []
							}
							columns={columns}
							topTip={
								(loading || records.AlarmLists.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
							}
							addons={
								[
									columnsResizable({
										onResizeEnd: (columns) => {
											setColumns(columns);
										},
										minWidth: 100,
										maxWidth: 1000,
										columns: ['Product'],
									}),
									pageable({
										recordCount: records.TotalCount ? records.TotalCount : 0,
										onPagingChange: ({ pageIndex, pageSize }) => {
											setFilterParams({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize,
											});
											getTabData({
												...filterParams,
												Limit: pageSize,
												Offset: (pageIndex - 1) * pageSize,
											});
										},
										pageSizeOptions: [10, 20, 30, 50, 100, 200],
										pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
										pageSize: filterParams.Limit,
									}),
									scrollable({
										minWidth: 1800,
									}),
								]
							}
						/>
						<AlarmDetailModal
							visible={detailVisible}
							id={detailId}
							appId={appId}
							handleVisible={(refresh) => {
								setDetailVisible(false);
								if (refresh) {
									getTabData(filterParams);
								}
							}}></AlarmDetailModal>
					</Card.Body>
				</Card>
			</Content.Body>
		</Body>
	);
}
