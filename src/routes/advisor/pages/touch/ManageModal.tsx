/* eslint-disable no-nested-ternary */
/* eslint-disable @typescript-eslint/indent */
/* eslint-disable indent */
/* eslint-disable no-param-reassign */
import React, { useEffect, useMemo, useState } from 'react';
import { Modal, Button, message, Table, Checkbox, StatusTip, Text, Icon } from '@tencent/tea-component';
import { getStorage } from '@src/utils/storage';
import { cloneDeep, some, difference, isEqual, sortBy, intersection } from 'lodash';
import { getDescribeCustomerPolicy, modifyCustomerPolicy } from '@src/api/advisor/touch';
import StuffSearchTDesign from '../../components/stuff-search-tdesign';
import s from './ManageModal.module.less';

export function ManageModal({ visible, handleVisible, id, title }) {
	const [records, setRecords] = useState([]);
	// 批量编辑电话指定人员弹窗
	const [benchmarkVisible, setBenchmarkVisible] = useState(false);
	// 批量编辑电话指定人员选项
	const [benchmarkOptions, setBenchmarkOptions] = useState([]);
	const [staticRecords, setStaticRecords] = useState([]);
	const [loading, setLoading] = useState(false);
	const [currentChnageInfo, setCurrentChnageInfo] = useState(null);
	const [saveBtnNotDiaabled, setSaveBtnNotDiaabled] = useState(true);
	const [tdCheckConfig, setTdCheckConfig] = useState({
		In: undefined,
		Out: undefined,
		Front: undefined,
		Call: undefined,
		CallCustom: undefined,
		indeterminateIn: false,
		indeterminateOut: false,
		indeterminateFront: false,
		indeterminateCall: false,
    indeterminateCallCustom: false,
	});
	const [tdCheckConfigValIn, setTdCheckConfigValIn] = useState<any>({
		In: undefined,
		indeterminateIn: false,
	});
	const [tdCheckConfigValOut, setTdCheckConfigValOut] = useState<any>({
		Out: undefined,
		indeterminateOut: false,
	});
	const [tdCheckConfigValFront, setTdCheckConfigValFront] = useState<any>({
		Front: undefined,
		indeterminateFront: false,
	});
	const [tdCheckConfigValCall, setTdCheckConfigValCall] = useState<any>({
		Call: undefined,
		indeterminateCall: false,
	});
  // 设置电话指定人是否选中、半选
  const [tdCheckConfigValCallCustom, setTdCheckConfigValCallCustom] = useState<any>({
		CallCustom: undefined,
		indeterminateCallCustom: false,
	});

	/**
	 * @description: 通过当前产品策略list计算出产品的checked状态,用于批量编辑
	 * @param {Array} list
	 * @param {Object} currentChnageInfo
	 * @return {Object}
	 */
	const changeItemTotalSel = (list, currentChnageInfo?: any) => {
		let In = true;
		let Out = true;
		let Front = true;
		let Call = true;
    let CallCustom = true;
		let indeterminateIn = false;
		let indeterminateOut = false;
		let indeterminateFront = false;
		let indeterminateCall = false;
		let indeterminateCallCustom = false;
		list.forEach((item) => {
			if (currentChnageInfo) {
				if (item.Id === currentChnageInfo.Id && item.Name === currentChnageInfo.Name) {
					item = {
						...currentChnageInfo,
					};
				}
			}
			if (item.In && In) {
				In = true;
			} else {
				In = false;
			}
			if (item.In) {
				indeterminateIn = true;
			}
			if (item.Out && Out) {
				Out = true;
			} else {
				Out = false;
			}
			if (item.Out) {
				indeterminateOut = true;
			}
			if (item.Front && Front) {
				Front = true;
			} else {
				Front = false;
			}
			if (item.Front) {
				indeterminateFront = true;
			}
			if (item.Call && Call) {
				Call = true;
			} else {
				Call = false;
			}
			if (item.Call) {
				indeterminateCall = true;
			}
      if (item.CallCustom && CallCustom) {
				CallCustom = true;
			} else {
				CallCustom = false;
			}
			if (item.CallCustom) {
				indeterminateCallCustom = true;
			}
		});
		return {
			In,
			Out,
			Front,
			Call,
      CallCustom,
			indeterminateIn: In ? false : indeterminateIn,
			indeterminateOut: Out ? false : indeterminateOut,
			indeterminateFront: Front ? false : indeterminateFront,
			indeterminateCall: Call ? false : indeterminateCall,
			indeterminateCallCustom: CallCustom ? false : indeterminateCallCustom,
		};
	};
	useMemo(() => {
		if (currentChnageInfo) {
      // 根据currentChnageInfo来
			if (currentChnageInfo.Title) {
				records.forEach((el) => {
					if (el.Title === currentChnageInfo.Title) {
						if (currentChnageInfo.In !== undefined) {
							el.In = currentChnageInfo.In;
							if (currentChnageInfo.In) {
								el.indeterminateIn = false;
							}
						}
						if (currentChnageInfo.Out !== undefined) {
							el.Out = currentChnageInfo.Out;
							el.Front = currentChnageInfo.Front;
							if (currentChnageInfo.Out) {
								el.indeterminateOut = false;
							}
						}
						if (currentChnageInfo.Front !== undefined) {
							el.Front = currentChnageInfo.Front;
							el.Out = currentChnageInfo.Out;
							if (currentChnageInfo.Front) {
								el.indeterminateFront = false;
							}
						}
						if (currentChnageInfo.Call !== undefined) {
							el.Call = currentChnageInfo.Call;
							if (currentChnageInfo.Call) {
								el.indeterminateCall = false;
							}
						}
            if (currentChnageInfo.CallCustom !== undefined) {
							el.CallCustom = currentChnageInfo.CallCustom;
							if (currentChnageInfo.CallCustom) {
								el.indeterminateCallCustom = false;
							}
						}
						el.Policy.forEach((val) => {
							if (currentChnageInfo.index === 2) {
								val.In = currentChnageInfo.sel;
							} else if (currentChnageInfo.index === 3) {
								if (val.IsOutActive) {
									val.Out = currentChnageInfo.sel;
									val.Front = currentChnageInfo.sel;
								}
							} else if (currentChnageInfo.index === 4) {
								if (val.IsFrontActive) {
									val.Front = currentChnageInfo.sel;
									val.Out = currentChnageInfo.sel;
								}
							} else if (currentChnageInfo.index === 5) {
								if (val.IsCallActive) {
									val.Call = currentChnageInfo.sel;
								}
							} else if (currentChnageInfo.index === 6) {
								if (val.IsCallCustomActive) {
									val.CallCustom = currentChnageInfo.sel;
								}
							}
						});
					}
				});
			} else {
				records.forEach((el) => {
          // 给产品级别添加 （策略级） 字段，用于表格根据 record 来展示 产品级row 以及控制 产品级row 是否选中与是否半选
					// eslint-disable-next-line max-len
					const { In, Out, Front, Call, indeterminateIn, indeterminateOut, indeterminateFront, indeterminateCall, indeterminateCallCustom, CallCustom } = changeItemTotalSel(el.Policy, currentChnageInfo);
					el.In = In;
					el.Out = Out;
					el.Front = Front;
					el.Call = Call;
          el.CallCustom = CallCustom;
					el.indeterminateIn = indeterminateIn;
					el.indeterminateOut = indeterminateOut;
					el.indeterminateFront = indeterminateFront;
					el.indeterminateCall = indeterminateCall;
          el.indeterminateCallCustom = indeterminateCallCustom;
				});
			}
			setRecords(records);
		}
	}, [currentChnageInfo]);
	useEffect(() => {
		let In = true;
		let Out = true;
		let Front = true;
		let Call = true;
    // 电话指定人默认启用状态
    let CallCustom = true;
		let indeterminateIn = false;
		let indeterminateOut = false;
		let indeterminateFront = false;
		let indeterminateCall = false;
    // 电话指定人默认是否半选
		let indeterminateCallCustom = false;
		const total = records.length;
		let InNum = 0;
		let OutNum = 0;
		let FrontNum = 0;
		let CallNum = 0;
    // 统计电话指定人是 checked 的次数
    let CallCustomNum = 0;
		records.forEach((el) => {
			if (el.In || el.indeterminateIn) {
				// eslint-disable-next-line no-plusplus
				InNum++;
			}
			if (el.Out || el.indeterminateOut) {
				// eslint-disable-next-line no-plusplus
				OutNum++;
			}
			if (el.Front || el.indeterminateFront) {
				// eslint-disable-next-line no-plusplus
				FrontNum++;
			}
			if (el.Call || el.indeterminateCall) {
				// eslint-disable-next-line no-plusplus
				CallNum++;
			}
      if (el.CallCustom || el.indeterminateCallCustom) {
				// eslint-disable-next-line no-plusplus
				CallCustomNum++;
			}
		});
		if (InNum === total) {
			In = true;
		} else if (InNum > 0) {
			In = false;
			indeterminateIn = true;
		} else {
			In = false;
			indeterminateIn = false;
		}
		if (OutNum === total) {
			Out = true;
		} else if (OutNum > 0) {
			Out = false;
			indeterminateOut = true;
		} else {
			Out = false;
			indeterminateOut = false;
		}
		if (FrontNum === total) {
			Front = true;
		} else if (FrontNum > 0) {
			Front = false;
			indeterminateFront = true;
		} else {
			Front = false;
			indeterminateFront = false;
		}
		if (CallNum === total) {
			Call = true;
		} else if (CallNum > 0) {
			Call = false;
			indeterminateCall = true;
		} else {
			Call = false;
			indeterminateCall = false;
		}
    // 判断电话指定人是否选中、半选
    if (CallCustomNum === total) {
      // 全选
			CallCustom = true;
		} else if (CallCustomNum > 0) {
      // 选中部分
			CallCustom = false;
			indeterminateCallCustom = true;
		} else {
      // 全部未选
			CallCustom = false;
			indeterminateCallCustom = false;
		}
		setTdCheckConfigValIn({
			In,
			indeterminateIn,
		});
		setTdCheckConfigValOut({
			Out,
			indeterminateOut,
		});
		setTdCheckConfigValFront({
			Front,
			indeterminateFront,
		});
		setTdCheckConfigValCall({
			Call,
			indeterminateCall,
		});
    // 设置电话指定人是否选中、半选
		setTdCheckConfigValCallCustom({
			CallCustom,
			indeterminateCallCustom,
		});
	}, [records, currentChnageInfo]);

  /**
   * @description: 监测是否有未选中的告警策略
   * @param {Array} records
   * @return {Boolean}
   */
  const formChecked = useMemo(() => {
    // 勾选“电话指定人” 一定要选择“指定人员账号”
    // eslint-disable-next-line
    for (const element of records) {
      // eslint-disable-next-line
      for (const policy of (element?.Policy || [])) {
        if (policy?.CallCustom && (!policy?.CallUserList || !policy?.CallUserList?.length)) {
          return false;
        }
      }
    }
    return true;
  }, [records, currentChnageInfo]);

	useMemo(() => {
		// eslint-disable-next-line max-len
		if (
      // 根据告警策略 顶部 是否全选 来控制 产品是否半选和选中 以及产品下面的策略是否选中
			tdCheckConfigValIn.needChange
			|| tdCheckConfigValOut.needChange
			|| tdCheckConfigValFront.needChange
			|| tdCheckConfigValCall.needChange
      || tdCheckConfigValCallCustom.needChange
		) {
			records.forEach((el) => {
				if (tdCheckConfigValIn.In !== undefined && tdCheckConfigValIn.needChange) {
					el.In = tdCheckConfigValIn.In;
					el.indeterminateIn = false;
				}
				if (tdCheckConfigValOut.Out !== undefined && tdCheckConfigValOut.needChange && el.IsProductOutActive) {
					el.Out = tdCheckConfigValOut.Out;
					el.Front = tdCheckConfigValOut.Out;
					el.indeterminateOut = false;
				}
				// eslint-disable-next-line max-len
				if (tdCheckConfigValFront.Front !== undefined && tdCheckConfigValFront.needChange && el.IsProductFrontActive) {
					el.Front = tdCheckConfigValFront.Front;
					el.Out = tdCheckConfigValFront.Front;
					el.indeterminateFront = false;
				}
				// eslint-disable-next-line max-len
				if (tdCheckConfigValCall.Call !== undefined && tdCheckConfigValCall.needChange && el.IsProductCallActive) {
					el.Call = tdCheckConfigValCall.Call;
					el.indeterminateCall = false;
				}
        // eslint-disable-next-line max-len
				if (tdCheckConfigValCallCustom.CallCustom !== undefined && tdCheckConfigValCallCustom.needChange && el.IsProductCallCustomActive) {
					el.CallCustom = tdCheckConfigValCallCustom.CallCustom;
					el.indeterminateCallCustom = false;
				}
				el.Policy.forEach((val) => {
					if (tdCheckConfigValIn.In !== undefined && tdCheckConfigValIn.needChange) {
						val.In = tdCheckConfigValIn.In;
					}
					if (tdCheckConfigValOut.Out !== undefined && tdCheckConfigValOut.needChange && val.IsOutActive) {
						val.Out = tdCheckConfigValOut.Out;
						val.Front = tdCheckConfigValOut.Out;
					}
					// eslint-disable-next-line max-len
					if (tdCheckConfigValFront.Front !== undefined && tdCheckConfigValFront.needChange && val.IsFrontActive) {
						val.Front = tdCheckConfigValFront.Front;
						val.Out = tdCheckConfigValFront.Front;
					}
					// eslint-disable-next-line max-len
					if (tdCheckConfigValCall.Call !== undefined && tdCheckConfigValCall.needChange && val.IsCallActive) {
						val.Call = tdCheckConfigValCall.Call;
					}
          // eslint-disable-next-line max-len
					if (tdCheckConfigValCallCustom.CallCustom !== undefined && tdCheckConfigValCallCustom.needChange && val.IsCallCustomActive) {
						val.CallCustom = tdCheckConfigValCallCustom.CallCustom;
					}
				});
			});
			setRecords(records);
		}
	// eslint-disable-next-line max-len
	}, [tdCheckConfigValIn, tdCheckConfigValOut, tdCheckConfigValFront, tdCheckConfigValCall, tdCheckConfigValCallCustom]);
	const columns = [
		{
			key: 'Name',
			header: '告警策略',
			width: 250,
		},
		{
			key: 'In',
			header: (
				<Checkbox
					indeterminate={tdCheckConfigValIn.indeterminateIn}
					value={tdCheckConfigValIn.In}
					onChange={(value) => {
						setTdCheckConfigValIn({
							In: value,
							indeterminateIn: false,
							needChange: true,
						});
						setTdCheckConfig({
							...tdCheckConfig,
							In: value,
							indeterminateIn: false,
						});
					}}
				>
					内部通知
				</Checkbox>
			),
			render: (item, rowKey, recordIndex, column, columnIndex) => {
				if (!item.Policy) {
					return (
						<Checkbox
							value={item.In}
							onChange={(val) => {
								item.In = val;
								setCurrentChnageInfo({
									...item,
								});
							}}
						/>
					);
				}
				return (
					<Checkbox
						indeterminate={item.indeterminateIn}
						value={item.In || false}
						onChange={(val) => {
							item.In = val;
							item.sel = val;
							item.index = columnIndex;
							setCurrentChnageInfo(cloneDeep(item));
						}}
					/>
				);
			},
		},
		{
			key: 'Out',
			header: (
				<Checkbox
					indeterminate={tdCheckConfigValOut.indeterminateOut}
					value={tdCheckConfigValOut.Out}
					onChange={(value) => {
						setTdCheckConfigValOut({
							Out: value,
							indeterminateOut: false,
							needChange: true,
						});
						setTdCheckConfigValFront({
							Front: value,
							indeterminateFront: false,
							needChange: true,
						});
						setTdCheckConfig({
							...tdCheckConfig,
							Out: value,
							indeterminateOut: false,
						});
					}}
				>
					客户售后群
				</Checkbox>
			),
			render: (item, rowKey, recordIndex, column, columnIndex) => {
				if (!item.Policy) {
					return <div style={{ display: 'flex' }}>
						<Checkbox
							value={item.Out}
							disabled={!item.IsOutActive}
							onChange={
								async (val) => {
									if (val && item?.IsUnsubscribed) {
										const isSure = await Modal.confirm({
											caption: '是否已与客户确认订阅告警？',
											message: <div style={{ fontSize: 14, fontWeight: 'normal' }}>
												<p>
													{`客户于${item.UnsubscribeTime}提交了该项告警策略退订请求，如需恢复订阅请及时与客户确认。`}
												</p>
												<p style={{ marginTop: 30, color: 'red' }}>*客户多次退订将导致策略扣分过多禁用！</p>
											</div>,
											okText: '确认',
											cancelText: '取消',
										});
										if (!isSure) return;
										item.Out = val;
										item.Front = val;
										setCurrentChnageInfo({
											...item,
										});
									} else {
										item.Out = val;
										item.Front = val;
										setCurrentChnageInfo({
											...item,
										});
									}
								}
							}
						/>
						{
							item?.IsUnsubscribed
							&& <Text theme="danger">
								客户退订！
							</Text>
						}
					</div>;
				}
				return <Checkbox
					disabled={!item.IsProductOutActive}
					indeterminate={item.indeterminateOut}
					value={item.Out || false}
					onChange={
						async (val) => {
							if (some(item.Policy, i => i.IsUnsubscribed) && val) {
								const isSure = await Modal.confirm({
									caption: '是否已与客户确认订阅告警？',
									message: <div style={{ fontSize: 14, fontWeight: 'normal' }}>
										<p>
											客户于提交了该项告警策略退订请求，如需恢复订阅请及时与客户确认。
										</p>
										<p style={{ marginTop: 30, color: 'red' }}>*客户多次退订将导致策略扣分过多禁用！</p>
									</div>,
									okText: '确认',
									cancelText: '取消',
								});
								if (!isSure) return;
								item.Out = val;
								item.Front = val;
								item.sel = val;
								item.index = columnIndex;
								setCurrentChnageInfo(cloneDeep(item));
							} else {
								item.Out = val;
								item.Front = val;
								item.sel = val;
								item.index = columnIndex;
								setCurrentChnageInfo(cloneDeep(item));
							}
						}
					}
				/>;
			},
		},
		{
			key: 'Front', // 一线
			header: (
				<Checkbox
					indeterminate={tdCheckConfigValFront.indeterminateFront}
					value={tdCheckConfigValFront.Front}
					onChange={(value) => {
						setTdCheckConfigValFront({
							Front: value,
							indeterminateFront: false,
							needChange: true,
						});
						setTdCheckConfigValOut({
							Out: value,
							indeterminateOut: false,
							needChange: true,
						});
						setTdCheckConfig({
							...tdCheckConfig,
							Front: value,
							indeterminateFront: false,
						});
					}}
				>
					建单给一线
				</Checkbox>
			),
			render: (item, rowKey, recordIndex, column, columnIndex) => {
				if (!item.Policy) {
					return (
						<Checkbox
							disabled={!item.IsFrontActive}
							value={item.Front}
							onChange={(val) => {
								item.Front = val;
								item.Out = val;
								setCurrentChnageInfo({
									...item,
								});
							}}
						/>
					);
				}
				return (
					<Checkbox
						disabled={!item.IsProductFrontActive}
						indeterminate={item.indeterminateFront}
						value={item.Front || false}
						onChange={(val) => {
							item.Front = val;
							item.Out = val;
							item.sel = val;
							item.index = columnIndex;
							setCurrentChnageInfo(cloneDeep(item));
						}}
					/>
				);
			},
		},
		{
			key: 'Call', // 电话售后负责人
			header: (
				<Checkbox
					style={{
						whiteSpace: 'nowrap',
					}}
					indeterminate={tdCheckConfigValCall.indeterminateCall}
					value={tdCheckConfigValCall.Call}
					onChange={(value) => {
						setTdCheckConfigValCall({
							Call: value,
							indeterminateCall: false,
							needChange: true,
						});
						setTdCheckConfig({
							...tdCheckConfig,
							Call: value,
							indeterminateCall: false,
						});
					}}
				>
					电话售后负责人
				</Checkbox>
			),
			render: (item, rowKey, recordIndex, column, columnIndex) => {
        // 非产品维度 即 策略维度
				if (!item.Policy) {
					return (
						<Checkbox
							disabled={!item.IsCallActive}
							value={item.Call}
							onChange={(val) => {
								item.Call = val;
								setCurrentChnageInfo({
									...item,
								});
							}}
						/>
					);
				}
        // 产品维度
				return (
					<Checkbox
						disabled={!item.IsProductCallActive}
						indeterminate={item.indeterminateCall}
						value={item.Call || false}
						onChange={(val) => {
							item.Call = val;
							item.sel = val;
							item.index = columnIndex;
							setCurrentChnageInfo(cloneDeep(item));
						}}
					/>
				);
			},
		},
		{
			key: 'CallCustom', // 电话指定人
			header: (
				<>
					<Checkbox
						style={{
							whiteSpace: 'nowrap',
							marginRight: 0,
						}}
						indeterminate={tdCheckConfigValCallCustom.indeterminateCallCustom}
						value={tdCheckConfigValCallCustom.CallCustom}
						onChange={(value) => {
							setTdCheckConfigValCallCustom({
								CallCustom: value,
								indeterminateCallCustom: false,
								needChange: true,
							});
							setTdCheckConfig({
								...tdCheckConfig,
								CallCustom: value,
								indeterminateCallCustom: false,
							});
						}}
					>
						电话指定人
					</Checkbox>
					<Icon type="pencil" className={s.pencil} onClick={() => setBenchmarkVisible(true)} />
					<Modal
						visible={benchmarkVisible}
						// @ts-ignore
						size="s"
						caption={'批量编辑电话指定人员'}
						onClose={() => setBenchmarkVisible(false)}
					>
						<Modal.Body>
							<StuffSearchTDesign value={benchmarkOptions} onChange={v => setBenchmarkOptions(v)} />
						</Modal.Body>
						<Modal.Footer>
							<Button
								type="primary"
								onClick={() => {
                  if (benchmarkOptions?.length) {
                    records.forEach((v) => {
                      v.ProductCallUserList = benchmarkOptions;
                      (v.Policy || []).forEach((element) => {
                        element.CallUserList = benchmarkOptions;
                      });
                    });
                    setBenchmarkVisible(false);
                    setBenchmarkOptions([]);
                  } else {
                    message.error({
                      content: '请选择指定人员',
                    });
                  }
								}}
							>
								确定
							</Button>
							<Button
								onClick={() => {
                  setBenchmarkOptions([]);
									setBenchmarkVisible(false);
								}}
							>
								取消
							</Button>
						</Modal.Footer>
					</Modal>
				</>
			),
			render: (item, rowKey, recordIndex, column, columnIndex) => {
        // 非产品维度 即 策略维度
				if (!item.Policy) {
					return (
						<Checkbox
              disabled={!item.IsCallCustomActive}
              value={item.CallCustom}
              onChange={(val) => {
                item.CallCustom = val;
                setCurrentChnageInfo({
                  ...item,
                });
              }}
            />
					);
				}
        // 产品维度
				return (
					<Checkbox
						disabled={!item.IsProductCallCustomActive}
						indeterminate={item.indeterminateCallCustom}
						value={item.CallCustom || false}
						onChange={(val) => {
							item.CallCustom = val;
							item.sel = val;
							item.index = columnIndex;
							setCurrentChnageInfo(cloneDeep(item));
						}}
					/>
				);
			},
		},
		{
			key: 'Accounts',
			width: 180,
			header: (
				<span
					style={{
						whiteSpace: 'nowrap',
					}}
				>
					指定人员账号
				</span>
			),
			// eslint-disable-next-line max-len
			render: (item, rowKey, recordIndex, column, columnIndex) => <StuffSearchTDesign disable={'indeterminateCallCustom' in item ? !(item.indeterminateCallCustom || item.CallCustom) : !item.CallCustom} emptyErrorMsg={('indeterminateCallCustom' in item) ? '' : item.CallCustom ? '请选择指定人员' : ''} value={'Policy' in item ? item.ProductCallUserList || [] : item.CallUserList || []} onChange={(v) => {
          if ('Policy' in item) {
            // 新增 - 产品新增同步到策略新增
            if ((item.ProductCallUserList || []).length < v.length) {
              item?.Policy?.forEach((el, i) => {
                if (!el.CallUserList) {
                  el.CallUserList = [];
                }
                const addList = difference(v,  el.CallUserList);
                if (addList?.length) {
                  el.CallUserList = [...el.CallUserList, ...addList];
                }
              });
            } else {
              // 删除 - 策略删除同步到产品删除
              const removeList = difference((item.ProductCallUserList || []),  v);
              item?.Policy?.forEach((el, i) => {
                if (!el.CallUserList) {
                  el.CallUserList = [];
                }
                if (removeList?.length) {
                  el.CallUserList = difference(el.CallUserList, removeList);
                }
              });
            }
            item.ProductCallUserList = v;
          } else {
            // 策略中都有的 用户 新增到产品上
            const data = records[recordIndex];
            const removeList = difference((item.CallUserList || []),  v);
            item.CallUserList = v;
            const callUserLists = (data?.Policy || []).map(v => v?.CallUserList);
            // 查找策略中都有的用户
            // eslint-disable-next-line prefer-spread
            const common = intersection.apply(null, callUserLists);
            if (common?.length) {
              if (!data.ProductCallUserList) {
                data.ProductCallUserList = [...common];
              } else {
                // eslint-disable-next-line max-len
                const uniqueUsers = difference(common, data.ProductCallUserList);
                data.ProductCallUserList = data.ProductCallUserList.concat(uniqueUsers);
              }
            }
            if (removeList?.length) {
              // eslint-disable-next-line max-len
              data.ProductCallUserList = data.ProductCallUserList?.filter(element => !removeList.includes(element));
            }
          }
          setRecords([...records]);
        }} />,
		},
	];
	const save = async () => {
		try {
			const list = [];
			records.forEach((item, i) => {
				item.Policy.forEach((el, j) => {
					if (
						staticRecords[i].Policy[j].In !== el.In
						|| staticRecords[i].Policy[j].Out !== el.Out
						|| staticRecords[i].Policy[j].Front !== el.Front
						|| staticRecords[i].Policy[j].Call !== el.Call
						|| staticRecords[i].Policy[j].CallCustom !== el.CallCustom
            || !isEqual(sortBy(staticRecords[i].Policy[j].CallUserList), sortBy(el.CallUserList))
					) {
						list.push({
							...el,
						});
					}
				});
			});
      // 勾选“电话指定人” 一定要选择“指定人员账号” 方案2
      // eslint-disable-next-line
      // for (const element of records) {
      //   // eslint-disable-next-line
      //   for (const policy of element.Policy) {
      //     if (policy?.CallCustom && (!policy?.CallUserList || !policy?.CallUserList?.length)) {
      //       message.error({
      //         content: '请选择指定人员',
      //       });
      //       break;
      //     }
      //   }
      // }

			const res = await modifyCustomerPolicy({
				AppId: id,
				Name: getStorage('engName'),
				Modify: list,
				OnlyData: true,
				ShowError: true,
			});
			message.success({
				content: res.Message,
			});
			handleVisible(true);
		} catch (err) {}
	};
	const { expandable, scrollable } = Table.addons;
	const [expandedKeys, setExpandedKeys] = useState([]);
	useMemo(async () => {
		if (visible) {
			setLoading(true);
			try {
				const res = await getDescribeCustomerPolicy({
					AppId: id,
					Name: getStorage('engName'),
					ShowError: true,
					OnlyData: true,
				});
				const list: any = [];
				// eslint-disable-next-line no-restricted-syntax
				for (const key in res.PolicyInfo) {
					const colSelResult = changeItemTotalSel(res.PolicyInfo[key]?.Policy);
					list.push({
						...res.PolicyInfo[key],
						Name: res.PolicyInfo[key].Title,
						sel: false,
						...colSelResult,
					});
				}
				setSaveBtnNotDiaabled(res.IsActive);
				setExpandedKeys([String(0)]);
				setStaticRecords(cloneDeep(list));
				setRecords(list);
				setLoading(false);
			} catch (err) {
				setLoading(false);
			}
		}
	}, [visible]);
	return (
		<Modal
			visible={visible}
			caption={title}
			size={1200}
			className={'manage-modal'}
			onClose={() => {
				handleVisible();
			}}
		>
			<Modal.Body>
				<Table
					records={records}
					columns={columns}
					topTip={(loading || records.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />}
					recordKey={(record, i) => {
						if (record.Id) {
							return String(record.Id);
						}
						return String(i);
					}}
					addons={[
						expandable({
							expandedKeys,
							// 产品展开为消息行
							expand: record => record.Policy || null,
							// 发生展开行为时，回调更新展开键值
							onExpandedKeysChange: (keys) => {
								setExpandedKeys(keys);
							},
							// 只有产品行允许展开
							shouldRecordExpandable: record => Boolean(record.Policy),
						}),
						scrollable({
							maxHeight: 500,
						}),
					]}
				/>
			</Modal.Body>
			<Modal.Footer>
				<Button type="primary" disabled={!saveBtnNotDiaabled || !formChecked} onClick={save}>
					保存
				</Button>
				<Button
					type="weak"
					onClick={() => {
						handleVisible();
					}}
				>
					取消
				</Button>
			</Modal.Footer>
		</Modal>
	);
}
