import React, { useState, useMemo, useEffect } from 'react';
import { Table, Button, Row, Card, Layout, Col, Select, Text, Input, StatusTip } from '@tencent/tea-component';
import { get } from 'lodash';
import { POLICY_URL } from '../../constants';
import {
	describeExternalAlarmInfo,
} from '@src/api/advisor/touch';
import { getDescribeProductList } from '@src/api/advisor/faultNotification';
const { Body, Content } = Layout;

const initFilterData = {
	product: '',
	policyId: '',
	policy: '',
	policyOwner: '',
};

// 分页
const { pageable } = Table.addons;

export function PlanManage() {
	const [loading, setLoading] = useState(false);

	const [filterData, setFilterData] = useState(initFilterData);
	const [externalPolicyList, setExternalPolicyList] = useState([]);

	const [pageSize, setPageSize] = useState<number>(10);
	const [totalPage, setTotalPage] = useState<number>(0);
	const [pageNumber, setPageNumber] = useState<number>(1);
	const [policyOption, setPolicyOption] = useState([]);
	const [productOption, setProductOption] = useState([]);

	const columns = [
		{
			key: 'ProductName',
			header: '产品',
		},
		{
			key: 'PolicyName',
			header: '告警策略',
			render: item => (
				<Text overflow tooltip>{item?.PolicyName}</Text>
			),
		},
		{
			key: 'PolicyId',
			header: '策略ID',
		},
		{
			key: 'PolicyStatus',
			header: '策略状态',
			render: item => (
				<Text theme={item.PolicyStatus === '已停用' ? 'danger' : 'strong'}>{item.PolicyStatus}</Text>
			),
		},
		{
			key: 'PolicyOwner',
			header: '策略负责人',
		},
		{
			key: 'PolicyOwnerCenter',
			header: '策略负责中心',
		},
		{
			key: 'PolicyScore',
			header: '信用分（累计退订次数）',
			render: item => (
				<div>
					<Text theme={item.PolicyScore < 60 ? 'danger' : 'primary'}>
						{`${item.PolicyScore}（${100 - item.PolicyScore}）`}
					</Text>
					<a href={POLICY_URL} target="_blank" rel="noopener noreferrer" style={{ color: 'red' }}>重置分数</a>
				</div>
			),
		},
	];

	const policyListOption = useMemo(() => {
		const policyOptionList = policyOption?.map(item => ({
			text: item?.PolicyName,
			value: item?.PolicyId,
		}));
		policyOptionList?.unshift({
			text: '全部策略',
			value: '',
		});
		return policyOptionList ?? [];
	}, [policyOption]);

	useEffect(() => {
		getExternalAlarmInfo(filterData);
	}, [pageSize, pageNumber]);

	useEffect(() => {
		getProductDict();
	}, []);

	const getExternalAlarmInfo = async (filters) => {
		setLoading(true);
		try {
			const { product, policyId, policy, policyOwner } = filters || {};
			const res = await describeExternalAlarmInfo({
				Product: product,
				PolicyId: +policyId,
				PolicyList: policy ? [+policy] : [],
				PolicyOwner: policyOwner,
				Offset: (pageNumber - 1) * pageSize,
				Limit: pageSize,
			});
			const resData = get(res, 'data.Response');
			setTotalPage(resData?.Total);
			setExternalPolicyList(resData?.ExternalPolicyList || []);
			setPolicyOption(resData?.PolicyNameList);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};

	const getProductDict = async () => {
		const result = await getDescribeProductList({
			OnlyData: true,
			ShowError: true,
			AppId: 1253985742,
		});
		const arr = [
			{ text: '全部产品', value: '' },
		];
		for (const key in result.ProductDict) {
			arr.push({
				text: result.ProductDict[key],
				value: key,
			});
		}
		setProductOption(arr);
	};
	const handlePageChange = ({ pageIndex, pageSize }) => {
		setPageSize(pageSize);
		setPageNumber(pageIndex);
	};

	return (
		<Body>
			<Content.Body>
				<Card>
					<Card.Body className='touch-filter-wrap'>
						<Row>
							<Col span={75}>
								<Row verticalAlign={'middle'}>
									<Col span={5} >
										<Text theme="label" verticalAlign="middle">云产品</Text>
									</Col>
									<Col span={19}>
										<Select
											options={productOption}
											appearance="button"
											matchButtonWidth
											searchable
											size='m'
											value={filterData.product}
											onChange={val => setFilterData({
												...filterData,
												product: val,
											})}
										/>
									</Col>
								</Row>
							</Col>
							<Col span={75}>
								<Row verticalAlign={'middle'}>
									<Col span={5} >
										<Text theme="label" verticalAlign="middle">策略ID</Text>
									</Col>
									<Col span={19}>
										<Input
											size='m'
											value={filterData.policyId}
											onChange={(val) => {
												setFilterData({
													...filterData,
													policyId: val,
												});
											}}
										/>
									</Col>
								</Row>
							</Col>
							<Col span={75}>
								<Row verticalAlign={'middle'}>
									<Col span={5} >
										<Text theme="label" verticalAlign="middle">告警策略</Text>
									</Col>
									<Col span={19}>
										<Select
											options={policyListOption}
											appearance="button"
											matchButtonWidth
											searchable
											size='m'
											value={filterData.policy}
											onChange={(val) => {
												setFilterData({
													...filterData,
													policy: val,
												});
											}}
										/>
									</Col>
								</Row>
							</Col>
							<Col span={75}>
								<Row verticalAlign={'middle'}>
									<Col span={5} >
										<Text theme="label" verticalAlign="middle">策略负责人</Text>
									</Col>
									<Col span={19}>
										<Input
											size='m'
											value={filterData.policyOwner}
											onChange={(val) => {
												setFilterData({
													...filterData,
													policyOwner: val,
												});
											}}
										/>
									</Col>
								</Row>
							</Col>
						</Row>
						<Row style={{ justifyContent: 'center', margin: '20px 10px 10px 10px' }}>
							<Button
								type='primary'
								onClick={() => {
									setPageNumber(1);
									getExternalAlarmInfo(filterData);
								}}
							>
                查询
							</Button>
							<Button
								style={{ marginLeft: '10px' }}
								onClick={() => {
									setFilterData(initFilterData);
									setPageNumber(1);
									getExternalAlarmInfo(initFilterData);
								}}>
                  重置
							</Button>
						</Row>
					</Card.Body>
				</Card>
				<Card>
					<Card.Body>
						<Table
							records = {externalPolicyList}
							columns={columns}
							topTip={
								(loading || externalPolicyList.length === 0) && <StatusTip status={loading ? 'loading' : 'empty'} />
							}
							addons = {
								[
									pageable({
										pageIndex: pageNumber,
										recordCount: totalPage,
										onPagingChange: handlePageChange,
									}),
								]
							}
						/>
					</Card.Body>
				</Card>
			</Content.Body>
		</Body>
	);
}
