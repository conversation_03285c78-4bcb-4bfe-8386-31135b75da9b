/* eslint-disable no-nested-ternary */
import React, { useEffect, useState } from 'react';
import { Tabs, TabPanel, Layout, ExternalLink } from '@tencent/tea-component';
import { NotPermission } from '@src/routes/NotPermission';
// import { useHistory } from '@tea/app';
import { AlarmQuery } from '@src/routes/advisor/pages/touch/AlarmQuery';
import { Manage } from '@src/routes/advisor/pages/touch/Manage';
import { PlanManage } from '@src/routes/advisor/pages/touch/components/plan-manage';
import { reportVisitPage } from '@src/utils/report';
import './index.less';

let timer;
export function ProactiveTouch() {
	const { Body, Content } = Layout;
	const [permission, setPermission] = useState(0);
	useEffect(() => {
		reportVisitPage({
			isaReportMeunName: '业务健康服务',
		});
	}, []);
	const CheckPermission = () => {
		const menuItems = JSON.parse(localStorage.getItem('menuItems'));
		if (menuItems) {
			// 判断是否存在
			const tmp = menuItems.filter((i) => {
				if (i.key === 'touch') {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
		// 临时测试
		// setPermission(1);
	};
	// 持续从localStorage获取菜单列表
	useEffect(() => {
		timer = setInterval(() => {
			CheckPermission();
		}, 10);
		if (timer && permission !== 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);
	const tabs = [
		{ id: 'index', label: '告警查询' },
		{ id: 'manage', label: '触达管理' },
		{ id: 'plan-manage', label: '策略管理' },
	];
	return (
		<Body className={'touch-wrap'}>{
			permission === 0
				?	<div></div>
				:	permission === 2
					?	<NotPermission />
					:	<>
						<Content.Header
							title={<p><span>产研告警推送</span><ExternalLink style={{ position: 'absolute', right: '24px', top: '14px', fontWeight: 'normal' }} href='https://iwiki.woa.com/p/1864488096'>查看【接入指引】</ExternalLink></p>}
						></Content.Header>
						<Content.Body>
							<Tabs
								ceiling
								animated={false}
								tabs={tabs}
								destroyInactiveTabPanel={false}
							>
								<TabPanel id="index">
									<AlarmQuery
									></AlarmQuery>
								</TabPanel>
								<TabPanel id="manage">
									<Manage
									></Manage>
								</TabPanel>
								<TabPanel id="plan-manage">
									<PlanManage />
								</TabPanel>
							</Tabs>
						</Content.Body>
					</>
		}
		</Body>
	);
}
