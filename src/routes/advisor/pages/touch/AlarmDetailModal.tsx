import React, { useMemo, useState } from 'react';
import { Modal, Card, Radio, Form, Button, Input, message, StatusTip } from '@tencent/tea-component';
import { getDescribeAlarmDetail, modifyAlarm } from "@src/api/advisor/touch";
import { getStorage } from "@src/utils/storage";

export function AlarmDetailModal({visible, handleVisible, id, appId}) {
	const [info, setInfo] = useState<any>({});
	const [loading, setLoading] = useState(false);
	const save = async ()=>{
		try {
			const res = await modifyAlarm({
				AppId: appId,
				Name: getStorage('engName'),
				Id: id,
				Block: parseInt(info.Block),
				Feedback: parseInt(info.Feedback),
				Comment: info.Comment,
				OnlyData: true,
				ShowError: true
			});
			message.success({
				content: res.Message
			});
			handleVisible(true);
		} catch (err) {}
	};
	useMemo(async ()=>{
		if (visible) {
			setLoading(true);
			try {
				const res = await getDescribeAlarmDetail({
					AppId: 1253985742,
					Id: id,
					ShowError: true,
					OnlyData: true
				});
				setInfo({
					...res,
					Feedback: res.Feedback?.split('|')[0],
					Block: res.Block?.split('|')[0]
				});
				setLoading(false);
			} catch (err) {
				setLoading(false);
			}
		}
	}, [visible]);
	return (
		<Modal visible={visible} caption="告警详情" onClose={
			()=>{
				handleVisible();
			}
		}>
			<Modal.Body>
				{
					loading
						?
						<div style={{
							minHeight: '200px',
							display: 'flex',
							justifyContent: 'center',
							alignItems: 'center'
						}}>
							<StatusTip.LoadingTip></StatusTip.LoadingTip>
						</div>
						:
						<>
							<Card>
								<Card.Body>
									{
										info?.Message?.replace('策略接口人', '产研告警推送人')?.split('\n').map((item, i)=>{
											return <div key={i}>{item}</div>;
										})
									}
								</Card.Body>
							</Card>
							<Form style={{
								marginTop: '30px'
							}}>
								<Form.Item
									label={'客户反馈'}
								>
									<Radio.Group
										value={info?.Feedback}
										onChange={value => setInfo({
										...info,
										Feedback: value
									})}>
										<Radio name="0">未反馈</Radio>
										<Radio name="1">准确</Radio>
										<Radio name="-1">不准确</Radio>
									</Radio.Group>
								</Form.Item>
								<Form.Item
									label={'屏蔽操作'}
								>
									<Radio.Group
										disabled={!info.IsActive}
										value={info?.Block}
										onChange={value => setInfo({
											...info,
											Block: value
										})}>
										<Radio name="0">不屏蔽</Radio>
										<Radio name="1">屏蔽实例所有告警</Radio>
										<Radio name="2">屏蔽实例该策略告警</Radio>
									</Radio.Group>
								</Form.Item>
								<Form.Item
									label={'备注'}
								>
									<Input
										value={info.Comment}
										onChange={(val)=>{
											setInfo({
												...info,
												Comment: val
											});
										}}
									/>
								</Form.Item>
							</Form>
						</>	
				}
			</Modal.Body>
			<Modal.Footer>
				<Button
					type="primary"
					onClick={save}
				>
					保存
				</Button>
				<Button type="weak" onClick={()=>{
					handleVisible();
				}}>
					取消
				</Button>
			</Modal.Footer>
		</Modal>
	);
}
