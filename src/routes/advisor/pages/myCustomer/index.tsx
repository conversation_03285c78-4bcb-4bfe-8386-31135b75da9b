import React, { useEffect, useMemo, useState } from 'react';
import {DescribeMyCustomers} from "@src/api/advisor/myCustomer";
import {getReportFileAsync, getReportResultAsync} from "@src/api/advisor/estimate";
import { Table, Button, Modal, Card, Layout, Segment, message, SelectMultiple, Text, Input, DatePicker, StatusTip, Checkbox, TagSelect } from '@tencent/tea-component';
import { FilterParams } from "@src/types/advisor/faultNotification";
import { cloneDeep } from "lodash";
import { getStorage } from "@src/utils/storage";
import {ComprehensiveSearch} from "@src/components/ComprehensiveSearch";
import { useHistory } from "@tea/app";
import { reportVisitPage } from '@src/utils/report';
import './index.less';
import { NotPermission } from "@src/routes/NotPermission";


const initParams = {
	Limit: 10,
	Offset: 0,
	Filters: [],
	OnlyData: true,
	ShowError: true,
	// AppId: 1253985742,
	Name: getStorage('engName')
};
let timer;
let isInit = true;
export function MyCustomer() {
	const history = useHistory();
	if (isInit && location.pathname === '/') {
    if (location.hostname === 'isa-intl.woa.com' || location.hostname === 'isa-intl-pre.woa.com') {
      history.push('/advisor/handle');
    } else {
      history.push('/advisor/new-architecture');
    }
		isInit = false;
	}
	const {Body, Content} = Layout;
	const [batchLoading, setBatchLoading] = useState(false);
	const [singleLoading, setSingleLoading] = useState(false);
	const [loadingAppid, setLoadingAppid] = useState('');
	const [selectedKeys, setSelectedKeys] = useState([]);
	const [reportTypeVisible, setReportTypeVisible] = useState(false);
	const [reportType, setReportType] = useState(1);
	const [downType, setDownType] = useState(1);
	const [filterParams, setFilterParams] = useState<FilterParams>({
		...cloneDeep(initParams)
	});
	const [records, setRecords] = useState<any>({
		CustomerInfoSet: []
	});
	const [loading, setLoading] = useState(false);
	const getColumns = (loadingAppid)=>{
		const isAbroad = location.origin.indexOf('intl') != -1;
		const columns = [
			{
				key: "CustomerName",
				header: "客户名称"
			},
			{
				key: "AppId",
				header: "APPID"
			},
			{
				key: "Uin",
				header: "UIN"
			},
			{
				key: "TsaStatus",
				header: "是否开通云顾问",
				render: item => {
					return item.TsaStatus == 0 ? '未开通' : '已开通';
				},
			},
			{
				key: "RiskManageInfos",
				header: "巡检风险治理",
				render: item => {
					return item.RiskManageInfos.map((el, i)=>{
						return <div key={i}>
							<Button
								type={'link'}
								onClick={
									()=>{
										const urlArr = el.Url.split('/');
										urlArr.splice(0, 3, '');
										history.push(urlArr.join('/'));
									}
								}
							>
								{
									el.Desc
								}
							</Button>
						</div>;
					});
				},
			},
			// {
			// 	key: "MapInfo",
			// 	header: "架构图",
			// 	render: item => {
			// 		return <Button
			// 			type={'link'}
			// 			className={'pic-btn'}
			// 			onClick={
			// 				()=>{
			// 					const urlArr = item.MapInfo.Url.split('/');
			// 					urlArr.splice(0, 3, '');
			// 					history.push(urlArr.join('/'));
			// 				}
			// 			}
			// 		>
			// 			{
			// 				item.MapInfo.Desc
			// 			}
			// 		</Button>;
			// 	},
			// },
			{
				key: "GuardInfo",
				header: "当前护航",
				render: item => {
					return <Button
						type={'link'}
						onClick={
							()=>{
								const urlArr = item.GuardInfo.Url.split('/');
								urlArr.splice(0, 3, '');
								history.push(urlArr.join('/'));
							}
						}
					>
						{
							item.GuardInfo.Desc
						}
					</Button>;
				},
			},
			{
				key: "ActiveInfos",
				header: "主动服务",
				render: item => {
					return item.ActiveInfos.map((el, i)=>{
						return <div key={i}>
							<Button
								type={'link'}
								onClick={
									()=>{
										const urlArr = el.Url.split('/');
										urlArr.splice(0, 3, '');
										history.push(urlArr.join('/'));
									}
								}
							>
								{
									el.Desc
								}
							</Button>
						</div>;
					});
				},
			},
			{
				key: "ReportInfo",
				header: "下载报告",
				fixed: 'right',
				render: item => {
					return item.ReportInfo.Desc == 2 ?
						loadingAppid == item.AppId
							?
							<StatusTip status={'loading'} loadingText={''}/>
							:
							<Button
								type={'link'}
								className={'down-btn'}
								onClick={
									()=>{
										if (batchLoading) {
											return;
										}
										setDownType(2);
										download(item.AppId, item.TaskId, 2);
									}
								}
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
								>
									<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#006eff" />
								</svg>
								下载报告
							</Button>
						:
						item.ReportInfo.Url
							?
							<Button
								type={'link'}
								className={'pic-btn'}
								onClick={
									()=>{
										const urlArr = item.ReportInfo.Url.split('/');
										urlArr.splice(0, 3, '');
										history.push(urlArr.join('/'));
									}
								}
							>
								{
									item.ReportInfo.Desc
								}
							</Button>
							: item.ReportInfo.Desc;
				},
			}
		];
		if (isAbroad) {
			columns.splice(7, 1);
		}
		return columns;
	};
	const columns: any = getColumns(loadingAppid);
	// 分页
	const {pageable, scrollable, selectable} = Table.addons;
	const getTabData = async (filterParams) => {
		setLoading(true);
		try {
			const res = await DescribeMyCustomers(filterParams);
			setRecords(res);
			setLoading(false);
		} catch (err) {
			setLoading(false);
		};
	};
	useMemo(() => {
		getTabData(filterParams);
		reportVisitPage({
			isaReportMeunName: '我的客户',
		});
	}, []);
	const [downUrlList, setDownUrlList] = useState([]);
	const [downFailUrlList, setDownFailUrlList] = useState([]);
	const getDownUrl = async (appId, resultId, downType)=>{
		try {
			const result = await getReportResultAsync({
				AppId: appId,
				ResultId: resultId
			});
			if (result.Error) {
				if (downType == 1) {
					setBatchLoading(false);
				} else {
					setLoadingAppid('');
				}
				const msg = result.Error.Message || '';
				message.error({ content: msg });
				return;
			}
			if (result.TaskStatus == 'running') {
				setTimeout(()=>{
					getDownUrl(appId, resultId, downType);
				}, 2000);
			} else {
				if (result.TaskStatus == 'success') {
					downUrlList.push({
						cos: result.CosUrl,
						pdf: result.CosUrlPdf
					});
					setDownUrlList(cloneDeep(downUrlList));
				}
			};
		} catch (err) {
			if (downType == 1) {
				setBatchLoading(false);
			} else {
				setLoadingAppid('');
			}
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	const download = async (appId, taskId, downType)=>{
		if (downType == 1) {
			setBatchLoading(true);
		} else {
			setLoadingAppid(appId);
		}
		const isAbroad = location.origin.indexOf('intl') != -1;
		try {
			const res = await getReportFileAsync({
				AppId: appId,
				Id: -1,
				Type: "Group",
				TaskId: taskId,
				Env: "public",
				Products: [],
				GroupIDs: [],
				StrategyIDs: [],
				Tags: [],
				Language: isAbroad ? 'en-US' : "zh-CN"
			});
			if (res.Error) {
				setLoadingAppid('');
				// const msg = res.Error.Message || '';
				// message.error({ content: msg });
				downFailUrlList.push(appId);
				setDownFailUrlList(cloneDeep(downFailUrlList));
				return;
			}
			getDownUrl(appId, res.ResultId, downType);
		} catch (err) {
			downFailUrlList.push(appId);
			setDownFailUrlList(cloneDeep(downFailUrlList));
			if (downType == 1) {
				setBatchLoading(false);
			} else {
				setLoadingAppid('');
			}
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	};
	useMemo(()=>{
		if (downType == 1) {
			if ((downUrlList.length + downFailUrlList.length) == selectedKeys.length && selectedKeys.length > 0) {
				setReportTypeVisible(true);
				setBatchLoading(false);
			}
		} else {
			if ((downUrlList.length + downFailUrlList.length) > 0) {
				setReportTypeVisible(true);
				setLoadingAppid('');
			}
		}
	}, [downUrlList, downFailUrlList]);
	const downloadFile = (url)=>{
		const iframe = document.createElement('iframe');
		iframe.style.display = 'none';
		document.body.appendChild(iframe);
		iframe.src = url;
		setTimeout(()=>{
			iframe.remove();
		}, 1000);
	};
	const [permission, setPermission] = useState(0);
	const CheckPermission = () => {
		const menuItems = JSON.parse(localStorage.getItem("menuItems"));
		if (menuItems) {
			// 判断是否存在
			const tmp = menuItems.filter(i => {
				if (i.key == 'my-customer') {
					return i;
				}
			});
			if (tmp.length > 0) {
				setPermission(1);
			} else {
				setPermission(2);
			}
		} else {
			setPermission(0);
		}
		// 临时测试
		// setPermission(1);
	};
	// 持续从localStorage获取菜单列表
	useEffect(() => {
		timer = setInterval(() => { CheckPermission() }, 10);
		if (timer && permission != 0) {
			clearInterval(timer);
		}
		return () => clearInterval(timer);
	}, [permission]);
	return (
		<>
			<Body className={'my-customer-wrap'}>
				{
					permission == 0
					?
						<div></div>
						:
						permission == 2
					?
							<NotPermission />
							:
							<>
								<Content.Body>
									<Card>
										<Card.Body>
											<ComprehensiveSearch
												originFilterData={
													[
														{
															label: '客户名称',
															name: 'customer_name',
															type: 'input',
															value: ''
														},
														{
															label: 'APPID',
															name: 'appid',
															type: 'input',
															value: ''
														},
														{
															label: 'UIN',
															name: 'uin',
															type: 'input',
															value: ''
														},
													]
												}
												onReset={()=>{
													setFilterParams({
														...filterParams,
														Filters: [],
														Offset: 0
													});
													getTabData({
														...filterParams,
														Filters: [],
														Offset: 0
													});
													setSelectedKeys([]);
													setReportType(1);
												}}
												onSearch={(filters)=>{
													setFilterParams({
														...filterParams,
														Filters: filters,
														Offset: 0
													});
													getTabData({
														...filterParams,
														Filters: filters,
														Offset: 0
													});
												}}
												suffix={
													<Button
														className={'batch-down-btn down-btn'}
														disabled={selectedKeys.length > 0 ? false : true}
														onClick={
															()=>{
																if (loadingAppid) {
																	return;
																}
																setDownType(1);
																records.CustomerInfoSet.forEach((item)=>{
																	if (selectedKeys.includes(item.AppId.toString())) {
																		download(item.AppId, item.TaskId, 1);
																	}
																});
															}
														}
													>
														{
															batchLoading
																?
																<>
																	<div className="progress-bg-wrap">
																		<svg className={'loading-icon'} width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="&#229;&#138;&#160;&#232;&#189;&#189;-Load"><path id="stroke3 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M11 1H12C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12V11H3V12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3H11V1Z" fill="#fff" fill-opacity="0.9"/></g></svg>
																		生成中 {
																		`${downUrlList.length}/${selectedKeys.length}`
																	}
																		<div className="progress-bg" style={
																			{
																				width: `${((downUrlList.length / selectedKeys.length) as any).toFixed(4) * 100}%`
																			}
																		}>
																			<svg className={'loading-icon-inner'} width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="&#229;&#138;&#160;&#232;&#189;&#189;-Load"><path id="stroke3 (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M11 1H12C18.0751 1 23 5.92487 23 12C23 18.0751 18.0751 23 12 23C5.92487 23 1 18.0751 1 12V11H3V12C3 16.9706 7.02944 21 12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3H11V1Z" fill="#fff" fill-opacity="0.9"/></g></svg>
																			生成中 {
																			`${downUrlList.length}/${selectedKeys.length}`
																		}
																		</div>
																	</div>
																</>
																:
																<>
																	<svg
																		xmlns="http://www.w3.org/2000/svg"
																		width="16"
																		height="16"
																	>
																		<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" />
																	</svg>
																	批量下载报告
																</>
														}
													</Button>
												}
											/>
										</Card.Body>
									</Card>
									<Card>
										<Card.Body>
											<Table
												recordKey="AppId"
												records = {
													records.CustomerInfoSet ? records.CustomerInfoSet : []
												}
												columns={columns}
												topTip={
													(loading || records.CustomerInfoSet.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
												}
												addons={
													[
														pageable({
															recordCount: records.TotalCount ? records.TotalCount : 0,
															onPagingChange: ({pageIndex, pageSize}) => {
																setFilterParams({
																	...filterParams,
																	Limit: pageSize,
																	Offset: (pageIndex - 1) * pageSize
																});
																getTabData({
																	...filterParams,
																	Limit: pageSize,
																	Offset: (pageIndex - 1) * pageSize
																});
															},
															pageSizeOptions: [10, 20, 30, 50, 100, 200],
															pageIndex: (filterParams.Offset / filterParams.Limit) + 1,
															pageSize: filterParams.Limit
														}),
														scrollable({
															minWidth: 1800
														}),
														selectable({
															value: selectedKeys,
															onChange: (keys, context) => {
																if (!batchLoading) {
																	setSelectedKeys(keys);
																};
															},
															rowSelectable(rowKey, {record}) {
																return record.ReportInfo.Desc == 2 ? true : false;
															}
														}),
													]
												}
											/>
										</Card.Body>
									</Card>
								</Content.Body>
								<Modal visible={reportTypeVisible} onClose={() => {
									setDownUrlList([]);
									setDownFailUrlList([]);
									setReportTypeVisible(false);
								}} caption={
									<Text>
										评估报告下载
										{
											downFailUrlList.length > 0
											&&
											<Text
												theme={'danger'}
												style={
													{
														marginLeft: '10px',
														wordBreak: 'break-all'
													}
												}
											>
												({
											downUrlList.length
											}/{
												downUrlList.length + downFailUrlList.length
											}生成成功,appid:{
												downFailUrlList.join(',')
											} 生成失败)
											</Text>
										}
									</Text>
								}>
									<Modal.Body>
										<span>请选择下载报告类型：</span>
										<br />

										<Segment
											value={reportType.toString()}
											onChange={(value) => setReportType(parseInt(value, 10))}
											options={[
												{
													text: (
														<>
															{'评估报告EXCEL版'}
														</>
													),
													value: '1',

													style: { width: '200px', height: '80px' },
												},
												{
													text: (
														<>
															{'评估报告PDF版'}
														</>
													),
													value: '2',

													style: { width: '200px', height: '80px', marginLeft: '10px' },
												},
											]}
										/>
									</Modal.Body>
									<Modal.Footer>
										<Button
											disabled={downUrlList.length == 0}
											type="primary"
											onClick={(e) => {
												if (reportType === 1) {
													downUrlList.forEach((item)=>{
														downloadFile(item.cos);
													});
												} else if (reportType === 2) {
													downUrlList.forEach((item)=>{
														downloadFile(item.pdf);
													});
												}
												setDownUrlList([]);
												setDownFailUrlList([]);
												setSelectedKeys([]);
												setReportType(1);
												e.stopPropagation();
												setReportTypeVisible(false);
											}}
										>
											确定
										</Button>
										<Button type="weak" onClick={() => {
											setDownUrlList([]);
											setDownFailUrlList([]);
											setReportTypeVisible(false);
										}}>
											取消
										</Button>
									</Modal.Footer>
								</Modal>
							</>
				}
			</Body>
		</>
	);
}
