.my-customer-wrap {
	.down-btn {
		display: flex;
		align-items: center;
		position: relative;
		&:hover{
			background-color: #fff;
		}
		svg {
			margin-right: 3px;
		}
		path {
			fill: #006eff;
		}
		@keyframs Routes {
			0% {
				transform: rotate(0);
			}
			100% {
				transform: rotate(1turn);
			}
		}
		.progress-bg-wrap {
			background-color: #fff;
			text-align: left;
			color: #006eff;
			display: flex;
			align-items: center;
			svg {
				fill: #006eff;
				flex: none;
				animation: Routes 1s linear infinite;
				margin-left: -10px;
			}
		}
		.progress-bg {
			height: 100%;
			background-color: #006eff;
			color: #fff;
			position: absolute;
			width: 0%;
			left: 0;
			display: flex;
			top: 0;
			align-items: center;
			overflow: hidden;
			svg {
				path {
					fill: #fff;
				}
				flex: none;
				margin-left: 10px;
				animation: Routes 1s linear infinite;
			}
		}
	}
	.batch-down-btn {
		margin-left: 10px;
	}
	.is-disabled {
		path {
			fill: #aacfff;
		}
	}
	.pic-btn {
		max-width: 200px;
		white-space: normal;
		text-align: left;
	}
}