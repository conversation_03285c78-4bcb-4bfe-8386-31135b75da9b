import React, { useMemo, useState, useEffect } from 'react';
import { useActivate } from 'react-activation';
import { Card, Layout, Button, StatusTip, Table, Text, Modal, Form, Input, message } from '@tea/component';
import { useField, useForm } from 'react-final-form-hooks';
import { ComprehensiveSearch } from '@src/components/ComprehensiveSearch';
import { DescribeStrategyList, ProcessStrategy, GetUserRole } from '@src/api/advisor/strategiesNew';
import { useHistory } from '@tea/app';
import { getDescribeProductList } from '@src/api/advisor/inspectionRelease';
import { cloneDeep } from 'lodash';
import './index.less';
import { NEEDS_TO_BE_EVALUATED_URL, STRATEGIC_OPERATIONS_REPORT_URL } from './constants';
import { Bubble } from '@tea/component/bubble';

const { Body, Content } = Layout;
const groupOptions = [
	{
		text: '安全',
		value: 1,
	},
	{
		text: '可靠',
		value: 2,
	},
	{
		text: '服务限制',
		value: 3,
	},
	{
		text: '成本',
		value: 4,
	},
	{
		text: '性能',
		value: 5,
	},
];
const groupMap = {};
groupOptions.forEach((item) => {
	groupMap[item.value] = item.text;
});
function getStatus(meta, validating) {
	if (meta.active && validating) {
		return 'validating';
	}
	if (!meta.touched) {
		return null;
	}
	return meta.error ? 'error' : 'success';
}
export function strategiesNew({ }) {
	const history = useHistory();
	const [role, setRole] = useState('');
	const [records, setRecords] = useState([]);
	const [pageLoading, setPageLoading] = useState(false);
	const [pageSize, setPageSize] = useState(10);
	const [pageIndex, setPageIndex] = useState(1);
	const { pageable, scrollable } = Table.addons;
	const [totalCount, setTotalCount] = useState(0);
	const [filters, setFilters] = useState([]);
	const [productList, setProductList] = useState([]);
	const [submitInfo, setSubmitInfo] = useState<any>({
		StrategyId: 0,
		Operation: '',
		visible: false,
	});
	const [saveLoading, setSaveLoading] = useState(false);
	const isManager = useMemo(() => role === 'Manager', [role]);
	const getUserRole = async () => {
		try {
			const res = await GetUserRole({
				OnlyData: true,
				ShowError: true,
			});
			setRole(res.Role);
		} catch (err) {};
	};
	const getDescribeStrategyList = async () => {
		setPageLoading(true);
		const obj = {};
		if (filters?.length > 0) {
			filters.forEach((item) => {
				if (item.Name === 'StrategyId') {
					obj[item.Name] = parseInt(item.Values[0]);
				} else if (item.Name === 'Time') {
					// eslint-disable-next-line @typescript-eslint/dot-notation
					obj['CreateBeginTime'] = item.Values?.[0];
					// eslint-disable-next-line @typescript-eslint/dot-notation
					obj['CreateEndTime'] = item.Values?.[1];
				}  else if (item.Name === 'Product') {
					obj[item.Name] = item.Values;
				}  else {
					obj[item.Name] = item.Values[0];
				}
			});
		}
		try {
			const res = await DescribeStrategyList({
				Filter: obj,
				PageSize: pageSize,
				Page: pageIndex,
				OnlyData: true,
			});
			setRecords(res.StrategyList);
			setTotalCount(res.Total);
			setPageLoading(false);
		} catch (err) {
			if (err?.Code === 'UnauthorizedOperation') {
				message.error({
					content: <div>
            当前用户没有操作权限，请点击<a href={'/advisor/privilege/apply-editor'}>权限申请</a>，申请“策略管理”相关权限。
					</div>,
					duration: 0,
				});
			}
			setPageLoading(false);
		}
	};
	useMemo(() => {
		setTimeout(() => {
			getDescribeStrategyList();
		}, 0);
	}, [pageIndex, pageSize, filters]);
	useActivate(() => {
		setTimeout(() => {
			getDescribeStrategyList();
		}, 0);
	});
	const handleProcessStrategy = async (info) => {
		if (info.isModalSubmit) {
			setSaveLoading(true);
		} else {
			info[`${info.Operation}Loading`] = true;
			setRecords(cloneDeep(records));
		}
		try {
			const res = await ProcessStrategy({
				StrategyId: info.StrategyId,
				Operation: info.Operation,
				Reason: info.Reason || '',
				ShowError: true,
				OnlyData: true,
			});
			if (info.isModalSubmit) {
				setSaveLoading(false);
				setSubmitInfo({
					...submitInfo,
					visible: false,
				});
			} else {
				info[`${info.Operation}Loading`] = false;
				setRecords(cloneDeep(records));
			}
			getDescribeStrategyList();
		} catch (err) {
			if (info.isModalSubmit) {
				setSaveLoading(false);
			} else {
				info[`${info.Operation}Loading`] = false;
				setRecords(cloneDeep(records));
			}
		}
	};
	const onSubmit = (value) => {
		handleProcessStrategy({
			StrategyId: submitInfo.StrategyId,
			Operation: submitInfo.Operation,
			Reason: value.Reason,
			isModalSubmit: true,
		});
	};
	const { form, validating, handleSubmit, values } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {},
		validate: (formInfo) => {
			const validateFormInfo = {
				Reason: !formInfo.Reason ? '请填写原因' : undefined,
			};
			return validateFormInfo;
		},
	});
	const reasonField = useField('Reason', form);
	useMemo(() => {
		if (!submitInfo.visible && Object.keys(reasonField.meta)?.length > 0) {
			form.reset();
			form.resetFieldState('Reason');
		}
	}, [submitInfo.visible]);
	const stateMap = {
		Init: {
			text: '新建',
			theme: 'success',
		},
		InReview: {
			text: '审核中',
			theme: 'primary',
		},
		InProcess: {
			text: '处理中',
			theme: 'primary',
		},
		Applied: {
			text: '已接入',
			theme: 'strong',
		},
		Rejected: {
			text: '已拒绝',
			theme: 'weak',
		},
		OnHold: {
			text: '已挂起',
			theme: 'weak',
		},
		Moa: {
			text: 'Moa审批中',
			theme: 'primary',
		},
	};
	const onlineStateMap = {
		0: {
			text: '未上线',
			theme: 'danger',
		},
		1: {
			text: '正常使用',
			theme: 'success',
		},
	};
	const envEffectMap = {
		tencent: {
			text: '自研',
			desc: '只覆盖自研账号',
		},
		public: {
			text: '外部',
			desc: '包含外部客户、自研账号，也可以支持护航巡检',
		},
		private: {
			text: '内部',
			desc: '只覆盖内部护航巡检',
		},
	};
	const columns: any = [
		{
			key: 'StrategyId',
			header: '策略ID',
			width: 70,
		},
		{
			key: 'GroupId',
			header: '类别',
			render(item) {
				return groupMap[item.GroupId];
			},
			width: 70,
		},
		{
			key: 'Product',
			header: '云产品',
			render(item): any {
				return <div>
					{
						productList.find((el) => {
							if (el.value?.toLowerCase() === item.Product?.toLowerCase()) {
								return el;
							}
						})?.text
					}
				</div>;
			},
			width: 160,
		},
		{
			key: 'Name',
			header: '策略名称',
			width: 230,
			render(item): any {
				return <div>
					{
						item.Name
					}
				</div>;
			},
		},
		// {
		// 	key: "CustomThreshold",
		// 	header: "是否自定义阈值",
		// 	render(item) {
		// 		return item.CustomThreshold ? '是' : '否';
		// 	}
		// },
		{
			key: 'DemandState',
			header: '接入状态',
			render(item) {
				return <Text theme={stateMap[item.DemandState]?.theme}>
					{
						stateMap[item.DemandState]?.text
					}
				</Text>;
			},
		},
		{
			key: 'Env',
			header: '国内环境',
			render(item) {
				return item.Env ? <Bubble content={envEffectMap[item.Env]?.desc}>
					<Text>
						{
							envEffectMap[item.Env]?.text
						}
					</Text>
				</Bubble> : <Text>
					{
						'-'
					}
				</Text>;;
			},
			width: 90,
		},
		{
			key: 'Online',
			header: '国内云巡检',
			render(item) {
				return item.Online !== -1 ? <Text theme={onlineStateMap[item.Online]?.theme}>
					{
						onlineStateMap[item.Online]?.text
					}
				</Text> : <Text>
					{
						'-'
					}
				</Text>;
			},
			width: 120,
		},
		{
			key: 'EnvAboard',
			header: '国际环境',
			render(item) {
				return item.EnvAboard ? <Bubble content={envEffectMap[item.EnvAboard]?.desc}>
					<Text>
						{
							envEffectMap[item.EnvAboard]?.text
						}
					</Text>
				</Bubble> : <Text>
					{
						'-'
					}
				</Text>;
			},
			width: 90,
		},
		{
			key: 'OnlineAboard',
			header: '国际云巡检',
			render(item) {
				return item.OnlineAboard !== -1 ? <Text theme={onlineStateMap[item.OnlineAboard]?.theme}>
					{
						onlineStateMap[item.OnlineAboard]?.text
					}
				</Text> : <Text>
					{
						'-'
					}
				</Text>;
			},
			width: 120,
		},
		{
			key: 'Guard',
			header: '国内云护航',
			render(item) {
				return item.Guard !== -1 ? <Text theme={onlineStateMap[item.Guard]?.theme}>
					{
						onlineStateMap[item.Guard]?.text
					}
				</Text> : <Text>
					{
						'-'
					}
				</Text>;
			},
			width: 120,
		},
		{
			key: 'GuardAboard',
			header: '国际云护航',
			render(item) {
				return item.GuardAboard !== -1 ? <Text theme={onlineStateMap[item.GuardAboard]?.theme}>
					{
						onlineStateMap[item.GuardAboard]?.text
					}
				</Text> : <Text>
					{
						'-'
					}
				</Text>;
			},
			width: 120,
		},
		{
			key: 'Expert',
			header: '策略接口人',
			render(item) {
				return item.Expert || '-';
			},
		},
		{
			key: 'Creator',
			header: '创建人',
			render(item) {
				return item.Creator || '-';
			},
		},
		{
			key: 'CreateTime',
			header: '创建时间',
			width: 140,
			render(item) {
				return item.CreateTime || '-';
			},
		},
		{
			key: 'UpdateTime',
			header: '最近更新时间',
			width: 140,
			render(item) {
				return item.UpdateTime || '-';
			},
		},
		{
			key: 'Tapd',
			header: '关联TAPD',
			render(item) {
				return !item.Tapd ? '-' : <a href={item.Tapd} target={'_blank'}>关联TAPD</a>;
			},
		},
		// {
		// 	key: "DemandPriority",
		// 	header: "优先级",
		// 	render(item) {
		//     return item.DemandPriority === 1 ? <Tag theme={'error'}>高</Tag> : item.DemandPriority === 2 ? <Tag theme={'warning'}>中</Tag> : <Tag theme={'success'}>低</Tag>;
		// 	}
		// },
		{
			key: 'instan',
			header: '操作',
			fixed: 'right',
			render(item) {
				return <div style={
					{
						display: 'flex',
						flexWrap: 'wrap',
					}
				}>
					{
						item.SupportOperation?.map((el, i) => <span key={i} style={
							{
								display: 'flex',
								alignItems: 'center',
							}
						}>
							{
								el === 'Describe' && <Button
									onClick={
										() => {
											history.push(`/advisor/strategies-manage/operation/see/other/${item.StrategyId}`);
										}
									}
									type={'link'}>详情</Button>
							}
							{
								el === 'Edit' && isManager && <Button
									onClick={
										() => {
											// eslint-disable-next-line no-nested-ternary
											const type = item.SupportOperation.includes('SubmitReview') ? 'submitReview' : item.SupportOperation.includes('Approve') ? 'approve' : item.SupportOperation.includes('Submit') ? 'submit' : item.DemandState === 'Applied' ? 'applied' : 'other';
											history.push(`/advisor/strategies-manage/operation/update/${type}/${item.StrategyId}`);
										}
									}
									type={'link'}>编辑</Button>
							}
							{/* { */}
							{/* 	el === 'SubmitReview' && ( */}
							{/* 		item['SubmitReviewLoading'] */}
							{/* 	? */}
							{/* 			<Icon type="loading" /> */}
							{/* 			: */}
							{/* 		<Button */}
							{/* 			onClick={ */}
							{/* 				()=>{ */}
							{/* 					item.Operation = 'SubmitReview'; */}
							{/* 					handleProcessStrategy(item); */}
							{/* 				} */}
							{/* 			} */}
							{/* 			type={'link'}>提交审核</Button> */}
							{/* 	) */}
							{/* } */}
							{/* { */}
							{/* 	el === 'Submit' && ( */}
							{/* 		item['SubmitLoading'] */}
							{/* 			? */}
							{/* 			<Icon type="loading" /> */}
							{/* 			: */}
							{/* 			<Button */}
							{/* 				onClick={ */}
							{/* 					()=>{ */}
							{/* 						item.Operation = 'Submit'; */}
							{/* 						handleProcessStrategy(item); */}
							{/* 					} */}
							{/* 				} */}
							{/* 				type={'link'}>提交</Button> */}
							{/* 	) */}
							{/* } */}
							{/* { */}
							{/* 	el === 'Approve' && ( */}
							{/* 		item['ApproveLoading'] */}
							{/* 			? */}
							{/* 			<Icon type="loading" /> */}
							{/* 			: */}
							{/* 			<Button */}
							{/* 				onClick={ */}
							{/* 					()=>{ */}
							{/* 						item.Operation = 'Approve'; */}
							{/* 						handleProcessStrategy(item); */}
							{/* 					} */}
							{/* 				} */}
							{/* 				type={'link'}>通过</Button> */}
							{/* 	) */}
							{/* } */}
							{
								el === 'Reject' && <Button
									onClick={
										() => {
											setSubmitInfo({
												StrategyId: item.StrategyId,
												Operation: 'Reject',
												visible: true,
											});
										}
									}
									type={'link'}>拒绝</Button>
							}
							{
								el === 'Hold' && <Button
									onClick={
										() => {
											setSubmitInfo({
												StrategyId: item.StrategyId,
												Operation: 'Hold',
												visible: true,
											});
										}
									}
									type={'link'}>挂起</Button>
							}
						</span>)
					}
					{
						item.DemandState === 'Applied' && <a href={`/advisor/strategies-manage/operational-detail/${item.StrategyId}`} target={'_blank'} style={
							{
								marginRight: '8px',
							}
						}>
							运营数据
						</a>
					}
					{
						<Button
							onClick={
								() => {
									history.push(`/advisor/strategies-manage/log/${item.StrategyId}`);
								}
							}
							type={'link'}>变更日志</Button>
					}
				</div>;
			},
			width: 160,
		},
	];
	const getAllProduct = async () => {
		try {
			const res = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742,
			});
			const list = [];
			// eslint-disable-next-line no-restricted-syntax
			for (const key in res.ProductDict) {
				list.push({
					text: res.ProductDict[key],
					value: key,
				});
			}
			setProductList(list);
		} catch (err) {};
	};
	useMemo(() => {
		getAllProduct();
	}, []);

	useEffect(() => {
		getUserRole();
	}, []);

	return (
		<Body>
			<Content>
				<Content.Header title="策略列表"></Content.Header>
				<Content.Body>
					<Card>
						<Card.Body>
							<ComprehensiveSearch
								originFilterData={
									[
										{
											label: '策略ID',
											name: 'StrategyId',
											type: 'input',
											value: '',
										},
										{
											label: '策略名称',
											name: 'Name',
											type: 'input',
											value: '',
										},
										{
											label: '产品',
											name: 'Product',
											type: 'mutipleSelect',
											value: '',
											clearable: true,
											options: productList,
											allOption: {
												value: 'all',
												text: '全部',
											},
										},
										{
											label: '类别',
											name: 'GroupId',
											type: 'select',
											value: '',
											clearable: true,
											options: [
												{
													text: '全部',
													value: '',
												},
												...groupOptions,
											],
										},
										{
											label: '国内云巡检',
											name: 'Online',
											type: 'select',
											value: '',
											clearable: true,
											options: [
												{
													text: '全部',
													value: '',
												},
												{
													text: '正常使用',
													value: 1,
												},
												{
													text: '未上线',
													value: 0,
												},
											],
										},
										{
											label: '国际云巡检',
											name: 'OnlineAboard',
											type: 'select',
											value: '',
											clearable: true,
											options: [
												{
													text: '全部',
													value: '',
												},
												{
													text: '正常使用',
													value: 1,
												},
												{
													text: '未上线',
													value: 0,
												},
											],
										},
										{
											label: '国内云护航',
											name: 'Guard',
											type: 'select',
											value: '',
											clearable: true,
											options: [
												{
													text: '全部',
													value: '',
												},
												{
													text: '正常使用',
													value: 1,
												},
												{
													text: '未上线',
													value: 0,
												},
											],
										},
										{
											label: '国际云护航',
											name: 'GuardAboard',
											type: 'select',
											value: '',
											clearable: true,
											options: [
												{
													text: '全部',
													value: '',
												},
												{
													text: '正常使用',
													value: 1,
												},
												{
													text: '未上线',
													value: 0,
												},
											],
										},
										{
											label: '接入状态',
											name: 'DemandState',
											type: 'select',
											value: '',
											clearable: true,
											options: [
												{
													text: '全部',
													value: '',
												},
												{
													text: '新建',
													value: 'Init',
												},
												{
													text: '审核中',
													value: 'InReview',
												},
												{
													text: '处理中',
													value: 'InProcess',
												},
												{
													text: '已接入',
													value: 'Applied',
												},
												{
													text: '已拒绝',
													value: 'Rejected',
												},
												{
													text: '已挂起',
													value: 'OnHold',
												},
												{
													text: ' Moa审批中',
													value: 'Moa',
												},
											],
										},
										{
											label: '创建人',
											name: 'Creator',
											type: 'input',
											value: '',
										},
										{
											label: '创建时间',
											name: 'Time',
											type: 'mutipleTime',
											value: '',
											showTime: true,
										},
									] as any
								}
								onReset={() => {
									setFilters([]);
									setPageIndex(1);
								}}
								onSearch={(filters) => {
									setFilters(filters);
                  setPageIndex(1);
								}}
								suffix={
									<>
										{ isManager && <Button
											style={
												{
													marginLeft: '10px',
												}
											}
											onClick={
												() => {
													history.push('/advisor/strategies-manage/operation/create/other/0');
												}
											}
										>
                      新增策略
										</Button> }
										<Button
											style={{ marginLeft: '10px' }}
											onClick={() => {
												window.open(`${STRATEGIC_OPERATIONS_REPORT_URL}`);
											}}
										>
                      查看运营报表
										</Button>
										<Button
											style={{ marginLeft: '10px' }}
											onClick={() => {
												window.open(`${NEEDS_TO_BE_EVALUATED_URL}`);
											}}
										>
                      查看待评估需求
										</Button>
									</>
								}
							/>
						</Card.Body>
					</Card>
					<Card>
						<Card.Body>
							<Table
								className={'strategies-list-tab'}
								records={records}
								rowDisabled={record => record.status === 'stopped'}
								rowClassName={record => record.status}
								columns={columns}
								topTip={
									(pageLoading || records?.length === 0)
									&&									<StatusTip status={pageLoading ? 'loading' : 'empty'} />
								}
								addons={
									[
										pageable({
											recordCount: totalCount,
											pageIndex,
											pageSize,
											pageSizeOptions: [10, 20, 30, 40],
											onPagingChange: ({ pageIndex, pageSize }) => {
												setPageSize(pageSize);
												setPageIndex(pageIndex);
											},
										}),
										scrollable({
											minWidth: 2280,
										}),
									]
								}
							/>
						</Card.Body>
					</Card>
				</Content.Body>
				<Modal visible={submitInfo.visible} caption="原因" onClose={
					() => {
						setSubmitInfo({
							visible: false,
						});
					}
				}>
					<form onSubmit={handleSubmit}>
						<Modal.Body>
							<Form>
								<Form.Item
									required={true}
									label={'原因'}
									status={getStatus(reasonField.meta, validating)}
									message={(getStatus(reasonField.meta, validating) === 'error' && reasonField.meta.error)}
								>
									<Input {...reasonField.input} autoComplete="off" placeholder={'请填写原因'} style={{ width: '350px' }}/>
								</Form.Item>
							</Form>
						</Modal.Body>
						<Modal.Footer>
							<Button htmlType={'submit'} type="primary" loading={saveLoading}>
								确定
							</Button>
							<Button type="weak" onClick={
								(e) => {
									setSubmitInfo({
										visible: false,
									});
									e.stopPropagation();
									e.preventDefault();
								}
							}>
								取消
							</Button>
						</Modal.Footer>
					</form>
				</Modal>
			</Content>
		</Body>
	);
}
