export function convertPNGtoJPG(pngFile: any, quality: number) {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx: any = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      canvas.toBlob((blob: any) => {
        const jpgFile = new File([blob], pngFile.name.replace('.png', '.jpg'), { type: 'image/jpeg' });
        resolve(jpgFile);
      }, 'image/jpeg', quality);
    };

    img.onerror = (error) => {
      reject(error);
    };

    img.src = URL.createObjectURL(pngFile);
  });
}

export function compressImageResolution(file: any, width: number, height: number) {
  return new Promise((resolve, reject) => {
    const img = new Image();
    const canvas = document.createElement('canvas');
    const ctx: any = canvas.getContext('2d');

    img.onload = () => {
      canvas.width = width;
      canvas.height = height;
      ctx.drawImage(img, 0, 0, width, height);
      canvas.toBlob((blob: any) => {
        const compressedFile = new File([blob], file.name, { type: 'image/jpeg' });
        resolve(compressedFile);
      }, 'image/jpeg');
    };

    img.onerror = (error) => {
      reject(error);
    };

    img.src = URL.createObjectURL(file);
  });
}

export function getImageResolution(file: any) {
  return new Promise((resolve, reject) => {
    const img = new Image();

    img.onload = () => {
      const resolution = {
        width: img.width,
        height: img.height,
      };
      resolve(resolution);
    };

    img.onerror = (error) => {
      reject(error);
    };

    img.src = URL.createObjectURL(file);
  });
}