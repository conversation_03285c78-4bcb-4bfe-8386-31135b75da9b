import React from 'react';
import {
	Modal,
} from '@tea/component';
import simpleMarkdownToHTML from "@src/utils/simpleMarkdownToHTML";
import './index.less';


interface IPreviewModalProps {
	visible: boolean;
	formInfo: any;
	onClose: Function;
}

export default function PreviewModal(props: IPreviewModalProps): React.ReactElement {
	const { visible, formInfo, onClose } = props;
	const conditionList = [];
	if (formInfo !== null && typeof formInfo === 'object' && Object.prototype.toString.call(formInfo) === '[object Object]') {
		for (const key in formInfo) {
			if (key.indexOf('-') !== -1) {
				const arr = key?.split('-');
				if (conditionList[arr[1]] === undefined) {
					conditionList[arr[1]] = {};
				}
				if (arr[0] === 'Level' || arr[0] === 'Desc' || arr[0] === 'DescEn') {
					conditionList[arr[1]][arr[0]] = formInfo[key];
				}
			}
		}
	}
	return (
		<Modal
			className={'strategies-preview-modal-wrap'}
			visible={visible}
			onClose={
				() => {
					onClose?.();
				}
			}
			caption={'预览'}
		>
			<div className="preview-con-wrap">
				<div className={'preview-language-name'}>中文：</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略名称：</div>
					<div className="preview-label-desc">{ formInfo?.Name || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略名称缩写：</div>
					<div className="preview-label-desc">{ formInfo?.ShortName || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略描述：</div>
					<div className="preview-label-desc">{ formInfo?.Desc || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略短描述：</div>
					<div className="preview-label-desc">{ formInfo?.ShortDesc || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">优化建议：</div>
					<div className="preview-label-desc" dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(formInfo?.Repair || '-') }}></div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">风险数量提示：</div>
					<div className="preview-label-desc">{ formInfo?.Notice || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">忽略提示：</div>
					<div className="preview-label-desc">{ formInfo?.Ignore || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">告警条件描述：</div>
					<div className="preview-label-desc">
						{
							conditionList?.filter(item => item.Desc !== undefined)?.length > 0
								?              conditionList?.filter(item => item.Desc !== undefined)?.map(item => <div className="risk-item-wrap">
									<div className={`risk-icon ${item.Level === 3 ? 'risk-icon-high' : item.Level === 2 ? 'risk-icon-mid' : ''}`}></div>
									<div className="risk-text">{item.Desc}</div>
								</div>)
								:                '-'
						}
					</div>
				</div>
				<div className={'preview-language-name preview-language-name-en'}>英文：</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略名称：</div>
					<div className="preview-label-desc">{ formInfo?.NameEn || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略名称缩写：</div>
					<div className="preview-label-desc">{ formInfo?.ShortNameEn || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略描述：</div>
					<div className="preview-label-desc">{ formInfo?.DescEn || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">策略短描述：</div>
					<div className="preview-label-desc">{ formInfo?.ShortDescEn || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">优化建议：</div>
					<div className="preview-label-desc" dangerouslySetInnerHTML={{ __html: simpleMarkdownToHTML(formInfo?.RepairEn || '-') }}></div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">风险数量提示：</div>
					<div className="preview-label-desc">{ formInfo?.NoticeEn || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">忽略提示：</div>
					<div className="preview-label-desc">{ formInfo?.IgnoreEn || '-' }</div>
				</div>
				<div className="preview-label-wrap">
					<div className="preview-label-t">告警条件描述：</div>
					<div className="preview-label-desc">
						{
							conditionList?.filter(item => item.DescEn !== undefined)?.length > 0
								?              conditionList?.filter(item => item.DescEn !== undefined)?.map(item => <div className="risk-item-wrap">
									<div className={`risk-icon ${item.Level === 3 ? 'risk-icon-high' : item.Level === 2 ? 'risk-icon-mid' : ''}`}></div>
									<div className="risk-text">{item.DescEn}</div>
								</div>)
								:                '-'
						}
					</div>
				</div>
			</div>
		</Modal>
	);
}
