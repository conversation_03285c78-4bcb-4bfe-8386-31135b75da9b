import React, { useState, useEffect, useRef } from 'react';
import { IDomEditor, IEditorConfig, IToolbarConfig, Boot, createEditor, createToolbar } from '@wangeditor/editor';
import PubSub from 'pubsub-js';
import { MediaTypeEnum } from '@src/routes/advisor/pages/strategiesNew/constants';
import FileChooseModal from './components/file-choose-modal';
import AddImageButton from './components/add-image-button';
import '@wangeditor/editor/dist/css/style.css';

const menu1Conf = {
	key: 'AddImageButton', // 定义 menu key ：要保证唯一、不重复（重要）
	factory() {
		return new AddImageButton({
			onClick: () => {
				PubSub.publish('AddImageButton');
			},
		});
	},
};

Boot.registerMenu(menu1Conf);

const domainImageMap = {
  'isa-test.woa.com': 'https://isa-scan-cos-1257943044.cos-internal.ap-chengdu.tencentcos.cn',
  'isa-pre.woa.com': 'https://isaonline-1251316161.cos.ap-guangzhou.myqcloud.com',
  'isa.woa.com': 'https://isaonline-1251316161.cos.ap-guangzhou.myqcloud.com'
};

interface IMyEditorProps {
	disabled?: boolean;
	value: string;
	onChange: (value: string) => void;
	onFocus?: () => void;
  onBlur?: () => void;
}

export default function MyEditor(props: IMyEditorProps): React.ReactElement {
	const {
		value,
		disabled,
		onChange,
		onFocus,
    onBlur,
	} = props;

	// editor 实例
	const [editor, setEditor] = useState<IDomEditor | null>(null);
	const [visible, setVisible] = useState(false);
	// eslint-disable-next-line @typescript-eslint/no-unused-vars
	const [fileType, setFileType] = useState(MediaTypeEnum.image);
  const valRef = useRef('');

	// 工具栏配置
	const toolbarConfig: Partial<IToolbarConfig> = {
		excludeKeys: ['todo', 'emotion', 'insertLink', 'group-image', 'group-video'],
		insertKeys: {
			index: 27, // 插入的位置，基于当前的 toolbarKeys
			keys: ['AddImageButton'],
		},
	};

	// 编辑器配置
	const editorConfig: Partial<IEditorConfig> = {
		placeholder: '请输入内容...',
		autoFocus: false,
		readOnly: disabled,
		onFocus: () => {
			onFocus?.();
		},
    onBlur: () => {
      onBlur?.();
    },
	};

	const onSuccessHandle = (mediaPath: string, mediaType: MediaTypeEnum) => {
		const origin = domainImageMap[location.hostname];
		editor?.focus();
		if (mediaType === MediaTypeEnum.image) {
			setTimeout(() => {
				// 区分插入的是普通图片alt="lone-text-image"
				editor?.dangerouslyInsertHtml(`<p><img src="${origin}/${mediaPath}" style="" data-href="lone-text-image" alt="${mediaPath}" /></p>`, true);
			}, 500);
		} else if (mediaType === MediaTypeEnum.arch) {
			setTimeout(() => {
				// 插入的是架构图alt="lone-text-arch-${mediaId}"
				editor?.dangerouslyInsertHtml(`<p><img src="${origin}/${mediaPath}" style="" data-href="lone-text-arch" alt="${mediaPath}"/></p>`, true);
			}, 500);
		} else {
			setTimeout(() => {
				// 插入的是视频alt="lone-text-video-${mediaId}"
				editor?.dangerouslyInsertHtml(`<p><img src="${origin}/${mediaPath}" style="" data-href="lone-text-video" alt="${mediaPath}"/></p>`, true);
			}, 500);
		}
		setVisible(false);
	};

	const onAddImageButtonHandle = () => {
		setVisible(true);
		setFileType(MediaTypeEnum.image);
	};

	useEffect(() => {
		if (editor) {
			if (disabled) {
				editor.disable();
			} else {
				editor.enable();
			}
		}
	}, [disabled, editor]);

	// 及时销毁 editor ，重要！
	useEffect(() => () => {
		if (editor === null) return;
		editor.destroy();
		setEditor(null);
	}, [editor]);

	useEffect(() => {
		PubSub.subscribe('AddImageButton', onAddImageButtonHandle);
		return () => {
			PubSub.unsubscribe(onAddImageButtonHandle);
		};
	}, []);

	useEffect(() => {
		const editor: any = createEditor({
			selector: '#wang-editor-container',
			html: '',
			config: editorConfig,
			mode: 'default',
		});
		editor.on('change', () => {
      if (valRef.current !== editor.getHtml()) {
        onChange?.(editor.getHtml());
      }
		});
		setEditor(editor);
	}, []);
	useEffect(() => {
		if (editor !== null) {
			createToolbar({
				editor,
				selector: '#wang-toolbar-container',
				config: toolbarConfig,
				mode: 'default', // or 'simple'
			});
		}
	}, [editor]);
  useEffect(()=>{
    if (editor !== null) {
      valRef.current = value;
      editor.setHtml(value);
    }
  }, [editor, value]);

	return (
		<div>
			<div style={{ border: '1px solid #ccc' }}>
				<div id={'wang-toolbar-container'} style={{ width: '820px', borderBottom: '1px solid #ccc' }}/>
				<div id={'wang-editor-container'} style={{ height: '500px', overflowY: 'hidden', fontSize: '12px' }}/>
			</div>
			<FileChooseModal
				visible={visible}
				onlyMediaType={fileType}
				success={(mediaId, mediaType) => {
					onSuccessHandle(mediaId, mediaType as any);
				}}
				close={() => {
					setVisible(false);
				}}
			/>
		</div>
	);
}
