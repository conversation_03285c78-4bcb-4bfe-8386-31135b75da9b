/* eslint-disable class-methods-use-this */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { IButtonMenu, IDomEditor } from '@wangeditor/editor';

export default class AddImageButton implements IButtonMenu {
  title: string;

  tag: string;

  onClick: () => void;

  constructor(props: any) {
    this.title = '插入图片'; // 自定义菜单标题
    this.tag = 'button';
    this.onClick = props.onClick;
  }

  // 获取菜单执行时的 value ，用不到则返回空 字符串或 false
  getValue(editor: IDomEditor): string | boolean {
    return false;
  }

  // 菜单是否需要激活（如选中加粗文本，“加粗”菜单会激活），用不到则返回 false
  isActive(editor: IDomEditor): boolean {
    return false;
  }

  // 菜单是否需要禁用（如选中 H1 ，“引用”菜单被禁用），用不到则返回 false
  isDisabled(editor: IDomEditor): boolean {
    return false;
  }

  // 点击菜单时触发的函数
  exec(editor: IDomEditor) {
    if (this.isDisabled(editor)) return;
    this.onClick();
  }
}
