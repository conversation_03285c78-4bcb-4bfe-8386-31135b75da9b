/* eslint-disable complexity */
import React, { useEffect, useState, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { Modal, Button, Form, message } from '@tencent/tea-component';
import COS from 'cos-js-sdk-v5';
import { MediaTypeEnum } from '@src/routes/advisor/pages/strategiesNew/constants';
import { convertPNGtoJPG, compressImageResolution, getImageResolution } from '@src/routes/advisor/pages/strategiesNew/utils/methods';
import { DescribeCosTmpCredential, CreateStrategyMedia } from '@src/api/advisor/strategiesNew';
import ImageUploadFormItem from '../image-upload-form-item';

interface IFileChooseModalProps {
	visible: boolean;
	onlyMediaType?: MediaTypeEnum | undefined;
	close: () => void;
	success: (mediaId: string, type?: MediaTypeEnum) => void;
}

export default function FileChooseModal(props: IFileChooseModalProps): React.ReactElement {
	const {
		visible,
		onlyMediaType = undefined,
		close,
		success,
	} = props;
	const [submitLoading, setSubmitLoading] = useState(false);

	const nowArchInfo = useRef({
		svg: '',
		detail: '',
		archName: '',
	});

	const {
		control,
		watch,
		handleSubmit,
		reset,
		setValue,
		formState: { errors },
	} = useForm({ mode: 'all' });
	const nowMediaType = watch('MediaType') || MediaTypeEnum.arch;
	const getStatus = (fieldState: any) => {
		if (fieldState?.error?.message) {
			return 'error';
		}
		if (!fieldState.isDirty) {
			return undefined;
		}
		return fieldState.invalid ? 'error' : 'success';
	};

	const createStrategyMedia = async (params: any) => {
    try {
      CreateStrategyMedia(params).then(() => {
        setSubmitLoading(false);
        success(params.MediaPath, nowMediaType);
        reset();
        setSubmitLoading(false);
      });
    } catch (err) {
      setSubmitLoading(false);
    }
	};

	const onSubmitHandle = async (values: any) => {
		try {
			setSubmitLoading(true);
			const {
				MediaType,
				Image,
				Video,
			} = values;
			let mediaName = '';
			if (MediaType === MediaTypeEnum.arch) {
				mediaName = `${nowArchInfo.current.archName}.arch`;
			} else if (MediaType === MediaTypeEnum.image) {
				mediaName = Image?.name;
			} else if (MediaType === MediaTypeEnum.video) {
				mediaName = Video?.name;
			}
			const rs = await DescribeCosTmpCredential({
				MediaName: mediaName,
				MediaType,
        ShowError: true,
        OnlyData: true
			});
			const {
				CosAddress,
				MediaPath,
				MediaThumbnailPath,
				MediaContentType,
				MediaId,
			} = rs || {};
			const {
				Bucket,
				CosId,
				CosKey,
				CosToken,
				ExpiredTime,
				Region,
				StartTime,
			} = CosAddress;
			const cos = new COS({
				SecretId: CosId, // sts服务下发的临时 secretId
				SecretKey: CosKey, // sts服务下发的临时 secretKey
				SecurityToken: CosToken, // sts服务下发的临时 SessionToken
				// @ts-ignore
				StartTime, // 建议传入服务端时间，可避免客户端时间不准导致的签名错误
				ExpiredTime, // 临时密钥过期时间
        Domain: `${Bucket}.cos-internal.${Region}.tencentcos.cn`
			});
			cos.uploadFile({
				Bucket, // 填入您自己的存储桶，必须字段
				Region, // 存储桶所在地域，例如ap-beijing，必须字段
				Key: MediaPath, // 存储在桶里的对象键（例如1.jpg，a/b/test.txt），必须字段
				Body: Image, // 必须，上传文件对象，可以是input[type="file"]标签选择本地文件后得到的file对象
				SliceSize: 1024 * 1024 * 5, // 触发分块上传的阈值，超过5MB使用分块上传，非必须
				ContentType: MediaContentType
			}, (err) => {
        console.log(err);
        if (err) {
          message.error({ content: '上传图片失败' });
          setSubmitLoading(false);
        } else {
          createStrategyMedia({
            MediaId,
            MediaPath,
            MediaType,
            ThumbnailImagePath: MediaThumbnailPath,
          });
        }
			});
		} catch (error) {
			console.error(error);
			setSubmitLoading(false);
		}
	};

	useEffect(() => {
		if (onlyMediaType && visible) {
			setValue('MediaType', onlyMediaType);
		}
	}, [onlyMediaType, visible]);

	return (
		<Modal
			disableEscape
			visible={visible}
			caption="添加附件"
			onClose={close}
		>
			<Modal.Body>
				<Form
					layout="fixed"
					fixedLabelWidth={100}
				>
					{
						nowMediaType === MediaTypeEnum.image && (
							<Controller
								name="Image"
								control={control}
								defaultValue={null}
								rules={{
									validate: value => (!value ? '请上传图片(PNG/JPG/JPEG)，5M以内' : undefined),
								}}
								render={({ field, fieldState }) => (
									<Form.Item
										required
										label="上传图片"
										status={fieldState.isValidating ? 'validating' : getStatus(fieldState)}
										message={errors.Image?.message as any}
									>
										<ImageUploadFormItem
											{...field}
										/>
									</Form.Item>
								)}
							/>
						)
					}
				</Form>
			</Modal.Body>
			<Modal.Footer>
				<Button
					loading={submitLoading}
					type="primary"
					onClick={handleSubmit(onSubmitHandle) as any}
				>
          确定
				</Button>
				<Button type="weak" onClick={close}>
          取消
				</Button>
			</Modal.Footer>
		</Modal>
	);
}
