/* eslint-disable jsx-a11y/anchor-is-valid */
/* eslint-disable react/jsx-no-bind */
import React from 'react';
import { Upload, Text, Button } from '@tencent/tea-component';

interface IImageUploadFormItemProps {
  value: any;
  onChange: (value: any) => void;
}

export default function ImageUploadFormItem(props: IImageUploadFormItemProps): React.ReactElement {
  const {
    value,
    onChange,
  } = props;

  function beforeUpload(file: any, fileList: any, isAccepted: any) {
    if (!isAccepted) {
      onChange(null);
      return false;
    }
    onChange(file);
    return false;
  }
  return (
    <Upload
      accept="image/png,image/jpg,image/jpeg"
      maxSize={5 * 1024 * 1024}
      beforeUpload={beforeUpload}
    >
      {({ open, isDragging }) => (
        <Upload.Dragger
          filename={value?.name}
          description={
            value && (
              <p>
                文件大小：
                {Math.floor(value.size / 1024)}
                K
              </p>
            )
          }
          button={(
            <>
              <Button type="link" onClick={open}>
                重新上传
              </Button>
              <Button
                type="link"
                style={{ marginLeft: 8 }}
                onClick={() => onChange(null)}
              >
                删除
              </Button>
            </>
          )}
        >
          {isDragging ? (
            '释放鼠标'
          ) : (
            <>
              <a onClick={open}>点击上传</a>
              <Text theme="weak">/拖拽到此区域</Text>
            </>
          )}
        </Upload.Dragger>
      )}
    </Upload>
  );
}
