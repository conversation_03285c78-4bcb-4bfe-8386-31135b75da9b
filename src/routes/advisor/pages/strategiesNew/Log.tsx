import React, { useMemo, useState } from "react";
import {
  Card,
  Layout,
  Button,
  StatusTip,
  Table,
  Text,
  Modal,
  Form,
  Input,
  Icon,
  Tag,
  message,
  H4, Row, Col,
} from "@tea/component";
import { useHistory } from "@tea/app";
import { DescribeStrategy, DescribeStrategyOperateLog } from "@src/api/advisor/strategiesNew";
import { iconSvg } from "@src/configs/iconSvg";
const { Body, Content } = Layout;
export function Log({ match }) {
  const history = useHistory();
  const id = parseInt(match.params.id);
  const [strategyInfo, setStrategyInfo] = useState<any>({});
  const [logList, setLogList] = useState([]);
  const [loading, setLoading] = useState(false);
  const riskLevelMap = {
    2: '中风险',
    3: '高风险'
  };
  const getDescribeStrategy = async () => {
    try {
      const res = await DescribeStrategy({
        OnlyData: true,
        ShowError: true,
        StrategyId: id
      });
      setStrategyInfo(res.StrategyDetail);
    } catch (err) {};
  };
  const getDescribeStrategyOperateLog = async () => {
    setLoading(true);
    try {
      const res = await DescribeStrategyOperateLog({
        OnlyData: true,
        ShowError: true,
        StrategyId: id
      });
      setLogList(res.OperateLogs);
      setLoading(false);
    } catch (err) {
      setLoading(false);
    };
  };
  useMemo(()=>{
    getDescribeStrategy();
    getDescribeStrategyOperateLog();
  }, []);
  return (
    <Body className={'strategies-log-wrap'}>
      <Content>
        <Content.Header
          showBackButton
          onBackButtonClick={() => {
            history.push(`/advisor/strategies-manage`);
          }}
          title="变更日志"
        />
        <Content.Body>
          <Card>
            <Card.Body>
              <H4>策略详情</H4>
              <div className={'desc-wrap'}>
                <Row>
                  <Col span={4}>
                    <Text theme="label">产品名称：</Text>
                  </Col>
                  <Col>
                    <Text>{strategyInfo.Product}</Text>
                  </Col>
                </Row>
                <Row verticalAlign={'middle'}>
                  <Col span={4}>
                    <Text theme="label">策略名称：</Text>
                  </Col>
                  <Col>
                    <Text>{strategyInfo.Name}</Text>
                  </Col>
                </Row>
                <Row verticalAlign={'middle'}>
                  <Col span={4}>
                    <Text theme="label">策略名称缩写：</Text>
                  </Col>
                  <Col>
                    <Text>{strategyInfo.ShortName}</Text>
                  </Col>
                </Row>
                <Row verticalAlign={'middle'}>
                  <Col span={4}>
                    <Text theme="label">策略描述：</Text>
                  </Col>
                  <Col>
                    <Text>{strategyInfo.Desc}</Text>
                  </Col>
                </Row>
                <Row verticalAlign={'middle'}>
                  <Col span={4}>
                    <Text theme="label">策略短描述：</Text>
                  </Col>
                  <Col>
                    <Text>{strategyInfo.ShortDesc}</Text>
                  </Col>
                </Row>
                <Row verticalAlign={'middle'}>
                  <Col span={4}>
                    <Text theme="label">专项接口人：</Text>
                  </Col>
                  <Col>
                    <Text>{strategyInfo.Expert}</Text>
                  </Col>
                </Row>
                <Row verticalAlign={'top'}>
                  <Col span={4}>
                    <Text theme="label">告警条件描述：</Text>
                  </Col>
                  <Col>
                    {
                      strategyInfo?.StrategyConditions?.map((item)=>{
                        return <div>
                          <Text>{item.Desc}</Text>（<Text>{riskLevelMap[item.Level]}</Text>，专家评分：<Text>{item.Score}</Text>分）
                        </div>;
                      })
                    }
                  </Col>
                </Row>
                <Row verticalAlign={'middle'}>
                  <Col span={4}>
                    <Text theme="label">优化建议：</Text>
                  </Col>
                  <Col>
                    <Text>{strategyInfo.Repair}</Text>
                  </Col>
                </Row>
              </div>
              <H4 style={{
                paddingTop: '20px',
                paddingBottom: '20px'
              }}>变更历史</H4>
              <Table
                records={logList}
                columns={
                  [
                    {
                      key: "ChangeTime",
                      header: "操作时间",
                      width: 150
                    },
                    {
                      key: "Operator",
                      header: "变更人",
                      width: 150
                    },
                    {
                      key: "OperateReason",
                      header: "原因",
                      width: 150
                    },
                    {
                      key: "OperatorType",
                      header: "操作类型",
                      width: 150
                    },
                    {
                      key: "OperateDetail",
                      header: "云巡检变更操作",
                      render(item) {
                        return <div dangerouslySetInnerHTML={{ __html: item.OperateDetail }}></div>;
                      }
                    },
                  ]
                }
                topTip={
                  (loading || logList?.length == 0) && <StatusTip status={loading ? "loading" : "empty"} />
                }
              />
            </Card.Body>
          </Card>
        </Content.Body>
      </Content>
    </Body>
  );
}
