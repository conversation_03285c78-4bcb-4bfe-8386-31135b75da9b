import React, { useState, useMemo, useEffect } from 'react';
import { Layout, Card, H3, Form, Input, Select, Button, message, Switch, Checkbox, Radio, InputNumber, ExternalLink, Status } from '@tea/component';
import MyEditor from "@src/routes/advisor/pages/strategiesNew/components/my-editor";
import PreviewModal from "@src/routes/advisor/pages/strategiesNew/components/preview-modal";
import { useField, useForm } from "react-final-form-hooks";
import { cloneDeep } from "lodash";
import { useHistory } from '@tea/app';
import { getDescribeProductList } from "@src/api/advisor/inspectionRelease";
import { UpdateStrategy, CreateStrategy, DescribeStrategy, GetUserRole, ProcessStrategy} from "@src/api/advisor/strategiesNew";


const conditionFieldInfo = [
	{
		name: 'Level',
		label: '风险等级',
		placeholder: '请选择风险等级',
		required: true,
		errorMessage: '请选择风险等级',
		type: 'radio-group',
		options: [
			{
				text: '高风险',
				value: 3
			},
			{
				text: '中风险',
				value: 2
			},
		]
	},
	{
		name: 'Desc',
		label: '告警条件描述',
		placeholder: '请填写告警条件描述',
		required: true,
		errorMessage: '请填写告警条件描述',
		type: 'textarea'
	},
	{
		name: 'DescEn',
		label: '告警条件描述-英文',
		placeholder: '请填写告警条件描述',
		required: false,
		errorMessage: '请填写告警条件描述',
		type: 'textarea'
	},
	{
		name: 'ApiDesc',
		label: '接口',
		placeholder: '接入 CAM 的云 API 或者 云监控指标文档链接：\r参数 & 判断条件：',
		required: true,
		errorMessage: '请填写接口',
		type: 'textarea',
		tips: '需要提供接入 CAM 的云 API 或者 云监控指标文档链接'
	},
	{
		name: 'Score',
		label: '专项评分（1-10）',
		placeholder: '请输入专项评分',
		required: true,
		errorMessage: '专项评分不能为0',
		type: 'input-number',
	},
	{
		name: 'Priority',
		label: '优先级',
		placeholder: '请输入优先级',
		required: true,
		errorMessage: '优先级不能为0',
		type: 'input-number',
		tips: '默认高风险填3， 中风险填2'
	},
	{
		name: 'UserPolicySupport',
		label: '是否支持自定阈值',
		placeholder: '',
		required: false,
		errorMessage: '',
		type: 'text',
	},
	{
		type: 'render',
		children: [
			{
				name: 'Uin',
				label: 'UIN',
				placeholder: '请输入UIN',
				required: false,
				errorMessage: '请输入UIN',
				type: 'input',
			},
			{
				name: 'Appid',
				label: 'APPID',
				placeholder: '请输入APPID',
				required: false,
				errorMessage: '请输入APPID',
				type: 'input',
			},
			{
				name: 'Resource',
				label: '资源/实例',
				placeholder: '请输入资源/实例',
				required: false,
				errorMessage: '请输入资源/实例',
				type: 'input',
			},
		]
	},
];

const formInfoList: any = [
	[
		{
			name: 'StrategyUsage',
			label: '选择接入功能',
			placeholder: '请勾选接入功能',
			required: true,
			errorMessage: '请勾选接入功能',
			type: 'checkbox-group',
			options: [
				{
					text: '云巡检',
					value: 1
				},
				{
					text: '云护航',
					value: 2
				},
			]
		},
		{
			name: 'Product',
			label: '产品名称',
			placeholder: '请选择产品',
			required: true,
			errorMessage: '请选择产品',
			type: 'select',
			searchable: true,
			options: [],
			tips: '如无对应产品，联系lanlanzhang'
		},
		{
			name: 'Expert',
			label: '策略接口人',
			placeholder: '请输入策略接口人',
			required: true,
			errorMessage: '请输入策略接口人',
			type: 'input'
		},
		{
			name: 'ProductOwner',
			label: '产品负责人',
			placeholder: '请输入产品负责人',
			required: true,
			errorMessage: '请输入产品负责人',
			type: 'input',
			tips: '用于申请CAM服务角色变更'
		},
		{
			name: 'GroupId',
			label: '类别',
			placeholder: '请选择类别',
			required: true,
			errorMessage: '请选择类别',
			type: 'select',
			options: [
				{
					text: '安全',
					value: 1
				},
				{
					text: '可靠',
					value: 2
				},
				{
					text: '服务限制',
					value: 3
				},
				{
					text: '成本',
					value: 4
				},
				{
					text: '性能',
					value: 5
				}
			]
		},
		{
			name: 'StrategyId',
			label: '巡检ID',
			placeholder: '',
			required: false,
			errorMessage: '请输入巡检ID',
			type: 'input',
			disabled: true
		},
		{
			name: 'Name',
			label: '策略名称',
			placeholder: '请输入策略名称',
			required: true,
			errorMessage: '请输入策略名称',
			type: 'textarea',
		},
		{
			name: 'NameEn',
			label: '策略名称-英文',
			placeholder: '请输入策略名称-英文',
			required: false,
			errorMessage: '请输入策略名称-英文',
			type: 'textarea',
		},
		{
			name: 'ShortName',
			label: '策略名称缩写',
			placeholder: '请输入策略名称缩写',
			required: true,
			errorMessage: '请输入策略名称缩写',
			type: 'textarea',
		},
		{
			name: 'ShortNameEn',
			label: '策略名称缩写-英文',
			placeholder: '请输入策略名称缩写-英文',
			required: false,
			errorMessage: '请输入策略名称缩写-英文',
			type: 'textarea',
		},
		{
			name: 'DemandBackground',
			label: '需求背景',
			placeholder: '请填写需求背景',
			required: true,
			errorMessage: '请填写需求背景',
			type: 'editor'
		},
		{
			name: 'Desc',
			label: '策略描述',
			placeholder: '请填写策略描述',
			required: true,
			errorMessage: '请填写策略描述',
			type: 'textarea'
		},
		{
			name: 'DescEn',
			label: '策略描述-英文',
			placeholder: '请填写策略描述-英文',
			required: false,
			errorMessage: '请填写策略描述-英文',
			type: 'textarea'
		},
		{
			name: 'ShortDesc',
			label: '策略短描述',
			placeholder: '请填写策略短描述',
			required: true,
			errorMessage: '请填写策略短描述',
			type: 'textarea'
		},
		{
			name: 'ShortDescEn',
			label: '策略短描述-英文',
			placeholder: '请填写策略短描述-英文',
			required: false,
			errorMessage: '请填写策略短描述-英文',
			type: 'textarea'
		},
		{
			name: 'Repair',
			label: '优化建议',
			placeholder: '请填写优化建议',
			required: true,
			errorMessage: '请填写优化建议',
			type: 'textarea'
		},
		{
			name: 'RepairEn',
			label: '优化建议-英文',
			placeholder: '请填写优化建议-英文',
			required: false,
			errorMessage: '请填写优化建议-英文',
			type: 'textarea'
		},
		{
			name: 'Notice',
			label: '风险数量提示',
			placeholder: '请填写风险数量提示',
			required: true,
			errorMessage: '请填写风险数量提示',
			type: 'textarea',
      tips: '示例：%d 个实例存在风险。'
		},
		{
			name: 'NoticeEn',
			label: '风险数量提示-英文',
			placeholder: '请填写风险数量提示-英文',
			required: false,
			errorMessage: '请填写风险数量提示-英文',
			type: 'textarea'
		},
		{
			name: 'Ignore',
			label: '忽略提示',
			placeholder: '请填写忽略提示',
			required: true,
			errorMessage: '请填写忽略提示',
			type: 'textarea',
      tips: '示例：%d 个实例被忽略。'
		},
		{
			name: 'IgnoreEn',
			label: '忽略提示-英文',
			placeholder: '请填写忽略提示-英文',
			required: false,
			errorMessage: '请填写忽略提示-英文',
			type: 'textarea'
		},
		{
			name: 'DemandPriority',
			label: '需求优先级',
			placeholder: '请选择需求优先级',
			required: true,
			errorMessage: '请选择需求优先级',
			type: 'radio-group',
			options: [
				{
					text: '高',
					value: 1
				},
				{
					text: '中',
					value: 2
				},
				{
					text: '低',
					value: 3
				},
			]
		},
		{
			name: 'NeedDev',
			label: '是否涉及研发排期',
			placeholder: '请选择是否涉及研发排期',
			required: true,
			errorMessage: '请选择是否涉及研发排期',
			type: 'radio-group',
			options: [
				{
					text: '是',
					value: 1
				},
				{
					text: '否',
					value: 2
				}
			]
		},
	],
	[
		conditionFieldInfo
	],
];
const originFormData = {};
let submitType = '';
let isClickSubmit = false;
function getStatus(meta, validating) {
  if (meta.active && validating) {
    return 'validating';
  }
  if (!meta.touched) {
    return null;
  }
  return meta.error ? 'error' : 'success';
}
export function Operation({match}) {
	const id = Number(match.params.id);
	const handle = match.params.handle;
  const type = match.params.type;
	const isUpdate = (handle === 'update');
	const isSee = (handle === 'see');
	const isCreate = (handle === 'create');
	const path = location.pathname.split('/')[2];
	const history = useHistory();
	const {Body, Content} = Layout;
	const [formRenderList, setFormRenderList] = useState(cloneDeep(formInfoList));
	const [saveLoading, setSaveLoading] = useState(false);
  const [submitReviewLoading, setSubmitReviewLoading] = useState(false);
  const [approveLoading, setApproveLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
	const [role, setRole] = useState('');
	const fieldNotInCreateList = ['DescEn', 'Priority', 'UserPolicySupport', 'NameEn', 'DescEn', 'ShortDescEn', 'RepairEn', 'NoticeEn', 'IgnoreEn', 'ShortNameEn', 'StrategyId'];
  const appliedList = ['NeedDev'];
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const handleProcessStrategy = async (operation, strategyId?: number)=>{
    try {
      const res = await ProcessStrategy({
        StrategyId: strategyId === undefined ? id : strategyId,
        Operation: operation,
        Reason: '',
        ShowError: true,
        OnlyData: true
      });
    } catch (err) {}
  };
  // 提交表单
	const onSubmit = async (value) => {
		const params: any = {
			ShowError: true,
			OnlyData: true,
			StrategyDetail: {
				StrategyConditions: []
			}
		};
		const testAccountsList = ['Appid', 'Resource', 'Uin'];
		for (const key in value) {
			if (key.indexOf('-') !== -1) {
				const arr = key.split('-');
				if (params.StrategyDetail.StrategyConditions[arr[1]] === undefined) {
					if (testAccountsList.includes(arr[0])) {
						params.StrategyDetail.StrategyConditions[arr[1]] = {
							TestAccounts: [
								{
									[arr[0]]: value[key]
								}
							]
						};
					} else {
						params.StrategyDetail.StrategyConditions[arr[1]] = {
							[arr[0]]: ((arr[0] === 'Level' || arr[0] === 'Priority' || arr[0] === 'Score' || arr[0] === 'UserPolicySupport') ? parseInt(value[key]) : value[key]),
							TestAccounts: [
								{

								}
							]
						};
					};
				} else {
					if (testAccountsList.includes(arr[0])) {
						if (params.StrategyDetail.StrategyConditions[arr[1]]['TestAccounts'][0] === undefined) {
							params.StrategyDetail.StrategyConditions[arr[1]]['TestAccounts'][0] = {};
						}
						params.StrategyDetail.StrategyConditions[arr[1]]['TestAccounts'][0][arr[0]] = value[key];
					} else {
						params.StrategyDetail.StrategyConditions[arr[1]][arr[0]] = ((arr[0] === 'Level' || arr[0] === 'Priority' || arr[0] === 'Score' || arr[0] === 'UserPolicySupport') ? parseInt(value[key]) : value[key]);
					};
				}
			} else {
				if (key !== 'StrategyConditions') {
					if (key === 'StrategyUsage') {
						params['StrategyDetail'][key] = value[key]?.map((item)=>{
							return parseInt(item);
						});
					} else if (key === 'DemandPriority' || key === 'GroupId' || key === 'NeedDev' || key === 'GroupId' || key === 'GroupId') {
						params['StrategyDetail'][key] = parseInt(value[key]);
					} else {
						params['StrategyDetail'][key] = value[key];
					}
				}
			}
		}
		try {
      if (submitType === 'submitReview') {
        setSubmitReviewLoading(true);
        let res: any = {};
        if (isCreate) {
          res = await CreateStrategy(params);
        } else {
          await UpdateStrategy(params);
        }
        await handleProcessStrategy('SubmitReview', res.StrategyId);
      } else if (submitType === 'submit') {
        setSubmitLoading(true);
        await UpdateStrategy(params);
        await handleProcessStrategy('Submit');
      } else if (submitType === 'approve') {
        setApproveLoading(true);
        await UpdateStrategy(params);
        await handleProcessStrategy('Approve');
      } else if (isCreate) {
        setSaveLoading(true);
        await CreateStrategy(params);
      } else {
        setSaveLoading(true);
        await UpdateStrategy(params);
      }
			message.success({
				content: '操作成功'
			});
      setSaveLoading(false);
      setSubmitReviewLoading(false);
      setApproveLoading(false);
			history.push(`/advisor/strategies-manage`);
		} catch (err) {
      setSaveLoading(false);
      setSubmitReviewLoading(false);
      setApproveLoading(false);
		};
	};
	const { form, handleSubmit, values, validating } = useForm<any>({
		onSubmit: value => onSubmit(value),
		initialValuesEqual: () => true,
		initialValues: {
			...originFormData,
		},
		validate: (formInfo) => {
			const validateFormInfo = {};
			formRenderList.forEach((el, i) => {
				if (i == 1) {
					el.forEach((val, j)=>{
						val.forEach((obj)=>{
							const name = `${obj.name}-${j}`;
							if ((obj.type != 'switch' && obj.required && obj.name?.indexOf('Priority') === -1 && obj.name?.indexOf('Score') === -1)) {
								validateFormInfo[name] = formInfo[name] !== '' && formInfo[name] !== undefined ? undefined : obj.errorMessage;
							}
							if (isManager && obj.name?.indexOf('Priority') !== -1 && !isCreate) {
								validateFormInfo[name] = (!formInfo[name]) ? obj.errorMessage : undefined;
							}
							if (obj.name?.indexOf('Score') !== -1) {
								validateFormInfo[name] = (!formInfo[name]) ? obj.errorMessage : undefined;
							}
						});
					});
				} else {
					el.forEach((item)=>{
            if (item.type === 'editor' && item.required) {
              validateFormInfo[item.name] = formInfo[item.name] !== '' && formInfo[item.name] !== undefined && formInfo[item.name] !== '<p><br></p>' ? undefined : item.errorMessage;
            } else if ((item.type != 'switch' && item.required)) {
              validateFormInfo[item.name] = formInfo[item.name] !== '' && formInfo[item.name] !== undefined ? undefined : item.errorMessage;
            }
					});
				}
			});
			if (isCreate || values?.NeedDev === undefined) {
				validateFormInfo['NeedDev'] = undefined;
			}
			return validateFormInfo;
		},
	});
	const formFieldInfo: any = {};
	formRenderList.forEach((el, i)=>{
		if (i === 1) {
			for (let j = 0;j < 10; ++j) {
				el[0].forEach((obj)=>{
					if (obj.type === 'render') {
						obj.children.forEach((el)=>{
							const name = `${el.name}-${j}`;
							formFieldInfo[name] = useField(name, form);
						});
					} else {
						const name = `${obj.name}-${j}`;
						formFieldInfo[name] = useField(name, form);
					}
				});
			}
		} else {
			el.forEach((item) => {
				formFieldInfo[item.name] = useField(item.name, form);
			});
		}
	});
	const getAllProduct = async () => {
		try {
			const res = await getDescribeProductList({
				OnlyData: true,
				ShowError: true,
				AppId: 1253985742
			});
			const list = [];
			for (const key in res.ProductDict) {
				list.push({
					text: res.ProductDict[key],
					value: key
				});
			}
			res.list = list;
			formRenderList[0][1]['options'] = list;
			setFormRenderList(cloneDeep(formRenderList));
		} catch (err) {};
	};
	const getDescribeStrategy = async () => {
		try {
			const res = await DescribeStrategy({
				OnlyData: true,
				ShowError: true,
				StrategyId: id
			});
			formRenderList[1] = res?.StrategyDetail?.StrategyConditions?.map(()=>{
				return conditionFieldInfo;
			});
			setFormRenderList(cloneDeep(formRenderList));
			const obj = {};
			const list = res?.StrategyDetail?.StrategyConditions;
			list?.forEach((item, i)=>{
				for (const k in item) {
					if (k === 'TestAccounts') {
						for (const key in item[k][0]) {
							obj[`${key}-${i}`] = item[k][0][key];
						}
					} else {
						if (k === 'UserPolicySupport') {
							obj[`${k}-${i}`] = item[k] === 0 ? '不支持' : item[k] === 1 ? '支持' : '-' ;
						} else {
							obj[`${k}-${i}`] = item[k];
						}
					}
				}
			});
			for (const k in res?.StrategyDetail) {
				obj[k] = res?.StrategyDetail[k];
			}
      form.initialize({
        ...obj
      });
		} catch (err) {};
	};
	const getUserRole = async () => {
		try {
			const res = await GetUserRole({
				OnlyData: true,
				ShowError: true
			});
			setRole(res.Role);
		} catch (err) {};
	};
	useEffect(() => {
		getAllProduct();
		getUserRole();
		if (isUpdate || isSee) {
			getDescribeStrategy();
		}
	}, []);

	const isManager = useMemo(() => (role ? role === 'Manager' : undefined), [role]);

	if (!isManager && handle === 'update') {
		return <Status
			icon={isManager === undefined ? 'loading' : 'no-permission'}
			size={'l'}
			title={isManager === undefined ? '加载中' : '暂无权限'}
		/>;
	}
	return <Body className={'detail-form-wrap strategies-detail-form-wrap'}>
		<Content.Header
			showBackButton
			onBackButtonClick={() => {
				history.push(`/advisor/${path}`);
			}}
			title={'云巡检策略-配置管理'}
			operation={
				<ExternalLink href="https://iwiki.woa.com/p/4012492499">
					帮助指引
				</ExternalLink>
			}
		/>
		<Content.Body>
			<form onSubmit={handleSubmit}>
				<Form
					layout={'inline'}
				>
					{
						formRenderList.map((el, i)=>{
							return <div key={i}>
								<Card>
									<H3>
										{
											i == 0
												?
												'策略配置'
												:
												i == 1
													?
													<>
														风险/条件配置
														<Button
															style={
																{
																	marginLeft: '30px'
																}
															}
                              disabled={formRenderList[1]?.length > 9}
															onClick={
																(e)=>{
																	formRenderList[1].push(cloneDeep(conditionFieldInfo));
																	setFormRenderList(cloneDeep(formRenderList));
																	e.stopPropagation();
																	e.preventDefault();
																}
															}
														>
															新增
														</Button>
													</>
													:
													''
										}
									</H3>
									{
										i === 1
											?
											<Card.Body>
												{
													el.map((obj, j)=>{
														return <div key={j} className={j > 0 && obj.length > 0 ? 'form-line' : ''}>
															{
																obj.map((item, i)=>{
																	const name = `${item.name}-${j}`;
																	if (fieldNotInCreateList.includes(item.name) && isCreate) {
																		return '';
																	}
																	return item.type == 'input'
																		?
																		<Form.Item
																			key={i}
																			required={item.required}
																			label={item.label}
																			status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																			message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																		>
																			<Input {...formFieldInfo[name].input} autoComplete="off" placeholder={item.placeholder} disabled={item.disabled || false}/>
																		</Form.Item>
																		:
																		item.type == 'input-number'
																	?
																			<Form.Item
																				key={i}
																				required={item.required}
																				label={item.label}
																				status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																				message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																				tips={item.tips && item.tips}
																			>
																				<InputNumber
																					{...formFieldInfo[name].input}
																					// value={(formFieldInfo[name].input.value === '') ? null : formFieldInfo[name].input.value}
																					min={0}
																					max={100}
																					step={1}
																					allowEmpty={true}
																				/>
																			</Form.Item>
																			:
																		item.type == 'textarea'
																			?
																			<div key={i}>
																				<Form.Item
																					required={item.required}
																					label={item.label}
																					status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																					message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																					tips={item.tips && item.tips}
																				>
																					<Input.TextArea
																						{...formFieldInfo[name].input}
																						autoComplete="off"
																						placeholder={item.placeholder}
																						style={{
																							width: '820px',
																							height: item.name === 'ApiDesc' ? '180px' : '60px'
																						}}
																					/>
																				</Form.Item>
																			</div>
																			:
																			item.type == 'text' && !isCreate
																	?
																				<Form.Item
																					className={'form-item-switch'}
																					key={i}
																					required={item.required}
																					label={item.label}
																				>
																					<Input {...formFieldInfo[name].input} disabled={true}/>
																				</Form.Item>
																				:
																			item.type == 'radio-group'
																				?
																				<Form.Item
																					className={'form-item-switch'}
																					key={i}
																					required={item.required}
																					label={item.label}
																					status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																					message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																				>
																					<Radio.Group {...formFieldInfo[name].input}>
																						{
																							item.options.map((el, k)=>{
																								return <Radio key={k} name={el.value}>{el.text}</Radio>
																							})
																						}
																					</Radio.Group>
																				</Form.Item>
																				:
																			item.type == 'select'
																				?
																				<Form.Item
																					key={i}
																					required={item.required}
																					label={item.label}
																					status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																					message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																				>
																					<Select
																						searchable={item.searchable || false}
																						matchButtonWidth
																						{...formFieldInfo[name].input}
																						options={item.options}
																						appearance="button"
																						size='m'
																					/>
																				</Form.Item>
																				:
																				item.type == 'switch'
																					?
																					<Form.Item
																						key={i}
																						className={'form-item-switch'}
																						required={item.required}
																						label={item.label}
																						status={item.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																						message={item.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																					>
																						<Switch {...formFieldInfo[name].input} />
																					</Form.Item>
																					:
																					item.type == 'render'
																					?
																						<div key={i}>
																							<div style={{marginBottom: '10px'}}>测试账号：</div>
																							{
																								item.children.map((el, k)=>{
																									const name = `${el.name}-${j}`;
																									return <Form.Item
																										key={k}
																										required={el.required}
																										label={el.label}
																										status={el.required ? getStatus(formFieldInfo[name].meta, validating) : null}
																										message={el.required ? (getStatus(formFieldInfo[name].meta, validating) === "error" && formFieldInfo[name].meta.error) : ''}
																									>
																										<Input {...formFieldInfo[name].input} autoComplete="off" placeholder={el.placeholder} disabled={el.disabled || false}/>
																									</Form.Item>;
																								})
																							}
																						</div>
																						:
																					'';
																})
															}
															{
																j > 0 && obj.length > 0 && <div>
																	<Button
																		style={
																			{
																				marginBottom: '20px'
																			}
																		}
																		onClick={
																			(e)=>{
																				formRenderList[1].forEach((el, k)=>{
																					conditionFieldInfo.forEach((val)=>{
																						const name = `${val.name}-${k}`;
																						if (j <= k) {
																							form.change(name, values[`${val.name}-${k + 1}`]);
																						}
																					});
																				});
																				formRenderList[1].splice(j, 1);
																				setFormRenderList(cloneDeep(formRenderList));
																				e.stopPropagation();
																				e.preventDefault();
																			}
																		}
																	>删除</Button>
																</div>
															}
														</div>;
													})
												}
											</Card.Body>
											:
											<Card.Body>
												{
													el.map((item, i)=>{
														if (fieldNotInCreateList.includes(item.name) && isCreate) {
															return '';
														}
                            if (item.name === 'NeedDev' && values.NeedDev === undefined) {
                              return '';
                            }
														return item.type == 'input'
															?
															<Form.Item
																key={i}
																required={item.required}
																label={item.label}
																status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																tips={item.tips && item.tips}
															>
																<Input {...formFieldInfo[item['name']].input} autoComplete="off" placeholder={item.placeholder} disabled={item.disabled || false} />
															</Form.Item>
															:
															item.type == 'checkbox-group'
														?
																<Form.Item
																	className={'form-item-switch'}
																	key={i}
																	required={item.required}
																	label={item.label}
																	status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																	message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																>
																	<Checkbox.Group {...formFieldInfo[item['name']].input}>
																		{
																			item.options.map((el, k)=>{
																				return <Checkbox key={k} name={el.value}>{el.text}</Checkbox>
																			})
																		}
																	</Checkbox.Group>
																</Form.Item>
																:
																item.type == 'radio-group'
														?
																	<Form.Item
																		className={'form-item-switch'}
																		key={i}
																		required={item.required}
																		label={item.label}
																		status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																		message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																	>
																		<Radio.Group {...formFieldInfo[item['name']].input}>
																			{
																				item.options.map((el, k)=>{
																					return <Radio key={k} name={el.value}>{el.text}</Radio>
																				})
																			}
																		</Radio.Group>
																	</Form.Item>
																	:
															item.type == 'textarea'
																?
																<div key={i}>
																	<Form.Item
																		required={item.required}
																		label={item.label}
																		status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																		message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
                                    tips={item.tips && item.tips}
                                  >
																		<Input.TextArea
																			{...formFieldInfo[item['name']].input}
																			autoComplete="off"
																			placeholder={item.placeholder}
																			style={{
																				width: '820px',
																				height: '60px'
																			}}
																		/>
																	</Form.Item>
																</div>
																:
                                item.type == 'editor'
                            ?
                                  <div key={i}>
                                    <Form.Item
                                      required={item.required}
                                      label={item.label}
                                      status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
                                      message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
                                      tips={item.tips && item.tips}
                                      className={'strategies-new-editor-form-item'}
                                    >
                                      <MyEditor
                                        value={values[item['name']]}
                                        onChange={
                                          (val)=>{
                                            form.change(item['name'], val);
                                          }
                                        }
                                        onBlur={
                                          ()=>{
                                            form.blur(item['name']);
                                          }
                                        }
                                      />
                                    </Form.Item>
                                  </div>
                                  :
																item.type == 'select'
																	?
																	<Form.Item
																		key={i}
																		required={item.required}
																		label={item.label}
																		status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																		message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																		tips={item.tips && item.tips}
																	>
																		<Select
																			searchable={item.searchable || false}
																			overlayClassName={item.name == 'Template' ? 'tem-sel' : ''}
																			matchButtonWidth
																			{...formFieldInfo[item['name']].input}
																			options={item.options}
																			appearance="button"
																			size='m'
																		/>
																	</Form.Item>
																	:
																	item.type == 'switch'
																		?
																		<Form.Item
																			key={i}
																			className={'form-item-switch'}
																			required={item.required}
																			label={item.label}
																			status={item.required ? getStatus(formFieldInfo[item['name']].meta, validating) : null}
																			message={item.required ? (getStatus(formFieldInfo[item['name']].meta, validating) === "error" && formFieldInfo[item['name']].meta.error) : ''}
																		>
																			<Switch {...formFieldInfo[item['name']].input} />
																		</Form.Item>
																		:
																		<></>;
													})
												}
											</Card.Body>
									}
									{
										(i === 1 && !isSee) && <div className='btn-wrap'>
                      <Button htmlType="button" onClick={
                        ()=>{
                          setPreviewModalVisible(true);
                        }
                      }>预览</Button>
											<Button htmlType="submit" type={'primary'} loading={saveLoading} onClick={
                        ()=>{
                          submitType = '';
                        }
                      }>保存</Button>
                      {
                        (isCreate || type === 'submitReview') && <Button htmlType="submit" loading={submitReviewLoading} onClick={
                          ()=>{
                            submitType = 'submitReview';
                          }
                        }>提交审核</Button>
                      }
                      {
                        (type === 'approve') && <Button htmlType="submit" loading={approveLoading} onClick={
                          ()=>{
                            submitType = 'approve';
                          }
                        }>通过</Button>
                      }
                      {
                        (type === 'submit') && <Button htmlType="submit" loading={submitLoading} onClick={
                          ()=>{
                            submitType = 'submit';
                            isClickSubmit = true;
                          }
                        }>提交</Button>
                      }
											<Button htmlType={'button'} onClick={() => {
												history.push(`/advisor/strategies-manage`);
											}}>返回</Button>
										</div>
									}
								</Card>
							</div>;
						})
					}
				</Form>
			</form>
		</Content.Body>
    <PreviewModal
      visible={previewModalVisible}
      formInfo={values}
      onClose={
        () => {
          setPreviewModalVisible(false);
        }
      }
    />
	</Body>;
}