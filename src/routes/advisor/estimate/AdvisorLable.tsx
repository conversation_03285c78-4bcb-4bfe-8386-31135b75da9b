import React from 'react';
import { MediaObject } from '@tencent/tea-component/lib/mediaobject';
import { Text } from '@tencent/tea-component/lib/text';
import { tabTitleMap } from '@src/routes/advisor/estimate/AdvisorConfig';
// import { t } from '@tencent/tea-app/i18n';

interface Props {
	id: string;
	groupSummary: object;
}
export function AdvisorLable({ id, groupSummary }: Props) {
	function getMediaClassName(id: string): string {
		return `intlc-assessment-tabs__media intlc-assessment-tabs__media--${id}`;
	}
	return (
		<>
			<MediaObject media={<div className={getMediaClassName(id)}></div>}>
				<div className="intlc-assessment-tabs__title">{tabTitleMap.get(id)}</div>
				{
					<div className="intlc-assessment-tabs__content">
						<div className="intlc-assessment-tabs-tag">
							<Text
								bgTheme="danger"
								verticalAlign="top"
								className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--tabs"
							></Text>
							<Text theme="danger" className="intlc-assessment-tabs-tag__num">
								{groupSummary[id] && groupSummary[id].high >= 0 ? groupSummary[id].high : '-'}
							</Text>
						</div>
						<div className="intlc-assessment-tabs-tag">
							<Text
								bgTheme="warning"
								verticalAlign="top"
								className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--tabs"
							></Text>
							<Text theme="warning" className="intlc-assessment-tabs-tag__num">
								{groupSummary[id] && groupSummary[id].medium >= 0 && groupSummary[id].low >= 0
									? groupSummary[id].medium + groupSummary[id].low
									: '-'}
							</Text>
						</div>
						<div className="intlc-assessment-tabs-tag">
							<Text
								bgTheme="success"
								verticalAlign="top"
								className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--tabs"
							></Text>
							<Text theme="success" className="intlc-assessment-tabs-tag__num">
								{groupSummary[id] && groupSummary[id].none >= 0 ? groupSummary[id].none : '-'}
							</Text>
						</div>
					</div>
				}
			</MediaObject>
		</>
	);
}
