import React, { useState, useContext } from 'react';
// import { t, Trans } from '@tencent/tea-app/i18n';
import { tabTitleMap } from '@src/routes/advisor/estimate/AdvisorConfig';

export function EmptyPanel({ id }) {
	return (
		<div className="intlc-assessment-tabitem">
			<div className="intlc-assessment-tabitem__advice">{tabTitleMap.get(id)}</div>
			<div className="intlc-assessment-tabitem">
				<div className="intlc-assessment-tabitem__empty">
					<span>暂无内容</span>
				</div>
			</div>
		</div>
	);
}
