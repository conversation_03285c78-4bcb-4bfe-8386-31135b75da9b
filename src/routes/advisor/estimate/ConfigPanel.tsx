import React, { useState, useContext, useEffect, useRef, useMemo } from 'react';
import { app } from '@tencent/tea-app';
// import { t, Trans } from '@tencent/tea-app/i18n';
import { tabTitleMap } from '@src/routes/advisor/estimate/AdvisorConfig';
import { ConfigStrategy } from '@src/types/advisor/estimate1';
import { Collapse } from '@tencent/tea-component/lib/collapse';
import { Icon } from '@tencent/tea-component/lib/icon';
import { Text } from '@tencent/tea-component/lib/text';

interface Prop {
	id: string;
	detectionStrategies: Array<ConfigStrategy>;
}

// 警告条件主题颜色映射
const conditionThemeConfig = {
	'-1': 'weak',
	0: 'success',
	1: 'warning',
	2: 'warning',
	3: 'danger',
};

export function ConfigPanel({ id, detectionStrategies }: Prop) {
	return (
		<div className="intlc-assessment-tabitem">
			<div className="intlc-assessment-tabitem__advice">{tabTitleMap.get(id)}</div>
			{detectionStrategies.map((strategy) => {
				return (
					<Collapse
						key={strategy.StrategyId}
						icon={(active) => (
							<Text
								verticalAlign="top"
								className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--item"
								style={{ backgroundColor: '#bbb' }}
							></Text>
						)}
						iconPosition="left"
						className="intlc-assessment-tabitem__content"
					>
						<Collapse.Panel
							id={strategy.StrategyId.toString()}
							title={
								<>
									<div className="intlc-assessment-tabitem__title">{strategy.Name}</div>
									<div className="intlc-assessment-tabitem__introduction">
										<div className="intlc-assessment-tabitem__instructions">{strategy.Desc}</div>
									</div>
								</>
							}
						></Collapse.Panel>
					</Collapse>
				);
			})}
		</div>
	);
}
