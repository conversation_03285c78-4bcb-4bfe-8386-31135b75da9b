import React, { useState, useContext, useEffect, useRef, useMemo } from 'react';
import { app } from '@tencent/tea-app';
// import { t, span } from '@tencent/tea-app/i18n';
import { Text } from '@tencent/tea-component/lib/text';
import { Icon } from '@tencent/tea-component/lib/icon';
import { Modal } from '@tencent/tea-component/lib//modal';
import { Button } from '@tencent/tea-component/lib//button';
import { Segment } from '@tencent/tea-component/lib/segment';

export function AdvisorStrategyReport({ id, type, reportStatusQueue, onChangeReportAsync }) {
	const [showLoading, setShowLoading] = useState(false);
	const [visible, setVisible] = useState(false);
	const [reportType, setReportType] = useState(1);
	const currentReport1 = reportStatusQueue.find((report) => id === report.Id && report.Type === type);
	const currentReport = currentReport1 ? JSON.parse(JSON.stringify(currentReport1)) : null;  //深度copy，避免useEffect不刷新
	const lastReport = useRef({ RequestId: '' }); //利用useRef,存放上一次数据，用来判断是否发生变化
	useEffect(() => {
		if (currentReport && lastReport.current.RequestId && lastReport.current.RequestId === currentReport.RequestId) {
			//前后两次调用，数据一致，则不变化
			return;
		} else {
			if (currentReport && (currentReport.TaskStatus === 'success' || currentReport.TaskStatus === 'failed')) {
				setShowLoading(false);
			}
		}
		if (currentReport) {
			lastReport.current = currentReport;
		}
	}, [currentReport]);

	return (
		<>
			{(!currentReport || currentReport.TaskStatus === 'failed') && !showLoading && (
				<Text
					style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
					onClick={(e) => {
						e.stopPropagation();
						setShowLoading(true);
						onChangeReportAsync(id, type);
					}}
				>
					<svg
						xmlns="http://www.w3.org/2000/svg"
						width="16"
						height="16"
						style={{ marginLeft: 30, marginRight: 5 }}
					>
						<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#006cff" />
					</svg>
					<Text style={{ fontSize: 12, color: '#006cff' }}>
						<span>生成报告</span>
					</Text>
				</Text>
			)}
			{showLoading && (
				<Text style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}>
					<Icon type="loading" style={{ marginLeft: 10 }}></Icon>
					<Text style={{ fontSize: 12, color: '#006cff', marginLeft: 5 }}>
						<span>报告生成中</span>
					</Text>
				</Text>
			)}
			{currentReport &&
				currentReport.TaskStatus === 'success' &&
				!showLoading &&
				(currentReport.CosUrlPdf !== '' ? (
					<Text
						style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
						onClick={(e) => {
							e.stopPropagation();
							setVisible(true);
						}}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="16"
							height="16"
							style={{ marginLeft: 30, marginRight: 5 }}
						>
							<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#d71111" />
						</svg>
						<Text style={{ fontSize: 12, color: '#d71111' }}>
							<span>下载报告</span>
						</Text>
					</Text>
				) : (
					<Text
						style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
						onClick={(e) => {
							e.stopPropagation();
							window.open(currentReport.CosUrl);
						}}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="16"
							height="16"
							style={{ marginLeft: 30, marginRight: 5 }}
						>
							<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#d71111" />
						</svg>
						<Text style={{ fontSize: 12, color: '#d71111' }}>
							<span>下载报告</span>
						</Text>
					</Text>
				))}
			<Modal visible={visible} onClose={() => setVisible(false)} caption="评估报告下载">
				<Modal.Body>
					<span>请选择下载报告类型：</span>
					<br />

					<Segment
						value={reportType.toString()}
						onChange={(value) => setReportType(parseInt(value, 10))}
						options={[
							{
								text: (
									<>
										<div>评估报告EXCEL版</div>
									</>
								),
								value: '1',

								style: { width: '200px', height: '80px' },
							},
							{
								text: (
									<>
										<div>评估报告PDF版</div>
									</>
								),
								value: '2',

								style: { width: '200px', height: '80px', marginLeft: '10px' },
							},
						]}
					/>
				</Modal.Body>
				<Modal.Footer>
					<Button
						type="primary"
						onClick={(e) => {
							if (reportType === 1) {
								e.stopPropagation();
								window.open(currentReport.CosUrl);
							} else if (reportType === 2) {
								e.stopPropagation();
								window.open(currentReport.CosUrlPdf);
							}
							setVisible(false);
						}}
					>
						<span>确定</span>
					</Button>
					<Button type="weak" onClick={() => setVisible(false)}>
						<span>取消</span>
					</Button>
				</Modal.Footer>
			</Modal>
		</>
	);
}
