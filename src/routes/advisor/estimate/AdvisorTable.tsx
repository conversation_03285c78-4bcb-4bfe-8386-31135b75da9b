import React, { useState, useContext, useEffect, useMemo } from 'react';
import { app } from '@tencent/tea-app';
// import { t, span } from '@tencent/tea-app/i18n';
import { message } from '@tea/component/message';
import { modifyIgnoreInstance, modifyIgnoreInstances, getTags } from '@src/api/advisor/estimate';
import { Instance, IgnoreInstance, ConfigStrategy, RegionCode } from '@src/types/advisor/estimate1';
import { Button } from '@tea/component/button';
import { SearchBox } from '@tencent/tea-component/lib/searchbox';
import { Text } from '@tencent/tea-component/lib/text';
import { Table } from '@tencent/tea-component/lib/table';
import { SortBy } from '@tencent/tea-component/lib/table/addons';
import { Tabs, TabPanel } from '@tencent/tea-component/lib/tabs';
import { Justify } from '@tencent/tea-component/lib/justify';
import { StatusTip } from '@tencent/tea-component/lib/tips';
import { Bubble } from '@tencent/tea-component/lib/bubble';
import { Icon } from '@tencent/tea-component/lib/icon';
import { Tag } from '@tencent/tea-component/lib/tag';
import { TagSearchBox } from '@tencent/tea-component/lib/tagsearchbox';
import _ from 'lodash';

const { pageable, sortable, selectable } = Table.addons;

interface Prop {
	AppId: number;
	product: string;
	strategyId: number;
	strategy: ConfigStrategy;
	regionCodes: Array<RegionCode>;
	currentTaskId: string;
	strategyAssesses: Array<Instance>;
	assessTotalCount: number;
	strategyIgnores: Array<Instance>;
	ignoreTotalCount: number;
	onChangeAssesses: Function;
	onChangeIgnores: Function;
}

export function AdvisorTable({
	AppId,
	product,
	strategyId,
	strategy,
	regionCodes,
	currentTaskId,
	strategyAssesses,
	assessTotalCount,
	strategyIgnores,
	ignoreTotalCount,
	onChangeAssesses,
	onChangeIgnores,
}: Prop) {
	// 评估中的实例列表
	const [assessInstances, setAssessInstances] = useState([]);
	// 忽略的实例列表
	const [ignoreInstances, setIgnoreInstances] = useState([]);
	// 评估中的实例列表的当前忽略状态
	const [assessStatus, setAssessStatus] = useState({});
	// 忽略的实例列表的当前忽略状态
	const [ignoreStatus, setIgnoreStatus] = useState({});
	// 评估中的实例列表当前页长
	const [assessPageSize, setAssessPageSize] = useState(10);
	// 忽略的实例列表当前分页长
	const [ignorePageSize, setIgnorePageSize] = useState(10);
	// 评估中的实例列表当前页码
	const [assessPageIndex, setAssessPageIndex] = useState(1);
	// 忽略的实例列表当前分页长
	const [ignorePageIndex, setIgnorePageIndex] = useState(1);
	// 列表项配置
	const [columns, setColumns] = useState([]);
	// 当前评估中的列表项
	const [assessRecords, setAssessRecords] = useState([]);
	const [assessSorts, setAssessSorts] = useState([{ by: 'Level', order: 'desc' } as SortBy]);
	// 当前忽略的列表项
	const [ignoreRecords, setIgnoreRecords] = useState([]);
	const [ignoreSorts, setIgnoreSorts] = useState([{ by: 'Level', order: 'desc' } as SortBy]);
	// 当前评估中的列表项状态
	const [assessTableStatus, setAssessTableStatus] = useState('none');
	// 当前忽略的列表项状态
	const [ignoreTableStatus, setIgnoreTableStatus] = useState('none');
	// 当前评估中的列表项已选择项
	const [assessSelectedKeys, setAssessSelectedKeys] = useState([]);
	// 当前忽略的列表项已选择项
	const [ignoreSelectedKeys, setIgnoreSelectedKeys] = useState([]);
	// 资源名称输入值
	const [resourceInputValue, setResourceInputValue] = useState('');
	// 当前选项卡
	const [currentTabId, setCurrentTabId] = useState('assess');
	// 当前操作
	const [currentOperation, setCurrentOperation] = useState('');
	// 当前操作实例
	const [optInstanceId, setOptInstanceId] = useState('');
	// 当前标签键/标签值选项
	const [tagSearchOptions, setTagSearchOptions] = useState([]);
	// 当前评估中的列表标签搜索支持属性
	const [assessTagSearchAttributes, setAssessTagSearchAttributes] = useState([]);
	// 当前评估中的列表标签搜索值
	const [assessTagSearchValue, setAssessTagSearchValue] = useState([]);
	// 当前评估中的列表标签值选项
	const [assessTagValueOptions, setAssessTagValueOptions] = useState([]);
	// 当前忽略的列表标签搜索支持属性
	const [ignoreTagSearchAttributes, setIgnoreTagSearchAttributes] = useState([]);
	// 当前忽略的列表标签搜索值
	const [ignoreTagSearchValue, setIgnoreTagSearchValue] = useState([]);
	// 当前忽略的列表标签值选项
	const [ignoreTagValueOptions, setIgnoreTagValueOptions] = useState([]);

	const resourceTabs = [
		{ id: 'assess', label: '评估中的资源列表' },
		{ id: 'ignore', label: '被忽略的资源列表' },
	];

	// 警告条件主题颜色映射
	const conditionThemeConfig = {
		'-1': 'weak',
		0: 'success',
		1: 'warning',
		2: 'warning',
		3: 'danger',
	};

	const BASE_URL = 'https://console.cloud.tencent.com';

	// 列表项唯一键
	const getRecordKey = () => {
		switch (product) {
			case 'mysql':
				return 'InstanceId';
			case 'cvm':
				return 'InstanceId';
			case 'mongodb':
				return 'InstanceId';
			case 'cbs':
				return 'DiskId';
			case 'Elasticsearch Service':
				return 'InstanceId';
			case 'redis':
				return 'InstanceId';
			case 'cos':
				return 'Id';
			case 'cdn':
				return 'ResourceId';
			case 'clb':
				return 'InstanceId';
			case 'eip':
				return 'AddressId';
			case 'tdsql':
				return 'InstanceId';
			case 'dayubgp':
				return 'RuleID';
			case 'dayu':
				return 'Id';
			case 'tke':
				return 'ID';
			case 'as':
				return 'LaunchConfigurationId';
			case 'cfw':
				return 'InstanceId';
			case 'tdmq':
				return 'ClusterId';
		}
	};
	const recordKey = getRecordKey();

	const TipHeaderComponent = useMemo(() => {
		return (
			<>
				{'操作'}
				<Bubble
					content={
						currentTabId === 'assess' ? (
							<>
								<Text parent="div">
									<Text style={{ fontWeight: 'bold' }}>
										<span>忽略</span>
									</Text>
									<span>：忽略后的资源，将不再被评估；</span>
								</Text>
								<Text parent="div">
									<Text style={{ fontWeight: 'bold' }}>
										<span>添加</span>
									</Text>
									<span>：将资源添加到评估列表中，下一次评估生效；</span>
								</Text>
							</>
						) : (
							<>
								<Text parent="div">
									<Text style={{ fontWeight: 'bold' }}>
										<span>添加</span>
									</Text>
									<span>：将资源添加到评估列表中，下一次评估生效；</span>
								</Text>
								<Text parent="div">
									<Text style={{ fontWeight: 'bold' }}>
										<span>忽略</span>
									</Text>
									<span>：忽略后的资源，将不再被评估；</span>
								</Text>
							</>
						)
					}
				>
					<Icon type="info" />
				</Bubble>
			</>
		);
	}, [currentTabId]);

	const getColumns = (codes) => {
		const getTagColumn = (instance) => {
			return instance.Tags && instance.Tags.length ? (
				<>
					{instance.Tags.map((tag) => (
						<Tag>
							{tag.Key || ''}:{tag.Value || ''}
						</Tag>
					))}
				</>
			) : (
				'无'
			);
		};

		switch (product) {
			case 'mysql':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'InstanceId',
						header: 'ID',
					},
					{
						key: 'InstanceName',
						header: '名称',
					},
					{
						key: 'Status',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								0: '创建中',
								1: '运行中',
								4: '隔离中',
								5: '已隔离',
							};
							return statusConfig[instance.Status];
						},
					},
					{
						key: 'Zone',
						header: '可用区',
					},
					{
						key: 'IP',
						header: '内网地址:端口',
						render: (instance) => {
							return instance.Vip && instance.Vport ? `${instance.Vip}:${instance.Vport}` : '';
						},
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'cvm':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'InstanceId',
						header: 'ID',
					},
					{
						key: 'InstanceName',
						header: '名称',
					},
					{
						key: 'InstanceState',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								PENDING: '创建中',
								LAUNCH_FAILED: '创建失败',
								RUNNING: '运行中',
								STOPPED: '关机',
								STARTING: '开机中',
								STOPPING: '关机中',
								REBOOTING: '重启中',
								SHUTDOWN: '停止待销毁',
								TERMINATING: '销毁中',
							};
							return statusConfig[instance.InstanceState];
						},
					},
					{
						key: 'Zone',
						header: '可用区',
					},
					{
						key: 'IP',
						header: 'IP地址',
						render: (instance) => {
							return (
								<div>
									{instance.PublicIpAddresses ? (
										<div>{`${instance.PublicIpAddresses.join(',')}${'(公)'}`}</div>
									) : (
										''
									)}
									{instance.PrivateIpAddresses ? (
										<div>{`${instance.PrivateIpAddresses.join(',')}${'(内)'}`}</div>
									) : (
										''
									)}
								</div>
							);
						},
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'mongodb':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'InstanceId',
						header: 'ID',
					},
					{
						key: 'InstanceName',
						header: '名称',
					},
					{
						key: 'Status',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								0: '待初始化',
								1: '流程处理中',
								2: '运行中',
								'-2': '实例已过期',
							};
							return statusConfig[instance.Status.toString()];
						},
					},
					{
						key: 'IP',
						header: '内网地址:端口',
						render: (instance) => {
							return instance.Vip && instance.Vport ? `${instance.Vip}:${instance.Vport}` : '';
						},
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'cbs':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'DiskId',
						header: 'ID',
					},
					{
						key: 'DiskName',
						header: '名称',
					},
					{
						key: 'DiskState',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								UNATTACHED: '未挂载',
								ATTACHING: '挂载中',
								ATTACHED: '已挂载',
								DETACHING: '解挂中',
								EXPANDING: '扩容中',
								ROLLBACKING: '回滚中',
								TORECYCLE: '待回收',
								DUMPING: '拷贝硬盘中',
							};
							return statusConfig[instance.DiskState];
						},
					},
					{
						key: 'Zone',
						header: '可用区',
					},
					{
						key: 'DiskUsage',
						header: '属性',
					},
					{
						key: 'DiskType',
						header: '类型',
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'Elasticsearch Service':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'InstanceId',
						header: 'ID',
					},
					{
						key: 'InstanceName',
						header: '名称',
					},
					{
						key: 'Status',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								0: '处理中',
								1: '正常',
								'-1': '停止',
								'-2': '销毁中',
								'-3': '已销毁',
								'-4': '已过期',
							};
							return statusConfig[instance.Status.toString()];
						},
					},
					{
						key: 'Zone',
						header: '可用区',
					},
					{
						key: 'NodeType',
						header: '集群配置',
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'redis':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'InstanceId',
						header: 'ID',
					},
					{
						key: 'InstanceName',
						header: '名称',
					},
					{
						key: 'Status',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								0: '待初始化',
								1: '实例在流程中',
								2: '实例运行中',
								'-2': '实例已隔离',
								'-3': '实例待删除',
							};
							return statusConfig[instance.Status.toString()];
						},
					},
					{
						key: 'Zone',
						header: '可用区',
					},
					{
						key: 'IP',
						header: '内网地址:端口',
						render: (instance) => {
							return instance.WanIp && instance.Port ? `${instance.WanIp}:${instance.Port}` : '';
						},
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'cos':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'Id',
						header: '存储桶名称',
					},
					{
						key: 'Region',
						header: '地域',
					},
					{
						key: 'CreationDate',
						header: '创建时间',
						render: (instance) => {
							return instance.CreationDate
								? instance.CreationDate.replace('T', ' ').replace('Z', '')
								: '';
						},
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'cdn':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'Domain',
						header: '域名',
					},
					{
						key: 'Status',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								rejected: '域名审核未通过',
								processing: '部署中',
								online: '已启动',
								offline: '已关闭',
							};
							return statusConfig[instance.Status];
						},
					},
					{
						key: 'Cname',
						header: 'CNAME',
					},
					{
						key: 'ServiceType',
						header: '业务类型',
						render: (instance) => {
							const serviceConfig = {
								web: '静态加速',
								download: '下载加速',
								media: '流媒体点播加速',
							};
							return serviceConfig[instance.ServiceType];
						},
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'clb':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
					},
					{
						key: 'InstanceId',
						header: 'ID',
					},
					{
						key: 'InstanceName',
						header: '名称',
					},
					{
						key: 'Status',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								0: '创建中',
								1: '正常运行',
							};
							return statusConfig[instance.Status];
						},
					},
					{
						key: 'Region',
						header: '地域',
					},
					{
						key: 'Vip',
						header: 'VIP',
						render: (instance) => {
							return <div>{instance.Vip ? `${instance.Vip.join(',')}` : ''}</div>;
						},
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'eip':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'AddressId',
						header: 'ID',
					},
					{
						key: 'AddressName',
						header: '名称',
					},
					{
						key: 'AddressStatus',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								CREATING: '创建中',
								BINDING: '绑定中',
								BIND: '已绑定',
								UNBINDING: '解绑中',
								UNBIND: '未绑定',
								OFFLINING: '释放中',
								BIND_ENI: '绑定悬空弹性网卡',
							};
							return statusConfig[instance.AddressStatus];
						},
					},
					{
						key: 'Region',
						header: '地域',
					},
					{
						key: 'AddressIp',
						header: 'EIP地址',
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'tdsql':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'InstanceId',
						header: 'ID',
					},
					{
						key: 'InstanceName',
						header: '名称',
					},
					{
						key: 'Status',
						header: '状态',
						render: (instance) => {
							const statusConfig = {
								0: '创建中',
								1: '流程处理中',
								2: '运行中',
								3: '实例未初始化',
								'-1': '实例已隔离',
								'-2': '实例已删除',
							};
							return statusConfig[instance.Status.toString()];
						},
					},
					{
						key: 'Zone',
						header: '可用区',
					},
					{
						key: 'IP',
						header: '内网地址:端口',
						render: (instance) => {
							return instance.Vip && instance.Vport ? `${instance.Vip}:${instance.Vport}` : '';
						},
					},

					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'dayubgp':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},

					{
						key: 'RuleID',
						header: '地址',
					},
					{
						key: 'IPAddress',
						header: 'IP地址',
					},
					{
						key: 'ErrorCode',
						header: '错误码',
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'dayu':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},

					{
						key: 'Id',
						header: '地址',
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'tke':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'Id',
						header: '实例ID',
					},
					{
						key: 'ClusterName',
						header: '集群ID',
					},
					{
						key: 'ResourceStatus',
						header: '集群状态',
						render: (instance) => {
							let ResourceStatus = '-';
							if (instance.ResourceStatus) {
								ResourceStatus = instance.ResourceStatus;
							}

							return <span>{ResourceStatus}</span>;
						},
					},

					{
						key: 'Region',
						header: '地域',
					},

					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'cam':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'Uin',
						header: 'Uin',
					},
					{
						key: 'SubAccounts',
						header: '子用户个数',
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'as':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'LaunchConfigurationId',
						header: '启动配置ID',
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'cfw':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'InstanceId',
						header: '启动配置ID',
					},
					{
						key: 'Tags',
						header: '标签',
						render: getTagColumn,
					},
					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
			case 'tdmq':
				return [
					{
						key: 'Level',
						header: '',
						align: 'center',
						width: 60,
						render: (instance) => {
							const level = instance.Level;
							return level >= 0 ? (
								<Text
									bgTheme={conditionThemeConfig[level]}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							) : (
								<Text
									style={{ backgroundColor: '#bbb' }}
									className="intlc-assessment-tabs-tag__block"
								></Text>
							);
						},
					},
					{
						key: 'ClusterId',
						header: '集群ID',
					},
					{
						key: 'EnvironmentId',
						header: '命名空间名',
					},
					{
						key: 'TopicName',
						header: '主题名',
					},
					{
						key: 'SubscriptionName',
						header: '订阅名',
						render: (instance) => {
							let SubscriptionName = '-';
							if (instance.SubscriptionName) {
								SubscriptionName = instance.SubscriptionName;
							}

							return <span>{SubscriptionName}</span>;
						},
					},

					{
						key: 'Operation',
						header: TipHeaderComponent,
					},
				];
		}
	};

	const getTagSearchAttributes = (tagSearchOptions, tabId) => {
		const tagKeyOptions = tagSearchOptions.map((opt) => {
			return {
				key: opt.TagKey || '',
				name: opt.TagKey || '',
			};
		});

		const tagValueOptionsConfig = new Map([
			['assess', assessTagValueOptions],
			['ignore', ignoreTagValueOptions],
		]);

		switch (product) {
			case 'mysql':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'cvm':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'mongodb':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'cbs':
				return [
					{
						type: 'input',
						key: 'DiskId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'Elasticsearch Service':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'redis':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'cos':
				return [
					{
						type: 'input',
						key: 'Id',
						name: '存储桶名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'cdn':
				return [
					{
						type: 'input',
						key: 'Domain',
						name: '域名',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'clb':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'tdsql':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'eip':
				return [
					{
						type: 'input',
						key: 'AddressId',
						name: '实例ID/名称',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'dayubgp':
				return [
					{
						type: 'input',
						key: 'RuleID',
						name: 'ID',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'dayu':
				return [
					{
						type: 'input',
						key: 'ID',
						name: 'ID',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'tke':
				return [
					{
						type: 'input',
						key: 'ID',
						name: '实例ID',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'as':
				return [
					{
						type: 'input',
						key: 'LaunchConfigurationId',
						name: '启动配置ID',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'cfw':
				return [
					{
						type: 'input',
						key: 'InstanceId',
						name: '启动配置ID',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
			case 'tdmq':
				return [
					{
						type: 'input',
						key: 'ClusterId',
						name: '集群ID',
					},
					{
						type: ['single', { searchable: true }],
						key: 'TagKey',
						name: '标签键',
						values: tagKeyOptions,
					},
					{
						type: ['multiple', { all: true, searchable: true }],
						key: 'TagValue',
						name: '标签值',
						values: tagValueOptionsConfig.get(tabId),
					},
				];
		}
	};

	// 标签搜索值转换为过滤值
	const getConvertTagSearchValue2Filter = (tagSearchValue) => {
		let tagSearchFilter = {};
		let tagFilters = {};

		tagSearchValue.forEach((tag) => {
			const tagAttr = tag.attr || {};
			const tagValues = tag.values || {};

			switch (tagAttr.key) {
				case 'InstanceId':
					tagSearchFilter['InstanceId'] = tagValues.map((v) => v.name || '')[0];
					break;
				case 'DiskId':
					tagSearchFilter['InstanceId'] = tagValues.map((v) => v.name || '')[0];
					break;
				case 'Id':
					tagSearchFilter['InstanceId'] = tagValues.map((v) => v.name || '')[0];
					break;
				case 'Domain':
					tagSearchFilter['InstanceId'] = tagValues.map((v) => v.name || '')[0];
					break;
				case 'AddressId':
					tagSearchFilter['InstanceId'] = tagValues.map((v) => v.name || '')[0];
					break;
				case 'TagKey':
					tagFilters[tagAttr.key || ''] = tagValues.map((v) => v.name || '')[0];
					break;
				case 'TagValue':
					tagFilters[tagAttr.key || ''] = tagValues.map((v) => v.name || '');
					break;
			}
		});

		// 临时兼容 Elasticsearch Service 为 ES
		const currentProduct = product === 'Elasticsearch Service' ? 'ES' : product;

		tagSearchFilter['Product'] = currentProduct || '';
		tagSearchFilter['TaskId'] = currentTaskId || '';

		if (tagFilters['TagKey']) {
			tagSearchFilter['TagFilters'] = [tagFilters] || [];
		}

		return JSON.stringify(tagSearchFilter);
	};

	// 添加/删除单个实例
	const modifyInstance = async (id, type, operation, instance) => {
		try {
			const result = await modifyIgnoreInstance({
				AppId: AppId,
				StrategyId: id,
				Operate: operation,
				Instance: instance,
			});
			if (result.Error) {
				const msg = result.Error.Message || '';
				//app.tips.error(message);
				message.error({ content: msg });

				return;
			}

			setCurrentOperation(operation);
			setOptInstanceId(instance.Id);

			if (type === 'assess') {
				const currentAssessStatus = _.cloneDeep(assessStatus);
				currentAssessStatus[instance.Id] = operation === 'add';
				setAssessStatus(currentAssessStatus);
			} else if (type === 'ignore') {
				const currentIgnoreStatus = _.cloneDeep(ignoreStatus);
				currentIgnoreStatus[instance.Id] = !(operation === 'delete');
				setIgnoreStatus(currentIgnoreStatus);
			}

			if (operation === 'add') {
				//app.tips.success('忽略资源成功');
				message.success({ content: '忽略资源成功' });
			} else {
				//app.tips.success('添加资源成功');
				message.success({ content: '添加资源成功' });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error(message);
			message.error({ content: msg });
		}
	};

	// 添加/删除多个实例
	const modifyInstances = async (id, type, operation) => {
		try {
			let currentInstances;

			if (type === 'assess') {
				currentInstances = assessInstances
					.filter((instance) => assessSelectedKeys.includes(instance.Id))
					.map((instance) => {
						return {
							Id: instance.Id,
							Region: instance.Region,
						};
					});
			} else if (type === 'ignore') {
				currentInstances = ignoreInstances
					.filter((instance) => ignoreSelectedKeys.includes(instance.Id))
					.map((instance) => {
						return {
							Id: instance.Id,
							Region: instance.Region,
						};
					});
			}

			const result = await modifyIgnoreInstances({
				AppId: AppId,
				StrategyId: id,
				Operate: operation,
				IgnoreInstances: currentInstances,
			});
			if (result.Error) {
				const msg = result.Error.Message || '';
				//app.tips.error(message);
				message.error({ content: msg });
				return;
			}

			if (type === 'assess') {
				const currentAssessStatus = _.cloneDeep(assessStatus);

				assessSelectedKeys.forEach((instanceId) => {
					currentAssessStatus[instanceId] = operation === 'add';
				});

				setAssessStatus(currentAssessStatus);
				setAssessSelectedKeys([]);
			} else if (type === 'ignore') {
				const currentIgnoreStatus = _.cloneDeep(ignoreStatus);

				ignoreSelectedKeys.forEach((instanceId) => {
					currentIgnoreStatus[instanceId] = !(operation === 'delete');
				});

				setIgnoreStatus(currentIgnoreStatus);
				setIgnoreSelectedKeys([]);
			}

			if (operation === 'add') {
				//app.tips.success('忽略资源成功');
				message.success({ content: '忽略资源成功' });
			} else {
				//app.tips.success('添加资源成功');
				message.success({ content: '添加资源成功' });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error(message);
			message.error({ content: msg });
		}
	};

	// 获取标签键/标签值
	const getTagSearchTags = async () => {
		try {
			// 临时兼容 Elasticsearch Service 为 ES
			const currentProduct = product === 'Elasticsearch Service' ? 'ES' : product;

			const result = await getTags({
				AppId: AppId,
				Product: currentProduct || '',
				TaskId: currentTaskId || '',
			});
			if (result.Error) {
				const msg = result.Error.Message || '';
				//app.tips.error(message);

				message.error({ content: msg });
				return;
			}

			const currentTagSearchOptions = result.Tags || [];

			setTagSearchOptions(currentTagSearchOptions);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error(message);
			message.error({ content: msg });
		}
	};

	// 获取对应产品的实例列表状态
	const getIgnoredStatus = () => {
		const currentAssessStatus = {};
		const currentIgnoreStatus = {};

		strategyAssesses.forEach((instance) => {
			currentAssessStatus[instance.Id] = instance.IgnoredStatus;
		});
		strategyIgnores.forEach((instance) => {
			currentIgnoreStatus[instance.Id] = instance.IgnoredStatus;
		});

		setAssessStatus(currentAssessStatus);
		setIgnoreStatus(currentIgnoreStatus);
	};

	// 获取对应产品的实例列表
	const getInstances = async () => {
		function handleAssessInstances() {
			const currAssInstances = strategyAssesses.map((instance) => {
				// debugger
				const detail = instance.Extra ? JSON.parse(instance.Extra) : {};

				return {
					...instance,
					...detail,
					Operation: (
						<>
							<Button
								className="intlc-assessment-tabcoll__button"
								type="link"
								disabled={assessStatus[instance.Id]}
								onClick={() => {
									modifyInstance(strategyId, 'assess', 'add', {
										Id: instance.Id,
										Region: instance.Region,
									});
								}}
							>
								<span>忽略</span>
							</Button>
							<Button
								className="intlc-assessment-tabcoll__button"
								type="link"
								disabled={!assessStatus[instance.Id]}
								onClick={() => {
									modifyInstance(strategyId, 'assess', 'delete', {
										Id: instance.Id,
										Region: instance.Region,
									});
								}}
							>
								<span>添加</span>
							</Button>
						</>
					),
				};
			});

			setAssessInstances(currAssInstances);
		}

		function handleIgnoreInstances() {
			const currIgInstances = strategyIgnores.map((instance) => {
				const detail = instance.Extra ? JSON.parse(instance.Extra) : {};

				return {
					...instance,
					...detail,
					Operation: (
						<>
							<Button
								className="intlc-assessment-tabcoll__button"
								type="link"
								disabled={!ignoreStatus[instance.Id]}
								onClick={() =>
									modifyInstance(strategyId, 'ignore', 'delete', {
										Id: instance.Id,
										Region: instance.Region,
									})
								}
							>
								<span>添加</span>
							</Button>
							<Button
								className="intlc-assessment-tabcoll__button"
								type="link"
								disabled={ignoreStatus[instance.Id]}
								onClick={() =>
									modifyInstance(strategyId, 'ignore', 'add', {
										Id: instance.Id,
										Region: instance.Region,
									})
								}
							>
								<span>忽略</span>
							</Button>
						</>
					),
				};
			});

			setIgnoreInstances(currIgInstances);
		}

		try {
			handleAssessInstances();
			handleIgnoreInstances();
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error(message);
			message.error({ content: msg });
		}
	};

	// useEffect(() => {
	// 	getTagSearchTags();
	// }, []);

	useEffect(() => {
		const currentColumns = getColumns(regionCodes);
		setColumns(currentColumns);
	}, [currentTabId]);

	useEffect(() => {
		const currentAssessTagSearchAttributes = getTagSearchAttributes(tagSearchOptions, 'assess');
		setAssessTagSearchAttributes(currentAssessTagSearchAttributes);

		const currentIgnoreTagSearchAttributes = getTagSearchAttributes(tagSearchOptions, 'ignore');
		setIgnoreTagSearchAttributes(currentIgnoreTagSearchAttributes);
	}, [currentTabId, tagSearchOptions, assessTagValueOptions, ignoreTagValueOptions]);

	useEffect(() => {
		getIgnoredStatus();
	}, [strategyAssesses, strategyIgnores]);

	useEffect(() => {
		getInstances();
	}, [strategyAssesses, strategyIgnores, assessStatus, ignoreStatus]);

	useEffect(() => {
		switch (currentTabId) {
			case 'assess':
				setAssessRecords(assessInstances);
				if (assessInstances.length > 0) {
					setAssessTableStatus('none');
				} else {
					setAssessTableStatus('empty');
				}

				break;
			case 'ignore':
				setIgnoreRecords(ignoreInstances);
				if (ignoreInstances.length > 0) {
					setIgnoreTableStatus('none');
				} else {
					setIgnoreTableStatus('empty');
				}

				break;
		}
	}, [currentTabId, assessInstances, ignoreInstances]);

	return (
		<>
			<div className="intlc-assessment-tabcoll__block">
				<Tabs
					tabs={resourceTabs}
					activeId={currentTabId}
					onActive={(tab, evt) => {
						setCurrentTabId(tab.id);
					}}
				>
					{resourceTabs.map((tab) => (
						<TabPanel key={tab.id} id={tab.id}></TabPanel>
					))}
				</Tabs>
			</div>
			<div className="intlc-assessment-tabcoll__table">
				{currentTabId === 'assess' ? (
					<>
						<Table.ActionPanel>
							<Justify
								left={
									<>
										<Button
											type="primary"
											disabled={
												!assessSelectedKeys.length ||
												(assessSelectedKeys.length &&
													assessSelectedKeys.some((instanceId) => assessStatus[instanceId]))
											}
											onClick={() => {
												modifyInstances(strategyId, 'assess', 'add');
											}}
										>
											<span>忽略</span>
										</Button>
										<Button
											disabled={
												!assessSelectedKeys.length ||
												(assessSelectedKeys.length &&
													assessSelectedKeys.some((instanceId) => !assessStatus[instanceId]))
											}
											onClick={() => {
												modifyInstances(strategyId, 'assess', 'delete');
											}}
										>
											<span>添加</span>
										</Button>
									</>
								}
								// right={
								// 	<SearchBox
								// 		size="l"
								// 		onSearch={(value) => {
								// 			setResourceInputValue(value)

								// 			if (currentTabId === 'assess') {
								// 				setAssessTableStatus('loading')
								// 			} else if (currentTabId === 'ignore') {
								// 				setIgnoreTableStatus('loading')
								// 			}

								// 			// 更新输入值后从第一页开始展示
								// 			setAssessPageIndex(1)
								// 			setIgnorePageIndex(1)
								// 			onChangeAssesses(strategyId, value, 0, assessPageSize)
								// 			onChangeIgnores(strategyId, value, 0, ignorePageSize)
								// 		}}
								// 		placeholder={t('输入资源关键词搜索')}
								// 	/>
								// }
								right={
									<TagSearchBox
										attributes={assessTagSearchAttributes}
										minWidth={420}
										value={assessTagSearchValue}
										onChange={(tagSearchValue) => {
											let currentTagSearchValue = _.cloneDeep(tagSearchValue);

											// 如果存在非法输入，自动替换为实例ID/名称
											if (tagSearchValue.some((v) => !v.attr)) {
												// 实例ID/名称对应的标签属性
												const instanceIdAttribute = assessTagSearchAttributes[0] || {};
												const instanceIdAttributeKey = instanceIdAttribute.key || '';

												_.remove(currentTagSearchValue, (v) => {
													return v.attr && v.attr.key === instanceIdAttributeKey;
												});

												currentTagSearchValue.forEach((v) => {
													if (!v.attr) {
														v.attr = _.cloneDeep(instanceIdAttribute);
													}
												});
											}

											setAssessTagSearchValue(currentTagSearchValue);

											// 获取标签键对应的标签值选项
											const currentTagKeyOption =
												currentTagSearchValue.find((t) => t.attr.key === 'TagKey') || {};

											if (
												currentTagKeyOption &&
												currentTagKeyOption.values &&
												currentTagKeyOption.values[0] &&
												currentTagKeyOption.values[0].key
											) {
												const tagOptions =
													tagSearchOptions.find(
														(t) => t.TagKey === currentTagKeyOption.values[0].key
													) || {};

												const tagValueOptions = tagOptions.TagValues.length
													? tagOptions.TagValues.map((value) => {
															return {
																key: value || '',
																name: value || '',
															};
													  })
													: [];

												setAssessTagValueOptions(tagValueOptions);
											} else {
												setAssessTagValueOptions([]);
											}

											// 自动触发搜索
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter =
												getConvertTagSearchValue2Filter(currentTagSearchValue);

											// 更新输入值后从第一页开始展示
											setAssessPageIndex(1);
											onChangeAssesses(strategyId, currentFilter, 0, assessPageSize);
										}}
										onSearchButtonClick={(e, value) => {
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter = getConvertTagSearchValue2Filter(value);

											// 更新输入值后从第一页开始展示
											setAssessPageIndex(1);
											onChangeAssesses(strategyId, currentFilter, 0, assessPageSize);
										}}
									/>
								}
							/>
						</Table.ActionPanel>
						<Table
							bordered={true}
							records={[...assessRecords].sort(sortable.comparer(assessSorts)) || []}
							recordKey="Id"
							columns={columns || []}
							topTip={
								assessTableStatus !== 'none' && (
									<StatusTip
										// @ts-ignore
										status={assessTableStatus}
										onClear={() => setAssessTableStatus('loading')}
										onRetry={() => setAssessTableStatus('loading')}
									/>
								)
							}
							addons={[
								// @ts-ignore
								pageable({
									recordCount: assessTotalCount,
									pageIndex: assessPageIndex,
									pageSize: assessPageSize,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setAssessTableStatus('loading');
										setAssessPageIndex(pageIndex);
										setAssessPageSize(pageSize);
										setAssessSelectedKeys([]);

										const currentFilter = getConvertTagSearchValue2Filter(assessTagSearchValue);

										onChangeAssesses(
											strategyId,
											currentFilter,
											(pageIndex - 1) * pageSize,
											pageSize
										);
									},
								}),
								selectable({
									value: assessSelectedKeys,
									onChange: (keys, context) => {
										setAssessSelectedKeys(keys);
									},
								}),
							]}
						/>
					</>
				) : (
					<>
						<Table.ActionPanel>
							<Justify
								left={
									<>
										<Button
											type="primary"
											disabled={
												!ignoreSelectedKeys.length ||
												(ignoreSelectedKeys.length &&
													ignoreSelectedKeys.some((instanceId) => !ignoreStatus[instanceId]))
											}
											onClick={() => modifyInstances(strategyId, 'ignore', 'delete')}
										>
											<span>添加</span>
										</Button>
										<Button
											disabled={
												!ignoreSelectedKeys.length ||
												(ignoreSelectedKeys.length &&
													ignoreSelectedKeys.some((instanceId) => ignoreStatus[instanceId]))
											}
											onClick={() => modifyInstances(strategyId, 'ignore', 'add')}
										>
											<span>忽略</span>
										</Button>
									</>
								}
								right={
									<TagSearchBox
										attributes={ignoreTagSearchAttributes}
										minWidth={420}
										value={ignoreTagSearchValue}
										onChange={(tagSearchValue) => {
											let currentTagSearchValue = _.cloneDeep(tagSearchValue);

											// 如果存在非法输入，自动替换为实例ID/名称
											if (tagSearchValue.some((v) => !v.attr)) {
												// 实例ID/名称对应的标签属性
												const instanceIdAttribute = ignoreTagSearchAttributes[0] || {};
												const instanceIdAttributeKey = instanceIdAttribute.key || '';

												_.remove(currentTagSearchValue, (v) => {
													return v.attr && v.attr.key === instanceIdAttributeKey;
												});

												currentTagSearchValue.forEach((v) => {
													if (!v.attr) {
														v.attr = _.cloneDeep(instanceIdAttribute);
													}
												});
											}

											setIgnoreTagSearchValue(currentTagSearchValue);

											// 获取标签键对应的标签值选项
											const currentTagKeyOption =
												currentTagSearchValue.find((t) => t.attr.key === 'TagKey') || {};

											if (
												currentTagKeyOption &&
												currentTagKeyOption.values &&
												currentTagKeyOption.values[0] &&
												currentTagKeyOption.values[0].key
											) {
												const tagOptions =
													tagSearchOptions.find(
														(t) => t.TagKey === currentTagKeyOption.values[0].key
													) || {};

												const tagValueOptions = tagOptions.TagValues.length
													? tagOptions.TagValues.map((value) => {
															return {
																key: value || '',
																name: value || '',
															};
													  })
													: [];

												setIgnoreTagValueOptions(tagValueOptions);
											} else {
												setIgnoreTagValueOptions([]);
											}

											// 自动触发搜索
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter =
												getConvertTagSearchValue2Filter(currentTagSearchValue);

											// 更新输入值后从第一页开始展示
											setIgnorePageIndex(1);
											onChangeIgnores(strategyId, currentFilter, 0, ignorePageSize);
										}}
										onSearchButtonClick={(e, value) => {
											if (currentTabId === 'assess') {
												setAssessTableStatus('loading');
											} else if (currentTabId === 'ignore') {
												setIgnoreTableStatus('loading');
											}

											const currentFilter = getConvertTagSearchValue2Filter(value);

											// 更新输入值后从第一页开始展示
											setIgnorePageIndex(1);
											onChangeIgnores(strategyId, currentFilter, 0, ignorePageSize);
										}}
									/>
								}
							/>
						</Table.ActionPanel>
						<Table
							bordered={true}
							records={[...ignoreRecords].sort(sortable.comparer(ignoreSorts)) || []}
							recordKey="Id"
							columns={columns || []}
							topTip={
								ignoreTableStatus !== 'none' && (
									<StatusTip
										// @ts-ignore
										status={ignoreTableStatus}
										onClear={() => setIgnoreTableStatus('loading')}
										onRetry={() => setIgnoreTableStatus('loading')}
									/>
								)
							}
							addons={[
								// @ts-ignore
								pageable({
									recordCount: ignoreTotalCount,
									pageIndex: ignorePageIndex,
									pageSize: ignorePageSize,
									onPagingChange: ({ pageIndex, pageSize }) => {
										setIgnoreTableStatus('loading');
										setIgnorePageIndex(pageIndex);
										setIgnorePageSize(pageSize);
										setIgnoreSelectedKeys([]);

										const currentFilter = getConvertTagSearchValue2Filter(ignoreTagSearchValue);

										onChangeIgnores(
											strategyId,
											currentFilter,
											(pageIndex - 1) * pageSize,
											pageSize
										);
									},
								}),
								selectable({
									value: ignoreSelectedKeys,
									onChange: (keys, context) => {
										setIgnoreSelectedKeys(keys);
									},
								}),
							]}
						/>
					</>
				)}
			</div>
		</>
	);
}
