import React, { useState, useContext } from 'react';
// import { t, Trans } from '@tencent/tea-app/i18n';
import { ConfigCondition, ResultCondition } from '@src/types/advisor/estimate1';
import { Icon } from '@tencent/tea-component/lib/icon';
import { Text } from '@tencent/tea-component/lib/text';

interface Prop {
	active: boolean;
	taskCompleted: boolean;
	strategyStatus: string;
	strategyLevel: number;
}

// 警告条件主题颜色映射
const conditionThemeConfig = {
	'-1': 'weak',
	0: 'success',
	1: 'warning',
	2: 'warning',
	3: 'danger',
};

export function StatusIcon({ active, taskCompleted, strategyStatus, strategyLevel }: Prop) {
	return (
		<>
			{!taskCompleted || strategyStatus === 'init' || strategyStatus === 'running' ? (
				<div className="intlc-assessment-tabs-tag__item--loading">
					<div className="intlc-assessment-tabs-tag__loadings"></div>
				</div>
			) : taskCompleted && (strategyStatus === 'fail' || !strategyStatus) ? (
				<Text
					style={{ backgroundColor: '#bbb' }}
					verticalAlign="top"
					className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--item"
				></Text>
			) : (
				<span className="intlc-assessment-tabs-tag__item">
					{active ? (
						<div className="intlc-assessment-tabs-tag__foldings arrowdown"></div>
					) : (
						<div className="intlc-assessment-tabs-tag__foldings arrowright"></div>
					)}
					{strategyLevel >= 0 ? (
						<Text
							bgTheme={conditionThemeConfig[strategyLevel.toString()]}
							verticalAlign="top"
							className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--item"
						></Text>
					) : (
						<Text
							style={{ backgroundColor: '#bbb' }}
							verticalAlign="top"
							className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--item"
						></Text>
					)}
				</span>
			)}
		</>
	);
}
