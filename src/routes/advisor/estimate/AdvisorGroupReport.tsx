import React, { useState, useContext, useEffect, useRef, useMemo } from 'react'
import { app } from '@tencent/tea-app'
import { t, Trans } from '@tencent/tea-app/i18n'
import { Text } from '@tencent/tea-component/lib/text'
import { Icon } from '@tencent/tea-component/lib/icon'
import { Modal } from '@tencent/tea-component/lib/modal'
import { Button } from '@tencent/tea-component/lib/button'
import { Segment } from '@tencent/tea-component/lib/segment'
import { Form } from '@tencent/tea-component/lib/form'
import { Bubble, Input, Radio } from '@tencent/tea-component'
import { Checkbox, PopConfirm } from '@tencent/tea-component'
import { SelectMultiple } from '@tencent/tea-component'
import { message } from '@tencent/tea-component'
import { TagSelect } from '@tencent/tea-component/lib/tagselect'
import { StatusTip } from '@tencent/tea-component/lib/tips'
import { Tag } from '@tencent/tea-component/lib/tag'
import { Table } from '@tencent/tea-component/lib/table'
import { Select } from '@tencent/tea-component/lib/select'
import { Card } from '@tencent/tea-component/lib/card'
import { describeReportTemplate, createReportTemplate, modifyReportTemplate, deleteReportTemplate, describeReportTemplateList } from '@src/api/advisor/estimate';

import _ from 'lodash'
import {
	DownLoadReportTplTag,
	DownLoadReportTplTagSon
} from '@src/types/advisor/estimate1';
import { Template } from 'webpack'

const { autotip, pageable, scrollable, filterable } = Table.addons

// 策略组ID映射
const tabIdMap = new Map([
	['overview', -1],
	['security', 1],
	['architecture', 2],
	['resource', 3],
	['cost', 4],
	['performance', 5],
]);

/*
const languageOptions = [
	{ value: "zh-CN", text: "中文", tooltip: "中文" },
	{ value: "en-US", text: "英文", tooltip: "英文" },
]*/

export function AdvisorGroupReport({ id, AppId, type, env, currentTaskId, TemplateOptions, Products, ProductsOptions, Groups, AllGroups, reportStatusQueue, onChangeReportAsync, getTemplatesList, downStatus, cosUrl }) {
	const [showLoading, setShowLoading] = useState(false);
	// 当前模板
	const [CurrentTemplate, setCurrentTemplate] = useState('0')
	// 当前模板名称
	const [CurrentTemplateName, setCurrentTemplateName] = useState('')
	// 新模板名称
	const [NewTemplateName, setNewTemplateName] = useState('')
	// 新增模板界面可视
	const [addTemplateVisiable, setAddTemplateVisiable] = useState(false)
	// 编辑模板界面可视
	const [editTemplateVisiable, setEditTemplateVisiable] = useState(false)
	// 删除模板界面可视
	const [deleteTemplateVisiable, setDeleteTemplateVisiable] = useState(false)
	// 勾选模板类型
	const [generateReportType, setGenerateReportType] = useState("no")
	// 系统模板列表--下拉框
	const [systemTemplateOptions, setSystemTemplateOptions] = useState([])
	// 初始值:下拉框,标签
	const [selectDefaultValue, setSelectDefaultValue] = useState('0')
	const [tagDefaultValue, setTagDefaultValue] = useState([])

	// 自定义产品列表
	const [CurrentProducts, setCurrentProducts] = useState<Array<string>>([])
	// 自定义维度列表
	const [CurrentGroups, setCurrentGroups] = useState([])
	// 自定义策略列表
	const [CurrentStrategyIds, setCurrentStrategyIds] = useState([])
	// 自定义维度列表
	const [CurrentLanguage, setCurrentLanguage] = useState('')
	// 模板产品选择-报错提示
	const [VisableProducts, setVisableProducts] = useState(false)
	// 模板维度选择-报错提示
	const [VisableGroups, setVisableGroups] = useState(false)
	// 模板语言选择-报错提示
	const [VisableLanguage, setVisableLanguage] = useState(false)
	// 自定义标签列表
	const [CurrentTags, setCurrentTags] = useState<Array<DownLoadReportTplTag>>([])
	// 标签列表状态
	const [CurrentTagsStatus, setCurrentTagsStatus] = useState('none')
	// 标签编辑框弹窗
	const [ShowEditTagModal, setShowEditTagModal] = useState(false)
	// 标签列表分页码
	const [pageIndex, setPageIndex] = useState(1)
	// 当前标签键/标签值选项
	const [tagSearchOptions, setTagSearchOptions] = useState([])
	// 当前输入中的标签键
	const [inputTagKey, setInputTagKey] = useState('')
	// 当前输入中的标签值
	const [inputTagValues, setInputTagValues] = useState([])
	// 标签键列表
	const [tagKeys, setTagKeys] = useState<Array<string>>([])
	// 标签键拉取loading 
	const [tagKeysLoading, setTagKeysLoading] = useState(false)
	// 标签值列表
	const [tagValues, setTagValues] = useState<Array<DownLoadReportTplTagSon>>([])
	// 标签值拉取loading
	const [tagValuesLoading, setTagValuesLoading] = useState(false)
	// 语言选项
	const [languageOptions, setLanguageOptions] = useState([])

	const [editVisible, setEditVisible] = useState(false);
	const [reportTypeVisible, setReportTypeVisible] = useState(false);
	const [reportType, setReportType] = useState(1);
	const currentReport1 = reportStatusQueue.find((report) => tabIdMap.get(id) === report.Id && report.Type === type);
	const currentReport = currentReport1 ? JSON.parse(JSON.stringify(currentReport1)) : null;  //深度copy，避免useEffect不刷新
	const lastReport = useRef({ RequestId: '' }); //利用useRef,存放上一次数据，用来判断是否发生变化


	// 打开自定义生成报告弹窗
	const initEditTpl = async () => {
		//初始化内容
		if (CurrentTemplate === '0') {
			setCurrentGroups(AllGroups)  //维度默认全选
			setCurrentProducts(Products) //产品默认全选
			setCurrentTags([])
		} else {
			try {
				const res = await describeReportTemplate({
					AppId: AppId,
					TemplateId: parseInt(CurrentTemplate),
					Type: generateReportType === "system" ? "system" : "",
				})
				if (res.Error) {
					let msg = res.Error.Message || "未知错误"
					message.error({ content: msg });
					return
				} else {
					// 设置模板对应的巡检项信息(类别Group,产品Product,标签Tag)，用于生成对应报告文件。
					if (generateReportType === "system") {
						// 系统模板返回指定 StrategyIDs，不返回的 GroupIDs/Products/Tags 默认全选。
						setCurrentGroups(AllGroups)
						setCurrentProducts(Products)
						setCurrentTags(res.Tags)
						setCurrentStrategyIds(res.StrategyIDs)
					} else {
						// 自定义模板返回指定 GroupIDs,Products,Tags
						let tmp = []
						res.GroupIDs.forEach(i => {
							tmp.push(i.toString())
						})
						setCurrentGroups(tmp)
						setCurrentProducts(res.Products)
						setCurrentTags(res.Tags)
					}
					setCurrentTemplateName(res.Name)
				}
			} catch (err) {
				const msg = err.msg || err.toString() || "未知错误"
				message.error({ content: msg });
			}
		}

		//报告语言取决于站点名称，国际站默认为英文
		if (localStorage.getItem('site') === 'sinapore') {
			setCurrentLanguage('en-US')
			setLanguageOptions([
				{ value: "zh-CN", text: "中文", tooltip: "中文" },
				{ value: "en-US", text: "英文", tooltip: "英文" },
			]
			)
		} else {
			setCurrentLanguage('zh-CN')
			setLanguageOptions([
				{ value: "zh-CN", text: "中文", tooltip: "中文" }
			]
			)
		}

		setVisableGroups(false)
		setVisableProducts(false)
		setVisableLanguage(false)
	}

	useEffect(() => {
		getSystemReportTemplateList()
	}, [])

	useEffect(() => {
		initEditTpl();
	}, [CurrentTemplate]);

	useEffect(() => {
		setSelectDefaultValue('0')
		setCurrentTemplate('0')
		setCurrentTags([])
	}, [generateReportType])

	/*
	useEffect(() => {
		if (currentReport && lastReport.current.RequestId && lastReport.current.RequestId === currentReport.RequestId) {
			//前后两次调用，数据一致，则不变化
			return;
		} else {
			if (currentReport && (currentReport.TaskStatus === 'success' || currentReport.TaskStatus === 'failed')) {
				setShowLoading(false);
			}
		}
		if (currentReport) {
			lastReport.current = currentReport;
		}

	}, [currentReport]);*/

	useEffect(() => {
		if (downStatus === 'success' || downStatus === 'failed') {
			setShowLoading(false);
		}
	}, [downStatus]);

	// 当前可选标签键选项
	const tagKeyOptions = useMemo(() => {
		const ignoreTagsKeys = (CurrentTags || []).map((tag) => tag.TagKey)
		const currentTagKeyOptions = tagKeys
			.filter((key) => !ignoreTagsKeys.includes(key))
			.map((key) => {
				return {
					value: key || '',
					text: key || '',
				}
			})
		return currentTagKeyOptions
	}, [tagKeys, CurrentTags])

	// 当前可选标签值选项
	const tagValueOptions = useMemo(() => {
		const currentTagValueOptions = tagValues.map((tag) => {
			return {
				value: tag.TagValue || '',
				text: tag.TagValue || '',
			}
		})
		return currentTagValueOptions
	}, [tagValues])

	// 当前是否有正在编辑中的标签
	const isTagEditing = useMemo(() => {
		return (CurrentTags || []).some((tag) => tag.editingKey || tag.editingValues)
	}, [CurrentTags])

	// 编辑标签
	const editTag = (TagKey) => {
		const newCurrentTags = _.cloneDeep(CurrentTags)
		const currentEditingTag = newCurrentTags.find((tag) => tag.TagKey === TagKey)
		currentEditingTag.editingValues = true
		//调用函数，更新标签值 
		// getAllTagValues(TagKey);
		setCurrentTags(newCurrentTags)
	}
	// 删除标签
	const deleteTag = async (TagKey) => {
		let newCurrentTags = CurrentTags.filter(i => { if (i.TagKey != TagKey) { return i } })
		setCurrentTags(newCurrentTags)
	}
	// 新增标签
	const addTag = () => {
		if (isTagEditing) {
			return;
		}
		const currentTag = {
			TagKey: '',
			TagValues: [],
			editingKey: true,
			editingValues: true,
		}
		let l = JSON.parse(JSON.stringify(CurrentTags))
		l.unshift(currentTag)
		setInputTagKey('')
		setInputTagValues([])
		setCurrentTags(l)
		setPageIndex(1)
	}
	// 取消编辑标签
	const cancelTag = (TagKey) => {
		//如果是空的，则删除
		if (TagKey === '') {
			setCurrentTags(CurrentTags.filter(i => { if (i.TagKey != '') { return i } }))
		} else {
			const newCurrentTags = _.cloneDeep(CurrentTags)
			const currentEditingTag = newCurrentTags.find((tag) => tag.TagKey === TagKey)
			currentEditingTag.editingValues = false
			currentEditingTag.editingKey = false
			setCurrentTags(newCurrentTags)
		}
	}
	// 保存标签
	const saveTag = (TagKey) => {
		if (inputTagKey === '' && TagKey === '') {
			message.error({ content: "未指定标签键" })
			return
		}
		const newCurrentTags = _.cloneDeep(CurrentTags)
		const currentEditingTag = newCurrentTags.find((tag) => tag.TagKey === TagKey)
		if (currentEditingTag.editingKey && inputTagKey) {
			currentEditingTag.TagKey = inputTagKey
		}
		currentEditingTag.editingValues = false
		currentEditingTag.editingKey = false
		currentEditingTag.TagValues = inputTagValues.length > 0 ? inputTagValues : tagValueOptions.map((opt) => opt.value)
		setCurrentTags(newCurrentTags)
	}

	// 新增模板
	const addTemplate = async () => {
		try {
			let tmpGroups = []
			CurrentGroups.forEach(i => {
				tmpGroups.push(parseInt(i))
			})
			const res = await createReportTemplate({
				AppId: AppId,
				Name: NewTemplateName,
				Products: CurrentProducts,
				GroupIds: tmpGroups,
				Tags: CurrentTags,
			})
			if (res.Error) {
				let msg = res.Error.Message
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });
				return
			} else {
				// 成功，刷新模板下拉框，并隐藏新增模板界面
				setAddTemplateVisiable(false);
				getTemplatesList(AppId);
				message.success({ content: "提示：新增模板成功！" });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });
		}
	}

	const modifyTemplate = async () => {
		try {
			let tmpGroups = []
			CurrentGroups.forEach(i => {
				tmpGroups.push(parseInt(i))
			})
			const res = await modifyReportTemplate({
				AppId: AppId,
				TemplateId: parseInt(CurrentTemplate),
				Name: NewTemplateName,
				Products: CurrentProducts,
				GroupIds: tmpGroups,
				Tags: CurrentTags,
			})
			if (res.Error) {
				let msg = res.Error.Message
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });
				return
			} else {
				// 成功，刷新模板下拉框，并隐藏修改模板界面
				setEditTemplateVisiable(false);
				getTemplatesList(AppId);
				message.success({ content: "提示：修改模板成功！" });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });
		}
	}

	const deleteTemplate = async () => {
		try {
			const res = await deleteReportTemplate({
				AppId: AppId,
				TemplateId: parseInt(CurrentTemplate),
			})
			if (res.Error) {
				let msg = res.Error.Message
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });
				return
			} else {
				// 成功，刷新模板下拉框，并隐藏删除模板界面
				setDeleteTemplateVisiable(false);
				getTemplatesList(AppId);
				setCurrentTemplate('0');
				message.success({ content: "提示：删除模板成功！" });
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });
		}
	}

	// 获取系统模板列表信息
	const getSystemReportTemplateList = async () => {
		try {
			const res = await describeReportTemplateList({
				AppId: AppId, //入参APPID在拉取系统模板时候没用上，任意值返回结果相同
				Type: "system"
			})
			if (res.Error) {
				let msg = res.Error.Message
				message.error({ content: msg });
				return
			} else {
				// let tmpTemplateOptions = [{ value: '0', text: '不使用模板' }]
				// if (res.TemplateList) {
				// 	res.TemplateList.map((item) => {
				// 		tmpTemplateOptions.push({ value: item.Id, text: item.Name })
				// 	})
				// }
				// setTemplateOptions(tmpTemplateOptions)// 模板下拉框*/
				let tmp = []
				if (res.TemplateList) {
					res.TemplateList.map((item) => {
						tmp.push({ value: item.Id, text: item.Name })
					})
				}
				setSystemTemplateOptions(tmp)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			message.error({ content: msg });
		}
	}

	return (
		<>
			{(downStatus === '' || downStatus === 'failed') && !showLoading && (
				<Text
					style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer', marginLeft: 30 }}
					onClick={(e) => {
						initEditTpl();
						setEditVisible(true);
					}}
				>
					<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" style={{ marginRight: 5 }}>
						<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#006cff" />
					</svg>
					<Text style={{ fontSize: 12, color: '#006cff' }}>
						<span>生成报告</span>
					</Text>
				</Text>
			)}
			<Modal visible={editVisible} size={"l"} onClose={() => setEditVisible(false)} caption="自定义生成报告">
				<Modal.Body>
					<div style={{ marginRight: 5 }} >
						<section>
							<Radio.Group value={generateReportType} onChange={value => setGenerateReportType(value)} layout="inline">
								<Radio name="no">不使用模板</Radio>
								<Radio name="self">使用自定义模板</Radio>
								<Radio name="system">使用系统模板</Radio>
							</Radio.Group>
						</section>
					</div>

					<hr />
					<div>
						<Form style={{ marginTop: 20 }} layout={"default"}>
							<Form.Item label="报告模板">
								<Select
									// style={{ marginLeft: 10 }}
									searchable
									boxSizeSync
									size="m"
									type="simulate"
									appearance="button"
									defaultValue={selectDefaultValue}
									value={CurrentTemplate}
									onChange={(value) => { setCurrentTemplate(value) }}
									options={
										generateReportType === "self" ? TemplateOptions : (generateReportType === "system" ? systemTemplateOptions : [{ value: '0', text: '不使用模板' }])
									}
									disabled={generateReportType === "no" ? true : false}
								/>
							</Form.Item>
							{generateReportType !== "system" && <Form.Item label="产品">
								<Bubble error visible={VisableProducts} content={"请至少选择一个产品"}>
									<SelectMultiple
										value={CurrentProducts}
										onChange={(value) => { setCurrentProducts(value); if (value.length) { setVisableProducts(false) } else { setVisableProducts(true) } }}
										options={ProductsOptions}
										allOption={{ value: '', text: 'All' }}
										searchable={true}
									/>
								</Bubble>
							</Form.Item>}
							{generateReportType !== "system" && <Form.Item label="维度">
								<Bubble error visible={VisableGroups} content={"请至少选择一个维度"}>
									<div style={{ marginRight: 5 }} >
										<Checkbox.Group>
											<Checkbox
												value={CurrentGroups.length === Groups.length}
												onChange={(value) => { if (value) { setCurrentGroups(AllGroups); setVisableGroups(false) } else { setCurrentGroups([]); setVisableGroups(true) } }}
												name={''}>{'全选'}</Checkbox>
										</Checkbox.Group>
									</div>
									<div style={{ marginTop: 10 }}>
										<Checkbox.Group value={CurrentGroups} onChange={(value) => {
											setCurrentGroups(value)
											if (value.length) {
												setVisableGroups(false)
											} else (
												setVisableGroups(true)
											)
										}} >
											{
												Groups.map((i, index) => (
													<Checkbox name={i.Id.toString()} key={index}>{i.GroupName}</Checkbox>
												))
											}
										</Checkbox.Group>
									</div>
								</Bubble>
							</Form.Item>}
							<Form.Item label="标签">
								<div style={{ padding: 1, fontSize: 12 }}>
									{
										CurrentTags.filter(i => { if (i.TagKey != '') { return i } }).length ? (<div style={{ float: 'left' }}>
											{CurrentTags.filter(i => { if (i.TagKey != '') { return i } }).map((tag) => {
												let str = tag.TagKey + ':' + tag.TagValues.join(',')
												return (
													<div key={tag.TagKey} >
														<Text theme="label" style={{ display: 'inline-block', marginRight: 10, marginBottom: 5 }}>
															{str}
														</Text>
														<Button type="link" onClick={() => { setShowEditTagModal(true) }} style={{ marginLeft: 10 }}>{"编辑"}</Button>
														<Button type="link" onClick={() => { deleteTag(tag.TagKey) }} style={{ marginLeft: 10 }}>{"删除"}</Button>
													</div>
												)
											})}
										</div>) : (<>
											{"尚未输入标签，默认全部资源"}
											<Button type="link" onClick={() => { addTag(); setShowEditTagModal(true) }} style={{ marginLeft: 10 }}>
												添加标签
											</Button>
										</>)}
								</div>
							</Form.Item>
							<Form.Item label="报告语言">
								<Bubble error visible={VisableLanguage} content={"请至少选择一种语言"}>
									<Select
										type="native"
										size="m"
										options={languageOptions}
										value={CurrentLanguage}
										defaultValue={CurrentLanguage}
										onChange={(value) => { if (value) { setCurrentLanguage(value); setVisableLanguage(false) } else { setCurrentLanguage(''); setVisableLanguage(true) } }}
										placeholder="请选择报告语言"
									/>
								</Bubble>
							</Form.Item>
						</Form>
					</div>
				</Modal.Body>
				<Modal.Footer>
					<Button
						type="primary"
						onClick={(e) => {
							setShowLoading(true);
							onChangeReportAsync(currentTaskId, env, type, CurrentLanguage, CurrentProducts, CurrentGroups, CurrentStrategyIds, CurrentTags);
							setEditVisible(false);
						}}
					>
						生成报告
					</Button>

					{generateReportType !== "system" && <Button
						type="weak"
						onClick={(e) => {
							setAddTemplateVisiable(true);
							setNewTemplateName('');
						}}
					>
						保存为新模版
					</Button>}
					<Modal visible={addTemplateVisiable} onClose={() => setAddTemplateVisiable(false)} caption="模板新增">
						<Modal.Body>
							<div style={{ fontSize: 16 }}>
								<Form style={{ marginTop: 20 }} layout={"default"}>
									<Form.Item label="保存当前选项为新的报告模板：">
										<Input
											size='m'
											value={NewTemplateName}
											onChange={(value, context) => {
												setNewTemplateName(value);
											}}
											placeholder="请输入新的模板名称"
										/>
									</Form.Item>
								</Form>
							</div>
						</Modal.Body>
						<Modal.Footer>
							<Button
								type="primary"
								onClick={(e) => {
									addTemplate();
									// setAddTemplateVisiable(false);
								}}
							>
								确定
							</Button>
							<Button type="weak" onClick={() => setAddTemplateVisiable(false)}>
								取消
							</Button>
						</Modal.Footer>
					</Modal>

					{generateReportType !== "system" && <Button
						type="weak"
						onClick={(e) => {
							setEditTemplateVisiable(true);
							setNewTemplateName(CurrentTemplateName);
						}}
						disabled={CurrentTemplate === '0'}
					>
						覆盖当前模板
					</Button>}
					<Modal visible={editTemplateVisiable} onClose={() => setEditTemplateVisiable(false)} caption="模板编辑">
						<Modal.Body>
							<div style={{ fontSize: 16, color: 'red' }}>
								<b>确定要将当前产品、维度、标签的选项覆盖至模板：{CurrentTemplateName}？</b>
								<br />
								<Form style={{ marginTop: 20 }} layout={"default"}>
									<Form.Item label="覆盖后的模板名称（可选）：">
										<Input
											size='m'
											value={NewTemplateName}
											onChange={(value, context) => {
												setNewTemplateName(value);
											}}
											placeholder="请输入覆盖后的模板名称"
										/>
									</Form.Item>
								</Form>
							</div>
						</Modal.Body>
						<Modal.Footer>
							<Button
								type="primary"
								onClick={(e) => {
									// setEditTemplateVisiable(false);
									modifyTemplate();
								}}
							>
								确定
							</Button>
							<Button type="weak" onClick={() => setEditTemplateVisiable(false)}>
								取消
							</Button>
						</Modal.Footer>
					</Modal>

					{generateReportType !== "system" && <Button
						type="weak"
						onClick={(e) => {
							setDeleteTemplateVisiable(true);
						}}
						disabled={CurrentTemplate === '0'}
					>
						删除当前模板
					</Button>}
					<Modal visible={deleteTemplateVisiable} onClose={() => setDeleteTemplateVisiable(false)} caption="模板删除">
						<Modal.Body>
							<div style={{ fontSize: 16, color: 'red' }}>
								<b>确认是否删除模板：{CurrentTemplateName}？</b>
							</div>
						</Modal.Body>
						<Modal.Footer>
							<Button
								type="primary"
								onClick={(e) => {
									// setDeleteTemplateVisiable(false);
									deleteTemplate();
								}}
							>
								确定
							</Button>
							<Button type="weak" onClick={() => setDeleteTemplateVisiable(false)}>
								取消
							</Button>
						</Modal.Footer>
					</Modal>

					<Button type="weak" onClick={() => setEditVisible(false)}>
						取消
					</Button>

				</Modal.Footer>
			</Modal>

			{/* 标签编辑框 */}
			<Modal visible={ShowEditTagModal} size={"l"} onClose={() => { setShowEditTagModal(false) }}>
				<Modal.Body>
					<Card.Body>
						<Table
							bordered
							addons={[
								pageable({
									pageIndex: pageIndex,
									onPagingChange: (query) => { setPageIndex(query.pageIndex) },
								}),]}
							records={CurrentTags}
							recordKey="TagKey"
							columns={[
								{
									key: 'TagKey',
									header: '标签键',
									render: (tag) => (
										<>
											{tag.editingKey && (
												<Input
													size="full"
													value={inputTagKey}
													onChange={(value) => {
														setInputTagKey(value);//选中标签键
														setInputTagValues([]);//清空原来的标签值
													}}
													placeholder="请输入标签键"
													disabled={false}
													readonly={false}
												/>
											)}

											{!tag.editingKey && <>{tag.TagKey}</>}
										</>
									),
								},
								{
									key: 'TagValues',
									header: '标签值',
									render: (tag) => (
										<>
											{tag.editingValues && (
												<TagSelect
													// optionsOnly
													// options={[{ text: "", value: "" }]}
													value={inputTagValues}
													onChange={(value) => setInputTagValues(value)}
													// tips={tagValuesLoading && <StatusTip status="loading"></StatusTip>}
													placeholder="请输入标签值，以回车结束"
												/>
											)}
											{!tag.editingValues && (
												<>
													{tagSearchOptions.find((opt) => opt.TagKey === tag.TagKey) &&
														tagSearchOptions.find((opt) => opt.TagKey === tag.TagKey).TagValues.length ===
														tag.TagValues.length ? (
														<Text>
															全部
														</Text>
													) : (
														tag.TagValues &&
														tag.TagValues.map((value) => {
															return <Tag>{value}</Tag>
														})
													)}
												</>
											)}
										</>
									),
								},
								{
									key: 'status',
									width: '8vw',
									header: '操作',
									render: (tag) => (
										<div className="intlc-evaluation-settin_table-link">
											{tag.editingKey || tag.editingValues ? (
												<>
													<Button
														type="link"
														onClick={() => { saveTag(tag.TagKey) }}
													>
														保存
													</Button>
													<Button
														type="link"
														onClick={() => { cancelTag(tag.TagKey) }}
													>
														取消
													</Button>
												</>
											) : (
												<>
													<Button type="link" disabled={isTagEditing} onClick={() => editTag(tag.TagKey)}>
														编辑
													</Button>
													<Button type="link" disabled={isTagEditing} onClick={() => deleteTag(tag.TagKey)}>
														删除
													</Button>
												</>
											)}
										</div>
									),
								},
							]}
							topTip={
								CurrentTagsStatus !== 'none' && (
									<StatusTip
										// @ts-ignore
										status={CurrentTagsStatus}
									/>
								)
							}
						/>
						<div className="intlc-evaluation-settin_table-add">
							<div
								className="intlc-evaluation-settin_table-tag"
								style={isTagEditing ? { cursor: 'not-allowed' } : {}}
								onClick={() => {
									if (isTagEditing) {
										return
									}
									addTag()
								}}
							>
								<Icon type="plus" />
								标签
							</div>
						</div>
					</Card.Body>
				</Modal.Body>
				<Modal.Footer>
					<div>
						<Button style={{ marginRight: 5 }} type={"primary"} onClick={() => { setShowEditTagModal(false) }}>{'确认'}</Button>
					</div>
				</Modal.Footer>
			</Modal>

			{showLoading && (
				<Text style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}>
					<Icon type="loading" style={{ marginLeft: 10 }}></Icon>
					<Text style={{ fontSize: 12, color: '#006cff', marginLeft: 5 }}>
						<span>报告生成中</span>
					</Text>
				</Text>
			)}
			{downStatus === "success" &&
				!showLoading &&
				(cosUrl.CosUrlPdf !== '' ? (
					<Text
						style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
						onClick={() => setReportTypeVisible(true)}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="16"
							height="16"
							style={{ marginLeft: 30, marginRight: 5 }}
						>
							<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#d71111" />
						</svg>
						<Text style={{ fontSize: 12, color: '#d71111' }}>
							<span>下载报告</span>
						</Text>
					</Text>
				) : (
					<Text
						style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer' }}
						onClick={(e) => {
							e.stopPropagation();
							window.open(cosUrl.CosUrl);
						}}
					>
						<svg
							xmlns="http://www.w3.org/2000/svg"
							width="16"
							height="16"
							style={{ marginLeft: 30, marginRight: 5 }}
						>
							<path d="M7.5 12L11 8H9V2H6v6H4zM2 13h11v2H2z" fill="#d71111" />
						</svg>
						<Text style={{ fontSize: 12, color: '#d71111' }}>
							<span>下载报告</span>
						</Text>
					</Text>
				))}
			<Modal visible={reportTypeVisible} onClose={() => setReportTypeVisible(false)} caption="评估报告下载">
				<Modal.Body>
					<span>请选择下载报告类型：</span>
					<br />

					<Segment
						value={reportType.toString()}
						onChange={(value) => setReportType(parseInt(value, 10))}
						options={[
							{
								text: (
									<>
										{'评估报告EXCEL版'}
									</>
								),
								value: '1',

								style: { width: '200px', height: '80px' },
							},
							{
								text: (
									<>
										{'评估报告PDF版'}
									</>
								),
								value: '2',

								style: { width: '200px', height: '80px', marginLeft: '10px' },
							},
						]}
					/>
				</Modal.Body>
				<Modal.Footer>
					<Button
						type="primary"
						onClick={(e) => {
							if (reportType === 1) {
								e.stopPropagation();
								window.open(cosUrl.CosUrl);
							} else if (reportType === 2) {
								e.stopPropagation();
								window.open(cosUrl.CosUrlPdf);
							}
							setReportTypeVisible(false);
						}}
					>
						确定
					</Button>
					<Button type="weak" onClick={() => setReportTypeVisible(false)}>
						取消
					</Button>
				</Modal.Footer>
			</Modal>
		</>
	);
}
