import React, { useState, useContext, useEffect, useRef, useMemo } from 'react';
import { app } from '@tencent/tea-app';
// import { t, span } from '@tencent/tea-app/i18n';
import { Link } from 'react-router-dom';
import {
	getReportFile,
	getTaskResultStrategyUnsafeDetail,
	getTaskResultStrategyIgnoredDetail,
	getReportFileAsync,
	getReportResultAsync,
	getRegionCodes,
} from '@src/api/advisor/estimate';
import { message } from '@tea/component/message';
import { ConfigStrategy, ResultStrategy } from '@src/types/advisor/estimate1';
import { Text } from '@tencent/tea-component/lib/text';
import { Collapse } from '@tencent/tea-component/lib/collapse';
import { Bubble } from '@tencent/tea-component/lib/bubble';
import { Icon } from '@tencent/tea-component/lib/icon';
import { AdvisorTable } from '@src/routes/advisor/estimate/AdvisorTable';
import { tabTitleMap } from '@src/routes/advisor/estimate/AdvisorConfig';
import { StatusIcon } from '@src/routes/advisor/estimate/StatusIcon';
import { AdvisorGroupReport } from '@src/routes/advisor/estimate/AdvisorGroupReport';
import { AdvisorStrategyReport } from '@src/routes/advisor/estimate/AdvisorStrategyReport';
import FileSaver from 'file-saver';
import { base64toBlob } from '@src/utils/base64toBlob';
import _ from 'lodash';

interface Region {
	Code: number;
	Region: string;
}
interface Prop {
	regionCodes: Array<Region>;
	AppId: number;
	id: string;
	currentTaskId: string;
	env: string;
	Groups: any,
	Products: any,
	ProductsOptions: any,
	strategyInputValue: string;
	taskCompleted: boolean;
	detectionStrategies: Array<ConfigStrategy>;
	strategySummary: object;
	onChangeReportRunning: Function;
}

// 当前下载报告状态队列池
let currentReportStatusQueue = [];

export function AdvisorPanel({
	regionCodes,
	AppId,
	id,
	currentTaskId,
	env,
	Groups,
	Products,
	ProductsOptions,
	strategyInputValue,
	taskCompleted,
	detectionStrategies,
	strategySummary,
	onChangeReportRunning,
}: Prop) {
	// 策略配置信息警告条件集合
	const [detectionConditions, setDetectionConditions] = useState({});
	// 策略执行结果概览Id集合
	const [summaryStrategyIds, setSummaryStrategyIds] = useState([]);
	// 策略执行状态集合
	const [strategyStatus, setStrategyStatus] = useState({});
	// 策略风险等级集合
	const [strategyRiskLevels, setStrategyRiskLevels] = useState({});
	// 策略有风险实例数量集合
	const [strategyRiskNum, setStrategyRiskNum] = useState({});
	// 策略被忽略实例数量集合
	const [strategyIgnoreNum, setStrategyIgnoreNum] = useState({});
	// 策略评估中的实例列表
	const [strategyAssesses, setStrategyAssesses] = useState({});
	// 策略评估中的实例列表总数
	const [strategyAssessesTotalCount, setStrategyAssessesTotalCount] = useState({});
	// 策略评估中的实例列表是否已加载首屏
	const [strategyAssessesFirstPageStatus, setStrategyAssessesFirstPageStatus] = useState({});
	// 策略忽略的实例列表
	const [strategyIgnores, setStrategyIgnores] = useState({});
	// 策略忽略的实例列表总数
	const [strategyIgnoresTotalCount, setStrategyIgnoresTotalCount] = useState({});
	// 策略忽略的实例列表是否已加载首屏
	const [strategyIgnoresFirstPageStatus, setStrategyIgnoresFirstPageStatus] = useState({});
	// 下载报告状态队列池
	const [reportStatusQueue, setReportStatusQueue] = useState([]);

	// 策略面板激活Id组
	const [strategyActiveIds, setStrategyActiveIds] = useState({});

	// 警告条件主题颜色映射
	const conditionThemeConfig = {
		'-1': 'weak',
		0: 'success',
		1: 'warning',
		2: 'warning',
		3: 'danger',
	};

	// 策略组ID映射
	const tabIdMap = new Map([
		['overview', -1],
		['security', 1],
		['architecture', 2],
		['resource', 3],
		['cost', 4],
		['performance', 5],
	]);

	// 当前策略评估中的实例列表
	const currentStrategyAssesses = _.cloneDeep(strategyAssesses);
	// 当前策略评估中的实例列表总数
	const currentStrategyAssessesTotalCount = _.cloneDeep(strategyAssessesTotalCount);
	// 当前策略评估中的实例列表是否已加载首屏
	const currentStrategyAssessesFirstPageStatus = _.cloneDeep(strategyAssessesFirstPageStatus);
	// 当前策略忽略的实例列表
	const currentStrategyIgnores = _.cloneDeep(strategyIgnores);
	// 当前策略忽略掉实例列表总数
	const currentStrategyIgnoresTotalCount = _.cloneDeep(strategyIgnoresTotalCount);
	// 当前策略忽略的实例列表是否已加载首屏
	const currentStrategyIgnoresFirstPageStatus = _.cloneDeep(strategyIgnoresFirstPageStatus);

	// 获取任务执行的有风险实例结果详情
	const getTaskUnsafeDetail = async (
		id: number,
		filter?: string,
		offset?: number,
		limit?: number,
		isFirstPage = false
	) => {
		// 如果该策略获取过首屏数据，直接返回
		if (currentStrategyAssessesFirstPageStatus && currentStrategyAssessesFirstPageStatus[id] && isFirstPage) {
			return;
		}

		// 如果该策略未获取过首屏数据，重置策略状态
		if (isFirstPage) {
			const currentStrategyStatus = _.cloneDeep(strategyStatus);
			currentStrategyStatus[id] = 'running';
			setStrategyStatus(currentStrategyStatus);
		}

		try {
			const unsafeDetail = await getTaskResultStrategyUnsafeDetail({
				AppId: AppId,
				TaskId: currentTaskId || '',
				StrategyId: id,
				Filter: filter || '',
				Offset: offset || 0,
				Limit: limit || 10,
			});

			if (unsafeDetail.Error) {
				const msg = unsafeDetail.Error.Message || '';
				//app.tips.error(message);
				message.error({ content: msg });
				return;
			}

			const detectionStrategy = detectionStrategies.find(
				(detectionStrategy) => detectionStrategy.StrategyId === id
			);

			if (unsafeDetail && unsafeDetail.UnsafeDetails.length > 0) {
				currentStrategyAssesses[id] = unsafeDetail.UnsafeDetails.map((unsafe) => {
					const level = detectionStrategy.Conditions.find(
						(detectCondition) => detectCondition.ConditionId === unsafe.ConditionId
					).Level;

					return {
						...unsafe.Instance,
						Level: level,
						IgnoredStatus: unsafe.IgnoredStatus,
					};
				});

				setStrategyAssesses(currentStrategyAssesses);
			} else {
				currentStrategyAssesses[id] = [];

				setStrategyAssesses(currentStrategyAssesses);
			}

			currentStrategyAssessesTotalCount[id] = unsafeDetail.TotalCount || 0;
			setStrategyAssessesTotalCount(currentStrategyAssessesTotalCount);

			if (isFirstPage) {
				currentStrategyAssessesFirstPageStatus[id] = true;
				setStrategyAssessesFirstPageStatus(currentStrategyAssessesFirstPageStatus);

				const currentStrategyStatus = _.cloneDeep(strategyStatus);
				currentStrategyStatus[id] = 'success';
				setStrategyStatus(currentStrategyStatus);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
			//app.tips.error({ message });
		}
	};

	// 获取任务执行的被忽略实例结果详情
	const getTaskIgnoredDetail = async (
		id: number,
		filter?: string,
		offset?: number,
		limit?: number,
		isFirstPage = false
	) => {
		// 如果该策略获取过首屏数据，直接返回
		if (currentStrategyIgnoresFirstPageStatus && currentStrategyIgnoresFirstPageStatus[id] && isFirstPage) {
			return;
		}

		try {
			const ignoredDetail = await getTaskResultStrategyIgnoredDetail({
				AppId: AppId,
				TaskId: currentTaskId || '',
				StrategyId: id,
				Filter: filter || '',
				Offset: offset || 0,
				Limit: limit || 10,
			});

			if (ignoredDetail.Error) {
				const msg = ignoredDetail.Error.Message || '';
				//app.tips.error(message);
				message.error({ content: msg });
				return;
			}

			if (ignoredDetail && ignoredDetail.IgnoredDetails.length > 0) {
				currentStrategyIgnores[id] = ignoredDetail.IgnoredDetails.map((ignored) => {
					return {
						...ignored.Instance,
						Level: -1,
						IgnoredStatus: ignored.IgnoredStatus,
					};
				});

				setStrategyIgnores(currentStrategyIgnores);
			} else {
				currentStrategyIgnores[id] = [];

				setStrategyIgnores(currentStrategyIgnores);
			}

			currentStrategyIgnoresTotalCount[id] = ignoredDetail.TotalCount || 0;
			setStrategyIgnoresTotalCount(currentStrategyIgnoresTotalCount);

			if (isFirstPage) {
				currentStrategyIgnoresFirstPageStatus[id] = true;
				setStrategyIgnoresFirstPageStatus(currentStrategyIgnoresFirstPageStatus);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	// 获取Excel异步下载结果
	const getResultAsync = async (id, currentReportStatusQueue, reportTimer) => {
		try {
			const reportResult = await getReportResultAsync({
				AppId: AppId,
				ResultId: id,
			});
			if (reportResult.Error) {
				const msg = reportResult.Error.Message || '';
				//app.tips.error({ message });
				message.error({ content: msg });
				return;
			}

			const currentReport = currentReportStatusQueue.find((report) => report.ReportId === id) || {};

			currentReport.CosUrl = reportResult.CosUrl || '';
			currentReport.CosUrlPdf = reportResult.CosUrlPdf || '';
			currentReport.TaskStatus = reportResult.TaskStatus || '';

			if (
				reportTimer &&
				currentReportStatusQueue.every(
					(report) => report.TaskStatus === 'success' || report.TaskStatus === 'failed'
				)
			) {
				clearInterval(reportTimer);
				setReportStatusQueue(currentReportStatusQueue);

				onChangeReportRunning(false);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	// 请求Excel异步下载
	const handleReportAsync = async (id, type) => {
		onChangeReportRunning(true);

		try {
			const reportAsync = await getReportFileAsync({
				AppId: AppId,
				Id: id,
				Type: type,
				TaskId: currentTaskId,
				Env: env,
				Products: [],
				GroupIDs: [],
				// GroupIDs: [1,2,3],
				Tags: [],
			});
			if (reportAsync.Error) {
				const msg = reportAsync.Error.Message || '';
				//app.tips.error({ message });
				message.error({ content: msg });
				//报告生成失败，仍旧写入一个失败的报告记录，并更新currentReportStatusQueue；方便报告相关的渲染
				if (currentReportStatusQueue.find((report) => id === report.Id && report.Type === type)) {
					let currentReport = currentReportStatusQueue.find((report) => id === report.Id && report.Type === type);
					let tmp = currentReportStatusQueue.indexOf(currentReport);
					let newcurrentReportStatusQueue = JSON.parse(JSON.stringify(currentReportStatusQueue)) //注意，要深度copy，不然子组件不会刷新
					newcurrentReportStatusQueue.splice(tmp, 1);
					currentReport.RequestId = reportAsync.RequestId;//无意义，仅用于标记失败的报告记录是否刷新
					currentReport.TaskStatus = 'failed';
					newcurrentReportStatusQueue.push(currentReport);
					setReportStatusQueue(newcurrentReportStatusQueue);
				} else {
					currentReportStatusQueue = [
						...currentReportStatusQueue,
						{
							Id: id,
							Type: type,
							ReportId: '',
							CosUrl: '',
							TaskStatus: 'failed',
							RequestId: reportAsync.RequestId,//无意义，仅用于标记失败的报告记录是否刷新
						},
					];
					setReportStatusQueue(currentReportStatusQueue);
				}
				return;
			}

			if (currentReportStatusQueue.find((report) => id === report.Id && report.Type === type)) {
				let currentReport = currentReportStatusQueue.find((report) => id === report.Id && report.Type === type);
				currentReport = {
					Id: id,
					Type: type,
					ReportId: reportAsync.ResultId || '',
					CosUrl: '',
					TaskStatus: '',
				};
			} else {
				currentReportStatusQueue = [
					...currentReportStatusQueue,
					{
						Id: id,
						Type: type,
						ReportId: reportAsync.ResultId || '',
						CosUrl: '',
						TaskStatus: '',
					},
				];
			}

			let reportTimer;

			getResultAsync(reportAsync.ResultId, currentReportStatusQueue, reportTimer);

			reportTimer = setInterval(() => {
				getResultAsync(reportAsync.ResultId, currentReportStatusQueue, reportTimer);
			}, 5000);
		} catch (err) {
			onChangeReportRunning(false);

			const msg = err.msg || err.toString() || '未知错误';
			//app.tips.error({ message });
			message.error({ content: msg });
		}
	};

	// 当前策略执行状态
	const currentStrategyStatus = {};
	// 当前策略风险等级
	const currentStrategyRiskLevels = {};
	// 当前策略有风险实例数量
	const currentStrategyRiskNum = {};
	// 当前策略被忽略实例数量
	const currentStrategyIgnoreNum = {};
	// 当前策略面板激活Id组
	const currentStrategyActiveIds = {};

	// 获取策略配置信息评估中和忽略的实例列表
	useEffect(() => {
		const currentSummaryStrategyIds = Object.keys(strategySummary);

		currentSummaryStrategyIds.forEach((strategyId) => {
			const currentStrategy = strategySummary[strategyId] || {};

			// 获取当前策略执行状态
			currentStrategyStatus[strategyId] = currentStrategy.status || '';

			// 获取当前策略风险等级
			if (currentStrategy.high > 0) {
				currentStrategyRiskLevels[strategyId] = 3;
			} else if (currentStrategy.medium > 0) {
				currentStrategyRiskLevels[strategyId] = 2;
			} else if (currentStrategy.low > 0) {
				currentStrategyRiskLevels[strategyId] = 1;
			} else {
				currentStrategyRiskLevels[strategyId] = 0;
			}

			// 获取当前策略有风险实例数量
			currentStrategyRiskNum[strategyId] =
				currentStrategy.high + currentStrategy.medium + currentStrategy.low || 0;

			// 获取当前策略被忽略实例数量
			currentStrategyIgnoreNum[strategyId] = currentStrategy.ignore || 0;

			// 获取当前策略面板激活Id组
			currentStrategyActiveIds[strategyId] = [];
		});

		setSummaryStrategyIds(currentSummaryStrategyIds);
		setStrategyStatus(currentStrategyStatus);
		setStrategyRiskLevels(currentStrategyRiskLevels);
		setStrategyRiskNum(currentStrategyRiskNum);
		setStrategyIgnoreNum(currentStrategyIgnoreNum);
		setStrategyActiveIds(currentStrategyActiveIds);
	}, [strategySummary]);

	// 获取地区对应编码
	// useEffect(() => {
	// 	getCodes();
	// }, []);

	// 开始评估后清空下载报告状态队列池、已加载首屏状态
	useEffect(() => {
		if (!taskCompleted) {
			currentReportStatusQueue = [];
			setReportStatusQueue([]);

			setStrategyAssessesFirstPageStatus({});
			setStrategyIgnoresFirstPageStatus({});
		}
	}, [taskCompleted]);

	// 重新搜索后清空评估中/已忽略实例列表已加载首屏状态和激活面板Id组
	useEffect(() => {
		setStrategyAssessesFirstPageStatus({});
		setStrategyIgnoresFirstPageStatus({});
		setStrategyActiveIds({});
	}, [strategyInputValue]);

	// 策略配置信息排序条件
	const handleSortStrategies = (prev, curr) => {
		const prevId = prev.StrategyId || 0;
		const currId = curr.StrategyId || 0;
		const prevProduct = prev.Product || '';
		const currProduct = curr.Product || '';
		const prevLevel = strategyRiskLevels[prevId] >= 0 ? strategyRiskLevels[prevId] : -1;
		const currLevel = strategyRiskLevels[currId] >= 0 ? strategyRiskLevels[currId] : -1;

		// 排序条件 风险等级（由高到低）> 产品名称（字母排序，由小到大）> 策略ID（由小到大）
		if (prevLevel !== currLevel) {
			return currLevel - prevLevel;
		} else if (prevProduct.toLowerCase() !== currProduct.toLowerCase()) {
			return prevProduct.toLowerCase() < currProduct.toLowerCase() ? -1 : 1;
		} else {
			return prevId - currId;
		}
	};

	//把建议中的markdown形式的超链接转换为html里的target为_blank的a标签
	const simpleMarkdownToHTML = (input: string): string => {
		/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
		let reg = new RegExp(
			'(\\[[\u4e00-\u9fa5_a-zA-Z0-9,、/-\\s]+\\])(\\((\\s?https?|ftp|file)://[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]\\))',
			'g'
		);
		/* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */
		let result = input.replace(reg, (match, text, link) => {
			return `<a href="${link.slice(1, -1)}" target="_blank">${text.slice(1, -1)}</a>`;
		});

		// 无序列表markdown语法转换
		if (result.includes('- ')) {
			result = result
				.split('- ')
				.filter((r) => r.length)
				.map((r, index) => {
					return !index ? `<div>• ${r}</div>` : `<div style="margin-top: 8px;">• ${r}</div>`;
				})
				.join('');
		}

		return result;
	};

	return (
		<div className="intlc-assessment-tabitem">
			<div className="intlc-assessment-tabitem__advice">
				<Text style={{ display: 'inline-flex', alignItems: 'center' }}>
					{tabTitleMap.get(id)}
					{taskCompleted &&
						detectionStrategies
							.map((detectionStrategy) => detectionStrategy.StrategyId)
							.some((id) => summaryStrategyIds.includes(id.toString())) &&
						reportStatusQueue && (
							<AdvisorGroupReport
								id=""
								AppId={AppId}
								type="Group"
								env={env}
								currentTaskId={currentTaskId}
								TemplateOptions={[]}
								Products={Products}
								ProductsOptions={ProductsOptions}
								Groups={Groups}
								AllGroups={{}}
								reportStatusQueue={reportStatusQueue}
								onChangeReportAsync={handleReportAsync}
								getTemplatesList={reportStatusQueue}
								downStatus={''}
								cosUrl={[]}
							></AdvisorGroupReport>
						)}
				</Text>
			</div>
			{detectionStrategies
				// .filter((strategy) => summaryStrategyIds.includes(strategy.StrategyId.toString()))
				.filter((strategy) => strategy.Name.includes(strategyInputValue)).length > 0 ? (
				detectionStrategies
					// .filter((strategy) => summaryStrategyIds.includes(strategy.StrategyId.toString()))
					.filter((strategy) => strategy.Name.includes(strategyInputValue))
					.sort(handleSortStrategies)
					.map((strategy) => {
						return (
							<Collapse
								key={strategy.StrategyId}
								activeIds={strategyActiveIds[strategy.StrategyId] || []}
								icon={(active) => (
									<StatusIcon
										active={active}
										taskCompleted={taskCompleted}
										strategyStatus={strategyStatus[strategy.StrategyId] || ''}
										strategyLevel={
											strategyRiskLevels[strategy.StrategyId] >= 0
												? strategyRiskLevels[strategy.StrategyId]
												: -1
										}
									></StatusIcon>
								)}
								iconPosition="left"
								onActive={(activeIds) => {
									const currentStrategyActiveIds = _.cloneDeep(strategyActiveIds);
									currentStrategyActiveIds[strategy.StrategyId] = activeIds;
									setStrategyActiveIds(currentStrategyActiveIds);

									if (summaryStrategyIds.includes(strategy.StrategyId.toString())) {
										getTaskUnsafeDetail(strategy.StrategyId, '', 0, 10, true);
										getTaskIgnoredDetail(strategy.StrategyId, '', 0, 10, true);
									}
								}}
								className="intlc-assessment-tabitem__content"
								// 暂时调整未评估策略与评估中策略左对齐
								style={{
									paddingLeft: !(
										taskCompleted &&
										strategyStatus[strategy.StrategyId] === 'success' &&
										summaryStrategyIds.includes(strategy.StrategyId.toString())
									)
										? 25
										: 0,
								}}
							>
								<Collapse.Panel
									id={strategy.StrategyId.toString()}
									title={(active) => (
										<>
											<div className="intlc-assessment-tabitem__title">
												<Text style={{ display: 'inline-flex', alignItems: 'center' }}>
													{strategy.Name}
													{taskCompleted &&
														strategyStatus[strategy.StrategyId] === 'success' &&
														reportStatusQueue && (
															<AdvisorStrategyReport
																id={strategy.StrategyId}
																type="Strategy"
																reportStatusQueue={reportStatusQueue}
																onChangeReportAsync={handleReportAsync}
															></AdvisorStrategyReport>
														)}
												</Text>
											</div>
											<div className="intlc-assessment-tabitem__introduction">
												<div className="intlc-assessment-tabitem__instructions">
													{strategy.Desc}
												</div>
												{taskCompleted &&
													(!active ||
														!summaryStrategyIds.includes(
															strategy.StrategyId.toString()
														)) && (
														<div className="intlc-assessment-tabitem__instructions intlc-assessment-tabitem__instructions--multiple">
															{`${strategy.Notice.replace(
																'%d',
																strategyRiskNum[strategy.StrategyId] || 0
															)}${strategy.Ignore.replace(
																'%d',
																strategyIgnoreNum[strategy.StrategyId] || 0
															)}`}
														</div>
													)}
											</div>
										</>
									)}
								>
									{taskCompleted &&
										strategyStatus[strategy.StrategyId] === 'success' &&
										summaryStrategyIds.includes(strategy.StrategyId.toString()) && (
											<div
												className={
													strategyStatus[strategy.StrategyId] === 'success'
														? 'intlc-assessment-tabcoll intlc-assessment-tabcoll--complete'
														: strategyStatus[strategy.StrategyId] === 'init' ||
															strategyStatus[strategy.StrategyId] === 'running'
															? 'intlc-assessment-tabcoll intlc-assessment-tabcoll--loading'
															: 'intlc-assessment-tabcoll'
												}
											>
												{/* 警告条件 */}
												<div className="intlc-assessment-tabcoll__title">
													<span>警告条件</span>
												</div>
												{strategy.Conditions.sort(function (c1, c2) {
													return c2.Level - c1.Level;
												}).map((condition) => {
													return (
														<div
															key={condition.ConditionId}
															className="intlc-assessment-tabcoll__conditions"
														>
															<Text
																bgTheme={
																	conditionThemeConfig[condition.Level.toString()]
																}
																verticalAlign="top"
																className="intlc-assessment-tabs-tag__block intlc-assessment-tabs-tag__block--instructions"
															></Text>
															<div className="intlc-assessment-tabitem__instructions intlc-assessment-tabitem__instructions--inline">
																{condition.Desc}
															</div>
														</div>
													);
												})}

												{/* 优化建议 */}
												<div className="intlc-assessment-tabcoll__title">
													<span>优化建议</span>
												</div>
												<div
													className="intlc-assessment-tabcoll__advice"
													dangerouslySetInnerHTML={{
														__html: simpleMarkdownToHTML(strategy.Repair),
													}}
												></div>

												{/* 资源列表 */}
												<div className="intlc-assessment-tabcoll__title">
													<span>资源列表</span>
												</div>
												<div className="intlc-assessment-tabitem__instructions">{`${strategy.Notice.replace(
													'%d',
													strategyRiskNum[strategy.StrategyId] || 0
												)}${strategy.Ignore.replace(
													'%d',
													strategyIgnoreNum[strategy.StrategyId] || 0
												)}`}</div>
												<AdvisorTable
													AppId={AppId}
													product={strategy.Product}
													strategyId={strategy.StrategyId}
													strategy={strategy}
													regionCodes={regionCodes}
													currentTaskId={currentTaskId}
													strategyAssesses={strategyAssesses[strategy.StrategyId] || []}
													assessTotalCount={
														strategyAssessesTotalCount[strategy.StrategyId] || 0
													}
													strategyIgnores={strategyIgnores[strategy.StrategyId] || []}
													ignoreTotalCount={
														strategyIgnoresTotalCount[strategy.StrategyId] || 0
													}
													onChangeAssesses={getTaskUnsafeDetail}
													onChangeIgnores={getTaskIgnoredDetail}
												></AdvisorTable>
											</div>
										)}
								</Collapse.Panel>
							</Collapse>
						);
					})
			) : (
				<div className="intlc-assessment-tabitem">
					<div className="intlc-assessment-tabitem__empty">
						<span>暂无内容</span>
					</div>
				</div>
			)}
		</div>
	);
}
