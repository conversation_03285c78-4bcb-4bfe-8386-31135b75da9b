import React, { useEffect } from 'react';
import { PageRoute } from './Route';
import { useAliveController } from 'react-activation';
import 'tdesign-react/es/style/index.css';

// 控制顶层页面缓存，访问这些页面时需要保留缓存
const whiteList = [
	'/advisor/broadcast',
	'/advisor/broadcast-editor',
	'/advisor/architecture',
	'/advisor/strategies-manage',
	'/advisor/guard',
	'/advisor/approval',
  '/advisor/gateway-administration',
  '/advisor/inspection-release-t',
  '/advisor/inspection-release-pre',
  '/advisor/inspection-release-domestic',
  '/advisor/inspection-release-international',
];

export function Advisor() {
	const { drop, clear, getCachingNodes } = useAliveController();
	let isKeep = false;

	// 控制同时存在的缓存只有一个 （注意：需要为每一个缓存块命名 name）
	const onChangeCache = async () => {
		const nodes = getCachingNodes();
		if (nodes.length > 1) {
			await drop(nodes[0].name);
		}
	};

	// 根据白名单来判断是否保持缓存
	for (const item of whiteList) {
		if (location.pathname.includes(item)) {
			isKeep = true;
			break;
		} else isKeep = false;
	}

	useEffect(() => {
		// 白名单之间出现缓存块和缓存块切换时，清理掉上一个缓存块
		if (getCachingNodes && isKeep) {
			onChangeCache();
		}
		// 切换到不在白名单内的地址清理掉所有缓存
		if (getCachingNodes && !isKeep) {
			clear();
		}
	}, [location.pathname]);
	return <PageRoute></PageRoute>;
}
