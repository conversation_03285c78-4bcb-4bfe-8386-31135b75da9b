import { useHistory } from '@tea/app';
import React from 'react';
import { Redirect, Route, Router, Switch } from 'react-router-dom';
import { Overview } from './pages/overview/Overview';
import { OverviewIndex } from './pages/overview/OverviewIndex';
import { FocusedStrategyManage } from './pages/overview/FocusedStrategyManage';
import { RiskQuery } from './pages/overview/RiskQuery';
import { Monitor } from './pages/Monitor';
import { StrategiesManage } from './pages/strategies/StrategiesManage';
import { strategiesNew } from './pages/strategiesNew';
import { Operation } from './pages/strategiesNew/Operation';
import { Log } from './pages/strategiesNew/Log';
import { StrategyEdit } from './pages/strategies/StrategyEdit';
import { OperationalDetail } from './pages/strategies/OperationalDetail';
import { Assess } from './pages/assess/Assess';

import { Summary } from './pages/assess/Summary';
import { Dashboard } from './pages/dashboard/Dashboard';
import { ProductOverview } from './pages/dashboard/ProductOverview';
import { CvmOverview } from './pages/dashboard/Cvm';
import { ClbOverview } from './pages/dashboard/Clb';
import { CbsOverview } from './pages/dashboard/Cbs';
import { VpcOverview } from './pages/dashboard/Vpc';
import { MysqlOverview } from './pages/dashboard/Mysql';
import { MongodbOverview } from './pages/dashboard/Mongodb';
import { RedisOverview } from './pages/dashboard/Redis';
import { CdnOverview } from './pages/dashboard/Cdn';
import { CkafkaOverview } from './pages/dashboard/Ckafka';
import { EsOverview } from './pages/dashboard/Es';
import { TdmqOverview } from './pages/dashboard/Tdmq';
import { LiveOverview } from './pages/dashboard/Live';
import { SgOverview } from './pages/dashboard/Sg';
import { CosOverview } from './pages/dashboard/Cos';
import { EipOverview } from './pages/dashboard/Eip';
import { AclOverview } from './pages/dashboard/Acl';
import { VpngwOverview } from './pages/dashboard/Vpngw';
import { BwpOverview } from './pages/dashboard/Bwp';
import { TkeOverview } from './pages/dashboard/Tke';

import { Alarm } from './pages/alarm/Alarm';
import { FailureIndex } from './pages/failure/FailureIndex';
import { FailureSummary } from './pages/failure/FailureSummary';

import { ArchitectureIndex } from '@src/routes/architecture/pages/ArchitectureIndex';
import { TemplateEditor, CloudMapEditor } from '@src/routes/architecture/components';

import { Guard } from '@src/routes/advisor/pages/guard/Guard';
// import { GuardGrafana } from '@src/routes/advisor/pages/guard/GuardGrafana';
import { GuardSteps } from '@src/routes/advisor/pages/guard/components/GuardSteps';
import { GuardSummary } from '@src/routes/advisor/pages/guard/components/GuardSummary';
import { GuardApproval } from '@src/routes/advisor/pages/guard/GuardApproval';
import { ApprovalDetail } from './pages/guard/components/approval/ApprovalDetail';
import { ApprovalSales } from './pages/guard/components/approval/ApprovalSales';
import { SalesConfirm } from './pages/guard/components/approval/sales-confirm';
import { SpecialApproval } from './pages/guard/components/approval/special-approval';
import { ApprovalExpert } from './pages/guard/components/approval/ApprovalExpert';
import { Report } from '@src/routes/advisor/pages/guard/Report';

import { ConfigManagement } from '@src/routes/advisor/pages/broadcast/ConfigManagement';
import { BroadStrategyEditor } from '@src/routes/advisor/pages/broadcast/BroadStrategyEditor';
import { Broadcast } from '@src/routes/advisor/pages/broadcast/Broadcast';
import { BroadcastEditor } from '@src/routes/advisor/pages/broadcast/BroadcastEditor';
import { BroadcastResult } from '@src/routes/advisor/pages/broadcast/BroadcastResult';
import { GuardBaseInfoEditor } from './pages/guard/components/guardEdit/GuardBaseInfoEditor';
import { GuardInstanceEditor } from './pages/guard/components/guardEdit/GuardInstanceEditor';
import { GuardResultEditor } from './pages/guard/components/guardEdit/GuardResultEditor';
import { BroadcastConfigEditor } from '@src/routes/advisor/pages/broadcast/components/broad-config-editor';
import { GuardBroadcastConfigEditor } from '@src/routes/advisor/pages/broadcast/components/guard-broadcast-config-editor';

import { PrivilegeApplyEditor } from '@src/routes/advisor/pages/privilege/PrivilegeApplyEditor';
import { PrivilegeApplyAndRole } from './pages/privilege/PrivilegeApplyAndRole';
import { PrivilegeApplyDetail } from './pages/privilege/PrivilegeApplyDetail';
import { PrivilegeApplyRenew } from './pages/privilege/PrivilegeApplyRenew';
import { FaultNotification } from './pages/faultNotification';
import { Detail as FaultNotificationDetail } from './pages/faultNotification/Detail';
import { AbroadDetail } from './pages/faultNotification/components/abroad-detail';
import { Examine as FaultNotificationExamine } from './pages/faultNotification/Examine';
import { New as FaultNotificationNew } from './pages/faultNotification/New';
import { ProactiveTouch } from './pages/touch';
import { AliveScope } from 'react-activation';
import { WithKeepWrapper } from './pages/withKeepWrapper';
// import { Service } from './pages/service';
// import { Detail as ServiceDetail } from './pages/service/Detail';
import { CommonEvent } from './pages/event';
import { New as CommonEventNew } from './pages/event/New';
import { Detail as CommonEventDetail } from './pages/event/Detail';
import { SelfDevelopedDetail } from './pages/event/SelfDevelopedDetail';
import NewFeaturesModal from '@src/components/NewFeaturesModal';
import { Handle } from '@src/routes/advisor/pages/handle';
import { Auth } from '@src/routes/advisor/pages/handle/Auth';
import { MyCustomer } from '@src/routes/advisor/pages/myCustomer';
import { InspectionRelease } from '@src/routes/advisor/pages/inspectionRelease';
import { StrategyConfigDetail } from '@src/routes/advisor/pages/inspectionRelease/StrategyConfigDetail';
import { MonitoringConfigDetail } from '@src/routes/advisor/pages/inspectionRelease/MonitoringConfigDetail';
import { ProductConfigDetail } from '@src/routes/advisor/pages/inspectionRelease/ProductConfigDetail';
import GuardStatuAntool from '@src/routes/advisor/pages/guard/components/GuardStatuAntool';
import MicroAppContainer from '@src/components/MicroAppContainer';
import GlobalRouteGuard from '@src/components/GlobalRouteGuard';
import DataScreening from '@src/routes/advisor/pages/dataScreening';
import InspectReport from '@src/routes/advisor/pages/inspectReport';
import SingletonAutoTrackBeacon from '@src/utils/autotracker-beacon';
import GuardBroadcastConfigList  from '@src/routes/advisor/pages/broadcast/components/guard-broadcast-config-list';
import { getStorage } from '@src/utils/storage';
import useOnceFunc from '@src/hooks/useOnceFunc';
import { getUserInfo } from '@src/api/common';
import { SESSION_USER_NAME, SESSION_DEPMENT } from '@src/configs/tree';
import { reportSitePv } from '@src/utils/report';
import { Transfer } from '@src/routes/advisor/pages/temporary';
import { GatewayAdministration } from '@src/routes/advisor/pages/gatewayAdministration';
import { ServiceDetail } from '@src/routes/advisor/pages/gatewayAdministration/ServiceDetail';
import { ApiDetail } from '@src/routes/advisor/pages/gatewayAdministration/ApiDetail';
import { CustomerRiskOverview } from '@src/routes/advisor/pages/customerRiskOverview';
import { IS_INTL } from '@src/utils/constants';

export function PageRoute() {
	const history = useHistory();
	const setBaseInfo = async () => {
		const engName = getStorage('engName');
		const { data } = await getUserInfo();
		localStorage.setItem(SESSION_USER_NAME, engName);
		localStorage.setItem(SESSION_DEPMENT, data?.DeptNameString ?? '');
		SingletonAutoTrackBeacon.getInstance({
			commonParams: { uid: engName },
		});
		reportSitePv({
			isaReportSitePv: 'visitSite',
		});
	};

	useOnceFunc(setBaseInfo);
	(window as any).isaHistory = history;
	return (
		<Router history={history}>
			{/* @ts-ignore */}
			<AliveScope>
				<GlobalRouteGuard>
					<NewFeaturesModal path={history.location.pathname}></NewFeaturesModal>
					<Switch>

						<Route exact={false} path="/advisor/cloud-inspection/inspect-overview" component={MicroAppContainer} />
						<Route exact={false} path="/advisor/cloud-inspection/inspect-detail" component={MicroAppContainer} />

						<Route exact path="/advisor/monitor" component={Monitor} />

						<Route exact path="/advisor/overview" component={Overview} />
						<Route exact path="/advisor/overviewindex" component={OverviewIndex} />
						<Route exact path="/advisor/overview/focused-strategy-manage/:appid" component={FocusedStrategyManage} />
						<Route exact path="/advisor/overview/risk-query/:appid" component={RiskQuery} />
						<Route exact path="/advisor/strategies-manage">
							<WithKeepWrapper Component={IS_INTL ? StrategiesManage : strategiesNew} cacheKey="StrategiesManage" name="strategies-manage" />
						</Route>
						<Route exact path="/advisor/strategies-manage/operation/:handle/:type/:id" component={Operation} />
						<Route exact path="/advisor/strategies-manage/log/:id" component={Log} />
						<Route exact path="/advisor/strategies-manage/edit/:sid" component={StrategyEdit} />
						<Route exact path="/advisor/strategies-manage/new" component={StrategyEdit} />
						<Route exact path="/advisor/strategies-manage/operational-detail/:strategyId" component={OperationalDetail} />
						<Route exact path="/advisor/monitor-config" component={GuardBroadcastConfigList} />
						<Route exact path="/advisor/monitor-config-editor/:strategyid" component={GuardBroadcastConfigEditor} />

						<Route exact path="/advisor/dashboard" component={Dashboard} />
						<Route exact path="/advisor/dashboard/result/:appid" component={ProductOverview} />
						<Route exact path="/advisor/dashboard/cvm/:appid" component={CvmOverview} />
						<Route exact path="/advisor/dashboard/clb/:appid" component={ClbOverview} />
						<Route exact path="/advisor/dashboard/cbs/:appid" component={CbsOverview} />
						<Route exact path="/advisor/dashboard/vpc/:appid" component={VpcOverview} />
						<Route exact path="/advisor/dashboard/mysql/:appid" component={MysqlOverview} />
						<Route exact path="/advisor/dashboard/mongodb/:appid" component={MongodbOverview} />
						<Route exact path="/advisor/dashboard/redis/:appid" component={RedisOverview} />
						<Route exact path="/advisor/dashboard/cdn/:appid" component={CdnOverview} />
						<Route exact path="/advisor/dashboard/ckafka/:appid" component={CkafkaOverview} />
						<Route exact path="/advisor/dashboard/es/:appid" component={EsOverview} />
						<Route exact path="/advisor/dashboard/tdmq/:appid" component={TdmqOverview} />
						<Route exact path="/advisor/dashboard/live/:appid" component={LiveOverview} />
						<Route exact path="/advisor/dashboard/sg/:appid" component={SgOverview} />
						<Route exact path="/advisor/dashboard/cos/:appid" component={CosOverview} />
						<Route exact path="/advisor/dashboard/eip/:appid" component={EipOverview} />
						<Route exact path="/advisor/dashboard/acl/:appid" component={AclOverview} />
						<Route exact path="/advisor/dashboard/vpngw/:appid" component={VpngwOverview} />
						<Route exact path="/advisor/dashboard/bwp/:appid" component={BwpOverview} />
						<Route exact path="/advisor/dashboard/tke/:appid" component={TkeOverview} />
						<Route exact path="/advisor/assess" component={Assess} />

						<Route exact path="/advisor/assess/result/:appid" component={Summary} />
						<Route exact path="/advisor/alarm" component={Alarm} />

						<Route exact path="/advisor/failure" component={FailureIndex} />
						<Route exact path="/advisor/failure/failure-summary/:faultid" component={FailureSummary} />

						<Route exact path="/advisor/failure" component={FailureIndex} />
						<Route exact path="/advisor/failure/failure-summary/:faultid" component={FailureSummary} />

						{/* <Route exact path="/advisor/guard" component={Guard} /> */}
						<Route exact path="/advisor/guard">
							<WithKeepWrapper Component={Guard} cacheKey="Guard" name="guard" />
						</Route>
						{/* Antool-流程信息页面 */}
						<Route exact path="/advisor/guard/status" component={GuardStatuAntool} />
						<Route exact path="/advisor/guard/create" component={GuardSteps} />
						<Route exact path="/advisor/guard/summary/:guardid" component={GuardSummary} />
						<Route exact path="/advisor/guard/baseInfo/:guardid" component={GuardBaseInfoEditor} />
						<Route exact path="/advisor/guard/instances/:guardid" component={GuardInstanceEditor} />
						<Route exact path="/advisor/guard/result/:guardid/:appid" component={GuardResultEditor} />
						<Route exact path="/advisor/guard/report" component={Report} />
						{/* <Route exact path="/advisor/grafana" component={GuardGrafana} />*/}
						{/* 审批路由：全局路由守卫统一处理灰度控制 */}
						<Route exact path="/advisor/approval">
							<WithKeepWrapper Component={GuardApproval} cacheKey="GuardApproval" name="approval" />
						</Route>
						<Route exact path="/advisor/approval/detail/:guardid/:apvlid" component={ApprovalDetail} />
						<Route exact path="/advisor/approval/expert/:guardid/:apvlid" component={ApprovalExpert} />
						<Route exact path="/advisor/approval/sales/:guardid" component={ApprovalSales} />
						{/* 售后确认审批 */}
						<Route exact path="/advisor/approval/sales-confirm/:guardid/:apvlid" component={SalesConfirm} />
						{/* 无架构图特殊审批 */}
						<Route exact path="/advisor/approval/special-approval/:guardid/:apvlid" component={SpecialApproval} />

						{/* <Route exact path="/advisor/broadcast" component={Broadcast} /> */}
						<Route exact path="/advisor/broadcast">
							<WithKeepWrapper Component={Broadcast} cacheKey="Broadcast" name="broadcast" />
						</Route>
						<Route exact path="/advisor/broadcast-editor/:broadcastid/:type" component={BroadcastEditor} />
						<Route exact path="/advisor/broadcast-result/:broadcastid/:guardid" component={BroadcastResult} />

						<Route exact path="/advisor/privilege/apply-editor" component={PrivilegeApplyEditor} />
						<Route exact path="/advisor/privilege/apply/:applyid" component={PrivilegeApplyDetail} />
						<Route exact path="/advisor/privilege/apply-renew/:applyid" component={PrivilegeApplyRenew} />
						<Route exact path="/advisor/privilege/my" component={PrivilegeApplyAndRole} />

						{/* <Route exact path="/advisor/architecture" component={ArchitectureIndex}/> */}
						<Route exact path="/advisor/architecture">
							<WithKeepWrapper Component={ArchitectureIndex} cacheKey="cloudEditor" name="cloudEditor" />
						</Route>
						<Route exact path="/advisor/architecture/template-editor/:tplid" component={TemplateEditor} />
						<Route exact path="/advisor/architecture/cloud-editor/:mapUUId" component={CloudMapEditor} />
						<Route exact={false} path="/advisor/new-architecture" component={MicroAppContainer} />
						<Route exact={false} path="/advisor/digital-assets" component={MicroAppContainer} />
						<Route exact={false} path="/advisor/email-center" component={MicroAppContainer} />
						{/* // 缓存页面 */}
						{/* <Route exact path="/advisor/broad-strategy">
							<WithKeepWrapper Component={BroadStrategy} cacheKey="broadStrategy" />
					</Route> */}
						<Route exact path="/advisor/config-management" component={ConfigManagement} />
						<Route exact path="/advisor/broad-strategy-editor/:strategyid" component={BroadStrategyEditor} />
						<Route exact path="/advisor/broadcast-config-editor/:strategyid" component={BroadcastConfigEditor} />
						<Route exact path="/advisor/proactive-touch" component={ProactiveTouch} />
						<Route exact path="/advisor/fault-notification" component={FaultNotification} />
						<Route exact path="/advisor/fault-notification/detail/:id" component={FaultNotificationDetail} />
						<Route exact path="/advisor/fault-notification/abroad-detail/:id" component={AbroadDetail} />
						<Route exact path="/advisor/fault-notification/examine" component={FaultNotificationExamine} />
						<Route exact path="/advisor/fault-notification/new/:id" component={FaultNotificationNew} />
						{/* <Route exact path="/advisor/service" component={Service} /> */}
						{/* <Route exact path="/advisor/service/detail/:id" component={ServiceDetail} /> */}
						<Route exact path="/advisor/common-event" component={CommonEvent} />
						<Route exact path="/advisor/common-event/new/:id" component={CommonEventNew} />
						<Route exact path="/advisor/common-event/detail/:id" component={CommonEventDetail} />
						<Route exact path="/advisor/common-event/self-developed-detail/:id" component={SelfDevelopedDetail} />
						<Route exact path={'/advisor/handle/:id/:uin'} component={Handle} />
						<Route exact path={'/advisor/handle'} component={Auth} />
						<Route exact={false} path="/advisor/operation-manage" component={MicroAppContainer} />
						<Route exact path="/advisor/my-customer" component={MyCustomer} />
						{/* <Route exact path="/advisor/inspection-release-t" component={InspectionRelease} /> */}
						<Route exact path="/advisor/inspection-release-t">
							<WithKeepWrapper Component={InspectionRelease} cacheKey="inspection-release-t" name="inspection-release-t" />
						</Route>
						<Route exact path="/advisor/inspection-release-t/detail" component={StrategyConfigDetail} />
						<Route exact path="/advisor/inspection-release-t/monitoring-detail" component={MonitoringConfigDetail} />
						<Route exact path="/advisor/inspection-release-t/product-detail" component={ProductConfigDetail} />
						{/* <Route exact path="/advisor/inspection-release-pre" component={InspectionRelease} /> */}
						<Route exact path="/advisor/inspection-release-pre">
							<WithKeepWrapper Component={InspectionRelease} cacheKey="inspection-release-pre" name="inspection-release-pre" />
						</Route>
						<Route exact path="/advisor/inspection-release-pre/detail" component={StrategyConfigDetail} />
						<Route exact path="/advisor/inspection-release-pre/monitoring-detail" component={MonitoringConfigDetail} />
						<Route exact path="/advisor/inspection-release-pre/product-detail" component={ProductConfigDetail} />
						{/* <Route exact path="/advisor/inspection-release-domestic" component={InspectionRelease} /> */}
						<Route exact path="/advisor/inspection-release-domestic">
							<WithKeepWrapper Component={InspectionRelease} cacheKey="inspection-release-domestic" name="inspection-release-domestic" />
						</Route>
						<Route exact path="/advisor/inspection-release-domestic/detail" component={StrategyConfigDetail} />
						<Route exact path="/advisor/inspection-release-domestic/monitoring-detail" component={MonitoringConfigDetail} />
						<Route exact path="/advisor/inspection-release-domestic/product-detail" component={ProductConfigDetail} />
						<Route exact path="/advisor/inspection-release-international">
							<WithKeepWrapper Component={InspectionRelease} cacheKey="inspection-release-international" name="inspection-release-international" />
						</Route>
						<Route exact path="/advisor/inspection-release-international/detail" component={StrategyConfigDetail} />
						<Route exact path="/advisor/inspection-release-international/monitoring-detail" component={MonitoringConfigDetail} />
						<Route exact path="/advisor/inspection-release-international/product-detail" component={ProductConfigDetail} />
						<Route exact path="/advisor/inspect-report" component={InspectReport} />
						<Route exact path="/advisor/data-screening/:type" component={DataScreening} />
						<Route exact path="/advisor/transfer/:type" component={Transfer} />
						<Route exact path="/advisor/gateway-administration">
							<WithKeepWrapper Component={GatewayAdministration} cacheKey="GatewayAdministration" name="gateway-administration" />
						</Route>
						<Route exact path="/advisor/gateway-administration/service/:type" component={ServiceDetail} />
						<Route exact path="/advisor/gateway-administration/api/:type" component={ApiDetail} />
						<Route exact path="/advisor/customer-risk-overview" component={CustomerRiskOverview} />
						<Route exact={false} path="/advisor/cloud-escort/approval" component={MicroAppContainer} />
						<Route exact={false} path="/advisor/cloud-escort/config-management" component={MicroAppContainer} />
						<Redirect from="/advisor*" to="/advisor/new-architecture"></Redirect>
					</Switch>
				</GlobalRouteGuard>
			</AliveScope>
		</Router>
	);
}
