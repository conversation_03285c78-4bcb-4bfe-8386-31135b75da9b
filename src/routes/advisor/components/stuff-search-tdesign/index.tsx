import React, { useState, useEffect } from 'react';
import { Select } from 'tdesign-react';
import useSWR from 'swr';
import { describeUserList } from '@src/api/advisor/user';

interface StuffSearchType {
	value?: string[];
	emptyErrorMsg?: string;
	disable?: boolean;
	onChange?: (value: string[]) => void;
}

/**
 * 获取员工数据
 * <AUTHOR>
 * @param {string} name - 名字
 * @return {object}
 */
const fetchStuff = (name: string) => {
	const { data, error, mutate, isLoading, isValidating } = useSWR(
		name ? `/api/describeUserList?name=${name}` : null,
		() => describeUserList({
			Name: name,
			Limit: 20,
		}),
		// 数据缓存时间为1分钟
		{ revalidateOnFocus: false, dedupingInterval: 60000 }
	);
	return {
		result: data,
		error,
		isLoading: isLoading || isValidating,
		reload: mutate,
	};
};

/**
 * 员工搜索组件
 * <AUTHOR> copy and upgrade from bswang
 * @param {StuffSearchType} props
 * @prop {string[]} [value] - 选中的员工id
 * @prop {(value: string[]) => void} [onChange] - 选中员工id变化回调
 * @returns {React.ReactElement}
 */
export default function StuffSearchTDesign(props: StuffSearchType) {
	const { value, onChange, emptyErrorMsg, disable } = props;
	const [name, setName] = useState('a');
	const [options, setOptions] = useState([]);
	const { result, isLoading } = fetchStuff(name);

	useEffect(() => {
		if (result) {
			if (result?.Response?.UserList) {
				const newOptions = result?.Response.UserList?.map(item => ({
					value: item.EnName,
					label: item.FullName,
				}));
				setOptions(newOptions);
			} else {
				setOptions([]);
			}
		}
	}, [result]);

	/**
	 * @description:  Search input callback
	 * @param {string} val search value
	 * @return {void}
	 */
	const onSearch = (val) => {
		setName(val);
	};
	return (
		<>
			<Select
				filterable
				disabled={disable}
				options={options}
				value={value}
				multiple
				placeholder="输入关键词搜索"
				onSearch={onSearch}
				// loading={isLoading}
				popupProps={{
					attach:
						document.getElementById('list-body')
						|| document.getElementById('app-header-body')
						|| (document.body as any),
				}}
				onChange={(value) => {
					onChange?.(value as any);
				}}
			/>
			{ ((!value || !value.length) && emptyErrorMsg) ? <span style={{ color: 'red', fontSize: '13px', marginTop: 2 }}>{emptyErrorMsg || '请选择'}</span> : <span /> }
		</>
	);
}
