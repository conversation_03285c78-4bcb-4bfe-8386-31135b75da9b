import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Modal, Button, Input, message, Text } from '@tencent/tea-component';
import { CreateSpecialAfterSaleArchApproval, DescribeSpecialAfterSaleArchApproval } from '@src/api/advisor/guard';
import { getProcessEnv } from '../../../../../app/utils';
interface Props {
	// 1 代表是提单时特批，2 代表是售后确认架构图特批
	type?: number
	guardData?: any;
	approvalCallBack?: Function;
	updateApprovalData?: Function;
	appId?: number;
}
function SpecialApprovalModal({ type = 1, guardData, approvalCallBack = () => {}
	, updateApprovalData = () => {}, appId }: Props, ref) {
	const reasonName = type === 1 ? 'Reason' : 'AfterSaleReason';

	// 弹框是否显示
	const [visible, setVisible] = useState(false);
    	// 特殊审批原因
	const [reason, setReason] = useState<string>('');
	// 特殊审批按钮loading
	const [btnLoading, setBtnLoading] = useState(false);

	// 暴露回调函数给父组件
	useImperativeHandle(ref, () => ({
		showModal: () => {
			setVisible(!visible);
		},
	}));

	// 查询审批原因
	async function getReason() {
		try {
			const params = {
				GuardID: guardData.GuardId,
				Operator: localStorage.getItem('engName') || '',
				AppId: appId,
			};
			const res = await DescribeSpecialAfterSaleArchApproval(params);
			if (res.Error) {
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			// 默认填充架构师填写的原因
			setReason(res?.SpecialAfterSaleArchApproval?.Reason);
			// 将审批信息回调给父组件
			updateApprovalData(res?.SpecialAfterSaleArchApproval);
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	}

	// 申请特殊审批
	async function specialApproval() {
		setBtnLoading(true);
		try {
			const params = {
				GuardId: guardData.GuardId,
				[reasonName]: reason,
				AppId: appId,
			};
			const res = await CreateSpecialAfterSaleArchApproval(params);
			if (res.Error) {
				setBtnLoading(false);
				const msg = res.Error.Message;
				message.error({ content: msg });
				return;
			}
			// 刷新审批数据
			getReason();
			setBtnLoading(false);
			setVisible(false);
			// 回掉父组件的回掉函数
			approvalCallBack();
		} catch (err) {
			setBtnLoading(false);
			const msg = err.msg || err.toString() || '未知错误';
			message.error({ content: msg });
		}
	}

	useEffect(() => {
		if (getProcessEnv() !== 'production-abroad') {
			getReason();
		}
	}, []);

	return (
		<Modal visible={visible} caption="是否确认提交特殊审批？" size='s' className='noArchModalWrap' onClose={() => {
			setVisible(false);
		}}>
			<Modal.Body>
				<div className='messageWrap'>
					<div className='firstLine'>请引导客户开通授权，以完成<Text theme="primary">辅助生成架构图</Text></div>
					<div className='secondLine'>无法引导客户开通授权？请填写原因：</div>
					<Input
						size='full'
						value={reason}
						onChange={(value) => {
							setReason(value);
						}}
						placeholder={type === 1 ? '请填写需要特殊审批的原因' : '请填写无法创建架构图，需要特殊审批的原因'}
					/>
				</div>
			</Modal.Body>
			<Modal.Footer>
				<Button type="primary" disabled={!reason} loading={btnLoading} onClick={specialApproval}>{type === 1 ? '申请特批' : '确认提交'}</Button>
				<Button type="weak" onClick={() => {
					setVisible(false);
				}}>取消</Button>
			</Modal.Footer>
		</Modal>
	);
}

export default forwardRef(SpecialApprovalModal);
