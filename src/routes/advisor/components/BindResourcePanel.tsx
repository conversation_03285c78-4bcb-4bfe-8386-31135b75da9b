import React, { useState, useContext, useEffect, useRef, useMemo } from 'react'
import { app } from '@tea/app'
//import { t, Trans } from '@tea/app/i18n'
import { Text } from '@tencent/tea-component/lib/text'
import { Icon } from '@tencent/tea-component/lib/icon'
import { Modal } from '@tea/component/modal'
import { Button } from '@tea/component/button'
import { Segment } from '@tencent/tea-component/lib/segment'

import { Card, Transfer, Table, SearchBox, Select, Form, Bubble, SelectMultiple, message } from "@tencent/tea-component";
const { selectable, removeable, scrollable, autotip } = Table.addons;

import { getProductRegionZoneOptions, getResourceByResourceId, getResourceByTag } from '@src/api/architecture/architecture';

// 源表列定义

const sourceColumns = [
	{
		key: "instance",
		header: "ID/实例名",
		render: resource => (
			<>
				<p>
					<a>{resource.instanceId}</a>
				</p>
				<p>{resource.instanceName}</p>
			</>
		),
	},
	{
		key: "status",
		header: "状态",
		width: 100,
		render: resource => {
			switch (resource.status) {
				case "running":
					return <span style={{ color: "green" }}>运行中</span>;
				case "stopped":
					return <span style={{ color: "red" }}>已关机</span>;
			}
			return resource.status;
		},
	},
];

// 源表
function SourceTable({ dataSource, targetKeys, onChange, loading, error }) {
	return (
		<Table
			records={dataSource}
			recordKey="instanceId"
			rowDisabled={record => record.status === "stopped"}
			rowClassName={record => record.status}
			columns={sourceColumns}
			addons={[
				scrollable({
					maxHeight: 310,
					onScrollBottom: () => console.log("到达底部"),
				}),
				selectable({
					value: targetKeys,
					onChange,
					rowSelect: true,
				}),
				autotip({
					isLoading: loading,
					isError: error,
				}),
			]}
		/>
	);
}
// 目标表列定义
const targetColumns = [
	{
		key: "instance",
		header: "ID/实例名",
		render: resource => (
			<>
				<p>
					<a>{resource.instanceId}</a>
				</p>
				<p>{resource.instanceName}</p>
			</>
		),
	},
	{
		key: "status",
		header: "状态",
		width: 100,
		render: resource => {
			switch (resource.status) {
				case "running":
					return <span style={{ color: "green" }}>运行中</span>;
				case "stopped":
					return <span style={{ color: "red" }}>已关机</span>;
			}
			return resource.status;
		},
	},
];

// 目标表
function TargetTable({ dataSource, onRemove, loading, error }) {
	return (
		<Table
			records={dataSource}
			recordKey="instanceId"
			columns={targetColumns}
			addons={[
				removeable({ onRemove }),
				autotip({
					isLoading: loading,
					isError: error,
				}),
			]}
		/>
	);
}

/*
export function BindResourcePanel({ appid, bindResource }) {
	// 绑定资源编辑框是否可见
	const [editVisiable, setEditVisiable] = useState(false);

	// 查询资源标志位
	const [queryFlag, setQueryFlag] = useState(true);
	// 表格数据加载状态标志位
	const [loading, setLoading] = useState(true);
	// 表格数据加载异常标志位
	const [error, setError] = useState(false);

	//搜索内容
	const [inputValue, setInputValue] = useState("");

	// 产品选项列表
	const [productOptions, setProductOptions] = useState([]);
	// 选中产品列表
	const [currentProducts, setCurrentProducts] = useState([]);

	// 地域选项列表
	const [regionOptions, setRegionOptions] = useState([]);
	// 选中地域列表
	const [currentRegions, setCurrentRegions] = useState([]);

	// 可用区选项列表
	const [zoneOptions, setZoneOptions] = useState([]);
	// 选中可用区列表
	const [currentZones, setCurrentZones] = useState([]);

	// 查找方式选项列表
	const [searchTypeOptions, setSearchTypeOptions] = useState([]);
	// 选中查找方式
	const [currentSearchType, setCurrentSearchType] = useState("resourceid");

	// 获取的 resource id 列表
	const [resourceIdList, setResourceIdList] = useState([]);
	// 已选绑定的 Resource ID key 列表
	const [targetResourceIdKeys, setTargetResourceIdKeys] = useState([]);

	// 获取未绑定的 tag 列表
	const [tagList, setTagList] = useState([]);
	// 已选绑定的 Tag key 列表
	const [targetTagKeys, setTargetTagKeys] = useState([]);


	// 初始化所有选项列表
	const initOptions = async () => {
		// 调用后台接口获取 appid 的产品列表、地域列表、可用区列表
		try {
			const res = await getProductRegionZoneOptions({
				AppId: appid,
			})
			if (res.Error) {
				let msg = res.Error.Message
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });
				return
			} else {
				// 成功
				setProductOptions(res.Products);
				setRegionOptions(res.Regions);
				setZoneOptions(res.Zones);
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });
		}
		// 设置搜索方式选项列表
		setSearchTypeOptions([{ value: "resourceid", text: "Resource ID" }, { value: "tag", text: "标签" }]);
	};

	useEffect(() => {
		initOptions();
	}, []);


	// 获取 ResourceID 列表
	const 
	= async () => {
		// 调用后端接口获取全量 ResourceID 列表及已绑定 ResourceID 列表
		try {
			setLoading(true)
			setError(false)

			const res = await getResourceByResourceId({
				AppId: appid,
				Products: currentProducts,
				Regions: currentRegions,
				Zones: currentZones,
			})
			if (res.Error) {
				let msg = res.Error.Message
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });

				setLoading(false)
				setError(true)
				return
			} else {
				// 成功
				setResourceIdList(res.ResourceList);

				let tmp = []
				res.BindResourceList.forEach(i => {
					tmp.push(i.instanceId)
				})
				setTargetResourceIdKeys(tmp)

				setLoading(false)
				setError(false)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });

			setLoading(false)
			setError(true)
		}
	};

	// 获取 Tag 列表
	const getTagList = async () => {
		// 调用后端接口获取全量 tag 列表及已绑定 tag 列表
		try {
			setLoading(true)
			setError(false)

			const res = await getResourceByTag({
				AppId: appid,
				Products: currentProducts,
				Regions: currentRegions,
				Zones: currentZones,
			})
			if (res.Error) {
				let msg = res.Error.Message
				// app.tips.error(t('{{msg}}', { msg }))
				message.error({ content: msg });

				setLoading(false)
				setError(true)
				return
			} else {
				// 成功
				setTagList(res.TagList);

				let tmp = []
				res.BindTagList.forEach(i => {
					tmp.push(i.instanceId)
				})
				setTargetTagKeys(tmp)

				setLoading(false)
				setError(false)
			}
		} catch (err) {
			const msg = err.msg || err.toString() || "未知错误"
			// app.tips.error(t('{{msg}}', { msg }))
			message.error({ content: msg });

			setLoading(false)
			setError(true)
		}
	};

	// 点击查询后，更新查找的结果
	useEffect(() => {
		if (currentSearchType === "resourceid") {
			getResourceIdList();
		} else if (currentSearchType === "tag") {
			getTagList();
		} else {
			console.log(appid);
			console.log(currentSearchType);
		}

	}, [queryFlag]);

	return (
		<>
			<Text
				style={{ display: 'inline-flex', alignItems: 'center', cursor: 'pointer', marginLeft: 30 }}
				onClick={(e) => {
					setEditVisiable(true);
				}}
			>
				<Text style={{ fontSize: 12, color: '#006cff' }}>
					<span>绑定资源</span>
				</Text>
			</Text>

			<Modal visible={editVisiable} size={"l"} onClose={() => setEditVisiable(false)} caption="绑定资源">
				<Modal.Body>
					<div>
						<Card>
							<Card.Body>
								<Form style={{ marginTop: 20 }} layout={"inline"}>
									<Form.Item label="产品：">
										<SelectMultiple
											value={currentProducts}
											onChange={(value) => { setCurrentProducts(value); }}
											options={productOptions}
											allOption={{ value: '', text: 'All' }}
											searchable={true}
										/>
									</Form.Item>
									<Form.Item label="地域：">
										<SelectMultiple
											value={currentRegions}
											onChange={(value) => { setCurrentRegions(value); }}
											options={regionOptions}
											allOption={{ value: '', text: 'All' }}
											searchable={true}
										/>
									</Form.Item>
									<Form.Item label="可用区：">
										<SelectMultiple
											value={currentZones}
											onChange={(value) => { setCurrentZones(value); }}
											options={zoneOptions}
											allOption={{ value: '', text: 'All' }}
											searchable={true}
										/>
									</Form.Item>
									<Form.Item label="查找方式：">
										<Select
											style={{ marginLeft: 10 }}
											boxSizeSync
											size="s"
											type="simulate"
											appearance="button"
											value={currentSearchType}
											onChange={(value) => { setCurrentSearchType(value); }}
											options={searchTypeOptions}
										/>
									</Form.Item>
									<Button
										type="primary"
										onClick={(e) => {
											setQueryFlag(!queryFlag);
										}}
									>
										查询
									</Button>
								</Form>
							</Card.Body>
						</Card>
						<Card>
							<Card.Body>
								{(currentSearchType === 'resourceid') && (
									<Transfer
										header={
											<>
											</>
										}
										leftCell={
											<Transfer.Cell
												scrollable={false}
												title="选择资源列表"
												// tip="支持按住 shift 键进行多选"
												header={
													<SearchBox
														value={inputValue}
														onChange={value => setInputValue(value)}
													/>
												}
											>
												<SourceTable
													dataSource={resourceIdList.filter(
														i =>
															i.instanceId.includes(inputValue) ||
															i.instanceName.includes(inputValue)
													)}
													targetKeys={targetResourceIdKeys}
													onChange={keys => setTargetResourceIdKeys(keys)}
													loading={loading}
													error={error}
												/>
											</Transfer.Cell>
										}
										rightCell={
											<Transfer.Cell title={`已选择 (${targetResourceIdKeys.length})`}>
												<TargetTable
													dataSource={resourceIdList.filter(i => targetResourceIdKeys.includes(i.instanceId))}
													onRemove={key => setTargetResourceIdKeys(targetResourceIdKeys.filter(i => i !== key))}
													loading={loading}
													error={error}
												/>
											</Transfer.Cell>
										}
									/>
								)}

								{(currentSearchType === 'tag') && (
									<Transfer
										header={
											<>
											</>
										}
										leftCell={
											<Transfer.Cell
												scrollable={false}
												title="选择 Tag 列表"
												// tip="支持按住 shift 键进行多选"
												header={
													<SearchBox
														value={inputValue}
														onChange={value => setInputValue(value)}
													/>
												}
											>
												<SourceTable
													dataSource={tagList.filter(
														i =>
															i.instanceId.includes(inputValue) ||
															i.instanceName.includes(inputValue)
													)}
													targetKeys={targetTagKeys}
													onChange={keys => setTargetTagKeys(keys)}
													loading={loading}
													error={error}
												/>
											</Transfer.Cell>
										}
										rightCell={
											<Transfer.Cell title={`已选择 (${targetTagKeys.length})`}>
												<TargetTable
													dataSource={tagList.filter(i => targetTagKeys.includes(i.instanceId))}
													onRemove={key => setTargetTagKeys(targetTagKeys.filter(i => i !== key))}
													loading={loading}
													error={error}
												/>
											</Transfer.Cell>
										}
									/>
								)}
							</Card.Body>
						</Card>
					</div>
				</Modal.Body>

				<Modal.Footer>
					<div>
						<Button
							style={{ marginRight: 5 }}
							type={"primary"}
							onClick={(e) => {
								bindResource()
								// setEditVisiable(false)
							}}
						>
							{'确认绑定'}
						</Button>
					</div>
				</Modal.Footer>
			</Modal>
		</>

	);
}
*/
