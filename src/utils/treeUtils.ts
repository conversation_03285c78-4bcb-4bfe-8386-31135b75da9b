import { zip, unzip } from "./zip";

// 根据节点id查找当前节点
export function deepTree(tree, id) {
	var retNode = null;
	function deepSearch(tree, id) {
		for (var i = 0; i < tree.length; i++) {
			if (tree[i].children && tree[i].children.length > 0) {
				deepSearch(tree[i].children, id);
			}
			if (id === tree[i].id) {
				retNode = tree[i];
				break;
			}
		}
	}
	deepSearch(tree, id);
	return retNode;
}

// 查询当前节点的父辈级节点id集
export function getParentNodeId(array, id) {
	let stack = [];
	let going = true;

	let walker = (array, id) => {
		array.forEach((item) => {
			if (!going) return;
			stack.push(item["id"]);
			if (item["id"] === id) {
				going = false;
			} else if (item["children"]) {
				walker(item["children"], id);
			} else {
				stack.pop();
			}
		});
		if (going) stack.pop();
	};

	walker(array, id);

	return stack;
}

// 遍历树处理成扁平化的数据
export function traverseTree(node) {
	const queue = [...node];
	const result = [];
	while (true) {
		const next = queue.shift();
		if (!next) {
			break;
		}
		result.push({
			id: next.id,
			content: next.content,
		});
		if (Array.isArray(next.children)) {
			queue.push(...next.children);
		}
	}

	return result;
}

// 检查并处理当前的文件名
export function checkoutUploadFileName(
	filename,
	tree,
	activeNode,
	activeNodeParent
) {
	let addNodeId = getAddNodeId(activeNode, activeNodeParent);
	let finalId = addNodeId ? `${addNodeId}/${filename}}` : filename;
	let isRepeat = false;
	function deepSearch(tree) {
		for (var i = 0; i < tree.length; i++) {
			if (tree[i].children && tree[i].children.length > 0) {
				deepSearch(tree[i].children);
			}
			if (finalId === tree[i].id) {
				isRepeat = true;
				break;
			}
		}
	}
	deepSearch(tree);
	if (!isRepeat) {
		return filename;
	}
	const fileExtension = filename.substring(0, filename.lastIndexOf("."));
	const copyName = fileExtension + "_copy.tf";
	return checkoutUploadFileName(copyName, tree, activeNode, activeNodeParent);
}

//返回需要添加到的节点id
export function getAddNodeId(activeNode, activeNodeParent) {
	let addNodeId = activeNode ? activeNode.id : undefined;
	// @ts-ignore
	if (activeNode && activeNode.id && !activeNode.isFolder) {
		// @ts-ignore
		addNodeId = activeNodeParent.id || "";
	}
	return addNodeId;
}

// 排序算法，文件夹始终靠前,同为文件夹或文件时按字母顺序排序
export function sortTreeNodeData(treeNodeData) {
	const sort = (nodeData) => {
		nodeData.forEach((data) => {
			data.children && data.children.length > 0 && sort(data.children);
		});
		nodeData.sort((frontNode, nextNode) => {
			if (frontNode.isFolder && !nextNode.isFolder) {
				return -1;
			}
			if (!frontNode.isFolder && nextNode.isFolder) {
				return 1;
			}
			return frontNode.id < nextNode.id ? -1 : 1;
		});
	};
	sort(treeNodeData);
}

// 将对象的key的第一级目录截取掉
export function createTreeUtil(data) {
	if (typeof data !== "object") {
		throw "The returned tfTree is invalid";
	}
	let objectTree = {};
	for (let key in data) {
		const slashIndex = key.indexOf("/");
		const interceptKey = key.substr(slashIndex + 1);
		objectTree[interceptKey] = data[key];
	}
	return structureTree(objectTree);
}

function structureTree(data) {
	let tree = [];
	for (let key in data) {
		const splitKey = key.split("/");

		// 初始化头节点
		const node = {
			id: splitKey[0],
			content: splitKey[0],
			file: splitKey.length === 1 ? data[splitKey.join("/")] : "",
			children: [],
			isFolder: splitKey.length !== 1,
		};
		let children = node.children;
		for (let n = 1; n < splitKey.length; n++) {
			// 节点为空，代表上个节点是最深层文件夹，空节点不作处理
			if (splitKey[n] !== "") {
				children.push({
					id: splitKey.slice(0, n + 1).join("/"),
					content: splitKey[n],
					// 最后一个节点不为空则为文件
					file:
						splitKey.length === n + 1
							? data[splitKey.join("/")]
							: "",
					children: [],
					isFolder: splitKey.length !== n + 1,
				});
				children = children[0].children;
			}
		}
		tree.push(node);
	}

	let trueList = [];
	tree.forEach((item) => {
		let trueI = JSON.stringify(item);
		trueI = JSON.parse(trueI);
		if (
			trueList.findIndex((trueItem) => trueItem.id === trueI["id"]) !== -1
		) {
			let index = trueList.findIndex(
				(trueItem) => trueItem.id === trueI["id"]
			);
			trueList[index] = removeDuplicateFiles(trueList, trueI);
		} else {
			trueList.push(trueI);
		}
	});
	return trueList;
}

function removeDuplicateFiles(list, trueI) {
	let index = list.findIndex((item) => item.id === trueI.id);
	let trueItem = JSON.stringify(trueI);
	trueItem = JSON.parse(trueItem);
	// @ts-ignore
	if (trueItem.children && trueItem.children.length > 0) {
		// @ts-ignore
		trueItem.children = list[index].children.concat(trueI.children);
		// @ts-ignore
		let childrenList = JSON.parse(JSON.stringify(trueItem.children));
		let trueList = [];
		childrenList.forEach((item) => {
			let Item = JSON.stringify(item);
			Item = JSON.parse(Item);
			if (
				trueList.findIndex((trueItem) => trueItem.id === Item["id"]) !==
				-1
			) {
				trueList[
					trueList.findIndex((trueItem) => trueItem.id === Item["id"])
				] = removeDuplicateFiles(trueList, Item);
			} else {
				trueList.push(Item);
			}
		});
		// @ts-ignore
		trueItem.children = trueList;
	}
	return trueItem;
}

// 树结构数据转换为扁平化的对象数据
function treeDataToObject(fileList, baseName = "$TENCENT_IAC_ROOT") {
	let tfTree = {};
	fileList.forEach((item) => {
		if (item.children && item.children.length !== 0) {
			tfTree = Object.assign(
				{},
				tfTree,
				treeDataToObject(item.children, `${baseName}/${item.content}`)
			);
		} else if (item.isFolder) {
			tfTree[`${baseName}/${item.content}/`] = item.file;
		} else if (item.children && item.children.length === 0) {
			tfTree[`${baseName}/${item.content}`] = item.file;
		}
	});
	return tfTree;
}

// 将后端传来的压缩树数据解压为结构树数据，并进行排序
export function unzipTree(zipedTree) {
	const objTree = JSON.parse(unzip(zipedTree));
	if (typeof objTree !== "object") {
		throw new Error("返回的tfTree格式错误");
	}
	const treeData = createTreeUtil(objTree);
	sortTreeNodeData(treeData);
	return treeData;
}

export function zipTree(treeData) {
	const objTree = treeDataToObject(treeData);
	return zip(JSON.stringify(objTree));
}
