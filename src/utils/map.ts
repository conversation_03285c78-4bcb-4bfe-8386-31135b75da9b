// advisor产品名称映射
export const PRODUCT_MAP = new Map([
	['cvm', '云服务器（CVM）'],
	['cos', '对象存储（COS）'],
	['Elasticsearch Service', 'Elasticsearch Service'],
	['mysql', '云数据库（MySQL）'],
	['redis', '云数据库（Redis）'],
	['mongodb', '云数据库（MongoDB）'],
	['cbs', '云硬盘（CBS）'],
	['clb', '负载均衡（CLB）'],
	['cdn', '内容分发网络（CDN）'],
	['eip', '弹性公网 IP（EIP）'],
	['dayu', 'DDoS防护（DDos）'],
	['dayubgp', 'DDoS防护-高防IP（BGP）'],
	['tke', '容器服务（TKE）'],
	['cam', '访问管理（CAM）'],
	['as', '弹性伸缩（AS）'],
	['cfw', '云防火墙（CFW）'],
	['tdsql', '云数据库（TDSQL）'],
	['tdmq', '消息队列（TDMQ）'],
	['billing', '计费（BILLING）'],
	['live', '云直播(CSS)'],
	['vpc', '私有网络(VPC)'],
	['ckafka', '消息队列(Ckafka)'],
	['vpngw', 'VPN网关(VPNGW)'],
	['bwp', '共享带宽包(BWP)'],
]);

//上线状态
export const ONLINE_MAP = new Map([
	[1, '已上线'],
	[0, '未上线'],
]);

// 分组名称映射
export const GROUP_MAP = new Map([
	[-1, '总览'],
	[1, '安全'],
	[2, '可靠'],
	[3, '服务限制'],
	[4, '成本'],
	[5, '性能'],
]);

// 风险等级映射
export const LEVEL_MAP = new Map([
	[1, '低风险'],
	[2, '中风险'],
	[3, '高风险'],
]);

// 策略组标题映射
export const TAB_TITLE_MAP = new Map([
	['overview', '总览'],
	['security', '安全'],
	['architecture', '可靠'],
	['performance', '性能'],
	['cost', '成本'],
	['resource', '服务限制'],
]);

// 分组ID classkey映射
export const GROUPID_CLASSKEY_MAP = new Map([
	[-1, 'overview'],
	[1, 'security'],
	[2, 'architecture'],
	[3, 'resource'],
	[4, 'cost'],
	[5, 'performance'],
]);

// 资源表格唯一键映射
export const RESOURCE_TABLE_KEY_MAP = new Map([
	['mysql', 'InstanceId'],
	['cvm', 'InstanceId'],
	['mongodb', 'InstanceId'],
	['Elasticsearch Service', 'InstanceId'],
	['redis', 'InstanceId'],
	['clb', 'InstanceId'],
	['cbs', 'DiskId'],
	['cos', 'ID'],
	['cdn', 'ResourceId'],
	['eip', 'AddressId'],
]);

export const LINE_NAME_MAP = new Map([
	['ResourceStackCount', '资源栈总数'],
	['DraftCount', '草稿状态数'],
	['PlanSuccessCount', 'Plan成功数'],
	['PlanFailureCount', 'Plan失败数'],
	['ApplySuccessCount', 'Apply成功数'],
	['ApplyFailureCount', 'Apply失败数'],
	['DestroySuccessCount', 'Destroy成功数'],
	['DestroyFailureCount', 'Destroy失败数'],
	['ManagedResourcesCount', '托管的资源总数'],
	['Count', '授权用户总数'],
	['HighRiskCount', '高风险数量'],
	['MediumRiskCount', '中风险数量'],
	['HealthCount', '健康数量'],
	['IgnoreCount', '忽略数量'],
	['AssessedResourceTotal', '评估资源总数'],
	['AssessedStrategyTotal', '评估策略总数'],
]);

export const jobStatusMap = new Map([
	['Init', { classname: 'init', text: '编辑中', type: 'init' }],
	['DryRunning', { classname: 'running', text: '预执行中', type: 'dryRun' }],
	['DryRan', { classname: 'complete', text: '预执行成功', type: 'dryRun' }],
	['DryRunFailed', { classname: 'error', text: '预执行失败', type: 'dryRun' }],
	['Running', { classname: 'running', text: '执行中', type: 'run' }],
	['Ran', { classname: 'complete', text: '执行成功', type: 'run' }],
	['RunFailed', { classname: 'error', text: '执行失败', type: 'run' }],
	['InActive', { classname: 'init', text: '未命中', type: 'run' }],
]);

export const ENV_SOURCE_MAP = new Map([
	['INTERNAL_IMPORT', '腾讯云实例'],
	['CSV_IMPORT', '文件导入'],
	['IP_ADD', '实例IP'],
]);
