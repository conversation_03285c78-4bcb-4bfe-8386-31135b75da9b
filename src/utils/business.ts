import { processState } from './constants';
// 当前登陆用户英文名
const rtx = localStorage.getItem('engName');

// 查询是否有修改护航单基本信息
export function baseInfoModifyAllowed(currentGuard) {
	let stateAllowed = true;
	let guysAllowed = true;
	// 护航期间，草稿状态状态不允许修改
	if (currentGuard.Status <= processState.onNoteId || !currentGuard.GuardInfoSupportUpdate) {
		stateAllowed = false;
	}
	// TAM Leader
	const tamLeader = currentGuard?.Approvals?.AfterSalesStatus?.Handler?.split(';');
	// 不是护航负责人、tam leader，不允许修改
	if (currentGuard.Responser.indexOf(rtx) === -1 && tamLeader.indexOf(rtx) === -1) {
		guysAllowed = false;
	}
	return stateAllowed && guysAllowed;
}

// 查询有修改护航单基本信息的人员：护航负责人、TAM Leader
export function getBaseInfoEditor(currentGuard) {
	let guys = [currentGuard.Responser];
	guys = guys.concat(currentGuard?.Approvals?.AfterSalesStatus?.Handler?.split(';'));
	return Array.from(new Set(guys)).filter(i => i);
}

// 查询是否有修改护航单实例权限
export function isInstanceModifyAllowed(currentGuard) {
	let stateAllowed = true;
	let guysAllowed = true;

	// 护航期间，草稿状态和计算状态不允许修改
	if (
		!currentGuard.GuardInfoSupportUpdate
			|| currentGuard.Status <= processState.onNoteId
    	|| [processState.onRunningId, processState.instanceAltering, processState.instanceAlteredRun]
    		.includes(currentGuard.Status)) {
		stateAllowed = false;
	}
	// 护航负责人审批状态，允许修改。该状态较特殊
	if (processState.onRunningId === currentGuard.Status && !currentGuard.Approvals.AfterSalesStatus.IsConfirm) {
		stateAllowed = true;
	}

	// 有权限修改的人员
	const insEditorList = getInstanceEditor(currentGuard);
	if (insEditorList.indexOf(rtx) === -1) {
		guysAllowed = false;
	}

	return stateAllowed && guysAllowed;
}

// 查询是有修改护航单实例的人员：建单人、改单人、APPID负责人、审批人及售后指派人
export function getInstanceEditor(currentGuard) {
	let guys = [];

	guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Handler.split(';'));
	guys = guys.concat(currentGuard.Approvals.AfterSalesStatus.Supporter.split(';'));
	if (currentGuard.Approvals.ExpertStatus) {
		currentGuard.Approvals.ExpertStatus.map((i) => {
			guys = guys.concat(i.Handler.split(';'));
		});
	}
	if (currentGuard.Approvals.ScanResultStatus) {
		currentGuard.Approvals.ScanResultStatus.map((i) => {
			guys = guys.concat(i.Handler.split(';'));
		});
	}

	guys = guys.concat(currentGuard.CreatedBy.trim());
	guys = guys.concat(currentGuard.UpdatedBy.trim());
	guys = guys.concat(currentGuard.Responser.split(';'));

	return Array.from(new Set(guys)).filter(i => i !== '');
}

