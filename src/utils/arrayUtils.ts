import { LINE_NAME_MAP } from "./map";

//求两个数组的差集，注意入参顺序
export function getDiffSet(subtrahend, minuend) {
	if (!Array.isArray(subtrahend) || !Array.isArray(minuend)) {
		throw new Error("输入需为两数组");
	}
	let subSet = new Set(minuend);
	return subtrahend.filter((value) => !subSet.has(value));
}

// [{Date:'2020-01-02', UserCount: 1000}] => [{date:'2020-01-02', value: 1000, type: LINE_NAME_MAP.get('UserCount')}]
export function expandNumberList(numberList) {
	let result = [];
	numberList.forEach((numberObj) => {
		for (let key in numberObj) {
			if (key !== "Date") {
				result.push({
					date: numberObj.Date,
					value: numberObj[key],
					type: LINE_NAME_MAP.get(key),
				});
			}
		}
	});
	return result;
}
