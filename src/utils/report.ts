import Cookies from 'js-cookie';
import SingletonAutoTrackBeacon from './autotracker-beacon';
import moment from 'moment';
import { SESSION_USER_NAME, SESSION_DEPMENT } from '@src/configs/tree';

export interface ReportType {
	type: string; // 自定义click/run/config等
	target?: any; // 节点列表
	action: string; // 自定义动作名称
	extraInfo: string; // 其他自定义信息
}

const ENV = 'ISA';

export const getCurrTimeStr = () => moment(Date.now()).format('YY-MM-DD HH-mm-ss');

const getUserName = () => localStorage.getItem(SESSION_USER_NAME)
	|| Cookies.get('t_uid')
	|| Cookies.get('km_uid')
	|| Cookies.get('bk_uid');

const getDepMent = () => localStorage.getItem(SESSION_DEPMENT);
// 统一公共参数，基础上报
const platformReport = (eid: string, extraParams?: object) => {
	try {
		const instance = SingletonAutoTrackBeacon.getInstance();
		const currTime = getCurrTimeStr();
		const userName = getUserName();
		instance?.dtReport('at_click', {
			eid, // 事件名称
			remark: JSON.stringify({
				env: ENV,
				time: currTime,
				...extraParams,
			}),
			isaReportEnv: ENV,
			isaReportTime: currTime,
			isaReportUserName: userName,
			isaReportUserDepName: getDepMent(),
			isaReportPath: location.pathname,
			...extraParams,
		});
	} catch (e) {
		console.error(e.message);
	}
};

// 上报页面的访问
export const reportVisitPage = (params: any) => {
	platformReport('visitPage', params);
};

export const reportSitePv = (params: any) => {
	platformReport('visitSite', params);
};
