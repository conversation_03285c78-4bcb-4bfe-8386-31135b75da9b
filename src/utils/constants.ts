export const IS_INTL = location.hostname === 'isa-intl.woa.com' || location.hostname === 'isa-intl-pre.woa.com'; // 判断是否为国际站

// 权限链接
export const authUrl = 'https://console.cloud.tencent.com/advisor/auth';

// 生图状态
export const GENERATE_STATUS = {
	// 开始生成/生成中
	START: 'start',
	// 生成成功
	SUCCESS: 'finish',
	// 生成失败
	FAIL: 'fail',
};

// 自动保存时间
export const AUTO_SAVE_TIME = 30000;

// 流程状态ID
export const processState = {
	onNoteId: 1,              // 草稿状态ID
	submitId: 2,              // 订单已提交状态ID
	onSaleApprovalId: 31,     // 售后审批状态ID
	onRunningId: 32,          // 正在巡检中状态ID
	onExpertApprovalId: 33,   // 专项分配人员状态ID
	onResultApprovalId: 34,   // 巡检结果审核状态ID
	onReportGenerating: 36,   // 巡检报告生成中
	onReportGenerated: 37,    // 巡检报告已完成
	instanceAltering: 40,     // 实例修改中ID
	instanceAlteredRun: 41,   // 实例修改后运行中ID
	tamCheckResultId: 48,     // 巡检风险审批（TAM）
	onFinishId: 50,           // 护航巡检完成状态ID
	processStopId: -1,        // 流程中止状态ID
	scanFailedId: -2,         // 巡检异常状态ID
	approvalFailedId: -3,     // 审核异常状态ID
	deletedId: -50,           // 已删除
};
