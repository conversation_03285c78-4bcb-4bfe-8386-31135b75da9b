import { map, reduce, toPairs, includes } from 'lodash';
import { EX_SOURCE } from '@src/routes/architecture/conf/eidtorConfig';
import { IS_INTL } from '@src/utils/constants';
import { getStorage } from '@src/utils/storage';
import { whitelist, IS_GRAYSCALE } from '@src/constants';

// 是否是通过antool访问
export function fromAntool() {
	return includes(EX_SOURCE, getSearchParam('source', location));
}

export function getSearchParam(name, location) {
	const { search } = location;
	const nameArr = search.split(`${name}=`);
	if (nameArr.length === 2) {
		const searchParamArr = nameArr[1].split('&');
		return searchParamArr[0];
	}
	return null;
}

export function getUrlParamFromLocation(nameList, location) {
	const retObj = {};
	map(nameList, (item) => {
		retObj[item] = getSearchParam(item, location);
	});
	return retObj;
}

export function getUrlParamsStr(paramObj) {
	return reduce(toPairs(paramObj), (ret, [key, value]) => {
		ret.push(`${key}=${value}`);
		return ret;
	}, []).join('&');
}

export const queryStringObj: any = (str) => {
	if (!str) return {};
	const arrUrl = str?.split('?')[1].split('&');
	const objUrl = {};
	for (let m = 0; m < arrUrl.length; m++) {
		objUrl[arrUrl[m].split('=')[0]] = arrUrl[m].split('=')[1];
	}
	return objUrl;
};

// 判断是否应该使用迁移路由
export const shouldUseMigratedRoute = () => {
	// 国际站始终使用原路由
	if (IS_INTL) return false;

	// 如果不进行灰度，所有用户都使用新路由
	if (!IS_GRAYSCALE) return true;

	// 进行灰度时，只有白名单用户使用新路由
	const currentUser = getStorage('engName') || '';
	return whitelist.includes(currentUser);
};
