import AutoTrackInit, { TParamAutoTrack } from '@tencent/autotracker-beacon-oa';
import { getProcessEnv } from '../../app/utils';

const HostKeyMap = {
	local: '',
	test: '',
	pre: '',
	production: '0WEB05UZ8N0X503T',
};

export const getAutoTrackBeaconInst = (): AutoTrackInit | null | undefined => SingletonAutoTrackBeacon.getInstance();

/**
 * 单例模式-AutoTrackBeacon实例工厂
 */
export default class SingletonAutoTrackBeacon {
	private static instance: AutoTrackInit | null;

	static getInstance(config?:
	Partial<TParamAutoTrack> &
	{ commonParams?: {[key: string]: string} }): AutoTrackInit | null {
  	if (!this.instance) {
  		this.instance = SingletonAutoTrackBeacon.initInstance(config);
  	}

  	return this.instance;
	}

	// 额外增加通用字段-每条上报都会携带
	static addAdditionalParams(config: {[key: string]: string}): void {
  	if (this.instance) {
  		this.instance.addAdditionalParams(config);
  	} else {
  		this.instance = SingletonAutoTrackBeacon.getInstance(config);
  	}
	}

	private static initInstance(config?:
	Partial<TParamAutoTrack> &
	{ commonParams?: {[key: string]: string} }): AutoTrackInit | null {
  	const env = getProcessEnv();
  	const key = HostKeyMap[env as keyof typeof HostKeyMap];
  	let instance: AutoTrackInit | null = null;
  	if (key) {
  		instance = new AutoTrackInit({
  			report: {
  				appkey: key, // 从datahub获取的appkey
  				enableReport: () => true, // 上报条件，例如线上环境才上报
  				commonParams: {
  					// 自定义的上报公共参数, 每条上报都会携带
  					...config?.commonParams,
  					uid: config?.commonParams?.uid, // 业务用户身份标示，推荐使用uid作为key
  				},
  				consolelog: false,
  			},
  			// track: config?.track,
  			uselib: config?.uselib ?? [], // 预设了ui库track规则，包括omui,antd,element,tdesign等；不设置该项则没有预设规则，完全依据传入的track配置
  		});
  		instance.config('Click', {
  			autoSelector: ['#report'],
  		});
  		instance.init();
  		// @ts-ignore
  		// eslint-disable-next-line no-underscore-dangle
  		window.__ISA_CLOUD_ARCH_AutoTrackBeacon = instance; // 挂载instance对象，方便在DevTools控制台查看元信息
  	}
  	return instance;
	}
}
