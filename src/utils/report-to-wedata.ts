import axios from 'axios';
import { getProcessEnv } from '../../app/utils';

const GROUP_ID = 'gwe00031753761';
const STREAME_ID = 'swe000009442d0';

const httpUrl = 'https://trace.inlong.qq.com/hn0_csigcommon/dataproxy/message';

function objectToQueryString(obj) {
	return Object.keys(obj)
		.map(key => `${key}=${obj[key]}`)
		.join('&');
}

export const isaCommonReport = (params: any) => {
	const env = getProcessEnv();
	axios.post(httpUrl, {
		groupId: GROUP_ID,
		streamId: STREAME_ID,
		body: objectToQueryString({ ...params, env }),
		cnt: 1,
	});
};
