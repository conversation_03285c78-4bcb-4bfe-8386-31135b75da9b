import request from '../request';

// 新增故障条目
export function addFailureItem(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateFaultSheet' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询故障条目
export function getAllFailureItems(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeFaultSheet' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 删除某条故障记录
export function deleteFailureItem(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteFaultSheet' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 修改某条故障记录
export function modifyFailureItem(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyFaultSheet' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询故障条目
export function getFailureSummary(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeFaultSummary' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}