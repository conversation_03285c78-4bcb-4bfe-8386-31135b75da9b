import request from '../request';

/**
 * 新增报表
 * @param data
 */
export function CreateOperationalReport(data: {
	ReportInfo: {
		Service: string,
		Name: string,
		Url: string,
		Desc?: string
	},
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateOperationalReport' },
	});
}

/**
 * 查询报表
 * @param data
 */
export function DescribeOperationalReportList(data: {
	Services?: Array<string>,
	Name?: string,
	Limit: number,
	Offset: number,
	ShowError?: boolean,
	OnlyData?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeOperationalReportList' },
	});
}

/**
 * 编辑报表
 * @param data
 */
export function ModifyOperationalReport(data: {
	ReportInfo: {
		Service: string,
		Name: string,
		Url: string,
		Desc?: string
	},
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyOperationalReport' },
	});
}

/**
 * 删除报表
 * @param data
 */
export function DeleteOperationalReport(data: {
	Id: number,
	ShowError?: boolean,
	OnlyData?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteOperationalReport' },
	});
}

/**
 * 置顶报表
 * @param data
 */
export function CreateTopOperationalReport(data: {
  Id: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'CreateTopOperationalReport' },
  });
}

/**
 * 新增自定义报表
 * @param data
 */
export function CreatePersonalOperationalReport(data: {
  ReportInfo: {
    Service: string,
    Name: string,
    Url: string,
    Desc?: string
  },
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'CreatePersonalOperationalReport' },
  });
}

/**
 * 展示自定义报表
 * @param data
 */
export function DescribePersonalOperationalReportList(data: {
  Services?: Array<string>,
  Name?: string,
  Limit: number,
  Offset: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DescribePersonalOperationalReportList' },
  });
}

/**
 * 编辑自定义报表
 * @param data
 */
export function ModifyPersonalOperationalReport(data: {
  ReportInfo: {
    Service: string,
    Name: string,
    Url: string,
    Desc?: string
  },
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'ModifyPersonalOperationalReport' },
  });
}

/**
 * 删除自定义报表
 * @param data
 */
export function DeletePersonalOperationalReport(data: {
  Id: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DeletePersonalOperationalReport' },
  });
}

/**
 * 置顶自定义报表
 * @param data
 */
export function CreateTopPersonalOperationalReport(data: {
  Id: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'CreateTopPersonalOperationalReport' },
  });
}


