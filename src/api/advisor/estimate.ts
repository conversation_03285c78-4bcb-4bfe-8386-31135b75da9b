import {
	Config,
	TaskResult,
	CreateTask,
	LastTask,
	ModifyInstances,
	ModifyInstance,
	IgnoreInstances,
	RegionCodes,
	DownloadFileType,
	DownloadFileContent,
	TaskResultSummary,
	TaskResultDetail,
	UnsafeDetails,
	IgnoredDetails,
	DownloadFileAsync,
	DownloadFileResult,
	TaskProgressSummary,
	Tag,
} from '@src/types/advisor/estimate1';
import { Filter } from '@src/types/architecture/architecture';
import request from '../request';

// 描述平台的策略配置信息
export function getConfig(data: { AppId: number }): Promise<Config> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeConfig' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 发起评估任务
export function createScanTask(data: { AppId: number; Env: string }): Promise<CreateTask> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'CreateScanTask' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 拉取上一个最新的任务的ID
export function getLastTask(data: { AppId: number; Env: string }): Promise<LastTask> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeLastTask' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询任务执行的结果总览
export function getTaskResultSummary(data: {
	AppId: number;
	TaskId?: string;
	Filters?: Filter;
	TaskType?: string;
	MapUUId?: string;
	CloudMapID?: string;
	ReportType?: number;
}): Promise<TaskResultSummary> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeTaskSummary' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询架构图巡检结果
export function getArchTaskSummary(data: {
	AppId: number;
	ScanSource: number;
	TaskId?: string;
	Filters?: Filter;
	TaskType?: string;
	MapUUId?: string;
	ReportType?: number;
}): Promise<TaskResultSummary> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeArchitectureTaskSummary' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询任务执行的结果总览
export function getTaskProgressSummary(data: { AppId: number; TaskId: string }): Promise<TaskProgressSummary> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeTaskProgress' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 获取Excel下载结果
export function getReportFile(data: DownloadFileType): Promise<TaskProgressSummary> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DownloadReportFile' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询任务执行的有风险实例结果详情
export function getTaskResultStrategyUnsafeDetail(data: TaskResultDetail): Promise<TaskProgressSummary> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeTaskStrategyUnsafeDetail' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询任务执行的被忽略实例结果详情
export function getTaskResultStrategyIgnoredDetail(data: TaskResultDetail): Promise<IgnoredDetails> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeTaskStrategyIgnoredDetail' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 请求Excel异步下载
export function getReportFileAsync(data: DownloadFileType): Promise<DownloadFileAsync> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DownloadReportFileAsync' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			// console.log(e);
			let errData = {
				Error: { Message: '错误：下载失败，请重新刷新页面！' },
			};
			if (e.toString().indexOf('code 401')) {
				errData = {
					Error: { Message: data.AppId + ' 客户暂未授权报告解读！' },
				};
			}
			return errData;
		});
}

// 获取Excel异步下载结果
export function getReportResultAsync(data: { ResultId: string; AppId: number }): Promise<DownloadFileResult> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeDownloadTask' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			let errData = {
				Error: { Message: '错误：下载失败，请重新刷新页面！' },
			};
			return errData;
		});
}

// 查询地区对应的编码
export function getRegionCodes(data: { AppId: number }): Promise<RegionCodes> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'ListRegionCodes' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 修改单个忽略的实例
export function modifyIgnoreInstance(data: ModifyInstance): Promise<any> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'ModifyIgnoreInstance' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 修改忽略的实例列表
export function modifyIgnoreInstances(data: ModifyInstances): Promise<any> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'ModifyIgnoreInstances' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 获取标签键/标签值
export function getTags(data: { Product?: string; TaskId: string; AppId: number }): Promise<Tag> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeTags' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

//获取产品、维度配置信息
export function getProductsGroups(data: { AppId: number; Env?: string; TaskType?: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeProductConfigList' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

//获取产品、维度配置信息 （获取用户名不鉴权）
export function getCustomerName(data: { AppId: number }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCustomerInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			return e;
		});
}

// 创建用户报告模板
export function createReportTemplate(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateReportTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询用户所有报告模板
export function describeReportTemplateList(data: { AppId: number; Type?: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeReportTemplateList' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询用户某个报告模板具体信息
export function describeReportTemplate(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeReportTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 修改用户报告模板
export function modifyReportTemplate(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyReportTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 删除用户报告模板
export function deleteReportTemplate(data: { AppId: number; TemplateId?: number }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteReportTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询护航单已选产品下选择的实例
export function getInstances(data: { AllAppid?: boolean, AppId: number; GuardId?: number; Limit?: number; Offset?: number, }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardInstance' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}

// 查询护航单需要新增的实例
export function DescribeGuardAddedInstance(data): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardAddedInstance' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => e);
}
