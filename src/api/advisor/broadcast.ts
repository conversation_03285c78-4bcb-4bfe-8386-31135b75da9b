import request from '../request';
import {
	CombinedDataM,
	CombinedData,
	CombinedDataResponse,
	BroadcastRes,
	StrategysListParams,
	InterfaceTestParams,
	InterfaceTestRes,
	CreateStrategyParams,
	CreateStrategyRes,
	ModifyStrategyParams,
	ModifyStrategyRes,
	ConfigStrategysListParams,
	BroadcastConfigRes,
	MonitorProductRes,
	NodeMonitorMetricRes,
	ConfiguredMonitorRes,
	CreateMonitorParams,
	UpdateStrategyStatusParams,
	MetricStatisticsParams,
	DescribeUserListParams,
	DescribeUserListRes,
	MetricConfigListParams,
	UpdateChatBiMetricParams,
	ModifyMetricConfigParams,
	CreateMetricConfigParams,
	UpdateDimensionInfoListParams,
} from '@src/types/cloudEscort/broadcast/broadStrategy/api';

import {
	BroadcastListParams,
	BroadcastListRes,
	BroadcastListResultParams,
	BroadcastListResultRes,
	BroadcastContentParams,
	BroadcastContentRes,
	BaseInfoParams,
	BaseInfoRes,
	BroadcastUserParams,
	BroadcastUserRes,
	BroadcastResourcesParams,
	BroadcastResourcesRes,
	BroadcastOnlineParams,
	BroadcastOnlineRes,
	GuardInstanceParams,
	GuardInstanceRes,
	MetricConfigRes,
	MonitorProductsRes,
	MonitorMetricsRes,
	SupportBroadCastMetricRes,
	DescribeAllMetricsRes,
	DimensionInfoRes,
} from '@src/types/cloudEscort/broadcast/broadcastSub/api';

// 查询组合播报内容
export function DescribeCombinedBroadcastConfigs(data: CombinedData): Promise<CombinedDataResponse> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeCombinedBroadcastConfigs' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 修改组合播报内容
export function ModifyCombinedBroadcastConfigs(data: CombinedDataM) {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'ModifyCombinedBroadcastConfigs' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询播报策略列表
export function DescribeBroadcastStrategys(data: StrategysListParams): Promise<BroadcastRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeBroadcastStrategys' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询配置化播报策略列表
export function GetMonitorMetricStrategyList(data: ConfigStrategysListParams): Promise<BroadcastConfigRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeMonitorMetricStrategyList' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询指标监控配置列表
export function getMetricConfig(data: MetricConfigListParams): Promise<MetricConfigRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeMetricConfig' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 接口测试
export function startInterfaceTest(data: InterfaceTestParams): Promise<InterfaceTestRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'VerifyBroadcastStrategy' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 创建播报策略
export function CreateBroadcastStrategy(data: CreateStrategyParams): Promise<CreateStrategyRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'CreateBroadcastStrategy' },
	}).then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
			return e;
		});
}

// 修改播报策略
export function ModifyBroadcastStrategy(data: ModifyStrategyParams): Promise<ModifyStrategyRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'ModifyBroadcastStrategy' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询播报订阅列表
export function DescribeBroadcastLists(data: BroadcastListParams): Promise<BroadcastListRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeBroadcastLists' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询播报结果列表
export function DescribeBroadcastResults(data: BroadcastListResultParams): Promise<BroadcastListResultRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeBroadcastResults' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询播报订阅内容
export function getBroadcastSheet(data: BroadcastContentParams): Promise<BroadcastContentRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeBroadcastSheet' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 修改播报订阅-规则内容
export function ModifyBroadcastBaseInfo(data: BaseInfoParams): Promise<BaseInfoRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'ModifyBroadcastBaseInfo' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 修改播报订阅-策略列表内容
export function ModifyBroadcastUserStrategy(data: BroadcastUserParams): Promise<BroadcastUserRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'ModifyBroadcastUserStrategy' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 修改播报订阅-资源补充内容
export function ModifyBroadcastResources(data: BroadcastResourcesParams): Promise<BroadcastResourcesRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'ModifyBroadcastResources' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 修改播报订阅-资源补充内容
export function describeBroadcastLimits(data: {Products: string[]}): Promise<BroadcastResourcesRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeBroadcastLimits' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 修改播报订阅-状态内容
export function ModifyBroadcastOnline(data: BroadcastOnlineParams): Promise<BroadcastOnlineRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'ModifyBroadcastOnline' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 获取护航单下的产品实例
export function getGuardProductInstance(data: GuardInstanceParams): Promise<GuardInstanceRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data, Action: 'DescribeGuardProductInstance' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 获取监控产品列表
export function GetMonitorProductList(): Promise<MonitorProductRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { Action: 'DescribeMonitorProductList' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 获取全部指标名称
export function getAllMetrics(): Promise<DescribeAllMetricsRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { Action: 'DescribeAllMetrics' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询护航节点监控指标
export function GetGuardNodeMonitorMetric(data: {ProductId: string}): Promise<NodeMonitorMetricRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'DescribeGuardNodeMonitorMetric' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询指标下的维度列表
export function getDimensionInfoListByStrategyId(data: {StrategyId: number}): Promise<DimensionInfoRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'DescribeDimensionInfoListByStrategyId' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询指标下的维度列表
export function updateDimensionInfoListByStrategyId(data: UpdateDimensionInfoListParams): Promise<DimensionInfoRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'UpdateDimensionInfoListByStrategyId' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询护航节点监控指标
export function getSupportBroadCastMetric(): Promise<SupportBroadCastMetricRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { Action: 'DescribeSupportBroadCastMetric' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询云监控支持的云产品
export function getMonitorProduct(): Promise<MonitorProductsRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { Action: 'DescribeMonitorProduct' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询护航节点监控指标
export function GetConfiguredMonitorMetricStrategy(data: {StrategyId: number}): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'DescribeConfiguredMonitorMetricStrategy' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 创建护航节点监控指标
export function CreateMonitorMetricStrategy(data: CreateMonitorParams): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'CreateMonitorMetricStrategy' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 创建节点监控指标
export function createMetricConfig(data: CreateMetricConfigParams): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'CreateMetricConfig' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 更改护航节点监控指标
export function UpdateMonitorMetricStrategy(data: CreateMonitorParams): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'UpdateMonitorMetricStrategy' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 更改节点监控指标
export function modifyMetricConfig(data: ModifyMetricConfigParams): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'UpdateChatBiMonitorMetricStrategy' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 更改指标状态
export function UpdateMonitorMetricStrategyStatus(data: UpdateStrategyStatusParams): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'UpdateMonitorMetricStrategyStatus' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 更改指标状态
export function updateChatBiMetricStatus(data: UpdateChatBiMetricParams): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'UpdateChatBiMetricStatus' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 获取指标汇总
export function GetNodeMonitorMetricStatistics(data: MetricStatisticsParams): Promise<ConfiguredMonitorRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'DescribeNodeMonitorMetricStatistics' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 获取指标汇总
export function getMonitorMetrics(data: {Namespace: string}): Promise<MonitorMetricsRes> {
	return request({
		method: 'post',
		url: '/broadcast',
		data: { ...data,  Action: 'DescribeMonitorMetrics' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 获取员工RTX
export function DescribeUserList(data: DescribeUserListParams): Promise<DescribeUserListRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data,  Action: 'DescribeUserList' },
	})
		.then(data => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}
