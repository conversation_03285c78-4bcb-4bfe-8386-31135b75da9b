import request from '../request';
import {
	FilterParams,
	DetailFilterParams,
	ExamineInfoParams,
	CreateNoticeParams,
	FaultInformPopWinParams,
	TranslateDetailParams,
} from '@src/types/advisor/faultNotification';

/**
 * 查询故障列表
 * @param data
 */
export function getDescribeFaultLists(data: FilterParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'DescribeFaultLists' },
	});
}


/**
 * 查询我的通知
 * @param data
 */
export function getDescribeFaultByOwner(data: FilterParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'DescribeFaultByOwner' },
	});
}

/**
 * 查询故障详情
 * @param data
 */
export function getDescribeFaultDetail(data: DetailFilterParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'DescribeFaultDetail' },
	});
}


/**
 * 预览客户群推送信息
 * @param data
 */
export function getDescribeFaultInform(data: ExamineInfoParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'DescribeFaultInform' },
	});
}

/**
 * 预览客户群推送信息
 * @param data
 */
export function getFaultInformPopWin(data: FaultInformPopWinParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'DescribeFaultInformPopWin' },
	});
}

/**
 * 翻译内容
 * @param data
 */
export function getTranslateDetail(data: TranslateDetailParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'Translate' },
	});
}

/**
 * 推送客户群信息
 * @param data
 */
export function pushFaultInform(data: ExamineInfoParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'PushFaultInform' },
	});
}

/**
 * 获取产品信息
 * @param data
 */
export function getProductInfo(data): Promise<any> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeProductConfigList' },
	});
}

/**
 * 获取产品信息新接口
 * @param data
 */
export function getDescribeProductList(data): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeProductList' },
	});
}

/**
 * 获取故障通知模板
 * @param data
 */
export function getDescribeFaultTemplate(data): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'DescribeFaultTemplate' },
	});
}


/**
 * 创建故障通知
 * @param data
 */
export function createFaultNotice(data: CreateNoticeParams): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'CreateFaultNotice' },
	});
}

/**
 * 获取故障通知产品模板
 * @param data
 */
export function DescribeFaultProductTemplates(data: {
	AppId: number,
	Product?: string,
	OnlyData?: boolean,
	ShowError: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/fault',
		data: { ...data, Action: 'DescribeFaultProductTemplates' },
	});
}
