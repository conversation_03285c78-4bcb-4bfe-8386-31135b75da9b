import { DescribeOverview } from '@src/types/advisor/summary';
import request from '../request';

// 描述平台的策略配置信息
export function getSummaryMsg(data: {
	AppId: number;
	Env: string;
	Date: string;
	TaskType?: string;
	CloudMapID?: string;
	MapUUId?: string;
}): Promise<DescribeOverview> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeOverview' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}

// 云构架报告--运营端查询客户架构图信息
export function getArchReports(data: { AppId: number; Date: string }): Promise<DescribeOverview> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCustomerCloudMapOverview' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}
