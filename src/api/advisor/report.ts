import request from '../request';
import {
	DescribeGuardReportDashboardParams,
	CreateGuardReportDashboardParams,
	DescribeGuardReportSummaryParams,
	CreateGuardReportSummaryParams,
	UpdateGuardReportSummaryParams,
	DeleteGuardReportSummaryParams,
	DescribeGuardScanRiskSummaryParams,
	DescribeGuardReportAppidOverViewParams,
	CreateGuardReportModelParams,
	DescribeGuardReportModelParams,
	DescribeGuardReportSubscriptionParams,
	CreateGuardReportSubscriptionParams,
	DescribeGuardReportTitleParams,
	DescribeGuardReportAppidRsTrendParams,
	DescribeGuardReportRsLoadParams,
	UpdateGuardReportStrategyStateParams,
	ModifyGuardAlarmStateParams,
	UpdateGuardReportResultParams,
	UpdateGuardReportStrategySortParams
} from "@src/types/advisor/report";

// 删除护航单的应急预案
export function DeleteGuardReportEmergencyPlan(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DeleteGuardReportEmergencyPlan' },
	});
}

// 修改护航单的应急预案
export function UpdateGuardReportEmergencyPlan(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'UpdateGuardReportEmergencyPlan' },
	});
}

// 查询护航单的应急预案
export function DescribeGuardReportEmergencyPlan(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardReportEmergencyPlan' },
	});
}
// 查询护航单的无风险巡检项
export function DescribeGuardNoRiskStrategies(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardNoRiskStrategies' },
	});
}

// 添加无风险巡检项到重点关注风险列表
export function CreateGuardOtherRiskItem(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'CreateGuardOtherRiskItem' },
	});
}


// 日报预览生成
export function CreateGuardReportShow(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'CreateGuardReportShow' },
	});
}

// 日报预览生成进度
export function DescribeGuardReportShowTask(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardReportShowTask' },
	});
}

/**
 * 获取日报Dashboard的产品与指标
 * @param data
 */
export function getDescribeGuardReportDashboard(data: DescribeGuardReportDashboardParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportDashboard' },
	});
}

/**
 * 创建护航日报Grafana URL
 * @param data
 */
export function createGuardReportDashboard(data: CreateGuardReportDashboardParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardReportDashboard' },
	});
}

/**
 * 获取概览
 * @param data
 */
export function getDescribeGuardReportSummary(data: DescribeGuardReportSummaryParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportSummary' },
	});
}

/**
 * 创建概览
 * @param data
 */
export function createGuardReportSummary(data: CreateGuardReportSummaryParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardReportSummary' },
	});
}

/**
 * 创建概览
 * @param data
 */
export function updateGuardReportSummary(data: UpdateGuardReportSummaryParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateGuardReportSummary' },
	});
}

/**
 * 删除概览
 * @param data
 */
export function deleteGuardReportSummary(data: DeleteGuardReportSummaryParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteGuardReportSummary' },
	});
}

/**
 * 护航Top5风险
 * @param data
 */
export function getDescribeGuardScanRiskSummary(data: DescribeGuardScanRiskSummaryParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardScanRiskSummary' },
	});
}

/**
 * 更新Top护航隐患是否展示
 * @param data
 */
export function updateGuardReportStrategyState(data: UpdateGuardReportStrategyStateParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateGuardReportStrategyState' },
	});
}

/**
 * 更新重点关注风险项排序
 * @param data
 */
export function updateGuardReportStrategySort(data: UpdateGuardReportStrategySortParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateGuardReportStrategySort' },
	});
}

/**
 * 获取APPID资源概览、风险分布
 * @param data
 */
export function getDescribeGuardReportAppidOverView(data: DescribeGuardReportAppidOverViewParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportAppidOverView' },
	});
}

/**
 * 创建报告模板
 * @param data
 */
export function createGuardReportModel(data: CreateGuardReportModelParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardReportModel' },
	});
}

/**
 * 获取报告模板
 * @param data
 */
export function getDescribeGuardReportModel(data: DescribeGuardReportModelParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportModel' },
	});
}

/**
 * 获取订阅信息
 * @param data
 */
export function getDescribeGuardReportSubscription(data: DescribeGuardReportSubscriptionParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportSubscription' },
	});
}

/**
 * 创建订阅信息
 * @param data
 */
export function createGuardReportSubscription(data: CreateGuardReportSubscriptionParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardReportSubscription' },
	});
}

/**
 * 获取邮件标题
 * @param data
 */
export function getDescribeGuardReportTitle(data: DescribeGuardReportTitleParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportTitle' },
	});
}

/**
 * 获取APPID资源趋势
 * @param data
 */
export function getDescribeGuardReportAppidRsTrend(data: DescribeGuardReportAppidRsTrendParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportAppidRsTrend' },
	});
}

/**
 * 获取负载水位信息
 * @param data
 */
export function getDescribeGuardReportRsLoad(data: DescribeGuardReportRsLoadParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardReportRsLoad' },
	});
}

/**
 * 更新主动服务触达状态
 * @param data
 */
export function modifyGuardAlarmState(data: ModifyGuardAlarmStateParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'ModifyGuardAlarmState' },
	});
}

/**
 * 更新护航日报结果
 * @param data
 */
export function updateGuardReportResult(data: UpdateGuardReportResultParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateGuardReportResult' },
	});
}