/* eslint-disable indent */
/* eslint-disable max-len */
import { AxiosResponse } from 'axios';
import request from '../request';
export interface DescribeUserListRes {
	UserList: {
		CnName: string;
		EnName: string;
		FullName: string;
	}[];
}

export interface DescribeUserListParams {
	Name: string;
	Limit?: number;
}

interface ErrorType {
	RequestId: string;
	Error?: {
		Code: number;
		Message: string;
	};
}

export interface ResponseResult<T> {
	Response: T & ErrorType;
}

/**
 * 获取用户信息
 * @param {Object} data
 * @param {string} data.Name - 用户名
 * @param {number} [data.Limit] - 一页多少个
 * @returns {Promise<ResponseResult<DescribeUserListRes>>}
 */
export function describeUserList(data: DescribeUserListParams): Promise<ResponseResult<DescribeUserListRes>> {
	return new Promise((resolve, reject) => request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeUserList' },
	})
		.then((response: AxiosResponse<ResponseResult<DescribeUserListRes>>) => {
			resolve(response.data);
		})
		.catch(error => reject(error)));
}

