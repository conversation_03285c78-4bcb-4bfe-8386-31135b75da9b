import request from '../request';
import {
	DescribeMonitorConfigInfoParams,
	DescribeStrategiesParams,
	ModifyMonitorConfigInfoParams,
	ModifyStrategyInfoParams,
	UpdateConfigToProductionParams,
	DescribeProductConfigInfoParams,
	ModifyProductConfigInfoParams,
} from '@src/types/advisor/inspectionRelease';

/**
 * 查询策略列表
 * @param data
 */
export function DescribeStrategies(data: DescribeStrategiesParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStrategyConfigInfo' },
	});
}

/**
 * 策略编辑接口
 * @param data
 */
export function ModifyStrategyInfo(data: ModifyStrategyInfoParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyStrategyInfo' },
	});
}

/**
 * 发布接口（策略、监控、产品）
 * @param data
 */
export function UpdateConfigToProduction(data: UpdateConfigToProductionParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateConfigToProduction' },
	});
}

/**
 * 策略监控配置查询接口
 * @param data
 */
export function DescribeMonitorConfigInfo(data: DescribeMonitorConfigInfoParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeMonitorConfigInfo' },
	});
}

/**
 * 策略监控配置编辑
 * @param data
 */
export function ModifyMonitorConfigInfo(data: ModifyMonitorConfigInfoParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyMonitorConfigInfo' },
	});
}

/**
 * 产品列表查询
 * @param data
 */
export function DescribeProductConfigInfo(data: DescribeProductConfigInfoParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeProductConfigInfo' },
	});
}

/**
 * 产品列表编辑
 * @param data
 */
export function ModifyProductConfigInfo(data: ModifyProductConfigInfoParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyProductConfigInfo' },
	});
}

/**
 * 获取产品信息新接口
 * @param data
 */
export function getDescribeProductList(data): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStrategyProductList' },
	});
}
