import { getAdvisorCustomerProps, getAdvisorCustomerRes } from '@src/types/advisor/overview';
import { listAdvisorAllScanStrategiesProps, listAdvisorAllScanStrategiesRes } from '@src/types/advisor/overview';
import { modifyAdvisorAuthorizedUserStrategyIdsProps, modifyAdvisorAuthorizedUserStrategyIdsRes } from '@src/types/advisor/overview';
import { listAdvisorRiskCountProps, listAdvisorRiskCountRes } from '@src/types/advisor/overview';
import request from '../request';

export function getAdvisorCustomer(data: getAdvisorCustomerProps): Promise<getAdvisorCustomerRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ListAdvisorCustomerInfo' },
	}).then((data) => data.data.Response);
}

export function listAdvisorAllScanStrategies(data: listAdvisorAllScanStrategiesProps): Promise<listAdvisorAllScanStrategiesRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ListAdvisorAllScanStrategies' },
	}).then((data) => data.data.Response);
}

export function modifyAdvisorAuthorizedUserStrategyIds(data: modifyAdvisorAuthorizedUserStrategyIdsProps): Promise<modifyAdvisorAuthorizedUserStrategyIdsRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyAdvisorAuthorizedUserStrategyIds' },
	}).then((data) => data.data.Response);
}

export function listAdvisorRiskCount(data: listAdvisorRiskCountProps): Promise<listAdvisorRiskCountRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ListAdvisorRiskCount' },
	}).then((data) => data.data.Response);
}

export function listAccountScanResult(data: {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeAccountScanTask' },
	}).then((data) => data.data.Response);
}

export function DescribeTsaAuthInfoOverview(data: {
	Operator: string,
	ShowError: boolean,
	OnlyData: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeTsaAuthInfoOverview' },
	});
}
