import request from '../request';
const isDev = process.env.NODE_ENV == 'development';

/**
 * 查询服务列表
 * @param data
 */
export function DescribeTsaServiceList(data: {
  Page: number,
  PageSize: number,
  Filters: Array<{
    Key: string,
    Values: Array<string>
  }>,
  IsStrict?: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: `${'interface'}`,
    data: { ...data, Action: 'DescribeTsaServiceList' },
  });
}

/**
 * 查询服务名称列表
 * @param data
 */
export function DescribeTsaServiceNameList(data: {
  Page: number,
  PageSize: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DescribeTsaServiceNameList' },
  });
}

/**
 * 新建服务
 * @param data
 */
export function CreateTsaService(data: {
  ServiceName: string,
  Description: string,
  Url: string,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'CreateTsaService' },
  });
}

/**
 * 更新服务
 * @param data
 */
export function UpdateTsaService(data: {
  ServiceName: string,
  Description: string,
  Url: string
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'UpdateTsaService' },
  });
}

/**
 * 删除服务
 * @param data
 */
export function DeleteTsaService(data: {
  ServiceName: Array<string>,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DeleteTsaService' },
  });
}

/**
 * 查询API列表
 * @param data
 */
export function DescribeTsaApiList(data: {
  Page: number,
  PageSize: number,
  Filters: Array<{
    Key: string,
    Values: Array<string>
  }>,
  IsStrict?: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DescribeTsaApiList' },
  });
}

/**
 * 查询API列表
 * @param data
 */
export function CreateTsaApi(data: {
  ApiCName: string,
  ApiName: string,
  ApiRuleType: string,
  ApiType: string,
  IsAuth: number,
  ServiceName: string
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'CreateTsaApi' },
  });
}

/**
 * 更新API
 * @param data
 */
export function UpdateTsaApi(data: {
  ApiCName: string,
  ApiName: string,
  ApiRuleType: string,
  ApiType: string,
  IsAuth: number,
  ServiceName: string
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'UpdateTsaApi' },
  });
}

/**
 * 删除API
 * @param data
 */
export function DeleteTsaApi(data: {
  ApiName: Array<string>,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DeleteTsaApi' },
  });
}