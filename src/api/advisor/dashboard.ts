import request from '../request';

//资源大盘概览接口 InnerDescribeProductOverview
export function InnerDescribeProductOverview(data: { AppId: number, Date?: string, region: string, zone: string, env: string }): Promise<any> {
    return request({
        method: 'post',
        url: '/interface',
        data: { ...data, Action: 'InnerDescribeProductOverview' },
    }).then((data) => data.data.Response);
}

//资源大盘详情接口 InnerDescribeProductDetail
export function InnerDescribeProductDetail(data: { AppId: number, Product: string, Date?: string }): Promise<any> {
    return request({
        method: 'post',
        url: '/interface',
        data: { ...data, Action: 'InnerDescribeProductDetail' },
    }).then((data) => data.data.Response);
}

//获取地域和可用区的中英文对照字典 InnerDescribeRegionAndZone
export function InnerDescribeRegionAndZone(data: {}): Promise<any> {
    return request({
        method: 'post',
        url: '/interface',
        data: { ...data, Action: 'InnerDescribeRegionAndZone' },
    }).then((data) => data.data.Response);
}

//获取指定产品的评估项清单 InnerDescribeProductStrategy
export function InnerDescribeProductStrategy(data: { AppId: number, Product: string, Date?: string }): Promise<any> {
    return request({
        method: 'post',
        url: '/interface',
        data: { ...data, Action: 'InnerDescribeProductStrategy' },
    }).then((data) => data.data.Response);
}

//获取指定产品的隐患资源列表 InnerDescribeProductRiskData
export function InnerDescribeProductRiskData(data: { AppId: number, Product: string, Offset: number, Limit: number, StrategyId: number, Date?: string }): Promise<any> {
    return request({
        method: 'post',
        url: '/interface',
        data: { ...data, Action: 'InnerDescribeProductRiskData' },
    }).then((data) => data.data.Response);
}

//获取客户已有的地域和可用区列表 InnerDescribeCustomerRegionAndZone
export function InnerDescribeCustomerRegionAndZone(data: {}): Promise<any> {
    return request({
        method: 'post',
        url: '/interface',
        data: { ...data, Action: 'InnerDescribeCustomerRegionAndZone' },
    }).then((data) => data.data.Response);
}