import {
	GetStrategiesConfigProps,
	GetStrategiesConfigRes,
	CreateStrategiesConfigProps,
	CreateStrategiesConfigRes,
	DeleteStrategiesConfigProps,
	DeleteStrategiesConfigRes,
	ModifyStrategyConfigProps,
	ModifyStrategyConfigRes,
} from '@src/types/advisor/strategies';
import request from '../request';

export function getStrategiesConfig(data?: GetStrategiesConfigProps): Promise<GetStrategiesConfigRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStrategyConfigs' },
	}).then((data) => data.data.Response);
}

export function createStrategiesConfig(data: CreateStrategiesConfigProps): Promise<CreateStrategiesConfigRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'AddStrategyConfig' },
	}).then((data) => data.data.Response);
}

export function deleteStrategiesConfig(data: DeleteStrategiesConfigProps): Promise<DeleteStrategiesConfigRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteStrategyConfig' },
	}).then((data) => data.data.Response);
}

export function modifyStrategiesConfig(data: ModifyStrategyConfigProps): Promise<ModifyStrategyConfigRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyStrategyConfig' },
	}).then((data) => data.data.Response);
}

// 查询风险项信息和风险项客户分布
export function DescribeStrategyOperationInfo(data){
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStrategyOperationInfo' },
	}).then((data) => data.data.Response);
}

// 查询风险项趋势信息
export function DescribeStrategyTrendInfo(data){
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStrategyTrendInfo' },
	}).then((data) => data.data.Response);
}

// 查询存在风险的客户列表
export function DescribeCustomerAndRiskList(data){
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCustomerAndRiskList' },
	}).then((data) => data.data.Response);
}

// 查询风险项风险治理信息
export function DescribeRiskManageInfo(data){
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeRiskManageInfo' },
	}).then((data) => data.data.Response);
}

// 查询风险项风险治理趋势信息
export function DescribeRiskManageTrendInfo(data){
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeRiskManageTrendInfo' },
	}).then((data) => data.data.Response);
}

