import request from '../request';
import {
	DescribeEventListsParams,
	CreateEventParams,
	DescribeEventDetailParams,
	CreateEventApprovalParams,
	modifyEventCommentParams,
	CreateEventChatParams,
} from '@src/types/advisor/commonEvent';

/**
 * 查询共性事件列表
 * @param data
 */
export function getDescribeEventLists(data: DescribeEventListsParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'DescribeEventLists' },
	});
}

/**
 * 查询共性事件列表
 * @param data
 */
export function updateEvent(data: CreateEventParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'UpdateEvent' },
	});
}

/**
 * 查询共性事件详情
 * @param data
 */
export function getDescribeEventDetail(data: DescribeEventDetailParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'DescribeEventDetail' },
	});
}

/**
 * 查询自研隐患单详情
 * @param data
 */
export function DescribeInternalEventDetail(data: DescribeEventDetailParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'DescribeInternalEventDetail' },
	});
}

/**
 * 更新内部自研隐患单
 * @param data
 */
export function ModifyInternalEvent(data: CreateEventParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'ModifyInternalEvent' },
	});
}

/**
 * 更新内部自研隐患单群聊
 * @param data
 */
export function ModifyInternalEventChat(data: CreateEventChatParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'ModifyInternalEventChat' },
	});
}

/**
 * 审批共性单
 * @param data
 */
export function createEventApproval(data: CreateEventApprovalParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'CreateEventApproval' },
	});
}

/**
 * 更新客户【备注】
 * @param data
 */
export function modifyEventComment(data: modifyEventCommentParams): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'ModifyEventComment' },
	});
}

/**
 * 导出隐患事件详情
 * @param data
 */
export function CreateEventDownload(data: {
	Id: number,
	OnlyData?: boolean,
	ShowError?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'CreateEventDownload' },
	});
}

/**
 * 查询导出结果
 * @param data
 */
export function DescribeEventDownload(data: {
	Id: number,
	TaskId: string,
	OnlyData?: boolean,
	ShowError?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/event',
		data: { ...data, Action: 'DescribeEventDownload' },
	});
}
