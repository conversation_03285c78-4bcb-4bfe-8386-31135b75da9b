import request from '../request';
import { Staffing, StaffingModify } from '@src/types/cloudEscort/broadcast/guardConfig/specialStaffingApi';


// 查询默认专项人员
export function DescribeGuardExpertConfig(data: Staffing): Promise<any> {
    return request({
        method: 'post',
        url: '/guard',
        data: { ...data, Action: 'DescribeGuardExpertConfig' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 默认专项人员修改
export function UpdateGuardExpertConfig(data: StaffingModify): Promise<any> {
    return request({
        method: 'post',
        url: '/guard',
        data: { ...data, Action: 'UpdateGuardExpertConfig' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

