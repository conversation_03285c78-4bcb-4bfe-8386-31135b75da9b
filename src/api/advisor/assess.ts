import {
	getAssessResultProps,
	getAssessResultRes,
	getIgnoreResourcesProps,
	getIgnoreResourcesRes,
	getUnsafeResourcesProps,
	getUnsafeResourcesRes,
	getReportFileAsyncProps,
	getReportFileAsyncRes,
	getReportResultAsyncProps,
	getReportResultAsyncRes,
} from "@src/types/advisor/assess";
import request from "../request";

// 获取评估结果总览
export function getAssessResult(
	data: getAssessResultProps
): Promise<getAssessResultRes> {
	return request({
		method: "post",
		url: "/qcloudapi/",
		data: { ...data, Action: "DescribeTaskData" },
	}).then((data) => data.data.Response);
}

// 获取不安全的资源详情
export function getUnsafeResources(
	data: getUnsafeResourcesProps
): Promise<getUnsafeResourcesRes> {
	return request({
		method: "post",
		url: "/qcloudapi/",
		data: { ...data, Action: "DescribeUnsafeDetail" },
	}).then((data) => data.data.Response);
}

// 获取忽略的资源详情
export function getIgnoreResources(
	data: getIgnoreResourcesProps
): Promise<getIgnoreResourcesRes> {
	return request({
		method: "post",
		url: "/qcloudapi/",
		data: { ...data, Action: "DescribeIgnoredDetail" },
	}).then((data) => data.data.Response);
}

// 创建下载任务
export function getReportFileAsync(
	data: getReportFileAsyncProps
): Promise<getReportFileAsyncRes> {
	return request({
		method: "post",
		url: "/qcloudapi/",
		data: { ...data, Action: "DownloadReportFileAsync" },
	}).then((data) => data.data.Response);
}

// 获取下载结果
export function getReportResultAsync(
	data: getReportResultAsyncProps
): Promise<getReportResultAsyncRes> {
	return request({
		method: "post",
		url: "/qcloudapi/",
		data: { ...data, Action: "DescribeDownloadTask" },
	}).then((data) => data.data.Response);
}
