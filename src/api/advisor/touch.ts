import request from '../request';
import {
	DescribeAlarmPolicyParams,
	DescribeAlarmListsParams,
	DescribeAlarmDetailParams,
	ModifyAlarmParams,
	DescribeAlarmCustomersParams,
	DescribeCustomerPolicyParams,
	ModifyCustomerPolicyParams,
} from '@src/types/advisor/touch';

/**
 * 查询告警策略
 * @param data
 */
export function getDescribeAlarmPolicy(data: DescribeAlarmPolicyParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'DescribeAlarmPolicy' },
	});
}


/**
 * 查询告警列表
 * @param data
 */
export function getDescribeAlarmLists(data: DescribeAlarmListsParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'DescribeAlarmLists' },
	});
}

/**
 * 查看告警详情
 * @param data
 */
export function getDescribeAlarmDetail(data: DescribeAlarmDetailParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'DescribeAlarmDetail' },
	});
}


/**
 * 操作告警
 * @param data
 */
export function modifyAlarm(data: ModifyAlarmParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'ModifyAlarm' },
	});
}

/**
 * 查询告警客户名单
 * @param data
 */
export function getDescribeAlarmCustomers(data: DescribeAlarmCustomersParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'DescribeAlarmCustomers' },
	});
}

/**
 * 查询客户下的告警策略
 * @param data
 */
export function getDescribeCustomerPolicy(data: DescribeCustomerPolicyParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'DescribeAlarmCustomerPolicy' },
	});
}

/**
 * 编辑客户下的告警策略
 * @param data
 */
export function modifyCustomerPolicy(data: ModifyCustomerPolicyParams): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'ModifyAlarmCustomerPolicy' },
	});
}


/**
 * 获取产品信息
 * @param data
 */
export function getProductInfo(data): Promise<any> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeProductConfigList' },
	});
}


/**
 * 告警策略列表
 * @param data
 */
export function describeExternalAlarmInfo(data): Promise<any> {
	return request({
		method: 'post',
		url: '/alarm',
		data: { ...data, Action: 'DescribeExternalAlarmInfo' },
	});
}
