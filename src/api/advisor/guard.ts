import request from '../request';
import { ReInspectionParams, AfterSalesApprovalRet, ApprovalProgressRet, ApprovalResultRet, DeleteGuardScanResultParams, DescribeGuardScanResultParams, DescribeGuardScanResultRet, EmergencyInsertRecordParams, EmergencyPlanId, ExpertApprovalParams, ExpertApprovalRet, Filter, GuardResultParams, TamConfirmParams, HandleGuardScanResultParams, HandleGuardScanResultRet, ModifyGuardInstancesParams, ModifyGuardInstancesRet, PushGuardChatRet, TransferGuardApprovalParams, getGuardSheetRes, GuardParams, getCustomerNameRes, downloadReportRes, DescribeDownloadTaskRes, Projects, DescribeAdvisorHintParams, DescribeAdvisorHintResultRet } from '@src/types/advisor/guard';

// 针对巡检失败任务重新发起巡检
export function restartGuardScanTask(data: ReInspectionParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'RestartGuardScanTask' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询云架构图不支持的产品
export function DescribeArchNotSupportProduct(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeArchNotSupportProduct' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 生图状态
export function DescribeGuardMapGenStatus(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardMapGenStatus' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 新建架构图
export function CreateSharedArch(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateSharedArch' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 申请售后特殊审批
export function CreateSpecialAfterSaleArchApproval(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'CreateSpecialAfterSaleArchApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 特殊审批
export function UpdateSpecialAfterSaleArchApproval(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'UpdateSpecialAfterSaleArchApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询特殊审批信息
export function DescribeSpecialAfterSaleArchApproval(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeSpecialAfterSaleArchApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询售后确认信息
export function DescribeGuardAfterSalesConfirmApproval(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardAfterSalesConfirmApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}


// 查询产品实例加入架构图情况
export function ModifyGuardAfterSaleConfirmStatus(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'ModifyGuardAfterSaleConfirmStatus' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询产品实例加入架构图情况
export function DescribeArchGuardAddedInstance(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeArchGuardAddedInstance' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 保存总监
export function UpdateOnsiteGuardApprovalDirector(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'UpdateOnsiteGuardApprovalDirector' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询驻场护航配置
export function DescribeOnsiteGuardConfig(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeOnsiteGuardConfig' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询架构图列表
export function DescribeGuardArchList(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardArchList' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询护航单日历
export function DescribeGuardCalendar(data): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardCalendar' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询最近一次巡检
export function DescribeLastAdvisorTask(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeLastAdvisorTask' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 无地域的产品
export function DescribeGuardNoRegionProduct(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardNoRegionProduct' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 监控面板自动授予登录人读权限
export function DescribeGuardDashboard(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardDashboard' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询护航可修改的信息
export function DescribeGuardBaseConfig(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardBaseConfig' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 编辑护航单
export function UpdateGuardSheet(data: GuardParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateGuardSheet' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询护航类型对应服务
export function DescribeGuardServiceDetails(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardServiceDetails' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 创建/修改护航单
export function CreateGuardApprovalUrge(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'CreateGuardApprovalUrge' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询护航单
export function DescribeGuardSheet(data: { Filters: Array<Filter>, Offset: number,
	// eslint-disable-next-line max-len
	Limit: number, AppId?: number, StartTime?: string, EndTime?: string, Type?: string, Operator?: string }): Promise<getGuardSheetRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardSheet' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询云护航插件列表
export function DescribePluginListByAdmin(): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { Action: 'DescribePluginListByAdmin' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询云护航插件全量的白名单
export function GetListPluginConfigByAdmin(data): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ListPluginConfigByAdmin' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 驻场护航提交建草稿单
export function BindGuardSheet(data): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'BindGuardSheet' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 创建/修改护航单
export function CreateGuardSheet(data: GuardParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardSheet' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 创建/修改护航单单独保存实例接口
export function BindGuardInstance(data: any): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'BindGuardInstance' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 接口说明：防止直接通过链接跳转，在跳转前鉴权
export function CheckIfAuthed(data: {Operator: string;PageStatus: string;FilterName?: {
	Key?: string;
	Value?: string|number;
}}): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'CheckIfAuthed' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 删除护航单
export function deleteGuardSheet(data: { AppId?: number, Ids: Array<number>, Operator: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteGuardSheet' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 根据appid查询客户名称
export function GetAccountInfoByFields(data: { AppId: number }): Promise<getCustomerNameRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'GetAccountInfoByFields' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 获取账号的产品、地域、可用区列表
export function DescribeProductRegionAndZone(data: { AppId: number, Product?: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeProductRegionAndZone' },
	})
		.then(data =>
		// return testRes
			data.data.Response)
		.catch(e => e);
}

// 查询护航产品实例信息接口
export function DescribeGuardProductInstances(data: any): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardProductInstances' },
	})
		.then(data =>
		// return testRes
			data.data.Response)
		.catch(e => e);
}

// 查询护航产品的容量策略 DescribeGuardProductPolicy
export function DescribeGuardProductPolicy(data: { AppId: number }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardProductPolicy' },
	})
		.then(data =>
		// return testRes
			data.data.Response)
		.catch(e => e);
}

// 生成护航单报告 DownloadGuardReport
export function DownloadGuardReport(data: { GuardId: number, Env: string,
	Language: string, AppId: number }): Promise<downloadReportRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DownloadGuardReport' },
	})
		.then(data =>
		// return testRes
			data.data.Response)
		.catch(e => e);
}

// 查询护航单报告结果
export function DescribeGuardDownloadTask(data: { PublicResultId: string,
	PrivateResultId: string, AppId: number, GuardId: any }): Promise<DescribeDownloadTaskRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardDownloadTask' },
	})
		.then(data => data.data.Response)
		.catch(() => {
			const errData = {
				Error: { Message: '错误：下载失败，请重新刷新页面！' },
			};
			return errData;
		});
}

// 查询护航项目清单 DescribeGuardProjects
export function DescribeGuardProjects(data: { AppId: number }): Promise<Projects> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardProjects' },
	})
		.then(data =>
		// return testRes
			data.data.Response)
		.catch(e => e);
}

// 查询应急预案接口 DescribeGuardEmergencyPlan
export function DescribeGuardEmergencyPlan(data: { AppId: number, SearchWord: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeGuardEmergencyPlan' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询与拉取审核列表信息 DescribeGuardApprovalProgress
export function DescribeGuardApprovalProgress(data: { Filters: Array<Filter>, Offset: number,
	Limit: number, AppId?: number, StartTime?: string, EndTime?: string }): Promise<ApprovalProgressRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardApprovalProgress' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 拉取解决方案 DescribeGuardScanResult，
/**
 *
 * @param data
 * @returns
 *
 * 返回 ScanResult[].Instance 字段表示用户输入的巡检实例，不是结果实例
 */
export function DescribeGuardScanResult(data: { AppId: number, AppIds: Array<number>,
	GuardId: number, Product: string, Operator: string }): Promise<ApprovalResultRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardScanResult' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 拉取结果实例 DescribeGuardInstanceInfo
export function DescribeGuardInstanceInfo(data: { StrategyId: number, AppId: number,
	GuardId: number, Offset: number, Limit: number, RiskLevel?: number }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardInstanceInfo', Language: 'zh-CN' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}


// 创建或修改应急预案 CreateGuardEmergencyPlanItem
export function CreateGuardEmergencyPlanItem(data: { AppId?: number, GuardId: number,
	Item: Array<EmergencyInsertRecordParams> }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardEmergencyPlanItem' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}


// 删除应急预案 DeleteGuardEmergencyPlanItem
export function DeleteGuardEmergencyPlanItem(data: { AppId: number, GuardId: number,
	Product?: string, Ids: Array<EmergencyPlanId> }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteGuardEmergencyPlanItem' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}


// 更改审核状态 ModifyGuardScanResultStatus。其中，State 表示审批结果，1:同意，-1:驳回。
export function ModifyGuardScanResultStatus(data: { GuardId: number, AppId: number, Product: string,
	Operator?: string, State?: number, Remark?: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyGuardScanResultStatus' }, // 接口必须传递公共参数 AppId
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 创建或修改护航巡检结果
export function CreateGuardScanResult(data: GuardResultParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardScanResult' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}


// 删除护航巡检结果 DeleteGuardScanResult
export function DeleteGuardScanResult(data: DeleteGuardScanResultParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteGuardScanResult' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询售后审批信息
export function DescribeGuardAfterSalesApproval(data: { AppId?: number, GuardId: number,
	Operator: string }): Promise<AfterSalesApprovalRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardAfterSalesApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 新建售后审批信息
export function CreateGuardAfterSalesApproval(data: { ApprovalId: number, GuardId: number,
	AppId: number, IsAgree: boolean, Supporter: Array<string>, Reason: string, Operator: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardAfterSalesApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询专项审批信息
export function DescribeGuardExpertApproval(data: { AppId?: number, GuardId: number,
	ApprovalId: number, Operator: string, AllAppid?: boolean }): Promise<ExpertApprovalRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardExpertApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 存储专项审批信息
export function CreateGuardExpertApproval(data: ExpertApprovalParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateGuardExpertApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 专项结果审批流转
export function TransferGuardApproval(data: TransferGuardApprovalParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'TransferGuardApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 一键入群
export function PushGuardChat(data: { GuardId: number, AppId: number,
	User: Array<string>, Type?: string }): Promise<PushGuardChatRet> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'PushGuardChat' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 修改护航实例
export function modifyGuardInstances(data: ModifyGuardInstancesParams): Promise<ModifyGuardInstancesRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyGuardInstances' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}


// 查询全量巡检结果
export function DescribeGuardScanResults(data: DescribeGuardScanResultParams): Promise<DescribeGuardScanResultRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeGuardScanResults' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 处理巡检结果
export function submitGuardScanApproval(data: TamConfirmParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'SubmitGuardScanApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}


// 处理巡检结果
export function HandleGuardScanResult(data: HandleGuardScanResultParams): Promise<HandleGuardScanResultRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'HandleGuardScanResult' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 复制护航单
export function CopyOneGuard(data: { GuardId: number, Operator: string, AppId:
number, OnlyData?: boolean, ShowError?: boolean }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CopyOneGuard' },
	});
}

// 获取功能更新弹窗的内容信息
export function getDescribeAdvisorHint(data: DescribeAdvisorHintParams): Promise<DescribeAdvisorHintResultRet> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}
// 生成护航单
export function GenerateGuardSheetExport(data: { GuardId: number, Name: string, ListType: number, AppId: number }) {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DownloadListReport' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 查询护航单结果
export const GetGuardSheetDownload = (data: { ResultID: string; AppId: number }): Promise<any> => request({
	method: 'post',
	url: '/guard',
	data: { ...data, Action: 'DescribeListDownloadTask' },
})
	.then(data => data.data.Response)
	.catch((e) => {
		console.log(e);
		return e;
	});

//  获取能够创建的子流程的信息描述
export const DescribeGuardSubApprovalDetails = (data): Promise<any> => request({
	method: 'post',
	url: '/guard',
	data: { ...data, Action: 'DescribeGuardSubApprovalDetails' },
})
	.then(data => data.data.Response)
	.catch((e) => {
		console.log(e);
		return e;
	});

//  为某个主流程添加子流程
export const AdditionGuardSubApproval = (data): Promise<any> => request({
	method: 'post',
	url: '/guard',
	data: { ...data, Action: 'CreateGuardSubApproval' },
})
	.then(data => data.data.Response)
	.catch((e) => {
		console.log(e);
		return e;
	});

//  获取添加的子流程
export const DescribeGuardSubApproval = (data): Promise<any> => request({
	method: 'post',
	url: '/guard',
	data: { ...data, Action: 'DescribeGuardSubApproval' },
})
	.then(data => data.data.Response)
	.catch((e) => {
		console.log(e);
		return e;
	});

//  审批某个子流程
export const CreateGuardSubApproval = (data): Promise<any> => request({
	method: 'post',
	url: '/guard',
	data: { ...data, Action: 'CreateGuardSubApprovalSubmit' },
})
	.then(data => data.data.Response)
	.catch((e) => {
		console.log(e);
		return e;
	});

// 查询行业
export function DescribeIndustryList(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeIndustryList' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 修改护航投入天数
export function ModifyGuardActualTime(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'ModifyGuardActualTime' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 获取文件
export function DescribeGuardFile(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeGuardFile' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 同步架构图产品
export function DescribeArchGuardInstanceSync(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeArchGuardInstanceSync' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}

// 认领
export function UpdateGuardToTAMApproval(data): Promise<any> {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'UpdateGuardToTAMApproval' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}
