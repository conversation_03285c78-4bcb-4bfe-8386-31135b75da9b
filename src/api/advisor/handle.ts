import request from '../request';
import {
	DescribeRiskOverviewParams,
	DescribeRiskListsParams,
	DescribeTaskSummaryV2Params,
	DescribeToRecommendRiskListsParams, CreateRiskRecommendParams,
} from '@src/types/advisor/handle';
import { DescribeOverview } from "@src/types/advisor/summary";

// 当前进展和历史概览
export function DescribeRiskOverview(data: DescribeRiskOverviewParams): Promise<any> {
	return request({
		method: 'post',
		url: '/risk',
		data: { ...data, Action: 'DescribeRiskOverview' },
	});
}

// 风险治理列表
export function DescribeRiskLists(data: DescribeRiskListsParams): Promise<any> {
	return request({
		method: 'post',
		url: '/risk',
		data: { ...data, Action: 'DescribeRiskLists' },
	});
}

// 风险治理列表
export function DescribeToRecommendRiskLists(data: DescribeToRecommendRiskListsParams): Promise<any> {
	return request({
		method: 'post',
		url: '/risk',
		data: { ...data, Action: 'DescribeToRecommendRiskLists' },
	});
}

// 获取巡检项信息
export function DescribeTaskSummaryV2(data: DescribeTaskSummaryV2Params): Promise<any> {
	return request({
		method: 'post',
		url: '/risk',
		data: { ...data, Action: 'DescribeTaskSummaryV2' },
	});
}

// 添加处理建议、提醒客户处理、给客户推荐风险
export function CreateRiskRecommend(data: CreateRiskRecommendParams): Promise<any> {
	return request({
		method: 'post',
		url: '/risk',
		data: { ...data, Action: 'CreateRiskRecommend' },
	});
}

// 风险治理入口权限校验
export function DescribeRiskAuthorization(data: {
	AppId: number,
	Name: string,
	OnlyData?: boolean,
	ShowError?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/risk',
		data: { ...data, Action: 'DescribeRiskAuthorization' },
	});
}

// 风险治理入口权限校验
export function DescribeRiskManageStrategyDetail(data: {
	AppId: number,
	StrategyId: number,
	TaskId: string,
	RecommendId: string,
	ObjectType: string,
	OnlyData?: boolean,
	ShowError?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/risk',
		data: { ...data, Action: 'DescribeRiskManageStrategyDetail' },
	});
}

// 获取产品、维度配置信息 （获取用户名不鉴权）
export function getCustomerName(data: {
	AppId: number,
	OnlyData?: boolean,
	ShowError?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCustomerInfo' },
	});
}

// 描述平台的策略配置信息
export function getSummaryMsg(data: {
	AppId: number,
	OnlyData?: boolean,
	ShowError?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeOverview' },
	});
}

