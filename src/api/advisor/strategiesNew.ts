import request from '../request';
import {UpdateStrategyParams} from "@src/types/advisor/strategiesNew";

// 查询策略列表
export function DescribeStrategyList(data: {
	PageSize: number,
	Page: number,
	Filter: any,
	OnlyData?: boolean,
	ShowError?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStrategyList' },
	});
}

// 更新策略
export function UpdateStrategy(data: UpdateStrategyParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateStrategy' },
	});
}

// 新建策略
export function CreateStrategy(data: UpdateStrategyParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateStrategy' },
	});
}


// 查询策略
export function DescribeStrategy(data: {
	StrategyId: number,
	ShowError?: boolean,
	OnlyData?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStrategy' },
	});
}

// 查询策略
export function ProcessStrategy(data: {
	Operation: string,
	Reason?: string,
	StrategyId: number,
	ShowError?: boolean,
	OnlyData?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ProcessStrategy' },
	});
}

// 查询策略
export function GetUserRole(data: {
	ShowError?: boolean,
	OnlyData?: boolean
}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'GetUserRole' },
	});
}

// 获取策略项变更日志
export function DescribeStrategyOperateLog(data: {
  StrategyId: number,
  ShowError?: boolean,
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DescribeStrategyOperateLog' },
  });
}

// 获取上传COS的临时证书
export function DescribeCosTmpCredential(data: {
  MediaName: string;
  MediaType: string;
  ShowError?: boolean;
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'DescribeCosTmpCredentialForStrategy' },
  });
}

// 创建博文媒体
export function CreateStrategyMedia(data: {
  MediaId: string;
  MediaPath: string;
  MediaType: string;
  ThumbnailImagePath: string;
  ExtendAttribute?: {
    NodeColorFillType: number;
    ThumbnailType: number;
    ArchId: string;
  }
  ShowError?: boolean;
  OnlyData?: boolean
}): Promise<any> {
  return request({
    method: 'post',
    url: '/interface',
    data: { ...data, Action: 'CreateStrategyMedia' },
  });
}