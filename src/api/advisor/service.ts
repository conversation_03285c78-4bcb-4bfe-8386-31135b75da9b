import request from '../request';
import {
	DescribeIsaHighAvailabilityServiceOrderListParams,
	DescribeIsaHighAvailabilityServiceDetailParams,
	DescribeIsaHighAvailabilityServiceAssessParams,
	DescribeIsaHighAvailabilityServiceDoneParams,
	DescribeDeleteAttachmentParams,
	DescribeCosUploadUrlParams
} from "@src/types/advisor/service";

/**
 * 高可用服务查询页
 * @param data
 */
export function getDescribeIsaHighAvailabilityServiceOrderList(data: DescribeIsaHighAvailabilityServiceOrderListParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeIsaHighAvailabilityServiceOrderList' },
	});
}

/**
 * 高可用服务查询页
 * @param data
 */
export function getDescribeIsaHighAvailabilityServiceDetail(data: DescribeIsaHighAvailabilityServiceDetailParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeIsaHighAvailabilityServiceDetail' },
	});
}

/**
 * 评估按钮接口文档
 * @param data
 */
export function handleDescribeIsaHighAvailabilityServiceAssess(data: DescribeIsaHighAvailabilityServiceAssessParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeIsaHighAvailabilityServiceAssess' },
	});
}

/**
 * 完成
 * @param data
 */
export function handleDescribeIsaHighAvailabilityServiceDone(data: DescribeIsaHighAvailabilityServiceDoneParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeIsaHighAvailabilityServiceDone' },
	});
}

/**
 * 删除附件
 * @param data
 */
export function handleDescribeDeleteAttachment(data: DescribeDeleteAttachmentParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeDeleteAttachment' },
	});
}

/**
 * 获取cos上传文件url
 * @param data
 */
export function getDescribeCosUploadUrl(data: DescribeCosUploadUrlParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCosUploadUrl' },
	});
}

/**
 * 获取cos下载文件url
 * @param data
 */
export function getDescribeCosDownloadUrl(data: DescribeCosUploadUrlParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCosDownloadUrl' },
	});
}

