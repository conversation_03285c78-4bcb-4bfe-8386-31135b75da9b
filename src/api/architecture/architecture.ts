import request from '../request';
// import { getLanguageParam } from '@src/utils/getLanguageParam'
import {
	Filter,
	MapBaseInfo,
	MapItemParams,
	DescribeMapItemParams,
	CloudMapTemplate,
	CloudMapBaseTemplate,
	CloudMapPushTemplate
} from '@src/types/architecture/architecture';

interface DownloadListParams {
	AppId: number;
	ListType: number;
	Name: string;
	Filters: any;
}
interface DescribeInstancesParams {
	AppId: number;
	Uuid: string;
	MapUUId: string;
	Product?: string;
	Env: string;
	Filter?: Array<Filter>;
	Limit: number;
	Offset: number;
	BindingTypes: Array<string>;
}

interface GetResourceListParams {
	AppId: number;
	Uin: string;
	Product: string;
	Regions?: Array<string>;
	Zones?: Array<string>;
	Filters?: Array<Filter>;
	Limit?: number;
	Offset?: number;
}

interface NodeRiskInfo {
	AppId: number;
	TaskId: number;
	MapUUId: string;
	ItemUUIDList?: Array<string>;
	Operator: string;
}

// 获取编辑器组件
export const getCloudMapShapes = (data: { AppId: number }) => {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeCloudMapShapes' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 获取行业信息
export const DescribeCloudMapConfig = (data: { AppId: number }): Promise<any> => {
	return request({
		url: '/qcloud',
		method: 'post',
		data: { ...data, Action: 'DescribeCloudMapConfig' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((err) => {
			console.log(err);
			return err;
		});
};
// 获取绑定实例列表
export const DescribeCloudMapItemInstances = (data: DescribeInstancesParams) => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCloudMapItemInstances' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 架构图模板
// 获取模版信息
export const GetArchitectureTemplateList = (data: {
	AppId: number;
	Filters: Array<any>;
	Offset?: number;
	Limit?: number;
	FileType?: number;
}): Promise<any> => {
	return request({
		url: '/interface',
		method: 'post',
		data: { ...data, Action: 'DescribeCloudMapTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((err) => {
			console.log(err);
			return err;
		});
};

// 创建模版
export const CreateCloudMapTemplate = (data: CloudMapBaseTemplate): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateCloudMapTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 修改模版
export const ModifyCloudMapTemplate = (data: CloudMapTemplate): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyCloudMapTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 删除模版
export const DeleteCloudMapTemplate = (data: {
	AppId: number;
	MapTemplateUUIds: Array<string>;
	Operator: string;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteCloudMapTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 创建推送模版
export const CreatePushTemplate = (data: CloudMapPushTemplate): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreatePushTemplate' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};
// 云架构
// 创建云架构
export const CreateCloudMapBaseInfo = (data: MapBaseInfo): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateCloudMapBaseInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 查询云架构信息
export const DescribeCloudMapBaseInfo = (data: {
	AppId: number;
	Filters: Array<any>;
	Offset?: number;
	Limit?: number;
	FileType?: number;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCloudMapBaseInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 查询架构图详细信息
export const DescribeCloudMapDetailInfo = (data: {
	AppId: number;
	MapUUId: string;
	Operator: string;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCloudMapDetailInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 修改云架构信息
export const ModifyCloudMapBaseInfo = (data: MapBaseInfo): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyCloudMapBaseInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 删除云架构信息
export const DeleteCloudMapBaseInfo = (data: {
	AppId: number;
	MapUUIds: Array<string>;
	Operator: string;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteCloudMapBaseInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 获取TAM分组信息
export const DescribeTAMGroupInfo = (data: { AppId: number }): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeTAMGroupInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 获取协作人信息
export const DescribeCollaoratorInfo = (data: { AppId: number; Operator: string; MapUUId: string }): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCollaoratorInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 修改协作人信息
export const ModifyCollaoratorInfo = (data: {
	AppId: number;
	Operator: string;
	MapUUId: string;
	CollaoratorList: Array<string>;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyCollaoratorInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 获取员工部门信息
export const DescribeStaffDepartment = (data: { AppId: number; Operator: string }): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeStaffDepartment' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 快照获取主架构图信息
export const DescribeMainMapBySubMapId = (data: {
	AppId: number;
	Operator: string;
	SubMapId: string;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeMainMapBySubMapId' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 查询售后owenr
export const DescribeAppidSalesSupportor = (data: { AppId: number }): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeAppIdSalesSupportor' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 同步前后端节点信息
export const AsyncNodeListInfo = (data: {
	AppId: number;
	MapUUId: string;
	Opertor: string;
	ItemUUIdList: Array<string>;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'UpdateCloudMapAllInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 同步节点状态
export const DescribeMapItemIsInstance = (data: {
	AppId: number;
	MapUUId: string;
	Opertor: string;
	ItemUUIdList: Array<string>;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeMapItemIsInstance' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 获取架构图分数excel
export const DescribeDownLoadScoreExcel = (data: {
	AppId: number;
	MapUUId: string;
	Operator: string;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeDownLoadScoreExcel' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 绑定资源
// 获取账号的产品、地域、可用区列表
export function getProductRegionZoneOptions(data: { AppId: number; Uin: string; Product: string }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeProductRegionAndZone' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}

// 以 ResourceID 形式获取资源列表
export function getResourceByResourceId(data: GetResourceListParams): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeProductInstance' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}

//获取不同产品的筛选配置
export function getProductFilterConfigs(data: { AppId: number }): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeProductConfigs' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}

// 获取已绑定的初始数据
export const DescribeCloudMapItem = (data: DescribeMapItemParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCloudMapItem' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 绑定资源数据
export const CreateCloudMapItem = (data: MapItemParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateCloudMapItem' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 修改已绑定的资源信息
export const ModifyCloudMapItem = (data: MapItemParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'ModifyCloudMapItem' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 删除绑定的资源数据
export const DeleteCloudMapItem = (data: {
	MapUUId: string;
	Ids: Array<string>;
	AppId: number;
	BindingTypes?: Array<string>;
}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DeleteCloudMapItem' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

//获取标签键option
export const getTagKeysOption = (data: { AppId: number; Product: string }): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeTagKeys' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

//获取标签值option
export const getTagValuesOption = (data: { AppId: number; Product: string; TagKey: string }): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeTagValues' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

//获取标签值option
export const getTagsOption = (data: { AppId: number; Product: string; Regions: Array<string> }): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeTags' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};
// 发起架构巡检
export const CreateScanTaskJob = (data: {}): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CreateMapScanTask' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 生成列表报告
export const DownloadListReport = (data: DownloadListParams): Promise<any> => {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DownloadListReport' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 获取单节点风险
export const GetNodeRiskInfo = (data: NodeRiskInfo): Promise<any> => {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeCloudMapNodeRiskInfo' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 查询列表报告进程
export const GetDownloadTask = (data: { ResultID: string; AppId: number }): Promise<any> => {
	return request({
		method: 'post',
		url: '/guard',
		data: { ...data, Action: 'DescribeListDownloadTask' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
};

// 获取账号的产品列表
export function getProductOptions(data: { AppId: number }): Promise<any> {
	let testRes = [
		{ value: 'cvm', text: 'CVM' },
		{ value: 'clb', text: 'CLB' },
		{ value: 'mysql', text: 'MySQL' },
	];

	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'InnerDescribeCustomerRegionAndZone' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}

// 获取账号的地域列表(前置条件为产品)
export function getRegionOptions(data: { AppId: number; Products: any }): Promise<any> {
	let testRes = [
		{ value: 'sh', text: '上海' },
		{ value: 'gz', text: '广州' },
	];

	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'InnerDescribeCustomerRegionAndZone' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}

// 获取账号的可用区列表(前置条件为产品、地域）
export function getZoneOptions(data: { AppId: number; Products: any; Regions: any }): Promise<any> {
	let testRes = [
		{ value: 'sh-1', text: '上海一区' },
		{ value: 'sh-2', text: '上海二区' },
		{ value: 'sh-3', text: '上海三区' },
		{ value: 'sh-5', text: '上海五区' },
		{ value: 'gz-1', text: '广州一区' },
	];
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'InnerDescribeCustomerRegionAndZone' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}

// 以 Tag 形式获取资源列表
export function getResourceByTag(data: {
	AppId: number;
	Uin: string;
	Product: any;
	Region: any;
	Zones?: any;
}): Promise<any> {
	let testRes = {
		TagList: [
			{
				instanceId: 'test1',
				instanceName: 'Hongkong VPN1',
				status: 'running',
			},
			{
				instanceId: 'test2',
				instanceName: 'Hongkong VPN2',
				status: 'running',
			},
			{
				instanceId: 'test3',
				instanceName: 'Guangzhou Test',
				status: 'stopped',
			},
		],
		BindTagList: [
			{
				instanceId: 'test1',
				instanceName: 'Hongkong VPN1',
				status: 'running',
			},
		],
	};
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'InnerDescribeCustomerRegionAndZone' },
	})
		.then((data) => {
			return data.data.Response;
		})
		.catch((e) => {
			console.log(e);
			return e;
		});
}
