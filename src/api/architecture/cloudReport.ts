import request from '../request';

// 获取模版信息
export const GetArchitectureTemplateList = (data: { AppId: number, Filters: Array<any>, Offset?: number, Limit?: number }): Promise<any> => {
    return request({
        url: '/qcloud',
        method: 'post',
        data: { ...data, Action: 'DescribeCloudMapTemplate' }
    }).then(data => {
        return data.data.Response;
    }).catch(err => {
        console.log(err);
        return err;
    });
}

//获取产品、维度配置信息
export const getProductsGroups = (data): Promise<any> => {
    return request({
        url: '/qcloud',
        method: 'post',
        data: { ...data, Action: 'DescribeProductConfigList' }
    }).then(data => {
        return data.data.Response;
    }).catch(err => {
        console.log(err);
        return err;
    });
}

export const DescribeRegionsAndZones = (data): Promise<any> => {
    return request({
        url: '/qcloud',
        method: 'post',
        data: { ...data, Action: 'DescribeRegionsAndZones' }
    }).then((data) => {
        return data.data.Response;
    }).catch((err) => {
        console.log(err);
        return err;
    });
}

//获取评估项表头 DescribeRiskDisplay
export const DescribeRiskDisplay = (data): Promise<any> => {
    return request({
        url: '/qcloud',
        method: 'post',
        data: { ...data, Action: 'DescribeRiskDisplay' }
    }).then(data => {
        return data.data.Response;
    }).catch(err => {
        console.log(err);
        return err;
    });
}

// 查询任务指定，指定评估项，有风险的实例列表 DescribeTaskStrategyRisks
export const DescribeTaskStrategyRisks = (data): Promise<any> => {
    return request({
        url: '/qcloud',
        method: 'post',
        data: { ...data, Action: 'DescribeTaskStrategyRisks' }
    }).then(data => {
        return data.data.Response;
    }).catch(err => {
        console.log(err);
        return err;
    });
}

//查询任务指定，指定评估项，忽略的实例列表 DescribeTaskStrategyIgnores
export const DescribeTaskStrategyIgnores = (data): Promise<any> => {
    return request({
        url: '/qcloud',
        method: 'post',
        data: { ...data, Action: 'DescribeTaskStrategyIgnores' }
    }).then(data => {
        return data.data.Response;
    }).catch(err => {
        console.log(err);
        return err;
    });
}