import {
    CreateUserRulesParams,
    CreateUserRulesRet,
    DescribeApproveUserRulesParams,
    DescribeApproveUserRulesRet,
    DescribeCustomerInfoByAppIDParams,
    DescribeCustomerInfoByAppIDRet,
    DescribeUserRolesParams,
    DescribeUserRolesRet,
    DescribeUserRulesParams,
    DescribeUserRulesRet,
    DescribeApproveInfoParams,
    DescribeApproveInfoRet
} from '@src/types/privilege/privilege';
import request from '../request';

// 查询业务角色列表
export function getUserRoles(data: DescribeUserRolesParams): Promise<DescribeUserRolesRet> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'DescribeUserRoles' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 查询业务角色列表
export function getSystemRoles(data: { UserRole: string, Limit?: number, Offset?: number }): Promise<any> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'DescribeSystemRoles' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 查询个人权限列表
export function getUserRules(data: DescribeUserRulesParams): Promise<DescribeUserRulesRet> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'DescribeUserRules' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 查询申请列表
export function getApproveUserRules(data: DescribeApproveUserRulesParams): Promise<DescribeApproveUserRulesRet> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'DescribeApproveUserRules' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 根据APPID查询客户信息
export function getCustomerInfoByAppID(data: DescribeCustomerInfoByAppIDParams): Promise<DescribeCustomerInfoByAppIDRet> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'DescribeCustomerInfoByAppID' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 创建申请单
export function createUserRules(data: CreateUserRulesParams): Promise<CreateUserRulesRet> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'CreateUserRules' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 创建续期申请单
export function createRenewUserRules(data: CreateUserRulesParams): Promise<any> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'CreateRenewUserRules' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}

// 查询申请单以及权限信息，APPID粒度
export function getApproveInfo(data: DescribeApproveInfoParams): Promise<DescribeApproveInfoRet> {
    return request({
        method: 'post',
        url: '/iam',
        data: { ...data, Action: 'DescribeApproveInfo' },
    })
        .then((data) => {
            return data.data.Response;
        })
        .catch((e) => {
            console.log(e);
        });
}
