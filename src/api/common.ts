import request from './request';
import axios from 'axios';
import {
	GetAppIDByUinResponse,
	GetMenuKeysProps,
	GetMenuKeysRes,
	GetUserInfoRes,
	StaffResponse,
} from '@src/types/common';

// 获取登录用户信息
export function getUserInfo(): Promise<GetUserInfoRes> {
	return axios.get('/ts:auth/tauth/info');
}

// 获取左侧菜单的Key
export function getMenuKeys(data: GetMenuKeysProps): Promise<GetMenuKeysRes> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'DescribeMenuConfig' },
	}).then((data) => data.data.Response);
}

// 获取部门RTX
export function getStaffByDepartment(data: { DepartmentIds: Array<number> }): Promise<StaffResponse> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'DescribeStaffByDepartment', AppId: 1253985742 },
	})
		.then((data) => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 根据UIN获取APPID
export function getAppIDByUin(data: { SrcUin: string }): Promise<GetAppIDByUinResponse> {
	return request({
		method: 'post',
		url: '/qcloud',
		data: { ...data, Action: 'GetAppIDByUin', AppId: 1253985742 },
	})
		.then((data) => data.data.Response)
		.catch((e) => {
			console.log(e);
		});
}

// 查询客户是否有对云架构协作的授权
export function CheckAuthorization(data = {}): Promise<any> {
	return request({
		method: 'post',
		url: '/interface',
		data: { ...data, Action: 'CheckAuthorization' },
	})
		.then(data => data.data.Response)
		.catch(e => e);
}
