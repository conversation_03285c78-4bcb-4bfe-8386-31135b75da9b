//   在http.js中引入axios
import axios from 'axios'; // 引入 axios
import { message, Modal } from '@tencent/tea-component';
import { get } from 'lodash';
import Raven from 'raven-js';
import { aegis } from '../../app/app';
import { getUrlParamFromLocation } from '@src/utils/common';
import { getStorage, setStorage } from '@src/utils/storage';
import React from 'react';
// @ts-ignore
const { source, signature, timestamp/* , env */ } = getUrlParamFromLocation(
	['source', 'signature', 'timestamp', 'env'],
	location
);

let baseURL = '';
const { domain } = document;
baseURL = `${window.location.origin}/1`;

if (domain.startsWith('isa-intl')) {
	setStorage('site', 'sinapore');
} else {
	setStorage('site', 'china');
}

// let baseURL = `${window.location.origin}/1`;
//
// if (env === 'abroad') {
// 	baseURL = `${window.location.origin}/2`;
// 	setStorage('site', 'sinapore');
// 	message.success({ content: '当前已跳转国际站' });
// } else {
// 	baseURL = `${window.location.origin}/1`;
// 	setStorage('site', 'china');
// }

console.log('环境', process.env.NODE_ENV);
console.log('站点', getStorage('site') || 'china');

const header = {
	'X-Request-Signature': signature,
	'X-Request-Timestamp': timestamp,
	'X-Request-Source': source,
};
// create an axios instance   创建axios实例
const service = axios.create({
	baseURL, // api 的 base_url
	timeout: 20 * 1000, // request timeout  设置请求超时时间
	withCredentials: true, // 是否允许带cookie这些
	headers: source === '' ? header : {},
});

// request interceptor
service.interceptors.request.use(
    config => {
        // 在发送请求之前做什么
        if (config.method === "post") {
            // TODO: 分请求方式做参数处理
            // config.data = qs.stringify(config.data);
            // config.data = JSON.stringify(config.data);
        } else {

        }
		config.headers.RtxName = getStorage('engName');
        return config;
    },
    error => {
        // 对请求错误做些什么，自己定义
        message.error({ content: '接口请求拦截出错！' })
        return Promise.reject(error);
    }
)

// response interceptor
service.interceptors.response.use(
	(response) => {
		// 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
		// 否则的话抛出错误
		let data: any = {};
		const { method } = response.config;
		if (method === 'post') {
			data = JSON.parse(response.config.data);
		} else {
			data = JSON.parse(response.config.params);
		}
		if (response.status === 200) {
			if (data.OnlyData) {
				if (data.isAll) {
					return Promise.resolve(response);
				}
				if (response.data.Response) {
					if (response.data.Response.Error) {
						if (data.ShowError) {
							message.error({ content: response.data.Response.Error.Message });
						}
						return Promise.reject(response.data.Response.Error);
					}
					return Promise.resolve(response.data.Response);
				}
        if (response.data) {
          return Promise.resolve(response.data);
        }
			} else {
				return Promise.resolve(response);
			}
		} else {
			return Promise.reject(response);
		}
	},
	// 服务器状态码不是2开头的的情况
	// 这里可以跟你们的后台开发人员协商好统一的错误状态码
	// 然后根据返回的状态码进行一些操作，例如登录过期提示，错误提示等等
	// 下面列举几个常见的操作，其他需求可自行扩展
	(error) => {
		console.log(error);
		const { config, status, statusText, data } = error.response;
		const errUrl = config.baseURL + config.url;
		const intefacerError = {
			url: errUrl,
			data: config.data,
			method: config.method,
			status,
			statusText,
			responseData: JSON.stringify(data),
			user: getStorage('engName'),
		};
		Raven.captureException(`API接口错误：${get(error, 'response.data.Response.Error.Message')}`, {
			// @ts-ignore
			contexts: { APImsg: intefacerError },
			level: 'error',
			logger: 'interface',
		});
		if (status) {
			switch (status) {
			// 401: 无访问权限
				case 401:
					Modal.error({
						message: '没有授权访问，请到 [权限管理] 按需申请权限。',
						description: (
							<>
								<div>为什么我会收到这个提示？</div>
								<div>1. 首次使用本模块功能。</div>
								<div>2. 权限到期，需要续期。</div>
								<div>3. APPID 各负责人默认授予权限发生变化，如账号Owner发生变更。</div>
							</>
						),
						buttonText: '申请权限/续期',
						onClose: () => {
							window.open(`${window.location.origin}/advisor/privilege/my`);
						},
					});
					break;
				// 404请求不存在
				case 404:
					message.error({ content: '404 网络请求不存在' });
					break;
				case 500:
					message.error({ content: '500 服务器错误' });
					break;
				// 其他错误，直接抛出错误提示
				default:
					aegis.report({ msg: `interface-status: ${status}`, level: '16', ext1: JSON.stringify(data), ext2: errUrl });
					message.error({ content: get(error, 'response.data.Response.Error.Message') });
			}
			return Promise.reject(get(error, 'response.data.Response'));
		}
	}
);

export default service;
