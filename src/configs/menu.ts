import { AppMenu } from '@tea/app/types';
import { IS_INTL } from '@src/utils/constants';
import { shouldUseMigratedRoute } from '@src/utils/common';

/**
 * 定义导航菜单
 */
export const menu: AppMenu = {
	title: '云顾问（运营端）',
	items: [
		{
			label: '客户服务',
			items: [
				{
					key: 'new-architecture',
					route: '/advisor/new-architecture',
					title: '云上治理',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/2658681c-eccd-4d63-94e0-d26355c97892.svg',
				},
				{
					key: 'handle',
					title: '云巡检',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/0b06548a-abd4-4801-9d9b-cf528bd52494.svg',
					items: [
						{
							key: 'handle',
							route: '/advisor/handle',
							title: '客户风险治理',
						},
						{
							key: IS_INTL ? 'strategies-manage' : 'overview',
							route: IS_INTL ? '/advisor/strategies-manage' : '/advisor/customer-risk-overview',
							title: IS_INTL ? '策略列表' : '客户风险总览',
						},
						{
							key: 'assess',
							route: '/advisor/assess',
							title: '云巡检报告查询',
						},
						{
							key: 'dashboard',
							route: '/advisor/dashboard',
							title: '客户云产品分析',
						},
					],
				},
				{
					title: '大盘监控',
					key: 'monitor',
					route: '/advisor/monitor',
					icon: 'https://imgcache.qq.com/qcloud/tcloud_dtc/static/console_aside_v4/10115ea7-14a3-40ae-9869-928e7fa08cc4.svg',
				},
				{
					key: 'alarm',
					route: '/advisor/alarm',
					title: '告警管理',
					icon: 'https://imgcache.qq.com/qcloud/tcloud_dtc/static/console_aside_v4/197a085a-d92b-417e-b623-f1cf0a2730e8.svg',
				},
				{
					key: 'touch',
					title: '云消息',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/1af9b5cb-13d2-41d2-ac18-930e4df037e2.svg',
					items: [
						{
							key: 'fault',
							route: '/advisor/fault-notification',
							title: '平台故障通知',
						},
						{
							key: 'touch',
							route: '/advisor/proactive-touch',
							title: '产研告警推送',
						},
						{
							key: 'event',
							route: '/advisor/common-event',
							title: '主动服务通知',
						},
					],
				},
				{
					key: 'guardtitle',
					title: '云护航',
					icon: 'https://imgcache.qq.com/qcloud/tcloud_dtc/static/console_aside_v4/bae98d4c-a299-4f59-8753-664d871406e1.svg',
					items: [
						{
							key: 'guard',
							route: '/advisor/guard',
							title: '护航管理',
						},
						{
							key: 'guard',
							route: shouldUseMigratedRoute() ? '/advisor/cloud-escort/approval' : '/advisor/approval',
							title: '护航审批',
						},
						{
							key: 'guard',
							route: '/advisor/broadcast',
							title: '播报订阅',
						},
						{
							key: 'guard',
							route: shouldUseMigratedRoute() ? '/advisor/cloud-escort/config-management' : '/advisor/config-management',
							title: '护航配置管理',
						},
					],
				},
				{
					key: 'email-center',
					title: '邮件中心',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/2a6872fb-0a40-4eca-ad1d-756c9af29767.svg',
					items: [
						{
							key: 'email-center-templates',
							route: '/advisor/email-center',
							title: '模板管理',
						},
						{
							key: 'email-center-tasks',
							route: '/advisor/email-center/tasks',
							title: '推送任务',
						},
						{
							key: 'email-center-logs',
							route: '/advisor/email-center/logs',
							title: '邮件日志',
						},
					],
				},
				{
					key: 'digital-assets',
					title: '数字资产',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/1cd0db6e-2113-49e9-881c-ef713e4384ea.svg',
					items: [
						{
							key: 'advisor-blog-admin',
							route: '/advisor/digital-assets',
							title: '云顾问博客',
						},
						{
							key: 'advisor-product-trend',
							route: '/advisor/digital-assets/product-trend',
							title: '产品动态',
						},
					],
				},
				{
					key: 'subscription-center',
					route: '',
					title: '订阅中心(建设中)',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/2a6872fb-0a40-4eca-ad1d-756c9af29767.svg',
					disabled: true,
				},
				{
					key: 'chaos-drill',
					route: '',
					title: '混沌演练(建设中)',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/8b1c6e30-7318-4bf3-8fe8-98faaee60cec.svg',
					disabled: true,
				},
				{
					key: 'plan-management',
					route: '',
					title: '预案管理(建设中)',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/e6269d2a-ba21-4ca5-b052-9a4acced351f.svg',
					disabled: true,
				},
				// {
				// 	key: 'architecture',
				// 	route: '/advisor/architecture',
				// 	title: '云架构（旧）',
				// 	icon: 'https://imgcache.qq.com/qcloud/tcloud_dtc/static/console_aside_v4/95fe2b9f-6cea-4b40-a077-7e4b90dfd562.svg',
				// },
			],
		},
		{
			label: '运营数据',
			items: [
				{
					key: 'data-screening',
					title: '运营数据总览',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/284510f3-aea3-462d-8ed6-4cf93d1b77dc.svg',
					route: '/advisor/data-screening/recommend',
				},
			],
		},
		{
			label: '系统配置',
			items: [
				{
					key: 'handle',
					title: '配置管理',
					icon: 'https://imgcache.qq.com/qcloud/tcloud_dtc/static/console_aside_v4/bae98d4c-a299-4f59-8753-664d871406e1.svg',
					items: [
						{
							key: IS_INTL ? 'strategies-manage-new' : 'strategies-manage',
							route: '/advisor/strategies-manage',
							title: '云巡检策略管理',
						},
						{
							key: 'excellent-config',
							route: '/advisor/new-architecture/excellent-config',
							title: '卓越架构评估管理',
						},
						{
							key: 'monitor-config',
							route: '/advisor/monitor-config',
							title: '监控配置',
						},
					],
				},
				{
					key: 'operation-manage',
					title: '开发者工具',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/665d8269-acba-4e57-864c-2c90fa7ea5ba.svg',
					items: [
						{
							key: 'operation-manage',
							route: '/advisor/operation-manage',
							title: '云架构插件配置',
							icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/665d8269-acba-4e57-864c-2c90fa7ea5ba.svg',
						},
						{
							key: 'restore-arch',
							route: '/advisor/operation-manage/restore-arch',
							title: '架构版本管理',
						},
						{
							key: 'gateway-administration',
							route: '/advisor/gateway-administration',
							title: '网关管理',
							icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/665d8269-acba-4e57-864c-2c90fa7ea5ba.svg',
						},
						{
							title: '巡检发布',
							key: 'inspection-release',
							route: '/advisor/inspection-release',
							// icon: 'https://imgcache.qq.com/qcloud/tcloud_dtc/static/console_aside_v4/1291e30e-1539-4a35-b347-b68d9be95bed.svg',
							isThird: true,
							items: [
								{
									key: 'inspection-release-t',
									route: '/advisor/inspection-release-t',
									title: '巡检发布-测试',
								},
								{
									key: 'inspection-release-pre',
									route: '/advisor/inspection-release-pre',
									title: '巡检发布-预发布',
								},
								{
									key: 'inspection-release-domestic',
									route: '/advisor/inspection-release-domestic',
									title: '巡检发布-国内',
								},
								{
									key: 'inspection-release-international',
									route: '/advisor/inspection-release-international',
									title: '巡检发布-国际',
								},
							],
						},
						{
							title: '巡检大盘',
							key: 'inspect-overview',
							isThird: true,
							items: [
								{
									key: 'inspect-overview',
									route: '/advisor/cloud-inspection/inspect-overview',
									title: '巡检调度大盘',
								},
								{
									key: 'inspect-detail',
									route: '/advisor/cloud-inspection/inspect-detail',
									title: '巡检任务治理',
								},
							],
						},
					],
				},
				{
					key: 'privilege',
					title: '权限管理',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/console_aside_v4/d76d40d4-45ee-4b31-aa1e-668ba5c1ee0d.svg',
					route: '/advisor/privilege/my',
					// items: [
					// 	// {
					// 	// 	key: 'apply-list',
					// 	// 	route: '/advisor/privilege/apply-list',
					// 	// 	title: '权限申请'
					// 	// },
					// 	{
					// 		key: 'my',
					// 		route: '/advisor/privilege/my',
					// 		title: '我的权限',
					// 	},
					// ],
				},
				// {
				// 	key: 'service',
				// 	route: '/advisor/service',
				// 	title: '专家服务(下线中)',
				// 	icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/581fbb1a-24c3-480f-b0ce-39d7726436cd.svg',
				// },
				{
					title: IS_INTL ? '我的客户' : '我的客户(已迁移)',
					key: 'my-customer',
					route: IS_INTL ? '/advisor/my-customer' : '/advisor/transfer/my-customer',
					icon: 'https://imgcache.qq.com/qcloud/tcloud_dtc/static/console_aside_v4/1291e30e-1539-4a35-b347-b68d9be95bed.svg',
				},
				{
					key: 'overview',
					title: IS_INTL ? '客户总览' : '客户总览(已迁移)',
					icon: 'https://cloudcache.tencent-cloud.com/qcloud/ui/static/static_source_business/284510f3-aea3-462d-8ed6-4cf93d1b77dc.svg',
					route: IS_INTL ? '/advisor/overviewindex' : '/advisor/transfer/overviewindex',
				},
			],
		},
		/*
		{
			key: 'failure',
			route: '/advisor/failure',
			title: '故障大盘'
		}*/
		// {
		// 	title: 'Advisor',
		// 	items: [
		// 		{
		// 			key: 'monitor',
		// 			route: '/advisor/monitor',
		// 			title: '大盘监控',
		// 		},
		// 		{
		// 			key: 'overview',
		// 			route: '/advisor/overview',
		// 			title: '客户总览',
		// 		},
		// 		{
		// 			key: 'strategies-manage',
		// 			route: '/advisor/strategies-manage',
		// 			title: '策略管理',
		// 		},
		// 		{ key: 'assess', route: '/advisor/assess', title: '服务报告' },
		// { key: 'estimate', route: '/advisor/estimate', title: '评估结果(内)' },
		// { key: 'history', route: '/advisor/history', title: '历史记录' },
		// {
		// 	key: 'log',
		// 	href:
		// 		localStorage.getItem('site') === 'sinapore'
		// 			? 'http://es-9p8kps7s.internal.kibana.tencentelasticsearch.com/'
		// 			: 'http://es-7ziqnevy.internal.kibana.tencentelasticsearch.com/',
		// 	title: '日志',
		// 	outerLinkIcon: true,
		// },
		// 	],
		// },
	],
};
