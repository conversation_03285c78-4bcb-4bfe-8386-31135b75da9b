// 子应用枚举
export enum MicroAppEnum {
	ISA_CLOUD_ARCH = 'isa-cloud-arch',
	CLOUD_ARCH_ADMIN = 'cloud-arch-admin',
	ISA_DIGITAL_ASSETS = 'isa-digital-assets',
	ISA_CLOUD_ESCORT = 'isa-cloud-escort',
	ISA_EMAIL_CENTER = 'isa-email-center',
	ISA_CLOUD_INSPECTION = 'isa-cloud-inspection',
}
export enum EnvEnum {
	local = 'others',
	test = 'test',
	pre = 'pre',
	prod = 'production',
	abroadPre = 'pre-abroad',
	abroad = 'production-abroad',
}

// 子应用列表
export const MICRO_APP_LIST = [
	{ appKey: MicroAppEnum.ISA_CLOUD_ARCH, appName: '云架构运营端' },
	{ appKey: MicroAppEnum.CLOUD_ARCH_ADMIN, appName: '云架构管理系统' },
	{ appKey: MicroAppEnum.ISA_DIGITAL_ASSETS, appName: '云顾问数字资产' },
	{ appKey: MicroAppEnum.ISA_EMAIL_CENTER, appName: '云顾问邮件中心' },
	{ appKey: MicroAppEnum.ISA_CLOUD_ESCORT, appName: '云护航' },
	{ appKey: MicroAppEnum.ISA_CLOUD_INSPECTION, appName: '云巡检' },
];

export const MICRO_APP_ENTRY = {
	[EnvEnum.local]: {
		[MicroAppEnum.ISA_CLOUD_ARCH]: '//localhost:3000',
		[MicroAppEnum.CLOUD_ARCH_ADMIN]: '//localhost:3001',
		[MicroAppEnum.ISA_DIGITAL_ASSETS]: '//localhost:3002',
		[MicroAppEnum.ISA_CLOUD_ESCORT]: '//localhost:3003',
		[MicroAppEnum.ISA_EMAIL_CENTER]: '//localhost:3003',
		[MicroAppEnum.ISA_CLOUD_INSPECTION]: '//localhost:3004',
	},
	[EnvEnum.test]: {
		[MicroAppEnum.ISA_CLOUD_ARCH]: '//isa-cloud-arch-test.woa.com',
		[MicroAppEnum.CLOUD_ARCH_ADMIN]: '//cloud-arch-admin-test.woa.com',
		[MicroAppEnum.ISA_DIGITAL_ASSETS]: '//advisor-blog-admin-test.woa.com',
		[MicroAppEnum.ISA_CLOUD_ESCORT]: '//isa-cloud-escort-test.woa.com',
		[MicroAppEnum.ISA_EMAIL_CENTER]: '//subscription-center-admin-test.woa.com',
		[MicroAppEnum.ISA_CLOUD_INSPECTION]: '//isa-cloud-inspection-test.woa.com',
	},
	[EnvEnum.pre]: {
		[MicroAppEnum.ISA_CLOUD_ARCH]: '//isa-cloud-arch-pre.woa.com',
		[MicroAppEnum.CLOUD_ARCH_ADMIN]: '//cloud-arch-admin-pre.woa.com',
		[MicroAppEnum.ISA_DIGITAL_ASSETS]: '//advisor-blog-admin-pre.woa.com',
		[MicroAppEnum.ISA_EMAIL_CENTER]: '//subscription-center-admin-pre.woa.com',
		[MicroAppEnum.ISA_CLOUD_ESCORT]: '//isa-cloud-escort-pre.woa.com',
		[MicroAppEnum.ISA_CLOUD_INSPECTION]: '//isa-cloud-inspection-pre.woa.com',
	},
	[EnvEnum.prod]: {
		[MicroAppEnum.ISA_CLOUD_ARCH]: '//isa-cloud-arch.woa.com',
		[MicroAppEnum.CLOUD_ARCH_ADMIN]: '//cloud-arch-admin.woa.com',
		[MicroAppEnum.ISA_DIGITAL_ASSETS]: '//advisor-blog-admin-prod.woa.com',
		[MicroAppEnum.ISA_EMAIL_CENTER]: '//subscription-center-admin.woa.com',
		[MicroAppEnum.ISA_CLOUD_ESCORT]: '//isa-cloud-escort.woa.com',
		[MicroAppEnum.ISA_CLOUD_INSPECTION]: '//isa-cloud-inspection.woa.com',
	},
	[EnvEnum.abroadPre]: {
		// 暂时国际站不使用
		// [MicroAppEnum.ISA_CLOUD_ARCH]: '//isa-cloud-arch-pre.woa.com',
		// [MicroAppEnum.CLOUD_ARCH_ADMIN]: '//cloud-arch-admin-pre.woa.com',
		// [MicroAppEnum.ISA_DIGITAL_ASSETS]: '//advisor-blog-admin-pre.woa.com',
		// [MicroAppEnum.ISA_EMAIL_CENTER]: '//subscription-center-admin-pre.woa.com',
		[MicroAppEnum.ISA_CLOUD_ESCORT]: '//isa-cloud-escort-pre.woa.com',
		[MicroAppEnum.ISA_CLOUD_INSPECTION]: '//isa-cloud-inspection-pre.woa.com',
	},
	[EnvEnum.abroad]: {
		[MicroAppEnum.ISA_CLOUD_ARCH]: '//isa-cloud-arch.woa.com',
		[MicroAppEnum.CLOUD_ARCH_ADMIN]: '//cloud-arch-admin.woa.com',
		[MicroAppEnum.ISA_DIGITAL_ASSETS]: '//advisor-blog-admin-prod.woa.com',
		[MicroAppEnum.ISA_CLOUD_ESCORT]: '//isa-cloud-escort.woa.com',
		[MicroAppEnum.ISA_EMAIL_CENTER]: '//subscription-center-admin.woa.com',
		[MicroAppEnum.ISA_CLOUD_INSPECTION]: '//isa-cloud-inspection.woa.com',
	},
};

export const MICRO_APP_CONTAINER_ID = 'micro-frontend-root';

export const MICRO_APP_CONTAINER_SELECTOR = `#${MICRO_APP_CONTAINER_ID}`;
