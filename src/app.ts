import './i18n';
// 导入样式
import '@tea/component/tea.css';
import './style/common.less';
// 导入重构css样式
import '@tencent/tea-style-international-code';
import '@tencent/tea-style-toc';

// 导入依赖
import { app } from '@tea/app';
import { start, registerMicroApps, addGlobalUncaughtErrorHandler } from 'qiankun';
import { message as tips } from '@tencent/tea-component';

// 导入组件
import { Advisor } from '@src/routes/advisor';
import { MyCustomer } from '@src/routes/advisor/pages/myCustomer';
import { getStorage } from '@src/utils/storage';
import { MICRO_FRONTEND_ROOT_READY } from '@src/components/MicroAppContainer';
import microAppConfig from './micro-app';
import { isaCommonReport } from './utils/report-to-wedata';

// 导入导航配置
const initMenu = {
	title: '云顾问运营平台',
	items: [],
};

// 子应用加载错误提示
addGlobalUncaughtErrorHandler((event: any) => {
	const { type, message } = event ?? {};
	if (type === 'error' && message?.includes('Uncaught TypeError: application ')) {
		tips.error({ content: message });
	}
	console.error('addGlobalUncaughtErrorHandler', message);
});

// 注册子应用
registerMicroApps(microAppConfig);
(window as any).__POWERED_BY_QIANKUN_PARENT__ = true;
const startMicro = () => {
	start({
		sandbox: {
			loose: true,
			experimentalStyleIsolation: true,
		},
		singular: true,
		prefetch: true,
		excludeAssetFilter: (assetUrl: string) => {
			// 插件脚本不被qiankun劫持处理
			if (
				assetUrl.includes('cloudcache-preview.woa.com')
				|| assetUrl.includes('https://127.0.0.1')
				|| assetUrl.includes('http://127.0.0.1')
			) {
				return true;
			}
			return false;
		},
	});
};
// 监听到 micro-frontend-root 挂载事件后，再启动子应用
document.addEventListener(MICRO_FRONTEND_ROOT_READY, startMicro, { once: true });

// 路由表，一个路由对应一个组件，不在路由表中的路由（如/ww）将重定向到路由表的第一项
const routes = {
	'/': { component: MyCustomer, exact: true },
	'/advisor': Advisor,
};
// 注册路由表/左侧菜单
app.routes(routes, initMenu);

(window as any).isaCommonReport = isaCommonReport;

(window as any).onload = function () {
	(window as any).CDSC_RTX = getStorage('engName');
	const as = document.createElement('script');
	as.src = '//pulse.woa.com/wt/js/wm.js';
	document.body.appendChild(as);
	as.onload = function () {
		(window as any).wmReload?.('', 0.05, 1);
	};
};
