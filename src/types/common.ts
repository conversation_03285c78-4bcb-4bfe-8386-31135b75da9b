import { _Error } from "./error";
export interface GetUserInfoRes {
	status: number;
	data: UserInfo;
}
interface UserInfo {
	EngName: string;
	ChnName: string;
	DeptNameString?: string;
}

export interface GetMenuKeysProps {
	Username: string;
}
export interface GetMenuKeysRes {
	RequestId: string;
	Error?: _Error;
	Items: Array<string>;
}

export interface ProductConfig {
	ProductName: string;
	Items: Array<string>;
}
export interface CommonResponse {
	RequestId: string;
	Error?: _Error;
}
/** 过滤字段 */
export interface Filter {
	/** 需要过滤的字段  */
	Name: string;
	/** 字段的过滤值  */
	Values: Array<string>;
}

export interface StaffResponse {
	RequestId: string;
	Error?: _Error;
	Staff: Array<string>;
}

export interface GetAppIDByUinResponse {
	RequestId: string;
	Error?: _Error;
	AppId: number;
}

export interface TagItem {
	TagKey: string;
	TagValues: Array<string>;
}
