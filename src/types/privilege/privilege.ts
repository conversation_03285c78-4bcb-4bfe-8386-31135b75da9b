import { _Error } from "../error";

export interface Filter {
	Name: string,
	Values: Array<string>,
}


export interface DescribeUserRolesParams {
	AccountArea?: number,
	Limit?: number,
	Offset?: number,
}

export interface DescribeUserRolesRet {
	RequestId: string,
	Error?: _Error;
	TotalCount?: number,
	UserRoleSet?: Array<UserRole>
	SystemRoleSet?: Array<SystemRoleInfoWithCategory>
}

export interface SystemRoleInfoWithCategory {
	Category: string
	SystemRoles: Array<SystemRoleInfo>
}

export interface SystemRoleInfo {
	Name: string
	CnName: string
	Approver: string
	ApproveType: number
}

export interface UserRole {
	Name: string,
	CnName: string
}
export interface RoleInfo {
	UserRole: string,
	UserRoleName?: string,
	SystemRole: Array<string>,
}

export interface RoleDict {
	SystemRole: Array<RoleEn2Zh>,
	UserRole: Array<RoleEn2Zh>
}

export interface RoleEn2Zh {
	EN: string,
	ZH: string,
}


export interface DescribeUserRulesParams {
	Name: string,
	Filters?: Array<Filter>,
	AccountArea?: number,
	Offset?: number,
	Limit?: number,
}

export interface DescribeUserRulesRet {
	RequestId: string,
	Error?: _Error;
	TotalCount?: number,
	RuleSet?: Array<RoleBinding>,
}

export interface RoleBinding {
	// AppId: string,
	// Name: string,
	// BillUuid: string,
	// UserRoles: Array<string>,
	// SystemRoles: Array<string>,
	// PolicySet: Array<string>,
	// CustomerInfoSet: CustomerInfoDict,
	// IsExpire: boolean,
	// LeaderApprover: string,
	// LeaderApproverStatus: string,
	// LeaderApproverMessage: string,
	// ProductApprover: string,
	// ProductApproverStatus: string,
	// ProductApproverMessage: string,
	// Status: number,
	// ApplyId: number,
	// ExpireTime: string,
	// CreateTime: string,
	// UpdateTime: string,
	AppId: number,
	Name: string,
	UserRole: string,
	SystemRoles: Array<string>,
	IsExpire: boolean,
	Status: number,
	ApplyId: number,
	CustomerInfo: CustomerInfo,
	ExpireTime: string,
	CreateTime: string,
	UpdateTime: string,
}

export interface CustomerInfoDict {
	AppId: number,
	CustomerName: string,
}

export function stringMap2Options(m: Map<string, string>) {
	var options: Array<{ text: string, value: string }> = []
	m.forEach((v, k) => {
		options.push({ text: v, value: k })
	})
	return options
}

export function numberMap2Options(m: Map<number, string>) {
	var options: Array<{ text: string, value: string }> = []
	m.forEach((v, k) => {
		options.push({ text: v, value: k.toString() })
	})
	return options
}

export interface DescribeApproveUserRulesParams {
	Name: string,
	Filters?: Array<Filter>,
	AccountArea?: number,
	Offset?: number,
	Limit?: number,
}

export interface DescribeApproveUserRulesRet {
	RequestId: string,
	Error?: _Error;
	TotalCount?: number,
	ApproveUserRulesSet?: Array<ApproveBinding>,
}

export interface ApproveBinding {
	Name: string
	UserRoleRelatedInfoSet: Array<UserRoleRelatedInfo>
	CurrentApprover: string
	TamApprover: string
	TamApproverStatus: string
	Status: string
	ApplyId: number
	ApplyReason: string
	Title: string
	DurationTime: number
	ExpireTime: string
	CreateTime: string
	UpdateTime: string
}

export interface UserRoleRelatedInfo {
	UserRole: string
	SystemRoleSet: Array<string>
	CustomerInfoSet: Array<CustomerInfo>
}

export interface DescribeCustomerInfoByAppIDParams {
	Name?: string,
	AppIDSet?: Array<number>,
	AccountArea?: number,
	UserRole?: string,
	Limit?: number,
	Offset?: number,
}

export interface DescribeCustomerInfoByAppIDRet {
	RequestId: string,
	Error?: _Error;
	TotalCount?: number,
	CustomerInfoSet?: Array<CustomerInfo>,
}

export interface CustomerInfo {
	AppID: number,
	CustomerName: string,
	Uin?: number,
	Status: number, // 是否已申请权限，0未申请权限，1已申请权限
}

export interface CreateUserRulesParams {
	Name: string,
	DurationTime: number,
	ApplyReason: string,
	Data?: any,
	AccountArea?: number,
	ApplyId?: number
}

export interface CreateUserRulesRet {
	RequestId: string,
	Error?: _Error;
	code?: number, //0表示成功
}

export interface DescribeApproveInfoParams {
	Name: string,
	Filters?: Array<Filter>,
	IsFinished?: number,
	AccountArea?: number,
	Offset?: number,
	Limit?: number,
}

export interface DescribeApproveInfoRet {
	RequestId: string,
	Error?: _Error,
	TotalCount: number,
	ApproveInfoSet: Array<ApproveInfo>,
}

export interface ApproveInfo {
	Id: number,
	CustormerInfo: CustomerInfo,
	ApplyId: number,
	UserRoleZh: string,
	SystemRoles: Array<string>,
	IsExpire: boolean,
	Status: number,
	ApproveMessage: string,
	CurrentApprover: string,
	ExpireTime: string,
	CreateTime: string,
	UpdateTime: string,
}

//业务角色映射
export const UserRoleDict = new Map([
	["CloudArchitect", "行业权限"],
	["TAM", "TAM权限"],
	["MapReadOnly", "云架构只读"],
	["Expert", "专项权限"],
])

//系统角色映射
export const SystemRoleDict = new Map([
	["cloudMapFullAccess", "云架构读写权限"],
	["cloudMapReadOnly", "云架构只读权限"],
	["guardReadOnly", "护航只读权限"],
	["guardFullAccess", "护航读写权限"],
	["advisorReadOnly", "云巡检只读权限"],
	["productInfoReadOnly", "产品信息大盘只读权限"],
])

//权限审批状态
export const ApprovalStatusDict = new Map([
	["reject", "驳回"],
	["finish", "已完成"],
	["approving", "审批中"],
	["fail", "申请失败(不属于已转售后客户)"],
])

//APPID申请状态
export const AppidStatusInRoleDict = new Map([
	[0, "未申请"],
	[1, "已申请"],
])

//申请时长
export const ApprovalDaysDict = new Map([
	[7, "7天"],
	[30, "30天"],
	[182, "6个月"],
	[365, "1年"],
])