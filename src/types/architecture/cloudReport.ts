export interface RiskFieldsDescItemSon {
    Field: string,
    FieldName: string,
    FieldType: string,
    FieldDict: Array<{ Key: string, Value: string }>,
}

export interface RiskFieldsDescItem {
    StrategyId: number,
    Url: string,
    LinkId: string,
    PriId: string,
    RiskFieldsDesc: Array<RiskFieldsDescItemSon>
}

export interface Instance {
    Id: string
    Region: string
    Extra?: string
    Level?: number
    IgnoredStatus?: boolean
}

export interface ConfigCondition {
    ConditionId: number
    Level: number
    Desc: string
}

export interface ConfigStrategy {
    StrategyId: number
    Product: string
    Name: string
    Desc: string
    Repair: string
    Notice: string
    Ignore: string
    IsSupportCustom: boolean
    Conditions: Array<ConfigCondition>
}

export interface RegionCode {
    Region: string
    Code: string
}