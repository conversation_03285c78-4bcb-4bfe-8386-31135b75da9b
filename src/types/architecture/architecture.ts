export interface MapBaseInfo {
	MapVersion?: number;
	MapUUId?: string;
	OperationType?: number;
	MapName?: string;
	Detail: string;
	SvgFile: string;
	Owner?: string;
	Creator?: string;
	Operator?: string;
}

export interface MapItemParams {
	AppId: number;
	MapVersion?: number;
	MapUUId?: string;
	Uuid?: string;
	ItemName?: string;
	ItemId?: number;
	Regions?: Array<string>;
	Zones?: Array<string>;
	Product?: string;
	Instance?: any;
	BindingType?: string;
}

export interface DescribeMapItemParams {
	AppId: number;
	Uuid: string;
	Env?: string;
	MapUUId: string;
}

export interface Filter {
	Name: string;
	Values: Array<string>;
}

export interface DescribeTemplateFilter {
	templateName: string;
	templateType: string;
	creater: string;
	templateDesc: string;
	templateSence: string;
	industry: Array<string>;
}

export interface CloudMapTemplate {
	MapTemplateUUId?: string;
	TemplateName: string;
	OperationType?: number;
	Property: number;
	PropertyDesc: string;
	Scenario: string;
	Detail?: string;
	SvgFile: string;
	VersionUUID?: string;
	Owner: string;
	Operator: string;
}

export interface CloudMapBaseTemplate {
	TemplateName: string;
	Property: number;
	PropertyDesc: string;
	Scenario: string;
	Detail?: string;
	SvgFile: string;
	Creator: string;
}

export interface DescribeMapFilter {
	appId: string;
	mapName: string;
	clientName: string;
	MapUUId: string;
	creater: string;
	industry: Array<string>;
}

export interface MapInfo {
	AppId?: number;
	MapName: string;
	MapUUId?: string;
	CustomerName: string;
	Detail: string;
	MapVersion?: number;
	Operator?: string;
	CanCreateTask?: boolean;
	TAMGroup?: string;
	VersionUUID?: string;
}

export interface CloudMapPushTemplate {
	AppId?: number;
	CustomerName: string;
	TemplateName?: string;
	Detail: string;
	SvgFile: string;
	PropertyDesc?: number;
	creator?: string;
	Industry: Array<string>;
}