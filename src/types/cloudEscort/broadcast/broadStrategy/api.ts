import { _Error } from '../../../error';

// 组合播报内容查询 request
export interface CombinedData {
	AppId?: number,
	BroadcastId?: number
}

// 组合播报内容查询 response
export interface CombinedDataResponse {
	RequestId: string;
	Error?: _Error;
	CombinedBroadcastConfig?: any;
}

// 组合播报内容修改 request
export interface CombinedDataM {
	AppId?: number,
	BroadcastId?: number,
	Updater?: string,
	CombinedBroadcastConfig?: any
}

// 播报策略管理 request
export interface StrategysListParams {
	Filters: Array<Filter>
	Offset?: number,
	Limit?: number,
	AppId?: number
}
export interface Filter {
	Name: string,
	Values: Array<string>,
}

export interface ConfigStrategysListParams {
	Filters: Array<FilterConfig>
	Offset?: number,
	Limit?: number,
	AppId?: number
}

export interface FilterConfig {
	Key: string,
	Value: Array<string>,
}

// 播报策略管理 response
export interface BroadcastRes {
	RequestId: string;
	Error?: _Error;
	BroadcastStrategy: Array<Strategy>;
	TotalCount?: number,
}

export interface BroadcastConfigRes {
	RequestId: string;
	Error?: _Error;
	List: Array<Strategy>;
	Total?: number,
}

export interface Strategy {
	StrategyId: number
	StrategySource: number;
	Product: string
	CNName: string
	ENName: string
	SubType: string
	Type: string
	Online: number
	CreateTime: string
	UpdateTime: string
	Creater: string
	Updater: string
	Limit: number
	Threshold: Array<Threshold>
	Server: Server
}
export interface Threshold {
	Values: number
	Factor: string
	Unit: string
}
export interface Server {
	Url: string
	Action: string
}

// 接口测试 request
export interface InterfaceTestParams {
	ProductName: string
	CNName: string
	Url: string
	RequestId: string
	TestJson: string
	User: string
	AppId?: number
}

// 接口测试 response
export interface InterfaceTestRes {
	ResourceSet?: Array<ResourceSet>
	OverviewSet?: Array<OverviewSet>
	RequestId?: string
	TotalCount?: number
	Pass?: number
	Content?: string
	Error?: _Error
}
export interface ResourceSet {
	ResourceId: string
	Policy: string
	IsNormal: number
	Message: string
	SubType: string
}
export interface OverviewSet {
	Policy: string
	IsNormal: number
	Message: string
	SubType: string
}


// 创建播报策略 request
export interface CreateStrategyParams {
	Product: string,
	Online: number,
	BroadcastStrategy: StrategyInfo,
	Server: Server,
	Threshold: Array<Threshold>,
	Creater: string
	AppId?: number
}
export interface StrategyInfo {
	CNName: string
	ENName: string
	Type: string
	SubType: string
	Desc: string
}

// 创建播报策略 response
export interface CreateStrategyRes {
	Error?: _Error
	RequestId: string
	Message?: string
}

// 修改播报策略 request
export interface ModifyStrategyParams {
	StrategyId: number
	Online: number
	Product: string
	BroadcastStrategy: StrategyInfo
	Server: Server
	Threshold: Array<Threshold>
	Updater: string
	AppId?: number
}

// 修改播报策略 response
export interface ModifyStrategyRes {
	Error?: _Error
	RequestId: string
	Message?: string
}


export interface getAssessResultProps {
	AppID: string;
	UIN?: string;
	TaskID?: string;
}

export interface MonitorProductRes {
	List: Array<MonitorProductItem>;
}

interface MonitorProductItem {
	ProductId: string;
	ProductName: string;
}


export interface NodeMonitorMetricRes {
	List: {
		Metric: string;
		Namespace: string;
		View: string;
		IsExist: string;
		MetricName: string;
	};
}

export interface ConfiguredMonitorRes {
	Info: {
		Desc: string;
		Expert: string;
		MetricName: string;
		ProductName: string;
		SubType: string;
		Type: string;
		Threshold: {
			Values: string;
			Factor: string;
			Unit: string;
		}
	}
}

export interface CreateMonitorParams {
	Desc: string;
	Expert: string;
	MetricName: string;
	Namespace: string;
	Metric: string;
	View: string;
	ProductName: string;
	ProductId: string;
	SubType: string;
	Type: string;
	Operator: string;
	Threshold: {
		Values: string;
		Factor: string;
		Unit: string;
	}
}

export interface UpdateStrategyStatusParams {
	EnabledStatus: number;
	StrategyId: string;
	Operator: string;
}


export interface UpdateChatBiMetricParams {
	ChatBiMetricId: number;
	Status: number;
	Operator: string;
}
export interface MetricStatisticsParams {
	Metric: string;
	MetricName: string;
	Namespace: string;
	View: string;
	ProductId: string;
}

export interface DescribeUserListParams {
	Limit: number;
	Name: string;
}


export interface DescribeUserListRes {
	UserList: {
		CnName: string;
		EnName: string;
		FullName: string;
	}[]
}

export interface MetricConfigListParams {
	Metric?: string[];
	ProductId?: Array<string>;
	Status?: number;
	Limit?: number;
	Offset?: number;
	AppId?: number
}

export interface ModifyMetricConfigParams {
	ProductId: string;
	Metric: string;
	MetricName: string;
	Unit: string;
	Desc: string;
	Dimensions: Array<string>;
	Status: number;
	InsField?: string;
}

export interface CreateMetricConfigParams {
	Id: number;
	ProductId: string;
	Metric: string;
	MetricName: string;
	Unit: string;
	Desc: string;
	Dimensions: Array<string>;
	Status: number;
}

export interface UpdateDimensionInfoListParams {
	StrategyId: number;
	Operator: string;
	DimensionInfoList: {
		DimensionId: number;
		DimensionName: string;
		NameSpace: string;
		SourceDataGetCondition: string;
	}[]
}
