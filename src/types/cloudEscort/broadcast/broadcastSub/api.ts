
import { Filter, Threshold } from '../broadStrategy/api';
import { _Error } from '../../../error';

// 获取播报订阅列表 request
export interface BroadcastListParams {
	Filters?: Array<Filter>
	Offset?: number,
	Limit?: number,
	AppId?: number
}
// 获取播报订阅列表 response
export interface BroadcastListRes {
	Error?: _Error;
	TotalCount: number
	RequestId: string
	BroadcastLists: Array<BroadcastList>
}
export interface BroadcastList {
	BroadcastId: number
	BroadcastName: string
	Scene: string
	GuardId: number
	GuardName: string
	CreateTime: string
	UpdateTime: string
	Creater: string
	Updater: string
	CustomerInfo: Array<CustomerInfo>
}
export interface CustomerInfo {
	AppId: number
	Name: string
}


// 获取播报订阅结果列表 request
export interface BroadcastListResultParams {
	Filters?: Array<Filter>
	Offset?: number,
	Limit?: number,
	AppId?: number
}
// 获取播报订阅结果列表 response
export interface BroadcastListResultRes {
	Error?: _Error;
	TotalCount: number
	RequestId: string
	BroadcastResults: Array<BroadcastResult>
}
export interface BroadcastResult {
	TaskId: string
	AppId: number
	Product: string
	StrategyId: number
	CNName: string
	Type: string
	SubType: string
	ActionStatus: number
	IsNormal: number
	Result: string
	SendTime: string
	Detail: string
}


// 查询播报订阅内容 request
export interface BroadcastContentParams {
	BroadcastId: number
	AppId?: number
}
// 查询播报订阅内容 response
export interface BroadcastContentRes {
	Error?: _Error;
	RequestId: string
	BroadcastSheet: BroadcastSheet
}
export interface BroadcastSheet {
	BroadcastId: number
	BroadcastName: string
	Scene: string
	GuardId: number
	Online: number
	GroupIds: Array<string>
	StartTime: string
	EndTime: string
	Period: number
	OnlyWorkdays: boolean
	BroadcastConfig?: Array<BroadcastConfig>
	Pending?: Array<Pending>
}
export interface BroadcastConfig {
	StrategyId: number
	Threshold: Array<Threshold>
}
export interface Pending {
	Product: string
	Name: string
	ResourceIds: Array<string>
}

// 修改播报订阅-规则内容 request
export interface BaseInfoParams {
	AppId?: number
	BroadcastId: number
	GroupIds: Array<string>
	StartTime: string
	EndTime: string
	Period: number
	OnlyWorkdays: boolean
	Updater: string
}
// 修改播报订阅-规则内容 response
export interface BaseInfoRes {
	Error?: _Error
	RequestId: string
	Message?: string
}

// 修改播报订阅-策略列表内容 request
export interface BroadcastUserParams {
	AppId?: number
	BroadcastId: number
	BroadcastConfig: Array<BroadcastPart>
	Updater: string,
	Enable?: boolean,
	CombinedBroadcastConfig?: any
}
export interface BroadcastPart {
	StrategyId: number
	Threshold: Array<Threshold>
}
// 修改播报订阅-策略列表内容 response
export interface BroadcastUserRes {
	Error?: _Error
	RequestId: string
	Message?: string
}


// 修改播报订阅-资源补充内容 request
export interface BroadcastResourcesParams {
	AppId?: number
	BroadcastId: number
	Resources: Array<Resources>
	Updater: string
}
export interface Resources {
	AppId: number
	Product: string
	Region: string
	ResourceId: string
	ResourceName: string
}
// 修改播报订阅-资源补充内容 response
export interface BroadcastResourcesRes {
	Error?: _Error
	RequestId: string
	Message?: string
}


// 修改播报订阅-状态内容 request
export interface BroadcastOnlineParams {
	AppId?: number
	BroadcastId: number
	Online: number
	Updater: string
}
// 修改播报订阅-状态内容 response
export interface BroadcastOnlineRes {
	Error?: _Error
	RequestId: string
	Message?: string
}


// 获取护航单下的产品实例 request
export interface GuardInstanceParams {
	Filters?: Array<Filter>
	Offset?: number,
	Limit?: number,
	AppId?: number
}
// 获取护航单下的产品实例 response
export interface GuardInstanceRes {
	Error?: _Error;
	TotalCount: number
	RequestId: string
	Instance: Array<Instance>
}
export interface Instance {
	AppId: number
	Product: string
	Region: string
	Zone: string
	InstanceId: string
	InstanceName: string
	InstanceTag: string
}


export interface MetricConfigRes {
	MetricConfigList: Array<MetricConfig>
	TotalCount: number
}

export interface MonitorProductsRes {
	MonitorProducts: Array<MonitorProductInfo>
	TotalCount: number
}

export interface MonitorProductInfo {
	ProductId: string;
	ProductCN: string;
	Namespaces: string[];
}

export interface MetricConfig {
	Id: number;
	Product: string;
	ProductName: string;
	Namespace: string;
	Metric: string;
	MetricName: string;
	Unit: string;
	Desc: string;
	Dimensions: Array<string>;
	Status: number;
	CreateTime: string;
	UpdateTime: string;
	Creater: string;
	Updater: string;
	IsAdmin: boolean;
}


export interface MonitorMetricsRes {
	MonitorMetricSet: Array<MonitorMetric>
	TotalCount: number
}

export interface MonitorMetric {
	Namespace: string;
	MetricName: string;
	MetricCNName: string;
	Unit: string;
	Dimensions: number;
}

export interface SupportBroadCastMetricRes {
	ProductList: Array<Product>
	ProductNameSpaceList: Array<ProductNameSpace>
	NameSpaceMetricList: Array<NameSpaceMetric>
}

export interface Product {
	ProductName: string
	Product: string
}

export interface ProductNameSpace {
	Product: string;
	NameSpaceList: string[];
}

export interface NameSpaceMetric {
	NameSpace: string;
	MetricList: Metric[];
}
export interface Metric {
	ChatBIMetricId: number;
	MetricName: string;
	Metric: string;
	Dimensions: string[];
	Unit: string;
}

export interface DescribeAllMetricsRes {
	TotalCount: number;
	MetricNameInfos: {
		Metric: string;
		MetricCNName: string;
	}[];
}

export interface DimensionInfoRes {
	DimensionInfoList: {
		DimensionId: number;
		DimensionName: string;
		NameSpace: string;
		SourceDataGetCondition: string;
	}
}
