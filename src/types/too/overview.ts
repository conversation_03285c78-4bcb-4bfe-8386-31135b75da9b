import { CommonResponse, Filter } from '../common';
/** 客户总览信息查询参数 */
export interface DescribeCustomerListRequest {
	/** SearchWord 代表Uin,AppID,CustomerName,Region  */
	SearchWord?: string;
	/** 排序字段，-字段表示降序，默认升序  */
	OrderBy?: string;
	/** 过滤的地区列表  */
	Regions?: Array<string>;
	/** 分页大小  */
	Limit?: number;
	/** 偏移，从多少条记录之后开始  */
	Offset?: number;
}
/** 客户信息数据集 */
export interface DescribeCustomerListResponse extends CommonResponse {
	/** 列表总数  */
	TotalCount: number;
	/** 客户总览信息列表  */
	ListUser: Array<User>;
}
/** 客户总览信息 */
export interface User {
	/** 主账号UIN  */
	Uin: string;
	/** APPID  */
	AppID: string;
	/** 客户简称  */
	CustomerName: string;
	/** 所属地域  */
	Region: string;
	/** 开通时间  */
	CreateTime: string;
	/** 用户的环境总数  */
	EnvironmentCount: number;
}
