import { CommonResponse, Filter } from '../common';
/** 创建模版的请求参数 */
export interface CreatePublicTemplateRequest {
	/** 模版内容  */
	Content: string;
	/** 模版名称  */
	Name: string;
	/** 模版描述  */
	Desc: string;
	/** 模版是否上线[Y:上线，N:不上线]  */
	IsOnline: string;
}
/** 模版操作响应体 */
export interface CreatePublicTemplateResponse extends CommonResponse {
	/** 模版ID  */
	TemplateID: string;
}
/** 删除模版请求参数 */
export interface DeletePublicTemplateRequest {
	/** 模版ID  */
	TemplateID: string;
}
/** 公有模版详情 */
export interface DescribePublicTemplateDetailResponse extends CommonResponse {
	/** 公有模版基础信息  */
	BaseInfo: PublicTemplateBaseInfo;
	/** 公有模版内容  */
	Content: string;
}
/** 公有模版列表请求参数 */
export interface DescribePublicTemplateListRequest {
	/** 搜索关键词  */
	SearchKey?: string;
	/** 排序字段  */
	OrderBy?: string;
	/** 过滤字段  */
	Filters?: Array<Filter>;
	/** 分页，每页显示条目数  */
	Limit?: number;
	/** 分页，当前页偏移量  */
	Offset?: number;
}
/** 公有模版响应体 */
export interface DescribePublicTemplateListResponse extends CommonResponse {
	/** 数据总数  */
	TotalCount: number;
	/** 列表数据  */
	PublicTemplateList: Array<PublicTemplateBaseInfo>;
}
/** 查询模版详情请求参数 */
export interface DescribeTemplateDetailRequest {
	/** 模版ID  */
	TemplateID: string;
}
/** 更新too公有模版请求体 */
export interface ModifyPublicTemplateRequest {
	/** 模版ID  */
	TemplateID: string;
	/** 模版内容  */
	Content: string;
	/** 模版名称  */
	Name: string;
	/** 模版描述  */
	Desc: string;
	/** 模版是否上线[Y:上线，N:不上线]  */
	IsOnline: string;
}
/** 公有模版基础信息 */
export interface PublicTemplateBaseInfo {
	/** 模版ID  */
	TemplateID: string;
	/** 模版名称  */
	Name: string;
	/** 版本号  */
	Version: string;
	/** 模版描述  */
	Desc: string;
	/** 模版是否上线[Y:上线，N:不上线]  */
	IsOnline: 'Y' | 'N';
	/** 创建时间  */
	CreateTime: string;
	/** 更新时间  */
	UpdateTime: string;
}
/** 模版是否上线 */
export enum IsOnline {
	/**  */
	Y = 0,
	/**  */
	N = 1,
}
/** 私有模版详情 */
export interface DescribePrivateTemplateDetailResponse extends CommonResponse {
	/** 私有模版基础信息  */
	BaseInfo: PrivateTemplateBaseInfo;
	/** 私有模版内容  */
	Content: string;
}
/** 私有模版列表请求参数 */
export interface DescribePrivateTemplateListRequest {
	/** 搜索关键词  */
	SearchKey?: string;
	/** 排序字段  */
	OrderBy?: string;
	/** 分页，每页显示条目数  */
	Limit?: number;
	/** 分页，当前页偏移量  */
	Offset?: number;
}
/** 私有模版列表响应体 */
export interface DescribePrivateTemplateListResponse extends CommonResponse {
	/** 数据总数  */
	TotalCount: number;
	/** 列表数据  */
	PrivateTemplateList: Array<PrivateTemplateBaseInfo>;
}
/** 私有模版基础信息 */
export interface PrivateTemplateBaseInfo {
	/** 模版ID  */
	TemplateID: string;
	/** 模版名称  */
	Name: string;
	/** 版本号  */
	Version: string;
	/** 模版描述  */
	Desc: string;
	/** 创建时间  */
	CreateTime: number;
	/** 更新时间  */
	UpdateTime: number;
	/** AppId  */
	AppId: string;
	/** Uin  */
	Uin: string;
	/** SubUin  */
	SubUin: string;
}
/** Job绑定的模版详情 */
export interface DescribeJobTemplateDetailRequest {
	/** 作业ID  */
	JobID: string;
}
/** job绑定模版的详情 */
export interface DescribeJobTemplateDetailResponse extends CommonResponse {
	/** 源模版ID  */
	OriginTemplateID: string;
	/** 模版内容  */
	Content: string;
	/** 模版名称  */
	Name: string;
	/** 模版描述  */
	Desc: string;
	/** 模版类型 */
	OriginTemplateType: string;
	/** 模版创建时间  */
	CreateTime: number;
	/** 模版更新时间  */
	UpdateTime: number;
}
