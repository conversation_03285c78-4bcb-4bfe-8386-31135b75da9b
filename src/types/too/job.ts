import { CommonResponse, Filter } from '../common';
/** 获取作业详情的请求参数 */
export interface DescribeJobDetailRequest {
	/** 作业ID  */
	JobID: string;
}
/** 获取作业详情的响应参数 */
export interface DescribeJobDetailResponse extends CommonResponse {
	/** 作业详情  */
	JobDetail: JobDetail;
}
/** 获取作业环境变量的请求参数 */
export interface DescribeJobEnvVarListRequest {
	/** 作业ID  */
	JobID: string;
}
/** 获取作业环境变量的响应参数 */
export interface DescribeJobEnvVarListResponse extends CommonResponse {
	/** 环境变量列表  */
	EnvVarList: Array<EnvVar>;
}
/** 获取作业主机的请求参数 */
export interface DescribeJobHostListRequest {
	/** 作业ID  */
	JobID: string;
	/** 分页，每页显示条目数  */
	Limit?: number;
	/** 分页，当前页偏移量  */
	Offset?: number;
	/** 过滤条件  */
	Filters?: Array<Filter>;
	/** 搜索关键词  */
	SearchKey?: string;
}
/** 获取作业主机的响应参数 */
export interface DescribeJobHostListResponse extends CommonResponse {
	/** 主机列表  */
	JobHostList: Array<JobHost>;
	/** 主机总数  */
	TotalCount: number;
}
/** 获取作业列表的请求参数 */
export interface DescribeJobListRequest {
	/** 搜索关键词  */
	SearchKey?: string;
	/** 过滤条件  */
	Filters?: Array<Filter>;
	/** 排序条件  */
	OrderBy?: string;
	/** 分页，每页显示条目数  */
	Limit?: number;
	/** 分页，当前页偏移量  */
	Offset?: number;
	/** 查询年份  */
	Year: number;
	/** 查询月份  */
	Month: number;
	/** APPID */
	AppID: string;
}
/** 获取作业列表的响应参数 */
export interface DescribeJobListResponse extends CommonResponse {
	/** 结果总数  */
	TotalCount: number;
	/** 作业详情集合  */
	JobList: Array<Job>;
}
/** 环境变量 */
export interface EnvVar {
	/** 环境变量键  */
	Key: string;
	/** 环境变量值  */
	Value: string;
	/** 是否为敏感信息  */
	Sensitive: boolean;
}
/**  */
export interface HostStatus {
	/**   */
	HostID: string;
	/**   */
	Status: string;
}
/** 作业列表详情 */
export interface Job {
	/** 作业ID  */
	ID: string;
	/** 作业名称  */
	Name: string;
	/** 作业状态  */
	Status: string;
	/** 作业描述  */
	Description: string;
	/** 作业创建时间  */
	CreateTime: number;
	/** 作业执行开始时间  */
	StartTime: number;
	/** 作业执行完成时间  */
	FinishTime: number;
}
/** 作业详情 */
export interface JobDetail {
	/** AppId  */
	AppID: string;
	/** Uin  */
	Uin: string;
	/** 作业ID  */
	ID: string;
	/** 作业名称  */
	Name: string;
	/** 作业状态  */
	Status: string;
	/** 作业描述  */
	Description: string;
	/** 任务进度百分比  */
	Progress: number;
	/** 作业创建时间  */
	CreateTime: string;
	/** 作业执行开始时间  */
	StartTime: string;
	/** 作业执行完成时间  */
	FinishTime: string;
	/** 作业环境信息  */
	Environment: Array<JobEnvironment>;
	/** 执行模板入口  */
	Entry: string;
}
/** 作业环境信息 */
export interface JobEnvironment {
	/** 环境ID  */
	ID: string;
	/** 主机信息  */
	Hosts: Array<string>;
}
/** 作业的主机信息 */
export interface JobHost {
	/** 公网IP  */
	PublicIP: string;
	/** 内网IP  */
	PrivateIP: string;
	/** 主机的作业执行状态  */
	Status: string;
	/** 主机所在的分组名称  */
	GroupName: Array<string>;
	/** 主机来源
 inventorys.v1.Source Source = 5;
实例id  */
	InstanceID: string;
	/** 实例名称  */
	InstanceName: string;
	/** Host内部id  */
	ID: string;
	/** 地域  */
	Region: string;
}
/** 作业状态 */
export enum JobStatus {
	/**  */
	Init = 0,
	/**  */
	DryRunning = 1,
	/**  */
	DryRan = 2,
	/**  */
	DryRunFailed = 3,
	/**  */
	Running = 4,
	/**  */
	Ran = 5,
	/**  */
	RunFailed = 6,
}
/**  */
export enum JobType {
	/**  */
	DryRun = 0,
	/**  */
	Run = 1,
}
/** GetJobLogRequest 获取job日志请求 */
export interface DescribeJobLogListRequest {
	/** job id  */
	JobID: string;
	/** 不传则返回总览日志  */
	HostID: string;
}
/** ListJobLogResponse 获取job日志响应 */
export interface DescribeJobLogListResponse extends CommonResponse {
	/** 日志数据，一行一个  */
	Logs: Array<string>;
}
/**  */
export interface DescribeWsConnectionListRequest {
	/** 偏移，从多少条记录之后开始  */
	Offset: number;
	/** 分页大小  */
	Limit: number;
}
/**  */
export interface DescribeWsConnectionListResponse {
	/** job ids  */
	JobIDs: Array<string>;
	/** 总记录数  */
	TotalSize: number;
}
