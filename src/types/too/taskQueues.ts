import { CommonResponse } from "../common";

export interface Filter {
    /** 需要过滤的字段  */
    Name: string;
    /** 字段的过滤值  */
    Values: Array<string>;
}
/** 获取任务队列列表的请求参数 */
export interface DescribeJobSchedulerListRequest {
    /** 搜索条件  */
	SearchWord?: string;
	/** 过滤字段  */
	Filters?: Array<Filter>;
	/** 排序字段  */
	OrderBy: string;
	/** 分页，每页显示条目数  */
	Limit: number;
	/** 分页，当前页偏移量  */
	Offset: number;
}
export interface JobSchedulerBaseInfo {
    /** 子任务ID  */
    JobSubnetID: string;
    /** vpc: 公有云   public: 外网    private:云梯  */
    InternetType: string;
    /** JobID信息  */
    JobID: string;
    /** 地域信息 */
    Region: string;
    /** VpcID  */
    VpcID: string;
    /** appID  */
    AppID: string;
    /** 模版信息  */
    JobTemplateUrl: string;
    /** EntryTemplate  */
    EntryTemplate: string;
    /** Envs  */
    Envs: string;
    /** Hosts  */
    Hosts: string;
    /** JobStatus 任务状态  */
    JobStatus: string;
    /** 创建时间  */
    CreateTime: number;
    ID: number;
	JobType: string;
}
/** 获取任务队列列表的响应参数 */
export interface DescribeJobSchedulerListResponse extends CommonResponse {
	/** 数据总数  */
	TotalCount: number;
	/** 列表数据  */
	JobSchedulerInfo: Array<JobSchedulerBaseInfo>;
}
/** 获取主题概览的请求参数 */
export interface DescribeGroupOffsetsListRequest {
	InstanceID?: string;
	Group?: string;
}
export interface GroupOffsetTopic {
    /** 主题名称  */
	Topic: string;
    /** 该主题分区数组，其中每个元素为一个 json object  */
    /** 注意：此字段可能返回 null，表示取不到有效值。  */
	Partitions: Array<GroupOffsetPartition>;
}
export interface GroupOffsetPartition {
	/** topic 的 partitionId  */
	Partition: number;
	/** 消费者提交的 offset 位置 -->消费的数量  */
	Offset: number;
    /** 支持消费者提交消息时，传入 metadata 作为它用，当前一般为空字符串  */
    /** 注意：此字段可能返回 null，表示取不到有效值。  */
	Metadata: string;
	/** 错误码  */
	ErrorCode: number;
	/** 当前 partition 最新的 offset -->总数  */
	LogEndOffset: number;
	/** 未消费的消息个数 -->剩余个数  */
	Lag: number;
}
/** 获取主题概览的响应参数 */
export interface DescribeGroupOffsetsListResponse extends CommonResponse {
	TotalCount: number;
	GroupOffsetTopic: Array<GroupOffsetTopic>;
}