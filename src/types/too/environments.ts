import { CommonResponse } from "../common";

export interface Environment {
	/** 环境id  */
	EnvID: string;
	/** 环境name  */
	EnvName: string;
	/** 环境描述  */
	EnvDesc: string;
	/** 该环境下实例数量  */
	InstanceCount: number;
	/** 组名称,一个环境下可有多个group  */
	GroupNames: Array<string>;
	/** 创建时间  */
	CreateTime: number;
	/** 更新时间  */
	UpdateTime: number;
	/** 来源  */
    Source: string;
    /** 用户appID  */
	AppID: number;
	/** 用户UIN  */
	Uin: string;
}
/** 获取环境列表的请求参数 */
export interface DescribeEnvironmentListRequest {
	/** 搜索条件  */
	SearchWord: string;
	/** 排序字段  */
	OrderBy: string;
	/** 列表长度限制  */
	Limit: number;
	/** 列表开始Index  */
	Offset: number;
}
/** 获取环境列表的响应参数 */
export interface DescribeEnvironmentListResponse extends CommonResponse {
	/** 总条数  */
	TotalCount: number;
	/** 环境详情列表  */
	Environments: Array<Environment>;
}
export interface CvmInstance {
	hostId?: string;
	id: string;
	name: string;
	publicIp: string | null;
	privateIp: string;
	zone: string;
	vpc: string;
	subnet: string;
	port?: number | null;
	username?: string | null;
	password?: string | null;
	type?: string;
}
export interface FileHost {
	hostId?: string;
	ip: string;
	port?: number | null;
	group: Array<string> | null;
	region: string | null;
	desc: string | null;
	provider: string | null;
	username?: string | null;
	password?: string | null;
	type?: string;
}
export type IpHost = FileHost;
/** 查询过滤器 */
export interface Filter {
	/** 需要过滤的字段  */
	Name: string;
	/** 字段的过滤值  */
	Values: Array<string>;
}
/** 获取实例列表的请求参数 */
export interface DescribeEnvironmentHostsRequest {
	/** 环境id  */
	EnvID: string;
	/** 过滤条件 支持GroupName查询 */
	Filters?: Array<Filter>;
	/** 列表长度限制  */
	Limit?: number;
	/** 列表开始Index  */
	Offset?: number;
	/** 搜索条件  */
	SearchWord?: string;
	/** 排序字段  */
	OrderBy?: string;
}
export interface EnvHost {
	/** 组  */
	GroupName: Array<string>;
	/** 地域  */
	Region: string;
	/** 实例id  */
	InstanceID: string;
	/** 实例name  */
	InstanceName: string;
	/** 公网ip  */
	PublicIP: string;
	/** 内网ip  */
	PrivateIP: string;
	/** 实例port  */
	InstancePort: number;
	/** vpc_id  */
	Vpc: string;
	/** 子网id  */
	SubnetID: string;
	/** 来源  */
	Source: string;
	/** 是否使用默认密码  */
	IsDefaultPwd: boolean;
	/** 可用区  */
	Zone: string;
	/** 云厂商  */
	Provider: string;
	/** 描述  */
	HostDesc: string;
	/** HostID 主机ID  */
	HostID: string;
	/** Username 主机用户名  */
	Username: string;
}
/** 获取实例列表的响应参数 */
export interface DescribeEnvironmentHostsResponse extends CommonResponse {
	/** 总条数  */
	TotalCount: number;
	/** host列表  */
	Hosts: Array<EnvHost>;
}