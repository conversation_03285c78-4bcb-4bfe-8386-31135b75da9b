
export interface DescribeAlarmPolicyParams {
	AppId: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface FilterItem {
	Name: string,
	Values: Array<string|number>
}

export interface DescribeAlarmListsParams {
	Limit: number,
	Offset: number,
	Filters?: Array<FilterItem>,
	OnlyData?: boolean,
	ShowError?: boolean,
	AppId: number
}

export interface DescribeAlarmDetailParams {
	AppId: number,
	Id: string,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface ModifyAlarmParams {
	AppId: number,
	Name: string,
	Id: string,
	Block: number,
	Feedback: number,
	Comment: string,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DescribeAlarmCustomersParams {
	Name: string,
	Filters: Array<FilterItem>,
	Limit: number,
	Offset: number
}

export interface DescribeCustomerPolicyParams {
	AppId: number,
	Name: string,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface ParamsItem {
	Id: number,
	In: boolean,
	Out: boolean,
	Front: boolean
}

export interface ModifyCustomerPolicyParams {
	Name: string,
	AppId: number,
	Modify: Array<ParamsItem>,
	OnlyData?: boolean,
	ShowError?: boolean,
}
