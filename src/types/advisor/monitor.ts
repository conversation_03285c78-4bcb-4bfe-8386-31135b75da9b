import { _Error } from "../error";

export interface getAdvisorMonitorRes {
	RequestId: string;
	Error?: _Error;
	ResourceTotalList: Array<ResourceAssessmentTotal>;
	StrategyTotalList: Array<StrategyAssessmentTotal>;
	/**  授权用户总数列表 */
	ListUsers: Array<UserNumber>;
}
interface UserNumber {
	/** 天，日期格式为："YYY-MM-DD"  */
	Date: string;
	/** 授权用户总数 */
	Count: number;
}
interface ResourceAssessmentTotal {
	/** 高风险数量 */
	HighRiskCount: number;
	/** 中风险数量 */
	MediumRiskCount: number;
	/** 健康数量 */
	HealthCount: number;
	/** 忽略数量 */
	IgnoreCount: number;
	/** 扫描日期，日期格式："YYYY-MM-DD" */
	Date: string;
	/** 资源类型 1 代表资源评估，2 代表策略评估  */
	resourceType: number;
}
interface StrategyAssessmentTotal {
	/** 高风险数量 */
	HighRiskCount: number;
	/** 中风险数量 */
	MediumRiskCount: number;
	/** 健康数量 */
	HealthCount: number;
	/** 忽略数量 */
	IgnoreCount: number;
	/** 扫描日期，日期格式："YYYY-MM-DD" */
	Date: string;
	/** 资源类型 1 代表资源评估，2 代表策略评估  */
	resourceType: number;
}
