export interface ServiceDetailListItem {
	ServerType: string,
	OrderName: string,
	OrderId: number,
	AppId: number,
	Uin: number,
	CustomerName: string,
	CreateTime: string,
	ExpectServiceTime: string,
	State: string,
	UpdatePerson: string,
	UpdateTime: string,
	AttachmentInfoList: Array<AttachmentInfoItem>
}
export interface DescribeIsaHighAvailabilityServiceOrderListResParams {
	AppId?: string,
	RequestId?: string,
	TotalCount?: number,
	ServiceDetailList: Array<ServiceDetailListItem>
}
export interface DescribeIsaHighAvailabilityServiceOrderListParams {
	AppId: string,
	CustomerName: string,
	Limit: number,
	Offset: number,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface DescribeIsaHighAvailabilityServiceDetailParams {
	OrderId: number,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface DescribeIsaHighAvailabilityServiceDetailResData {
	OrderName: string,
	OrderId: number,
	CustomerInfo: string,
	OrderNeed: string,
	ExpectServiceTime: number,
	InvolveProductList: Array<string>,
	ContactPerson: string,
	ContactPhone: string,
	ContactEmail: string,
	OtherInformation: string,
	EvaluateResult: string,
	EvaluateReason: string,
	Attachment: Array<AttachmentInfoItem>,
	RequestId?: string
}

export interface DescribeIsaHighAvailabilityServiceAssessParams {
	OrderId: number,
	EvaluateResult: number,
	EvaluateReason: string,
	UpdatePerson: string,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface DescribeIsaHighAvailabilityServiceDoneParams {
	OrderId: number,
	UpdatePerson: string,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface AttachmentInfoItem {
	AttachmentInstruction: string,
	AttachmentName: string,
	CosUrl?: string
}

export interface DescribeDeleteAttachmentParams {
	OrderId: number,
	AttachmentInfoList: Array<AttachmentInfoItem>,
	UpdatePerson: string,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface DescribeCosUploadUrlParams {
	OrderId: number,
	AttachmentInfoList: Array<AttachmentInfoItem>,
	UpdatePerson: string,
	ShowError?: boolean,
	OnlyData?: boolean
}