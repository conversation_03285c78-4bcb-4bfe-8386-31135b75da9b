import { _Error } from "../error";

export interface GetStrategiesConfigProps {
	Group?: number;
	Product?: string;
	StrategyID?: number;
	Online?: number;
	Env?: string;
}

export interface GetStrategiesConfigRes {
	StrategyConfigList: Array<StrategyConfig>;
	RequestId: string;
	Error?: _Error;
}
interface StrategyConfig {
	GroupId: number;
	Online: boolean;
	Env: string;
	StrategyId?: number;
	Product: string;
	Name: string;
	Desc: string;
	Repair: string;
	Notice: string;
	Ignore: string;
	Conditions: Array<ConfigCondition>;
	IsSupportCustom?: boolean;
}
interface ConfigCondition {
	ConditionId?: number;
	Level: number;
	Desc: string;
}

export interface CreateStrategiesConfigProps {
	StrategyConfigList: Array<StrategyConfig>;
}
export interface CreateStrategiesConfigRes {
	RequestId: string;
	Error?: _Error;
	result: boolean;
}

export interface DeleteStrategiesConfigProps {
	StrategyID: number;
}
export interface DeleteStrategiesConfigRes {
	RequestId: string;
	Error?: _Error;
	result: boolean;
}

export interface ModifyStrategyConfigProps {
	Config: StrategyConfig;
}
export interface ModifyStrategyConfigRes {
	RequestId: string;
	Error?: _Error;
	result: boolean;
}

/**
 * 表格展示的策略信息
 */
export interface Strategy {
	strategyId: number;
	name: string;
	groupId: number;
	product: string;
	desc: string;
	online: boolean;
	env: string;
	isSupportCustom?: boolean;
}
