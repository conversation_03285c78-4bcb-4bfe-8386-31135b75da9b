export interface DescribeStrategiesParams {
	Limit: number,
	Offset: number,
  Name: string,
  Env: number,
  AppId: number,
  OnlyData?: boolean,
  ShowError?: boolean
  Filters: Array<{
    Name: string,
    Values: Array<string>
  }>
}

export interface AdvisorStrategyInfo{
	StrategyId: number,
	StrategyName: string,
	StrategyNameEn?: string,
	Env: string,
	Online: number,
	GuardOnline: number,
	GuardVolumeOnline: number,
	Product: string,
	Group: string,
	Desc: string,
	DescEn?: string,
	Repair: string,
	RepairEn?: string,
	Notice: string,
	NoticeEn?: string,
	Ignore: string,
	IgnoreEn?: string,
	IsSupportUserPolicy: number,
	InstanceType: string,
	Score: number
}
export interface AdvisorConditionInfo{
	StrategyId: number,
	ConditionId: number,
	Level: number,
	Desc: string,
	DescEn?: string,
	Repair: string,
	RepairEn?: string,
	Policy: string,
	PolicyV2: string,
	Score: number,
	Priority: number
}
export interface AdvisorFrontendInfo{
	StrategyId: number,
	Fileds: string,
	Url: string,
	UrlEn?: string,
	LinkedId: string,
	PriId: string
}
export interface FullStrategyInfo {
	AdvisorStrategyInfo: AdvisorStrategyInfo,
	AdvisorConditionInfo: Array<AdvisorConditionInfo>,
    AdvisorFrontendInfo: AdvisorFrontendInfo
}

export interface ModifyStrategyInfoParams {
	StrategyInfo?: FullStrategyInfo,
	ModifyType?: number,
	StrategyId?: number,
	Online?: number,
	GuardOnline?: number,
	GuardVolumeOnline?: number,
	DeleteStatus?: number,
	Env?: number,
  Reason?: string,
  Type?: string,
  Updater?: string,
  ShowError?: boolean,
  OnlyData?: boolean
}

export interface UpdateConfigToProductionParams {
  SourceEnv: number,
	Env: number,
	ConfigType: number,
	Id: number
}

export interface DescribeMonitorConfigInfoParams {
  Limit: number,
  Offset: number,
  Name: string,
  Env: number,
  AppId: number,
  OnlyData?: boolean,
  ShowError?: boolean
  Filters: Array<{
    Name: string,
    Values: Array<string>
  }>
}

export interface MonitorConfigInfo {
	Id: number,
    MetricName: string,
	Namespace: string,
	Product: string,
	InsType: string,
	Period: number,
	Percents: Array<number>,
	Days: number,
	DimensionName: string,
	Online: number,
	UseInsIdAsDimension: number
}

export interface ModifyMonitorConfigInfoParams {
	Id?: number,
	Online?: number,
	DeleteStatus?: number,
	MonitorConfigInfo?: MonitorConfigInfo,
	ModifyType?: number,
	Env?: number
}

export interface DescribeProductConfigInfoParams {
  Limit: number,
  Offset: number,
  Name: string,
  Env: number,
  AppId: number,
  OnlyData?: boolean,
  ShowError?: boolean
  Filters: Array<{
    Name: string,
    Values: Array<string>
  }>
}
export interface ProductConfigInfo {
	SubProduct: string,
	FatherProduct: string,
	SubProductZh: string,
	SubProductEn: string,
	MonitorDataV3: number
}
export interface ModifyProductConfigInfoParams {
	SubProduct?: string,
	MonitorDataV3?: number,
	DeleteStatus?: number,
	ProductConfigInfo?: ProductConfigInfo,
	ModifyType?: number,
	Env?: number
}