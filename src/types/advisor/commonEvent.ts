import { FaultItem } from "@src/types/advisor/faultNotification";

export interface FiltersItem {
	Name: string,
	Values: Array<string>
}

export interface DescribeEventListsParams {
	AppId: number,
	Filters: Array<FiltersItem>,
	Limit: number,
	Offset: number,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface CreateEventParams {
	Name: string,
	AppId: number,
	Status: number,
	Id: number,
	Title?: string,
	Product?: string,
	NoticeTime?: string,
	PrivateDesc?: string,
	PrivateAdvice?: string,
	PublicDesc?: string,
	PublicAdvice?: string,
	Affected?: Array<AffectedItem>,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface CreateEventChatParams {
	Name: string,
	Id: number,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface AffectedItem {
	AppId: number,
	Uin: number,
	Customer: string,
	Owner: string,
	Resources: Array<string>
}

export interface DescribeEventDetailParams {
	AppId: number,
	Name: string,
	Id: number,
	Limit?: number,
	Offset?: number,
	Filters?: Array<FiltersItem>,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface DescribeInternalEventDetailParams {
	AppId: number,
	Name: string,
	Id: number,
	Limit?: number,
	Offset?: number,
	Filters?: Array<FiltersItem>,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface CreateEventApprovalParams {
	AppId: number,
	Name: string,
	Id: number,
	IsApproved: boolean,
	Reason?: string
}

export interface ConfigGroup {
	IsCreateActive?: boolean,
	Tip?: string,
	EventLists?: Array<FaultItem>,
	TotalCount?: number,
	RequestId?: string
}

export interface modifyEventCommentParams {
	AppId: number,
	Name: string,
	Id: number,
	OnlyData?: boolean,
	ShowError?: boolean,
	Comment: string
}