import { _Error } from '../error';

// 新增确认状态
export const ConfirmStatus = '32c';
export const ConfirmText = '护航负责人确认';

// 重新发起巡检入参
export interface ReInspectionParams {
	GuardId: number,
	Operator: string,
}

export interface getAssessResultProps {
	AppID: string;
	UIN?: string;
	TaskID?: string;
}

// 查询报告结果返回
export interface DescribeDownloadTaskRes {
	PrivateCosUrl: string;
	PrivateCosUrlPdf: string;
	PrivateTaskStatus: string,
	PublicCosUrl: string,
	PublicCosUrlPdf: string,
	PublicTaskStatus: string,
	[propName: string]: any;
}
// 下载报告接口返回
export interface downloadReportRes {
	RequestId?: string,
	ResultId?: string,
	PrivateResultId?: string,
	TaskStatus?: string,
	Error?: _Error;
}
// 获取评估结果总览
export interface getGuardSheetRes {
	RequestId: string;
	Error?: _Error;
	Guard: Array<GuardParams>;
	TotalCount?: number,
}

// 客户名称返回
export interface getCustomerNameRes {
	RequestId: string;
	Error?: _Error;
	CustomerName: string,
	IsAuthorized?: boolean,
	HasAfterSale?: boolean,
	Uin?: string
}

export interface Filter {
	Name: string,
	Values: Array<string>,
}

export interface GuardItem {
	AppId: number,
	EndTime: string,
	GuardId: string,
	GuardName: string,
	Origin: string,
	Project: string,
	Standard: string,
	StartTime: string,
	Status: string,
}

// 护航单创建params
export interface GuardParams {
	AppIdOwner?: string,
	SubmittedBy?: string,
	SubmitTime?: string,
	GuardId?: number,
	GuardName?: string,
	Standard?: number,
	CustomerAuthStatus?: number,
	Project?: number,
	StartTime?: string,
	EndTime?: string,
	StartClock?: string,
	EndClock?: string,
	Responser?: string, // TAM负责人
	CreatedBy?: string, // 创建人
	UpdatedBy?: string, // 更新人
	MainAppId?: number,
	CustomerName?: string, // 客户名称
	CustomerContacts?: string, // 客户接口人名字
	CustomerPhone?: string,
	Products?: Array<string>,
	RejectedProducts?: Array<string>,
	Origin?: number,
	Status?: number,
	RelatedAppId?: Array<number>,
	RelatedCustomerNames?: Array<string>,
	ExpectedEnlargeTimes?: number,
	ExpectedEnlargeDays?: number,
	PressureTestPlan?: string,
	LimitStrategy?: string,
	BusinessEmergencyPlan?: string,
	InstanceTemplate?: Array<InstanceItem>,
	ProductTemplate?: Array<ProductTemplateItem>,
	TemplateId?: string,
	OnsiteTime?: Array<string>,
	OnsiteProducts?: Array<string>,
	GrafanaDashboardUrl?: string,
	ProductDesc?: Array<ProductDescItem>,
	Approvals?: ApprovalInfo,
	AppId?: number,
	RiskData?: GuardRiskData,
	ClosedNetworkDemand?: string,
	StatementOfNeeds?: string,
	InstanceTemplateCount?: string
	InstanceImportantTemplateCount?: string,
	GuardServiceStatus?: Array<any>,
	GuardServiceIds?: Array<any>
	GuardService?: Array<any>,
	GuardInfoSupportUpdate?: boolean,
	CronType?: number
	// 对接其他平台
	Platform?: string,
	PlatformUniqueId?: string,
	GuardDashboardExist?: boolean,
	StandardDesc?: any,
	ImportantGuardTime?: Array<string>
	CloudGuardBaseInfoOtherPlatform?: any
	ActualDays?: number;
	MapName?: string
}

// 护航搜索条件
export interface DescribeGuardFilter {
	guardId: string,
	guardName: string,
	standardList: Array<string>,
	industryList: Array<string>,
	statusList: Array<string>,
	created_by: string,
	responser: string,
	customerName: string,
	appId: string,
	productList: Array<string>,
	originList: Array<string>,
	projectList: Array<string>,
	startTime?: any,
	endTime?: any,
	participants?: any
}

// 护航审批信息
export interface ApprovalInfo {
	AfterSalesStatus?: AfterSalesStatus,
	ExpertStatus?: Array<ExpertStatus>,
	ScanResultStatus?: Array<ScanResultStatus>,
}

// 护航审批信息：售后
export interface AfterSalesStatus {
	Handler?: string,
	Supporter?: string,   // 派遣人
	IsApproved?: boolean, // 售后审批
	IsConfirm?: boolean,  // 护航负责人审批
	State?: number,
}

// 护航审批信息：专项分配
export interface ExpertStatus {
	Handler?: string,
	IsApproved?: boolean,
	Product?: string,
	ProductName?: string,
}

// 护航审批信息：结果审批
export interface ScanResultStatus {
	Handler?: string,
	IsApproved?: boolean,
	Product?: string,
	ProductName?: string,
}

// 护航风险数量
export interface GuardRiskData {
	HighRiskStrategyCount: number,
	MediumRiskStrategyCount: number,
}

// 护航巡检结果
export interface GuardResultParams {
	// Id: string,
	ResultId: number,
	AppId: string,
	GuardId: string,
	Product: string,
	StrategyName: string,
	RiskLevel: string,
	StrategyDesc: string,
	InstanceIds: string,
	Repair: string,
	TaskType: string,         // "guardScanTaskType" or "guardVolumeTaskType"
	EvaluateMethod: number,
	Env: string,
	Comment: string,
}

// 删除巡检结果参数
export interface DeleteGuardScanResultParams {
	AppId: number,
	GuardId: number,
	Product: string,
	Ids: Array<ScanResultId>
}

// 巡检结果ID(自定义风险策略的ResultId、预定义风险策略的StrategyId)
export interface ScanResultId {
	StrategyId: number,
	ResultId: number,
}

// 审核单信息
export interface ApprovalProgressRet {
	RequestId?: string,
	Error?: _Error;
	TotalCount?: number,
	Progress?: Array<ApprovalProgressParams>
}

// 审核单信息
export interface ApprovalProgressParams {
	Id?: number,         // 审核ID
	TaskName?: string,   // 审核任务名称
	TaskType?: number,   // 审核任务类型
	Status?: number,     // 0(未处理), 1(已同意), -1(已中止), -2(已驳回)
	Handler?: string,
	GuardId?: number,
	GuardName?: string,
	Project?: number,
	Product?: string,
	StartTime?: string,
	EndTime?: string,
	MainAppId?: number,
	Origin?: number,
	IsAgree?: number
}

// 审核列表信息
export interface ApprovalResultRet {
	RequestId?: string,
	Error?: _Error;
	ApprovalId?: string,
	ApprovalType?: number,                // 审核任务类型
	ApprovalStatus?: number,
	FrontendField?: Array<FrontendField>, // 结果实例字段表头
	GuardId?: number,
	GuardName?: string,
	MainAppId?: number,
	CustomerInfo?: Array<AppIdCustomerNameDict>,
	LastTime?: string,
	Project?: number,
	HasOpsPermission?: boolean,
	ScanResult?: Array<ApprovalResultParams>,
	TransferRoute?: Array<Transfer>,
	SubApprovalFinished?: boolean
}

export interface Transfer {
	Handlers: Array<string>,
	UpdateTime: string,
}

// 审核详细信息
export interface ApprovalResultParams {
	// TaskName?: string,                       // 审核任务名称
	AppId?: number,
	Instance?: any,                             // 结果实例数据
	VolumeScanResult?: Array<VolumeResult>,     // 容量巡检策略
	ScanResult?: Array<ScanResult>,             // 隐患巡检策略
	EmergencyPlan?: Array<EmergencyPlanResult>, // 应急方案
	Comment?: string,                            // 产品备注
}

// 应急预案ID
export interface EmergencyPlanId {
	PlanId: number, // 如果非0，表示预定义的应急预案
	ItemId: number, // 如果非0，表示自定义的应急预案，包括从基于预定义修改或新建的预案
}

// 护航结果映射表头
export interface FrontendField {
	Field: string,
	FieldName: string,
	FieldType: string,
	FieldDict: any,
}

// 容量巡检解决方案。类型同 ScanResult。
export interface VolumeResult {
	AppId: number,
	// Id: number,
	ResultId: number,
	StrategyId: number,
	StrategyName: string,
	StrategyDesc: string,
	Repair: string,
	RiskLevel: number,
	EvaluateMethod: string,
	Env: string,
	InstanceIds: string,
	Comment: string,
	TaskType?: string,
}

// 隐患巡检解决方案。类型：a）自定义策略。ResultId!=0 && StrategyId==0。b）预定义策略。ResultId==0 && StrategyId!=0。
export interface ScanResult {
	AppId: number,
	// Id: number,         // 自定义策略。可以新建或从预定义策略修改而来。
	ResultId: number,      // 自定义策略。可以新建或从预定义策略修改而来。
	StrategyId: number,    // 预定义策略。
	StrategyName: string,
	StrategyDesc: string,
	Repair: string,
	RiskLevel: number,
	EvaluateMethod: string, // 评估方式，0:人工，1:自动。
	Env: string,
	InstanceIds: string,
	Comment: string,
	TaskType?: string,
}

// 全量巡检结果里面返回的隐患、容量结果，近同 ScanResult、VolumeResult 类型
export interface GuardScanResult {
	ResultId: number,
	StrategyId: number,
	StrategyName: string,
	RiskLevel: number,
	StrategyDesc: string,
	InstanceIds: string,
	Repair: string,
	TaskType: string,       // 任务类型，guardScanTaskType，guardVolumeTaskType
	EvaluateMethod: string,
	Env: string,
	Comment: string,
	ProcessComment: string,
	ProcessStatus: number,  // 处理状态，0:未处理，1:已处理
}

export interface GuardEmergencyPlan {
	'PlanId': number,
	'ItemId': number,
	'RiskScenario': string,
	'Measure': string,
	'Env': string
}

// 隐患巡检实例结果
export interface ScanResultInstance {
	RequestId?: string,
	FrontendField?: Array<FrontendField>, // 结果实例字段表头
	Instance: Array<any>,
	TotalCount: number,
}

/**
 * 应急预案结果。返回记录类型：
 * a）返回预定义应急预案( PlanId!=0 && ItemId==0 )
 * b）返回自定义应急预案( PlanId==0 && ItemId!=0 )
 * c）返回预定义修改后的应急预案( PlanId!=0 && ItemId!=0 )
 */
export interface EmergencyPlanResult {
	PlanId: number,  // 预定义应急预案ID。
	ItemId: number,  // 自定义应急预案ID。
	RiskScenario: string,
	Measure: string,
	Env: string,
}

export interface EmergencyInsertRecordParams {
	Id?: number,
	Product: string,
	RiskScenario: string,
	Measure: string,
	Env: string
}

// 护航实例数据结构--包括容量策略
export interface InstanceItem {
	Product: string,
	AppId: number,
	Region: string,
	Zone: string,
	InstanceId: string,
	InstanceName: string,
	InstanceTag: string,
	Policy: Array<policyItem>,
	Extra?: any
}
// 容量策略
export interface policyItem {
	MetricName: string,
	CNName: string,
	ENName: string,
	Days: number,
	Value: number | string,
	Unit: string,
	IsRequired: boolean,
	Type: string,
	Desc: string,
	OtherValue?: any
}

// 产品维度护航策略
export interface ProductTemplateItem {
	AppId: number,
	Product: string,
	Regions: Array<string>,
	Policy: Array<ProductPolicyItem>,
}

// 产品维度护航指标
export interface ProductPolicyItem {
	MetricName: string,
	CNName: string,
	ENName: string,
	Value: string,
	Unit: string,
	IsRequired: boolean,
	Type: string,
	Desc: string,
	FieldType: string,
}

// 护航任务产品描述
export interface ProductDescItem {
	AppId: number,
	IsAuthorized?: boolean,
	Product: string,
	IsProductSupported?: boolean,
	InstanceIds?: string,
	Comment?: string,
}

// 护航单状态映射
export const StatusDict = new Map([
	[1, '草稿'],
	[2, '订单已提交'],
	[31, '售后审批（TAM）'],
	[32, '正在巡检中'],
	[33, '专项人员分配（专项接口人）'],
	[34, '巡检结果审批（专项Owner）'],
	[36, '巡检报告生成中'],
	[37, '巡检报告已完成'],
	[40, '实例变更中'],
	[41, '重新巡检中'],
	[48, '巡检风险审批（护航负责人）'],
	[50, '护航巡检已完成'],
	[-1, '流程中止'],
	[-2, '巡检任务异常'],
	[-3, '审核任务异常'],
]);

// 新增状态映射
export const AddStatusDict = new Map([
	['32a', '售后确认架构图审批'],
	['32b', '护航无架构图特殊审批'],
	['32c', '护航负责人确认'],
]);

// 护航单来源映射
export const OriginDict = new Map([
	[0, '运营端'],
	[1, '租户端'],
]);

// 护航单类型展示映射
export const ShowStandardDict = new Map([
	[0, '标准护航'],
	[1, '售中迁云割接护航'],
	[3, '自助护航'],
]);

// 护航单类型选择映射
export const StandardDict = new Map([
	[0, '标准护航'],
	[1, '售中迁云割接护航'],
	[2, '安全重保护航'],
	[3, '自助护航'],
]);


// 审批单状态映射
export const ApvlStatusDict = new Map([
	[0, '未处理'],
	[1, '已处理'],
	[-1, '已驳回'],
	[-2, '已中止'],
]);

// 审批单状态映射
export const ApvlTypeDict = new Map([
	[0, '临时巡检审核'],
	[1, '巡检结果审批（专项Owner）'],
	[2, '售后审批（TAM）'],
	[7, '售后确认架构图'],
	[8, '护航无架构图特殊审批'],
	[3, '专项人员分配（专项接口人）'],
]);

// 是否可以对外
export const EnvTypeDict = new Map([
	['public', '外部'],
	['private', '内部'],
	['', '未知'],
]);

// 风险等级
export const RiskLevelTypeDict = new Map([
	[2, '中风险'],
	[3, '高风险'],
]);

// 评估方式
export const EvaluateWayDict = new Map([
	[0, '人工'],
	[1, '自动'],
]);

export function map2options(m: Map<number, string>) {
	const options: Array<{ text: string, value: string }> = [];
	m.forEach((v, k) => {
		options.push({ text: v, value: k.toString() });
	});
	return options;
}

// 资源搜索 FilterInfoItem
export interface FilterInfoItem {
	FilterAlias: string,
	FilterName: string,
	IsSupportFuzzy: boolean
	Type: string,
}
// 资源搜索 YunapiFilterPolicy
export interface YunapiFilterPolicyItem {
	Product: string,
	FilterInfo: Array<FilterInfoItem>
}

export interface Project {
	Id: number,
	Name: string,
}

export interface Projects {
	Projects: Array<Project>,
	Error?: _Error;
}

// 应急预案
export interface EmergencyPlanParams {
	CreateTime?: string,
	CreatedBy?: string,
	Env?: string,                        // 是否对外
	Id?: number,
	Measure?: string,                    // 应对措施或保障方案
	Product?: string,                    // 产品
	RiskScenario?: string,               // 场景
	RiskScenarioCategory?: string,       // 标题
	UpdateTime?: string,
	UpdatedBy?: string,
}

// 售后审批基本信息
export interface AfterSalesApprovalRet {
	Error?: _Error;
	AfterSalesApproval: AfterSalesApproval,
	RequestId: string,
}

// 售后审批基本信息
export interface AfterSalesApproval {
	ApprovalId?: number,
	ApprovalName?: string,
	Creator: string,
	GuardId: number,
	GuardName: string,
	CustomerInfo: Array<AppIdCustomerNameDict>,
	Project: number,
	Status: number,
	Standard: number,
	StartTime: string,
	EndTime: string,
	OnsiteTime: Array<string>,
	OnsiteProducts: Array<string>,
	Supporter: Array<string>,
	Reason: string,
	IsAgree: boolean,
	HasOpsPermission: boolean,
	ClosedNetworkDemand?: string,
	StatementOfNeeds?: string,
	CronType?: number,
	ImportantGuardTime?: Array<string>
}

export interface AppIdCustomerNameDict {
	AppId: number,
	CustomerName: string,
}

// 专项审批基本信息
export interface ExpertApprovalRet {
	Error?: _Error;
	ExpertApproval: ExpertApproval,
	FrontendField?: Array<ExpertApprovalInstanceFrontendField>,
	Instance?: Array<Object>,
	RequestId: string,
	InstanceTotalCount?: number
}

// 专项审批基本信息
export interface ExpertApproval {
	ApprovalId?: number,
	ApprovalName?: string,
	Creator: string,
	GuardId: number,
	GuardName: string,
	Product: string,
	CustomerInfo: Array<AppIdCustomerNameDict>,
	Project: number,
	Status: number,
	Standard: number,
	StartTime: string,
	EndTime: string,
	OnsiteTime: Array<string>,
	OnsiteProducts: Array<string>,
	Supporter: Array<string>,
	Follower: Array<string>,
	Reason: string,
	IsAgree: number,  // 1(同意)，2(仅在线支持)，-1(驳回)
	HasOpsPermission: boolean,
	ClosedNetworkDemand?: string,
	StatementOfNeeds?: string,
	CronType?: number,
	ImportantGuardTime?: Array<string>
}

// 专项分配实例
export interface ExpertApprovalInstanceFrontendField {
	Field: string,
	FieldDict: Array<{ Key: string, Value: string }>,
	FieldName: string,
	FieldType: string,
}


// 专项审批入参
export interface ExpertApprovalParams {
	ApprovalId?: number,
	GuardId: number,
	AppId?: number,
	Product: string,
	IsAgree: number,  // 1(同意)，2(仅在线支持)，-1(驳回)
	Supporter: Array<string>,
	Reason: string,
	Follower: Array<string>,
	Operator: string,
}

export interface TransferGuardApprovalParams {
	GuardId: number,
	AppId?: number,
	ApprovalId: number,
	Product: string,
	Handlers: Array<string>,
	Operator: string
}


// 一键入群返回信息
export interface PushGuardChatRet {
	Code: number,
	Message: string,
	Error?: _Error;
	RequestId?: string,
}

// 护航实例修改入参
export interface ModifyGuardInstancesParams {
	TemplateId?: string,
	GuardId: number,
	AppId: number,
	ModifiedProduct?: Array<ModifiedProductItem>,
	ModifiedPolicyProduct?: Array<ModifiedProductItem>,
	ProductDesc?: Array<ProductDescItem>,
	InstanceTemplate?: Array<InstanceItem>,
	ProductTemplate?: Array<ProductTemplateItem>,
	Operator: string,
}

// 护航实例修改对应的产品对象
export interface ModifiedProductItem {
	AppId: number,
	Product: string,
}

// 护航实例修改返回
export interface ModifyGuardInstancesRet {
	Error?: _Error;
	RequestId?: string,
}

// 处理巡检结果请求
export interface TamConfirmParams {
	GuardId: number,
	AppId: number,
	Reason: string,
	UserName: string
}

// 处理巡检结果请求
export interface HandleGuardScanResultParams {
	GuardId: number,
	AppId: number,
	ScanResult: Array<HandleScanResultItem>,
}

export interface HandleScanResultItem {
	ResultId: number,
	StrategyId: number,
	AppId: number,
	Product: string,
	TaskType: string,
	EvaluateMethod: string,
	Env: string,
	Comment: string,
	ProcessComment: string
}


// 处理巡检结果返回
export interface HandleGuardScanResultRet {
	Error?: _Error;
	RequestId?: string,
}

// 护航全产品巡检结果请求
export interface DescribeGuardScanResultParams {
	GuardId: number,
	AppId: number,
	Operator: string,
}

// 护航全产品巡检结果返回
export interface DescribeGuardScanResultRet {
	Error?: _Error;
	RequestId?: string,
	HasOpsPermission: boolean,
	ScanResult: Array<GuardResultItem>,
	IsScanApprovalSubmit?: number,
}

export interface GuardResultItem {
	AppId: number,
	Product: string,
	VolumeScanResult: Array<GuardScanResult>,
	ScanResult: Array<GuardScanResult>,

	EmergencyPlan: Array<GuardEmergencyPlan>
	IsScanApproval?: number
}

// 功能更新弹窗的请求参数类型和返回值类型
export interface DescribeAdvisorHintParams {
	Action: string,
	CanShow: boolean,
	Filters: Array<{
		Name: string,
		Values: Array<string>
	}>,
	Limit: number
}
export interface DescribeAdvisorHintResultRet {
	Error?: _Error,
	RequestId: string,
	AdvisorHints: Array<DescribeAdvisorHintContent>,
	Total: number
}
export interface DescribeAdvisorHintContent {
	Id: number,
	Title: string,
	TitleTwo: string,
	TitleThree: string,
	Page: string,
	Content: string,
	Language: string,
	StartTime: string,
	EndTime: string,
	CreateTime: string,
	Extra: string,
	Updater: string,
	Status: number
}
