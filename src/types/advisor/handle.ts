export interface DescribeRiskOverviewParams {
	AppId: number,
	Duration?: number,
	OnlyData: boolean,
	ShowError: boolean
}

export interface DescribeRiskOverviewRes {
	RecommendTime: string,
	Total: number,
	TotalSolved: number,
	Progress: number,
	Todo: number,
	TodoRatio: number,
	Doing: number,
	DoingRatio: number,
	History: {
		Done: number,
		Pending: number,
		Reject: number,
		TimeCost: number,
	},
	IsRiskRecommendSupport: boolean
}

export interface FilterItem {
	Name: string,
	Values: Array<string|number>
}

export interface DescribeRiskListsParams {
	AppId: number,
	Type: number,
	Filters: Array<FilterItem>,
	Limit: number,
	Offset: number,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface RiskItem {
	Id: number,
	Name: string,
	State: number,
	Duration: number,
	RiskCount: number,
	Responser: string,
	RecommendId?: string,
	RecommendTime: string
}

export interface DescribeRiskListsRes {
	TotalCount: number,
	RiskLists: Array<RiskItem>
}

export interface DescribeRiskManageStrategyTrendParams {
	StrategyId: number
}

export interface DescribeRiskManageStrategyDetailParams {
	StrategyId: number,
	TaskId: string,
	RecommendId: string,
	ObjectType: string
}

export interface UpdateRiskManageStrategyStateParams {
	StrategyId: number,
	Handler: string,
	State: number,
	TaskId: string,
	RecommendId: string,
	Source?: string,
	Reason?: {
		ReasonId: number,
		ReasonNote: string
	},
	ObjectType: string
}

export interface DescribeEventResourcesParams {
	EventId: number,
	Limit: number,
	Offset: number,
	Filters: Array<{
		Name: string,
		Values: Array<string>
	}>
}

export interface UpdateEventResourcesParams {
	EventId: number,
	ResourceLists: Array<{
		ResourceId: string,
		State: number,
		Comment: string
	}>
}

export interface DescribeTaskSummaryV2Params {
	AppId: number,
	Uin: number,
	Filters: Array<FilterItem>,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface DescribeToRecommendRiskListsParams {
	AppId: number,
	Filters: Array<FilterItem>,
	Limit: number,
	Offset: number,
	ShowError?: boolean,
	OnlyData?: boolean
}

export interface CreateRiskRecommendParams {
	AppId: number,
	StrategyId: number,
	ObjectType?: string,
	RecommendId?: string,
	LastSuccessTaskId: string,
	Type: number,
	Advice?: string,
	Name: string,
	OnlyData?: boolean,
	ShowError?: boolean
}