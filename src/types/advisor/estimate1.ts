export interface ConfigCondition {
	ConditionId: number;
	Level: number;
	Desc: string;
}

export interface ConfigStrategy {
	StrategyId: number;
	Product: string;
	Name: string;
	Desc: string;
	Repair: string;
	Notice: string;
	Ignore: string;
	Conditions: Array<ConfigCondition>;
}

export interface ConfigGroup {
	GroupId: number;
	Name: string;
	Strategies: Array<ConfigStrategy>;
}

export interface Config {
	Groups: Array<ConfigGroup>;
	[propName: string]: any;
}

export interface Instance {
	Id: string;
	Region: string;
	Extra?: string;
	Level?: number;
	IgnoredStatus?: boolean;
}

export interface ResultCondition {
	ConditionId: number;
	UnsafeInstances: Array<Instance>;
}

export interface ResultStrategy {
	Conditions: Array<ResultCondition>;
	AllInstances: Array<Instance>;
	StrategyId: number;
	Code: string;
	Message: string;
}

export interface TaskResult {
	InsertTime: number;
	FinishTime: number;
	Strategies: Array<ResultStrategy>;
	[propName: string]: any;
}

export interface CreateTask {
	TaskId: string;
	[propName: string]: any;
}

export interface LastTask {
	TaskId: string;
	Has: boolean;
	[propName: string]: any;
}

export interface IgnoreInstance {
	Id: string;
	StrategyId?: number;
	Region?: string;
	Level?: number;
}

export interface ModifyInstances {
	AppId: number;
	StrategyId: number;
	Operate: string;
	IgnoreInstances: Array<IgnoreInstance>;
	[propName: string]: any;
}

export interface ModifyInstance {
	AppId: number;
	StrategyId: number;
	Operate: string;
	Instance: IgnoreInstance;
	[propName: string]: any;
}

export interface IgnoreInstances {
	Ignores: Array<IgnoreInstance>;
	[propName: string]: any;
}

export interface RegionCode {
	Region: string;
	Code: number;
}

export interface RegionCodes {
	Codes: Array<RegionCode>;
	[propName: string]: any;
}

export interface DownloadFileType {
	AppId: number;
	Id?: number;
	Type?: string;
	TaskId: string;
	TopicType?: number;
	MapUUId?: string;
	CloudMapID?: string;
	CloudMapUuid?: string;
	Env?: string;
	Products?: any;
	GroupIDs?: any;
	Tags?: any;
	StrategyIDs?: Array<number>;
	Language?: string;
}

export interface DownloadFileContent {
	Name: string;
	Content: string;
	[propName: string]: any;
}

export interface TaskResultSummary {
	CreateTime: string;
	FinishTime: string;
	StrategySummaries: Array<StrategySummary>;
	GroupSummaries: Array<GroupSummary>;
	[propName: string]: any;
}

interface StrategySummary {
	StrategyId: number;
	Code: string;
	GroupId: number;
	HighRiskCount: number;
	MediumRiskCount: number;
	LowRiskCount: number;
	NoRiskCount: number;
	IgnoredInstanceCount: number;
}

interface GroupSummary {
	GroupId: number;
	HighRiskCount: number;
	MediumRiskCount: number;
	LowRiskCount: number;
	NoRiskCount: number;
}

export interface TaskResultDetail {
	AppId: number;
	TaskId: string;
	StrategyId: number;
	Offset: number;
	Limit: number;
	Filter: string;
}

export interface UnsafeDetails {
	TotalCount: number;
	UnsafeDetails: Array<UnsafeDetail>;
	[propName: string]: any;
}

interface UnsafeDetail {
	ConditionId: number;
	IgnoredStatus: boolean;
	Instance: Instance;
}

export interface IgnoredDetails {
	TotalCount: number;
	IgnoredDetails: Array<IgnoredDetail>;
	[propName: string]: any;
}

interface IgnoredDetail {
	IgnoredStatus: boolean;
	Instance: Instance;
}

export interface DownloadFileAsync {
	ResultId: string;
	[propName: string]: any;
}

export interface DownloadFileResult {
	CosUrl: string;
	Code: string;
	[propName: string]: any;
}

export interface TaskProgressSummary {
	Finished: boolean;
	Overview: Progress;
	EstimatedTime: number;
	Details: Array<ProductProgress>;
	[propName: string]: any;
}

interface Progress {
	ScannedCount: number;
	TotalCount: number;
}

interface ProductProgress {
	Name: string;
	Progress: Progress;
}

export interface Tag {
	TotalCount: number;
	Tags: Array<Tags>;
	[propName: string]: any;
}

interface Tags {
	TagKey: string;
	TagValues: string;
}

export interface DownLoadReportTplTag {
	TagKey: string;
	TagValues: Array<string>;
	editingKey?: boolean;
	editingValues?: boolean;
}

export interface DownLoadReportTplTagSon {
	TagKey: string;
	TagValue: string;
}
