export interface DescribeGuardReportDashboardParams {
	AppId: number,
	GuardId: number,
	ProductList: Array<string>,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface CreateGuardReportDashboardParams {
	AppId: number,
	GuardId: number,
	ProductPanelIdMap: any,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DescribeGuardReportSummaryParams {
	AppId: number,
	GuardId: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface CreateGuardReportSummaryParams {
	AppId: number,
	GuardId: number,
	ReportSummaryContentList: Array<string>,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface ReportSummaryItem {
	Id: number,
	Content: string
}

export interface UpdateGuardReportSummaryParams {
	AppId: number,
	GuardId: number,
	ReportSummaryList: Array<ReportSummaryItem>,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DeleteGuardReportSummaryParams {
	AppId: number,
	GuardId: number,
	ReportSummaryIds: Array<string>
}

export interface DescribeGuardScanRiskSummaryParams {
	AppId: number,
	GuardId: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DescribeGuardReportAppidOverViewParams {
	AppId: number,
	GuardId: number,
	RiskTop?: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface CreateGuardReportModelParams {
	AppId: number,
	GuardId: number,
	ModelWorkList: Array<{
		State: number,
		List: Array<number>
	}>,
	ModelSortList?: Array<number>,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DescribeGuardReportModelParams {
	AppId: number,
	GuardId: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DescribeGuardReportSubscriptionParams {
	AppId: number,
	GuardId: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface CreateGuardReportSubscriptionParams {
	AppId: number,
	GuardId: number,
	Subscription: {
		Receiver: string,
		ReceiveTime: string
	},
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DescribeGuardReportTitleParams {
	AppId: number,
	GuardId: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface DescribeGuardReportAppidRsTrendParams {
	AppId: number,
	GuardId: number,
	StartDate: string,
	EndDate: string,
	TaskType: string,
	OnlyData?: boolean,
	ShowError?: boolean,
	Products?: Array<string>
}

export interface DescribeGuardReportRsLoadParams {
	AppId: number,
	GuardId: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface UpdateGuardReportStrategyStateParams {
	AppId: number,
	GuardId: number,
	StrategyId: number,
	State: number,
	OnlyData?: boolean,
	ShowError?: boolean,
	RiskLevel?: number
}

export interface UpdateGuardReportStrategySortParams {
	AppId: number,
	GuardId: number,
	SortStrategyIdS: Array<number>
}

export interface ModifyGuardAlarmStateParams {
	GuardId: number,
	AlarmId: number,
	State: number,
	OnlyData?: boolean,
	ShowError?: boolean,
}

export interface UpdateGuardReportResultParams {
	GuardId: number,
	AppId: number,
	ResultType: string,
	ResultData: string,
	OnlyData?: boolean,
	ShowError?: boolean,
}