export interface UpdateStrategyParams {
	'StrategyId'?: number,
	'StrategyUsage': number,
	'DemandBackground': string,
	'DemandPriority': number,
	'Desc': string,
	'DescEn'?: string,
	'Expert': string,
	'GroupId': number,
	'Ignore': string,
	'IgnoreEn'?: string,
	'Name': string,
	'NameEn'?: string,
	'NeedDev': number,
	'Notice': string,
	'NoticeEn'?: string,
	'Product': string,
	'ProductOwner': string,
	'Repair': string,
	'RepairEn'?: string,
	'ShortDesc': string,
	'ShortDescEn'?: string,
	'ShortName': string,
	'ShortNameEn'?: string,
	'StrategyConditions': Array<{
		'ApiDesc': string,
		'Desc': string,
		'DescEn': string,
		'Level': number,
		'Priority': number,
		'Score': number,
		'UserPolicySupport'?: number,
		'TestAccounts': [
			{
				'Appid': string,
				'Resource': string,
				'Uin': string
			}
		],
	}>
}
