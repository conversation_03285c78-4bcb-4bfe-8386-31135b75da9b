
export interface FaultItem {
	Product: Array<string>
	Id: number,
	Duration: number,
	AffectedNum: number,
	InformedNum: number,
	Title: string,
	StartTime: string,
	EndTime: string,
	Status: string
}

export interface ConfigGroup {
	FaultLists?: Array<FaultItem>;
	IsCreateActive?: boolean,
	TotalCount?: number;
	RequestId?: string;
}

export interface FilterItem {
	Name: string,
	Values: Array<string|number>
}

export interface FilterParams {
	Limit: number,
	Offset: number,
	Filters?: Array<FilterItem>,
	OnlyData?: boolean,
	ShowError?: boolean,
	Name?: string,
	AppId: number
}

export interface DetailFilterParams {
	Name?: string,
	AppId?: number,
	Id: number,
	Limit: number,
	Offset: number,
	Filters?: Array<FilterItem>,
	OnlyData?: boolean,
	ShowError?: boolean
}

export interface AffectedItem {
	AppId: number,
	CustomerName: string,
	Owner: string,
	Resources: Array<string>,
	IsInformed: number,
	InformTime: string,
	IsActive: boolean,
}

export interface FaultDetailInfo {
	Name: string,
	Product: Array<string>,
	StartTime: string,
	EndTime: string,
	Desc: string,
	Reason: string,
	TotalCount: number,
	RequestId: string,
	AffectedLists: Array<AffectedItem>,
	Advice: string,
	Tool: string,
	Status: string
	ContentEn?: {
		AdviceEn: string,
		DescEn: string,
		NameEn: string,
		ReasonEn: string,
		ToolEn: string,
	}
}

export interface ExamineInfoParams {
	Id: number,
	Name: string,
	AppId: number,
	OnlyData?: boolean,
	Language?: string,
	ShowError?: boolean
}

export interface AffectItem {
	AppId: number,
	Customer: string,
	Owner: string,
	Resources: Array<string>
}

export interface CreateNoticeParams {
	Name: string,
	Id: number,
	Title: string,
	Product: Array<string>,
	Reason: string,
	Desc: string,
	StartTime: string,
	EndTime: string,
	Status: number,
	Affected: Array<AffectItem>
}

export interface FaultInformPopWinParams {
	Id: number,
	Name: string,
	AppId: number,
	OnlyData?: boolean,
	lang?: string,
	ShowError?: boolean
	Content?: {
		Advice: string,
		Desc: string,
		Name: string,
		Reason: string,
		Tool: string,
	}
	Language?: string
}

export interface TranslateDetailParams {
	Id: number,
	SourceContent: {
		Advice: string,
		Desc: string,
		Name: string,
		Reason: string,
		Tool: string,
	},
	AppId: number,
	OnlyData?: boolean,
	ShowError?: boolean
}
