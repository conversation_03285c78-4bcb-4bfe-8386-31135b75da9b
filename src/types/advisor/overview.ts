import { _Error } from "../error";

export interface getAdvisorCustomerProps {
	SearchWord: string;
	OrderBy?: string;
	Regions?: Array<string>;
	Limit?: number;
	Offset?: number;
}

export interface listAdvisorAllScanStrategiesProps {
	Limit?: number;
	Offset?: number;
	Env?: string;
}

export interface modifyAdvisorAuthorizedUserStrategyIdsProps {
	AppId: number;
	StrategyIds?: Array<number>;
}

export interface listAdvisorRiskCountProps {
	AppId: number;
	StartDate?: string;
	EndDate?: string;
	IsDefaultStrategy?: boolean;
	RiskLevel?: Array<number>;
}

export interface getAdvisorCustomerRes {
	RequestId: string;
	Error?: _Error;
	Total: number;
	CustomerList: Array<AdvisorCustomer>;
}

export interface listAdvisorAllScanStrategiesRes {
	RequestId: string;
	Error?: _Error;
	Total: number;
	Page: number;
	Strategy: Array<StrategyDetail>;
}

export interface modifyAdvisorAuthorizedUserStrategyIdsRes {
	RequestId: string;
	Error?: _Error;
	Status: string;
}

export interface listAdvisorRiskCountRes {
	RequestId: string;
	Error?: _Error;
	DailyRiskCount: any;
	RiskCount: any;
}

interface AdvisorCustomer {
	Uin: string;
	AppID: string;
	CustomerName: string;
	MarkName: string;
	SalesSupportor: string;
	Responser1: string;
	Responser2: string;
	BusinessManager: string;
	ClientManager: string;
	SalesGrade: string;
	FocusedStrategyId: Array<number>;
	Region: string;
	TotalScore: number;
	HighRiskCount: number;
	MediumRiskCount: number;
	LowRiskCount: number;
	OpeningTime: string;
	LastEvaluationTime: string;
}

interface StrategyDetail {
	ID: number;
	GroupID: number;
	Product: string;
	Name: string;
	Desc: string;
	Online: number;
	Repair: string;
	Notice: string;
	Ignore: string;
	Env: string;
}
