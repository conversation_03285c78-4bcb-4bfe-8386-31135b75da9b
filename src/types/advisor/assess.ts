import { _Error } from "../error";
export interface getAssessResultProps {
	AppID: string;
	UIN?: string;
	TaskID?: string;
}

// 获取评估结果总览
export interface getAssessResultRes {
	RequestId: string;
	Error?: _Error;
	TaskID: string;
	LastScanTime: string;
	LevelInfoList: Array<LevelInfo>;
	StrategyDataList: Array<StrategyData>;
}
interface LevelInfo {
	GroupID: number;
	Name: string;
	Info: GroupInfo;
}
export interface GroupInfo {
	High: number;
	Middle: number;
	Low: number;
}
interface StrategyData {
	Name: string;
	Desc: string;
	Notice: string;
	Ignore: string;
	NoticeResult: number;
	IgnoreResult: number;
	Level: string;
	GroupID: string;
	GroupName: string;
	StrategyID: string;
	Product: string;
	LevelCount: Array<number>;
	Repair: string;
	Conditions: Array<Condition>;
}
interface Condition {
	ConditionId: number;
	Level: number;
	Desc: string;
	Operation: string;
}

// 获取不安全的资源详情
export interface getUnsafeResourcesProps {
	TaskID: string;
	StrategyID: number;
	Filter: string;
	Offset: number;
	Limit: number;
	AppID: string;
}

export interface getUnsafeResourcesRes {
	RequestId: string;
	Error?: _Error;
	TotalCount: number;
	UnsafeDetails: Array<UnsafeDetail>;
}
interface UnsafeDetail {
	ConditionId: number;
	IgnoredStatus: boolean;
	Instance: InstanceData;
}
interface InstanceData {
	ID: string;
	Region: string;
	Extra: string;
}

// 获取忽略的资源详情
export interface getIgnoreResourcesProps {
	TaskID: string;
	StrategyID: number;
	Filter: string;
	Offset: number;
	Limit: number;
	AppID: string;
}
export interface getIgnoreResourcesRes {
	RequestId: string;
	Error?: _Error;
	TotalCount: number;
	IgnoredDetails: Array<IgnoredDetail>;
}
interface IgnoredDetail {
	IgnoredStatus: boolean;
	Instance: InstanceData;
}

// 创建下载任务
export interface getReportFileAsyncProps {
	/**
	 * 评估结果对应产品的Id，与Type字段配合使用。
	 * 当Type为Group时，表示类别Id（-1：总览，1：安全，2：可靠，3：服务限制），
	 * 当Type为Strategy时，表示策略Id。
	 * */
	ID: number;
	Type: "Group" | "Strategy";
	TaskID: string;
	Language?: "zh-CN" | "en-US";
	AppID: string;
}
export interface getReportFileAsyncRes {
	RequestId: string;
	Error?: _Error;
	/** 根据请求参数，返回的唯一id，用于后续excel下载。 */
	ResultID: string;
}

// 获取下载结果
export interface getReportResultAsyncProps {
	ResultID: string;
}
export interface getReportResultAsyncRes {
	RequestId: string;
	Error?: _Error;
	TaskStatus: "init" | "running" | "success" | "fail";
	CosUrl: string;
}
