import { _Error } from '../error';
export interface DescribeOverview {
	CreateTime: string;
	FinshTime: string;
	Has: boolean;
	requestId: string;
	TaskId: string;
	GroupSummaries: Array<GroupSummaries>;
	ProductSummaries: any;
	RiskDays: any;
	StrategyTopSummaries: any;
	Error?: _Error;
	[propName: string]: any;
}

interface GroupSummaries {
	GroupId: number;
	HighRiskCount: number;
	MediumRiskCount: number;
	LowRiskCount: number;
	NoRiskCount: number;
}
