import { _Error } from "../error";
// 获取私有模板列表
export interface GetPrivateTemplateListProps {
	/** Template Name或Id或用户Appid  */
	SearchWord: string;
	OrderBy?: string;
	Offset: string;
	Limit: string;
}
export interface GetPrivateTemplateListRes {
	RequestId: string;
	Error?: _Error;
	Count: string;
	TemplateList: Array<PrivateTemplateInfo>;
}
interface PrivateTemplateInfo {
	Id: string;
	AppId: string;
	Status: string;
	Name: string;
	Desc: string;
	VersionId: string;
	StackId: string;
	TfUrl: string;
	CreateTime: string;
	UpdateTime: string;
}
// 获取私有模板详情
export interface GetPrivateTemplateDetailProps {
	TemplateId: string;
}
export interface GetPrivateTemplateDetailRes {
	RequestId: string;
	Error?: _Error;
	TemplateDetail: PrivateTemplateDetail;
}
interface PrivateTemplateDetail {
	TemplateInfo: PrivateTemplateInfo;
	TfTree: string;
}
// 获取公有模板列表
export interface GetPublicTemplateListProps {
	/** Template Name或Id */
	SearchWord: string;
	Tags?: Array<string>;
	Categories?: Array<string>;
	Status?: Array<number>;
	OrderBy?: string;
	Offset: string;
	Limit: string;
}
export interface GetPublicTemplateListRes {
	RequestId: string;
	Error?: _Error;
	Count: string;
	TemplateList: Array<PublicTemplateInfo>;
}
interface PublicTemplateInfo {
	Id: string;
	Status: string;
	Name: string;
	Desc: string;
	Diagram: string;
	VersionId: string;
	StackId: string;
	TfUrl: string;
	CreateTime: string;
	UpdateTime: string;
	Category: string;
	Tag: string;
	Icon: string;
	DefaultStackName: string;
	DefaultRegion: string;
	ChineseName: string;
	ChineseDesc: string;
	ChineseDiagram: string;
}
// 获取共有模板详情
export interface GetPublicTemplateDetailProps {
	TemplateId: string;
}
export interface GetPublicTemplateDetailRes {
	RequestId: string;
	Error?: _Error;
	TemplateDetail: PublicTemplateDetail;
}
interface PublicTemplateDetail {
	TemplateInfo: PublicTemplateInfo;
	TfTree: string;
}
// 创建公有模板
export interface CreatePublicTemplateProps {
	TfTree: string;
	Name: string;
	Desc: string;
	Category: string;
	Tag: string;
	Icon: string;
	DefaultStackName: string;
	ChineseName: string;
	ChineseDesc: string;
}
export interface CreatePublicTemplateRes {
	RequestId: string;
	Error?: _Error;
	Status: string;
	TemplateId: string;
}
// 删除公有模板
export interface DeletePublicTemplateProps {
	TemplateId: string;
}
export interface DeletePublicTemplateRes {
	RequestId: string;
	Error?: _Error;
	Status: string;
}
// 修改公有模板
export interface ModifyPublicTemplateProps {
	TemplateId: string;
	TfTree: string;
	Name: string;
	Desc: string;
	Category: string;
	Tag: string;
	Icon: string;
	DefaultStackName: string;
	ChineseName: string;
	ChineseDesc: string;
}
export interface ModifyPublicTemplateRes {
	RequestId: string;
	Error?: _Error;
	Status: string;
}

export interface GetTemplatesProps {
	TemplateType: 0 | 1;
	Filter?: string;
	OrderBy?: string;
	Offset?: number;
	Limit?: number;
	Category?: string;
	Tag?: string;
}
export interface GetTemplatesRes {
	Limit: number;
	Offset: number;
	Total: number;
	Templates: Array<TemplateInfo>;
	RequestId: string;
	Error?: _Error;
}
interface TemplateInfo {
	TemplateId: number;
	Uin: number | null;
	TemplateType: 0 | 1;
	AppId: number | null;
	ZhName?: string;
	ZhDesc?: string | null;
	Name: string;
	Desc: string | null;
	InsertTime: number;
	UpdateTime: number;
	TfTree: string | null;
	Category: string | null;
	Tag: string | null;
	Icon: string | null;
	DefaultStackName: string | null;
}

export interface GetTemplateDetailRes {
	RequestId: string;
	Error?: _Error;
	Template: TemplateInfo;
}
