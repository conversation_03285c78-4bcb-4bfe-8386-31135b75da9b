import { _Error } from "../error";

export interface GetResourceListStackProps {
	OrderBy?: string;
	Regions?: Array<string>;
	Status?: Array<string>;
	Offset?: number;
	Limit?: number;
	UIN?: string;
	StackID?: string;
	VersionID?: string;
	EventID?: string;
}
export interface GetResourceListStackRes {
	RequestId: string;
	Error?: _Error;
	Stacks: Array<ResourceStack>;
	TotalSize: number;
}
interface ResourceStack {
	ID: string;
	StackID: string;
	APP_ID: string;
	/** 所属账号UIN */
	UIN: string;
	/** 子账号UIN */
	SubAccountUin: string;
	Name: string;
	Description: string;
	Region: string;
	/** tf模版来源，url，私有，公共，直写等  */
	Source: string;
	/** TODO */
	TokenFlag: boolean;
	Status: string;
	CreateTime: string;
	UpdateTime: string;
	Deleted: boolean;
}

// 获取版本列表请求
export interface GetVersionListProps {
	OrderBy: string;
	SearchWord: string;
	Status: Array<string>;
	Offset: number;
	Limit: number;
	StackID: string;
	VersionID?: string | number;
}
// 获取版本列表响应
export interface GetVersionListRes {
	RequestId: string;
	Error?: _Error;
	Versions: Array<Version>;
	TotalSize: string;
}
interface Version {
	ID: string;
	StackID: string;
	Name: string;
	Status: string;
	Description: string;
	/** 执行后的消息 */
	Message: string;
	/** tf内容的URL  */
	TfUrl: string;
	CreateTime: string;
	UpdateTime: string;
	Deleted: string;
	APP_ID: string;
	Uin: string;
	SubAccountUin: string;
	/** tf内容文本 */
	TfContent: string;
}

// 带TfTree的版本详情
export interface GetVersionDetailRes {
	RequestId: string;
	Error?: _Error;
	ID: string;
	StackID: string;
	Name: string;
	Status: string;
	Description: string;
	/** 执行后的消息 */
	Message: string;
	/** tf内容的URL  */
	TfUrl: string;
	CreateTime: string;
	UpdateTime: string;
	Deleted: string;
	APP_ID: string;
	Uin: string;
	SubAccountUin: string;
	/** tf内容文本 */
	TfContent: string;
	ConsoleContent: string;
}

export interface GetVersionDetailProps {
	VersionID: string;
}

export interface GetResourceListProps {
	Offset: number;
	Limit: number;
	SearchWord?: string;
	/** sid */
	StackID: string;
	AppID: string;
}

export interface GetResourceListRes {
	RequestId: string;
	Error?: _Error;
	Resources: Array<Resource>;
	TotalSize: number;
}
interface Resource {
	ID: string;
	APPID: string;
	Provider: string;
	Product: string;
	UniqueKey: string;
	/** sid */
	StackID: string;
	FindVersionId: string;
	FindEventId: string;
	DisappearVersionId: string;
	DisappearEventId: string;
	FindTime: string;
	DisappearTime: string;
}

interface ProviderResourceInfo {
	ProviderName: string;
	ResourceList: Array<ResourceInfo>;
	DataList: Array<ResourceInfo>;
}
interface ResourceInfo {
	Id: string;
	AttrName: string | null;
	Type: string;
	Name: string;
	Index: number;
}

export interface GetEventListProps {
	SearchWord?: string;
	OrderBy: string;
	Status: Array<string>;
	Offset: number;
	Limit: number;
	StackID: string;
	EventID?: string | number;
}

export interface GetEventListRes {
	TotalSize: number;
	RequestId: string;
	Error?: _Error;
	Events: Array<Event>;
}
interface Event {
	ID: string;
	APP_ID: string;
	Uin: string;
	SubAccountUin: string;
	VersionID: string;
	StackID: string;
	Type: string;
	ConsoleURL: string;
	LogURL: string;
	StateURL: string;
	ExtraURL: string;
	Description: string;
	Status: string;
	Message: string;
	CreateTime: string;
	UpdateTime: string;
	Deleted: boolean;
	ConsoleContent: string;
}

export interface GetEventDetailProps {
	EventID: string;
}

export interface GetEventDetailRes {
	RequestId: string;
	Error?: _Error;
	ID: string;
	APP_ID: string;
	Uin: string;
	SubAccountUin: string;
	VersionID: string;
	StackID: string;
	Type: string;
	ConsoleURL: string;
	LogURL: string;
	StateURL: string;
	ExtraURL: string;
	Description: string;
	Status: string;
	Message: string;
	CreateTime: string;
	UpdateTime: string;
	Deleted: boolean;
	ConsoleContent: string;
	TfContent: string;
}
