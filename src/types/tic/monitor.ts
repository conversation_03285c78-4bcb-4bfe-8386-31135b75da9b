import { _Error } from "../error";

export interface getTicMonitorRes {
	RequestId: string;
	Error?: _Error;
	/**  授权用户总数列表 */
	ListUsersCount: Array<UsersCount>;
	/**  资源栈总数列表 */
	ListResourceStackCount: Array<ResourceStackCount>;
	/** 资源数总览列表 */
	ListResourceCount: Array<ResourceCount>;
}
interface UsersCount {
	/** 天，日期格式为："YYY-MM-DD"  */
	Date: string;
	/** 授权用户总数 */
	Count: number;
}
interface ResourceStackCount {
	/** 天，日期格式为："YYY-MM-DD"  */
	Date: string;
	/** 资源栈总数 */
	ResourceStackCount: number;
	/** 草稿状态总数 */
	DraftCount: number;
	/** Plan成功总数  */
	PlanSuccessCount: number;
	/** Plan失败总数 */
	PlanFailureCount: number;
	/** Apply成功总数 */
	ApplySuccessCount: number;
	/** Apply失败总数 */
	ApplyFailureCount: number;
}
interface ResourceCount {
	/** 天，日期格式为："YYY-MM-DD"  */
	Date: string;
	/** 托管的资源数 */
	ResourceCount: number;
}
