import { _Error } from "../error";
export interface getTicCustomerProps {
	SearchWord: string;
	OrderBy?: string;
	Regions?: Array<string>;
	Limit?: number;
	Offset?: number;
}

export interface getTicCustomerRes {
	RequestId: string;
	Error?: _Error;
	TotalCount: number;
	ListCustomer: Array<TicCustomer>;
}

interface TicCustomer {
	Uin: string;
	AppID: string;
	CustomerName: string;
	Region: string;
	StackCount: number;
	ResourceCount: number;
	CreateTime: string;
	UpdateTime: string;
}
