import React, { useEffect, useMemo, useState } from 'react';
import { Button, Row, Col, Select, SelectMultiple, Text, Input, DatePicker } from '@tencent/tea-component';
import { cloneDeep } from "lodash";
import './index.less';
import { TagSelect } from "@tea/component";

export interface params{
	originFilterData: Array<{
		label: string, // 搜索条件名称
		name: string, // 字段名
		type: 'input' | 'select' | 'tagSelect' | 'mutipleTime' | 'mutipleSelect' | 'textarea' // 组件类型
		placeholder?: string,
		options?: Array<{
			text: string,
			value: string
		}>
		value: string // 控件默认初始值
		showTime?: boolean,
		clearable?: boolean
	}>
	onSearch: Function, // 点击搜索时获取Filters
	onReset: Function, // 点击重置时调用
	onCallBack?: Function, // 需要实时缓存筛选条件时调用
	suffix?: any
}
let initLoad = true;
export function ComprehensiveSearch({originFilterData, onSearch, onReset, suffix, onCallBack}: params) {
	const [filterData, setFilterData] = useState<any>([...cloneDeep(originFilterData)]);
	const { RangePicker } = DatePicker;
	const [filters, setFilters] = useState([]);
	useMemo(()=>{
		let isFinsh = true;
		originFilterData.forEach((item) => {
			if (item?.options?.length === 0) {
				isFinsh = false;
			}
		});
		if (isFinsh && initLoad) {
			setFilterData([...cloneDeep(originFilterData)]);
			initLoad = false;
		}
	}, [originFilterData]);
	useEffect(()=>{
		filterData.forEach(item => {
			refreshAllParams(item.name, item.value)
		});
		return ()=>{
			initLoad = true;
		};
	}, []);
	const refreshAllParams = (name, val) => {
		let notHasName = true;
		filters.forEach((item) => {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				if (item.Name == nameList[0]) {
					item.Values = [val[0]];
					notHasName = false;
				}
				if (item.Name == nameList[1]) {
					item.Values = [val[1]];
					notHasName = false;
				}
			} else {
				if (item.Name == name) {
					item.Values = Array.isArray(val) ? [...val] : [val];
					notHasName = false;
				}
			}
		});
		if (notHasName) {
			if (name.indexOf(',') != -1) {
				const nameList = name.split(',');
				filters.push({
					Name: nameList[0],
					Values: [val[0]]
				});
				filters.push({
					Name: nameList[1],
					Values: [val[1]]
				});
			} else {
				filters.push({
					Name: name,
					Values: Array.isArray(val) ? [...val] : [val]
				});
			}
		}
		setFilters(cloneDeep(filters));
		if (onCallBack) {
			onCallBack(filters.filter(item => !item.Values.every(value => value === '')));
		}
	};
	const len = filterData.length;
	return <div className={`filter-wrap filter-wrap-${len}`}>
		<Row>
			{
				filterData.map((item, i) => {
					return item.type != 'tagSelect' && (<Col span={6} key={i}>
						<Row verticalAlign={"middle"}>
							<Col span={5}>
								<Text theme="label" verticalAlign="middle">{item.label}</Text>
							</Col>
							<Col span={19}>
								{
									item.type == 'input' ?
										<Input onChange={(val) => {
											item.value = val;
											setFilterData([
												...filterData
											]);
											refreshAllParams(item.name, val);
										}
										}
											   value={item.value}
										/>
										:
										item.type == 'textarea' ?
											<Input.TextArea
												style={
													{
														height: '30px'
													}
												}
												onChange={(val) => {
												item.value = val;
												setFilterData([
													...filterData
												]);
												refreshAllParams(item.name, val);
											}
											}
												   value={item.value}
											/>
											:
										item.type == 'mutipleTime' ?
											<RangePicker
												showTime={item.showTime || false}
												value={item.value}
												range={item.range}
												onChange={(val) => {
													item.value = [...val];
													setFilterData([
														...filterData
													]);
													if (item.showTime) {
														refreshAllParams(item.name, [
															val[0].format("YYYY-MM-DD HH:mm:ss"),
															val[1].format("YYYY-MM-DD HH:mm:ss")
														]);
													} else {
														refreshAllParams(item.name, [
															val[0].format("YYYY-MM-DD"),
															val[1].format("YYYY-MM-DD")
														]);
													}
												}
												}
											/>
											:
											item.type == 'select' ?
												<Select
													clearable={item.clearable}
													searchable
													options={item.options}
													appearance="button"
													size='m'
													value={item.value}
													onChange={(val) => {
														item.value = val;
														setFilterData([
															...filterData
														]);
														refreshAllParams(item.name, val);
													}
													}
												></Select>
												:
												item.type == 'mutipleSelect'
													?
													<SelectMultiple
														clearable={item.clearable}
														searchable
														listWidth={200}
														options={item.options}
														appearance="button"
														size='m'
														value={item.value}
														onChange={(val) => {
															item.value = val;
															setFilterData([
																...filterData
															]);
															refreshAllParams(item.name, val);
														}
														}
                            allOption={item.allOption || null}
													></SelectMultiple>
													:
												<></>
								}
							</Col>
						</Row>
					</Col>);
				})
			}
		</Row>
		{
			filterData[filterData.length - 1]?.type == 'tagSelect'
			&&
			<Row verticalAlign={"middle"} style={{
				marginTop: '10px'
			}}>
				<Col span={3}>
					<Text theme="label" verticalAlign="middle">{filterData[filterData.length - 1].label}</Text>
				</Col>
				<Col>
					<TagSelect
						options={filterData[filterData.length - 1].options}
						value={filterData[filterData.length - 1].value}
						onChange={(val) => {
							filterData[filterData.length - 1].value = [...val];
							setFilterData([
								...filterData
							]);
							refreshAllParams(filterData[filterData.length - 1].name, val);
						}
						}
						style={{
							maxWidth: '1000px'
						}}
					></TagSelect>
				</Col>
			</Row>
		}
		<Row style={{
			justifyContent: 'center',
			margin: '20px 10px 10px 10px'
		}}>
			<Button type='primary' onClick={() => {
				onSearch(filters.filter(item => !item.Values.every(value => value === '')));
			}}>查询</Button>
			<Button style={{
				marginLeft: '10px'
			}} onClick={()=>{
				setFilterData([...cloneDeep(originFilterData.map(item => {
					return {...item, value: ''};
				}))]);
				setFilters([]);
				onReset([]);
			}}>重置</Button>
			{
				suffix
			}
		</Row>
	</div>;
}