.filter-wrap {
	.tea-grid {
		margin-left: 0;
		margin-right: 0;
	}
	.tea-grid__item-5 {
		width: 20%;
	}
	.tea-grid [class*=tea-grid__item] {
		padding-left: 0;
		padding-right: 0;
	}
	.tea-datetimepicker__input.tea-datepicker__input--range {
		width: 290px;
	}
	.tea-tag-input {
		width: fit-content;
		min-width: 200px;
	}
}
@media screen and (max-width: 900px){
	.filter-wrap{
		.tea-grid__item-6 {
			width: 100%;
		}
	}
}
@media screen and (min-width: 900px){
	.filter-wrap{
		.tea-grid__item-6 {
			width: 50%;
		}
		.tea-grid__item-3{
			width: 10%;
		}
	}
}
@media screen and (min-width: 1468px){
	.filter-wrap{
		.tea-grid__item-6 {
			width: 33.33333%;
		}
		.tea-grid__item-3{
			width: 6.7%;
		}
	}
}
@media screen and (min-width: 1600px){
	.filter-wrap{
		.tea-grid__item-6{
			width: 25%;
		}
		.tea-grid__item-3{
			width: 5%;
		}
	}
	.filter-wrap-3 {
		.tea-grid__item-6{
			width: 33.33333%;
		}
	}
	.filter-wrap-2 {
		.tea-grid__item-6{
			width: 50%;
		}
	}
}