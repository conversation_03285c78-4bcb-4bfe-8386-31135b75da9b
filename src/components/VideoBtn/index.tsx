import React, { useState, useEffect } from 'react';
import { Video, message, Button } from '@tencent/tea-component';
import { DescribeGuardFile } from '@src/api/advisor/guard';

// 多个appid文本输入框 （含分割、去重、校验）
function VideoBtn() {
	// 视频地址
	const [videoUrl, setVideoUrl] = useState('');

	// 获取绘制视频
	function getVideo() {
		DescribeGuardFile({ FileName: 'ArchGuideFile' })
			.then((res: any) => {
				setVideoUrl(res?.GuardFileUrl || '');
			})
			.catch((err) => {
				const msg = err.msg || err.toString() || '未知错误';
				message.error({ content: msg });
			});
	}

	// 页面初始化
	useEffect(() => {
		getVideo();
	}, []);

	return (
		<>
			<Video
				style={{ display: 'inline-block' }}
				src={videoUrl}
				title="架构图绘制"
			>
				<Button type="link"
					style={{
						verticalAlign: 'baseline',
						textDecoration: 'underline',
					}}>如何绘制？</Button>
			</Video>
		</>
	);
}

export default VideoBtn;
