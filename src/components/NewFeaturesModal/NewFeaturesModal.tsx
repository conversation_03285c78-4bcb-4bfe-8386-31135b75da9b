import React, { useState, useEffect, FC } from 'react';
import './style.less';
import request from '@src/api/request';
import { List, ExternalLink, Modal, H3, Checkbox, Button, message as tips} from '@tencent/tea-component';

interface ModalProps {
	path?: string;
}
//功能更新弹窗的请求参数类型和返回值类型
interface DescribeAdvisorHintParams {
	Action: string;
	CanShow: boolean;
	Filters: Array<{
		Name: string;
		Values: Array<string>;
	}>;
	Limit: number;
}
interface DescribeAdvisorHintResultRet {
	RequestId: string;
	AdvisorHints: Array<DescribeAdvisorHintContent>;
	Total: number;
}
interface DescribeAdvisorHintContent {
	Id: number;
	Title: string;
	TitleTwo: string;
	TitleThree: string;
	Page: string;
	Content: string;
	Language: string;
	StartTime: string;
	EndTime: string;
	CreateTime: string;
	Extra: string;
	Updater: string;
	Status: number;
}
// 获取功能更新弹窗的内容信息
async function getDescribeAdvisorHint(data: DescribeAdvisorHintParams): Promise<DescribeAdvisorHintResultRet> {
	try {
		const res = await request({
			method: 'post',
			url: '/interface',
			data: { ...data },
		});
		return res.data.Response;
	} catch (e) {
		return e;
	}
}
const NewFeaturesModal: FC<ModalProps> = (props: ModalProps) => {
	const { path } = props;

	//页面加载开始，获取更新弹窗对话框的内容信息
	const getModalContent = async () => {
		try {
			const res = await getDescribeAdvisorHint({
				Action: 'DescribeAdvisorHint',
				CanShow: true,
				Filters: [
					{
						Name: 'page',
						Values: [path],
					},
				],
				Limit: -1,
			});
			if (!res.AdvisorHints.length) return;
			else {
				const newIdString = res.AdvisorHints.map((item) => item.Id).join('');
				const getCookie = (cname: string) => {
					let name = cname + '=';
					let ca = document.cookie.split(';');
					for (let i = 0; i < ca.length; i++) {
						let c = ca[i].trim();
						if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
					}
					return '';
				};
				const showMoalCookie: string = getCookie(`modalCanShow${path}`);
				if (!showMoalCookie) {
					setModalContent(res.AdvisorHints);
					setVisible(true);
				} else {
					if (showMoalCookie !== newIdString) {
						document.cookie = `modalCanShow${path}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`;
						setModalContent(res.AdvisorHints);
						setVisible(true);
					}
				}
			}
		} catch (err) {
			const msg = err.msg || err.toString() || '未知错误';
			tips.error({ content: msg });
		}
	};
	//只要 path 发生了变化，那么就会重新获取后端数据再次渲染弹窗组件
	useEffect(() => {
		getModalContent();
	}, [path]);

	//更新提示弹窗
	const [visible, setVisible] = useState<boolean>(false)
	const [modalContent, setModalContent] = useState<Array<DescribeAdvisorHintContent>>([])
	const renderModalContents = (items: Array<DescribeAdvisorHintContent>) => {
		const parseContent = (item: DescribeAdvisorHintContent) => {
			let result = [], res = '';
			for (let i = 0; i < item.Content.length; i++) {
				const char = item.Content[i];
				if ((/[\r\n\t\f\v]/.test(char))) res;//去除无意义的空字符
				else if (char === '%') {
					result.push(res);
					res = '';
				} else res += char;
				if (i === item.Content.length - 1) result.push(res);
			}
			const urlArr = item.Extra.split(',')
			return result.map((str, i) => {
				if ((i & 1) === 0) {
					return str
				} else {
					return (
						<ExternalLink href={urlArr[(i >> 1)]} style={{ marginRight: 5, marginLeft: 5 }}>
							{str}
						</ExternalLink>
					)
				}
			})
		}
		const listContents = (items: Array<DescribeAdvisorHintContent>) => {
			return items.map((item: DescribeAdvisorHintContent) => {
				return (
					<List.Item key={item.Id}>
						{!!item.Extra ? parseContent(item) : item.Content}
					</List.Item>
				);
			});
		};

		const myMap = new Map();
		const newContents = [];
		//将后端数据转化成树形数组 newContents
		for (const item of items) {
			if (!myMap.has(item.TitleTwo)) {
				const obj = {};
				obj['title'] = item.TitleTwo;
				obj['contents'] = [item];
				myMap.set(item.TitleTwo, obj);
				newContents.push(obj);
			} else {
				myMap.get(item.TitleTwo)['contents'].push(item);
			}
		}
		return newContents.map((newitem) => {
			return (
				<>
					<H3 style={{ margin: '20px 0px' }}>{newitem.title + '：'}</H3>
					<List type="number" key={newitem.title}>
						{listContents(newitem.contents)}
					</List>
				</>
			);
		});
	};
	return (
		/* 新功能更新弹窗*/
		<Modal
			size="auto"
			visible={visible}
			onClose={() => setVisible(false)}
			caption={modalContent[0]?.Title}
			className='new-features-modal'
		>
			<Modal.Body
				className='new-features-modal-body'
			>
				{renderModalContents(modalContent)}
				<Checkbox
					className='new-features-modal-checkbox'
					onChange={(val) => {
						const setCookie = (name: string, value: string, expires: Date) => {
							document.cookie = `${name}=${value}; expires=${expires.toUTCString()}; path=/`;
						};
						if (val == false) {
							//取消选择则删除 cookie 
							setCookie(`modalCanShow${path}`, '', new Date('Thu, 01 Jan 1970 00:00:00 GMT'));
						}
						else {
							const cookieValue = modalContent.map((item) => item.Id).join('');
							const expirationDate = new Date(modalContent[0]?.EndTime);
							setCookie(`modalCanShow${path}`, cookieValue, expirationDate);
						}
					}}
				>
					已了解，不再显示
				</Checkbox>
			</Modal.Body>
			<Modal.Footer>
				<Button
					type="primary"
					onClick={() => {
						setVisible(false);
					}}
				>
					<span>关闭</span>
				</Button>
			</Modal.Footer>
		</Modal>

	);
};

export default NewFeaturesModal;
