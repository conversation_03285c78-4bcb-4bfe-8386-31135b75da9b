import React, { useRef, useEffect } from 'react';
import { Loading } from 'tdesign-react';
import { Layout } from '@tencent/tea-component';
import { useHistory } from '@tea/app';
import { MICRO_APP_CONTAINER_ID } from '@src/configs/micro-app-config';
import { reportVisitPage } from '@src/utils/report';
import './index.less';

const { Body } = Layout;

export const MICRO_FRONTEND_ROOT_READY = 'micro-frontend-root-ready';

const MICRO_APP_ROUTE = ['/advisor/new-architecture', '/advisor/operation-manage', '/advisor/email-center', '/advisor/cloud-escort'];

const isMicroAppRoute = (pathname: string) => {
	const index = MICRO_APP_ROUTE.findIndex(e => pathname.includes(e));
	return index > -1;
};

/**
 * 子应用容器
 * @returns
 */
export default function MicroAppContainer(): React.ReactElement {
	const rootRef = useRef<HTMLDivElement>(null);
	const history = useHistory();

	useEffect(() => {
		const event = new Event(MICRO_FRONTEND_ROOT_READY);
		document.dispatchEvent(event);
		reportVisitPage({
			isaReportAction: 'visit',
			isaReportPath: location.pathname,
			isaReportMeunName: '云架构',
		});
		return () => {
			// FIXME 修复从微应用切换到主应用路由路由组件不渲染问题
			const { pathname } = history.location;
			if (!isMicroAppRoute(pathname)) {
				history.push({
					pathname: '/advisor/handle',
				});
				// 下一个事件循环回到目标路由，强行制造一次路由切换
				setTimeout(() => {
					history.push({
						pathname,
					});
				}, 0);
			}
		};
	}, []);

	return (
		<Body>
			<div id={MICRO_APP_CONTAINER_ID} className="micro-frontend-container" ref={rootRef}>
				<div className="loading">
					<Loading text="拼命加载中..." />
				</div>
			</div>
		</Body>
	);
}
