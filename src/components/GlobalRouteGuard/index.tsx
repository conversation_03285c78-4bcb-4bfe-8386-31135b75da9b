import React, { useEffect } from 'react';
import { useHistory } from '@tea/app';
import { shouldUseMigratedRoute } from '@src/utils/common';
import MicroAppContainer from '@src/components/MicroAppContainer';

// 需要进行路由守卫的路径配置
const GUARDED_ROUTES = [
	{
		pattern: /^\/advisor\/approval(\/.*)?$/,
		originalBase: '/advisor/approval',
		migratedBase: '/advisor/cloud-escort/approval',
	},
	{
		pattern: /^\/advisor\/config-management(\/.*)?$/,
		originalBase: '/advisor/config-management',
		migratedBase: '/advisor/cloud-escort/config-management',
	},
	{
		pattern: /^\/advisor\/broad-strategy-editor(\/.*)?$/,
		originalBase: '/advisor/broad-strategy-editor',
		migratedBase: '/advisor/cloud-escort/config-management/broad-strategy-editor',
	},
	{
		pattern: /^\/advisor\/broadcast-config-editor(\/.*)?$/,
		originalBase: '/advisor/broadcast-config-editor',
		migratedBase: '/advisor/cloud-escort/config-management/broadcast-config-editor',
	},
];

interface GlobalRouteGuardProps {
	children: React.ReactNode;
}

/**
 * 全局路由守卫组件
 * 在应用级别统一处理路由重定向和灰度控制
 */
export const GlobalRouteGuard: React.FC<GlobalRouteGuardProps> = ({ children }) => {
	const history = useHistory();
	const { location } = history;
	const currentPath = location.pathname;

	// 检查当前路径是否需要路由守卫
	const guardedRoute = GUARDED_ROUTES.find(route => route.pattern.test(currentPath));
	const needsMigration = guardedRoute && shouldUseMigratedRoute();

	useEffect(() => {
		if (needsMigration) {
			// 需要迁移到新路由
			const newPath = currentPath.replace(guardedRoute.originalBase, guardedRoute.migratedBase);
			const fullNewPath = `${newPath}${location.search}${location.hash}`;

			// 只有当路径真的需要改变时才进行替换
			if (currentPath !== newPath) {
				history.replace(fullNewPath);
				return;
			}
		}
	}, [currentPath, guardedRoute, location.search, location.hash, needsMigration]);

	// 如果当前路径需要守卫且应该使用新路由，渲染子应用容器
	if (needsMigration) {
		return <MicroAppContainer />;
	}

	// 否则渲染原始内容
	return <>{children}</>;
};

export default GlobalRouteGuard;
