import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Bubble } from "@tencent/tea-component";
import { Input } from "@tencent/tea-component/lib/input";
const { TextArea } = Input;

interface Props {
    CallBack: Function,
    Disabled?: boolean,
    Need?: boolean,
    Value?: string,
    AppId?: string
}

//多个appid文本输入框 （含分割、去重、校验）
function AppIdsInput({ CallBack, Disabled = false, Need = false, Value = '', AppId }: Props, ref) {
    //输入文本
    const [str, setstr] = useState<string>(Value)
    //首次加载
    const [firstLoad, setFirstLoad] = useState<boolean>(true)
    //不合法提示文本
    const [visibleContent, setVisibleContent] = useState<string>('')
    //提取后有效appid列表
    const [appids, setAppIds] = useState<Array<string>>([])

    //校验
    const Check = () => {
        let flag = true
        //分割
        let l = str.trim().split(/\n|\r|,|;|:|\||\s/).filter(j => { if (j != "") { return j } });
        let l1 = []
        l.map(item => {
            let t = /^\d{10}$/.exec(item)
            if (t && !l1.includes(t[0])) {
                l1.push(t[0])
            }
        })
        //如果没有过滤到有效appid，则提示
        if (Need) {
            if (str.trim() === '') {
                setVisibleContent('不能为空')
                flag = false
            } else {
                if (l1.length === 0) {
                    setVisibleContent('没有过滤到有效appid')
                    flag = false
                } else {
                    setVisibleContent('')
                }
            }
        } else {
            if (str.trim() === '') {
                setVisibleContent('')
            } else {
                if (l1.length === 0) {
                    setVisibleContent('没有过滤到有效appid')
                    flag = false
                } else if (AppId?.trim() !== '' && l1.includes(AppId)) {
                    setVisibleContent('关联APPID与客户APPID存在重复')
                } else {
                    setVisibleContent('')
                }
            }
        }
        setAppIds(l1)
        return flag
    }

    //刷新输入框的值
    const Flush = (x) => {
        setstr(x)
    }

    //刷新输入框的值
    useEffect(() => {
        Flush(Value)
    }, [Value])

    //输入变化，刷新变化
    useEffect(() => {
        Check();
    }, [str])

    //回调接口，返回appid
    useEffect(() => {
        CallBack(appids, visibleContent);
    }, [appids, visibleContent])

    //暴露回调函数给父组件
    useImperativeHandle(ref, () => ({
        setInputValue: (value) => {
            setstr(value)
        },
    }));
    
    return (
        <>
            <Bubble content={visibleContent} visible={visibleContent != ""} error>
                <Input size="full" value={str} disabled={Disabled} onChange={(value) => { setFirstLoad(false); setstr(value) }} placeholder="也可输入多个appid，用换行/空格/英文分号逗号分割" />
            </Bubble>
        </>
    )
}

export default forwardRef(AppIdsInput);