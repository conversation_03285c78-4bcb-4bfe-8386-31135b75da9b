import React, { useEffect, useState } from "react";
import { FileCatalog } from "./FileCatalog";
import { FileEditDetails } from "./FileEditDetails";

interface Props {
	treeData: any[];
	language?: string;
	isFullScreen: boolean;
	onChangeFullScreen: object;
	isReadOnly?: boolean;
	onChangeTreeData?: object;
}

export function TreeEditor({
	treeData,
	language = "terraform",
	onChangeTreeData = () => {},
	isFullScreen,
	onChangeFullScreen,
	isReadOnly = false,
}: Props) {
	const [openList, setOpenList] = useState([]);
	const [activeIds, setActiveIds] = useState([]);
	const [defaultExpandedIds, setDefaultExpandedIds] = useState([]);
	// 左侧激活节点
	const [leftActiveNode, setLeftActiveNode] = useState(null);

	const getDefaultNodeId = (treeData) => {
		// 先临时这么写一下，因为有文件夹，所以要广度优先遍历树，并将得到的第一个节点的父节点展开
		const firstNode =
			treeData.length > 0 &&
			typeof treeData[0] === "object" &&
			treeData[0] !== null &&
			typeof treeData[0].content === "string"
				? [treeData[0].content]
				: [];
		setActiveIds(firstNode.slice(-1));
		setOpenList(firstNode.slice(-1));
		setDefaultExpandedIds(firstNode);
	};

	useEffect(() => {
		getDefaultNodeId(treeData);
	}, []);

	return (
		<div className="intlc-eventstack-editer intlc-eventstack-editer-flex intlc-eventstack-editer-validate">
			<FileCatalog
				treeNodeData={treeData}
				activeIds={activeIds}
				isReadOnly={isReadOnly}
				expandedIds={defaultExpandedIds}
				openList={openList}
				onHandleActiveNode={(id) => {
					setActiveIds(id ? [id] : []);
				}}
				onChangeExpandedNode={(ids) => {
					setDefaultExpandedIds(ids);
				}}
				onHandleOpen={(ids) => {
					setOpenList(ids);
				}}
				onHandleChangeTree={onChangeTreeData}
				onHandleChangeLeftActiveNode={(leftActiveNode) => {
					setLeftActiveNode(leftActiveNode);
				}}
			/>
			<FileEditDetails
				isReadOnly={isReadOnly}
				language={language}
				treeNodeData={treeData}
				activeIds={activeIds}
				openList={openList}
				expandedIds={defaultExpandedIds}
				leftActiveNode={leftActiveNode}
				onChangeActiveNode={(id) => {
					setActiveIds(id ? [id] : []);
				}}
				onChangeExpandedNode={(...ids) => {
					setDefaultExpandedIds(ids);
				}}
				onChangeOpenList={(...ids) => {
					setOpenList(ids);
				}}
				onChangeTreeNode={onChangeTreeData}
				onHandleSaveContent={() => {}}
				isFullScreen={isFullScreen}
				onChangeFullScreen={onChangeFullScreen}
			/>
		</div>
	);
}
