import {
	checkoutUploadFileName,
	deepTree,
	getParentNodeId,
	traverseTree,
	getAddNodeId,
	sortTreeNodeData,
} from "@src/utils/treeUtils";
import { getDiffSet } from "@src/utils/arrayUtils";
import { verificationFile } from "@src/utils/verificationFile";
import {
	Bubble,
	Button,
	Icon,
	Input,
	Justify,
	Modal,
	Form,
	Tree,
	message as tips,
} from "@tea/component";
import classNames from "classnames";
import FileSaver from "file-saver";
import $ from "jquery";
import _ from "lodash";
import React, { useEffect, useRef, useState } from "react";
import { MAX_LEVEL } from "@src/configs/tree";

// treeNodeData 目录树数据
// isReadOnly 目录是否是只读模式
// activeIds 上侧高亮的节点
// openList 需要在外侧展示的文件列表
// onHandleActiveNode 选中高亮事件
// onHandleOpen 点击在外侧展示的事件
// expandedIds 默认展开的节点
// onHandleChangeTree 改变目录树数据事件

export function FileCatalog({
	treeNodeData = [],
	isReadOnly = false,
	activeIds = [],
	openList = [],
	onHandleActiveNode,
	onHandleOpen,
	expandedIds,
	onHandleChangeTree,
	onChangeExpandedNode,
	onHandleChangeLeftActiveNode,
}) {
	const leafIcon = <Icon type="phone" />;
	const uploadFile = useRef(null);
	const parentIcon = ({ expanded }) => (
		<Icon type={expanded ? "folderopen" : "folderclose"} />
	);

	const [editingFolderValue, setEditingFolderValue] = useState("");
	const [isDeleteVisible, setIsDeleteVisible] = useState(false);
	const [isAddNodeVisible, setIsAddNodeVisible] = useState(false);
	// 新增的节点名是否合规
	const [isNodeNameCompliance, setIsNodeNameCompliance] = useState(true);
	// 编辑中的文件id
	const [editingFolderId, setEditingFolderId] = useState("");
	// 当前激活选中节点的层级
	const [nodeLevel, setNodeLevel] = useState(0);
	// 新增的选项
	const [addOption, setAddOption] = useState("file");
	// 新增节点名称
	const [addNodeName, setAddNodeName] = useState("");
	// 当前左侧激活高亮的节点
	const [activeNode, setActiveNode] = useState(null);
	// 当前激活高亮节点的父节点
	const [activeNodeParent, setActiveNodeParent] = useState({});
	const [formInputStatus, setFormInputStatus] = useState({
		status: "",
		message: " ",
	});
	// 当前面板引用
	const boardRef = useRef(null);

	// 上方文件列表影响左侧文件列表
	useEffect(() => {
		let newActiveNode = deepTree(treeNodeData, activeIds[0]);
		setActiveNode(activeIds.length ? newActiveNode : null);
	}, [activeIds]);

	//根据左侧激活节点设置文件层级
	useEffect(() => {
		const nodeIds = activeNode
			? getParentNodeId(treeNodeData, activeNode.id)
			: [];
		setNodeLevel(nodeIds.length);
		onHandleChangeLeftActiveNode(activeNode);
	}, [activeNode]);

	// 排除空白区域其余部分白名单
	const OUT_TREE_CLICK_WHITE_CLICK = [
		// 文件/文件夹
		".tea-tree__label-title",
		// 上传
		"#upload",
		// 新建文件
		".tea-icon-tic-newfile",
		// 新建文件夹
		".tea-icon-tic-newfolder",
		// 下载
		".tea-icon-download",
		// 打开文件夹
		".tea-icon-folderopen",
		// 关闭文件夹
		".tea-icon-folderclose",
		// 展开
		".tea-icon-arrowdown",
		".tea-icon-arrowright",
		// 编辑
		".tea-icon-pencil",
		// 输入框
		".tea-input.intlc-edit-input-minsize",
		// 删除
		".tea-icon-delete",
	];

	// 点击当前面板空白区域节点失去激活态
	const handleOutTreeClick = (e) => {
		let closeActiveStatus = true;
		OUT_TREE_CLICK_WHITE_CLICK.map((nodeClass) => {
			if (e.target === $(nodeClass)[0]) {
				closeActiveStatus = false;
			}
		});
		if (closeActiveStatus) {
			//onHandleActiveNode()
			setActiveNode(null);
			setActiveNodeParent({});
		}
	};

	useEffect(() => {
		const boardDOM = boardRef.current;
		boardDOM && boardDOM.addEventListener("click", handleOutTreeClick);
		return () => {
			boardDOM &&
				boardDOM.removeEventListener("click", handleOutTreeClick);
		};
	}, []);

	// 点击高亮节点事件
	const handleActiveNode = (avtive, content) => {
		// 获取当前高亮节点
		const activeNode = deepTree(treeNodeData, content.nodeId);
		setActiveNode(activeNode);
		// 获取当前高亮节点IDy以及其的父辈节点ID组合
		const nodeIds = getParentNodeId(treeNodeData, content.nodeId);
		const parentId = nodeIds[nodeIds.length - 2];

		const parentNode = parentId ? deepTree(treeNodeData, parentId) : {};
		setActiveNodeParent(parentNode);
		//文件夹展开收拢的逻辑参照vscode, 若是文件夹且没有被展开，则展开它，否则收拢它
		if (activeNode && activeNode.isFolder) {
			if (expandedIds.indexOf(content.nodeId) === -1) {
				onChangeExpandedNode(expandedIds.concat(content.nodeId));
			} else {
				onChangeExpandedNode(getDiffSet(expandedIds, [content.nodeId]));
			}
		} else {
			const openNodeIds = Array.from(new Set([...openList, ...avtive]));
			onHandleOpen(openNodeIds);
			//不是文件夹才会影响右侧的激活节点
			onHandleActiveNode(content.nodeId);
		}
	};

	//展开文件夹
	const handleExpandFolder = (node) => {
		//若文件夹没有没有被展开，则展开它
		if (node && node.isFolder && expandedIds.indexOf(node.id) === -1) {
			onChangeExpandedNode(expandedIds.concat(node.id));
		}
	};

	// 检查节点名称
	const checkoutNodeName = (data, name) => {
		let isRepeat = true;
		const reg = new RegExp('[\\[\\]/"?$|&@#/(){}]');
		const regularResult = reg.test(name);
		//需要被添加到的节点的id
		let addNodeId = getAddNodeId(activeNode, activeNodeParent);
		//被添加的节点id
		let finalId = addNodeId ? `${addNodeId}/${name}` : name;
		function deepSearch(tree) {
			for (var i = 0; i < tree.length; i++) {
				if (tree[i].children && tree[i].children.length > 0) {
					deepSearch(tree[i].children);
				}
				if (finalId === tree[i].id) {
					isRepeat = false;
					break;
				}
			}
		}
		deepSearch(data);
		if (regularResult || !isRepeat) {
			setIsNodeNameCompliance(false);
			setFormInputStatus({
				status: "error",
				message: regularResult ? "名称非法" : "名称重复",
			});
		}
		if (!regularResult && isRepeat) {
			setIsNodeNameCompliance(true);
			setFormInputStatus({
				status: "",
				message: "",
			});
		}
	};

	// 查询id是否重复
	const handleSearchRepeat = (tree, finalId) => {
		var isRepeat = false;
		function deepSearch(tree) {
			for (var i = 0; i < tree.length; i++) {
				if (tree[i].children && tree[i].children.length > 0) {
					deepSearch(tree[i].children);
				}
				if (finalId === tree[i].id) {
					isRepeat = true;
				}
			}
		}
		deepSearch(tree);
		return isRepeat;
	};

	// 编辑树节点
	const handleEditTreeNode = (tree, id, name, finalId) => {
		function deepSearch(tree, id) {
			for (var i = 0; i < tree.length; i++) {
				if (tree[i].children && tree[i].children.length > 0) {
					deepSearch(tree[i].children, id);
				}
				if (id === tree[i].id) {
					tree[i].content = name;
					tree[i].id = finalId;
					break;
				}
			}
		}
		deepSearch(tree, id);
		return tree;
	};

	// 删除节点
	const handleDeleteNode = (tree) => {
		const deleteId = activeNode.id;
		let treeData = _.cloneDeep(tree);

		function funDeleteNode(treeData) {
			for (var i = treeData.length; i > 0; i--) {
				if (treeData[i - 1].id === deleteId) {
					treeData.splice(i - 1, 1);
				} else {
					if (treeData[i - 1].children) {
						funDeleteNode(treeData[i - 1].children);
					}
				}
			}
		}
		funDeleteNode(treeData);
		sortTreeNodeData(treeData);
		onHandleChangeTree(treeData);
		onHandleActiveNode("");

		const flatTree = traverseTree(treeData);
		const flatTreeIds = new Set(flatTree.map((node) => node.id));
		// 根据删除后的树来比对openList里的id是否有被删除的,如果有,则过滤掉.
		const intersectionIds = Array.from(
			new Set(openList.filter((v) => flatTreeIds.has(v)))
		);
		onHandleOpen(intersectionIds);

		setIsDeleteVisible(false);
	};

	// 新增节点
	const handleAddNode = (tree, id, node) => {
		let treeNode = _.cloneDeep(tree);
		function deepSearch(treeNode, id) {
			for (var i = 0; i < treeNode.length; i++) {
				if (treeNode[i].children && treeNode[i].children.length > 0) {
					deepSearch(treeNode[i].children, id);
				}
				if (id === treeNode[i].id) {
					treeNode[i].children.push(node);
					break;
				}
			}
		}
		deepSearch(treeNode, id);
		return treeNode;
	};

	// 新增文件或文件夹
	const handleAddFileFolder = (fileName, file) => {
		let treeNode = _.cloneDeep(treeNodeData);
		let addNodeId = getAddNodeId(activeNode, activeNodeParent);
		const node = {
			id: addNodeId ? `${addNodeId}/${fileName}` : fileName,
			content: fileName,
			file: file,
			children: [],
			isFolder: file ? false : addOption !== "file",
		};
		if (!activeNode || !addNodeId) {
			treeNode.push(node);
			sortTreeNodeData(treeNode);
			onHandleChangeTree(treeNode);
		} else {
			const newTree = handleAddNode(treeNodeData, addNodeId, node);
			sortTreeNodeData(newTree);
			onHandleChangeTree(newTree);
		}
		setAddNodeName("");
		setIsAddNodeVisible(false);
		//设置新建的节点为左侧激活节点
		setActiveNode(node);
		//若是文件则同时设置新建的节点为上侧激活节点
		if (node && !node.isFolder) {
			onHandleActiveNode(node.id);
			onHandleOpen(openList.concat(node.id));
		}
	};

	// 重命名
	const handleFileBlue = () => {
		const reg = new RegExp('[\\[\\]/"?$|&@#/(){}]');
		let isFocus = reg.test(editingFolderValue);
		let finalId = editingFolderId
			.split("/")
			.slice(0, -1)
			.concat(editingFolderValue)
			.join("/");
		if (finalId === editingFolderId) {
			setEditingFolderId("");
			setEditingFolderValue("");
			return;
		}
		const checkRepeat = handleSearchRepeat(treeNodeData, finalId);
		if (checkRepeat) {
			$(".intlc-edit-input-minsize").eq(0).focus();
			tips.error({ content: "名称重复" });
			setEditingFolderId("");
			setEditingFolderValue("");
			return;
		}
		if (isFocus || !editingFolderValue) {
			$(".intlc-edit-input-minsize").eq(0).focus();
			tips.error({ content: "名称非法" });
			setEditingFolderId("");
			setEditingFolderValue("");
			return;
		}
		const editTreeData = handleEditTreeNode(
			treeNodeData,
			editingFolderId,
			editingFolderValue,
			finalId
		);
		sortTreeNodeData(editTreeData);
		onHandleChangeTree(editTreeData);

		const editNode = deepTree(editTreeData, finalId);
		const editOpenList = Array.from(openList);
		if (editNode && !editNode.isFolder) {
			onHandleActiveNode(finalId);
			editOpenList[openList.indexOf(editingFolderId)] = finalId;
			onHandleOpen(editOpenList);
		}
		setEditingFolderId("");
		setEditingFolderValue("");
	};

	// 上传新文件
	const handleUploadFile = (uploadFile) => {
		try {
			const verificationFileResult = verificationFile(uploadFile);
			if (verificationFileResult) {
				tips.error({ content: verificationFileResult });
				return;
			}
			let file = uploadFile.files[0];
			var reader = new FileReader(); //新建一个FileReader
			reader.readAsText(file, "UTF-8"); //读取文件
			if (file.name === "") {
				return;
			}
			reader.onload = (evt) => {
				//读取完文件之后会回来这里
				const fileName = checkoutUploadFileName(
					file.name,
					treeNodeData,
					activeNode,
					activeNodeParent
				);
				const fileString = evt.target.result; // 读取文件内容
				handleAddFileFolder(fileName, fileString);
			};
			reader.onerror = (err) => {
				//读取完文件之后会回来这里
				throw err.toString();
			};
		} catch (err) {
			let message = err.msg || err;
			tips.error({ content: message });
		}
	};

	//下载文件
	const handleDownLoadFile = () => {
		// @ts-ignore
		if (!activeNode || (activeNode && activeNode.isFolder)) {
			tips.error({ content: "请选择一个文件" });
			return;
		}
		try {
			// @ts-ignore
			const blob = new Blob([activeNode.file], {
				type: "text/plain;charset=utf-8",
			});
			// @ts-ignore
			FileSaver.saveAs(blob, activeNode.content);
		} catch (err) {
			let message = err.msg;
			tips.error({ content: message });
		}
	};

	const toolMencDom = () => {
		return (
			<div
				className={classNames({
					"intlc-editer-files-operate-disable": isReadOnly,
				})}
			>
				<Bubble
					content={
						isReadOnly
							? ""
							: nodeLevel < MAX_LEVEL
							? "新建文件"
							: `只支持${MAX_LEVEL}层结构`
					}
					dark={true}
				>
					<Icon
						type="tic-newfile"
						onClick={() => {
							if (isReadOnly) {
								return;
							}
							if (nodeLevel < MAX_LEVEL) {
								handleExpandFolder(activeNode);
								setAddOption("file");
								setIsAddNodeVisible(true);
							}
						}}
					></Icon>
				</Bubble>
				<Bubble
					content={
						isReadOnly
							? ""
							: nodeLevel < MAX_LEVEL - 1
							? "新建文件夹"
							: `只支持${MAX_LEVEL}层结构`
					}
					dark={true}
				>
					<Icon
						type="tic-newfolder"
						onClick={() => {
							if (isReadOnly) {
								return;
							}
							if (nodeLevel < MAX_LEVEL - 1) {
								handleExpandFolder(activeNode);
								setAddOption("folder");
								setIsAddNodeVisible(true);
							}
						}}
					></Icon>
				</Bubble>
				<Bubble content={isReadOnly ? "" : "下载"} dark={true}>
					<Icon
						type="download"
						onClick={() => {
							if (isReadOnly) {
								return;
							}
							handleDownLoadFile();
						}}
					></Icon>
				</Bubble>
				<Bubble content={isReadOnly ? "" : "上传"} dark={true}>
					<Icon
						className="footer-upload"
						type="upload"
						style={{ position: "relative" }}
					>
						{!isReadOnly && (
							<input
								id="upload"
								type="file"
								ref={uploadFile}
								title=""
								style={{
									opacity: "0",
									width: "100%",
									height: "100%",
									position: "absolute",
									top: "0",
									left: "0",
								}}
								onChange={(e) => {
									handleUploadFile(e.target);
									e.target.value = "";
								}}
							/>
						)}
					</Icon>
				</Bubble>
			</div>
		);
	};

	const treeNodeContent = (node) => {
		return editingFolderId === node.id &&
			activeNode &&
			activeNode.id === node.id ? (
			<Input
				className="intlc-edit-input-minsize"
				value={editingFolderValue}
				style={{ width: "calc(100% - 30px)" }}
				autoFocus
				onChange={(value) => setEditingFolderValue(value)}
				onBlur={() => handleFileBlue()}
				onKeyDown={(e) => e.key === "Enter" && handleFileBlue()}
			></Input>
		) : (
			<>
				{node.content}
				{!isReadOnly && activeNode && activeNode.id === node.id && (
					<>
						<Icon
							type="pencil"
							onClick={() => (
								setEditingFolderValue(node.content),
								setEditingFolderId(node.id)
							)}
						/>{" "}
						<Icon
							type="delete"
							onClick={() => {
								setIsDeleteVisible(true);
							}}
						/>
					</>
				)}
			</>
		);
	};

	const renderTree = (treeNodes) => {
		return treeNodes.map((node) => {
			if (!node.isFolder) {
				return (
					<Tree.Node
						id={node.id}
						key={node.id}
						content={treeNodeContent(node)}
						icon={leafIcon}
					/>
				);
			} else {
				return (
					<Tree.Node
						id={node.id}
						content={treeNodeContent(node)}
						icon={parentIcon}
						key={node.id}
					>
						{renderTree(node.children)}
					</Tree.Node>
				);
			}
		});
	};

	const addNodeModal = () => {
		return (
			<Modal
				visible={isAddNodeVisible}
				caption={addOption === "file" ? "新建文件" : "新建文件夹"}
				size="auto"
				onClose={() => {
					setAddNodeName("");
					setIsAddNodeVisible(false);
					setFormInputStatus({
						status: "",
						message: "",
					});
				}}
			>
				<Modal.Body>
					<Form layout="vertical" style={{ width: "100%" }}>
						<Form.Item
							label={
								addOption === "file"
									? "请输入文件名"
									: "请输入文件夹名"
							}
							// @ts-ignore
							status={formInputStatus.status}
							message={formInputStatus.message}
						>
							<Input
								size="full"
								value={addNodeName}
								autoFocus={true}
								onChange={(value) => {
									checkoutNodeName(treeNodeData, value);
									setAddNodeName(value);
								}}
								//名称合法（不为空，不重复）时可按下Enter键确认
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										addNodeName &&
											isNodeNameCompliance &&
											handleAddFileFolder(
												addNodeName,
												""
											);
										e.preventDefault();
									}
								}}
							/>
						</Form.Item>
					</Form>
				</Modal.Body>
				<Modal.Footer>
					<Button
						type="primary"
						disabled={!addNodeName || !isNodeNameCompliance}
						onClick={() => {
							handleAddFileFolder(addNodeName, "");
						}}
					>
						确认
					</Button>
					<Button
						type="weak"
						onClick={() => {
							setIsAddNodeVisible(false);
							setAddNodeName("");
							setFormInputStatus({
								status: "",
								message: "",
							});
						}}
					>
						取消
					</Button>
				</Modal.Footer>
			</Modal>
		);
	};

	return (
		<>
			<div className="intlc-eventstack-editer-r intlc-eventstack-editer-side">
				<div className="intlc-eventstack-editer-inner" ref={boardRef}>
					<Justify
						left={
							<div className="intlc-eventstack-editer-tit">
								文件
							</div>
						}
						right={toolMencDom()}
					/>
					<Tree
						activable
						onActive={(avtive, content) =>
							handleActiveNode(avtive, content)
						}
						activeIds={activeNode ? [activeNode.id] : []}
						expandedIds={expandedIds}
						onExpand={(expandIds, cxt) => {
							// 获取当前高亮节点IDy以及其的父辈节点ID组合
							const nodeIds = getParentNodeId(
								treeNodeData,
								cxt.nodeId
							);
							const parentId = nodeIds[nodeIds.length - 2];

							const parentNode = parentId
								? deepTree(treeNodeData, parentId)
								: {};
							if (cxt.expanded) {
								setActiveNodeParent(parentNode);
								onChangeExpandedNode(
									expandedIds.concat(cxt.nodeId)
								);
							} else {
								setActiveNodeParent({});
								onChangeExpandedNode(
									getDiffSet(expandedIds, [cxt.nodeId])
								);
							}
						}}
					>
						{renderTree(treeNodeData)}
					</Tree>
				</div>
			</div>
			<Modal
				visible={isDeleteVisible}
				size="auto"
				onClose={() => {
					setIsDeleteVisible(false);
				}}
			>
				<Modal.Body>确认删除吗</Modal.Body>
				<Modal.Footer>
					<Button
						type="primary"
						onClick={() => {
							handleDeleteNode(treeNodeData);
						}}
					>
						确认
					</Button>
					<Button
						type="weak"
						onClick={() => {
							setIsDeleteVisible(false);
						}}
					>
						取消
					</Button>
				</Modal.Footer>
			</Modal>
			{addNodeModal()}
		</>
	);
}
