// @ts-nocheck
import ace from "ace-builds";
import "ace-builds/src-noconflict/ext-language_tools";
import "ace-builds/src-noconflict/mode-terraform";
import "ace-builds/src-noconflict/snippets/terraform";
import "ace-builds/src-noconflict/mode-yaml";
import "ace-builds/src-noconflict/snippets/yaml";
import "ace-builds/src-noconflict/theme-kuroir";
import "ace-builds/src-noconflict/theme-merbivore";
import React from "react";

export class CodeEditor extends React.Component {
	componentDidMount() {
		this.aceEditor = ace.edit(this.ace, {
			fontSize: 14,
			theme: "ace/theme/merbivore",
			mode: `ace/mode/${this.props.language}`,
			wrap: true,
			tabSize: 2,
			readOnly: this.props.readOnly,
		});
		this.aceEditor.setOptions({
			enableSnippets: true,
			enableLiveAutocompletion: true,
			enableBasicAutocompletion: true,
			showPrintMargin: false,
			maxLines: Infinity,
			minLines: 1, // 最小高度
		});
		this.aceEditor.focus();
		this.aceEditor
			.getSession()
			.selection.on("changeCursor", this.onChangeCursor);
		// @ts-ignore
		this.aceEditor.getSession().on("change", this.onChange);

		//绑定ctrl+s 保存
		this.aceEditor.commands.addCommand({
			name: "myCommand",
			bindKey: "Ctrl-s",
			exec: () => {
				this.props.onSaveContent();
			},
		});
	}

	onChange = () => {
		this.props.onChangeContent(this.aceEditor.getSession().getValue());
	};

	onChangeCursor = () => {
		this.props.onChangeCursor(this.aceEditor.getCursorPosition());
	};

	componentDidUpdate = (prevProps) => {
		if (this.props.fontSize !== prevProps.fontSize) {
			this.aceEditor.setFontSize(parseInt(this.props.fontSize));
		}
		if (this.props.content !== this.aceEditor.getSession().getValue()) {
			this.aceEditor.setValue(
				this.props.content,
				this.props.content.length
			);
		}
		if (
			(!prevProps.leftActiveNode &&
				this.props.leftActiveNode &&
				!this.props.leftActiveNode.isFolder) ||
			(prevProps.leftActiveNode &&
				this.props.leftActiveNode &&
				this.props.leftActiveNode.id !== prevProps.leftActiveNode.id &&
				!this.props.leftActiveNode.isFolder)
		) {
			this.aceEditor.focus();
		}
		if (!this.props.position) {
			this.aceEditor.moveCursorToPosition(
				this.aceEditor.getCursorPosition()
			);
			return;
		}
		if (
			this.props.position.row !==
				this.aceEditor.getCursorPosition().row ||
			this.props.position.column !==
				this.aceEditor.getCursorPosition().column
		) {
			this.aceEditor.moveCursorToPosition(this.props.position);
			this.aceEditor.clearSelection();
			this.aceEditor.resize(true);
		}
	};

	render() {
		return (
			<div
				className="Editor"
				style={{
					position: "absolute",
					height: "100%",
					top: 0,
					right: 0,
					bottom: 0,
					left: 0,
					overflowY: "auto",
				}}
			>
				<div
					ref={(ace) => (this.ace = ace)}
					style={{
						width: "100%",
						// height: this.props.height,
						position: "absolute" /* Added */,
						top: "24px",
						right: "0",
						bottom: "0",
						left: "0",
						overflowX: "hidden",
						marginBottom: "30px",
					}}
				></div>
			</div>
		);
	}
}
