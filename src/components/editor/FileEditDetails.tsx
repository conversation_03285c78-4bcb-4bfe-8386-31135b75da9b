import { deepTree, getParentNodeId } from "@src/utils/treeUtils";
import { Bubble, Icon, message as tips, Select } from "@tea/component";
import classNames from "classnames";
import copy from "copy-to-clipboard";
import $ from "jquery";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { CodeEditor } from "./CodeEditor";

export function FileEditDetails({
	isReadOnly,
	language,
	treeNodeData,
	activeIds,
	openList,
	expandedIds,
	leftActiveNode,
	onChangeActiveNode,
	onChangeExpandedNode,
	onChangeOpenList,
	onChangeTreeNode,
	onHandleSaveContent,
	isFullScreen,
	onChangeFullScreen,
}) {
	// 当前文件
	const [currentNode, setCurrentNode] = useState(null);
	// 激活态文件队列
	const [activeNodeQueue, setActiveNodeQueue] = useState([]);

	// 编辑器高度
	const [editorHeight, setEditorHeight] = useState(0);
	// 信息框高度
	const [infoHeight, setInfoHeight] = useState(0);
	// 编辑器实际高度
	const [height, setHeight] = useState(0);
	// 头部操作区展开按钮
	const expandBtn = useRef(null);
	const [expandBtnVisible, setExpandBtnVisible] = useState(false);
	// 头部操作区展开面板
	const expandBoard = useRef(null);
	const [expandBoardVisible, setExpandBoardVisible] = useState(false);
	// 可视区域头部
	const [shownWindowList, setShownWindowList] = useState([]);
	// 窗口数量
	const [tabNum, setTabNum] = useState(0);
	// 字体大小
	const [fontSize, setFontSize] = useState("16");

	const EDITOR_HEIGHT_OFFSET = 8;

	//此Map的key为文件名，value为同文件名节点的id集合
	const shownWindowListMap = useMemo(() => {
		let map = new Map();
		shownWindowList.forEach((value) => {
			let name = value.split("/").pop();
			if (!map.has(name)) {
				map.set(name, [value]);
			} else {
				map.get(name).push(value);
			}
		});
		return map;
	}, [shownWindowList]);

	useEffect(() => {
		const activeNode = activeIds && activeIds[0];
		const treeNode = deepTree(treeNodeData, activeNode);
		// 激活态最近的节点
		const historyNode = activeNodeQueue.filter((node) =>
			deepTree(treeNodeData, node)
		)[-1];
		const curNode =
			activeNode && treeNode && !treeNode.isFolder
				? activeNode
				: historyNode;
		setCurrentNode(curNode);
		if (!activeNodeQueue.includes(curNode)) {
			setActiveNodeQueue([...activeNodeQueue, curNode]);
		}
	}, [treeNodeData, activeIds]);

	useEffect(() => {
		const handleSetHeight = () => {
			const codingHeight = $(".intlc-eventstack-editer").outerHeight();
			const codingHdHeight = $(".lab-edi-coding-hd").outerHeight();
			const codingBdIndexHeight = $(
				".lab-edi-coding-bd-index"
			).outerHeight();
			const designerInfoHeight = $(".app-tic-card__header").outerHeight();
			// 编辑器高度 = 编辑面板高度 - 头部操作区高度 - 文件索引高度 - 偏移量
			const codingEditorHeight =
				codingHeight -
				codingHdHeight -
				codingBdIndexHeight -
				EDITOR_HEIGHT_OFFSET;
			setEditorHeight(codingEditorHeight);
			setInfoHeight(designerInfoHeight);
		};
		handleSetHeight();
		$(window).resize(() => {
			handleSetHeight();
		});
		return () => {
			$(window).resize = null;
		};
	}, []);

	useEffect(() => {
		setHeight(
			isFullScreen ? $(".lab-edi-coding").height() - 60 : editorHeight
		);
	}, [isFullScreen, editorHeight]);

	// 改变当前窗口
	const handleChangeWindow = (id) => {
		if (currentNode === id) {
			return;
		}
		onChangeActiveNode(id);
		//祖先节点列表
		let parentNodeIds = getParentNodeId(treeNodeData, id).slice(0, -1);
		//向展开节点列表中添加祖先节点列表并去重
		Array.isArray(expandedIds) &&
			onChangeExpandedNode(
				...Array.from(new Set(expandedIds.concat(parentNodeIds)))
			);
	};

	// 关闭窗口
	// A.关闭当前窗口以外的窗口
	// B.关闭当前窗口
	// B.1 当前窗口为窗口列表仅有的窗口
	// B.2 当前窗口你为窗口列表的最后一个窗口，则当前窗口变为上一个窗口
	// B.3 其余情况当前窗口变为下一个窗口
	const handleCloseWindow = (id) => {
		const restWindowList = openList.filter((winId) => winId !== id);
		if (currentNode === id) {
			const curWinIndex = openList.indexOf(currentNode);
			let curWinNode = null;
			if (openList && openList.length === 1) {
				curWinNode = null;
			} else if (curWinIndex === openList.length - 1) {
				curWinNode = openList[curWinIndex - 1];
			} else {
				curWinNode = openList[curWinIndex + 1];
			}
			onChangeActiveNode(curWinNode);
			setCurrentNode(curWinNode);
			//祖先节点列表
			let parentNodeIds = getParentNodeId(treeNodeData, curWinNode).slice(
				0,
				-1
			);
			//向展开节点列表中添加祖先节点列表并去重
			Array.isArray(expandedIds) &&
				onChangeExpandedNode(
					...Array.from(new Set(expandedIds.concat(parentNodeIds)))
				);
		}
		onChangeOpenList(...restWindowList);
	};

	// 当前文件内容
	const currentContent = useMemo(() => {
		const exactNode = deepTree(treeNodeData, currentNode);
		return exactNode && exactNode.file ? exactNode.file : "";
	}, [currentNode, treeNodeData, isFullScreen]);

	// 当前文件光标
	const currentPosition = useMemo(() => {
		const exactNode = deepTree(treeNodeData, currentNode);
		return exactNode && exactNode.position ? exactNode.position : "";
	}, [currentNode, treeNodeData, isFullScreen]);

	// 改变当前文件内容
	const handleChangeContent = (file) => {
		const newTreeNodeData = Array.from(treeNodeData);
		function deepSearch(tree, id) {
			for (var i = 0; i < tree.length; i++) {
				if (tree[i].children && tree[i].children.length > 0) {
					deepSearch(tree[i].children, id);
				}
				if (id === tree[i].id) {
					tree[i].file = file;
					break;
				}
			}
		}
		deepSearch(newTreeNodeData, currentNode);
		onChangeTreeNode(newTreeNodeData);
	};

	// 改变当前文件光标
	const handleChangeCursor = (position) => {
		const newTreeNodeData = Array.from(treeNodeData);
		function deepSearch(tree, id) {
			for (var i = 0; i < tree.length; i++) {
				if (tree[i].children && tree[i].children.length > 0) {
					deepSearch(tree[i].children, id);
				}
				if (id === tree[i].id) {
					tree[i].position = position;
					break;
				}
			}
		}
		deepSearch(newTreeNodeData, currentNode);
		onChangeTreeNode(newTreeNodeData);
	};

	// 头部操作区展开面板隐藏
	const handleOutExpandClick = (e) => {
		if (
			e.target !== expandBtn.current &&
			e.target !== expandBoard.current
		) {
			setExpandBoardVisible(false);
		}
	};

	useEffect(() => {
		// 点击展开按钮和展开面板外的其他区域需要隐藏面板
		document.addEventListener("click", handleOutExpandClick);
		return () =>
			document.removeEventListener("click", handleOutExpandClick);
	}, []);

	const getOverWidthIndex = () => {
		const $tabCollection = $(".lab-tablist").find("li:not('.fillspace')");
		const $tabScroll = $(".lab-tab-scroll");

		setTabNum($tabCollection.length);
		const curTabNum = tabNum || $tabCollection.length;

		let tabWidthSum = 0;
		for (let index = 0; index < curTabNum; index++) {
			tabWidthSum += $tabCollection.eq(index).outerWidth();
			if (tabWidthSum > $tabScroll.outerWidth()) {
				return index - 1;
			}
		}
		return -1;
	};

	const initShownWindowList = () => {
		setShownWindowList(openList);
		const overWidthIndex = getOverWidthIndex();
		const curWinIndex = openList.indexOf(currentNode);
		if (overWidthIndex && overWidthIndex >= 0) {
			if (openList.indexOf(currentNode) > overWidthIndex) {
				// A.当前文件索引大于超限索引，可视区域头部向左滑动至当前文件在可视区域最后一个
				setShownWindowList(
					openList.slice(
						curWinIndex - overWidthIndex,
						curWinIndex + 1
					)
				);
			} else {
				// B.当前文件索引小于超限索引，可视区域头部正常显示
				setShownWindowList(openList.slice(0, overWidthIndex + 1));
			}
			openList.length > overWidthIndex + 1
				? setExpandBtnVisible(true)
				: setExpandBtnVisible(false);
		} else {
			const $tabCollection = $(".lab-tablist").find(
				"li:not('.fillspace')"
			);
			if (curWinIndex > $tabCollection.length - 1) {
				// C.xxx
				setShownWindowList(openList.slice(-$tabCollection.length - 1));
			} else {
				// D.超限索引不存在时，可视区域头部正常显示
				setExpandBtnVisible(false);
				setShownWindowList(openList.slice(0, openList.length));
			}
		}
	};

	useEffect(() => {
		initShownWindowList();
		$(window).resize(() => {
			initShownWindowList();
		});
		return () => {
			$(window).resize = null;
		};
	}, [openList, currentNode, expandBtnVisible]);

	useEffect(() => {
		if (currentNode && openList.length > 0) {
			// 强制触发编辑器状态更新
			setTimeout(() => {
				$(window).trigger("resize");
			}, 0);
		}
	}, [currentNode, isFullScreen, height, openList]);

	// 保存模板内容
	const handleSaveContent = async () => {
		onHandleSaveContent();
	};

	// 复制模板内容
	const handleCopyContent = () => {
		if (currentContent) {
			copy(currentContent);
			tips.success({ content: "复制成功" });
		} else {
			tips.error({ content: "请确认模板内容不为空" });
		}
	};

	const getBubbleContent = (win) => {
		let name = win.split("/").pop();
		let sameNameList = shownWindowListMap.get(name);
		//上方文件列表有同名文件则展示带路径的文件名，否则展示单文件名
		return sameNameList.length > 1 ? win : name;
	};

	const getFileName = (win) => {
		let name = win.split("/").pop();
		let sameNameList = shownWindowListMap.get(name);
		if (sameNameList.length > 1) {
			//如果有同名文件，将文件名粗体，并显示路径，参照vscode
			let pathWithSpace = win.split("/").reverse().slice(1).join(" ");
			return (
				<span>
					<b>{name}</b> {pathWithSpace}
				</span>
			);
		}
		return <span>{name}</span>;
	};

	return (
		<div className="intlc-eventstack-editer-l intlc-eventstack-editer-code">
			<div className="lab-edi-coding">
				<div className="lab-edi-coding-hd">
					<div
						className="lab-tab-scroll"
						style={
							expandBtnVisible
								? { width: "calc(100% - 145px)" }
								: { width: "calc(100% - 115px)" }
						}
					>
						<ul className="lab-tablist">
							{shownWindowList.length >= 0 &&
								shownWindowList.map((win, index) => {
									return (
										<li
											className={
												currentNode === win
													? "actived"
													: ""
											}
											key={index}
											onClick={() => {
												handleChangeWindow(win);
											}}
										>
											<Bubble
												content={getBubbleContent(win)}
												dark={isReadOnly}
											>
												{getFileName(win)}
											</Bubble>
											<Icon
												type="close"
												onClick={(e) => {
													e.stopPropagation();
													handleCloseWindow(win);
												}}
											/>
										</li>
									);
								})}
							<li
								className="fillspace"
								style={{
									maxWidth: "100%",
									width: "100%",
									background: isReadOnly ? "" : "#474747",
								}}
							></li>
						</ul>
					</div>
					<div className="lab-edi-coding-hd-r">
						{expandBtnVisible && (
							<div className="lab-edi-extend">
								<Icon
									ref={expandBtn}
									className={classNames(
										"lab-edi-extend-right",
										{ onclicked: expandBoardVisible }
									)}
									type="fullscreen"
									tooltip={"全屏"}
									onClick={() =>
										setExpandBoardVisible(
											!expandBoardVisible
										)
									}
								/>
							</div>
						)}
						<div className="lab-edi-coding-hd-save">
							<Select
								type="simulate"
								appearance="default"
								boxSizeSync
								value={fontSize}
								options={[
									{ value: "14", text: "14px" },
									{ value: "15", text: "15px" },
									{ value: "16", text: "16px" },
									{ value: "17", text: "17px" },
									{ value: "18", text: "18px" },
								]}
								onChange={(value) => setFontSize(value)}
								style={{ color: "gray", width: 50 }}
							/>
							<span>
								{!isReadOnly && (
									<Icon
										type="fullscreen"
										className="lab-edi-savebtn"
										tooltip={"复制"}
										onClick={handleCopyContent}
									/>
								)}
							</span>
							<span
								onClick={() =>
									onChangeFullScreen(!isFullScreen)
								}
								style={{ cursor: "pointer" }}
							>
								<Icon
									type={
										isFullScreen
											? "fullscreenquit"
											: "fullscreen"
									}
									className="lab-edi-fullscreen"
									tooltip={isFullScreen ? "退出全屏" : "全屏"}
									onClick={() =>
										onChangeFullScreen(!isFullScreen)
									}
								/>
							</span>
						</div>
					</div>
					{currentNode && (
						<div className="lab-edi-coding-bd-index">
							{getParentNodeId(treeNodeData, currentNode).map(
								(node, index) => {
									return node === currentNode ? (
										<span
											key={index}
											className="lab-edi-coding-bd-index-files current"
										>
											<i className="files-icon"></i>
											{node.split("/").pop()}
										</span>
									) : (
										<span
											key={index}
											className="lab-edi-coding-bd-index-files"
										>
											{node.split("/").pop()} {">"}
										</span>
									);
								}
							)}
						</div>
					)}
					{expandBoardVisible && (
						<div className="lab-edi-coding-dropdown">
							<ul
								className="lab-edi-coding-dropdown-menu"
								ref={expandBoard}
							>
								{openList.length >= 0 &&
									openList.map((win, index) => {
										return (
											<li
												key={index}
												onClick={() => {
													setExpandBoardVisible(
														false
													);
													handleChangeWindow(win);
												}}
												className={classNames({
													"is-showed":
														currentNode === win,
												})}
											>
												<p>
													{currentNode === win && (
														<Icon
															type="check"
															size="s"
														/>
													)}
													{win}
												</p>
											</li>
										);
									})}
							</ul>
						</div>
					)}
				</div>

				{openList.length >= 0 && (
					<div
						className="lab-edi-coding-main"
						style={{ paddingTop: "2px", position: "relative" }}
					>
						{currentNode && openList.length > 0 && (
							<CodeEditor
								// @ts-ignore
								onChangeContent={handleChangeContent}
								onChangeCursor={handleChangeCursor}
								content={currentContent}
								position={currentPosition}
								leftActiveNode={leftActiveNode}
								onSaveContent={handleSaveContent}
								height={height}
								readOnly={isReadOnly}
								fontSize={fontSize}
								language={language}
							/>
						)}
					</div>
				)}
			</div>
		</div>
	);
}
