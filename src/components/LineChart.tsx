import React from "react";
import { Card, Icon, StatusTip } from "@tencent/tea-component";
import { BasicLine } from "@tencent/tea-chart/lib/basicline";
import { saveSvgAsPng } from "save-svg-as-png";

export function LineChart({ mark, data, title, status, fetch }) {
	const tooltipFormatter = (series) => {
		return series.map((item, index) => {
			return {
				...item,
				label: `${item.label}`,
			};
		});
	};

	const legendFormatter = (label, index) => {
		return `${label}`;
	};

	const handleDowload = (): void => {
		let selector = `div.tea-card__body.${mark} div.tea-card__content div:first-child div:first-child svg`;
		let svgNode = document.querySelector(selector);
		saveSvgAsPng(svgNode, "chart.png");
	};

	return (
		<Card>
			<Card.Header>
				<h3 style={{ display: "inline-block", padding: 15 }}>
					{title}
				</h3>
				<Icon
					type="download"
					style={{ width: 20, height: 20, cursor: "pointer" }}
					onClick={handleDowload}
				/>
			</Card.Header>
			<Card.Body className={mark}>
				<BasicLine
					height={250}
					dataSource={data}
					position="date*value"
					color="type"
					tooltip={{ formatter: tooltipFormatter }}
					legend={{ formatter: legendFormatter }}
					tips={
						status && <StatusTip status={status} onRetry={fetch} />
					}
				/>
			</Card.Body>
		</Card>
	);
}
