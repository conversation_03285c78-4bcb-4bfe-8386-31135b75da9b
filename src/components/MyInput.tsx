import React, { useState, useEffect, useContext, useMemo, useImperativeHandle, forwardRef, useRef } from 'react';
import { Bubble } from "@tencent/tea-component";
import { Input, TextArea } from "@tencent/tea-component/lib/input";

interface RegxCheck {
    Regx: RegExp,
    Content: string,
}

interface Props {
    CallBack: Function,
    Need?: boolean,
    RegxCheckGroup?: Array<RegxCheck>,
    Value?: string,
    Multiple?: boolean,
    Placeholder?: string,
    updateDeps?: any,
    disabled?: boolean,
}

function MyInput({ CallBack, Need = false, RegxCheckGroup = [], Value = '', Multiple = false, Placeholder = "", updateDeps = [], disabled = false }: Props, ref) {
    //输入文本对象
    const [inputStr, setInputStr] = useState<string>(Value)
    //visibleContent
    const [visibleContent, setVisibleContent] = useState<string>('')
    //firstLoad
    const [firstLoad, setFirstLoad] = useState<boolean>(true)

    useImperativeHandle(ref, () => ({
        Check: Check,
        // Flush: Flush,
    }))

    //校验函数
    const Check = () => {
        let flag = true
        if (firstLoad) {
            setFirstLoad(false)
            return flag;
        }
        //遍历正则校验
        RegxCheckGroup.map(item => {
            if (item.Regx.exec(inputStr.trim())) {
                setVisibleContent('')
            } else {
                if (inputStr.trim() != '') {
                    setVisibleContent(item.Content)
                    flag = false
                }
            }
        })

        //非空校验
        if (flag) {
            if (inputStr.trim() === '') {
                if (Need) {
                    setVisibleContent('不能为空')
                    flag = false
                }
                else {
                    setVisibleContent('')
                    flag = true
                }
            } else {
                setVisibleContent('')
                flag = true
            }
        }
        return flag
    }

    //刷新输入框的值
    const Flush = (x) => {
        setInputStr(x)
    }

    //输入文本变化或入参属性变化，发起校验
    useEffect(() => {
        Check();
    }, [inputStr, Need])

    //输入文本，或输入文本合法性变化，进行回调，把结果返回
    useEffect(() => {
        CallBack(inputStr.trim())
    }, [inputStr, visibleContent])

    //刷新输入框的值
    useEffect(() => {
        Flush(Value)
    }, [Value])

    return (
        <>
            <Bubble placement={"top"} content={visibleContent} visible={visibleContent != ''} error updateDeps={updateDeps}>
                {
                    Multiple ?
                        <TextArea
                            disabled={disabled}
                            onChange={(value) => { setInputStr(value) }}
                            value={inputStr}
                            placeholder={Placeholder}
                        />
                        :
                        <Input
                            disabled={disabled}
                            onChange={(value) => { setInputStr(value) }}
                            value={inputStr}
                            placeholder={Placeholder}
                            size="full"
                        />
                }
            </Bubble >
        </>
    )
}

export default forwardRef(MyInput)