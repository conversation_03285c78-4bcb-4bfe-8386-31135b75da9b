import { MicroAppEnum, MICRO_APP_CONTAINER_SELECTOR, MICRO_APP_ENTRY } from '@src/configs/micro-app-config';
import { S_KEY } from '@src/components/MicroDevMode';
import { IMicroDevConfig } from '@src/components/MicroDevMode/index.type';
import { getProcessEnv } from '../app/utils';

const env = getProcessEnv();

const defaultMicroAppConfig = [
	// {
	// 	name: MicroAppEnum.ISA_CLOUD_ARCH, // 云架构运营端
	// 	entry: MICRO_APP_ENTRY[env]?.[MicroAppEnum.ISA_CLOUD_ARCH],
	// 	container: MICRO_APP_CONTAINER_SELECTOR,
	// 	activeRule: '/advisor/new-architecture',
	// 	props: {},
	// },
	// {
	// 	name: MicroAppEnum.CLOUD_ARCH_ADMIN, // 云架构管理系统
	// 	entry: MICRO_APP_ENTRY[env]?.[MicroAppEnum.CLOUD_ARCH_ADMIN],
	// 	container: MICRO_APP_CONTAINER_SELECTOR,
	// 	activeRule: '/advisor/operation-manage',
	// 	props: {},
	// },
	// {
	// 	name: MicroAppEnum.ISA_DIGITAL_ASSETS, // 云顾问数字资产
	// 	entry: MICRO_APP_ENTRY[env]?.[MicroAppEnum.ISA_DIGITAL_ASSETS],
	// 	container: MICRO_APP_CONTAINER_SELECTOR,
	// 	activeRule: '/advisor/digital-assets',
	// 	props: {},
	// },
	// {
	// 	name: MicroAppEnum.ISA_EMAIL_CENTER, // 云顾问邮件中心
	// 	entry: MICRO_APP_ENTRY[env]?.[MicroAppEnum.ISA_EMAIL_CENTER],
	// 	container: MICRO_APP_CONTAINER_SELECTOR,
	// 	activeRule: '/advisor/email-center',
	// 	props: {},
	// },
	{
		name: MicroAppEnum.ISA_CLOUD_INSPECTION, // 云巡检
		entry: MICRO_APP_ENTRY[env]?.[MicroAppEnum.ISA_CLOUD_INSPECTION],
		container: MICRO_APP_CONTAINER_SELECTOR,
		activeRule: '/advisor/cloud-inspection',
		props: {},
	},
	{
		name: MicroAppEnum.ISA_CLOUD_ESCORT, // 云护航
		entry: MICRO_APP_ENTRY[env]?.[MicroAppEnum.ISA_CLOUD_ESCORT],
		container: MICRO_APP_CONTAINER_SELECTOR,
		activeRule: '/advisor/cloud-escort',
		props: {},
	},
];

const getFinalMicroAppConfig = () => {
	const storage = window.localStorage;
	try {
		const itemStr = storage.getItem(S_KEY) ?? '';
		let config: IMicroDevConfig | undefined;
		if (itemStr) {
			config = JSON.parse(itemStr);
		}
		if (typeof config === 'object') {
			return defaultMicroAppConfig.map((e) => {
				const appName = e.name ?? '';
				const devConfig = config?.[appName] ?? {};
				if (devConfig.devSwitch) {
					return {
						...e,
						entry: devConfig.url ?? e.entry,
					};
				}
				return e;
			});
		}
		return defaultMicroAppConfig;
	} catch (e) {
		console.error(e?.message);
		return defaultMicroAppConfig;
	}
};

// 生产环境屏蔽microDev模式
const FINAL_MICRO_APP_CONFIG = env === 'prod' ? defaultMicroAppConfig : getFinalMicroAppConfig();

export default FINAL_MICRO_APP_CONFIG;
