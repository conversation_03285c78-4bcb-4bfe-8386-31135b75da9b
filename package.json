{"name": "@tencent/tea-app-iac-operation-platform", "version": "1.0.0", "description": "The iac-operation-platform tea app", "main": "src/app.js", "scripts": {"dev": "CI=1 tea dev", "build": "tea build", "scan": "tea scan", "test": "jest"}, "keywords": ["tea", "app", "iac-operation-platform"], "engines": {"typescript": ">3.3"}, "license": "UNLICENSED", "dependencies": {"@tencent/autotracker-beacon-oa": "^4.3.3", "@tencent/eslint-plugin-tea-i18n": "^0.1.13", "@tencent/qmfe-yoa-react-ui": "^1.0.25", "@tencent/sigma-editor": "^0.2.2-fix.7", "@tencent/tea-app": "^2.1.7", "@tencent/tea-chart": "^2.4.6", "@tencent/tea-component": "^2.7.8", "@tencent/tea-style-international-code": "^1.0.175", "@tencent/tea-style-toc": "^1.0.9", "@tencent/webpack-sentry-plugin": "^2.0.4", "ace-builds": "^1.4.12", "aegis-web-sdk": "^1.36.9", "ahooks": "^3.7.4", "antd": "^5.8.3", "axios": "^0.21.2", "classnames": "^2.2.6", "elliptic": "^6.6.1", "file-saver": "^2.0.2", "final-form": "^4.20.1", "history": "^4.9.0", "jquery": "^3.5.1", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "moment": "^2.29.4", "pako": "^1.0.11", "qiankun": "^2.10.16", "raven-js": "^3.27.2", "react": "^16.11.0", "react-activation": "^0.12.2", "react-dom": "^16.11.0", "react-final-form-hooks": "^2.0.2", "react-hook-form": "^7.54.2", "react-router-dom": "^4.3.1", "react-syntax-highlighter": "^15.5.0", "react-uuid": "^1.0.3", "save-svg-as-png": "^1.4.17", "swr": "^2.2.5", "tdesign-icons-react": "^0.3.2", "tdesign-react": "^1.3.0", "xlsx": "^0.18.5", "pbkdf2": "^3.1.3", "form-data": "2.5.4"}, "devDependencies": {"@babel/preset-env": "^7.11.5", "@babel/preset-react": "^7.10.4", "@hot-loader/react-dom": "^16.11.0", "@tencent/eslint-config-tencent": "^0.12.2", "@tencent/tea-scripts": "^2.1.17", "@tencent/tea-types": "^0.1.12", "@types/jest": "^26.0.14", "@types/react": "^16.8.4", "@types/react-dom": "^16.8.2", "@types/react-router-dom": "^4.3.1", "@types/webpack": "^4.4.32", "@typescript-eslint/eslint-plugin": "^4.6.1", "@typescript-eslint/parser": "^4.6.1", "@wangeditor/editor": "^5.1.23", "babel-jest": "^26.3.0", "cos-js-sdk-v5": "^1.8.0", "cross-env": "^7.0.3", "eslint": "^7.12.1", "jest": "^26.0.0", "jest-extended": "^0.11.5", "pubsub-js": "^1.9.5", "react-test-renderer": "^16.13.1", "ts-jest": "^26.4.0", "tvision-charts-react": "^3.2.15", "typescript": "^3.8.2"}, "volta": {"node": "16.8.0"}}