{"stats": {"marked": 57, "enUntranslated": 35, "jaUntranslated": 57, "koUntranslated": 57, "unmarked": 475}, "zh": {"untranslated": ["k_002qcua", "k_002qrgn", "k_002rq6l", "k_002uu5g", "k_002vfrj", "k_003mgxt", "k_003tyud", "k_039ag85", "k_03b5ku9", "k_03enx2m", "k_0cl60bh", "k_0d0v50n", "k_0h4eog8", "k_0lqtuon", "k_0ngrz6p", "k_0ohqnyg", "k_0wxw5up", "k_12209oh", "k_13qhcur", "k_15g70ie", "k_16iwm3r", "k_17bsyzp", "k_18go7ez", "k_19k8xx9", "k_1fbo32k", "k_1t56f44", "k_1vwqlwv", "k_1ynfm2i"], "unused": ["k_0003jjb", "k_0003y9x", "k_002qhaa", "k_002r79h", "k_002rflt", "k_002v9zj", "k_002vxya", "k_002wzvs", "k_003hn48", "k_003j5tp", "k_003l8u0", "k_003mam4", "k_003n1v2", "k_003nabw", "k_003ngex", "k_003ntss", "k_003nzwy", "k_003pb7f", "k_003psvr", "k_003py1h", "k_003q07e", "k_003q276", "k_003qcl1", "k_003qdlq", "k_003qqzh", "k_003qytf", "k_003r4v2", "k_003rk1s", "k_003u02c", "k_02iirea", "k_02iq161", "k_02n863s", "k_02n8dgw", "k_036vcco", "k_036vddt", "k_0375n87", "k_0376nm2", "k_03aj50y", "k_03bdfew", "k_03bkewo", "k_03bzpgk", "k_03ce9tt", "k_03ckobz", "k_03dd1g6", "k_03efxyg", "k_03el9pa", "k_03eo46q", "k_03f4c34", "k_03f610l", "k_03fdfbs", "k_03fgn8g", "k_03fqp9o", "k_03gltpg", "k_03gm54s", "k_03i6tsc", "k_03i78mh", "k_03i7hu1", "k_03i7ok1", "k_03i98ch", "k_03i98l8", "k_03i9b76", "k_03i9ihe", "k_03i9qmf", "k_03ia8tl", "k_03iaiks", "k_03ianq2", "k_03ibg5h", "k_03ibj2i", "k_03icebg", "k_03ig9m1", "k_03ih30j", "k_03ih7p4", "k_03ihq2y", "k_03jbfdj", "k_03jbol1", "k_03jcqz8", "k_04e6gdv", "k_0520foa", "k_066stkq", "k_076kgm3", "k_0bh7ody", "k_0bp8b8u", "k_0by6a1n", "k_0bya3vq", "k_0bz6bjr", "k_0c6h5uj", "k_0c8r25e", "k_0c9j5xo", "k_0dtu2a6", "k_0eqxnsy", "k_0f7dbkw", "k_0f7g13k", "k_0f7haai", "k_0fbfutw", "k_0foh402", "k_0fv59jz", "k_0fw33u6", "k_0fz88bx", "k_0gsa28x", "k_0gxjx8y", "k_0i7zezh", "k_0kt4vkn", "k_0lpi4yf", "k_0lpk0ac", "k_0lqh56w", "k_0ml5pgp", "k_0ot1va2", "k_0oyzrvp", "k_0oz6tc6", "k_0qhczfd", "k_0ru3vyu", "k_0rwfaqi", "k_0tzrfcd", "k_0u61bk6", "k_0vl1km5", "k_0vuea5m", "k_0vx4c8u", "k_0w81vyr", "k_0wr1l4p", "k_0wry3ub", "k_0y8fvcp", "k_0yjmsh6", "k_0yjv1z1", "k_0yklp86", "k_0zszstl", "k_0zvckkz", "k_10fn5ti", "k_10hanur", "k_10rcmlz", "k_10rftyn", "k_11k4fu4", "k_11yo8ly", "k_1260def", "k_12cv7pq", "k_12dkdi9", "k_12dt9ms", "k_12wkh89", "k_132siea", "k_132z5dz", "k_1332qj7", "k_134jb2t", "k_137rrbz", "k_14w5k1n", "k_150zc86", "k_15ihqtd", "k_15ihyxa", "k_15kabpm", "k_15kx1ws", "k_15mt02y", "k_15ohzol", "k_15w8p2y", "k_15wcm81", "k_15wgku3", "k_1653ra8", "k_16v2xx3", "k_17aqykb", "k_17univl", "k_18frlt8", "k_18n2xxa", "k_19k8uzp", "k_19k8uzp_switch", "k_19khdlm", "k_19km6lh", "k_1cmnz4v", "k_1dfpb2r", "k_1fkd43e", "k_1gqmg84", "k_1icqfe0", "k_1iq4unz", "k_1k2t0sj", "k_1k4s2ly", "k_1kjins0", "k_1kwhdgw", "k_1lcuyce", "k_1m45y2g", "k_1mfs12e", "k_1mfu8nj", "k_1mguztd", "k_1mm1hd6", "k_1n4b1w3", "k_1snda0u", "k_1ss6n7b", "k_1tci5e5", "k_1w0r2ux", "k_1w749h6", "k_1w7avpb", "k_1yk18j2"]}, "en": {"untranslated": ["k_002qcua", "k_002qrgn", "k_002rq6l", "k_002uu5g", "k_002vfrj", "k_003mgxt", "k_003tyud", "k_039ag85", "k_03b5ku9", "k_03enx2m", "k_08oqvxi", "k_0am4rt3", "k_0aof5hu", "k_0cl60bh", "k_0d0v50n", "k_0h4eog8", "k_0lqtuon", "k_0ngrz6p", "k_0ohqnyg", "k_0olfaft", "k_0wxw5up", "k_11fw1oj", "k_12209oh", "k_13qhcur", "k_15g70ie", "k_166g259", "k_16iwm3r", "k_17bsyzp", "k_18go7ez", "k_19k8xx9", "k_1asbjtx", "k_1fbo32k", "k_1t56f44", "k_1vwqlwv", "k_1ynfm2i"], "unused": ["k_0003jjb", "k_0003y9x", "k_002qhaa", "k_002r79h", "k_002rflt", "k_002v9zj", "k_002vxya", "k_002wzvs", "k_003hn48", "k_003j5tp", "k_003l8u0", "k_003mam4", "k_003n1v2", "k_003nabw", "k_003ngex", "k_003ntss", "k_003nzwy", "k_003pb7f", "k_003psvr", "k_003py1h", "k_003q07e", "k_003q276", "k_003qcl1", "k_003qdlq", "k_003qqzh", "k_003qytf", "k_003r4v2", "k_003rk1s", "k_003u02c", "k_02iirea", "k_02iq161", "k_02n863s", "k_02n8dgw", "k_036vcco", "k_036vddt", "k_0375n87", "k_0376nm2", "k_03aj50y", "k_03bdfew", "k_03bkewo", "k_03bzpgk", "k_03ce9tt", "k_03ckobz", "k_03dd1g6", "k_03efxyg", "k_03el9pa", "k_03eo46q", "k_03f4c34", "k_03f610l", "k_03fdfbs", "k_03fgn8g", "k_03fqp9o", "k_03gltpg", "k_03gm54s", "k_03i6tsc", "k_03i78mh", "k_03i7hu1", "k_03i7ok1", "k_03i98ch", "k_03i98l8", "k_03i9b76", "k_03i9ihe", "k_03i9qmf", "k_03ia8tl", "k_03iaiks", "k_03ianq2", "k_03ibg5h", "k_03ibj2i", "k_03icebg", "k_03ig9m1", "k_03ih30j", "k_03ih7p4", "k_03ihq2y", "k_03jbfdj", "k_03jbol1", "k_03jcqz8", "k_04e6gdv", "k_0520foa", "k_066stkq", "k_076kgm3", "k_0bh7ody", "k_0bp8b8u", "k_0by6a1n", "k_0bya3vq", "k_0bz6bjr", "k_0c6h5uj", "k_0c8r25e", "k_0c9j5xo", "k_0dtu2a6", "k_0eqxnsy", "k_0f7dbkw", "k_0f7g13k", "k_0f7haai", "k_0fbfutw", "k_0foh402", "k_0fv59jz", "k_0fw33u6", "k_0fz88bx", "k_0gsa28x", "k_0gxjx8y", "k_0i7zezh", "k_0kt4vkn", "k_0lpi4yf", "k_0lpk0ac", "k_0lqh56w", "k_0ml5pgp", "k_0ot1va2", "k_0oyzrvp", "k_0oz6tc6", "k_0qhczfd", "k_0ru3vyu", "k_0rwfaqi", "k_0tzrfcd", "k_0u61bk6", "k_0vl1km5", "k_0vuea5m", "k_0vx4c8u", "k_0w81vyr", "k_0wr1l4p", "k_0wry3ub", "k_0y8fvcp", "k_0yjmsh6", "k_0yjv1z1", "k_0yklp86", "k_0zszstl", "k_0zvckkz", "k_10fn5ti", "k_10hanur", "k_10rcmlz", "k_10rftyn", "k_11k4fu4", "k_11yo8ly", "k_1260def", "k_12cv7pq", "k_12dkdi9", "k_12dt9ms", "k_12wkh89", "k_132siea", "k_132z5dz", "k_1332qj7", "k_134jb2t", "k_137rrbz", "k_14w5k1n", "k_150zc86", "k_15ihqtd", "k_15ihyxa", "k_15kabpm", "k_15kx1ws", "k_15mt02y", "k_15ohzol", "k_15w8p2y", "k_15wcm81", "k_15wgku3", "k_1653ra8", "k_16v2xx3", "k_17aqykb", "k_17univl", "k_18frlt8", "k_18n2xxa", "k_19k8uzp", "k_19k8uzp_switch", "k_19khdlm", "k_19km6lh", "k_1cmnz4v", "k_1dfpb2r", "k_1fkd43e", "k_1gqmg84", "k_1icqfe0", "k_1iq4unz", "k_1k2t0sj", "k_1k4s2ly", "k_1kjins0", "k_1kwhdgw", "k_1lcuyce", "k_1m45y2g", "k_1mfs12e", "k_1mfu8nj", "k_1mguztd", "k_1mm1hd6", "k_1n4b1w3", "k_1snda0u", "k_1ss6n7b", "k_1tci5e5", "k_1w0r2ux", "k_1w749h6", "k_1w7avpb", "k_1yk18j2"]}, "jp": {"untranslated": ["k_002qcua", "k_002qjre", "k_002qrgn", "k_002rq6l", "k_002tu9r", "k_002uu5g", "k_002vfrj", "k_002vh2u", "k_003mgxt", "k_003mzsp", "k_003nevv", "k_003pnfo", "k_003rzap", "k_003tyud", "k_039ag85", "k_03b5ku9", "k_03enx2m", "k_07l47vm", "k_08oqvxi", "k_08ud61f", "k_08vxg3x", "k_0am4rt3", "k_0aof5hu", "k_0cl60bh", "k_0d0v50n", "k_0dttd66", "k_0dyjq0b", "k_0gz4ni4", "k_0h4eog8", "k_0iqe17e", "k_0lqtuon", "k_0ngrz6p", "k_0ohqnyg", "k_0olfaft", "k_0wxw5up", "k_0x11b3p", "k_0yt1rk9", "k_11fw1oj", "k_12209oh", "k_13qhcur", "k_15g70ie", "k_15giktr", "k_15oyv98", "k_166g259", "k_16iwm3r", "k_17bsyzp", "k_18go7ez", "k_190x241", "k_19k8xx9", "k_1asbjtx", "k_1fbo32k", "k_1ljoe0j", "k_1pkz2rt", "k_1t56f44", "k_1vwqlwv", "k_1xlly5e", "k_1ynfm2i"], "unused": ["k_0003jjb", "k_0003y9x", "k_002qhaa", "k_002r79h", "k_002rflt", "k_002v9zj", "k_002vxya", "k_002wzvs", "k_003hn48", "k_003j5tp", "k_003l8u0", "k_003mam4", "k_003n1v2", "k_003nabw", "k_003ngex", "k_003ntss", "k_003nzwy", "k_003pb7f", "k_003psvr", "k_003py1h", "k_003q07e", "k_003q276", "k_003qcl1", "k_003qdlq", "k_003qqzh", "k_003qytf", "k_003r4v2", "k_003rk1s", "k_003u02c", "k_02iirea", "k_02iq161", "k_02n863s", "k_02n8dgw", "k_036vcco", "k_036vddt", "k_0375n87", "k_0376nm2", "k_03aj50y", "k_03bdfew", "k_03bkewo", "k_03bzpgk", "k_03ce9tt", "k_03ckobz", "k_03dd1g6", "k_03efxyg", "k_03el9pa", "k_03eo46q", "k_03f4c34", "k_03f610l", "k_03fdfbs", "k_03fgn8g", "k_03fqp9o", "k_03gltpg", "k_03gm54s", "k_03i6tsc", "k_03i78mh", "k_03i7hu1", "k_03i7ok1", "k_03i98ch", "k_03i98l8", "k_03i9b76", "k_03i9ihe", "k_03i9qmf", "k_03ia8tl", "k_03iaiks", "k_03ianq2", "k_03ibg5h", "k_03ibj2i", "k_03icebg", "k_03ig9m1", "k_03ih30j", "k_03ih7p4", "k_03ihq2y", "k_03jbfdj", "k_03jbol1", "k_03jcqz8", "k_04e6gdv", "k_0520foa", "k_066stkq", "k_076kgm3", "k_0bh7ody", "k_0bp8b8u", "k_0by6a1n", "k_0bya3vq", "k_0bz6bjr", "k_0c6h5uj", "k_0c8r25e", "k_0c9j5xo", "k_0dtu2a6", "k_0eqxnsy", "k_0f7dbkw", "k_0f7g13k", "k_0f7haai", "k_0fbfutw", "k_0foh402", "k_0fv59jz", "k_0fw33u6", "k_0fz88bx", "k_0gsa28x", "k_0gxjx8y", "k_0i7zezh", "k_0kt4vkn", "k_0lpi4yf", "k_0lpk0ac", "k_0lqh56w", "k_0ml5pgp", "k_0ot1va2", "k_0oyzrvp", "k_0oz6tc6", "k_0qhczfd", "k_0ru3vyu", "k_0rwfaqi", "k_0tzrfcd", "k_0u61bk6", "k_0vl1km5", "k_0vuea5m", "k_0vx4c8u", "k_0w81vyr", "k_0wr1l4p", "k_0wry3ub", "k_0y8fvcp", "k_0yjmsh6", "k_0yjv1z1", "k_0yklp86", "k_0zszstl", "k_0zvckkz", "k_10fn5ti", "k_10hanur", "k_10rcmlz", "k_10rftyn", "k_11k4fu4", "k_11yo8ly", "k_1260def", "k_12cv7pq", "k_12dkdi9", "k_12dt9ms", "k_12wkh89", "k_132siea", "k_132z5dz", "k_1332qj7", "k_134jb2t", "k_137rrbz", "k_14w5k1n", "k_150zc86", "k_15ihqtd", "k_15ihyxa", "k_15kabpm", "k_15kx1ws", "k_15mt02y", "k_15ohzol", "k_15w8p2y", "k_15wcm81", "k_15wgku3", "k_1653ra8", "k_16v2xx3", "k_17aqykb", "k_17univl", "k_18frlt8", "k_18n2xxa", "k_19k8uzp", "k_19k8uzp_switch", "k_19khdlm", "k_19km6lh", "k_1cmnz4v", "k_1dfpb2r", "k_1fkd43e", "k_1gqmg84", "k_1icqfe0", "k_1iq4unz", "k_1k2t0sj", "k_1k4s2ly", "k_1kjins0", "k_1kwhdgw", "k_1lcuyce", "k_1m45y2g", "k_1mfs12e", "k_1mfu8nj", "k_1mguztd", "k_1mm1hd6", "k_1n4b1w3", "k_1snda0u", "k_1ss6n7b", "k_1tci5e5", "k_1w0r2ux", "k_1w749h6", "k_1w7avpb", "k_1yk18j2"]}, "ko": {"untranslated": ["k_002qcua", "k_002qjre", "k_002qrgn", "k_002rq6l", "k_002tu9r", "k_002uu5g", "k_002vfrj", "k_002vh2u", "k_003mgxt", "k_003mzsp", "k_003nevv", "k_003pnfo", "k_003rzap", "k_003tyud", "k_039ag85", "k_03b5ku9", "k_03enx2m", "k_07l47vm", "k_08oqvxi", "k_08ud61f", "k_08vxg3x", "k_0am4rt3", "k_0aof5hu", "k_0cl60bh", "k_0d0v50n", "k_0dttd66", "k_0dyjq0b", "k_0gz4ni4", "k_0h4eog8", "k_0iqe17e", "k_0lqtuon", "k_0ngrz6p", "k_0ohqnyg", "k_0olfaft", "k_0wxw5up", "k_0x11b3p", "k_0yt1rk9", "k_11fw1oj", "k_12209oh", "k_13qhcur", "k_15g70ie", "k_15giktr", "k_15oyv98", "k_166g259", "k_16iwm3r", "k_17bsyzp", "k_18go7ez", "k_190x241", "k_19k8xx9", "k_1asbjtx", "k_1fbo32k", "k_1ljoe0j", "k_1pkz2rt", "k_1t56f44", "k_1vwqlwv", "k_1xlly5e", "k_1ynfm2i"], "unused": ["k_0003jjb", "k_0003y9x", "k_002qhaa", "k_002r79h", "k_002rflt", "k_002v9zj", "k_002vxya", "k_002wzvs", "k_003hn48", "k_003j5tp", "k_003l8u0", "k_003mam4", "k_003n1v2", "k_003nabw", "k_003ngex", "k_003ntss", "k_003nzwy", "k_003pb7f", "k_003psvr", "k_003py1h", "k_003q07e", "k_003q276", "k_003qcl1", "k_003qdlq", "k_003qqzh", "k_003qytf", "k_003r4v2", "k_003rk1s", "k_003u02c", "k_02iirea", "k_02iq161", "k_02n863s", "k_02n8dgw", "k_036vcco", "k_036vddt", "k_0375n87", "k_0376nm2", "k_03aj50y", "k_03bdfew", "k_03bkewo", "k_03bzpgk", "k_03ce9tt", "k_03ckobz", "k_03dd1g6", "k_03efxyg", "k_03el9pa", "k_03eo46q", "k_03f4c34", "k_03f610l", "k_03fdfbs", "k_03fgn8g", "k_03fqp9o", "k_03gltpg", "k_03gm54s", "k_03i6tsc", "k_03i78mh", "k_03i7hu1", "k_03i7ok1", "k_03i98ch", "k_03i98l8", "k_03i9b76", "k_03i9ihe", "k_03i9qmf", "k_03ia8tl", "k_03iaiks", "k_03ianq2", "k_03ibg5h", "k_03ibj2i", "k_03icebg", "k_03ig9m1", "k_03ih30j", "k_03ih7p4", "k_03ihq2y", "k_03jbfdj", "k_03jbol1", "k_03jcqz8", "k_04e6gdv", "k_0520foa", "k_066stkq", "k_076kgm3", "k_0bh7ody", "k_0bp8b8u", "k_0by6a1n", "k_0bya3vq", "k_0bz6bjr", "k_0c6h5uj", "k_0c8r25e", "k_0c9j5xo", "k_0dtu2a6", "k_0eqxnsy", "k_0f7dbkw", "k_0f7g13k", "k_0f7haai", "k_0fbfutw", "k_0foh402", "k_0fv59jz", "k_0fw33u6", "k_0fz88bx", "k_0gsa28x", "k_0gxjx8y", "k_0i7zezh", "k_0kt4vkn", "k_0lpi4yf", "k_0lpk0ac", "k_0lqh56w", "k_0ml5pgp", "k_0ot1va2", "k_0oyzrvp", "k_0oz6tc6", "k_0qhczfd", "k_0ru3vyu", "k_0rwfaqi", "k_0tzrfcd", "k_0u61bk6", "k_0vl1km5", "k_0vuea5m", "k_0vx4c8u", "k_0w81vyr", "k_0wr1l4p", "k_0wry3ub", "k_0y8fvcp", "k_0yjmsh6", "k_0yjv1z1", "k_0yklp86", "k_0zszstl", "k_0zvckkz", "k_10fn5ti", "k_10hanur", "k_10rcmlz", "k_10rftyn", "k_11k4fu4", "k_11yo8ly", "k_1260def", "k_12cv7pq", "k_12dkdi9", "k_12dt9ms", "k_12wkh89", "k_132siea", "k_132z5dz", "k_1332qj7", "k_134jb2t", "k_137rrbz", "k_14w5k1n", "k_150zc86", "k_15ihqtd", "k_15ihyxa", "k_15kabpm", "k_15kx1ws", "k_15mt02y", "k_15ohzol", "k_15w8p2y", "k_15wcm81", "k_15wgku3", "k_1653ra8", "k_16v2xx3", "k_17aqykb", "k_17univl", "k_18frlt8", "k_18n2xxa", "k_19k8uzp", "k_19k8uzp_switch", "k_19khdlm", "k_19km6lh", "k_1cmnz4v", "k_1dfpb2r", "k_1fkd43e", "k_1gqmg84", "k_1icqfe0", "k_1iq4unz", "k_1k2t0sj", "k_1k4s2ly", "k_1kjins0", "k_1kwhdgw", "k_1lcuyce", "k_1m45y2g", "k_1mfs12e", "k_1mfu8nj", "k_1mguztd", "k_1mm1hd6", "k_1n4b1w3", "k_1snda0u", "k_1ss6n7b", "k_1tci5e5", "k_1w0r2ux", "k_1w749h6", "k_1w7avpb", "k_1yk18j2"]}}