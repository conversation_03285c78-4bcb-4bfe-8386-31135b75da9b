{"k_01hiidh": "After deleting the managed API key name: {{providerName}}, the bound stack will orchestrate cloud resources with TIC authorization by default.", "k_02i36uv": "Authorization configuration does not exist, please ", "k_0375iec": "All", "k_0375uzd": "New", "k_037waxo": "Clear", "k_039yc3y": "Ready", "k_03uu4qq": "Please enter secretKey", "k_03zh5v1": "Please enter description, Limit of 200 characters", "k_04c8id8": "Activate", "k_05z4f28": "TIC Authorization", "k_09pci7m": "Access Management", "k_0aq726q": "TIC Authorization documentation", "k_0bemtn5": "reauthorize", "k_0ckw01v": "Service authorization", "k_0coa49g": "Create Time", "k_0ebb6xc": "Operation", "k_0jeq8y1": "Active", "k_0jftlek": "Delete", "k_0ll2pn3": "TIC authorization has been enabled, it is recommended that you clean up the API key configuration as soon as possible.", "k_0npuqg3": "Cancel", "k_0p2wl97": "You are authorizing TIC service orchestration of CVM/VPC/COS and other Tencent Cloud resources. Please go to \"Access Management\" to complete the authorization.", "k_0p44u35": "Secret Key format error！secret key should be 21~250 characters", "k_0r54wt3": "Confirm", "k_0rrszaz": "After TIC authorization is enabled, activating API key configuration is not supported.", "k_0s1jgmb": "Description", "k_0u9xe7g": "Add Provider Successfully", "k_0wimefl": "Secret ID format error！secret id should be 21~250 characters", "k_0yisp9e": "Name", "k_0yj494p": "Edit", "k_0ysjezp": "API Credentials", "k_0z2ui0i": "Click the Clean button to delete all API key configurations managed on the TIC platform. The stack will orchestrate cloud resources with TIC authorization by default.", "k_0z75dco": "Please enter name, Limit of 60 characters", "k_0zei6kx": "Status", "k_12uxuap": "You are disabling the TIC Authorization. After disabling the authorization, you will not be able to orchestrate Tencent Cloud resources through the TIC service. Confirm the disabling?", "k_15ec786": "Authorize TIC to orchestrate CVM/VPC/COS and other Tencent Cloud resource permissions.", "k_15rpfem": "There are still stacks that are bound to TIC authorization and do not support disabling operations.", "k_1abdd2e": "After TIC authorization is enabled, adding API key configuration is not supported.", "k_1c1nbwd": "Authorization failed", "k_1c6wzy0": "Please enter secretId", "k_1f9f8ic": "Disable TIC Authorization", "k_1hbx8ab": "TIC authorization has been enabled", "k_1msp9ul": "Authorization succeeded", "k_1qo2uww": "Please release the resource first and then delete.", "k_1v1gi5s": "TIC authorization disabled", "k_1v7vgn7": "TIC authorization provides a safer and more efficient resource orchestration capability. It is recommended that you enable the TIC authorization function as soon as possible.", "k_1w8ljcl": "API Key Escrow", "k_1w9zqpj": "Modify Provider Config Successfully"}