/* eslint-disable */
/**
 * @fileoverview
 *
 * 本文件词条由 `tea scan` 命令扫描生成，请勿手动编辑
 *
 * 国际化方案，请参考文档 http://tapd.oa.com/tcp_access/markdown_wikis/0#1020399462008817031
 */

/**
 * @type {import('@tea/app').I18NTranslation}
 */
var translation = {
  "k_002qjre": "architecture", // fallback from en
  "k_003j5tp": "ignore", // fallback from en
  "k_003l8u0": "Name", // fallback from en
  "k_003m82u": "Config", // fallback from en
  "k_003ntss": "Attribute", // fallback from en
  "k_003nzwy": "Status", // fallback from en
  "k_003pnfo": "security", // fallback from en
  "k_003py1h": "Type", // fallback from en
  "k_003qdlq": "add", // fallback from en
  "k_003u02c": "Operation", // fallback from en
  "k_02i36uv": "Authorization configuration does not exist, please ",
  "k_037waxo": "Clear",
  "k_03fdfbs": "Availability zone", // fallback from en
  "k_05z4f28": "TIC Authorization",
  "k_09pci7m": "Access Management",
  "k_0aq726q": "TIC Authorization documentation",
  "k_0bemtn5": "reauthorize",
  "k_0ckw01v": "Service authorization",
  "k_0dtu2a6": "{{Message}}", // fallback from en
  "k_0eqxnsy": "Last assessment time", // fallback from en
  "k_0fr42gy": "Enter resource name or address search", // fallback from en
  "k_0fv59jz": "Optimization suggestions", // fallback from en
  "k_0fw33u6": "Resources list", // fallback from en
  "k_0npuqg3": "Cancel",
  "k_0p2wl97": "You are authorizing TIC service orchestration of CVM/VPC/COS and other Tencent Cloud resources. Please go to \"Access Management\" to complete the authorization.",
  "k_0r54wt3": "Confirm",
  "k_0vl1km5": "Enter the evaluation item name to search", // fallback from en
  "k_0wr1l4p": "Ignored resources list", // fallback from en
  "k_0wry3ub": "Assessment resources list", // fallback from en
  "k_0ysjezp": "API Credentials",
  "k_0z2ui0i": "Click the Clean button to delete all API key configurations managed on the TIC platform. The stack will orchestrate cloud resources with TIC authorization by default.",
  "k_0zei6kx": "Status",
  "k_12crr52": "Suggest measures", // fallback from en
  "k_12dt9ms": "Clusters", // fallback from en
  "k_12lcbwb": "Current user has not set API key, please go to", // fallback from en
  "k_12uxuap": "You are disabling the TIC Authorization. After disabling the authorization, you will not be able to orchestrate Tencent Cloud resources through the TIC service. Confirm the disabling?",
  "k_134jn8n": "Version", // fallback from en
  "k_13gkjut": "Start", // fallback from en
  "k_13gmbol": "Testing", // fallback from en
  "k_15ec786": "Authorize TIC to orchestrate CVM/VPC/COS and other Tencent Cloud resource permissions.",
  "k_15giktr": "resource", // fallback from en
  "k_15kx1ws": "IP Address", // fallback from en
  "k_15rpfem": "There are still stacks that are bound to TIC authorization and do not support disabling operations.",
  "k_17aqykb": "No content", // fallback from en
  "k_18n2xxa": "Warning condition", // fallback from en
  "k_190xgcd": "Assessment help document", // fallback from en
  "k_1c1nbwd": "Authorization failed",
  "k_1d5rgji": "Assessment", // fallback from en
  "k_1f9f8ic": "Disable TIC Authorization",
  "k_1fqqx3e": "overview", // fallback from en
  "k_1hbx8ab": "TIC authorization has been enabled",
  "k_1m4ae0d": "Inspection settings", // fallback from en
  "k_1msp9ul": "Authorization succeeded",
  "k_1ughqsg": "Set API key to use TIC function", // fallback from en
  "k_1v1gi5s": "TIC authorization disabled",
  "k_1v7vgn7": "TIC authorization provides a safer and more efficient resource orchestration capability. It is recommended that you enable the TIC authorization function as soon as possible.",
};

module.exports = { translation: translation };
