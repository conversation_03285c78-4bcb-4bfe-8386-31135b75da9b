/* eslint-disable */
/**
 * @fileoverview
 *
 * 本文件词条由 `tea scan` 命令扫描生成，请勿手动编辑
 *
 * 国际化方案，请参考文档 http://tapd.oa.com/tcp_access/markdown_wikis/0#1020399462008817031
 */

/**
 * @type {import('@tea/app').I18NTranslation}
 */
var translation = {
  "k_002qjre": "Reliability",
  "k_002tu9r": "Performance",
  "k_002vh2u": "Overview",
  "k_003mzsp": "Cost",
  "k_003nevv": "Cancel",
  "k_003pnfo": "Security",
  "k_003rzap": "Confirm",
  "k_07l47vm": "TencentDB for MySQL",
  "k_08ud61f": "CBS",
  "k_08vxg3x": "CLB",
  "k_0dttd66": "{{message}}",
  "k_0dyjq0b": "CDN",
  "k_0gz4ni4": "unknown error",
  "k_0iqe17e": "TencentDB for MongoDB",
  "k_0x11b3p": "Assessment Item",
  "k_0yt1rk9": "CVM",
  "k_15giktr": "Service limits",
  "k_15oyv98": "Download report",
  "k_190x241": "TencentDB for Redis",
  "k_1ljoe0j": "COS",
  "k_1pkz2rt": "Search by resource keyword",
  "k_1xlly5e": "EIP",
};

module.exports = { translation: translation };
