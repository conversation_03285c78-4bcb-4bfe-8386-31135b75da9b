const path = require("path");
module.exports = {
	rootDir: path.resolve(__dirname),
	transform: {
		"^.+\\.[jt]sx?$": "ts-jest",
	},
	moduleFileExtensions: ["js", "json", "jsx", "ts", "tsx", "node"],
	setupFilesAfterEnv: ["jest-extended"],
	preset: "ts-jest",
	collectCoverage: false,
	collectCoverageFrom: [
		"src/**/*.{js,jsx,ts,tsx}",
		"!**/fixtures/**",
		"!**/routes/**",
		"!**/examples/**",
		"!**/locales/**",
		"!**/typings/**",
		"!**/__snapshots__/**",
		"!**/types/**",
	],
	testPathIgnorePatterns: ["node_modules", "examples", "fixtures"],
	testRegex: "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$",
	moduleNameMapper: {
		"^@tea/app$": "<rootDir>/node_modules/@tencent/tea-app",
		"^@tea/app/(.*)$": "<rootDir>/node_modules/@tencent/tea-app/lib/$1",
		"^@tea/component$": "<rootDir>/node_modules/@tencent/tea-component/lib",
		"^@tea/component/(.*)$":
			"<rootDir>/node_modules/@tencent/tea-component/lib/$1",
		"^@src/(.*)$": "<rootDir>/src/$1",
	},
};
