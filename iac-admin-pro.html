<!DOCTYPE html>

<html>
  <head>
    <title>Protocol Documentation</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Ubuntu:400,700,400italic"/>
    <style>
      body {
        width: 60em;
        margin: 1em auto;
        color: #222;
        font-family: "Ubuntu", sans-serif;
        padding-bottom: 4em;
      }

      h1 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      h2 {
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
        margin: 1.5em 0;
      }

      h3 {
        font-weight: normal;
        border-bottom: 1px solid #aaa;
        padding-bottom: 0.5ex;
      }

      a {
        text-decoration: none;
        color: #567e25;
      }

      table {
        width: 100%;
        font-size: 80%;
        border-collapse: collapse;
      }

      thead {
        font-weight: 700;
        background-color: #dcdcdc;
      }

      tbody tr:nth-child(even) {
        background-color: #fbfbfb;
      }

      td {
        border: 1px solid #ccc;
        padding: 0.5ex 2ex;
      }

      td p {
        text-indent: 1em;
        margin: 0;
      }

      td p:nth-child(1) {
        text-indent: 0;  
      }

       
      .field-table td:nth-child(1) {  
        width: 10em;
      }
      .field-table td:nth-child(2) {  
        width: 10em;
      }
      .field-table td:nth-child(3) {  
        width: 6em;
      }
      .field-table td:nth-child(4) {  
        width: auto;
      }

       
      .extension-table td:nth-child(1) {  
        width: 10em;
      }
      .extension-table td:nth-child(2) {  
        width: 10em;
      }
      .extension-table td:nth-child(3) {  
        width: 10em;
      }
      .extension-table td:nth-child(4) {  
        width: 5em;
      }
      .extension-table td:nth-child(5) {  
        width: auto;
      }

       
      .enum-table td:nth-child(1) {  
        width: 10em;
      }
      .enum-table td:nth-child(2) {  
        width: 10em;
      }
      .enum-table td:nth-child(3) {  
        width: auto;
      }

       
      .scalar-value-types-table tr {
        height: 3em;
      }

       
      #toc-container ul {
        list-style-type: none;
        padding-left: 1em;
        line-height: 180%;
        margin: 0;
      }
      #toc > li > a {
        font-weight: bold;
      }

       
      .file-heading {
        width: 100%;
        display: table;
        border-bottom: 1px solid #aaa;
        margin: 4em 0 1.5em 0;
      }
      .file-heading h2 {
        border: none;
        display: table-cell;
      }
      .file-heading a {
        text-align: right;
        display: table-cell;
      }

       
      .badge {
        width: 1.6em;
        height: 1.6em;
        display: inline-block;

        line-height: 1.6em;
        text-align: center;
        font-weight: bold;
        font-size: 60%;

        color: #89ba48;
        background-color: #dff0c8;

        margin: 0.5ex 1em 0.5ex -1em;
        border: 1px solid #fbfbfb;
        border-radius: 1ex;
      }
    </style>

    
    <link rel="stylesheet" type="text/css" href="stylesheet.css"/>
  </head>

  <body>

    <h1 id="title">Protocol Documentation</h1>

    <h2>Table of Contents</h2>

    <div id="toc-container">
      <ul id="toc">
        
          
          <li>
            <a href="#api%2fAdvisorConfig.proto">api/AdvisorConfig.proto</a>
            <ul>
              
                <li>
                  <a href="#api.AddStrategyConfigRequest"><span class="badge">M</span>AddStrategyConfigRequest</a>
                </li>
              
                <li>
                  <a href="#api.AddStrategyConfigResponse"><span class="badge">M</span>AddStrategyConfigResponse</a>
                </li>
              
                <li>
                  <a href="#api.Condition"><span class="badge">M</span>Condition</a>
                </li>
              
                <li>
                  <a href="#api.DeleteStrategyConfigRequest"><span class="badge">M</span>DeleteStrategyConfigRequest</a>
                </li>
              
                <li>
                  <a href="#api.DeleteStrategyConfigResponse"><span class="badge">M</span>DeleteStrategyConfigResponse</a>
                </li>
              
                <li>
                  <a href="#api.DescribeStrategyConfigsRequest"><span class="badge">M</span>DescribeStrategyConfigsRequest</a>
                </li>
              
                <li>
                  <a href="#api.DescribeStrategyConfigsResponse"><span class="badge">M</span>DescribeStrategyConfigsResponse</a>
                </li>
              
                <li>
                  <a href="#api.ModifyStrategyConfigRequest"><span class="badge">M</span>ModifyStrategyConfigRequest</a>
                </li>
              
                <li>
                  <a href="#api.ModifyStrategyConfigResponse"><span class="badge">M</span>ModifyStrategyConfigResponse</a>
                </li>
              
                <li>
                  <a href="#api.StrategyConfig"><span class="badge">M</span>StrategyConfig</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.AdvisorStrategyConfigService"><span class="badge">S</span>AdvisorStrategyConfigService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fAdvisorCustomerOverview.proto">api/AdvisorCustomerOverview.proto</a>
            <ul>
              
                <li>
                  <a href="#api.Customer"><span class="badge">M</span>Customer</a>
                </li>
              
                <li>
                  <a href="#api.ListCustomerRequest"><span class="badge">M</span>ListCustomerRequest</a>
                </li>
              
                <li>
                  <a href="#api.ListCustomerResponse"><span class="badge">M</span>ListCustomerResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.AdvisorCustomerOverviewService"><span class="badge">S</span>AdvisorCustomerOverviewService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fAdvisorData.proto">api/AdvisorData.proto</a>
            <ul>
              
                <li>
                  <a href="#api.DescribeIgnoredDetailRequest"><span class="badge">M</span>DescribeIgnoredDetailRequest</a>
                </li>
              
                <li>
                  <a href="#api.DescribeIgnoredDetailResponse"><span class="badge">M</span>DescribeIgnoredDetailResponse</a>
                </li>
              
                <li>
                  <a href="#api.DescribeTaskDataRequest"><span class="badge">M</span>DescribeTaskDataRequest</a>
                </li>
              
                <li>
                  <a href="#api.DescribeTaskDataResponse"><span class="badge">M</span>DescribeTaskDataResponse</a>
                </li>
              
                <li>
                  <a href="#api.DescribeUnsafeDetailRequest"><span class="badge">M</span>DescribeUnsafeDetailRequest</a>
                </li>
              
                <li>
                  <a href="#api.DescribeUnsafeDetailResponse"><span class="badge">M</span>DescribeUnsafeDetailResponse</a>
                </li>
              
                <li>
                  <a href="#api.GroupInfo"><span class="badge">M</span>GroupInfo</a>
                </li>
              
                <li>
                  <a href="#api.IgnoredDetail"><span class="badge">M</span>IgnoredDetail</a>
                </li>
              
                <li>
                  <a href="#api.InstanceData"><span class="badge">M</span>InstanceData</a>
                </li>
              
                <li>
                  <a href="#api.LevelInfo"><span class="badge">M</span>LevelInfo</a>
                </li>
              
                <li>
                  <a href="#api.StrategyData"><span class="badge">M</span>StrategyData</a>
                </li>
              
                <li>
                  <a href="#api.UnsafeDetail"><span class="badge">M</span>UnsafeDetail</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.AdvisorTaskDataService"><span class="badge">S</span>AdvisorTaskDataService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fAdvisorMarketMonitoring.proto">api/AdvisorMarketMonitoring.proto</a>
            <ul>
              
                <li>
                  <a href="#api.ListMarketMonitoringRequest"><span class="badge">M</span>ListMarketMonitoringRequest</a>
                </li>
              
                <li>
                  <a href="#api.ListResourceResponse"><span class="badge">M</span>ListResourceResponse</a>
                </li>
              
                <li>
                  <a href="#api.ResourceAssessmentTotal"><span class="badge">M</span>ResourceAssessmentTotal</a>
                </li>
              
                <li>
                  <a href="#api.StrategyAssessmentTotal"><span class="badge">M</span>StrategyAssessmentTotal</a>
                </li>
              
                <li>
                  <a href="#api.UsersCount"><span class="badge">M</span>UsersCount</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.AdvisorMarketMonitoringService"><span class="badge">S</span>AdvisorMarketMonitoringService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fTicCustomerOvervies.proto">api/TicCustomerOvervies.proto</a>
            <ul>
              
                <li>
                  <a href="#api.ListTicCustomerRequest"><span class="badge">M</span>ListTicCustomerRequest</a>
                </li>
              
                <li>
                  <a href="#api.ListTicCustomerResponse"><span class="badge">M</span>ListTicCustomerResponse</a>
                </li>
              
                <li>
                  <a href="#api.TicCustomer"><span class="badge">M</span>TicCustomer</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.TicCustomerOverviewService"><span class="badge">S</span>TicCustomerOverviewService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fTicMarketMonitoring.proto">api/TicMarketMonitoring.proto</a>
            <ul>
              
                <li>
                  <a href="#api.ListMonitoringResponse"><span class="badge">M</span>ListMonitoringResponse</a>
                </li>
              
                <li>
                  <a href="#api.ListTicMarketMonitoringRequest"><span class="badge">M</span>ListTicMarketMonitoringRequest</a>
                </li>
              
                <li>
                  <a href="#api.ResourceCount"><span class="badge">M</span>ResourceCount</a>
                </li>
              
                <li>
                  <a href="#api.ResourceStackCount"><span class="badge">M</span>ResourceStackCount</a>
                </li>
              
                <li>
                  <a href="#api.TicUsersCount"><span class="badge">M</span>TicUsersCount</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.TicMarketMonitoringService"><span class="badge">S</span>TicMarketMonitoringService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fTicStack.proto">api/TicStack.proto</a>
            <ul>
              
                <li>
                  <a href="#api.GetStackVersionRequest"><span class="badge">M</span>GetStackVersionRequest</a>
                </li>
              
                <li>
                  <a href="#api.GetVersionEventRequest"><span class="badge">M</span>GetVersionEventRequest</a>
                </li>
              
                <li>
                  <a href="#api.ListResourceRequest"><span class="badge">M</span>ListResourceRequest</a>
                </li>
              
                <li>
                  <a href="#api.ListStackResourceResponse"><span class="badge">M</span>ListStackResourceResponse</a>
                </li>
              
                <li>
                  <a href="#api.ListStackVersionRequest"><span class="badge">M</span>ListStackVersionRequest</a>
                </li>
              
                <li>
                  <a href="#api.ListStackVersionResponse"><span class="badge">M</span>ListStackVersionResponse</a>
                </li>
              
                <li>
                  <a href="#api.ListVersionEventRequest"><span class="badge">M</span>ListVersionEventRequest</a>
                </li>
              
                <li>
                  <a href="#api.ListVersionEventResponse"><span class="badge">M</span>ListVersionEventResponse</a>
                </li>
              
                <li>
                  <a href="#api.Resource"><span class="badge">M</span>Resource</a>
                </li>
              
                <li>
                  <a href="#api.ResourceStack"><span class="badge">M</span>ResourceStack</a>
                </li>
              
                <li>
                  <a href="#api.SearchResourceStackRequest"><span class="badge">M</span>SearchResourceStackRequest</a>
                </li>
              
                <li>
                  <a href="#api.SearchResourceStackResponse"><span class="badge">M</span>SearchResourceStackResponse</a>
                </li>
              
                <li>
                  <a href="#api.StackVersion"><span class="badge">M</span>StackVersion</a>
                </li>
              
                <li>
                  <a href="#api.VersionEvent"><span class="badge">M</span>VersionEvent</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.ResourceStackService"><span class="badge">S</span>ResourceStackService</a>
                </li>
              
                <li>
                  <a href="#api.StackVersionService"><span class="badge">S</span>StackVersionService</a>
                </li>
              
                <li>
                  <a href="#api.VersionEventService"><span class="badge">S</span>VersionEventService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fTicTemplate.proto">api/TicTemplate.proto</a>
            <ul>
              
                <li>
                  <a href="#api.CreatePublicTemplateRequest"><span class="badge">M</span>CreatePublicTemplateRequest</a>
                </li>
              
                <li>
                  <a href="#api.CreatePublicTemplateResponse"><span class="badge">M</span>CreatePublicTemplateResponse</a>
                </li>
              
                <li>
                  <a href="#api.DeletePublicTemplateRequest"><span class="badge">M</span>DeletePublicTemplateRequest</a>
                </li>
              
                <li>
                  <a href="#api.DeletePublicTemplateResponse"><span class="badge">M</span>DeletePublicTemplateResponse</a>
                </li>
              
                <li>
                  <a href="#api.DescribePrivateTemplateRequest"><span class="badge">M</span>DescribePrivateTemplateRequest</a>
                </li>
              
                <li>
                  <a href="#api.DescribePrivateTemplateResponse"><span class="badge">M</span>DescribePrivateTemplateResponse</a>
                </li>
              
                <li>
                  <a href="#api.DescribePublicTemplateRequest"><span class="badge">M</span>DescribePublicTemplateRequest</a>
                </li>
              
                <li>
                  <a href="#api.DescribePublicTemplateResponse"><span class="badge">M</span>DescribePublicTemplateResponse</a>
                </li>
              
                <li>
                  <a href="#api.ModifyPublicTemplateRequest"><span class="badge">M</span>ModifyPublicTemplateRequest</a>
                </li>
              
                <li>
                  <a href="#api.ModifyPublicTemplateResponse"><span class="badge">M</span>ModifyPublicTemplateResponse</a>
                </li>
              
                <li>
                  <a href="#api.PrivateTemplateDetail"><span class="badge">M</span>PrivateTemplateDetail</a>
                </li>
              
                <li>
                  <a href="#api.PrivateTemplateInfo"><span class="badge">M</span>PrivateTemplateInfo</a>
                </li>
              
                <li>
                  <a href="#api.PrivateTemplateListRequest"><span class="badge">M</span>PrivateTemplateListRequest</a>
                </li>
              
                <li>
                  <a href="#api.PrivateTemplateListResponse"><span class="badge">M</span>PrivateTemplateListResponse</a>
                </li>
              
                <li>
                  <a href="#api.PublicTemplateDetail"><span class="badge">M</span>PublicTemplateDetail</a>
                </li>
              
                <li>
                  <a href="#api.PublicTemplateInfo"><span class="badge">M</span>PublicTemplateInfo</a>
                </li>
              
                <li>
                  <a href="#api.PublicTemplateListRequest"><span class="badge">M</span>PublicTemplateListRequest</a>
                </li>
              
                <li>
                  <a href="#api.PublicTemplateListResponse"><span class="badge">M</span>PublicTemplateListResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.TicTemplateService"><span class="badge">S</span>TicTemplateService</a>
                </li>
              
            </ul>
          </li>
        
          
          <li>
            <a href="#api%2fTicUser.proto">api/TicUser.proto</a>
            <ul>
              
                <li>
                  <a href="#api.UserInfo"><span class="badge">M</span>UserInfo</a>
                </li>
              
                <li>
                  <a href="#api.UserListRequest"><span class="badge">M</span>UserListRequest</a>
                </li>
              
                <li>
                  <a href="#api.UserListResponse"><span class="badge">M</span>UserListResponse</a>
                </li>
              
              
              
              
                <li>
                  <a href="#api.TicUserService"><span class="badge">S</span>TicUserService</a>
                </li>
              
            </ul>
          </li>
        
        <li><a href="#scalar-value-types">Scalar Value Types</a></li>
      </ul>
    </div>

    
      
      <div class="file-heading">
        <h2 id="api/AdvisorConfig.proto">api/AdvisorConfig.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="api.AddStrategyConfigRequest">AddStrategyConfigRequest</h3>
        <p>增加策略接口的请求体.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>StrategyConfigList</td>
                  <td><a href="#api.StrategyConfig">StrategyConfig</a></td>
                  <td>repeated</td>
                  <td><p>修改的具体策略配置详情 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.AddStrategyConfigResponse">AddStrategyConfigResponse</h3>
        <p>增加的返回.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>result</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否成功 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.Condition">Condition</h3>
        <p>策略的风险判别</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ConditionId</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>条件ID </p></td>
                </tr>
              
                <tr>
                  <td>Level</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>风险等级 </p></td>
                </tr>
              
                <tr>
                  <td>Desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>风险条件 eg：证书7天内到期。 </p></td>
                </tr>
              
                <tr>
                  <td>Operation</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>是否新增 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DeleteStrategyConfigRequest">DeleteStrategyConfigRequest</h3>
        <p>删除策略接口的请求体.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>StrategyID</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>策略ID， </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DeleteStrategyConfigResponse">DeleteStrategyConfigResponse</h3>
        <p>删除的返回.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>result</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否成功 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribeStrategyConfigsRequest">DescribeStrategyConfigsRequest</h3>
        <p>过滤字段，请求体为空则查询全部.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Group</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>以组别纬度进行过滤 </p></td>
                </tr>
              
                <tr>
                  <td>Product</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>以产品纬度进行过滤 </p></td>
                </tr>
              
                <tr>
                  <td>StrategyID</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>策略ID， </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribeStrategyConfigsResponse">DescribeStrategyConfigsResponse</h3>
        <p>配置详情.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>StrategyConfigList</td>
                  <td><a href="#api.StrategyConfig">StrategyConfig</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>RequestID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>请求id </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ModifyStrategyConfigRequest">ModifyStrategyConfigRequest</h3>
        <p>修改策略</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Config</td>
                  <td><a href="#api.StrategyConfig">StrategyConfig</a></td>
                  <td></td>
                  <td><p>修改的具体策略配置详情 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ModifyStrategyConfigResponse">ModifyStrategyConfigResponse</h3>
        <p>修改的返回.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>result</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否成功 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.StrategyConfig">StrategyConfig</h3>
        <p>策略配置详情</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>GroupId</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>所属组 </p></td>
                </tr>
              
                <tr>
                  <td>Product</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>产品名  eg：&#34;cvm&#34; </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>策略名  eg：云数据库（MySQL）Root 账号安全 </p></td>
                </tr>
              
                <tr>
                  <td>Desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg：检查云数据库（MySQL）账号配置，若只存在 root 账号，则会告警。 </p></td>
                </tr>
              
                <tr>
                  <td>Online</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否上线，0不上线，1上线 </p></td>
                </tr>
              
                <tr>
                  <td>Repair</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg：登录 CDN 控制台，参考[变更证书](https://cloud.tencent.com/document/product/228/41687#.E5.8F.98.E6.9B.B4.E8.AF.81.E4.B9.A6)文档，更新证书内容。 </p></td>
                </tr>
              
                <tr>
                  <td>Notice</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg：%d个 CVM 实例存在公网访问高危端口。 </p></td>
                </tr>
              
                <tr>
                  <td>Ignore</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg: %d个实例被忽略。 </p></td>
                </tr>
              
                <tr>
                  <td>StrategyId</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>策略ID， </p></td>
                </tr>
              
                <tr>
                  <td>Conditions</td>
                  <td><a href="#api.Condition">Condition</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.AdvisorStrategyConfigService">AdvisorStrategyConfigService</h3>
        <p>对策略的增删改查</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>ModifyStrategyConfig</td>
                <td><a href="#api.ModifyStrategyConfigRequest">ModifyStrategyConfigRequest</a></td>
                <td><a href="#api.ModifyStrategyConfigResponse">ModifyStrategyConfigResponse</a></td>
                <td><p>修改策略</p></td>
              </tr>
            
              <tr>
                <td>AddStrategyConfig</td>
                <td><a href="#api.AddStrategyConfigRequest">AddStrategyConfigRequest</a></td>
                <td><a href="#api.AddStrategyConfigResponse">AddStrategyConfigResponse</a></td>
                <td><p>增加策略</p></td>
              </tr>
            
              <tr>
                <td>DeleteStrategyConfig</td>
                <td><a href="#api.DeleteStrategyConfigRequest">DeleteStrategyConfigRequest</a></td>
                <td><a href="#api.DeleteStrategyConfigResponse">DeleteStrategyConfigResponse</a></td>
                <td><p>删除策略</p></td>
              </tr>
            
              <tr>
                <td>DescribeStrategyConfigs</td>
                <td><a href="#api.DescribeStrategyConfigsRequest">DescribeStrategyConfigsRequest</a></td>
                <td><a href="#api.DescribeStrategyConfigsResponse">DescribeStrategyConfigsResponse</a></td>
                <td><p>查询策略</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>ModifyStrategyConfig</td>
                <td>POST</td>
                <td>/v1/advisor_db/strategy</td>
                <td>*</td>
              </tr>
              
            
              
              
              <tr>
                <td>AddStrategyConfig</td>
                <td>PUT</td>
                <td>/v1/advisor_db/strategy</td>
                <td>*</td>
              </tr>
              
            
              
              
              <tr>
                <td>DeleteStrategyConfig</td>
                <td>DELETE</td>
                <td>/v1/advisor_db/strategy</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>DescribeStrategyConfigs</td>
                <td>GET</td>
                <td>/v1/advisor_db/strategy</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/AdvisorCustomerOverview.proto">api/AdvisorCustomerOverview.proto</h2><a href="#title">Top</a>
      </div>
      <p>Copyright (c) 2020. Tencent Technologies, Inc.</p><p>Permission is hereby granted, free of charge, to any person obtaining a copy</p><p>of this software and associated documentation files (the "Software"), to deal</p><p>in the Software without restriction, including without limitation the rights</p><p>to use, copy, modify, merge, publish, distribute, sublicense, and/or sell</p><p>copies of the Software, and to permit persons to whom the Software is</p><p>furnished to do so, subject to the following conditions:</p><p>The above copyright notice and this permission notice shall be included in</p><p>all copies or substantial portions of the Software.</p><p>Author: pulse-line-dev (<EMAIL>)</p>

      
        <h3 id="api.Customer">Customer</h3>
        <p>客户总览信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Uin</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>主账号 UIN </p></td>
                </tr>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>AppID </p></td>
                </tr>
              
                <tr>
                  <td>CustomerName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>客户简称 </p></td>
                </tr>
              
                <tr>
                  <td>Region</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>所属地域 </p></td>
                </tr>
              
                <tr>
                  <td>TotalScore</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>总评分 </p></td>
                </tr>
              
                <tr>
                  <td>HighRiskCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>高风险数量 </p></td>
                </tr>
              
                <tr>
                  <td>MediumRiskCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>中风险数量 </p></td>
                </tr>
              
                <tr>
                  <td>LowRiskCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>低风险数量 </p></td>
                </tr>
              
                <tr>
                  <td>OpeningTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>开通时间 </p></td>
                </tr>
              
                <tr>
                  <td>LastEvaluationTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>上次评估时间 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListCustomerRequest">ListCustomerRequest</h3>
        <p>客户总览Request</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>SearchWord</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>SearchWord 代表Uin,AppID,CustomerName,Region </p></td>
                </tr>
              
                <tr>
                  <td>OrderBy</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>排序字段，值&#43;字段表示生序，值-字段表示降序 </p></td>
                </tr>
              
                <tr>
                  <td>regions</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>过滤的地区列表 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>分页大小 </p></td>
                </tr>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移，从多少条记录之后开始 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListCustomerResponse">ListCustomerResponse</h3>
        <p>客户总览Response</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Total</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>总条数 </p></td>
                </tr>
              
                <tr>
                  <td>CustomerList</td>
                  <td><a href="#api.Customer">Customer</a></td>
                  <td>repeated</td>
                  <td><p>客户总览列表 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.AdvisorCustomerOverviewService">AdvisorCustomerOverviewService</h3>
        <p>Advisor客户总览服务</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>ListCustomerInfos</td>
                <td><a href="#api.ListCustomerRequest">ListCustomerRequest</a></td>
                <td><a href="#api.ListCustomerResponse">ListCustomerResponse</a></td>
                <td><p>客户总览查询详情</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>ListCustomerInfos</td>
                <td>GET</td>
                <td>/v1/advisor/monitor/customer_info</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/AdvisorData.proto">api/AdvisorData.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="api.DescribeIgnoredDetailRequest">DescribeIgnoredDetailRequest</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TaskID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>任务Id </p></td>
                </tr>
              
                <tr>
                  <td>StrategyID</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>策略Id </p></td>
                </tr>
              
                <tr>
                  <td>Filter</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>过滤条件 </p></td>
                </tr>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移量，默认为0 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>返回数量，默认为20，最大值为100 </p></td>
                </tr>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>appid </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribeIgnoredDetailResponse">DescribeIgnoredDetailResponse</h3>
        <p>Describe Ignored Detail Response</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TotalCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>返回的实例数量 </p></td>
                </tr>
              
                <tr>
                  <td>IgnoredDetails</td>
                  <td><a href="#api.IgnoredDetail">IgnoredDetail</a></td>
                  <td>repeated</td>
                  <td><p>该策略下被忽略的实例列表 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribeTaskDataRequest">DescribeTaskDataRequest</h3>
        <p>页面数据请求体.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Uin</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>TaskID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribeTaskDataResponse">DescribeTaskDataResponse</h3>
        <p>页面数据.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TaskID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>LastScanTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>执行时间 </p></td>
                </tr>
              
                <tr>
                  <td>LevelInfoList</td>
                  <td><a href="#api.LevelInfo">LevelInfo</a></td>
                  <td>repeated</td>
                  <td><p>各个组别的数据 </p></td>
                </tr>
              
                <tr>
                  <td>StrategyDataList</td>
                  <td><a href="#api.StrategyData">StrategyData</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribeUnsafeDetailRequest">DescribeUnsafeDetailRequest</h3>
        <p>Describe Unsafe Detail Request</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TaskID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>任务Id </p></td>
                </tr>
              
                <tr>
                  <td>StrategyID</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>策略Id </p></td>
                </tr>
              
                <tr>
                  <td>Filter</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>过滤条件 </p></td>
                </tr>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移量，默认为0 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>返回数量，默认为20，最大值为100 </p></td>
                </tr>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>appid </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribeUnsafeDetailResponse">DescribeUnsafeDetailResponse</h3>
        <p>Describe Unsafe Detail Response</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TotalCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>返回的实例数量 </p></td>
                </tr>
              
                <tr>
                  <td>UnsafeDetails</td>
                  <td><a href="#api.UnsafeDetail">UnsafeDetail</a></td>
                  <td>repeated</td>
                  <td><p>该策略下风险实例列表 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.GroupInfo">GroupInfo</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>High</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>Middle</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>Low</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.IgnoredDetail">IgnoredDetail</h3>
        <p>该策略下被忽略的实例</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>IgnoredStatus</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>实例的忽略状态，true表示被忽略。 </p></td>
                </tr>
              
                <tr>
                  <td>Instance</td>
                  <td><a href="#api.InstanceData">InstanceData</a></td>
                  <td></td>
                  <td><p>实例信息。 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.InstanceData">InstanceData</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>Region</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>Extra</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.LevelInfo">LevelInfo</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>GroupID</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>Info</td>
                  <td><a href="#api.GroupInfo">GroupInfo</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.StrategyData">StrategyData</h3>
        <p>每个策略的详情</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg: 对象存储（COS）未设置子账号访问 </p></td>
                </tr>
              
                <tr>
                  <td>Desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg: 检查对象存储（COS）存储桶账号配置，若未设置子账号访问权限，则会告警。 </p></td>
                </tr>
              
                <tr>
                  <td>Notice</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg: %d个存储桶未设置子账号访问。 </p></td>
                </tr>
              
                <tr>
                  <td>Ignore</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>eg: %d个存储桶被忽略。 </p></td>
                </tr>
              
                <tr>
                  <td>NoticeResult</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>eg:	30 </p></td>
                </tr>
              
                <tr>
                  <td>IgnoreResult</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>eg: 	10 </p></td>
                </tr>
              
                <tr>
                  <td>Level</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>安全等级 </p></td>
                </tr>
              
                <tr>
                  <td>GroupID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>属于哪个组 </p></td>
                </tr>
              
                <tr>
                  <td>GroupName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>组名称：安全 		     eg: 安全 </p></td>
                </tr>
              
                <tr>
                  <td>StrategyID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>策略id </p></td>
                </tr>
              
                <tr>
                  <td>Product</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>产品名称 			     eg: CVM </p></td>
                </tr>
              
                <tr>
                  <td>LevelCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td>repeated</td>
                  <td><p>实例列表风险等级数量，高-&gt;低 </p></td>
                </tr>
              
                <tr>
                  <td>Repair</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>使用&#34;COS阻止公共访问&#34;来控制,优化建议 </p></td>
                </tr>
              
                <tr>
                  <td>Conditions</td>
                  <td><a href="#api.Condition">Condition</a></td>
                  <td>repeated</td>
                  <td><p>策略的风险判别 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.UnsafeDetail">UnsafeDetail</h3>
        <p>该策略下风险实例</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ConditionId</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>命中的条件ID </p></td>
                </tr>
              
                <tr>
                  <td>IgnoredStatus</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>实例的忽略状态，true表示被忽略。 </p></td>
                </tr>
              
                <tr>
                  <td>Instance</td>
                  <td><a href="#api.InstanceData">InstanceData</a></td>
                  <td></td>
                  <td><p>实例信息。 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.AdvisorTaskDataService">AdvisorTaskDataService</h3>
        <p>获取advisor任务数据</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>DescribeTaskData</td>
                <td><a href="#api.DescribeTaskDataRequest">DescribeTaskDataRequest</a></td>
                <td><a href="#api.DescribeTaskDataResponse">DescribeTaskDataResponse</a></td>
                <td><p>获取advisor任务数据</p></td>
              </tr>
            
              <tr>
                <td>DescribeUnsafeDetail</td>
                <td><a href="#api.DescribeUnsafeDetailRequest">DescribeUnsafeDetailRequest</a></td>
                <td><a href="#api.DescribeUnsafeDetailResponse">DescribeUnsafeDetailResponse</a></td>
                <td><p></p></td>
              </tr>
            
              <tr>
                <td>DescribeIgnoredDetail</td>
                <td><a href="#api.DescribeIgnoredDetailRequest">DescribeIgnoredDetailRequest</a></td>
                <td><a href="#api.DescribeIgnoredDetailResponse">DescribeIgnoredDetailResponse</a></td>
                <td><p></p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>DescribeTaskData</td>
                <td>GET</td>
                <td>/v1/advisor_db/task_data</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>DescribeUnsafeDetail</td>
                <td>GET</td>
                <td>/v1/advisor_db/unsafe_detail</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>DescribeIgnoredDetail</td>
                <td>GET</td>
                <td>/v1/advisor_db/ignored_detail</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/AdvisorMarketMonitoring.proto">api/AdvisorMarketMonitoring.proto</h2><a href="#title">Top</a>
      </div>
      <p>Copyright (c) 2020. Tencent Technologies, Inc.</p><p>Permission is hereby granted, free of charge, to any person obtaining a copy</p><p>of this software and associated documentation files (the "Software"), to deal</p><p>in the Software without restriction, including without limitation the rights</p><p>to use, copy, modify, merge, publish, distribute, sublicense, and/or sell</p><p>copies of the Software, and to permit persons to whom the Software is</p><p>furnished to do so, subject to the following conditions:</p><p>The above copyright notice and this permission notice shall be included in</p><p>all copies or substantial portions of the Software.</p><p>Author: pulse-line-dev (<EMAIL>)</p>

      
        <h3 id="api.ListMarketMonitoringRequest">ListMarketMonitoringRequest</h3>
        <p>大盘监控Request</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>StartTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>查询开始时间，日期格式：&#34;YYYY-MM-DD&#34; </p></td>
                </tr>
              
                <tr>
                  <td>EndTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>查询结束时间，日期格式：&#34;YYYY-MM-DD&#34; </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListResourceResponse">ListResourceResponse</h3>
        <p>大盘监控Response</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ResourceTotalList</td>
                  <td><a href="#api.ResourceAssessmentTotal">ResourceAssessmentTotal</a></td>
                  <td>repeated</td>
                  <td><p>资源评估总览列表 </p></td>
                </tr>
              
                <tr>
                  <td>StrategyTotalList</td>
                  <td><a href="#api.StrategyAssessmentTotal">StrategyAssessmentTotal</a></td>
                  <td>repeated</td>
                  <td><p>策略评估总览列表 </p></td>
                </tr>
              
                <tr>
                  <td>ListUsers</td>
                  <td><a href="#api.UsersCount">UsersCount</a></td>
                  <td>repeated</td>
                  <td><p>开通用户数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ResourceAssessmentTotal">ResourceAssessmentTotal</h3>
        <p>资源评估总览</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>HighRiskCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>高风险数量 </p></td>
                </tr>
              
                <tr>
                  <td>MediumRiskCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>中风险数量 </p></td>
                </tr>
              
                <tr>
                  <td>HealthCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>健康数量 </p></td>
                </tr>
              
                <tr>
                  <td>IgnoreCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>忽略数量 </p></td>
                </tr>
              
                <tr>
                  <td>Date</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>扫描日期，日期格式：&#34;YYYY-MM-DD&#34; </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.StrategyAssessmentTotal">StrategyAssessmentTotal</h3>
        <p>策略评估总览</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>HighRiskCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>高风险数量 </p></td>
                </tr>
              
                <tr>
                  <td>MediumRiskCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>中风险数量 </p></td>
                </tr>
              
                <tr>
                  <td>HealthCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>健康数量 </p></td>
                </tr>
              
                <tr>
                  <td>IgnoreCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>忽略数量 </p></td>
                </tr>
              
                <tr>
                  <td>Date</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>扫描日期 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.UsersCount">UsersCount</h3>
        <p>开通用户数</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Date</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>天，日期格式为：&#34;YYY-MM-DD&#34; </p></td>
                </tr>
              
                <tr>
                  <td>Count</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.AdvisorMarketMonitoringService">AdvisorMarketMonitoringService</h3>
        <p>Advisor大盘监控服务</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>ListResourceInfos</td>
                <td><a href="#api.ListMarketMonitoringRequest">ListMarketMonitoringRequest</a></td>
                <td><a href="#api.ListResourceResponse">ListResourceResponse</a></td>
                <td><p>大盘资源信息查询详情</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>ListResourceInfos</td>
                <td>GET</td>
                <td>/v1/advisor/monitor/resource_info</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/TicCustomerOvervies.proto">api/TicCustomerOvervies.proto</h2><a href="#title">Top</a>
      </div>
      <p>Copyright (c) 2020. Tencent Technologies, Inc.</p><p>Permission is hereby granted, free of charge, to any person obtaining a copy</p><p>of this software and associated documentation files (the "Software"), to deal</p><p>in the Software without restriction, including without limitation the rights</p><p>to use, copy, modify, merge, publish, distribute, sublicense, and/or sell</p><p>copies of the Software, and to permit persons to whom the Software is</p><p>furnished to do so, subject to the following conditions:</p><p>The above copyright notice and this permission notice shall be included in</p><p>all copies or substantial portions of the Software.</p><p>Author: pulse-line-dev (<EMAIL>)</p>

      
        <h3 id="api.ListTicCustomerRequest">ListTicCustomerRequest</h3>
        <p>客户总览信息查询参数</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>SearchWord</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>SearchWord 代表Uin,AppID,CustomerName,Region </p></td>
                </tr>
              
                <tr>
                  <td>OrderBy</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>排序字段，值&#43;字段表示生序，值-字段表示降序 </p></td>
                </tr>
              
                <tr>
                  <td>regions</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>过滤的地区列表 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>分页大小 </p></td>
                </tr>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移，从多少条记录之后开始 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListTicCustomerResponse">ListTicCustomerResponse</h3>
        <p>客户信息数据集</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TotalCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>列表总数 </p></td>
                </tr>
              
                <tr>
                  <td>ListCustomer</td>
                  <td><a href="#api.TicCustomer">TicCustomer</a></td>
                  <td>repeated</td>
                  <td><p>客户总览信息列表 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.TicCustomer">TicCustomer</h3>
        <p>客户总览信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Uin</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>主账号UIN </p></td>
                </tr>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>APPID </p></td>
                </tr>
              
                <tr>
                  <td>CustomerName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>客户简称 </p></td>
                </tr>
              
                <tr>
                  <td>Region</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>所属地域 </p></td>
                </tr>
              
                <tr>
                  <td>StackCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>资源栈总数 </p></td>
                </tr>
              
                <tr>
                  <td>ResourceCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>资源总数 </p></td>
                </tr>
              
                <tr>
                  <td>CreateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>开通时间 </p></td>
                </tr>
              
                <tr>
                  <td>UpdateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>最近编排时间 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.TicCustomerOverviewService">TicCustomerOverviewService</h3>
        <p>客户总览服务</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>ListCustomerInfo</td>
                <td><a href="#api.ListTicCustomerRequest">ListTicCustomerRequest</a></td>
                <td><a href="#api.ListTicCustomerResponse">ListTicCustomerResponse</a></td>
                <td><p>客户总览信息查询</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>ListCustomerInfo</td>
                <td>GET</td>
                <td>/v1/tic/monitor/customer/info</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/TicMarketMonitoring.proto">api/TicMarketMonitoring.proto</h2><a href="#title">Top</a>
      </div>
      <p>Copyright (c) 2020. Tencent Technologies, Inc.</p><p>Permission is hereby granted, free of charge, to any person obtaining a copy</p><p>of this software and associated documentation files (the "Software"), to deal</p><p>in the Software without restriction, including without limitation the rights</p><p>to use, copy, modify, merge, publish, distribute, sublicense, and/or sell</p><p>copies of the Software, and to permit persons to whom the Software is</p><p>furnished to do so, subject to the following conditions:</p><p>The above copyright notice and this permission notice shall be included in</p><p>all copies or substantial portions of the Software.</p><p>Author: pulse-line-dev (<EMAIL>)</p>

      
        <h3 id="api.ListMonitoringResponse">ListMonitoringResponse</h3>
        <p>资源栈总数和资源数总览查询返回结果值</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ListResourceStackCount</td>
                  <td><a href="#api.ResourceStackCount">ResourceStackCount</a></td>
                  <td>repeated</td>
                  <td><p>资源栈总数列表 </p></td>
                </tr>
              
                <tr>
                  <td>ListResourceCount</td>
                  <td><a href="#api.ResourceCount">ResourceCount</a></td>
                  <td>repeated</td>
                  <td><p>资源数总览列表 </p></td>
                </tr>
              
                <tr>
                  <td>ListUsersCount</td>
                  <td><a href="#api.TicUsersCount">TicUsersCount</a></td>
                  <td>repeated</td>
                  <td><p>开通用户数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListTicMarketMonitoringRequest">ListTicMarketMonitoringRequest</h3>
        <p>资源栈总数和资源数总览查询参数</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>StartTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>查询开始时间，日期格式：&#34;YYYY-MM-DD&#34; </p></td>
                </tr>
              
                <tr>
                  <td>EndTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>查询结束时间，日期格式：&#34;YYYY-MM-DD&#34; </p></td>
                </tr>
              
            </tbody>
          </table>

          
            
            
            <h4>Validated Fields</h4>
            <table>
              <thead>
                <tr>
                  <td>Field</td>
                  <td>Validations</td>
                </tr>
              </thead>
              <tbody>
              
                <tr>
                  <td>StartTime</td>
                  <td>
                    <ul>
                    
                      <li>string.min_len: 0</li>
                    
                      <li>string.max_len: 10</li>
                    
                    </ul>
                  </td>
                </tr>
              
                <tr>
                  <td>EndTime</td>
                  <td>
                    <ul>
                    
                      <li>string.min_len: 0</li>
                    
                      <li>string.max_len: 10</li>
                    
                    </ul>
                  </td>
                </tr>
              
              </tbody>
            </table>
            
          

        
      
        <h3 id="api.ResourceCount">ResourceCount</h3>
        <p>资源数总览信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Date</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>天，日期格式为：&#34;YYY-MM-DD&#34; </p></td>
                </tr>
              
                <tr>
                  <td>ManagedResourcesCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>托管的资源总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ResourceStackCount">ResourceStackCount</h3>
        <p>资源栈总数信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Date</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>天，日期格式为：&#34;YYY-MM-DD&#34; </p></td>
                </tr>
              
                <tr>
                  <td>ResourceStackCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>资源栈总数 </p></td>
                </tr>
              
                <tr>
                  <td>DraftCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>草稿状态总数 </p></td>
                </tr>
              
                <tr>
                  <td>PlanSuccessCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>Plan成功总数 </p></td>
                </tr>
              
                <tr>
                  <td>PlanFailureCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>Plan失败总数 </p></td>
                </tr>
              
                <tr>
                  <td>ApplySuccessCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>Apply成功总数 </p></td>
                </tr>
              
                <tr>
                  <td>ApplyFailureCount</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>Apply失败总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.TicUsersCount">TicUsersCount</h3>
        <p>开通用户数</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Date</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>天，日期格式为：&#34;YYY-MM-DD&#34; </p></td>
                </tr>
              
                <tr>
                  <td>Count</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.TicMarketMonitoringService">TicMarketMonitoringService</h3>
        <p>大盘监控服务</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>ListResourceInfo</td>
                <td><a href="#api.ListTicMarketMonitoringRequest">ListTicMarketMonitoringRequest</a></td>
                <td><a href="#api.ListMonitoringResponse">ListMonitoringResponse</a></td>
                <td><p>资源栈总数和资源数总览查询</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>ListResourceInfo</td>
                <td>GET</td>
                <td>/v1/tic/monitor/resource/info</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/TicStack.proto">api/TicStack.proto</h2><a href="#title">Top</a>
      </div>
      <p></p>

      
        <h3 id="api.GetStackVersionRequest">GetStackVersionRequest</h3>
        <p>获取某个版本内容</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>VersionID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>版本ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.GetVersionEventRequest">GetVersionEventRequest</h3>
        <p>获取某个事件</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>EventID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListResourceRequest">ListResourceRequest</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移，从多少条记录之后开始 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>分页大小 </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>资源栈ID，数字形式 </p></td>
                </tr>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>对应的appID </p></td>
                </tr>
              
            </tbody>
          </table>

          
            
            
            <h4>Validated Fields</h4>
            <table>
              <thead>
                <tr>
                  <td>Field</td>
                  <td>Validations</td>
                </tr>
              </thead>
              <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td>
                    <ul>
                    
                      <li>int32.gte: 0</li>
                    
                    </ul>
                  </td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td>
                    <ul>
                    
                      <li>int32.lte: 100</li>
                    
                      <li>int32.gte: 1</li>
                    
                    </ul>
                  </td>
                </tr>
              
              </tbody>
            </table>
            
          

        
      
        <h3 id="api.ListStackResourceResponse">ListStackResourceResponse</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Resources</td>
                  <td><a href="#api.Resource">Resource</a></td>
                  <td>repeated</td>
                  <td><p>资源集 </p></td>
                </tr>
              
                <tr>
                  <td>TotalSize</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>总记录数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListStackVersionRequest">ListStackVersionRequest</h3>
        <p>资源栈的版本请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移，从多少条记录之后开始 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>分页大小 </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>查询某个资源栈的版本，必填，数字类型ID </p></td>
                </tr>
              
                <tr>
                  <td>VersionID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>用户指定查询（filter）的版本号，若找到，则整个列表只返回1项，否则为空 </p></td>
                </tr>
              
            </tbody>
          </table>

          
            
            
            <h4>Validated Fields</h4>
            <table>
              <thead>
                <tr>
                  <td>Field</td>
                  <td>Validations</td>
                </tr>
              </thead>
              <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td>
                    <ul>
                    
                      <li>int32.gte: 0</li>
                    
                    </ul>
                  </td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td>
                    <ul>
                    
                      <li>int32.lte: 100</li>
                    
                      <li>int32.gte: 1</li>
                    
                    </ul>
                  </td>
                </tr>
              
              </tbody>
            </table>
            
          

        
      
        <h3 id="api.ListStackVersionResponse">ListStackVersionResponse</h3>
        <p>资源栈的版本响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Versions</td>
                  <td><a href="#api.StackVersion">StackVersion</a></td>
                  <td>repeated</td>
                  <td><p>结果集 </p></td>
                </tr>
              
                <tr>
                  <td>TotalSize</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>结果总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ListVersionEventRequest">ListVersionEventRequest</h3>
        <p>事件列表请求</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移，从多少条记录之后开始 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>分页大小 </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>资源栈ID，required，格式为数字ID，通过资源栈详情返回 </p></td>
                </tr>
              
                <tr>
                  <td>EventID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>用户指定查询（filter）的事件号，若找到，则整个列表只返回1项，否则为空 </p></td>
                </tr>
              
            </tbody>
          </table>

          
            
            
            <h4>Validated Fields</h4>
            <table>
              <thead>
                <tr>
                  <td>Field</td>
                  <td>Validations</td>
                </tr>
              </thead>
              <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td>
                    <ul>
                    
                      <li>int32.gte: 0</li>
                    
                    </ul>
                  </td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td>
                    <ul>
                    
                      <li>int32.lte: 100</li>
                    
                      <li>int32.gte: 1</li>
                    
                    </ul>
                  </td>
                </tr>
              
              </tbody>
            </table>
            
          

        
      
        <h3 id="api.ListVersionEventResponse">ListVersionEventResponse</h3>
        <p>事件列表请求响应</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Events</td>
                  <td><a href="#api.VersionEvent">VersionEvent</a></td>
                  <td>repeated</td>
                  <td><p>结果集 </p></td>
                </tr>
              
                <tr>
                  <td>TotalSize</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>结果总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.Resource">Resource</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID </p></td>
                </tr>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>App id </p></td>
                </tr>
              
                <tr>
                  <td>Provider</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>tf provider </p></td>
                </tr>
              
                <tr>
                  <td>Product</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>产品 </p></td>
                </tr>
              
                <tr>
                  <td>UniqueKey</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>唯一标识符 </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>资源栈ID，数字形式 </p></td>
                </tr>
              
                <tr>
                  <td>FindVersionId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>FindVersionId </p></td>
                </tr>
              
                <tr>
                  <td>FindEventId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>FindEventId </p></td>
                </tr>
              
                <tr>
                  <td>DisappearVersionId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>DisappearVersionId </p></td>
                </tr>
              
                <tr>
                  <td>DisappearEventId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>DisappearEventId </p></td>
                </tr>
              
                <tr>
                  <td>FindTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>FindTime </p></td>
                </tr>
              
                <tr>
                  <td>DisappearTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>DisappearTime </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ResourceStack">ResourceStack</h3>
        <p>资源栈</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID，资源栈ID，数字形式 </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>ID显示名字，格式`stk-xxx` </p></td>
                </tr>
              
                <tr>
                  <td>APP_ID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>app id </p></td>
                </tr>
              
                <tr>
                  <td>UIN</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属账号的UIN </p></td>
                </tr>
              
                <tr>
                  <td>SubAccountUin</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属子账号ID </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>名字 </p></td>
                </tr>
              
                <tr>
                  <td>Description</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>描述 </p></td>
                </tr>
              
                <tr>
                  <td>Region</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>所在可用区 </p></td>
                </tr>
              
                <tr>
                  <td>Source</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>tf模版来源，url，私有，公共，直写等 </p></td>
                </tr>
              
                <tr>
                  <td>TokenFlag</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>TODO </p></td>
                </tr>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>当前tf状态，如 APPLY_COMPLETED，VERSION_EDITING 等 </p></td>
                </tr>
              
                <tr>
                  <td>CreateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间，格式rfc3339 </p></td>
                </tr>
              
                <tr>
                  <td>UpdateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间，格式rfc3339 </p></td>
                </tr>
              
                <tr>
                  <td>Deleted</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否已删除 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.SearchResourceStackRequest">SearchResourceStackRequest</h3>
        <p>SearchResourceStackRequest 获取资源栈列表</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>偏移，从多少条记录之后开始 </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>分页大小 </p></td>
                </tr>
              
                <tr>
                  <td>UIN</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>VersionID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>EventID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          
            
            
            <h4>Validated Fields</h4>
            <table>
              <thead>
                <tr>
                  <td>Field</td>
                  <td>Validations</td>
                </tr>
              </thead>
              <tbody>
              
                <tr>
                  <td>Offset</td>
                  <td>
                    <ul>
                    
                      <li>int32.gte: 0</li>
                    
                    </ul>
                  </td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td>
                    <ul>
                    
                      <li>int32.lte: 100</li>
                    
                      <li>int32.gte: 1</li>
                    
                    </ul>
                  </td>
                </tr>
              
              </tbody>
            </table>
            
          

        
      
        <h3 id="api.SearchResourceStackResponse">SearchResourceStackResponse</h3>
        <p>SearchResourceStackResponse 获取资源栈列表</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Stacks</td>
                  <td><a href="#api.ResourceStack">ResourceStack</a></td>
                  <td>repeated</td>
                  <td><p>资源栈结果集 </p></td>
                </tr>
              
                <tr>
                  <td>TotalSize</td>
                  <td><a href="#int32">int32</a></td>
                  <td></td>
                  <td><p>总记录数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.StackVersion">StackVersion</h3>
        <p>资源栈版本</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>对应资源栈ID，数字类型，通过资源栈详情返回 </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>版本名 </p></td>
                </tr>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>当前tf状态，如 APPLY_COMPLETED，VERSION_EDITING 等 </p></td>
                </tr>
              
                <tr>
                  <td>Description</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>描述 </p></td>
                </tr>
              
                <tr>
                  <td>Message</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>执行后的消息，如 `command.apply execution failed,command exit code is 1`, ` Destroy complete! Resources: 1 destroyed.` </p></td>
                </tr>
              
                <tr>
                  <td>TfUrl</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>tf内容的URL </p></td>
                </tr>
              
                <tr>
                  <td>CreateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间，格式rfc3339 </p></td>
                </tr>
              
                <tr>
                  <td>UpdateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间，格式rfc3339 </p></td>
                </tr>
              
                <tr>
                  <td>Deleted</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否已删除 </p></td>
                </tr>
              
                <tr>
                  <td>APP_ID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>app id </p></td>
                </tr>
              
                <tr>
                  <td>Uin</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属账号的UIN </p></td>
                </tr>
              
                <tr>
                  <td>SubAccountUin</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属子账号的UIN </p></td>
                </tr>
              
                <tr>
                  <td>TfContent</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>tf内容文本 </p></td>
                </tr>
              
                <tr>
                  <td>ConsoleContent</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>对应的Console plan内容，如果有
 string PlanConsoleContent = 15;
 // 对应的Console apply内容，如果有
 string ApplyConsoleContent = 16;
 // 对应的Console destroy内容，如果有
 string DestroyConsoleContent = 17;
控制台输出，可能是plan/apply/destroy之一 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.VersionEvent">VersionEvent</h3>
        <p>某个版本应用的事件（plan，apply等）</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>ID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>ID </p></td>
                </tr>
              
                <tr>
                  <td>APP_ID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>app id </p></td>
                </tr>
              
                <tr>
                  <td>Uin</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属账户UIN </p></td>
                </tr>
              
                <tr>
                  <td>SubAccountUin</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属子账户UIN </p></td>
                </tr>
              
                <tr>
                  <td>VersionID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属版本ID </p></td>
                </tr>
              
                <tr>
                  <td>StackID</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>所属资源栈ID，数字类型 </p></td>
                </tr>
              
                <tr>
                  <td>Type</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>tf操作类型，如plan, apply等 </p></td>
                </tr>
              
                <tr>
                  <td>ConsoleURL</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>控制台内容URL </p></td>
                </tr>
              
                <tr>
                  <td>LogURL</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>日志内容URL </p></td>
                </tr>
              
                <tr>
                  <td>StateURL</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>tf state文件URL </p></td>
                </tr>
              
                <tr>
                  <td>ExtraURL</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>各种补充等URL，json字符串，可能plan，apply，log等 </p></td>
                </tr>
              
                <tr>
                  <td>Description</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>描述，如 `Plan: 1 to add, 0 to change, 0 to destroy.` 等 </p></td>
                </tr>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>状态，如 failed, success, queueing 等 </p></td>
                </tr>
              
                <tr>
                  <td>Message</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>消息，命名执行的返回值，如 `command.destroy execution failed,command exit code is 1` </p></td>
                </tr>
              
                <tr>
                  <td>CreateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>创建时间，格式rfc3339 </p></td>
                </tr>
              
                <tr>
                  <td>UpdateTime</td>
                  <td><a href="#google.protobuf.Timestamp">google.protobuf.Timestamp</a></td>
                  <td></td>
                  <td><p>更新时间，格式rfc3339 </p></td>
                </tr>
              
                <tr>
                  <td>Deleted</td>
                  <td><a href="#bool">bool</a></td>
                  <td></td>
                  <td><p>是否删除 </p></td>
                </tr>
              
                <tr>
                  <td>ConsoleContent</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>控制台内容文本 </p></td>
                </tr>
              
                <tr>
                  <td>TfContent</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>tf内容文本 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.ResourceStackService">ResourceStackService</h3>
        <p>资源栈相关rpc接口</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>SearchResourceStack</td>
                <td><a href="#api.SearchResourceStackRequest">SearchResourceStackRequest</a></td>
                <td><a href="#api.SearchResourceStackResponse">SearchResourceStackResponse</a></td>
                <td><p></p></td>
              </tr>
            
              <tr>
                <td>ListResource</td>
                <td><a href="#api.ListResourceRequest">ListResourceRequest</a></td>
                <td><a href="#api.ListStackResourceResponse">ListStackResourceResponse</a></td>
                <td><p></p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>SearchResourceStack</td>
                <td>GET</td>
                <td>/v1/tic/stacks:search</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>ListResource</td>
                <td>GET</td>
                <td>/v1/tic/stacks/-/resources</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
        <h3 id="api.StackVersionService">StackVersionService</h3>
        <p>资源版本相关rpc接口</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>ListStackVersion</td>
                <td><a href="#api.ListStackVersionRequest">ListStackVersionRequest</a></td>
                <td><a href="#api.ListStackVersionResponse">ListStackVersionResponse</a></td>
                <td><p>版本列表。通过`StackID`参数指定某个资源栈的版本</p></td>
              </tr>
            
              <tr>
                <td>GetStackVersion</td>
                <td><a href="#api.GetStackVersionRequest">GetStackVersionRequest</a></td>
                <td><a href="#api.StackVersion">StackVersion</a></td>
                <td><p>通过版本号直接获取版本内容</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>ListStackVersion</td>
                <td>GET</td>
                <td>/v1/tic/stacks/{StackID}/versions</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>GetStackVersion</td>
                <td>GET</td>
                <td>/v1/tic/stacks/-/versions/{VersionID}</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
        <h3 id="api.VersionEventService">VersionEventService</h3>
        <p>事件相关rpc接口</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>ListVersionEvent</td>
                <td><a href="#api.ListVersionEventRequest">ListVersionEventRequest</a></td>
                <td><a href="#api.ListVersionEventResponse">ListVersionEventResponse</a></td>
                <td><p>事件列表</p></td>
              </tr>
            
              <tr>
                <td>GetVersionEvent</td>
                <td><a href="#api.GetVersionEventRequest">GetVersionEventRequest</a></td>
                <td><a href="#api.VersionEvent">VersionEvent</a></td>
                <td><p>获取某个事件</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>ListVersionEvent</td>
                <td>GET</td>
                <td>/v1/tic/stacks/{StackID}/events</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>GetVersionEvent</td>
                <td>GET</td>
                <td>/v1/tic/stacks/-/versions/-/events/{EventID}</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/TicTemplate.proto">api/TicTemplate.proto</h2><a href="#title">Top</a>
      </div>
      <p>Copyright (c) 2020. Tencent Technologies, Inc.</p><p>Permission is hereby granted, free of charge, to any person obtaining a copy</p><p>of this software and associated documentation files (the "Software"), to deal</p><p>in the Software without restriction, including without limitation the rights</p><p>to use, copy, modify, merge, publish, distribute, sublicense, and/or sell</p><p>copies of the Software, and to permit persons to whom the Software is</p><p>furnished to do so, subject to the following conditions:</p><p>The above copyright notice and this permission notice shall be included in</p><p>all copies or substantial portions of the Software.</p><p>Author: pulse-line-dev (<EMAIL>)</p>

      
        <h3 id="api.CreatePublicTemplateRequest">CreatePublicTemplateRequest</h3>
        <p>Request message for TicTemplateService.AddPublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TfTree</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>TF内容 </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>名称 </p></td>
                </tr>
              
                <tr>
                  <td>Desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>描述 </p></td>
                </tr>
              
                <tr>
                  <td>Category</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>类别 </p></td>
                </tr>
              
                <tr>
                  <td>Tag</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>标记 </p></td>
                </tr>
              
                <tr>
                  <td>Icon</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>图标链接 </p></td>
                </tr>
              
                <tr>
                  <td>DefaultStackName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>默认栈名称 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>中文名称 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseDesc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>中文描述 </p></td>
                </tr>
              
                <tr>
                  <td>Diagram</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>架构图 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseDiagram</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>中文架构图 </p></td>
                </tr>
              
                <tr>
                  <td>DefaultRegion</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>默认地域 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.CreatePublicTemplateResponse">CreatePublicTemplateResponse</h3>
        <p>Response message for TicTemplateService.AddPublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>添加状态： 0 成功， -1失败 </p></td>
                </tr>
              
                <tr>
                  <td>TemplateId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>创建的模板ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DeletePublicTemplateRequest">DeletePublicTemplateRequest</h3>
        <p>Request message for TicTemplateService.DeletePublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>公有模板ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DeletePublicTemplateResponse">DeletePublicTemplateResponse</h3>
        <p>Response message for TicTemplateService.DeletePublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>删除状态： 0 成功， -1失败 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribePrivateTemplateRequest">DescribePrivateTemplateRequest</h3>
        <p>Request message for TicTemplateService.DescribePrivateTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>私有模板ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribePrivateTemplateResponse">DescribePrivateTemplateResponse</h3>
        <p>Response message for TicTemplateService.DescribePrivateTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateDetail</td>
                  <td><a href="#api.PrivateTemplateDetail">PrivateTemplateDetail</a></td>
                  <td></td>
                  <td><p>私有模板详情 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribePublicTemplateRequest">DescribePublicTemplateRequest</h3>
        <p>Request message for TicTemplateService.DescribePublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>公有模板ID </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.DescribePublicTemplateResponse">DescribePublicTemplateResponse</h3>
        <p>Response message for TicTemplateService.DescribePublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateDetail</td>
                  <td><a href="#api.PublicTemplateDetail">PublicTemplateDetail</a></td>
                  <td></td>
                  <td><p>公有模板详情 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ModifyPublicTemplateRequest">ModifyPublicTemplateRequest</h3>
        <p>Request message for TicTemplateService.ModifyPublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>公有模板ID </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>名称 </p></td>
                </tr>
              
                <tr>
                  <td>Desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>描述 </p></td>
                </tr>
              
                <tr>
                  <td>Category</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>类别 </p></td>
                </tr>
              
                <tr>
                  <td>Tag</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>标记 </p></td>
                </tr>
              
                <tr>
                  <td>Icon</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>图标链接 </p></td>
                </tr>
              
                <tr>
                  <td>DefaultStackName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>默认栈名称 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>中文名称 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseDesc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>中文描述 </p></td>
                </tr>
              
                <tr>
                  <td>TfTree</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>TF内容 </p></td>
                </tr>
              
                <tr>
                  <td>Diagram</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>架构图 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseDiagram</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>中文架构图 </p></td>
                </tr>
              
                <tr>
                  <td>DefaultRegion</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>默认地域 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.ModifyPublicTemplateResponse">ModifyPublicTemplateResponse</h3>
        <p>Response message for TicTemplateService.ModifyPublicTemplate.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>修改状态： 0 成功， -1失败 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PrivateTemplateDetail">PrivateTemplateDetail</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateInfo</td>
                  <td><a href="#api.PrivateTemplateInfo">PrivateTemplateInfo</a></td>
                  <td></td>
                  <td><p>私有模板信息 </p></td>
                </tr>
              
                <tr>
                  <td>TfTree</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>TF内容 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PrivateTemplateInfo">PrivateTemplateInfo</h3>
        <p>私有模板信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template ID </p></td>
                </tr>
              
                <tr>
                  <td>AppId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>客户Appid </p></td>
                </tr>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template状态, 0 未删除, -1 已删除 </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template名字 </p></td>
                </tr>
              
                <tr>
                  <td>Desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template描述 </p></td>
                </tr>
              
                <tr>
                  <td>VersionId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template版本号 </p></td>
                </tr>
              
                <tr>
                  <td>StackId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template资源栈ID </p></td>
                </tr>
              
                <tr>
                  <td>TfUrl</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template内容的cos tf URL </p></td>
                </tr>
              
                <tr>
                  <td>CreateTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>UpdateTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template最后一次修改时间 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PrivateTemplateListRequest">PrivateTemplateListRequest</h3>
        <p>Request message for TicTemplateService.PrivateTemplateList.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>SearchWord</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>Template Name或Id或用户Appid </p></td>
                </tr>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>列表开始Index </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>列表长度限制 </p></td>
                </tr>
              
                <tr>
                  <td>OrderBy</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>排序，私有模板支持创建时间正序：CreateTime，创建时间逆序-CreateTime，修改时间正序UpdateTime, 修改时间逆序-UpdateTime </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PrivateTemplateListResponse">PrivateTemplateListResponse</h3>
        <p>Response message for TicTemplateService.PrivateTemplateList.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateList</td>
                  <td><a href="#api.PrivateTemplateInfo">PrivateTemplateInfo</a></td>
                  <td>repeated</td>
                  <td><p>模板列表 </p></td>
                </tr>
              
                <tr>
                  <td>Count</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>模板总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PublicTemplateDetail">PublicTemplateDetail</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateInfo</td>
                  <td><a href="#api.PublicTemplateInfo">PublicTemplateInfo</a></td>
                  <td></td>
                  <td><p>公有模板信息 </p></td>
                </tr>
              
                <tr>
                  <td>TfTree</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>TF内容 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PublicTemplateInfo">PublicTemplateInfo</h3>
        <p>共有模板信息</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>Id</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template ID </p></td>
                </tr>
              
                <tr>
                  <td>Status</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template状态, 0 未删除, -1删除 </p></td>
                </tr>
              
                <tr>
                  <td>Name</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template名字 </p></td>
                </tr>
              
                <tr>
                  <td>Desc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template描述 </p></td>
                </tr>
              
                <tr>
                  <td>VersionId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template版本号 </p></td>
                </tr>
              
                <tr>
                  <td>StackId</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>template资源栈ID </p></td>
                </tr>
              
                <tr>
                  <td>TfUrl</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template内容的cos tf URL </p></td>
                </tr>
              
                <tr>
                  <td>CreateTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template创建时间 </p></td>
                </tr>
              
                <tr>
                  <td>UpdateTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template最后一次修改时间 </p></td>
                </tr>
              
                <tr>
                  <td>Category</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template类别 </p></td>
                </tr>
              
                <tr>
                  <td>Tag</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template Tag </p></td>
                </tr>
              
                <tr>
                  <td>Icon</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template ICON的URL </p></td>
                </tr>
              
                <tr>
                  <td>DefaultStackName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template默认栈名 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseName</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template的中文名 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseDesc</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template的中文描述 </p></td>
                </tr>
              
                <tr>
                  <td>Diagram</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template的架构图 </p></td>
                </tr>
              
                <tr>
                  <td>ChineseDiagram</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template的中文架构图 </p></td>
                </tr>
              
                <tr>
                  <td>DefaultRegion</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>template的默认地域 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PublicTemplateListRequest">PublicTemplateListRequest</h3>
        <p>Request message for TicTemplateService.PublicTemplateList.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>SearchWord</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>Template Name或Id </p></td>
                </tr>
              
                <tr>
                  <td>Offset</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>列表开始Index </p></td>
                </tr>
              
                <tr>
                  <td>Limit</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>列表长度限制 </p></td>
                </tr>
              
                <tr>
                  <td>Tags</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>标签数组，标签按照或逻辑筛选 </p></td>
                </tr>
              
                <tr>
                  <td>Categories</td>
                  <td><a href="#string">string</a></td>
                  <td>repeated</td>
                  <td><p>类别数组，类别按照或逻辑筛选 </p></td>
                </tr>
              
                <tr>
                  <td>OrderBy</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p>排序，公有模板支持创建时间正序：CreateTime，创建时间逆序-CreateTime， </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.PublicTemplateListResponse">PublicTemplateListResponse</h3>
        <p>Response message for TicTemplateService.PublicTemplateList.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>TemplateList</td>
                  <td><a href="#api.PublicTemplateInfo">PublicTemplateInfo</a></td>
                  <td>repeated</td>
                  <td><p>模板列表 </p></td>
                </tr>
              
                <tr>
                  <td>Count</td>
                  <td><a href="#int64">int64</a></td>
                  <td></td>
                  <td><p>模板总数 </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.TicTemplateService">TicTemplateService</h3>
        <p>TicTemplate Service</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>PrivateTemplateList</td>
                <td><a href="#api.PrivateTemplateListRequest">PrivateTemplateListRequest</a></td>
                <td><a href="#api.PrivateTemplateListResponse">PrivateTemplateListResponse</a></td>
                <td><p>PrivateTemplateList Stub</p></td>
              </tr>
            
              <tr>
                <td>DescribePrivateTemplate</td>
                <td><a href="#api.DescribePrivateTemplateRequest">DescribePrivateTemplateRequest</a></td>
                <td><a href="#api.DescribePrivateTemplateResponse">DescribePrivateTemplateResponse</a></td>
                <td><p>DescribePrivateTemplate Stub</p></td>
              </tr>
            
              <tr>
                <td>PublicTemplateList</td>
                <td><a href="#api.PublicTemplateListRequest">PublicTemplateListRequest</a></td>
                <td><a href="#api.PublicTemplateListResponse">PublicTemplateListResponse</a></td>
                <td><p>PublicTemplateList Stub</p></td>
              </tr>
            
              <tr>
                <td>DescribePublicTemplate</td>
                <td><a href="#api.DescribePublicTemplateRequest">DescribePublicTemplateRequest</a></td>
                <td><a href="#api.DescribePublicTemplateResponse">DescribePublicTemplateResponse</a></td>
                <td><p>DescribePublicTemplate Stub</p></td>
              </tr>
            
              <tr>
                <td>CreatePublicTemplate</td>
                <td><a href="#api.CreatePublicTemplateRequest">CreatePublicTemplateRequest</a></td>
                <td><a href="#api.CreatePublicTemplateResponse">CreatePublicTemplateResponse</a></td>
                <td><p>CreatePublicTemplate</p></td>
              </tr>
            
              <tr>
                <td>ModifyPublicTemplate</td>
                <td><a href="#api.ModifyPublicTemplateRequest">ModifyPublicTemplateRequest</a></td>
                <td><a href="#api.ModifyPublicTemplateResponse">ModifyPublicTemplateResponse</a></td>
                <td><p>ModifyPublicTemplate</p></td>
              </tr>
            
              <tr>
                <td>DeletePublicTemplate</td>
                <td><a href="#api.DeletePublicTemplateRequest">DeletePublicTemplateRequest</a></td>
                <td><a href="#api.DeletePublicTemplateResponse">DeletePublicTemplateResponse</a></td>
                <td><p>DeletePublicTemplate</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>PrivateTemplateList</td>
                <td>GET</td>
                <td>/tic/template/list_private_template</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>DescribePrivateTemplate</td>
                <td>GET</td>
                <td>/tic/template/describe_private_template</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>PublicTemplateList</td>
                <td>GET</td>
                <td>/tic/template/list_public_template</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>DescribePublicTemplate</td>
                <td>GET</td>
                <td>/tic/template/describe_public_template</td>
                <td></td>
              </tr>
              
            
              
              
              <tr>
                <td>CreatePublicTemplate</td>
                <td>POST</td>
                <td>/tic/template/create_public_template</td>
                <td>*</td>
              </tr>
              
            
              
              
              <tr>
                <td>ModifyPublicTemplate</td>
                <td>POST</td>
                <td>/tic/template/modify_public_template</td>
                <td>*</td>
              </tr>
              
            
              
              
              <tr>
                <td>DeletePublicTemplate</td>
                <td>DELETE</td>
                <td>/tic/template/delete_public_template</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    
      
      <div class="file-heading">
        <h2 id="api/TicUser.proto">api/TicUser.proto</h2><a href="#title">Top</a>
      </div>
      <p>Copyright (c) 2020. Tencent Technologies, Inc.</p><p>Permission is hereby granted, free of charge, to any person obtaining a copy</p><p>of this software and associated documentation files (the "Software"), to deal</p><p>in the Software without restriction, including without limitation the rights</p><p>to use, copy, modify, merge, publish, distribute, sublicense, and/or sell</p><p>copies of the Software, and to permit persons to whom the Software is</p><p>furnished to do so, subject to the following conditions:</p><p>The above copyright notice and this permission notice shall be included in</p><p>all copies or substantial portions of the Software.</p><p>Author: pulse-line-dev (<EMAIL>)</p>

      
        <h3 id="api.UserInfo">UserInfo</h3>
        <p></p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>AppID</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>Uin</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>CreateTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
                <tr>
                  <td>UpdateTime</td>
                  <td><a href="#string">string</a></td>
                  <td></td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      
        <h3 id="api.UserListRequest">UserListRequest</h3>
        <p>Request message for TicUserService.UserList.</p>

        

        
      
        <h3 id="api.UserListResponse">UserListResponse</h3>
        <p>Response message for TicUserService.UserList.</p>

        
          <table class="field-table">
            <thead>
              <tr><td>Field</td><td>Type</td><td>Label</td><td>Description</td></tr>
            </thead>
            <tbody>
              
                <tr>
                  <td>UserList</td>
                  <td><a href="#api.UserInfo">UserInfo</a></td>
                  <td>repeated</td>
                  <td><p> </p></td>
                </tr>
              
            </tbody>
          </table>

          

        
      

      

      

      
        <h3 id="api.TicUserService">TicUserService</h3>
        <p>TicUser Service</p>
        <table class="enum-table">
          <thead>
            <tr><td>Method Name</td><td>Request Type</td><td>Response Type</td><td>Description</td></tr>
          </thead>
          <tbody>
            
              <tr>
                <td>UserList</td>
                <td><a href="#api.UserListRequest">UserListRequest</a></td>
                <td><a href="#api.UserListResponse">UserListResponse</a></td>
                <td><p>UserList Stub</p></td>
              </tr>
            
          </tbody>
        </table>

        
          
          
          <h4>Methods with HTTP bindings</h4>
          <table>
            <thead>
              <tr>
                <td>Method Name</td>
                <td>Method</td>
                <td>Pattern</td>
                <td>Body</td>
              </tr>
            </thead>
            <tbody>
            
              
              
              <tr>
                <td>UserList</td>
                <td>GET</td>
                <td>/tic/user/user_list</td>
                <td></td>
              </tr>
              
            
            </tbody>
          </table>
          
        
    

    <h2 id="scalar-value-types">Scalar Value Types</h2>
    <table class="scalar-value-types-table">
      <thead>
        <tr><td>.proto Type</td><td>Notes</td><td>C++</td><td>Java</td><td>Python</td><td>Go</td><td>C#</td><td>PHP</td><td>Ruby</td></tr>
      </thead>
      <tbody>
        
          <tr id="double">
            <td>double</td>
            <td></td>
            <td>double</td>
            <td>double</td>
            <td>float</td>
            <td>float64</td>
            <td>double</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="float">
            <td>float</td>
            <td></td>
            <td>float</td>
            <td>float</td>
            <td>float</td>
            <td>float32</td>
            <td>float</td>
            <td>float</td>
            <td>Float</td>
          </tr>
        
          <tr id="int32">
            <td>int32</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="int64">
            <td>int64</td>
            <td>Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="uint32">
            <td>uint32</td>
            <td>Uses variable-length encoding.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int/long</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="uint64">
            <td>uint64</td>
            <td>Uses variable-length encoding.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint32">
            <td>sint32</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sint64">
            <td>sint64</td>
            <td>Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="fixed32">
            <td>fixed32</td>
            <td>Always four bytes. More efficient than uint32 if values are often greater than 2^28.</td>
            <td>uint32</td>
            <td>int</td>
            <td>int</td>
            <td>uint32</td>
            <td>uint</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="fixed64">
            <td>fixed64</td>
            <td>Always eight bytes. More efficient than uint64 if values are often greater than 2^56.</td>
            <td>uint64</td>
            <td>long</td>
            <td>int/long</td>
            <td>uint64</td>
            <td>ulong</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="sfixed32">
            <td>sfixed32</td>
            <td>Always four bytes.</td>
            <td>int32</td>
            <td>int</td>
            <td>int</td>
            <td>int32</td>
            <td>int</td>
            <td>integer</td>
            <td>Bignum or Fixnum (as required)</td>
          </tr>
        
          <tr id="sfixed64">
            <td>sfixed64</td>
            <td>Always eight bytes.</td>
            <td>int64</td>
            <td>long</td>
            <td>int/long</td>
            <td>int64</td>
            <td>long</td>
            <td>integer/string</td>
            <td>Bignum</td>
          </tr>
        
          <tr id="bool">
            <td>bool</td>
            <td></td>
            <td>bool</td>
            <td>boolean</td>
            <td>boolean</td>
            <td>bool</td>
            <td>bool</td>
            <td>boolean</td>
            <td>TrueClass/FalseClass</td>
          </tr>
        
          <tr id="string">
            <td>string</td>
            <td>A string must always contain UTF-8 encoded or 7-bit ASCII text.</td>
            <td>string</td>
            <td>String</td>
            <td>str/unicode</td>
            <td>string</td>
            <td>string</td>
            <td>string</td>
            <td>String (UTF-8)</td>
          </tr>
        
          <tr id="bytes">
            <td>bytes</td>
            <td>May contain any arbitrary sequence of bytes.</td>
            <td>string</td>
            <td>ByteString</td>
            <td>str</td>
            <td>[]byte</td>
            <td>ByteString</td>
            <td>string</td>
            <td>String (ASCII-8BIT)</td>
          </tr>
        
      </tbody>
    </table>
  </body>
</html>

